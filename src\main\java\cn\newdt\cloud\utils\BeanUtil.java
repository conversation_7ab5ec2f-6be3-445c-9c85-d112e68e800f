package cn.newdt.cloud.utils;

import cn.newdt.cloud.domain.KubeConfig;
import cn.newdt.cloud.vo.ConfigManageVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shindata.redis.v1.redisspec.IpList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.ClassUtils;
import org.apache.commons.lang3.builder.Diff;
import org.apache.commons.lang3.builder.DiffBuilder;
import org.apache.commons.lang3.builder.DiffResult;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.beans.PropertyDescriptor;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class BeanUtil {
    /**
     * Returns an array of null properties of an object
     * @param source
     * @return
     */
    private static String[] getNullPropertyNames (Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();
        Set<String> emptyNames = new HashSet<>();
        for(java.beans.PropertyDescriptor pd : pds) {
            //check if value of this property is null then add it to the collection
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) emptyNames.add(pd.getName());
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    private static String[] getNonNullPropertyNames (Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();
        Set<String> emptyNames = new HashSet<>();
        for(java.beans.PropertyDescriptor pd : pds) {
            //check if value of this property is null then add it to the collection
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue != null) emptyNames.add(pd.getName());
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    public static void copyNonNullProperties(Object source, Object destination){
        BeanUtils.copyProperties(source, destination,
                getNullPropertyNames(source));
    }

    public static void copyWithoutOverwrite(KubeConfig kubeConfig, ConfigManageVO vo) {
        BeanUtils.copyProperties(kubeConfig, vo, getNonNullPropertyNames(vo));
    }

    /**
     * @param bean
     * @param bean_
     * @param ignores 忽略的属性，只能对不重复的属性生效. 例如 {A:'', B:{A:''}} 无法对A属性做出正确判断.
     * @return
     */
    public static <T, U> boolean compareEqual(T bean, U bean_, Set<String> ignores) throws Exception {
        return compareEqual(bean, bean_, ignores, false);
    }

    private ObjectMapper objectMapper = new ObjectMapper();
    /**
     * @param ignoreNullProperties set to true will ignore U's null properties. set two compared bean in appropriate order if you need this feature
     */
    public static <T, U> List<String> diff(T bean, U bean_, Set<String> ignores, boolean ignoreNullProperties, String propertyPath) throws Exception {
        PropertyDescriptor[] propertyDescriptors = PropertyUtils.getPropertyDescriptors(bean);
        ArrayList<String> diffResults = new ArrayList<>();
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            boolean b = true;
            Object property = PropertyUtils.getProperty(bean, propertyDescriptor.getName());
            Object property1 = PropertyUtils.getProperty(bean_, propertyDescriptor.getName());
            if (property == null ) {
                b = property1 == null || ignoreNullProperties;
            } else if (property1 == null) {
                b = ignoreNullProperties;
            } else if (property instanceof Collection) {
//                b = property.equals(property1);
                Iterator<?> iterator = ((Collection<?>) property).iterator();
                Iterator<?> iterator1 = ((Collection<?>) property1).iterator();

                if (((Collection<?>) property).size() != ((Collection<?>) property1).size())
                    b = false;
                else
                    for (; iterator.hasNext(); ) {
                        Object next = iterator.next();
                        Object next1 = iterator1.next();
                        List<String> diff = diff(next, next1, ignores, ignoreNullProperties,
                                propertyPath + "." + propertyDescriptor.getName());
                        diffResults.addAll(diff);
                    }
            } else if (property instanceof Map) {
                b = Objects.equals(property, property1);
            } else if (propertyDescriptor.getPropertyType() == String.class
                    || ClassUtils.isPrimitiveWrapper(propertyDescriptor.getPropertyType())
                    || propertyDescriptor.getPropertyType().isEnum()) {
                b = Objects.equals(property, property1);
            } else if (propertyDescriptor.getPropertyType().isPrimitive()) {
                b = property.equals(property1);
            } else {
//                b = compareEqual(property,property1,ignores, ignoreNullProperties);
                if (property.getClass() != property1.getClass()) {
                    Map map = JsonUtil.toMap(property);
                    Map map1 = JsonUtil.toMap(property1);

                    DiffResult<Map> diffResult = new DiffBuilder<>(map, map1, ToStringStyle.JSON_STYLE).build();
                    List<Diff<?>> mapDiffs = ignoreNullProperties ? diffResult.getDiffs() : diffResult.getDiffs().stream()
                            .filter(d -> d.getLeft() != null && d.getRight() != null)
                            .collect(Collectors.toList());
                    b = mapDiffs.isEmpty();
                    if (!b) diffResults.add(String.format("heterogeneous p:%s l:%s r:%s",
                            propertyPath + "." + propertyDescriptor.getName(), property, property1));
                } else {
                    List<String> embeddedDiffResults = diff(property, property1, ignores, ignoreNullProperties,
                            propertyPath + "." + propertyDescriptor.getName());
                    // add to upper level results
                    diffResults.addAll(embeddedDiffResults);
                }
            }
            if (!b && (ignores == null || !ignores.contains(propertyDescriptor.getName()))) {
                if (property == null || property1 == null
                        || propertyDescriptor.getPropertyType() == String.class
                        || ClassUtils.isPrimitiveWrapper(propertyDescriptor.getPropertyType())
                        || propertyDescriptor.getPropertyType().isEnum()
                        || propertyDescriptor.getPropertyType().isPrimitive()
                        || Collection.class.isAssignableFrom(propertyDescriptor.getPropertyType()))
                diffResults.add(String.format("p:%s l:%s r:%s",
                        propertyPath + "." + propertyDescriptor.getName(), property, property1));
            }
        }
        log.debug("compared results: " + diffResults.toString());
        return diffResults;
    }

    public static <T, U> boolean compareEqual(T bean, U bean_, Set<String> ignores, boolean ignoreNullProperties) throws Exception {
        return diff(bean, bean_, ignores, ignoreNullProperties, "").isEmpty();
    }

}
