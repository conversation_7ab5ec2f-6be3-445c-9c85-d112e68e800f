package cn.newdt.cloud.utils;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CSIEnum;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.cr.MongoDBCommunity;
import cn.newdt.cloud.dto.Label;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.service.csi.CSIUtil;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import io.fabric8.kubernetes.api.model.*;

import java.util.*;
import java.util.stream.Collectors;

import static cn.newdt.cloud.domain.cr.MongoDBCommunity.BACKUP_VOLUME;
import static cn.newdt.cloud.domain.cr.MongoDBCommunity.DATA_VOLUME;
import static cn.newdt.cloud.service.impl.MongoDbService.MONGODB_REPLICASET_EXPORTER_SVC_NAME_SUFFIX;

public class MongoUtil {
    public static final String DB_CONTAINER_NAME = "mongod";
    public static final String ADMIN = CloudAppConstant.UsernameAndPassword.mongoDBUsername;
    public static final String DMP_ADMIN = "dmpadmin";
    public static final String BACKUP_MOUNT_PATH = "/mnt/share";

    public static final String MONGODB_REPLICASET_EXPORTER_SVC_ENDPOINT_NAME = "mongodb-exporter";

    public static PersistentVolume createHostPathPv(String name, String path, String storage, Label... labels) {
        Map<String, String> labelMap = labels == null ? null :
                Arrays.stream(labels).collect(Collectors.toMap(Label::getName, Label::getValue));
        return new PersistentVolumeBuilder().withNewMetadata().withName(name).withLabels(labelMap).endMetadata()
                .withNewSpec().withStorageClassName(CSIUtil.getStorageClassNameOfHostpath()).withCapacity(Collections.singletonMap("storage", new Quantity(storage)))
                .withAccessModes("ReadWriteOnce")
                .withNewHostPath().withType("DirectoryOrCreate").withNewPath(path).endHostPath()
                .endSpec().build();
    }

    /**
     * 构造hostpath pvc对象, 调用createBatch方法批量创建
     * @param name pvc name
     * @param namespace 存储request
     * @param storage 存储request
     * @param labelMap pvc 本身label
     * @param selectorLabelMap pvc select pv的label
     */
    public static PersistentVolumeClaim buildHostPathPvc(String name, String namespace, String storage, Map<String, String> labelMap, Map<String, String> selectorLabelMap) {
        return new PersistentVolumeClaimBuilder().withNewMetadata().withName(name).withNamespace(namespace).withLabels(labelMap).endMetadata()
                .withNewSpec().withStorageClassName(CSIUtil.getStorageClassNameOfHostpath())
                .withAccessModes("ReadWriteOnce")
                .withNewResources().withRequests(Collections.singletonMap("storage", new Quantity(storage))).endResources()
                .withNewSelector().withMatchLabels(selectorLabelMap).endSelector()
                .endSpec().build();
    }

    public static CloudApp convertToApp(String crRun) {
        crRun = crRun.replaceAll("!!.*", "");
        MongoDBCommunity expected = YamlEngine.unmarshal(crRun, MongoDBCommunity.class);
        CloudApp app = new CloudApp();
        Map<String, Quantity> limits = expected.getSpec().getStatefulSet().getSpec().getTemplate().getSpec().getContainers()
                .stream().filter(container -> container.getName().equals(AppKind.MongoDB.getContainerName()))
                .findFirst().orElseThrow(() -> new IllegalStateException())
                .getResources().getLimits();
        app.setCpu(limits.get("cpu").toString());
        app.setMemory(limits.get("memory").toString());
        app.setMembers(expected.getSpec().getMembers());
        // ip list
        app.setDisk(expected.getSpec().getStatefulSet().getSpec().getVolumeClaimTemplates().stream().filter(pvc -> pvc.getMetadata().getName().equals(DATA_VOLUME))
                .findAny().orElseThrow(()->new IllegalStateException("未找到data-volume 在volumeClaimTemplates定义里")).getSpec().getResources().getRequests().get("storage").toString());
        expected.getSpec().getStatefulSet().getSpec().getVolumeClaimTemplates().stream()
                        .filter(pvc -> pvc.getMetadata().getName().equals(BACKUP_VOLUME))
                                .findAny().ifPresent(pvc -> {
                    app.setBackupDisk(pvc.getSpec().getResources().getRequests().get("storage").toString());
                });
        app.setVersion(expected.getSpec().getVersion());
        return app;
    }

    public static Label[] getPodLabel(String crName){
        return AppKind.MongoDB.labels(crName);
    }

    public static String getSecretName(String crName, String username) {
        return "mongodb-" + crName + "-" + username + "-password";
    }

    public static String getScramSecretName(String crName, String username) {
        return getSecretName(crName, username) + "-scram-credentials";
    }

    public static List<String> getPodNames(String appName, Integer members){
        List<String> podNames = new ArrayList<>(members);
        for (int i = 0; i < members; i++) {
            podNames.add(appName + "-" + i);
        }
        return podNames;
    }

    /**
     * 亲和方法
     * @param name
     * @param path
     * @param storage
     * @param labels
     * @return
     */
    public static PersistentVolume createHostPathPvAffinity(List<String> nodes, String name, String path, String storage, Label... labels) {
        Map<String, String> labelMap = labels == null ? null :
                Arrays.stream(labels).collect(Collectors.toMap(Label::getName, Label::getValue));

        NodeSelectorTerm nodeSelectorTerm = new NodeSelectorTerm();
        List<NodeSelectorRequirement> matchExpressions = new ArrayList<>();
        NodeSelectorRequirement nodeSelectorRequirement = new NodeSelectorRequirement();
        nodeSelectorRequirement.setKey("kubernetes.io/hostname");
        nodeSelectorRequirement.setOperator("In");
        nodeSelectorRequirement.setValues(nodes);
        matchExpressions.add(nodeSelectorRequirement);
        nodeSelectorTerm.setMatchExpressions(matchExpressions);
        return new PersistentVolumeBuilder().withNewMetadata().withName(name).withLabels(labelMap).endMetadata()
                .withNewSpec().withStorageClassName(CSIEnum.HOSTPATH.getStorageClass()).withCapacity(Collections.singletonMap("storage", new Quantity(storage)))
                .withAccessModes("ReadWriteOnce")
                .withNewHostPath().withType("DirectoryOrCreate").withNewPath(path).endHostPath()
                .withNewNodeAffinity().withNewRequired().withNodeSelectorTerms(
                        nodeSelectorTerm
                ).endRequired().endNodeAffinity()
                .endSpec().build();
    }

    public static String getComponentKind(PodDTO pod, CloudApp app) {
        String prefixName = "mc-" + app.getCrName() + "-";
        String ipStr = pod.getPodIp().replace(".", "-");
        String suffixName = "-" + ipStr;
        String pluginKind = pod.getPodName().substring(prefixName.length(), pod.getPodName().indexOf(suffixName));
        if("shard".equalsIgnoreCase(pluginKind)){
            return CloudAppConstant.MongoDB.SHARD_NAME;
        }else if("config".equalsIgnoreCase(pluginKind)){
            return CloudAppConstant.MongoDB.CONFIG_NAME;
        }else if(CloudAppConstant.MongoDB.ROUTER.equalsIgnoreCase(pluginKind)){
            return CloudAppConstant.MongoDB.MONGOS_NAME;
        }
        throw new UnsupportedOperationException();
    }

    public static String getSharedBackupPVCName(String crName, String backupVolume) {
        return String.format("%s-%s", backupVolume, crName);
    }

    public static String getBackupRootPath(String namespace, String crName) {
        String backupRoot = BACKUP_MOUNT_PATH + "/";
        backupRoot += AppKind.MongoDB.getKind().toLowerCase() + "/" + AppKind.MongoDB.getArch().toLowerCase() + "/" + namespace + "/" + crName; // 共享存储需要自行创建目录层级
        return backupRoot;
    }

    public static String getExporterServiceName(String crName) {
        return crName;
    }

    public static String getBackupCMName(String crName) {
        return crName + "-backup-cm";
    }

    public static String getScriptsCMName(String crName) {
        return crName + "-ftp-cm";
    }
}
