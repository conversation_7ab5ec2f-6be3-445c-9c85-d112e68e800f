package cn.newdt.cloud.repository;

import cn.newdt.cloud.config.KubeExecSimpleListener;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.domain.KubeConfig;
import cn.newdt.cloud.domain.KubeScheduler;
import cn.newdt.cloud.domain.cr.DistributedRedisCluster;
import cn.newdt.cloud.domain.cr.MongoDBCommunity;
import cn.newdt.cloud.dto.*;
import cn.newdt.cloud.repository.impl.FabricKubeClientFactory;
import cn.newdt.cloud.utils.DateUtil;
import cn.newdt.cloud.utils.MetricUtil;
import cn.newdt.cloud.utils.YamlUtil;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.exception.CustomException;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLMapper;
import com.shindata.mysql.v1.MySQLHA;
import com.shindata.opengauss.v1.OpenGaussCluster;
import com.shindata.opengauss.v1.OpenGaussClusterStatus;
import com.shindata.redis.v1.RedisCluster;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.api.model.apps.Deployment;
import io.fabric8.kubernetes.api.model.apps.StatefulSet;
import io.fabric8.kubernetes.api.model.coordination.v1.Lease;
import io.fabric8.kubernetes.api.model.metrics.v1beta1.ContainerMetrics;
import io.fabric8.kubernetes.api.model.metrics.v1beta1.PodMetrics;
import io.fabric8.kubernetes.api.model.storage.StorageClass;
import io.fabric8.kubernetes.client.CustomResource;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.fabric8.kubernetes.client.KubernetesClientException;
import io.fabric8.kubernetes.client.dsl.ExecWatch;
import io.fabric8.kubernetes.client.dsl.FilterWatchListDeletable;
import io.fabric8.kubernetes.client.dsl.MixedOperation;
import io.fabric8.kubernetes.client.dsl.Resource;
import io.fabric8.kubernetes.client.dsl.base.PatchContext;
import io.fabric8.kubernetes.client.dsl.base.PatchType;
import io.fabric8.kubernetes.client.dsl.base.ResourceDefinitionContext;
import io.fabric8.kubernetes.client.utils.Serialization;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.CloudAppConstant.KubeSchedulerMode.TAINT;
import static cn.newdt.cloud.constant.CloudAppConstant.Operator.*;
import static io.kubernetes.client.util.taints.Taints.Effect.NO_EXECUTE;
import static io.kubernetes.client.util.taints.Taints.Effect.NO_SCHEDULE;

@Slf4j
public class KubeApiClientFabric implements KubeClient {

    private final KubernetesClient apiClient;

    private KubeConfig kubeConfig;

    public KubeApiClientFabric(KubeConfig config) {
        Objects.requireNonNull(config, "kubeconfig is null");
        this.kubeConfig = config;
        this.apiClient = createApiClientByFabric();
        Serialization.jsonMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    @Deprecated
    public KubeApiClientFabric(KubernetesClient apiClient) {
        this.apiClient = apiClient;
    }

    private KubernetesClient createApiClientByFabric() {
        return FabricKubeClientFactory.createApiClientByFabric(kubeConfig);
    }

    @Override
    public List<PodDTO> listPod(String ns, Label... labels) {
        List<Pod> items;
        Map<String, String> labelMap = labels != null ?
                Arrays.stream(labels).collect(Collectors.toMap(l -> l.getName(), l -> l.getValue(), (v1, v2) -> v2)) :
                Collections.emptyMap();
        if (StringUtils.isEmpty(ns)) {
            // partition labels to two group, single value and multiple value
            Map<Boolean, List<Label>> partition =
                    Arrays.stream(labels).collect(Collectors.partitioningBy(l -> l.getValue().contains(",")));
            // for each label which contains multiple value pass to withLabelIn filter
            // for all labels contain single value pass to withLabels filter
            FilterWatchListDeletable<Pod, PodList> inAnyNamespace =
                    apiClient.pods().inAnyNamespace().withLabels(partition.get(false).stream()
                            .collect(Collectors.toMap(l -> l.getName(), l -> l.getValue())));
            for (Label label : partition.get(true)) {
                inAnyNamespace = inAnyNamespace.withLabelIn(label.getName(), label.getValue());
            }
            items = inAnyNamespace.list().getItems();
        }
        // 运维
        else {
            items = apiClient.pods().inNamespace(ns).withLabels(labelMap).list().getItems();
        }

        // map metric and pod by pod name
        return items.stream().map(o -> {
            PodDTO p = new PodDTO();
            p.setPod(o);
            p.setPodName(o.getMetadata().getName());
            p.setNamespace(o.getMetadata().getNamespace());
            p.setPodIp(o.getStatus().getPodIP());
            p.setNodeName(o.getSpec().getNodeName());
            p.setLabels(o.getMetadata().getLabels());
            p.setContainers(o.getSpec().getContainers().stream().map(c->c.getName()).collect(Collectors.toList()));
            return p;
        }).collect(Collectors.toList());
    }

    @Override
    public List<Pod> listPod(String ns) {
        return apiClient.pods().inNamespace(ns).list().getItems();
    }

    @Override
    public List<PodDTO> listPodMetrics(String ns, Label[] labels) {
        Map<String, String> labelMap = Collections.emptyMap();
        if (labels != null) {
            labelMap = new HashMap<>((int) Math.ceil(labels.length / 0.75));
            for (Label label : labels) {
                labelMap.put(label.getName(), label.getValue());
            }
        }
        PodList pods = apiClient.pods().inNamespace(ns).withLabels(labelMap).list();
        try {
            // cpu, memmory, fetch disk metric by prometheus
            List<PodMetrics> metrics = new ArrayList<>();
            try {
                metrics = apiClient.top().pods().metrics(ns).getItems();
            } catch (Exception e) {
                log.error(e.getMessage());
            }
//            for (Pod pod : pods.getItems()) {
//            execCmd(ns, pod.getMetadata().getName(), CloudAppConstant.DB_CONTAINER_NAME, )
//            }
            // pod name filter
            Set<String> podNameSet = pods.getItems().stream().map(p -> p.getMetadata().getName()).collect(Collectors.toSet());
            Predicate<PodMetrics> predicate = (p) -> podNameSet.contains(p.getMetadata().getName());
            Map<String, List<ContainerMetrics>> collect = metrics.stream().filter(predicate)
                    .collect(Collectors.toMap(p -> p.getMetadata().getName(), PodMetrics::getContainers));
            // map metric and pod by pod name
            return pods.getItems().stream().map(o -> {
                PodDTO p = new PodDTO();
                p.setPod(o);
                List<ContainerMetrics> containers = collect.get(p.getPod().getMetadata().getName());
                if (containers != null) {
                    p.setMetrics(containers.stream().collect(Collectors.toMap(ContainerMetrics::getName, Function.identity())));
                }
                p.setPodName(o.getMetadata().getName());
                p.setNamespace(o.getMetadata().getNamespace());
                return p;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            // 当查询异常时,返回空属性
            log.error("label:{} metric error:{}", labels, e);
            return pods.getItems().stream().map(o -> {
                PodDTO p = new PodDTO();
                p.setPod(o);
                return p;
            }).collect(Collectors.toList());
        }
    }

    @Override
    public Pod getPod(String namespace, String podName) {
        return apiClient.pods().inNamespace(namespace).withName(podName).get();
    }

    @Override
    public ConfigMap getConfigMap(String name, String namespace) {
        return apiClient.configMaps().inNamespace(namespace).withName(name).get();
    }

    @Override
    public void deleteConfigMap(String name, String namespace) {
        Boolean delete = apiClient.configMaps().inNamespace(namespace).withName(name).delete();
        if (delete) {
            log.info("Delete configMap {}/{}", namespace, name);
        }
    }

    @Override
    public void deleteConfigMaps(List<String> names, String namespace) {
        List<ConfigMap> collect = names.stream().map(n -> new ConfigMapBuilder().withMetadata(new ObjectMetaBuilder()
                .withName(n).withNamespace(namespace).build()).build()).collect(Collectors.toList());
        Boolean delete = apiClient.configMaps().inNamespace(namespace).delete(collect);
        if (delete) {
            log.info("Delete configMap {}/{}", namespace, JSONObject.toJSONString(names));
        }
    }

    @Override
    public List<ConfigMap> listConfigMap(String namespace, Label... labels) {
        if (ArrayUtils.isEmpty(labels)){
            log.info(Arrays.toString(labels));
            return apiClient.configMaps().inNamespace(namespace).list().getItems();
        }
        return apiClient.configMaps().inNamespace(namespace).withLabels(Label.toMap(labels)).list().getItems();
    }

    @Override
    public Secret createSecret(String name, String password, String namespace) {
        return createSecret(name, namespace, "password", password);
    }

    @Override
    public Secret createSecret(String name, String namespace, String passwordKey, String password) {
        Secret t = new Secret();
        t.setMetadata(new ObjectMetaBuilder().withName(name).build());
        t.setStringData(Collections.singletonMap(passwordKey, password));
        return apiClient.secrets().inNamespace(namespace).createOrReplace(t);
    }


    @Override
    public String createSecret(String namespace, String name, Map<String, String> labels, Map<String, String> data) {
        createSecret(namespace, name, labels, data, null);
        return name;
    }

    @Override
    public Boolean deleteSecret(String name, String namespace) {
        Boolean delete = apiClient.secrets().inNamespace(namespace).withName(name).delete();
        if (delete) {
            log.info("Delete secret {}/{}", namespace, name);
        }
        return delete;
    }

    @Override
    public void createPv(PersistentVolume... pvs) {
        KubernetesList kubernetesList = new KubernetesList();
        kubernetesList.setItems(Arrays.asList(pvs));
        kubernetesList.setApiVersion(pvs[0].getApiVersion());
        kubernetesList.setKind(pvs[0].getKind());
        apiClient.lists().create(kubernetesList);
    }

    @Override
    public boolean deletePv(String name) {
        return apiClient.persistentVolumes().withName(name).delete();
    }

    @Override
    public boolean deletePvc(String namespace, String name) {
        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(namespace)) {
            return false;
        }
        Boolean delete = apiClient.persistentVolumeClaims().inNamespace(namespace).withName(name).delete();
        if (delete) {
            log.info("Delete pvc {}/{}", namespace, name);
        }
        return delete;
    }

    @Override
    public PersistentVolumeList listPv(Map<String, String> labels) {
        if (CollectionUtils.isEmpty(labels)) {
            return apiClient.persistentVolumes().list();
        }
        return apiClient.persistentVolumes().withLabels(labels).list();
    }

    @Override
    public List<PersistentVolume> listPv(String scName) {
        PersistentVolumeList pvs = apiClient.persistentVolumes().list();
        if (StringUtils.isEmpty(scName) || ObjectUtils.isEmpty(pvs) || CollectionUtils.isEmpty(pvs.getItems())) {
            return pvs.getItems();
        }
        List<PersistentVolume> pvList = pvs.getItems().stream().filter(pv -> scName.equalsIgnoreCase(pv.getSpec().getStorageClassName())).collect(Collectors.toList());
        return pvList;

    }

    @Override
    public PersistentVolumeList listPvWithLabelKey(String labelKey) {
        return apiClient.persistentVolumes().withLabel(labelKey).list();
    }

    @Override
    public PersistentVolumeClaimList listPvcWithLabelKeyAndNameSpace(String namespace, String labelKey) {
        if (StringUtils.isEmpty(namespace)) {
            if (!StringUtils.isEmpty(labelKey)) {
                return apiClient.persistentVolumeClaims().withLabel(labelKey).list();
            }
            return apiClient.persistentVolumeClaims().list();
        }
        if (StringUtils.isEmpty(labelKey)) {
            return apiClient.persistentVolumeClaims().inNamespace(namespace).list();
        }
        return apiClient.persistentVolumeClaims().inNamespace(namespace).withLabel(labelKey).list();
    }

    /**
     * @param namespace
     * @param labels
     */
    @Override
    public PersistentVolumeClaimList listPvc(String namespace, Map<String, String> labels) {
        log.debug(apiClient.getMasterUrl().getHost());
        if (StringUtils.isEmpty(namespace)) {
            if(!CollectionUtils.isEmpty(labels)) {
                return apiClient.persistentVolumeClaims().inAnyNamespace().withLabels(labels).list();
            }
            return apiClient.persistentVolumeClaims().list();
        }
        if (CollectionUtils.isEmpty(labels)) {
            return apiClient.persistentVolumeClaims().inNamespace(namespace).list();
        }
        return apiClient.persistentVolumeClaims().inNamespace(namespace).withLabels(labels).list();
    }

    @Override
    public PersistentVolumeClaimList listPvc(List<String> namespaceList, Map<String, String> labels) {
        PersistentVolumeClaimList list;
        if(!CollectionUtils.isEmpty(labels)) {
            list = apiClient.persistentVolumeClaims().inAnyNamespace().withLabels(labels).list();
        }
        else list = apiClient.persistentVolumeClaims().inAnyNamespace().list();
        if (!namespaceList.isEmpty()) {
            List<PersistentVolumeClaim> items = list.getItems().stream().filter(pvc->namespaceList.contains(pvc.getMetadata().getNamespace())).collect(Collectors.toList());
            list.setItems(items);
        }
        return list;
    }

    @Override
    public Secret updateSecret(Secret secret) {
        Secret replace = apiClient.secrets().replace(secret);
        log.info("Update secret: {} in namespace {}", replace.getMetadata().getName(), replace.getMetadata().getNamespace());
        return replace;
    }

    @Override
    public void createPvc(PersistentVolumeClaim pvc, String nameSpace) {
        apiClient.persistentVolumeClaims().inNamespace(nameSpace).create(pvc);
    }

    @Override
    public void deleteStatefulset(String name, String namespace) {
        String uid = apiClient.apps().statefulSets().inNamespace(namespace).withName(name).get().getMetadata().getUid();
        apiClient.apps().statefulSets().inNamespace(namespace).withName(name)
                .withPropagationPolicy(DeletionPropagation.ORPHAN)
                .delete();
        apiClient.apps().statefulSets().inNamespace(namespace).withName(name)
                .waitUntilCondition(sts -> sts == null || !sts.getMetadata().getUid().equals(uid), 10, TimeUnit.SECONDS);
        log.info("Delete sts {}/{}", namespace, name);
    }

    @Override
    public void createSecret(String namespace, String name, Map<String, String> labels, Map<String, String> data, HasMetadata owner) {
        Secret t = new Secret();
        t.setStringData(data);
        if(Objects.nonNull(labels)) {
            t.setMetadata(new ObjectMetaBuilder().withName(name).withLabels(labels).build());
        } else {
            t.setMetadata(new ObjectMetaBuilder().withName(name).build());
        }
        if (owner != null)
            t.getMetadata().setOwnerReferences(Collections.singletonList(
                    new OwnerReferenceBuilder().withName(owner.getMetadata().getName()).withKind(owner.getKind())
                            .withUid(owner.getMetadata().getUid())
                            .withApiVersion(owner.getApiVersion())
                            .build()));
        Optional.ofNullable(apiClient.secrets().inNamespace(namespace).createOrReplace(t))
                .ifPresent(s -> log.info("Create secret {}/{}", s.getMetadata().getNamespace(), s.getMetadata().getName()));
    }

    @Override
    public ResourceQuota getResourceQuota(String namespace, String name) {
        return apiClient.resourceQuotas().inNamespace(namespace).withName(name).get();
    }

    /**
     * describe nodes = kubectl describe nodes, 与kubectl不同的是
     * nodedto.total = kubectl describe node.allocatable
     * nodedto.allocatable = total - sum(pod.request)
     * <p>TODO 该方法需要用户有列举集群所有pod的权限 </p>
     */
    @Override
    public List<NodeDTO> describeNodes(String nodeName, KubeScheduler kubeScheduler) {
        List<NodeDTO> nodeDTOS = listSimpleNodes(kubeScheduler);
        List<Pod> nonTerminatedPodsList = apiClient.pods()
                .withoutField("status.phase", CloudAppConstant.PodPhase.SUCCEEDED)
                .withoutField("status.phase", CloudAppConstant.PodPhase.FAILED).list().getItems();
        nodeDTOS.parallelStream().forEach(dto -> {
            List<Pod> nodeNonTerminatedPodsList = nonTerminatedPodsList.stream()
                    .filter(p -> dto.getNodeName().equalsIgnoreCase(p.getSpec().getNodeName()))
                    .collect(Collectors.toList());
            // get resources allocated: sum requests and limit of all non terminated pods
            Map<String, BigDecimal> totalRequests = new HashMap<>(), totalLimits = new HashMap<>(); // 单位为cpu: m, memory: byte

            if (nodeNonTerminatedPodsList.isEmpty()) return;

            for (Pod pod : nodeNonTerminatedPodsList) {
                Map<String, BigDecimal> requests = new HashMap<>(), limits = new HashMap<>();
                for (Container container : pod.getSpec().getContainers()) {
                    if (container.getResources().getRequests()!=null)
                        for (Map.Entry<String, Quantity> stringQuantityEntry : container.getResources().getRequests().entrySet()) {
                            BigDecimal amount = Quantity.getAmountInBytes(stringQuantityEntry.getValue());
                            requests.compute(stringQuantityEntry.getKey(), (k,v) -> v==null?amount:v.add(amount));
                        }
                    if (container.getResources().getLimits()!=null)
                        for (Map.Entry<String, Quantity> stringQuantityEntry : container.getResources().getLimits().entrySet()) {
                            BigDecimal amount = Quantity.getAmountInBytes(stringQuantityEntry.getValue());
                            limits.compute(stringQuantityEntry.getKey(), (k,v) -> v==null?amount:v.add(amount));
                        }
                }
                // see https://github.com/kubernetes/kubernetes/issues/44697#issuecomment-296116592
                // pod 资源申请量取 max(max(init 容器request), sum of other containers)
                for (Container initContainer : pod.getSpec().getInitContainers()) {
                    if (initContainer.getResources().getRequests()!=null)
                        for (Map.Entry<String, Quantity> stringQuantityEntry : initContainer.getResources().getRequests().entrySet()) {
                            BigDecimal amount = Quantity.getAmountInBytes(stringQuantityEntry.getValue());
                            requests.computeIfPresent(stringQuantityEntry.getKey(), (k,v)->amount.compareTo(v)>0?amount:v);
                        }
                    if (initContainer.getResources().getLimits()!=null)
                        for (Map.Entry<String, Quantity> stringQuantityEntry : initContainer.getResources().getLimits().entrySet()) {
                            BigDecimal amount = Quantity.getAmountInBytes(stringQuantityEntry.getValue());
                            limits.computeIfPresent(stringQuantityEntry.getKey(), (k,v)->amount.compareTo(v)>0?amount:v);
                        }

                }
                // 汇总
                for (Map.Entry<String, BigDecimal> entry : requests.entrySet()) {
                    totalRequests.compute(entry.getKey(), (k, v) -> v == null ? entry.getValue() : v.add(entry.getValue()));
                }
                for (Map.Entry<String, BigDecimal> entry : limits.entrySet()) {
                    totalLimits.compute(entry.getKey(), (k, v) -> v == null ? entry.getValue() : v.add(entry.getValue()));
                }
            }

            Optional.ofNullable(totalRequests.get("cpu"))
                    .ifPresent(cpu -> dto.setCpuRequest(cpu.multiply(new BigDecimal(1000)).longValue()));
            Optional.ofNullable(totalLimits.get("cpu"))
                    .ifPresent(cpu -> dto.setCpuLimit(cpu.multiply(new BigDecimal(1000)).longValue()));
            Optional.ofNullable(totalRequests.get("memory"))
                    .ifPresent(mem -> dto.setMemRequest(mem.longValue()));
            Optional.ofNullable(totalLimits.get("memory"))
                    .ifPresent(mem -> dto.setMemLimit(mem.longValue()));

            // get allocatable
            dto.calculateAllocatableCpuAndMemory();
        });

        return nodeDTOS;
    }

    @Override
    public void createSecret(String namespace, String name, Map<String, String> data) {
        apiClient.secrets().inNamespace(namespace).createOrReplace(new SecretBuilder()
                .withMetadata(new ObjectMetaBuilder().withName(name).withNamespace(namespace).build())
                .withStringData(data)
                .build());
    }

    @Override
    public StatefulSet patchStatefulSet(StatefulSet patchSts) {
        ObjectMeta metadata = patchSts.getMetadata();
        StatefulSet patch = apiClient.apps().statefulSets().inNamespace(metadata.getNamespace())
                .withName(metadata.getName())
                .patch(patchSts);
        log.info("StatefulSet {}/{} patched, revision: {}", metadata.getNamespace(), metadata.getName(), patch.getMetadata().getResourceVersion());
        return patch;
    }

    @Override
    public void createOrReplaceSts(StatefulSet oldSts) {
        apiClient.apps().statefulSets().createOrReplace(oldSts);
    }

    @Override
    public void scaleSts(String name, String namespace, int replicas) {
        StatefulSet statefulSet = getStatefulSet(name, namespace);
        if (statefulSet == null) return;
        statefulSet.getSpec().setReplicas(replicas);
        apiClient.resource(statefulSet).createOrReplace();
    }

    @Override
    public void scaleDeploy(String namespace, String name, int num) {
        try {
            apiClient.apps().deployments().inNamespace(namespace).withName(name).scale(num);
        } catch (Exception e) {
            if (e instanceof KubernetesClientException) {
                if (((KubernetesClientException) e).getCode() == 404)
                    return;
            }
            throw new RuntimeException(e);
        }
    }

    @Override
    public void deleteStatefulset(String namespace, Label[] labels) {
        apiClient.apps().statefulSets().inNamespace(namespace).withLabels(Label.toMap(labels))
                .delete();
    }

    @Override
    public List<Lease> getLease(String finalOperatorNamespace) {
        return apiClient.leases().inNamespace(finalOperatorNamespace).list().getItems();
    }

    @Override
    public void updateGenericResource(GroupVersionKind gvk, String plural, String ns, String name, Map<String, String> specPatch) {
        ResourceDefinitionContext ctx = new ResourceDefinitionContext.Builder().withGroup(gvk.getGroup())
                .withKind(gvk.getKind())
                .withPlural(plural)
                .withVersion(gvk.getVersion())
                .withNamespaced(true).build();
        GenericKubernetesResource genericResource = apiClient.genericKubernetesResources(ctx)
                .inNamespace(ns).withName(name).get();


        Map<String, Object> spec = Optional.ofNullable(genericResource)
                .map( gr -> (Map<String, Object>) gr.getAdditionalProperties().get("spec"))
                .orElseThrow(() -> new CustomException(600, "没有找到要修改的k8s资源", String.format("没有找到要修改的k8s资源, %s/%s/%s", plural, ns, name)));
        for (String key : specPatch.keySet()) {
            spec.put(key, specPatch.get(key));
        }
        genericResource.getAdditionalProperties().put("spec", spec);
        GenericKubernetesResource patch = apiClient.genericKubernetesResources(ctx).inNamespace(ns).withName(name)
                .patch(PatchContext.of(PatchType.JSON_MERGE), genericResource);
        log.info("Patch generic resource {}, {}/{}, revision: {}", plural, ns, name, patch.getMetadata().getResourceVersion());

    }

    @Override
    public void deletePvc(Map<String, String> labels, String namespace) {
        if (apiClient.persistentVolumeClaims().inNamespace(namespace).withLabels(labels).delete()) {
            log.info("Delete pvc by labels: {}", labels.toString());
        }
    }

    /**
     * 集群下Node集合
     * @param kubeScheduler：当没指定调度策略kubeScheduler时返回全部Node列表，如果指定则返回可调度的Node列表
     * @return nodeDTO
     */
    public List<NodeDTO> listSimpleNodes(KubeScheduler kubeScheduler) {
        NodeList list = getNodeList(kubeScheduler);
        return list.getItems().stream().map(simpleNodeConverter()).collect(Collectors.toList());
    }

    /**
     *
     * 集群下NodeList 对象
     * 1. 根据标签到集群上查询node列表
     * 2. 对返回的node列表依据容忍配置进行污点过滤
     * @param kubeScheduler 当没指定调度策略kubeScheduler时返回全部Node列表，如果指定则返回可调度的Node列表
     */
    private NodeList getNodeList(KubeScheduler kubeScheduler) {
        if (kubeScheduler == null) {
            return apiClient.nodes().list();
        }
        NodeList list = apiClient.nodes().list();
        // 如果是污点模式，则仅根据污点过滤node ，安装时判断模式
        if (TAINT.equals(kubeScheduler.getMode())) {
            list.setItems(list.getItems().stream().filter(filterByTaint(kubeScheduler.getTaint())).collect(Collectors.toList()));
        } else {
            // 如果是标签容忍模式，则根据标签+容忍度过滤node
            list.setItems(list.getItems().stream()
                    .filter(filterByLabel(kubeScheduler.getSelector()))
                    .filter(filterByToleration(kubeScheduler.getToleration()))
                    .collect(Collectors.toList()));
        }
        return list;
    }

    /**
     * 根据标签过滤，筛选出符合标签规则的node
     */
    private Predicate<? super Node> filterByLabel(SelectorDTO[] selectors) {
        return node -> {
            // 依据节点亲和过滤
            Map<String, String> labels = node.getMetadata().getLabels();
            // 多个标签选择器之间是且的关系，需全部满足才能认为node可调度
            for (SelectorDTO selector : selectors) {
                if (StringUtils.isEmpty(selector.getOperator())) {
                    continue;
                }
                if (IN.equals(selector.getOperator())) {
                    String val = labels.get(selector.getKey());
                    if (!selector.getValues().contains(val)) {
                        return false;
                    }
                } else if (NOT_IN.equals(selector.getOperator())) {
                    String val = labels.get(selector.getKey());
                    if (selector.getValues().contains(val)) {
                        return false;
                    }
                } else if (EXISTS.equals(selector.getOperator())) {
                    if (!labels.containsKey(selector.getKey())) {
                        return false;
                    }
                } else if (DOESNOTEXIST.equals(selector.getOperator())) {
                    if (labels.containsKey(selector.getKey())) {
                        return false;
                    }
                } else if (GT.equals(selector.getOperator())) {
                    String val = labels.get(selector.getKey());
                    if (!NumberUtils.isCreatable(val) || (Integer.parseInt(val) <= Integer.parseInt(selector.getValues().get(0)))) {
                        return false;
                    }
                } else if (LT.equals(selector.getOperator())) {
                    String val = labels.get(selector.getKey());
                    if (!NumberUtils.isCreatable(val) || (Integer.parseInt(val) >= Integer.parseInt(selector.getValues().get(0)))) {
                        return false;
                    }
                }
            }
            return true;
        };
    }

    /**
     * 根据容忍度过滤：筛选出携带能够被容忍的污点的节点和没用污点的节点即可部署的节点
     */
    private Predicate<? super Node> filterByToleration(TolerationDTO[] tolerations) {
        return node -> {
            List<Taint> taints = node.getSpec().getTaints();
            if (CollectionUtils.isEmpty(taints)) return true;

            if (!ArrayUtils.isEmpty(tolerations)) {
                int matchCount = 0;
                for (TolerationDTO toleration : tolerations) {
                    String key = toleration.getKey();
                    String effect = toleration.getEffect();
                    if (EQUAL.equals(toleration.getOperator())) {
                        if (taints.stream().anyMatch(taint -> {
                            String value = toleration.getValue();
                            // 调度策略污点的value为空，NODE的对应污点的value必须也为空才认为NODE可用
                            // 调度策略污点的effect为空，NODE的对应污点的effect为空或和前者相同均认为NODE可用
                            return taint.getKey().equals(key)
                                    && (StringUtils.isEmpty(value) && StringUtils.isEmpty(taint.getValue()) || value.equals(taint.getValue()))
                                    && (StringUtils.isEmpty(effect) || effect.equals(taint.getEffect()));
                        })) {
                            matchCount++;
                        }
                    } else if (EXISTS.equals(toleration.getOperator())) {
                        // 两种特殊情况
                        // 1.容忍度能容忍任何污点
                        if (StringUtils.isEmpty(key)) {
                            matchCount += taints.size();
                            break;
                        }
                        // 2.effect 为空，则可以与所有键名相同的污点匹配
                        if (StringUtils.isEmpty(effect)) {
                            List<Taint> someKeyNoEffect = taints.stream().filter(taint -> taint.getKey().equals(key)).collect(Collectors.toList());
                            if (!CollectionUtils.isEmpty(someKeyNoEffect)) {
                                matchCount += someKeyNoEffect.size();
                                continue;
                            }
                        }
                        if (taints.stream().anyMatch(taint -> taint.getKey().equals(key) && taint.getEffect().equals(effect))) {
                            matchCount++;
                        }
                    }
                }
                if (matchCount < taints.size()) {
                    return false;
                }
            } else if (taints.stream().allMatch(taint -> NO_SCHEDULE.toString().equals(taint.getEffect())
                    || NO_EXECUTE.toString().equals(taint.getEffect()))) {
                // 当存在污点(effect：不可调度和不可执行)且没有容忍时设置node不可调度
                return false;
            }
            return true;
        };
    }

    /**
     * 根据污点过滤：筛选出携带指定污点的node,
     * 如果污点为空，筛选出没有污点的node
     */
    private Predicate<? super Node> filterByTaint(TaintDTO taint) {
        return node -> {
            List<Taint> taints = node.getSpec().getTaints();
            if (StringUtils.isEmpty(taint.getKey())) {
              if (taints.isEmpty())
                  return true;
              else
                  return false;
            }
            // 调度策略污点的value为空，NODE的对应污点的value必须也为空才认为NODE可用
            // 调度策略污点的effect为空，NODE的对应污点的effect为空或和前者相同均认为NODE可用
            return taints.stream().anyMatch(item -> item.getKey().equals(taint.getKey())
                    && (StringUtils.isEmpty(item.getValue()) && StringUtils.isEmpty(taint.getValue()) || item.getValue().equals(taint.getValue()))
                    && (StringUtils.isEmpty(taint.getEffect()) || item.getEffect().equals(taint.getEffect())));
        };
    }

    private Function<Node, NodeDTO> simpleNodeConverter() {
        return node -> {
            String clusterVersion = apiClient.getVersion().getGitVersion();
            NodeDTO dto = new NodeDTO();
            dto.setVersion(clusterVersion);
            dto.setNodeName(node.getMetadata().getName());
            // get role
            String expectLabel; // see https://github.com/kubernetes/kubernetes/blob/master/CHANGELOG/CHANGELOG-1.20.md
            if (!StringUtils.isEmpty(clusterVersion) && clusterVersion.compareTo("v1.20") >= 0) {
                expectLabel = "node-role.kubernetes.io/control-plane";
            } else {
                expectLabel = "node-role.kubernetes.io/master";
            }
            if (node.getMetadata().getLabels().containsKey(expectLabel)) {
                dto.setRole("master");
            } else {
                dto.setRole("worker");
            }
            // get status
            dto.setReady(node.getStatus().getConditions().stream()
                    .anyMatch(c -> CloudAppConstant.NodeStatus.READY.equalsIgnoreCase(c.getType()) && "true".equalsIgnoreCase(c.getStatus())));
            dto.setStatus(dto.isReady() ? CloudAppConstant.NodeStatus.READY : CloudAppConstant.NodeStatus.NOT_READY);
            Map<String, String> addressMap = node.getStatus().getAddresses().stream()
                    .collect(Collectors.toMap(a -> a.getType(), a -> a.getAddress(), (e, r) -> e + "," + r)); //多个InternalIp
            dto.setHostname(addressMap.get("Hostname"));

            dto.setInternal_ip(addressMap.get("InternalIP"));
            dto.setExternal_ip(addressMap.get("ExternalIP"));
            dto.setNodeIp(dto.getInternal_ip());
            dto.setOs(node.getStatus().getNodeInfo().getOperatingSystem());
            dto.setOs_core(node.getStatus().getNodeInfo().getKernelVersion());
            dto.setCri(node.getStatus().getNodeInfo().getContainerRuntimeVersion());
            dto.setDuration(DateUtil.formatDuration(Duration.between(LocalDateTime.parse(node.getMetadata().getCreationTimestamp(), DateTimeFormatter.ISO_DATE_TIME), LocalDateTime.now(ZoneId.of("Z")))));
            // get resource capacity
            dto.setCpuTotal(MetricUtil.getCpuMilliCores(node.getStatus().getAllocatable().get("cpu").toString()));
            dto.setMemTotal(MetricUtil.getResourceLongValue(node.getStatus().getAllocatable().get("memory").toString()));
            dto.setTaint(node.getSpec().getTaints());
            dto.setLabels(node.getMetadata().getLabels());
            dto.setKubeConfigName(this.kubeConfig.getName());
            //判断是否为master，是则不可调度
            dto.setSchedulable(node.getSpec().getUnschedulable() == null || node.getSpec().getUnschedulable());

            // 当dto.schedulable可调度且调度策略标签选择器不为空
/*
            if (dto.isSchedulable() && kubeScheduler != null && !ArrayUtils.isEmpty(kubeScheduler.getSelector())) {
                Map<String, String> labels = dto.getLabels();
                // 多个标签选择器之间是且的关系，需全部满足才能认为node可调度
                for (SelectorDTO selector : kubeScheduler.getSelector()) {
                    if (StringUtils.isEmpty(selector.getOperator())) {
                        continue;
                    }
                    if (IN.equals(selector.getOperator())) {
                        String val = labels.get(selector.getKey());
                        if (!selector.getValues().contains(val)) {
                            dto.setSchedulable(false);
                            break;
                        }
                    } else if (NOT_IN.equals(selector.getOperator())) {
                        String val = labels.get(selector.getKey());
                        if (selector.getValues().contains(val)) {
                            dto.setSchedulable(false);
                            break;
                        }
                    } else if (EXISTS.equals(selector.getOperator())) {
                        if (!labels.containsKey(selector.getKey())) {
                            dto.setSchedulable(false);
                            break;
                        }
                    } else if (DOESNOTEXIST.equals(selector.getOperator())) {
                        if (labels.containsKey(selector.getKey())) {
                            dto.setSchedulable(false);
                            break;
                        }
                    }
                }
            }
*/
            // 当dto.schedulable可调度且容忍配置存在且node存在污点时判断
           /* List<Taint> taints = dto.getTaint();
            if (dto.isSchedulable() && kubeScheduler != null && !ArrayUtils.isEmpty(kubeScheduler.getToleration()) && !CollectionUtils.isEmpty(taints)) {
                int matchCount = 0;
                for (TolerationDTO toleration : kubeScheduler.getToleration()) {
                    String key = toleration.getKey();
                    String effect = toleration.getEffect();
                    if (EQUAL.equals(toleration.getOperator())) {
                        if (taints.stream().anyMatch(taint -> taint.getKey().equals(key)
                                && taint.getValue().equals(toleration.getValue())
                                && taint.getEffect().equals(effect))) {
                            matchCount++;
                        }
                    } else if (EXISTS.equals(toleration.getOperator())) {
                        // 两种特殊情况
                        // 1.容忍度能容忍任何污点
                        if (StringUtils.isEmpty(key)) {
                            matchCount += taints.size();
                            break;
                        }
                        // 2.effect 为空，则可以与所有键名相同的污点匹配
                        if (StringUtils.isEmpty(effect)) {
                            List<Taint> someKeyNoEffect = taints.stream().filter(taint -> taint.getKey().equals(key)).collect(Collectors.toList());
                            if (!CollectionUtils.isEmpty(someKeyNoEffect)) {
                                matchCount += someKeyNoEffect.size();
                                continue;
                            }
                        }
                        if (taints.stream().anyMatch(taint -> taint.getKey().equals(key) && taint.getEffect().equals(effect))) {
                            matchCount++;
                        }
                    }
                }
                if (matchCount < taints.size()) {
                    dto.setSchedulable(false);
                }
            } else if(dto.isSchedulable() && !CollectionUtils.isEmpty(taints) && taints.stream().allMatch(taint -> NO_SCHEDULE.toString().equals(taint.getEffect())
                    || NO_EXECUTE.toString().equals(taint.getEffect()))){
                // 当dto.schedulable可调度且存在污点(effect：不可调度和不可执行)且没有容忍时设置node不可调度
                dto.setSchedulable(false);
            }*/

            return dto;
        };
    }
    @Override
    public Optional<HasMetadata> getByYaml(String yaml) {
        return Optional.ofNullable(apiClient.resource(yaml).get());
    }

    @Override
    public Optional<HasMetadata> getGenericResource(String apiVersion, String kind, String ns, String name) {
        return Optional.ofNullable(apiClient.genericKubernetesResources(apiVersion, kind).inNamespace(ns)
                .withName(name).get());
    }

    @Override
    public void deleteDeployment(String deploymentName, String namespace) {
        apiClient.apps().deployments().inNamespace(namespace).withName(deploymentName).delete();
    }

    @Override
    public OpenGaussClusterStatus getOGCrStatus(String name, String namespace) {
        ConfigMap configMap = apiClient.configMaps().inNamespace(namespace).withName(name).get();
        if (configMap == null)
            return null;
        Map<String, String> data = configMap.getData();
        String crStatus = data.get("crStatus");
        return YamlEngine.unmarshal(crStatus, OpenGaussClusterStatus.class);
    }

    @Override
    public void applyConfigMap(String name, String namespace, Map<String, String> data, Label... labels) {
        ConfigMap mysqlCnfConfigMap = new ConfigMap();
        ObjectMeta objectMeta = new ObjectMeta();
        Map<String, String> labelMap = Arrays.stream(labels)
                .collect(Collectors.toMap(Label::getName, Label::getValue));
        objectMeta.setLabels(labelMap);
//        objectMeta.setResourceVersion("v1");
        objectMeta.setName(name);
        mysqlCnfConfigMap.setMetadata(objectMeta);
        mysqlCnfConfigMap.setData(data);
        applyConfigMap(mysqlCnfConfigMap, namespace);
    }

    @Override
    public void mergeConfigMap(String name, String namespace, Map<String, String> data, Map<String, String> binaryData, Label... labels) {
        ConfigMap configMap = getConfigMap(name, namespace);
        Map<String, String> mergedData = new HashMap<>(configMap != null ? configMap.getData() != null ? configMap.getData() : Collections.emptyMap() : Collections.emptyMap());
        Map<String, String> mergedBData = new HashMap<>(configMap != null ? configMap.getBinaryData() != null ? configMap.getBinaryData() : Collections.emptyMap() : Collections.emptyMap());
        if (data != null) {
            mergedData.putAll(data);
        }
        if (binaryData != null) {
            mergedBData.putAll(binaryData);
        }
        ConfigMap cm = new ConfigMap();
        ObjectMeta objectMeta = new ObjectMeta();
        Map<String, String> labelMap = Label.toMap(labels);
        objectMeta.setLabels(labelMap);
        objectMeta.setName(name);
        cm.setMetadata(objectMeta);
        cm.setData(mergedData);
        cm.setBinaryData(mergedBData);
        applyConfigMap(cm, namespace);
    }

    @Override
    public void applyConfigMap(ConfigMap cm, String namespace) {
        ConfigMap cm_ = apiClient.configMaps().inNamespace(namespace).createOrReplace(cm);
        if(cm_ != null)
            log.info("Update configMap {}/{}", namespace, cm_.getMetadata().getName());
    }

    @Override
    public String getVersion() {
        return apiClient.getVersion().getGitVersion();
    }

    @Override
    public List<StorageClassDTO> listStorageClass() {
        List<StorageClass> items = apiClient.storage().storageClasses().list().getItems();
        if (items == null) return Collections.emptyList();
        return items.stream().map(o -> {
            StorageClassDTO dto = new StorageClassDTO();
            dto.setName(o.getMetadata().getName());
            dto.setProvisioner(o.getProvisioner());
            dto.setReclaimPolicy(o.getReclaimPolicy());
            dto.setVolumeBindingMode(o.getVolumeBindingMode());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public void deleteStorageClass(String scName) {
        List<StorageClassDTO> storageClassDTOS = listStorageClass();
        List<StorageClassDTO> scList = storageClassDTOS.stream().filter(storageClass -> scName.equals(storageClass.getName())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(scList)) {
            StorageClassDTO sc = scList.get(0);
            apiClient.storage().storageClasses().withField("metadata.name", scName).delete();
        }
    }

    @Override
    public void createOrReplaceNamespace(String name) {
        Namespace ns = new NamespaceBuilder().withNewMetadata()
                .withName(name)
                .addToLabels("type", "department")
                .endMetadata()
                .build();
        log.info("Created namespace: {} in {}", apiClient.namespaces().createOrReplace(ns).getMetadata().getName(),
                apiClient.getMasterUrl());
    }

    @Override
    public Boolean deleteNamespace(String namespace) {
        Boolean isDelete = apiClient.namespaces().withName(namespace).delete();
        log.info("delete namespace: {} ", namespace);
        return isDelete;
    }

    @Override
    public void createResourceQuota(String namespace, Map<String, String> quotaMap, String quotaName) {
        ResourceQuotaFluent.SpecNested<ResourceQuotaBuilder> resourceQuotaBuilderSpecNested = new ResourceQuotaBuilder()
                .withNewMetadata().withName(quotaName).endMetadata()
                .withNewSpec();
        for (Map.Entry<String, String> quota : quotaMap.entrySet()) {
            resourceQuotaBuilderSpecNested.addToHard(quota.getKey(), new Quantity(quota.getValue()));
        }
        ResourceQuota resourceQuota = resourceQuotaBuilderSpecNested.endSpec().build();
        log.info("Created resource-quota: {}" , apiClient.resourceQuotas().inNamespace(namespace)
                .createOrReplace(resourceQuota)
                .getMetadata().getName());
    }

    @Override
    public Set<String> getAllNamespaceName() {
        return apiClient.namespaces().list().getItems().stream()
                .map(n -> n.getMetadata().getName())
                .collect(Collectors.toSet());
    }

    @Override
    public Namespace getNamespaceName(String name) {
        return apiClient.namespaces().withName(name).get();
    }


    @Override
    public void updatePvcCapacity(String pvc, String namespace, String newCap) {
        String patch = String.format("{\"spec\":{\"resources\":{\"requests\":{\"storage\":\"%s\"}}}}", newCap);
        apiClient.persistentVolumeClaims().inNamespace(namespace).withName(pvc)
                .patch(patch); // patch by raw string. @since 5.4.0
    }

    @Override
    public void updatePvClaimRef(PersistentVolume pvByLabels, String namespace) {
        apiClient.persistentVolumes().replace(pvByLabels).getSpec().setClaimRef(null);
    }

    @Override
    public void updatePv(PersistentVolume pvByLabels) {
        apiClient.persistentVolumes().replace(pvByLabels);
    }
    @Override
    public void updatePvc(PersistentVolumeClaim pvcByLabels,String namespace) {
        apiClient.persistentVolumeClaims().inNamespace(namespace).createOrReplace(pvcByLabels);
    }

    @Override
    public Deployment getDeployments(String namespace, String name) {
        return apiClient.apps().deployments().inNamespace(namespace).withName(name).get();
    }

    @Override
    public void createOrReplaceDefaultLimitRange(String namespace, Map<String, String> limitRangeConfig) {
        String yaml = "apiVersion: v1\n" +
                "kind: LimitRange\n" +
                "metadata:\n" +
                "  name: mem-limit-range\n" +
                "spec:\n" +
                "  limits:\n" +
                "  - default:\n" +
                "      memory: ${container.limit.memory}\n" +
                "      cpu: ${container.limit.cpu}\n" +
                "    defaultRequest:\n" +
                "      memory: ${container.request.memory}\n" +
                "      cpu: ${container.request.cpu}\n" +
                "    type: Container";
        yaml = YamlUtil.evaluateTemplate(yaml, limitRangeConfig);
        ObjectMapper mapper = new YAMLMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        LimitRange limitRange = null;
        try {
            limitRange = mapper.readValue(yaml, LimitRange.class);
        } catch (IOException ignore) {
        }
        apiClient.limitRanges().inNamespace(namespace).createOrReplace(limitRange);
    }

    @Override
    public void deleteQuota(String namespace, String name) {
        if (apiClient.resourceQuotas().inNamespace(namespace).withName(name).delete()) {
            log.info("Delete quota {}/{}", namespace, name);
        }
    }

    @Override
    public void deleteLimitRange(String namespace) {
        apiClient.limitRanges().inNamespace(namespace).delete();
    }

    @Override
    public boolean checkRbac(String name, String namespace) {
        return apiClient.rbac().roleBindings().inNamespace(namespace).withName(name).get() != null;
    }

    @Override
    public void applyYaml(String yaml, String namespace) {
        if (StringUtils.isEmpty(yaml)) {
            throw new IllegalStateException("yaml cannot be null");
        }
        apiClient.load(new ByteArrayInputStream(yaml.getBytes(StandardCharsets.UTF_8)))
                .inNamespace(namespace).createOrReplace()
                .forEach(i->log.info(i.getKind() + "/" + i.getMetadata().getName() + " created"));
    }

    @Override
    public void applyYaml2(String yaml) {
        // fixme it not the same as apply -f, see https://github.com/fabric8io/kubernetes-client/issues/3896
        Optional.ofNullable(apiClient.resource(yaml).createOrReplace())
                .ifPresent(m -> log.info(m.getKind() + "/" + m.getMetadata().getName() + " created or updated"));
    }

    @Override
    public void applyYaml(String yaml) {
        if (StringUtils.isEmpty(yaml)) {
            throw new IllegalStateException("yaml cannot be null");
        }
        apiClient.load(new ByteArrayInputStream(yaml.getBytes(StandardCharsets.UTF_8)))
                .createOrReplace()
                .forEach(i->log.info(i.getKind() + "/" + i.getMetadata().getName() + " created"));
    }

    @Override
    public void deleteYaml(String yaml) {
        if (StringUtils.isEmpty(yaml)) {
            throw new IllegalStateException("yaml cannot be null");
        }
        apiClient.load(new ByteArrayInputStream(yaml.getBytes(StandardCharsets.UTF_8)))
                .delete();
    }

    @Override
    public void updatePodLabel(String namespace, String name, Label label) {
        Pod pod = apiClient.pods().inNamespace(namespace).withName(name).get();
        if (pod != null) {
            pod.getMetadata().getLabels().put(label.getName(), label.getValue());
            apiClient.pods().inNamespace(namespace).withName(name).replace(pod);
        }
    }

//    @Override
//    public GenericKubernetesResource applyCrYaml(ResourceDefinitionContext context, String yaml, String namespace) {
//        Resource<GenericKubernetesResource> cr = apiClient.genericKubernetesResources(context)
//                .load(new ByteArrayInputStream(yaml.getBytes(StandardCharsets.UTF_8)));
//        return cr.create();
//    }

    @Override
    public StatefulSet getStatefulSet(String name, String namespace) {
        return apiClient.apps().statefulSets().inNamespace(namespace).withName(name).get();
    }

    @Override
    public List<StatefulSet> listStatefulSetWithLables(String namespace, Label... labels) {
        return apiClient.apps().statefulSets().inNamespace(namespace).withLabels(Label.toMap(labels)).list().getItems();
    }

//    @Override
//    public GenericKubernetesResource applyCrYaml(ResourceDefinitionContext context, String yaml, String namespace) {
//        Resource<GenericKubernetesResource> cr = apiClient.genericKubernetesResources(context)
//                .load(new ByteArrayInputStream(yaml.getBytes(StandardCharsets.UTF_8)));
//        return cr.create();
//    }

    @Override
    public List<NodeDTO> listNodes() {
        NodeList list = apiClient.nodes().list();

        List<NodeDTO> nodeDTOList = list.getItems().stream().map(node -> {
            NodeDTO dto = simpleNodeConverter().apply(node);
            dto.setAnnotations(node.getMetadata().getAnnotations());
            dto.setAllocatables(node.getStatus().getAllocatable());
            // list images loaded on the node
            dto.setImages(node.getStatus().getImages().stream()
                    .map(image -> image.getNames().get(image.getNames().size()-1))
                    .collect(Collectors.toSet()));
            dto.setContainerImages(node.getStatus().getImages());
            // schedulable: 受保护节点的condition schedulingdisabled, 规约unschedulable标记为true, 否则为null
            Boolean unschedulable = node.getSpec().getUnschedulable();
            if (ObjectUtils.isEmpty(unschedulable)) {
                dto.setSchedulable(true);
            } else {
                dto.setSchedulable(!unschedulable);
            }
            log.debug("[选择node]Schedulable结果为:" + dto.isSchedulable());
            return dto;
        }).collect(Collectors.toList());
        log.debug("[选择node]返回node结果为:" + nodeDTOList.toString());
        return nodeDTOList;
    }

    @Override
    public List<NodeDTO> listMasterNodes(boolean isReady) {
        NodeList list = apiClient.nodes().list();
        String status = isReady ? CloudAppConstant.NodeStatus.READY : CloudAppConstant.NodeStatus.NOT_READY;

        List<NodeDTO> nodeDTOList = list.getItems().stream().map(node -> {
            NodeDTO dto = simpleNodeConverter().apply(node);
            dto.setAnnotations(node.getMetadata().getAnnotations());
            dto.setAllocatables(node.getStatus().getAllocatable());
            // list images loaded on the node
            dto.setImages(node.getStatus().getImages().stream()
                    .map(image -> image.getNames().get(image.getNames().size()-1))
                    .collect(Collectors.toSet()));
            dto.setContainerImages(node.getStatus().getImages());
            // schedulable: 受保护节点的condition schedulingdisabled, 规约unschedulable标记为true, 否则为null

            log.debug("[选择node]Schedulable结果为:" + dto.isSchedulable());
            return dto;
        }).filter(dto -> status.equalsIgnoreCase(dto.getStatus())
                && CloudAppConstant.NodeRole.MASTER.equalsIgnoreCase(dto.getRole())).collect(Collectors.toList());
        log.debug("[选择node]返回node结果为:" + nodeDTOList.toString());
        return nodeDTOList;
    }

    @Override
    public List<NodeDTO> listReadyMasterNodes() {
        return listMasterNodes(true);
    }

    @Override
    public String getReadyMasterIp() {
        return listReadyMasterNodes().get(0).getNodeIp();
    }

    @Override
    public List<Service> listService(String namespace) {
        if (namespace == null) {
            return apiClient.services().inAnyNamespace().list().getItems();
        } else {
            return apiClient.services().inNamespace(namespace).list().getItems();
        }
    }

    @Override
    public Service getService(String namespace, String serviceName) {
        return apiClient.services().inNamespace(namespace).withName(serviceName).get();
    }

    @Override
    public void deleteService(String svcName, String namespace) {
        Boolean delete = apiClient.services().inNamespace(namespace).withName(svcName).delete();
        if (delete != null && delete) {
            log.info("Delete svc {}/{}", namespace, svcName);
        }
    }

    @Override
    public Service patchNodePortSvc(Service svcInK8s, int newPort) {
        svcInK8s.getSpec().getPorts().get(0).setNodePort(newPort);
        Service patch = apiClient.services().inNamespace(svcInK8s.getMetadata().getNamespace())
                .withName(svcInK8s.getMetadata().getName())
                .patch(svcInK8s);
        log.info("Service {}/{} patched, revision: {}", patch.getMetadata().getNamespace(),
                patch.getMetadata().getName(), patch.getMetadata().getResourceVersion());
        return patch;
    }

    @Override
    public void replaceService(String serviceName, Integer nodePort, String namespace, String type) {
        Service service = apiClient.services().inNamespace(namespace).withName(serviceName).get();
        ServicePort servicePort = service.getSpec().getPorts().get(0);
        servicePort.setNodePort(nodePort);
        apiClient.services().inNamespace(namespace).withName(serviceName).createOrReplace(
                new ServiceBuilder().withApiVersion(service.getApiVersion())
                        .withKind(service.getKind())
                        .withMetadata(
                                new ObjectMetaBuilder().withLabels(service.getMetadata().getLabels())
                                        .withNamespace(namespace)
                                        .withName(serviceName)
                                        .build()
                        )
                        .withSpec(
                                new ServiceSpecBuilder().withType(type)
                                        .withPorts(servicePort)
                                        .withSelector(service.getSpec().getSelector())
                                        .build()
                        )
                        .build()
        );
    }

    @Override
    public void replaceService(
            String serviceName, Integer nodePort, String namespace,
            String type,Map<String, String> annotations,String loadbalancerIp) {
        Service service = apiClient.services().inNamespace(namespace).withName(serviceName).get();
        ServicePort servicePort = service.getSpec().getPorts().get(0);
        servicePort.setNodePort(nodePort);
        apiClient.services().inNamespace(namespace).withName(serviceName).createOrReplace(
                new ServiceBuilder().withApiVersion(service.getApiVersion())
                        .withKind(service.getKind())
                        .withMetadata(
                                new ObjectMetaBuilder().withLabels(service.getMetadata().getLabels())
                                        .withNamespace(namespace)
                                        .withName(serviceName)
                                        .withAnnotations(annotations)
                                        .build()
                        )
                        .withSpec(
                                new ServiceSpecBuilder().withType(type)
                                        .withPorts(servicePort)
                                        .withSelector(service.getSpec().getSelector())
                                        .withLoadBalancerIP(loadbalancerIp)
                                        .build()
                        )
                        .build()
        );
    }

    @Override
    public void createService(String name, Integer writePort, String namespace, int i) {

    }

    @Override
    public Service createService(
            String name, String namespace, Integer nodePort, int port, Map<String, String> labelMap) {
        return apiClient.services().inNamespace(namespace).createOrReplace(
                new ServiceBuilder().withMetadata(new ObjectMetaBuilder().withName(name).build())
                        .withSpec(new ServiceSpecBuilder().withType(CloudAppConstant.ServiceType.NODE_PORT)
                                .withSelector(labelMap)
                                .withPorts(new ServicePortBuilder().withPort(port).withNodePort(nodePort).build())
                                .build())
                        .build()
        );
    }

    @Override
    public void createLoadBalancerService(String name, String namespace, int nodePort,
                                          int port, Map<String, String> labelMap,
                                          Map<String, String> annotationMap, String loadbalancerIp) {
        apiClient.services().inNamespace(namespace).createOrReplace(
                new ServiceBuilder().withMetadata(new ObjectMetaBuilder().withAnnotations(annotationMap).withName(name).build())
                        .withSpec(new ServiceSpecBuilder().withType(CloudAppConstant.ServiceType.LOAD_BALANCER)
                                .withSelector(labelMap)
                                .withLoadBalancerIP(loadbalancerIp)
                                .withPorts(new ServicePortBuilder().withPort(port).withNodePort(nodePort).build())
                                .build())
                        .build()
        );
    }

    @Override
    public void createService(String name, Integer nodePort, String namespace,
                              int port, Map<String, String> label, Map<String, String> selector) {
        apiClient.services().inNamespace(namespace).createOrReplace(
                new ServiceBuilder().withApiVersion("v1")
                        .withKind("Service")
                        .withMetadata(new ObjectMetaBuilder()
                                .withName(name)
                                .withLabels(label)
                                .withNamespace(namespace).build())
                        .withSpec(new ServiceSpecBuilder()
                                .withType("NodePort")
                                .withSelector(selector)
                                .withPorts(new ServicePortBuilder()
                                        .withPort(port)
                                        .withNodePort(nodePort)
                                        .withTargetPort(new IntOrString(port))
                                        .withProtocol("TCP").build())
                                .build())
                        .build()
        );
    }

    @Override
    public void createClusterService(String name, String namespace, Integer port, String endpointName,
                                     Map<String,String> labels, Map<String,String> selectors) {
        apiClient.services().inNamespace(namespace).createOrReplace(  new ServiceBuilder().withApiVersion("v1")
                .withKind("Service")
                .withMetadata(new ObjectMetaBuilder()
                        .withName(name)
                        .withLabels(labels)
                        .withNamespace(namespace).build())
                .withSpec(new ServiceSpecBuilder()
                        .withType("ClusterIP")
                        .withSelector(selectors)
                        .withPorts(new ServicePortBuilder()
                                .withPort(port)
                                .withName(endpointName)
                                .withTargetPort(new IntOrString(port))
                                .withProtocol("TCP").build())
                        .build())
                .build());
    }

    @Override
    public <T extends CustomResource> T createCustomResource(T t, Class<T> clz, String namespace) {
        return apiClient.<T>customResources(clz).inNamespace(namespace).create(t);
    }

    @Override
    public <T extends CustomResource> T createOrReplaceCustomResource(T t, Class<T> clz, String namespace) {
        return apiClient.<T>customResources(clz).inNamespace(namespace).createOrReplace(t);
    }

    @Override
    public <T extends CustomResource> T createCustomResource(T t, Class<T> clz) {
        T t1 = apiClient.resources(clz).inNamespace(t.getMetadata().getNamespace()).create(t);
        log.info("Created custom resource {}: {}/{}", clz.getSimpleName(), t1.getMetadata().getNamespace(), t1.getMetadata().getName());
        return t1;
    }

    @Override
    public <T extends CustomResource> void deleteCustomResource(T t, Class<T> clz) {
        Boolean delete = apiClient.resources(clz).inNamespace(t.getMetadata().getNamespace()).withName(t.getMetadata().getName()).delete();
        if (delete) {
            log.info("Delete custom resource {}: {}/{}", clz.getSimpleName(), t.getMetadata().getNamespace(), t.getMetadata().getName());
        }
    }

    @Override
    public <T extends CustomResource> boolean deleteCustomResource(String namespace, String crName, Class<T> crType) {
        Boolean delete = apiClient.resources(crType).inNamespace(namespace).withName(crName).delete();
        if (delete) {
            log.info("Delete custom resource {}/{}/{}", crType.getSimpleName(), namespace, crName);
        }
        return delete;
    }

    /**
     * 更新cr. 更新为覆盖更新 而不是合并更新
     *
     * @param t 要更新的cr对象, 需要首先通过api获取, 在此基础上做修改
     * @return
     */
    @Override
    public <T extends CustomResource> T updateCustomResource(T t, Class<T> type) {
        T replace = apiClient.resources(type).inNamespace(t.getMetadata().getNamespace()).withName(t.getMetadata().getName()).replace(t);
        log.info("Update custom resource {}/{} of type {}", replace.getMetadata().getName(), replace.getMetadata().getNamespace(), t.getKind());
        return replace;
    }

    @Override
    public <T extends CustomResource> List<T> listCustomResource(Class<T> type) {
        try {
            return apiClient.resources(type).inAnyNamespace().list().getItems();
        } catch (Exception e) {
            log.error("list cr error", e);
            return Collections.emptyList();
        }
    }

    @Override
    public <T extends CustomResource> List<T> listCustomResource(Class<T> type, String namespace) {
        try {
            if (namespace == null)
                return listCustomResource(type);
            return apiClient.resources(type).inNamespace(namespace).list().getItems();
        } catch (Exception e) {
            log.error("list cr error", e);
            return Collections.emptyList();
        }
    }

    @Override
    public <T extends CustomResource> T listCustomResource(Class<T> type, String name, String namespace) {
        return apiClient.resources(type).inNamespace(namespace).withName(name).get();
    }

    @Override
    public <T extends CustomResource> KubernetesResourceList<T> listCustomResource(Class<T> type, String namespace, Label... labels) {
        Map<String, String> labelMap = labels != null ?
                Arrays.stream(labels).collect(Collectors.toMap(l->l.getName(), l->l.getValue())) :
                Collections.emptyMap();
        return apiClient.resources(type).inNamespace(namespace).withLabels(labelMap).list();
    }

    @Override
    public MixedOperation<DistributedRedisCluster, KubernetesResourceList<DistributedRedisCluster>, Resource<DistributedRedisCluster>> getDistributedRedisCluster() {
        return apiClient.resources(DistributedRedisCluster.class);
    }

    @Override
    public KubernetesResourceList<DistributedRedisCluster> listDistributedRedisCluster(String namespace) {
        return apiClient.resources(DistributedRedisCluster.class).inNamespace(namespace).list();
    }

    @Override
    public MixedOperation<RedisCluster, KubernetesResourceList<RedisCluster>, Resource<RedisCluster>> getRedisClusterClient() {
        return apiClient.resources(RedisCluster.class);
    }

    @Override
    public KubernetesResourceList<RedisCluster> listRedisCluster(String namespace) {
        return apiClient.resources(RedisCluster.class).inNamespace(namespace).list();
    }


    @Override
    public MixedOperation<MongoDBCommunity, KubernetesResourceList<MongoDBCommunity>, Resource<MongoDBCommunity>> getMongoDBClient() {
        return apiClient.resources(MongoDBCommunity.class);
    }

    @Override
    public KubernetesResourceList<MongoDBCommunity> listMongoDB(String namespace) {
        if (StringUtils.isEmpty(namespace)) {
            return apiClient.resources(MongoDBCommunity.class).list();
        }
        return apiClient.resources(MongoDBCommunity.class).inNamespace(namespace).list();
    }

    @Override
    public KubernetesResourceList<MySQLHA> listMysqlHA(String namespace) {
        if (StringUtils.isEmpty(namespace)) {
            return apiClient.resources(MySQLHA.class).list();
        }
        return apiClient.resources(MySQLHA.class).inNamespace(namespace).list();
    }

    @Override
    public MixedOperation<OpenGaussCluster, KubernetesResourceList<OpenGaussCluster>, Resource<OpenGaussCluster>> getOpenGaussClusterClient() {
        return apiClient.resources(OpenGaussCluster.class);
    }

    @Override
    public KubernetesResourceList<OpenGaussCluster> listOpenGaussCluster(String namespace) {
        if (StringUtils.isEmpty(namespace)) {
            return apiClient.resources(OpenGaussCluster.class).list();
        }
        return apiClient.resources(OpenGaussCluster.class).inNamespace(namespace).list();
    }


    @Override
    public String execCmd(String namespace, String pod, String container, String... command) throws Exception {
        return execCmd(namespace, pod, container, 600, command);
    }

    @Override
    public String execCmd(String namespace, String pod, String container, boolean tty, int timeout, String... command) throws Exception {
        return execCmd(tty, namespace, pod, container, timeout, command);

    }

    @Override
    public String execCmd(String namespace, String pod, String container, int timeout, String... command) throws Exception {
        return execCmd(false, namespace, pod, container, timeout, command);
    }
    private String execCmd(boolean tty, String namespace, String pod, String container, int timeout, String... command) throws Exception {
        CompletableFuture<String> data = new CompletableFuture<>();
        if (log.isDebugEnabled()) {
            log.debug(Arrays.toString(command));
        }
        try (ExecWatch execWatch = newExec(tty, namespace, pod, container, data, command)) {
            String output;
            if (timeout > 0)
                output = data.get(timeout, TimeUnit.SECONDS);
            else output = data.get();
            if (log.isDebugEnabled())
                log.debug(output);
            return output;
        } catch (Exception e) {
            log.error(String.format("exec returns error %s/%s, cmd %s", namespace, pod, Arrays.toString(command)), e);
            throw e;
        }
    }
    @Override
    public void execCmdOneway(String namespace, String pod, String container, String... command) throws Exception {
//        CompletableFuture<String> data = new CompletableFuture<>();
        try (ExecWatch execWatch = newExec(false,namespace, pod, container, null, command)) {
        } catch (Exception e) {
            throw e;
        }
        // get result non-blocking

    }

    private ExecWatch newExec(boolean tty, String namespace, String podName, String container, CompletableFuture<String> data, String... cmd) {
        // container name. If omitted, the first container in the pod will be chosen
        if (StringUtils.isEmpty(container)) {
            Pod pod = apiClient.pods().inNamespace(namespace).withName(podName).get();
            container = pod.getSpec().getContainers().get(0).getName();
        }

        log.info("kubectl exec -it -n {} {} -c {} -- {}", namespace ,podName, container, Arrays.toString(cmd));
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ByteArrayOutputStream baos_ = new ByteArrayOutputStream();
        if (tty)
            return apiClient.pods()
                    .inNamespace(namespace)
                    .withName(podName)
                    .inContainer(container)
                    .writingOutput(baos)
                    .writingError(baos_)
                    .writingErrorChannel(baos_)
                    .withTTY()
                    .usingListener(data != null ? new KubeExecSimpleListener(data, baos, baos_) : null)
                    .exec(cmd);
        else
            return apiClient.pods()
                    .inNamespace(namespace)
                    .withName(podName)
                    .inContainer(container)
                    .writingOutput(baos)
                    .writingError(baos_)
                    .writingErrorChannel(baos_)
                    .usingListener(data != null ? new KubeExecSimpleListener(data, baos, baos_) : null)
                    .exec(cmd);

    }

    @Override
    public InputStream getFile(String namespace, String podName, String container, String file) {
        return apiClient.pods()
                .inNamespace(namespace)
                .withName(podName)
                .inContainer(container)
                .file(file)
                .read();
    }

    @Override
    public Boolean uploadFile(String namespace, String podName, String container, String destFile, Path localFile) {
        return apiClient.pods()
                .inNamespace(namespace)
                .withName(podName)
                .inContainer(container)
                .file(destFile)
                .upload(localFile);
    }

    @Override
    public PersistentVolumeClaim getPvc(String namespace, String name) {
        return apiClient.persistentVolumeClaims().inNamespace(namespace).withName(name).get();
    }

    @Override
    public PersistentVolume getPv(String name) {
        return apiClient.persistentVolumes().withName(name).get();
    }


    @Override
    public ServiceAccountList getServiceAccountList(String namespace) {
        return apiClient.serviceAccounts().inNamespace(namespace).list();
    }

    @Override
    public void editServiceAccount(String namespace, ServiceAccount serviceAccount) {
        apiClient.serviceAccounts().inNamespace(namespace)
                .patch(serviceAccount);
    }

    @Override
    public Secret getSecret(String namespace, String secretName) {
        Optional<Secret> optional = apiClient.secrets().inNamespace(namespace).list().getItems().stream()
                .filter(item -> item.getMetadata().getName().equals(secretName)).findFirst();
        return optional.orElse(null);
    }

    @Override
    public boolean existLimitRange(String namespace) {
        return !apiClient.limitRanges().inNamespace(namespace).list().getItems().isEmpty();
    }

    @Override
    public void createBatch(String kind, HasMetadata... items) {
        KubernetesList kubernetesList = new KubernetesList();
        kubernetesList.setItems(Arrays.asList(items));
        kubernetesList.setApiVersion(items[0].getApiVersion());
        kubernetesList.setKind(kind);
        Arrays.stream(items).parallel()
                .forEach(item -> {
                    apiClient.resource(item).createOrReplace();
                    log.info("Create {} {}/{}", item.getKind(),
                            item.getMetadata().getNamespace() == null ? "-" : item.getMetadata().getNamespace(),
                            item.getMetadata().getName());
                });
        // create(kubernetesList) 实际在一个连接里执行
//        Optional.ofNullable(apiClient.lists().create(kubernetesList).getItems())
//                .ifPresent(created -> created.forEach(item -> log.info("Create {} {}/{}", item.getKind(),
//                        item.getMetadata().getNamespace() == null ? "-" : item.getMetadata().getNamespace(), item.getMetadata().getName())));
    }

    @Override
    public void deleteBatch(String kind, HasMetadata... items) {
        KubernetesList kubernetesList = new KubernetesList();
        kubernetesList.setItems(Arrays.asList(items));
        kubernetesList.setApiVersion(items[0].getApiVersion());
        kubernetesList.setKind(kind);
        if (apiClient.lists().delete(kubernetesList)) {
            Arrays.stream(items).forEach(item -> log.info("Delete {} {}/{}", item.getKind(),
                    item.getMetadata().getNamespace() == null ? "-" : item.getMetadata().getNamespace(), item.getMetadata().getName()));
        }
    }


    @Override
    public boolean deletePod(String namespace, String podName) {
        Boolean delete = apiClient.pods().inNamespace(namespace).withName(podName).delete();
        if (delete) {
            log.info("Delete pod {}/{}", namespace, podName);
        }
        return delete;
    }


    @Override
    public Node getNode(String nodeName) {
        return apiClient.nodes().withName(nodeName).get();

    }

    @Override
    public List<PodDTO> listPodMetricsOnly(String namespace, Label[] labelOfPod) {
        return null;
    }

    @Override
    public KubernetesClient getClient() {
        return apiClient;
    }

    @Override
    public KubeConfig getConfig() {
        return kubeConfig;
    }
}
