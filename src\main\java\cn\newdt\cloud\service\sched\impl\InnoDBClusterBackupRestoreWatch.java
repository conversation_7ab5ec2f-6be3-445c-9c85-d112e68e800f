package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.domain.cr.InnoDBCluster;
import cn.newdt.cloud.domain.dmp.BinlogBackupHis;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.mapper.BackupMapper;
import cn.newdt.cloud.mapper.RestoreMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.BackupService;
import cn.newdt.cloud.service.CloudDatabaseUserService;
import cn.newdt.cloud.service.RestoreServiceImpl;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.CloudBackupStorageVO;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.UserUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableSet;
import io.fabric8.kubernetes.api.model.apps.StatefulSet;
import io.fabric8.kubernetes.client.CustomResource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.DatasourceConstant.CLOUD_BACKUP_HIS;
import static cn.newdt.cloud.constant.DatasourceConstant.SCHEMA;
import static cn.newdt.cloud.constant.ScheduleConstant.JOB_DATA_KEY_CHANGE_ID;

@Slf4j
// TODO not support ftp yet
public class InnoDBClusterBackupRestoreWatch extends OpsProcessorContext implements OpsPostProcessor<InnoDBCluster> {

    public static final String PREPARED = "prepared";
    private static final String RESTORE_COMMITTED = "restored";
    private static final Object RESTARTED = "restarted";
    private static final Object RESTORE_FIN = "restore_fin";
    public static final String XBK_CONTAINER = "xtrabackup";
    public static final String BINLOG_COMMITTED = "binlog_committed";
    @Autowired
    private BackupService backupService;
    @Autowired
    private RestoreServiceImpl restoreServiceImpl;
    @Autowired
    private CloudDatabaseUserService databaseUserService;
    @Autowired
    private BackupMapper backupMapper;
    @Autowired
    private RestoreMapper restoreMapper;
    @Autowired
    private BackupUtil backupUtil;

    @Override
    public OpsResultDTO postProcess(TriggerHis triggerHis) throws Exception {
        OpsResultDTO.Builder result = OpsResultDTO.builder().stopJob(false);
        ResourceChangeHis his = resourceChangeHisService.get(Integer.parseInt(triggerHis.getJobDataMap().get(JOB_DATA_KEY_CHANGE_ID)));
        ActionEnum actionEnum = ActionEnum.actionTypeOf(his.getAction());
        Map<String, String> dataMap = triggerHis.returnMergedJobDataMap();
        String appId = dataMap.get("appId");
        CloudApp app = appService.get(Integer.valueOf(appId));
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        //获取超时时间
        Integer backupTimeOut = backupUtil.getBackupTimeOut(app.getId());
        String backupHisIdStr = dataMap.get("backupHisId");
        BackupHis backupHis = backupService.get(Integer.valueOf(backupHisIdStr));
        if (actionEnum == ActionEnum.BACKUP) {
            String checkFile = dataMap.get("checkFile");
            String binlogBackupFlag = dataMap.get("binlogBackupFlag");
            String latestBinlogCodes = dataMap.get("latestBinlogCodes");
            BackupResult backupResult = JsonUtil.toObject(BackupResult.class, backupHis.getMessage());
            // 备份校验结束可以直接跳过
            if (backupResult == null || !backupResult.check()) {
                watchBackupResult(result, checkFile, app, backupHis, kubeClient, backupTimeOut);
            }
            if (backupResult.check() || result.isSuccessful()) {
                if (StringUtils.isNotEmpty(binlogBackupFlag) && Boolean.parseBoolean(binlogBackupFlag)) {
                    result.stopJob(false).status(StatusConstant.RUNNING);
                    watchBinlogBackup(result, backupHis, kubeClient, app, latestBinlogCodes);
                }
            }
            // update data backhis finally
        }
        // issue prepare at primary pod
        // check prepare result
        // issue restore at all pods
        // check restore result
        // restart all pods after complete
        if (actionEnum == ActionEnum.RESTORE) {
            String restoreHisIdStr = dataMap.get("restoreHisId");
            String restoreTime = dataMap.get("restoreTime");
            RestoreHis restoreHis = restoreServiceImpl.get(Integer.valueOf(restoreHisIdStr));

            watchRestoreResult(triggerHis, result, app, backupHis, kubeClient, backupTimeOut, restoreHis);
            // perform restore-in-time
            if (result.isSuccessful() && StringUtils.isNotEmpty(restoreTime)) {
                result.stopJob(false).status(StatusConstant.RUNNING);
                watchBinlogRestoreResult(result, restoreTime, backupHis, kubeClient, app, restoreHis);
            }
            if (result.isStopped()) {
                OpsResultDTO build = result.build();
                restoreHis.setMessage(build.getMsg());
                restoreHis.setStatus(build.getStatus());
                restoreHis.setEndTime(Timestamp.valueOf(LocalDateTime.now()));
                restoreServiceImpl.update(restoreHis);
            }
        }
        if (result.isStopped())
            appService.handleWatchResult(app.getId(), result.isSuccessful());
        return result.build();
    }

    /**
     * 创建目录 /backup/mysql/mgr/{namespace}/{crname}/binlog/{timestamp}
     * 遍历扫描出binlog目录新增的binlog文件
     * 拷贝binlog文件到备份目录
     *
     */
    private void watchBinlogBackup(OpsResultDTO.Builder result, BackupHis backupHis, KubeClient kubeClient, CloudApp app, String latestBinlogCodes) {
        String message = backupHis.getMessage();
        Map backupMessage = JsonUtil.toObject(message, new TypeReference<Map>() {});
        Optional<Object> opt = Optional.ofNullable(backupMessage.get("binlog_backup_his_id"));
        PodDTO primaryPod = backupUtil.getBackupPodMgr(app, kubeClient).orElse(null);
        if (primaryPod == null) result.msg("无法确定主库来应用binlog进行数据恢复，将重试");

        if (opt.isPresent()){
            log.info("[mysql备份binlog文件备份]  latestBinlogCodes{}", latestBinlogCodes);
            String binlogResult;
            try {
                // 返回样式 mysql-bin.000001 20240202101010mysql-bin.000001
                binlogResult = kubeClient.execCmd(app.getNamespace(), primaryPod.getPodName(), "mysql", "sh", "-c",
                        "cd /var/lib/mysql/; ls -l --time-style='+%Y%m%d%H%M%S' "+ app.getCrName() + ".* | awk -F'[ ]+' '{print $7,$6_$7}' ");
                log.info("[mysql备份binlog文件备份] 本次备份 binlogResult:{}", binlogResult);
            } catch (Exception e) {
                log.error("[mysql备份binlog文件备份] 查找binlog文件信息失败，信息为:{} 详细信息为:{}",e.getMessage(), e);
                throw new CustomException(600, "备份binlog失败！");
            }
            if (StringUtils.isEmpty(binlogResult)) {
                throw new CustomException(600, "binlog文件为空！");
            }
            // key binlog文件名称，value binlog文件的识别码
            Map<String, String> binlogMap = Arrays.stream(binlogResult.trim().split("\n")).map(item -> item.split("\\s+"))
                    .collect(Collectors.toMap(arr -> arr[0], arr -> arr[1]));
            // 去除当前使用的binlog和索引文件
            binlogMap.remove(app.getCrName() + ".index");
            binlogMap.remove(Collections.max(binlogMap.keySet()));
            Collection<String> uploadBinlogNames = binlogMap.keySet();
            // 对比上次binlog文件，找出差异文件上传
            if (!StringUtils.isEmpty(latestBinlogCodes)) {
                List<String> latestBinlogCodeList = Arrays.stream(latestBinlogCodes.split(",")).collect(Collectors.toList());
                uploadBinlogNames = binlogMap.entrySet().stream().filter(item -> !latestBinlogCodeList.contains(item.getValue()))
                        .map(Map.Entry::getKey).collect(Collectors.toSet());
            }
            log.info("[mysql备份binlog文件备份] binlog {}", JsonUtil.toJson(uploadBinlogNames));

            String timestamp = backupHis.getStartTime().toLocalDateTime().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

            CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
            String binlogBackupPodPath = String.format("/mnt/share/mysql/mgr/%s/%s/binlog/%s/", app.getNamespace(), app.getCrName(), timestamp);
            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
                //临时挂载
                String mountCmd = "bash /scripts/mount-remote-storage.sh true " + CloudAppConstant.OperatorStorageType.NFS + " " + mountPath + " /mnt/share";
                try {
                    kubeClient.execCmd(app.getNamespace(), primaryPod.getPodName(), "xtrabackup", "sh", "-c", mountCmd);
                    log.info("[mysql备份binlog临时挂载]命令为：" + mountCmd);
                } catch (Exception e) {
                    log.error("[mysql备份binlog临时挂载]临时挂载失败，信息为：" + e.getMessage() + "     详细信息为:" + e);
                    throw new CustomException(600, "备份binlog失败！！");
                }
            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                String mountCmd = createMountCmd(cloudBackupStorageVO);
                try {
                    kubeClient.execCmd(app.getNamespace(), primaryPod.getPodName(), "xtrabackup", "sh", "-c", mountCmd);
                    log.info("[mysql备份binlog临时挂载]命令为：" + mountCmd);
                } catch (Exception e) {
                    log.error("[mysql备份binlog临时挂载]临时挂载失败，信息为：" + e.getMessage() + "     详细信息为:" + e);
                    throw new CustomException(600, "备份binlog失败！！");
                }
            } else {
                throw new CustomException(600, "备份binlog失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
            }

            try {
                kubeClient.execCmdOneway(app.getNamespace(), primaryPod.getPodName(), "xtrabackup", "sh", "-c", "mkdir -p " + binlogBackupPodPath);
                for (String binlog : uploadBinlogNames) {
                    String cmd = String.format("cp /var/lib/mysql/%s %s ", binlog, binlogBackupPodPath);
                    log.info("[mysql备份binlog文件备份]命令为：" + cmd);
                    kubeClient.execCmdOneway(app.getNamespace(), primaryPod.getPodName(), "xtrabackup", "sh", "-c", cmd);
                }
            } catch (Exception e) {
                log.error("[mysql备份binlog文件备份]备份binlog失败，信息为：" + e.getMessage() + "     详细信息为:" + e);
                throw new CustomException(600, "备份binlog失败！！");
            }
            //取消临时挂载
            String umountCmd = "umount -lf /mnt/share";
            try {
                kubeClient.execCmd(app.getNamespace(), primaryPod.getPodName(), "xtrabackup", "sh", "-c", umountCmd);
                log.info("[mysql备份binlog取消临时挂载]命令为：" + umountCmd);
            } catch (Exception e) {
                log.error("[mysql备份binlog取消临时挂载]临时挂载失败，信息为：" + e.getMessage() + "     详细信息为:" + e);
                throw new CustomException(600, "备份binlog失败！！");
            }
            BinlogBackupHis binlogBackupHis = backupService.listBinlogBackupHis(app.getId());
            binlogBackupHis.setFileCodes(String.join(",", binlogMap.values()));
            binlogBackupHis.setEndTime(new Timestamp(System.currentTimeMillis()));
            binlogBackupHis.setBackupLength(0);
            binlogBackupHis.setClusterId(app.getId());
            binlogBackupHis.setClusterName(app.getName());
            binlogBackupHis.setMsg("Y");
            binlogBackupHis.setStatus("0");
            binlogBackupHis.setBackupDir(binlogBackupPodPath);
            backupMapper.updateBinlogBackupHis(UserUtil.getSchema(), DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, binlogBackupHis);
            result.stopJob(true).status(StatusConstant.SUCCESS);
        } else {
            BinlogBackupHis binlogBackupHis = new BinlogBackupHis();
            binlogBackupHis.setStartTime(backupHis.getStartTime());
            binlogBackupHis.setClusterId(backupHis.getAppId());
            binlogBackupHis.setClusterName(backupHis.getAppName());
            binlogBackupHis.setFirstFileName(backupHis.getBinlogName());
            backupService.insertBinlogBackupHis(binlogBackupHis);
            BackupResult backupResult = JsonUtil.toObject(BackupResult.class, backupHis.getMessage());
            backupResult.setBinlog_backup_his_id(binlogBackupHis.getId());
            backupHis.setMessage(JsonUtil.toJson(backupResult));
            backupService.update(backupHis);
        }
    }

    private static String createMountCmd(CloudBackupStorageVO cloudBackupStorageVO) {
        String region = cloudBackupStorageVO.getRegion();
        //临时挂载
        String mountCmd = "s3fs -o passwd_file=/data/s3pwd " +
                "-o use_path_request_style";
        if (StringUtils.isNotBlank(region))
                mountCmd += " -o endpoint=" + region;
        mountCmd = mountCmd +" -o url=" + cloudBackupStorageVO.getServer() +
                " -o allow_other" +
                " -o no_check_certificate " +
                cloudBackupStorageVO.getBucket() +
                " /mnt/share" +
                " -o ssl_verify_hostname=0";
        return mountCmd;
    }

    public static void main(String[] args) {
            CloudBackupStorageVO storageVO = new CloudBackupStorageVO();
            storageVO.setServer("s3.example.com");
            storageVO.setBucket("my-bucket");
            storageVO.setRegion("ny-east-1");
            String expectedMountCmd = "s3fs -o passwd_file=/data/s3pwd " +
                    "-o use_path_request_style " +
                    "-o endpoint=ny-east-1 " +
                    "-o url=s3.example.com -o allow_other -o no_check_certificate my-bucket /mnt/share -o ssl_verify_hostname=0";
            String actualMountCmd = createMountCmd(storageVO);

        System.out.println(actualMountCmd.equals(expectedMountCmd));

        for (int i = 0; i < expectedMountCmd.length(); i++) {
            if (expectedMountCmd.charAt(i) != actualMountCmd.charAt(i)) {
                System.out.println("Mismatch at index " + i);
                System.out.println("Expected: " + expectedMountCmd.charAt(i));
                System.out.println("Actual: " + actualMountCmd.charAt(i));
            }
        }
    }

    private void watchBinlogRestoreResult(OpsResultDTO.Builder result, String restoreTime, BackupHis backupHis,
                                          KubeClient kubeClient, CloudApp app, RestoreHis restoreHis) {
        Map restoreMsg = StringUtils.isEmpty(restoreHis.getMessage()) ? new HashMap() : JsonUtil.toObject(Map.class, restoreHis.getMessage());
        String binlogFlag = (String) restoreMsg.get(BINLOG_COMMITTED);
        String chkFile = "binlog_restore_" + restoreHis.getRestoreHisId();
        PodDTO primaryPod = backupUtil.getBackupPodMgr(app, kubeClient).orElse(null);

        if (primaryPod == null) result.msg("无法确定主库来应用binlog进行数据恢复，将重试");
        if ("Y".equalsIgnoreCase(binlogFlag)) {
            Object primaryPodName = restoreMsg.get("primaryPod");
            PrepareResult chkFileContent = getChkFileContent(result, chkFile, app, kubeClient, primaryPodName.toString(), PrepareResult.class);
            if (chkFileContent != null && "success".equalsIgnoreCase(chkFileContent.getStatus())) {
                result.msg("restore to time complete").stopJob(true).status(StatusConstant.SUCCESS);
            } else if ("failed".equalsIgnoreCase(chkFileContent.getStatus()) || chkFileContent.getReason().contains("Not Found Any required binlog file")) {
                result.msg(chkFileContent.getReason()).stopJob(true).status(StatusConstant.FAIL);
            }
        } else {
            // 脚本参数  binlogPath=$1 binlogName=$2 binlogPos=$3 restoreTime=$4 chkFile=$5
            // get related binlog backup his
            // 获取恢复时间点后的一次binlog备份记录，拿到binlog文件所在时间戳目录。里面存放着binlog恢复所需的文件
            BinlogBackupHis binlogBackupHis = backupService.getBinlogBackupHisGtStartTime(backupHis.getAppId(), String.valueOf(backupHis.getEndTime()));
            // binlogBackupHis 为null代表，restoreTime恢复时间点后没有备份操作
            if (binlogBackupHis != null){
                CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
                String backupDir = binlogBackupHis.getBackupDir();
                String startBinlogName = backupHis.getBinlogName();
                String binlogPos = backupHis.getBinlogPos();
                String script = "";
                if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                    String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
                    script = "/scripts/mysql-binlog-restore.sh " +
                            backupDir + " " +
                            startBinlogName + " " +
                            binlogPos + " " +
                            restoreTime + " " +
                            chkFile + " " +
                            CloudAppConstant.OperatorStorageType.NFS + " " +
                            mountPath;
                } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                    String region = cloudBackupStorageVO.getRegion();
                    if (org.springframework.util.StringUtils.isEmpty(region)) {
                        region = "\"\"";
                    }
                    script = "/scripts/mysql-binlog-restore.sh " +
                            backupDir + " " +
                            startBinlogName + " " +
                            binlogPos + " " +
                            restoreTime + " " +
                            chkFile + " " +
                            CloudAppConstant.StorageType.S3 + " " +
                            cloudBackupStorageVO.getServer() + " " +
                            region + " " +
                            cloudBackupStorageVO.getBucket() + " " +
                            cloudBackupStorageVO.getAccessKey() + " " +
                            cloudBackupStorageVO.getSecretKey();
                } else {
                    throw new CustomException(600, "备份失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                }

                try {
                    kubeClient.execCmdOneway(app.getNamespace(), primaryPod.getPodName(), XBK_CONTAINER, "sh", "-c", script);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
            restoreMsg.put(BINLOG_COMMITTED, "Y");
            restoreMsg.put("primaryPod", primaryPod.getPodName());
            restoreHis.setMessage(JsonUtil.toJson(restoreMsg));
            restoreServiceImpl.update(restoreHis);
        }
    }

    private OpsResultDTO.Builder watchRestoreResult(TriggerHis triggerHis, OpsResultDTO.Builder result, CloudApp app, BackupHis backupHis, KubeClient kubeClient, Integer backupTimeOut, RestoreHis restoreHis) throws Exception {
        // pre-check if each step has been completed
        Map restoreMsg = StringUtils.isEmpty(restoreHis.getMessage()) ? new HashMap() : JsonUtil.toObject(Map.class, restoreHis.getMessage());
        String isPrepared = (String) restoreMsg.get(PREPARED);
        String isRestoreCommitted = (String) restoreMsg.get(RESTORE_COMMITTED);
        String isRestarted = (String) restoreMsg.get(RESTARTED);
        String isRestoreFin = (String) restoreMsg.get(RESTORE_FIN);
        result.msg("prepare: " + isPrepared);
        result.msg("restore: " + isRestoreCommitted);
        result.msg("restarted: " + isRestarted);
        result.msg("xbk_restore_complete: " + isRestoreFin);
        // 恢复已完成，直接返回
        if ("Y".equalsIgnoreCase(isRestoreFin)) {
            return result.stopJob(true).status(StatusConstant.SUCCESS);
        }
        // 恢复已提交，检查恢复结果文件
        String restoreCheckFile = "restore_" + restoreHis.getRestoreHisId();
        List<PodDTO> allPods = kubeClient.listPod(app.getNamespace(), AppKind.MYSQL_MGR.labels(app.getCrName()));
        if ("Y".equalsIgnoreCase(isRestoreCommitted)) {
            watchRestoreStepResult(triggerHis, result, app, kubeClient, backupTimeOut, restoreHis, restoreMsg, isRestarted, restoreCheckFile, allPods, backupHis);
            return result;
        }

        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        BackupResult backupResult = JsonUtil.toObject(BackupResult.class, backupHis.getMessage());
        String backupFilePath = backupResult.getFtpBackupPath();
        String fileName = backupHis.getFileName();
        String incrPath = "";
        if (backupHis.getBackupType().equals(CloudAppConstant.BackupType.incre)) {
            BackupHis fullBackupHis = backupMapper.getBackupHisByBaseTime(SCHEMA, CLOUD_BACKUP_HIS, backupHis.getBaseTime(), AppKind.MYSQL_MGR.getKind(), backupHis.getAppId(), "full");
            fileName = fullBackupHis.getFileName();
            incrPath = backupHis.getFileName();
        }
        String fullPath = fileName;

        result.msg("检测prepare步骤");
        String prepareCheckFile = "prepare_" + restoreHis.getRestoreHisId();
        String preparePodName = app.getCrName() + "-" + 0; // 共享存储，仅进行一次prepare
        if (StringUtils.isEmpty(isPrepared) || !"Y".equalsIgnoreCase(isPrepared)) {
            for (PodDTO pod : allPods) {
                String podName = pod.getPodName();
                String prepareCmd = "";
                if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                    String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
                    if (org.springframework.util.StringUtils.isEmpty(incrPath)) {
                        incrPath = "\"\"";
                    }
                    prepareCmd = "/scripts/mysql-restore-prepare.sh "
                            + prepareCheckFile + " "
                            + CloudAppConstant.OperatorStorageType.NFS + " "
                            + mountPath + " "
                            + backupHis.getAppName() + " "
                            + fullPath + " "
                            + incrPath + " ";
                } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                    String region = cloudBackupStorageVO.getRegion();
                    if (org.springframework.util.StringUtils.isEmpty(region)) {
                        region = "\"\"";
                    }
                    if (org.springframework.util.StringUtils.isEmpty(incrPath)) {
                        incrPath = "\"\"";
                    }
                    prepareCmd = "/scripts/mysql-restore-prepare.sh "
                            + prepareCheckFile + " "
                            + CloudAppConstant.StorageType.S3 + " "
                            + cloudBackupStorageVO.getServer() + " "
                            + backupHis.getAppName() + " "
                            + fullPath + " "
                            + incrPath + " "
                            + region + " "
                            + cloudBackupStorageVO.getBucket() + " "
                            + cloudBackupStorageVO.getAccessKey() + " "
                            + cloudBackupStorageVO.getSecretKey();
                } else {
                    throw new CustomException(600, "恢复失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                }
                result.msg("执行" + prepareCmd);
                try {
                    kubeClient.execCmdOneway(app.getNamespace(), podName, XBK_CONTAINER, "sh", "-c", prepareCmd);
                } catch (Exception e) {
                    log.error("", e);
                    result.msg("prepare commit failed " + e.getMessage()).status(StatusConstant.FAIL).stopJob(true);
                    return result;
                }
            }
            //对备份历史放入已提交上传的标记
            restoreMsg.put(PREPARED, "Y");
            //插入mes
            restoreHis.setMessage(JsonUtil.toJson(restoreMsg));
            restoreServiceImpl.update(restoreHis);
        } else if ("Y".equalsIgnoreCase(isPrepared)) {
            result.msg("prepare 提交成功");
            PrepareResult prepareResult = getChkFileContent(result, prepareCheckFile, app, kubeClient, preparePodName, PrepareResult.class);
            if (prepareResult == null) return result;
            if ("success".equals(prepareResult.getStatus())) {
                // issue restore

                for (PodDTO pod : allPods) {
                    String podName = pod.getPodName();
                    String restoreCmd = "";
                    if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                        try {
                            String realRestoreCheckFilePerPod = getRealRestoreCheckFilePerPod(restoreCheckFile, podName);
                            String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
                            if (org.springframework.util.StringUtils.isEmpty(incrPath)) {
                                incrPath = "\"\"";
                            }
                            restoreCmd = "/scripts/mysql-restore.sh " +
                                    realRestoreCheckFilePerPod + " " +
                                    CloudAppConstant.OperatorStorageType.NFS + " " +
                                    mountPath + " " +
                                    backupHis.getAppName() + " " +
                                    fullPath + " " +
                                    incrPath;
                            result.msg(podName + " 上执行" + restoreCmd);
                            kubeClient.execCmdOneway(app.getNamespace(), podName, XBK_CONTAINER, "sh", "-c", restoreCmd);
                        } catch (Exception e) {
                            log.error("", e);
                            result.msg(podName + " restore commit failed " + e.getMessage()).status(StatusConstant.FAIL).stopJob(true);
                            return result;
                        }
                    } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                        try {
                            String realRestoreCheckFilePerPod = getRealRestoreCheckFilePerPod(restoreCheckFile, podName);
                            if (org.springframework.util.StringUtils.isEmpty(incrPath)) {
                                incrPath = "\"\"";
                            }
                            String region = cloudBackupStorageVO.getRegion();
                            if (org.springframework.util.StringUtils.isEmpty(region)) {
                                region = "\"\"";
                            }
                            restoreCmd = "/scripts/mysql-restore.sh " +
                                    realRestoreCheckFilePerPod + " " +
                                    CloudAppConstant.StorageType.S3 + " " +
                                    cloudBackupStorageVO.getServer() + " " +
                                    backupHis.getAppName() + " " +
                                    fullPath + " " +
                                    incrPath + " " +
                                    region + " " +
                                    cloudBackupStorageVO.getBucket() + " " +
                                    cloudBackupStorageVO.getAccessKey() + " " +
                                    cloudBackupStorageVO.getSecretKey();
                            result.msg(podName + " 上执行" + restoreCmd);
                            kubeClient.execCmdOneway(app.getNamespace(), podName, XBK_CONTAINER, "sh", "-c", restoreCmd);
                        } catch (Exception e) {
                            log.error("", e);
                            result.msg(podName + " restore commit failed " + e.getMessage()).status(StatusConstant.FAIL).stopJob(true);
                            return result;
                        }
                    } else {
                        throw new CustomException(600, "恢复失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                    }
                }

                restoreMsg.put(RESTORE_COMMITTED, "Y");
                //插入mes
                restoreHis.setMessage(JsonUtil.toJson(restoreMsg));
                restoreServiceImpl.update(restoreHis);
            } else if ("failed".equals(prepareResult.getStatus())) {
                result.stopJob(true).status(StatusConstant.FAIL).msg(prepareResult.getReason());
            }
        }
        return result;
    }

    private void watchRestoreStepResult(TriggerHis triggerHis, OpsResultDTO.Builder result, CloudApp app, KubeClient kubeClient, Integer backupTimeOut, RestoreHis restoreHis, Map restoreMsg, String isRestarted, String restoreCheckFile, List<PodDTO> allPods, BackupHis backupHis) throws Exception {
        Map<String, Long> restoreResultGroup = allPods.stream().collect(Collectors.groupingBy(podDTO -> {
            RestoreResult restoreResult = getChkFileContent(result,
                    getRealRestoreCheckFilePerPod(restoreCheckFile, podDTO.getPodName()),
                    app, kubeClient, podDTO.getPodName(), RestoreResult.class);
            if (restoreResult != null) {
                return restoreResult.getStatus();
            } else {
                return "Not finish yet";
            }
        }, Collectors.counting()));
        if (ObjectUtils.isNotEmpty(restoreResultGroup.get("success")) && restoreResultGroup.get("success") == allPods.size()) {
            if (!"Y".equalsIgnoreCase(isRestarted)) {
                List<CloudDatabaseUser> admin = databaseUserService.findDbUser(app.getId(), CloudAppConstant.UserRole.ADMIN);

//                Secret secret = kubeClient.getSecret(app.getNamespace(), MGRUtil.getSecretName(app.getCrName(), admin.isEmpty() ? "root" : admin.get(0).getUsername()));
//                String username = new String(Base64.getDecoder().decode(secret.getData().get(MGRUtil.Constants.SECRET_USERNAME_KEY).getBytes()));
//                String password = new String(Base64.getDecoder().decode(secret.getData().get(MGRUtil.Constants.SECRET_PASSWORD_KEY).getBytes()));
                String username = "k8sadmin";
                String password = "k8sadmin";
                String restartCmd = String.format("mysql -u%s -p%s -e 'restart'", username, password);
                for (PodDTO pod : allPods) {
                    try {
                        kubeClient.execCmd(app.getNamespace(), pod.getPodName(), AppKind.MYSQL_MGR.getContainerName(), "sh", "-c", restartCmd);
                        Thread.sleep(10 * 1000);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
                // restore secret(避免跨应用恢复失败)
                restoreMsg.put(RESTARTED, "Y");
                //插入mes
                restoreHis.setMessage(JsonUtil.toJson(restoreMsg));
                restoreServiceImpl.update(restoreHis);
            } else {
                // todo make sure restart success

                InnoDBCluster cr = kubeClient.listCustomResource(InnoDBCluster.class, app.getCrName(), app.getNamespace());
                OpsResultDTO.Builder watchResult = evalOpsResult(triggerHis, cr, app, kubeClient);
                if (watchResult.isSuccessful()) {
                    String allPodNames = allPods
                            .stream().map(p -> p.getPodName()).collect(Collectors.joining(","));
                    restoreHis.setPodName(allPodNames);
                    restoreHis.setEndTime(Timestamp.valueOf(LocalDateTime.now()));
                    restoreHis.setRestoreLength((int) restoreHis.getStartTime().toLocalDateTime().until(restoreHis.getEndTime().toLocalDateTime(), ChronoUnit.SECONDS));
//                    restoreHis.setStatus(StatusConstant.SUCCESS);
                    restoreMsg.put(RESTORE_FIN, "Y");
                    restoreHis.setMessage(JsonUtil.toJson(restoreMsg));
                    restoreServiceImpl.update(restoreHis);
                }
                OpsResultDTO build = watchResult.build();
                result.msg(build.getMsg()).status(build.getStatus()).stopJob(build.getStopJob());
            }
        } else if (restoreResultGroup.get("failed") > 0) {
            result.status(StatusConstant.FAIL).stopJob(true);
        } else {
            if (backupUtil.checkBackupAndRestoreTimeout(restoreHis.getStartTime(), new Date(), backupTimeOut)) {
                restoreHis.setStatus(StatusConstant.FAIL);
                restoreHis.setMessage("恢复超时！");
                restoreServiceImpl.update(restoreHis);
                app.setStatus(CloudAppConstant.AppStatus.FAILED);
                app.setCrRun("");
                appService.update(app);
                result.stopJob(true).status(StatusConstant.FAIL);
            }
        }
    }

    private static String getRealRestoreCheckFilePerPod(String restoreCheckFile, String podName) {
        return restoreCheckFile + "_" + podName.substring(podName.lastIndexOf("-") + 1);
    }

    private static <T> T getChkFileContent(OpsResultDTO.Builder result, String checkFile, CloudApp app, KubeClient kubeClient, String podName, Class<T> clz) {
        String catCmd = "cat /data/tmp/" + checkFile;
        String checkFileContent = null;
        try {
            checkFileContent = kubeClient.execCmd(app.getNamespace(), podName, XBK_CONTAINER, "sh", "-c", catCmd);
            result.msg("checkFileContent: " + checkFileContent);
        } catch (Exception e) {
            result.msg("check file error: " + e.getMessage());
        }
        return StringUtils.isEmpty(checkFileContent) ? null : JsonUtil.toObject(clz, checkFileContent);
    }


    private void watchBackupResult(OpsResultDTO.Builder result, String checkFile, CloudApp app, BackupHis backupHis, KubeClient kubeClient, Integer backupTimeOut) {
        String catCmd = "cat /data/tmp/" + checkFile;
        String checkFileContent = null;
        try {
            checkFileContent = kubeClient.execCmd(app.getNamespace(), backupHis.getPodName(), XBK_CONTAINER, "sh", "-c", catCmd);
        } catch (Exception e) {
            result.msg("check file error: " + e.getMessage());
        }
        if (StringUtils.isNotEmpty(checkFileContent)) {
            if (checkFileContent.contains("Failed to mount")) {
                backupHis.setStatus(StatusConstant.FAIL);
                backupHis.setMessage(checkFileContent);
                backupService.update(backupHis);
                app.setStatus(CloudAppConstant.AppStatus.FAILED);
                app.setCrRun("");
                appService.update(app);
                result.stopJob(true).status(StatusConstant.FAIL);
            }
            BackupResult backupResult = JsonUtil.toObject(BackupResult.class, checkFileContent);
            if (backupResult == null) {
                result.msg("未能解析备份结果: " + checkFileContent);
                return;
            }
            result.msg(backupResult.getStatus());
            if ("complete".equals(backupResult.getStatus())) {

                String filePath = backupResult.getFilePath().replaceAll("^/mnt/share/", ""); // remove /backup which is mount path of xbk container
                backupResult.setFtpBackupPath(filePath); // 记录在his.message，恢复时的下载路径为 ftpBackupPath + fileName
                long dataSize = KubeClientUtil.dfUsage(kubeClient, backupHis.getPodName(), AppKind.MYSQL_MGR.getContainerName(), app.getNamespace(), "/var/lib/mysql"); // todo magic literal
                backupResult.setDataSize(Collections.singletonMap(backupHis.getPodName(), dataSize)); // 记录备份时数据大小(todo 移到备份提交前记录）
                backupHis.setStatus(StatusConstant.SUCCESS);
                backupHis.setMessage(JsonUtil.toJson(backupResult));
                backupHis.setStartTime(Timestamp.valueOf(backupResult.getStartTime()));
                backupHis.setEndTime(Timestamp.valueOf(backupResult.getEndTime()));
                backupHis.setDuration((int) ((backupHis.getEndTime().getTime() - backupHis.getStartTime().getTime()) / 1000));
                backupHis.setFileName(backupResult.fileName);
                backupHis.setBinlogName(backupResult.getBinlogName()); // binlog 恢复起始文件
                backupHis.setBinlogPos(backupResult.getBinlogPos()); // binlog 恢复起始位置
                if ("full".equalsIgnoreCase(backupHis.getBackupType())) {
                    backupHis.setBaseTime(backupResult.baseTime);
                } else {
                    // 增备指定全备的time
                    BackupHis fullBackupHis = backupMapper
                            .getLastSuccessFullBackupHis(SCHEMA, CLOUD_BACKUP_HIS, backupHis.getAppId());
                    backupHis.setBaseTime(fullBackupHis.getBaseTime());
                }
                backupHis.setVersion(app.getVersion());
//                backupHis.setBinlogName(backupResult.getBackupEndLsn());
                backupService.update(backupHis);
                app.setStatus(CloudAppConstant.AppStatus.SUCCESS);
                app.setCrRun("");
                appService.update(app);
                result.stopJob(true).msg(JsonUtil.formatJson(backupResult)).status(StatusConstant.SUCCESS);
            } else if ("failed".equals(backupResult.getStatus())) {
                backupHis.setStatus(StatusConstant.FAIL);
                backupHis.setMessage(checkFileContent);
                backupService.update(backupHis);
                app.setStatus(CloudAppConstant.AppStatus.FAILED);
                app.setCrRun("");
                appService.update(app);
                result.stopJob(true).status(StatusConstant.FAIL);
            } else {
                if (backupUtil.checkBackupAndRestoreTimeout(backupHis.getStartTime(), new Date(), backupTimeOut)) {
                    backupHis.setStatus(StatusConstant.FAIL);
                    backupHis.setMessage("备份超时！");
                    backupService.update(backupHis);
                    app.setStatus(CloudAppConstant.AppStatus.FAILED);
                    app.setCrRun("");
                    appService.update(app);
                    result.stopJob(true).status(StatusConstant.FAIL);
                }
            }
        }
    }

    //{
//    {
//        "status": "complete",
//            "fileName": "20231108.091501.full.xbstream",
//            "filePath": "/backup/mgr/yan-test/mycluster-18/backup",
//            "fileSize": 126,
//            "startTime": "2023-11-08 09:15:01",
//            "endTime": "2023-11-08 09:15:05",
//            "baseTime": "20231108091501",
//            "binlogName": "mycluster-18.000004",
//            "binlogPos": 277
//    }
    //}
    @Data
    private static class BackupResult {
        private String status;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startTime;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endTime;
        private String filePath;
        private String fileName;
        private String fileSize;
        private String baseTime;
        private String binlogName;
        private String binlogPos;
        private String ftpBackupPath;
        private Map dataSize;
        private Integer binlog_backup_his_id;

        public boolean check() {
            return "complete".equals(status);
        }
    }

    @Data
    private static class RestoreResult {
        private String status;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startTime;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endTime;
        private String filePath;
        private String fileName;
        private String fileSize;
        private String baseTime;
        private String binlogName;
        private String binlogPos;
        private String ftpBackupPath;
        private Map dataSize;

        public boolean check() {
            return "success".equals(status);
        }
    }

    //    {"status": "success","reason": "Restore prepare complete"}
    @Data
    private static class PrepareResult {
        private String status;
        private String reason;
    }

    @Data
    private static class BinlogBackResult {
        private String status;
        private String reason;
        private String firstBinLogName;
        private String lastBinLogName;
    }

    public OpsResultDTO.Builder evalOpsResult(TriggerHis triggerHis, CustomResource genericCr, CloudApp app, KubeClient kubeClient) throws Exception {
        OpsResultDTO.Builder result = OpsResultDTO.builder().stopJob(false);
        if (null == genericCr) {
            String crLoseFailMsg = String.format("实例名称：==%s，实例信息丢失！", app.getCrName());
            log.error(crLoseFailMsg);
            return result.stopJob(true).status(StatusConstant.FAIL).msg(crLoseFailMsg);
        }

        Map<String, String> dataMap = triggerHis.returnMergedJobDataMap();
        String oldRevision = dataMap.get(MGRUtil.REVISION_JOB_DATA_KEY);

        InnoDBCluster actCr = (InnoDBCluster) genericCr;
        InnoDBCluster expCr = YamlEngine.unmarshal(app.getCrRun(), InnoDBCluster.class);

        InnoDBCluster.InnoDBClusterStatus statusObj = actCr.getStatus();

        if (null == statusObj)
            return result.msg(String.format("实例名称：==%s，暂未获取到当前实例的状态信息...", app.getCrName()));

        if (statusObj.getCluster() != null) {
            InnoDBCluster.InnoDBClusterStatusEnum status = statusObj.getCluster().getStatus();
            if (status == null) return result;
            // 非缩容操作成功条件： 实例数和在线数一致且状态为ONLINE
            // 缩容操作成功条件： 实例数和在线数一致且 状态为ONLINE或ONLINE_PARTIAL
            if ((status == InnoDBCluster.InnoDBClusterStatusEnum.ONLINE ||
                    (triggerHis.getTriggerName().startsWith("Scale_In") && status == InnoDBCluster.InnoDBClusterStatusEnum.ONLINE_PARTIAL))
                    && actCr.getSpec().getInstances().equals(actCr.getStatus().getCluster().getOnlineInstances())) {
                if (AppUtil.compareBean(actCr.getSpec(), expCr.getSpec(), null,
                        ImmutableSet.of("instances", "podSpec", "datadirVolumeClaimTemplates", "secretName"))) {
                    StatefulSet statefulSet = kubeClient.getStatefulSet(app.getCrName(), app.getNamespace());
                    if (KubeClientUtil.validateStsStatus(statefulSet, oldRevision)) {
                        result.stopJob(true).status(StatusConstant.SUCCESS);
                    } else {
                        result.msg("statefulset update not complete");
                    }
                } else {
                    result.msg("app's spec not match");
                }
            } else {
                result.msg("the cluster was in phase:" + status);
            }
        }
        return result;
    }

}
