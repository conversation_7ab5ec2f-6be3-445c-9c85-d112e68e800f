variables:
  DEV_APPLICATION_NAME: cloud-service
  DEV_ES_IP_PLACE_HOLDER: *************
  DEV_MYSQL_SHINDB_CLOUD_SERVER_PORT_HOLDER: 31232
  DEV_MYSQL_SHINDB_CLOUD_SERVER_IP_HOLDER: *************
  DEV_MYSQL_DISCOVERY_SERVER_ADDR: **************:8848
  DEV_MYSQL_CONFIG_SERVER_ADDR: **************:8848
  DEV_PG_SHINDB_CLOUD_SERVER_PORT_HOLDER: 31233
  DEV_PG_SHINDB_CLOUD_SERVER_IP_HOLDER: *************
  DEV_PG_DISCOVERY_SERVER_ADDR: *************:8848
  DEV_PG_CONFIG_SERVER_ADDR: *************:8848
  TEST_APPLICATION_NAME: cloud-service
  TEST_ES_IP_PLACE_HOLDER: *************
  TEST_MYSQL_SHINDB_CLOUD_SERVER_PORT_HOLDER: 31231
  TEST_MYSQL_SHINDB_CLOUD_SERVER_IP_HOLDER: *************
  TEST_MYSQL_DISCOVERY_SERVER_ADDR: ************:8848
  TEST_MYSQL_CONFIG_SERVER_ADDR: ************:8848
  TEST_PG_SHINDB_CLOUD_SERVER_PORT_HOLDER: 31234
  TEST_PG_SHINDB_CLOUD_SERVER_IP_HOLDER: *************
  TEST_PG_DISCOVERY_SERVER_ADDR: *************:8848
  TEST_PG_CONFIG_SERVER_ADDR: *************:8848
  TEST_DEMO_APPLICATION_NAME: cloud-service
  TEST_DEMO_ES_IP_PLACE_HOLDER: *************
  TEST_DEMO_MYSQL_SHINDB_CLOUD_SERVER_PORT_HOLDER: 31230
  TEST_DEMO_MYSQL_SHINDB_CLOUD_SERVER_IP_HOLDER: *************
  TEST_DEMO_MYSQL_DISCOVERY_SERVER_ADDR: **************:8848
  TEST_DEMO_MYSQL_CONFIG_SERVER_ADDR: **************:8848

stages:
  - build
  - deploy-feature
  - deploy-develop
  - deploy-test
#  - deploy-release

# 使用 maven build java 程序
jar-build:
  stage: build
  script:
    - docker run --rm -v "$PWD":/usr/src/mymaven -v /opt/.m2:/root/.m2 -w /usr/src/mymaven harbor.shindata.com/tools/maven:3.9.1-amazoncorretto-8 mvn -DskipTests clean package
    - ls target
    - docker build -t $CI_REGISTRY/$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA .
    - docker push $CI_REGISTRY/$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
    - docker rmi $CI_REGISTRY/$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
  tags:
    - shindb
    - cloud
  only: #feature Branch & !Scheduling Pipelines
    - /^feature.*/
    - develop
    - test
#  artifacts:
#    name: "jar"
#    paths:
#      - target/cloud-service.jar

# 使用 docker build & push 镜像
#docker-push:
#  stage: build
#  tags:
#    - shindb
#    - cloud
#  script:
#    - ls target
#    - docker build -t $CI_REGISTRY/$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA .
#    - docker push $CI_REGISTRY/$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
#    - docker rmi $CI_REGISTRY/$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
#  dependencies:
#    - jar-build
#  only: #当前的 branch 名称，用于触发 CI/CD
#    - feature/1021083_lianzb

deploy-feat:
  stage: deploy-feature
  tags:
    - shindb
    - cloud
  #  image: kustomize/kustomize:v3.8.7
  before_script:
    - git remote set-url origin http://${CI_USERNAME}:${CI_PASSWORD}@***********/newdt/shindb/shindatacloudservice.git
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "lianzb"
  script:
    - git checkout -B $CI_BUILD_REF_NAME
    - |
      sed -i "s%spring\.application\.name=.*%spring.application.name=cloud-service-${CI_COMMIT_REF_SLUG}%g" src/main/resources/bootstrap.properties
      sed -i "s%nodePort:.*%nodePort: $(grep 'spring.cloud.nacos.discovery.port' src/main/resources/bootstrap-${CI_COMMIT_REF_SLUG}.properties | awk -F'=' '{print $2}')%g" deploy/dbpaas-cloud-service-svc.yaml
      sed -i "s%branch:.*%branch: ${CI_COMMIT_REF_SLUG}%g" kustomization.yaml
      sed -i "/- src\/main\/resources\/bootstrap-.*/d" kustomization.yaml
      sed -i "s%value: default.*%value: default,${CI_COMMIT_REF_SLUG}%g" profiles-patch.yaml
    #    - kustomize edit set namespace $GITLAB_USER_NAME-feat
    - kustomize edit add patch --path profiles-patch.yaml --name dbpaas-cloud-service-dep --kind Deployment --group apps --version v1
    - kustomize edit set namespace cloud-dev
    - kustomize edit set namesuffix -- -$CI_COMMIT_REF_SLUG
    - kustomize edit set image HARBOR_URL_PLACE_HOLDER/shindatacloud/cloud-service=$CI_REGISTRY/$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
#    - kustomize edit set image HARBOR_URL_PLACE_HOLDER/shindatacloud/cloud-service=$CI_REGISTRY/$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
    # 添加feature分支的bootstrap
    - kustomize edit add configmap dbpaas-cloud-service-configmap --behavior=create --from-file=src/main/resources/bootstrap-$CI_COMMIT_REF_SLUG.properties
    #    - kustomize edit add configmap dbpaas-cloud-service-configmap --behavior=merge --from-file=config/logback-spring.xml --from-file=src/main/resources/bootstrap.yml --from-file=src/main/resources/application.yml
    #    - kustomize edit add label {shindata.branch:$CI_BUILD_REF_NAME},{shindata.service:$CI_PROJECT_NAME}
    - cat kustomization.yaml
    - git commit -am '[skip ci] feature branch image update'
    - git push origin $CI_BUILD_REF_NAME
  only: #feature Branch & !Scheduling Pipelines
    - /^feature.*/
  except:
    - schedules

deploy:
  stage: deploy-develop
  tags:
    - shindb
    - cloud
  #  image: kustomize/kustomize:v3.8.7
  before_script:
    - git remote set-url origin http://${CI_USERNAME}:${CI_PASSWORD}@***********/newdt/shindb/shindatacloudservice.git
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "lianzb"
  script:
    - git checkout -B $CI_BUILD_REF_NAME
    - |
      sed -i "s%spring\.application\.name=.*%spring.application.name=cloud-service%g" src/main/resources/bootstrap.properties
      sed -i "s%spring\.cloud\.nacos\.discovery\.server-addr=.*%spring.cloud.nacos.discovery.server-addr=${DEV_MYSQL_DISCOVERY_SERVER_ADDR}%g" src/main/resources/bootstrap.properties
      sed -i "s%spring\.cloud\.nacos\.config\.server-addr=.*%spring.cloud.nacos.config.server-addr=${DEV_MYSQL_CONFIG_SERVER_ADDR}%g" src/main/resources/bootstrap.properties
      sed -i "s%spring\.cloud\.nacos\.discovery\.ip=.*%spring.cloud.nacos.discovery.ip=${DEV_MYSQL_SHINDB_CLOUD_SERVER_IP_HOLDER}%g" src/main/resources/bootstrap.properties
      sed -i "s%spring\.cloud\.nacos\.discovery\.port=.*%spring.cloud.nacos.discovery.port=${DEV_MYSQL_SHINDB_CLOUD_SERVER_PORT_HOLDER}%g" src/main/resources/bootstrap.properties
      sed -i "s%tablePrefix:.*%tablePrefix: ndtmdb.CLOUD_QRTZ_%g" src/main/resources/application.yml
      sed -i "/elasticsearch:/ { N; s/elasticsearch:\n  ip: .*/elasticsearch:\n  ip: ${DEV_ES_IP_PLACE_HOLDER}/; }" src/main/resources/application.yml
      sed -i "s%nodePort:.*%nodePort: ${DEV_MYSQL_SHINDB_CLOUD_SERVER_PORT_HOLDER}%g" deploy/dbpaas-cloud-service-svc.yaml
      sed -i "s%branch:.*%branch: ${CI_COMMIT_REF_SLUG}%g" kustomization.yaml
      sed -i "/- src\/main\/resources\/bootstrap-.*/d" kustomization.yaml
      rm -f src/main/resources/bootstrap-*
    #    - kustomize edit set namespace $GITLAB_USER_NAME-feat
    - kustomize edit set namespace cloud-dev
    - kustomize edit set namesuffix -- -$CI_COMMIT_REF_SLUG
    - kustomize edit set image HARBOR_URL_PLACE_HOLDER/shindatacloud/cloud-service=$CI_REGISTRY/$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
    # 去除feature的spring.profiles.active的patch
    - kustomize edit remove patch --path profiles-patch.yaml --name dbpaas-cloud-service-dep --kind Deployment --group apps --version v1
#        - kustomize edit add configmap dbpaas-cloud-service-configmap --behavior=merge --from-file=config/logback-spring.xml --from-file=src/main/resources/bootstrap.properties --from-file=src/main/resources/application.yml
    #    - kustomize edit add label {shindata.branch:$CI_BUILD_REF_NAME},{shindata.service:$CI_PROJECT_NAME}
    - cat kustomization.yaml
    - git commit -am '[skip ci] develop branch image update'
    - git push origin $CI_BUILD_REF_NAME
  only: #develop Branch & !Scheduling Pipelines
    - develop
  except:
    - schedules

#deploy-pg:
#  stage: deploy-develop
#  tags:
#    - shindb
#    - cloud
#  #  image: kustomize/kustomize:v3.8.7
#  before_script:
#    - git remote set-url origin http://${CI_USERNAME}:${CI_PASSWORD}@***********/newdt/shindb/shindatacloudservice.git
#    - git config --global user.email "<EMAIL>"
#    - git config --global user.name "lianzb"
#  script:
#    - git checkout develop_pg
#    - git pull
#    - git merge -X theirs develop --no-edit
#    - |
#      sed -i "s%spring\.cloud\.nacos\.discovery\.server-addr=.*%spring.cloud.nacos.discovery.server-addr=${DEV_PG_DISCOVERY_SERVER_ADDR}%g" src/main/resources/bootstrap.properties
#      sed -i "s%spring\.cloud\.nacos\.config\.server-addr=.*%spring.cloud.nacos.config.server-addr=${DEV_PG_CONFIG_SERVER_ADDR}%g" src/main/resources/bootstrap.properties
#      sed -i "s%spring\.cloud\.nacos\.discovery\.ip=.*%spring.cloud.nacos.discovery.ip=${DEV_PG_SHINDB_CLOUD_SERVER_IP_HOLDER}%g" src/main/resources/bootstrap.properties
#      sed -i "s%spring\.cloud\.nacos\.discovery\.port=.*%spring.cloud.nacos.discovery.port=${DEV_PG_SHINDB_CLOUD_SERVER_PORT_HOLDER}%g" src/main/resources/bootstrap.properties
#      sed -i "/elasticsearch:/ { N; s/elasticsearch:\n  ip: .*/elasticsearch:\n  ip: ${DEV_ES_IP_PLACE_HOLDER}/; }" src/main/resources/application.yml
#      sed -i "s%nodePort:.*%nodePort: ${DEV_PG_SHINDB_CLOUD_SERVER_PORT_HOLDER}%g" deploy/dbpaas-cloud-service-svc.yaml
#      sed -i "s%branch:.*%branch: develop-pg%" kustomization.yaml
#      sed -i "/- src\/main\/resources\/bootstrap-.*/d" kustomization.yaml
##      rm -f src/main/resources/bootstrap-*
#    #    - kustomize edit set namespace $GITLAB_USER_NAME-feat
##    - kustomize edit set namespace cloud-dev
#    - kustomize edit set namesuffix -- -develop-pg
##    - kustomize edit set image HARBOR_URL_PLACE_HOLDER/shindatacloud/cloud-service=$CI_REGISTRY/$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
#    # 去除feature的spring.profiles.active的patch
##    - kustomize edit remove patch --path profiles-patch.yaml --name dbpaas-cloud-service-dep --kind Deployment --group apps --version v1
##    - kustomize edit add configmap dbpaas-cloud-service-configmap --behavior=merge --from-file=config/logback-spring.xml --from-file=src/main/resources/bootstrap.properties --from-file=src/main/resources/application.yml
#    #    - kustomize edit add label {shindata.branch:$CI_BUILD_REF_NAME},{shindata.service:$CI_PROJECT_NAME}
#    - cat kustomization.yaml
#    - git commit -am '[skip ci] develop pg branch image update' --allow-empty
#    - git push origin develop_pg
#  only: #develop Branch & !Scheduling Pipelines
#    - develop
#  except:
#    - schedules

deploy-test:
  stage: deploy-test
  tags:
    - shindb
    - cloud
  #  image: kustomize/kustomize:v3.8.7
  before_script:
    - git remote set-url origin http://${CI_USERNAME}:${CI_PASSWORD}@***********/newdt/shindb/shindatacloudservice.git
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "lianzb"
  script:
    - git checkout -B $CI_BUILD_REF_NAME
    - |
      sed -i "s%spring\.application\.name=.*%spring.application.name=cloud-service%" src/main/resources/bootstrap.properties
      sed -i "s%spring\.cloud\.nacos\.discovery\.server-addr=.*%spring.cloud.nacos.discovery.server-addr=${TEST_MYSQL_DISCOVERY_SERVER_ADDR}%g" src/main/resources/bootstrap.properties
      sed -i "s%spring\.cloud\.nacos\.config\.server-addr=.*%spring.cloud.nacos.config.server-addr=${TEST_MYSQL_CONFIG_SERVER_ADDR}%g" src/main/resources/bootstrap.properties
      sed -i "s%spring\.cloud\.nacos\.discovery\.ip=.*%spring.cloud.nacos.discovery.ip=${TEST_MYSQL_SHINDB_CLOUD_SERVER_IP_HOLDER}%g" src/main/resources/bootstrap.properties
      sed -i "s%spring\.cloud\.nacos\.discovery\.port=.*%spring.cloud.nacos.discovery.port=${TEST_MYSQL_SHINDB_CLOUD_SERVER_PORT_HOLDER}%g" src/main/resources/bootstrap.properties
      sed -i "s%tablePrefix:.*%tablePrefix: ndtmdb.CLOUD_QRTZ_%g" src/main/resources/application.yml
      sed -i "/elasticsearch:/ { N; s/elasticsearch:\n  ip: .*/elasticsearch:\n  ip: ${TEST_ES_IP_PLACE_HOLDER}/; }" src/main/resources/application.yml
      sed -i "s%nodePort:.*%nodePort: ${TEST_MYSQL_SHINDB_CLOUD_SERVER_PORT_HOLDER}%g" deploy/dbpaas-cloud-service-svc.yaml
      sed -i "s%branch:.*%branch: ${CI_COMMIT_REF_SLUG}%" kustomization.yaml
    #    - kustomize edit set namespace $GITLAB_USER_NAME-feat
    - kustomize edit set namespace cloud-dev
    - kustomize edit set namesuffix -- -$CI_COMMIT_REF_SLUG
    - kustomize edit set image HARBOR_URL_PLACE_HOLDER/shindatacloud/cloud-service=$CI_REGISTRY/$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
    - cat kustomization.yaml
    - git commit -am '[skip ci] test branch deploy' --allow-empty
    - git push origin $CI_BUILD_REF_NAME
  only:
    - test

#deploy-test-pg:
#  stage: deploy-test
#  tags:
#    - shindb
#    - cloud
#  #  image: kustomize/kustomize:v3.8.7
#  before_script:
#    - git remote set-url origin http://${CI_USERNAME}:${CI_PASSWORD}@***********/newdt/shindb/shindatacloudservice.git
#    - git config --global user.email "<EMAIL>"
#    - git config --global user.name "lianzb"
#  script:
#    - git checkout -B $CI_BUILD_REF_NAME
#    - |
#      sed -i "s%spring\.application\.name=.*%spring.application.name=cloud-service%" src/main/resources/bootstrap.properties
#      sed -i "s%spring\.cloud\.nacos\.discovery\.server-addr=.*%spring.cloud.nacos.discovery.server-addr=${TEST_PG_DISCOVERY_SERVER_ADDR}%" src/main/resources/bootstrap.properties
#      sed -i "s%spring\.cloud\.nacos\.config\.server-addr=.*%spring.cloud.nacos.config.server-addr=${TEST_PG_CONFIG_SERVER_ADDR}%" src/main/resources/bootstrap.properties
#      sed -i "s%spring\.cloud\.nacos\.discovery\.ip=.*%spring.cloud.nacos.discovery.ip=${TEST_PG_SHINDB_CLOUD_SERVER_IP_HOLDER}%" src/main/resources/bootstrap.properties
#      sed -i "s%spring\.cloud\.nacos\.discovery\.port=.*%spring.cloud.nacos.discovery.port=${TEST_PG_SHINDB_CLOUD_SERVER_PORT_HOLDER}%" src/main/resources/bootstrap.properties
#      sed -i "s%tablePrefix:.*%tablePrefix: ndtmdb.CLOUD_QRTZ_%g" src/main/resources/application.yml
#      sed -i "/elasticsearch:/ { N; s/elasticsearch:\n  ip: .*/elasticsearch:\n  ip: ${TEST_ES_IP_PLACE_HOLDER}/; }" src/main/resources/application.yml
#      sed -i "s%nodePort:.*%nodePort: ${TEST_PG_SHINDB_CLOUD_SERVER_PORT_HOLDER}%" deploy/dbpaas-cloud-service-svc.yaml
#      sed -i "s%branch:.*%branch: ${CI_COMMIT_REF_SLUG}%" kustomization.yaml
#    #    - kustomize edit set namespace $GITLAB_USER_NAME-feat
#    - kustomize edit set namespace cloud-dev
#    - kustomize edit set namesuffix -- -$CI_COMMIT_REF_SLUG
#    - kustomize edit set image HARBOR_URL_PLACE_HOLDER/shindatacloud/cloud-service=$CI_REGISTRY/$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
#    - cat kustomization.yaml
#    - git commit -am '[skip ci] test branch deploy' --allow-empty
#    - git push origin $CI_BUILD_REF_NAME
#  only:
#    - test

#deploy-test-demo:
#  stage: deploy-test
#  tags:
#    - shindb
#    - cloud
#  #  image: kustomize/kustomize:v3.8.7
#  before_script:
#    - git remote set-url origin http://${CI_USERNAME}:${CI_PASSWORD}@***********/newdt/shindb/shindatacloudservice.git
#    - git config --global user.email "<EMAIL>"
#    - git config --global user.name "lianzb"
#  script:
#    - git checkout test_demo
#    - git pull
#    - git merge -X theirs test --no-edit
#    - |
#      sed -i "s%spring\.application\.name=.*%spring.application.name=cloud-service%" src/main/resources/bootstrap.properties
#      sed -i "s%spring\.cloud\.nacos\.discovery\.server-addr=.*%spring.cloud.nacos.discovery.server-addr=${TEST_DEMO_MYSQL_DISCOVERY_SERVER_ADDR}%g" src/main/resources/bootstrap.properties
#      sed -i "s%spring\.cloud\.nacos\.config\.server-addr=.*%spring.cloud.nacos.config.server-addr=${TEST_DEMO_MYSQL_CONFIG_SERVER_ADDR}%g" src/main/resources/bootstrap.properties
#      sed -i "s%spring\.cloud\.nacos\.discovery\.ip=.*%spring.cloud.nacos.discovery.ip=${TEST_DEMO_MYSQL_SHINDB_CLOUD_SERVER_IP_HOLDER}%g" src/main/resources/bootstrap.properties
#      sed -i "s%spring\.cloud\.nacos\.discovery\.port=.*%spring.cloud.nacos.discovery.port=${TEST_DEMO_MYSQL_SHINDB_CLOUD_SERVER_PORT_HOLDER}%g" src/main/resources/bootstrap.properties
#      sed -i "s%tablePrefix:.*%tablePrefix: ndtmdb.CLOUD_QRTZ_%g" src/main/resources/application.yml
#      sed -i "/elasticsearch:/ { N; s/elasticsearch:\n  ip: .*/elasticsearch:\n  ip: ${TEST_DEMO_ES_IP_PLACE_HOLDER}/; }" src/main/resources/application.yml
#      sed -i "s%nodePort:.*%nodePort: ${TEST_DEMO_MYSQL_SHINDB_CLOUD_SERVER_PORT_HOLDER}%g" deploy/dbpaas-cloud-service-svc.yaml
#      sed -i "s%branch:.*%branch: test-demo%" kustomization.yaml
#    #    - kustomize edit set namespace $GITLAB_USER_NAME-feat
#    - kustomize edit set namespace cloud-dev
#    - kustomize edit set namesuffix -- -test-demo
#    - cat kustomization.yaml
#    - git commit -am '[skip ci] test_demo branch deploy' --allow-empty
#    - git push origin test_demo
#  only:
#    - test

#deploy-release:
#  stage: deploy-release
#  tags:
#    - shindb
#    - cloud
#  #  image: kustomize/kustomize:v3.8.7
#  before_script:
#    - git remote set-url origin http://${CI_USERNAME}:${CI_PASSWORD}@***********/newdt/shindb/shindatacloudservice.git
#    - git config --global user.email "<EMAIL>"
#    - git config --global user.name "lianzb"
#  script:
#    - git checkout -B $CI_BUILD_REF_NAME
#    - kustomize edit set namespace develop
#    - kustomize edit set image HARBOR_URL_PLACE_HOLDER/shindatacloud/cloud-service=$CI_REGISTRY/$CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
#    #    - kustomize edit add configmap dbpaas-cloud-service-configmap --behavior=merge --from-file=config/logback-spring.xml --from-file=src/main/resources/bootstrap.yml --from-file=src/main/resources/application.yml
#    #    - kustomize edit add label {shindata.branch:$CI_BUILD_REF_NAME},{shindata.service:$CI_PROJECT_NAME}
#    - cat kustomization.yaml
#    - git commit -am '[skip ci] FEAT image update'
#    - git push origin $CI_BUILD_REF_NAME
#  only: #当前的 branch 名称，用于触发 CI/CD
#    - develop
##  when: manual