package cn.newdt.cloud.service.alert;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.AlertConfigConstant;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.CloudAppLogic;
import cn.newdt.cloud.domain.alert.AlertConfig;
import cn.newdt.cloud.domain.alert.AlertMetric;
import cn.newdt.cloud.domain.alert.AlertRuleConfig;
import cn.newdt.cloud.domain.alert.AlertRuleResource;
import cn.newdt.cloud.domain.alert.json.Contact;
import cn.newdt.cloud.domain.alert.json.RuleSet;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.filter.ResourceChangeLog;
import cn.newdt.cloud.filter.ResourceView;
import cn.newdt.cloud.mapper.AlertMetricMapper;
import cn.newdt.cloud.mapper.AlertRuleConfigMapper;
import cn.newdt.cloud.mapper.AlertRuleResourceMapper;
import cn.newdt.cloud.service.AppKindService;
import cn.newdt.cloud.service.AppServiceLoader;
import cn.newdt.cloud.service.CloudAppLogicService;
import cn.newdt.cloud.service.ComposedAppService;
import cn.newdt.cloud.service.impl.SysConfigService;
import cn.newdt.cloud.utils.DateUtil;
import cn.newdt.cloud.utils.JsonUtil;
import cn.newdt.cloud.utils.MybatisUtil;
import cn.newdt.cloud.vo.AlertRuleConfigVo;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.UserUtil;
import com.github.pagehelper.Page;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.DatasourceConstant.SCHEMA;
import static cn.newdt.cloud.service.alert.AlertConfigSyncService.SEPARATOR;

@Service
@Slf4j
public class AlertConfigService {
    @Resource
    private AlertRuleConfigMapper ruleSetMapper;
    @Resource
    private AlertRuleResourceMapper ruleResourceMapper;
    @Autowired
    private AlertConfigSyncService syncService;
    @Autowired
    private AlertNotifyConfigService notifyConfigService;
    @Autowired
    private AlertMetricMapper alertMetricMapper;
    @Autowired
    private CloudAppLogicService appLogicService;

    private final String UNITS_OPTS = "[" +
            "{\n" +
            "    'type': 'storage',\n" +
            "    'opts': ['MB', 'GB']\n" +
            "},{\n" +
            "    'type': 'none',\n" +
            "    'opts': []\n" +
            "},{\n" +
            "    'type': 'percent',\n" +
            "    'opts': ['%']\n" +
            "},{\n" +
            "    'type': 'time',\n" +
            "    'opts': ['s', 'm', 'h']\n" +
            "}" +
            "]";

    /**
     * 更新告警配置，新增配置或从模板应用
     * @param alertRules
     * @return
     */
    @Transactional
    public Integer saveAlertRules(AlertRuleConfig alertRules) {
        String backup = alertRules.getJson();
        RuleSet parsed = JsonUtil.toObject(RuleSet.class, alertRules.getJson());
        //根据resources中是否有对象，来判断是修改模板还是策略
        List<cn.newdt.cloud.domain.alert.json.Resource> jsonResources = parsed.getResources();
        if (!CollectionUtils.isEmpty(jsonResources)) {
            final int logicId = jsonResources.get(0).getLogicAppId();
            CloudAppLogic logicApp = appLogicService.get(logicId);

            // (multi_az)展开dmp发送的jsonResource对象
            List<cn.newdt.cloud.domain.alert.json.Resource> resourceCollect =
                    jsonResources.stream().flatMap(originResource -> {
                        return appLogicService.getPhysicApps(logicId).stream().map(app -> {
                            cn.newdt.cloud.domain.alert.json.Resource flatResource =
                                    new cn.newdt.cloud.domain.alert.json.Resource();
                            flatResource.copyFrom(originResource);
                            flatResource.setName(app.getCrName());
                            flatResource.setType(app.getKind() + "_" + app.getArch());
                            flatResource.setNamespace(app.getNamespace());
                            flatResource.setKubeId(app.getKubeId());
                            return flatResource;
                        });
                    }).collect(Collectors.toList());
            jsonResources = resourceCollect;
            // 组合型 appKind(仅rocketmq)
            fetchReferringResource(logicApp).ifPresent(jsonResources::add);
            String ruleConfigName = generateConfigName(
                    logicApp.getKind(), logicApp.getArch(), logicApp.getNamespace(), logicApp.getCrName());
            // 目前 先1)installwatch 自动告警配置, 再2)dmp纳管触发告警配置.
            // 1) 可能失败时，fix --bug=1014156 --user=闫浩杰 mgr创建成功后告警配置报错 https://www.tapd.cn/44948243/s/1622433
            AlertRuleConfig alertRuleConfig = ruleSetMapper.selectByName(SCHEMA, ruleConfigName);
            if (alertRuleConfig != null) {
                alertRules.setId(alertRuleConfig.getId());
                alertRules.setConfigName(alertRuleConfig.getConfigName());
            } else {
                alertRules.setConfigName(ruleConfigName);
                alertRules.setOwnerTenant(logicApp.getOwnerTenant());
                alertRules.setOwnerUser(logicApp.getOwnerUser());
                alertRules.setResourceType(logicApp.getKind());
                alertRules.setResourceNamespace(logicApp.getNamespace());
            }
        } else {
            //根据类型获取模板
            String resourceType = alertRules.getResourceType();
            Map<String, Object> condition = new HashMap<>();
            condition.put("resourceType", resourceType);
            condition.put("status", "1");
            List<AlertRuleConfig> alertRuleConfigs = ruleSetMapper.selectByJoin(SCHEMA, condition);
            if (CollectionUtils.isEmpty(alertRuleConfigs)) {
                //未获取到告警模板
                throw new CustomException(600, "未查询到告警模板！");
            }
            AlertRuleConfig alertRuleConfig = alertRuleConfigs.get(0);
            //还原数据
            alertRules.setId(alertRuleConfig.getId());
            alertRules.setConfigName(alertRuleConfig.getConfigName());
        }

        parsed.setRuleSetName(alertRules.getConfigName());

//        validate(parsed);

        final List<cn.newdt.cloud.domain.alert.json.Resource> resources =
                jsonResources == null ? Collections.emptyList() : jsonResources;
        parsed.setResources(null);
        String checksum = DigestUtils.md5Hex(JsonUtil.toJson(parsed));

        // 解析更新类型 toUpdate/toDelete/toCreate
        // toUpdate/toCreate
        Map<String, AlertRuleResource> currentAllRRRMap = findAllResourceRuleRelation();
        Map<String, List<cn.newdt.cloud.domain.alert.json.Resource>> rrListGroup = resources.stream().collect(Collectors.groupingBy(rr -> {
            String key = getKey(rr);
            if (!currentAllRRRMap.containsKey(key))
                return "toCreate";
            else if (!checksum.equals(currentAllRRRMap.get(key).getChecksum()))
                return "toUpdate";
            else return "default";
        }, () -> {
            Map<String, List<cn.newdt.cloud.domain.alert.json.Resource>> map = new HashMap<>();
            map.put("toCreate", new ArrayList<>());
            map.put("toUpdate", new ArrayList<>());
            map.put("default", new ArrayList<>());
            return map;
        }, Collectors.toList()));

        List<cn.newdt.cloud.domain.alert.json.Resource> rrListToUpdate = rrListGroup.get("toUpdate");
        List<cn.newdt.cloud.domain.alert.json.Resource> rrListToCreate = rrListGroup.get("toCreate");

        // 更新/创建alertRule
        alertRules.setChecksum(checksum);
        alertRules.setJson(JsonUtil.toJson(parsed));
        LocalDateTime now = LocalDateTime.now();
        if (alertRules.getId() != null) {
            alertRules.setUpdateTime(now);
            ruleSetMapper.updateByPrimaryKeySelective(SCHEMA, alertRules);
        } else {
            if (!rrListToUpdate.isEmpty() || !rrListToCreate.isEmpty()) { // 模板重复应用处理, 避免新增一条空的配置
                alertRules.setCreateTime(now);
                alertRules.setUpdateTime(now);
                alertRules.setOwnerUser(UserUtil.getCurrentUser().getUserid());
                alertRules.setStatus(0);
                ruleSetMapper.insert(SCHEMA, alertRules);
            }
        }

        Integer id = alertRules.getId();

        // toDelete
        Map<String, cn.newdt.cloud.domain.alert.json.Resource> expectRuleResourceMap = resources.stream().collect(Collectors
                .toMap(this::getKey, Function.identity()));
        List<AlertRuleResource> rrListToDelete = currentAllRRRMap.entrySet().stream().filter(e ->
                !expectRuleResourceMap.containsKey(e.getKey()) && e.getValue().getRuleSetId().equals(id)
        ).map(e -> e.getValue()).collect(Collectors.toList());

        Function<cn.newdt.cloud.domain.alert.json.Resource, AlertRuleResource> convertFunc = resource -> {
            AlertRuleResource aResource = new AlertRuleResource();
            aResource.setRuleSetId(id);
            aResource.setResourceName(resource.getName());
            aResource.setResourceNamespace(resource.getNamespace());
            aResource.setResourceType(resource.getType());
            aResource.setKubeId(resource.getKubeId());
            aResource.setResourceId(resource.getLogicAppId());
            aResource.setPrimaryResourceId(resource.getPrimaryLogicId());
//                    aResource.setChecksum(checksum); to be synced
            aResource.setUpdateTime(now);
            return aResource;
        };
        List<AlertRuleResource> aRRListToUpdate = rrListToUpdate.stream().map(convertFunc).collect(Collectors.toList());
        List<AlertRuleResource> aRRListToCreate = rrListToCreate.stream().map(convertFunc).collect(Collectors.toList());

        // 更新/创建/删除alertrule和resource的关联关系
        aRRListToUpdate.stream().forEach(expectRR -> {
            String key = ruleResourceMapKey(expectRR.getResourceType(), expectRR.getResourceNamespace(), expectRR.getResourceName(), expectRR.getKubeId());
            AlertRuleResource currentRR = currentAllRRRMap.get(key);
            ruleResourceMapper.deleteByPrimaryKey(SCHEMA, currentRR.getId());
            // 删除没有任何关联(dangling)的告警配置
            Integer ruleSetId = currentRR.getRuleSetId();
            if (!id.equals(ruleSetId)  // 从模板应用会创建新的配置，旧配置删除
                    && ruleResourceMapper.selectList(SCHEMA, ImmutableMap.of("ruleSetId", ruleSetId)).isEmpty()) {
                ruleSetMapper.deleteByPrimaryKey(SCHEMA, ruleSetId);
            }
        });
        if (!aRRListToCreate.isEmpty()) {
            ruleResourceMapper.insertMultiple(SCHEMA, aRRListToCreate);
            aRRListToCreate.stream().map(rr ->
                            new AlertConfig(JsonUtil.toObject(RuleSet.class, alertRules.getJson()), rr.getKubeId(), rr, checksum))
                    .forEach(ac -> syncService.enqueueSyncTask(ac)); // 每个resource独立同步到k8s
        }
        if (!aRRListToUpdate.isEmpty()) {
            ruleResourceMapper.insertMultiple(SCHEMA, aRRListToUpdate);
            aRRListToUpdate.stream().map(rr ->
                            new AlertConfig(JsonUtil.toObject(RuleSet.class, alertRules.getJson()), rr.getKubeId(), rr, checksum))
                    .forEach(ac -> syncService.enqueueSyncTask(ac));
        }
        rrListToDelete.stream().forEach(rr -> {
            if (rr.getRuleSetId().equals(id)) // 原配置更新，非模板应用
                ruleResourceMapper.deleteByPrimaryKey(SCHEMA, rr.getId());
            // todo unsync
        });
        return id;
    }

    @Autowired private SysConfigService sysConfigService;
    private void validate(RuleSet parsed) {
        int seconds = parsed.getNotifyStrategy().getRepeatIntervalTime();
        Duration duration = Duration.ofSeconds(seconds);
        Optional.ofNullable(sysConfigService.findOne(CloudAppConstant.SysCfgCategory.ALERT_CFG,
                        AlertConfigConstant.ALERTMANAGER_RETENTION))
                .ifPresent(cfg -> {
                    Duration limitDuration = DateUtil.parseDuration(cfg);
                    if (duration.compareTo(limitDuration) > 0) {
                        throw new CustomException(600, "有效重复通知间隔不能大于"+cfg);
                    }
                });

    }

    private String getKey(cn.newdt.cloud.domain.alert.json.Resource rr) {
        return ruleResourceMapKey(rr.getType(), rr.getNamespace(), rr.getName(), rr.getKubeId());
    }

    private List<AlertRuleResource> findAllResourceRule() {
        return ruleResourceMapper.selectList(SCHEMA, Collections.emptyMap());
    }
    /**
     * 获取cloud_alert_rule_resource所有数据。
     * 同一应用可以存在不同关联维度的多条关联记录，同一维度只能有一个关联规则
     *
     * @return map key: type-namespace-name
     */
    private Map<String, AlertRuleResource> findAllResourceRuleRelation() {
        return ruleResourceMapper.selectList(SCHEMA, Collections.emptyMap())
                .stream()
                .collect(Collectors.toMap(
                        r -> ruleResourceMapKey(r.getResourceType(), r.getResourceNamespace(), r.getResourceName(), r.getKubeId()),
                        r -> r,
                        (rl, rr) -> rr));
    }

    private String ruleResourceMapKey(String resourceType, String resourceNamespace, String resourceName, Integer kubeId) {
        return generateConfigName(resourceType, resourceNamespace, resourceName) + (kubeId == null ? "" : "_" + kubeId);
    }

    /**
     * 查询指定资源对象存在的规则配置
     * @return AlertRuleConfig, null if not exist
     * @throws CustomException if many alertRuleConfigs found
     */
    public AlertRuleConfig describeCurrentRuleSet(@Nonnull String namespace, @Nonnull String type, @Nonnull String name) {
        List<AlertRuleResource> alertRuleResources = ruleResourceMapper.selectList(SCHEMA, ImmutableMap.of("resourceNamespace", namespace,
                "resourceType", type, "resourceName", name));

        if (alertRuleResources.stream().map(AlertRuleResource::getRuleSetId).distinct().count() > 1) {
            log.warn("指定资源对象存在多个告警配置, {}/{}/{}", type, namespace, name);
        }
        if (alertRuleResources.isEmpty())
            return null;

        AlertRuleResource alertRuleResource = alertRuleResources.get(0);
        Integer ruleSetId = alertRuleResource.getRuleSetId();
        return describeCurrentRuleSet(ruleSetId);
    }

    public AlertRuleConfig describeCurrentRuleSet(int id) {
        AlertRuleConfig alertRuleConfig = ruleSetMapper.selectByPrimaryKey(SCHEMA, id);
        if (alertRuleConfig == null) return null;
        List<AlertRuleResource> ruleResourceInstances = ruleResourceMapper.selectList(SCHEMA, Collections.singletonMap("ruleSetId", id));
        alertRuleConfig.setInstances(ruleResourceInstances);

        List<cn.newdt.cloud.domain.alert.json.Resource> list = ruleResourceInstances.stream()
                .map(i -> new cn.newdt.cloud.domain.alert.json.Resource().copyFrom(i))
                .collect(Collectors.toList());
        RuleSet ruleSet = JsonUtil.toObject(RuleSet.class, alertRuleConfig.getJson());
        ruleSet.setResources(list);
        alertRuleConfig.setJson(JsonUtil.toJson(ruleSet));
        return alertRuleConfig;
    }

    public List<AlertRuleConfig> getAlertRuleSetList(Map<String, Object> condition) {
        return ruleSetMapper.selectByJoin(SCHEMA, condition == null ? Collections.emptyMap() : condition);
    }

    public List<AlertRuleConfigVo> getAlertRuleSetTemaplteVoList(PageDTO pageDTO) {
        // 多加一层，因为模板不需要权限过滤
        return getAlertRuleSetVoList(pageDTO);
    }

    @ResourceView
    public List<AlertRuleConfigVo> getAlertRuleSetVoList(PageDTO pageDTO) {
        if (pageDTO.getPageNum() != null && pageDTO.getPageSize() != null) {
            pageDTO.getCondition().put("offset", pageDTO.getPageNum());
            pageDTO.getCondition().put("pageSize", pageDTO.getPageSize());
        }
        if (StringUtils.isNotEmpty(pageDTO.getPageSortProp())) {
            pageDTO.getCondition().put("orderCol", MybatisUtil.getJavaPropMapDBColumn("cn.newdt.cloud.mapper.AlertRuleConfigMapper.BaseResultMap", pageDTO.getPageSortProp()));
            pageDTO.getCondition().put("order", pageDTO.getPageSort() != null ? pageDTO.getPageSort() : "asc");
        }
        List<AlertRuleConfig> alertRuleConfigs = getAlertRuleSetList(pageDTO.getCondition());
        if (alertRuleConfigs.isEmpty()) return Collections.emptyList();
        Map<Integer, String> userStore = notifyConfigService.listUserAsContact().stream()
                .collect(Collectors.toMap(c -> c.getUserId(), c -> c.getUsername()));
        Map<Integer, String> groupStore = notifyConfigService.listContactGroup().stream()
                .collect(Collectors.toMap(cg -> cg.getGroupId(), cg -> cg.getName()));
        Map<String, AlertMetric> metricMetadata = getAlertMetricMetadata();
        List<AlertRuleConfigVo> list = alertRuleConfigs.stream().map(rs -> {
            AlertRuleConfigVo vo = new AlertRuleConfigVo();
            RuleSet ruleSet = rs.parseJson();
            ruleSet.format();
            vo.setId(rs.getId());
            vo.setName(rs.getConfigName());
            vo.setChecksum(rs.getChecksum());
            vo.setCreateTime(rs.getCreateTime());
            vo.setUpdateTime(rs.getUpdateTime());
            vo.setRules(ruleSet.getRules().stream().map(rule -> {
                AlertMetric alertMetric = metricMetadata.get(rule.getMetricName());
                return alertMetric != null ? alertMetric.getSummary() : rule.getMetricName();
            }).collect(Collectors.joining(",")));
            vo.setResources(rs.getInstances());
            vo.setResourceType(rs.getResourceType());
            vo.setReceiverGroups(ruleSet.getNotifyStrategy().getChannels().stream()
                    .filter(channel -> channel.getContacts() != null).flatMap(channel -> {
                        List<Contact> contacts = channel.getContacts();
                        return contacts.stream()
                                .filter(contact -> contact.getGroupId() != null)
                                .map(contact -> groupStore.get(contact.getGroupId()));
                    }).collect(Collectors.joining(",")));
            vo.setReceivers(ruleSet.getNotifyStrategy().getChannels().stream().flatMap(channel -> {
                return channel.getContacts().stream()
                        .filter(contact -> contact.getId() != null)
                        .map(contact -> userStore.get(contact.getId()));
            }).collect(Collectors.joining(",")));
            return vo;
        }).collect(Collectors.toList());
        Page page = new Page();
        page.addAll(list);
        page.setTotal(ruleSetMapper.countSelectByJoin(SCHEMA, pageDTO.getCondition()));
        return page;
    }

    private Map<String, AlertMetric> getAlertMetricMetadata() {
        return alertMetricMapper.listByMap(SCHEMA, Collections.emptyMap()).stream()
                .collect(Collectors.toMap(AlertMetric::getMetricName, Function.identity()));
    }

    public int updateAlertRuleResourceSynced(AlertRuleResource record, String checksum) {
        record.setChecksum(checksum);
        record.setSyncTime(LocalDateTime.now());
        return ruleResourceMapper.updateByPrimaryKeySelective(SCHEMA, record);
    }

    public AlertRuleConfig selectById(int templateId) {
        return ruleSetMapper.selectByPrimaryKey(SCHEMA, templateId);
    }

    public void createConfigForApp(CloudApp app) {

        int templateMarkCode = AlertRuleConfig.Status.Default.getCode();
        AlertRuleConfig template = selectOne(ImmutableMap.of("resourceType",
                app.getKind(), "status", templateMarkCode));
        if (template == null) {
            throw new IllegalStateException("启用告警失败: 默认模板不存在");
        }
        cn.newdt.cloud.domain.alert.json.Resource element = createResource(app);
        String configName = generateConfigName(element.getType(), element.getNamespace(), element.getName());

        List<cn.newdt.cloud.domain.alert.json.Resource> resources = new ArrayList<>();
        resources.add(element);
        // 为关联应用创建告警。(仅模板自动应用场景，手动应用暂不处理)
        fetchReferringResource(app).ifPresent(resources::add);
        createNewConfigFromTemplate(resources, template.getId(), configName);
    }

    private static cn.newdt.cloud.domain.alert.json.Resource createResource(CloudApp app) {
        return createResource(app, app.getLogicAppId());
    }

    private static cn.newdt.cloud.domain.alert.json.Resource createResource(CloudApp app, int primaryLogicId) {
        cn.newdt.cloud.domain.alert.json.Resource element = new cn.newdt.cloud.domain.alert.json.Resource();
        element.setType(app.getKind() + SEPARATOR + app.getArch());
        element.setNamespace(app.getNamespace());
        element.setName(app.getCrName());
        element.setLogicAppId(app.getLogicAppId());
        element.setKubeId(app.getKubeId());
        element.setPrimaryLogicId(primaryLogicId);
        element.setTenantId(app.getOwnerTenant());
        return element;
    }

    public Optional<cn.newdt.cloud.domain.alert.json.Resource> fetchReferringResource(CloudApp app) {
        AppKindService instance = AppServiceLoader.getInstance(app.getKind(), app.getArch());
        if (instance instanceof ComposedAppService)
            return Optional.of(
                    createResource(
                            ((ComposedAppService) instance).referringApp(app),
                            app.getLogicAppId()
                    ));
        else return Optional.empty();
    }

    public Optional<cn.newdt.cloud.domain.alert.json.Resource> fetchReferringResource(CloudAppLogic app) {
        return fetchReferringResource(Optional.ofNullable(appLogicService.getPhysicApps(app.getId()))
                .flatMap(apps -> apps.stream().findFirst())
                .orElseThrow(() -> new IllegalArgumentException("查询物理应用为空")));
    }

    private AlertRuleConfig selectOne(Map<String, Object> param) {
        List<AlertRuleConfig> alertRuleSetList = getAlertRuleSetList(param);
        if (alertRuleSetList.size() == 1) {
            return alertRuleSetList.get(0);
        }
        if (alertRuleSetList.size() == 2) {
            throw new IllegalStateException("return many results when select one alert config template");
        }
        return null;
    }

    @ResourceChangeLog(action = ActionEnum.APPLY_ALERT_CONFIG_TEMPLATE)
    public void createNewConfigFromTemplate(List<cn.newdt.cloud.domain.alert.json.Resource> resources, int templateId, String configName) {
        AlertRuleConfig template = selectById(templateId);

        // 规定 指定configName 则生成一个config 记录(安装自动应用场景); 手动应用configName 为空，为一对多
        if (StringUtils.isEmpty(configName))
            // rule_config 与 rule_resource 一对一
            for (cn.newdt.cloud.domain.alert.json.Resource element : resources) {
                configName = generateConfigName(element.getType(), element.getNamespace(), element.getName());
                AlertRuleConfig newConfig = constructNewConfigFromTemplate(Collections.singletonList(element), configName, template);
                saveAlertRules(newConfig);
            }
        else {
            // 一对多
            AlertRuleConfig newConfig = constructNewConfigFromTemplate(resources, configName, template);
            saveAlertRules(newConfig);
        }
    }

    private static String generateConfigName(String kind, String arch, String namespace, String name) {
        return generateConfigName(kind + "_" + arch, namespace, name);
    }

    private static String generateConfigName(String type, String namespace, String name) {
        return String.format("%s_%s_%s", type, namespace, name);
    }

    private AlertRuleConfig constructNewConfigFromTemplate(List<cn.newdt.cloud.domain.alert.json.Resource> resources, String configName, AlertRuleConfig template) {
        AlertRuleConfig newConfig = new AlertRuleConfig();
        newConfig.setConfigName(configName);
        String json = template.getJson();
        RuleSet object = JsonUtil.toObject(RuleSet.class, json);
        object.setResources(resources);
        newConfig.setJson(JsonUtil.toJson(object));
        newConfig.setResourceType(template.getResourceType());
        // note: 资源列表的公共属性应该一致
        cn.newdt.cloud.domain.alert.json.Resource typicalResource = resources.get(0);
        Integer tenantId = typicalResource.getTenantId();
        if (tenantId == null) {
            tenantId = appLogicService.get(typicalResource.getLogicAppId()).getOwnerTenant();
        }
        String namespace = typicalResource.getNamespace();
        newConfig.setOwnerTenant(tenantId);
        newConfig.setResourceNamespace(namespace);
        return newConfig;
    }

    public int deleteRuleConfig(int id) {
        return ruleSetMapper.deleteByPrimaryKey(SCHEMA, id);
    }

    /**
     * 取消同步 and 更新配置状态
     */
    public void disableAlertFor(CloudApp app) {
        AlertRuleConfig ar = describeCurrentRuleSet(app);
        if (ar == null) return;
        ar.setStatus(AlertRuleConfig.Status.Disable.getCode());
        ruleSetMapper.updateByPrimaryKeySelective(SCHEMA, ar);

        syncService.unSyncConfig(app.getNamespace(), app.getKind(), app.getArch(), app.getCrName(), ar);
    }

    public void deleteAlertFor(CloudApp app) {
        AlertRuleConfig ar = describeCurrentRuleSet(app);
        if (ar == null) return;
        ar.setStatus(AlertRuleConfig.Status.Disable.getCode());
        ruleSetMapper.updateByPrimaryKeySelective(SCHEMA, ar);

        syncService.unSyncConfig(app.getNamespace(), app.getKind(), app.getArch(), app.getCrName(), ar);

        List<AlertRuleResource> removedConfigInstance = ar.getInstances().stream()
                .filter(i -> (app.getKind() + SEPARATOR + app.getArch()).equals(i.getResourceType())
                        && app.getCrName().equals(i.getResourceName()) && app.getNamespace().equals(i.getResourceNamespace()))
                .collect(Collectors.toList());

        // delete 告警配置关联记录
        removedConfigInstance.forEach(i -> ruleResourceMapper.deleteByPrimaryKey(SCHEMA, i.getId()));
        // check if AlertRuleConfig need to be cleaned
        if (removedConfigInstance.size() == ar.getInstances().size()) {
            deleteRuleConfig(ar.getId());
        }
    }

    public void enableAlertFor(CloudApp app) {
        AlertRuleConfig ar = describeCurrentRuleSet(app);
        if (ar == null) return;
        ar.setStatus(0);
        ruleSetMapper.updateByPrimaryKeySelective(SCHEMA, ar);

        for (AlertRuleResource instance : ar.getInstances()) {
            syncService.syncConfig(new AlertConfig(JsonUtil.toObject(RuleSet.class, ar.getJson()), app.getKubeId(), instance, ar.getChecksum()));
        }
    }

    private AlertRuleConfig describeCurrentRuleSet(CloudApp app) {
        return describeCurrentRuleSet(app.getNamespace(), app.getKind() + "_" + app.getArch(), app.getName());
    }
}
