package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.StatusConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.cr.MongoDBCommunity;
import cn.newdt.cloud.dto.Label;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.impl.MongoDbService;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.commons.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Stream;

/**
 * 校验指定pod 是否成为master节点
 */
@Slf4j
public class MongoDBSwitchMasterWatch extends OpsProcessorContext implements OpsPostProcessor<MongoDBCommunity> {

    @Autowired
    private MongoDbService mongoDbService;

    @Override
    public OpsResultDTO postProcess(TriggerHis triggerHis) throws Exception {
        OpsResultDTO.Builder result = OpsResultDTO.builder();
        Map<String, String> jobDataMap = triggerHis.returnMergedJobDataMap();
        String appIdData = jobDataMap.get("appId");
        long startTime = Long.parseLong(jobDataMap.get("startTime"));
        String podName = jobDataMap.get("podName");
        int appId = Integer.parseInt(appIdData);
        CloudApp app = appService.get(appId);
        String namespace = app.getNamespace();
        String crName = app.getCrName();
        try {
            KubeClient kubeClient = kubeClientService.get(app.getKubeId());
            MongoDBCommunity cr = kubeClient.listCustomResource(MongoDBCommunity.class, crName, namespace);
            String state = cr.getStatus().getPhase();

            if (Objects.equals(state, "Running")) {
                Map<String, String> mongoDbRoleMap = mongoDbService
                        .getMongoDbRoleMap(kubeClient, namespace, crName, podName);
                if (mongoDbRoleMap == null) {
                    throw new CustomException(600, "[MongoDBSwitchMasterWatch]角色获取失败");
                }
                String primary = mongoDbRoleMap.entrySet().stream()
                        .filter(role -> role.getValue().equalsIgnoreCase("primary"))
                        .map(Map.Entry::getKey).findFirst()
                        .orElseThrow(() -> new CustomException(600, "[MongoDBSwitchMasterWatch]角色获取失败"));

                if (podName.equals(primary)) {
                    // 由于更新角色 label 是通过自定义 container 实现，不会和 CR 的状态同步
                    // 所以在通过 mongo cli 确认切换成功后，再判断包含 app.kubernetes.io/role=primary 的 podName 是否正确且只有一个.
                    // 这里存在延迟的情况，但只要切换成功了最终会更新
                    List<PodDTO> primaryPods = kubeClient.listPod(
                            namespace,
                            Stream.concat(Arrays.stream(AppKind.MongoDB.labels(crName)),
                            Arrays.stream(new Label[]{new Label(
                                    CloudAppConstant.CustomLabels.POD_ROLE, CloudAppConstant.ROLE_PRIMARY)}))
                                    .toArray(Label[]::new));
                    if (primaryPods != null && primaryPods.size() == 1 && primaryPods.get(0).getPodName().equals(podName)) {
                        result.stopJob(true).msg("[MongoDBSwitchMasterWatch]mongodb switch master successful. ")
                                .status(StatusConstant.SUCCESS);
                    } else {
                        log.info("[MongoDBSwitchMasterWatch]Pod does not have primary role label yet");
                        result.msg("mongodb switching master ...");
                    }
                } else {
                    result.stopJob(true).msg("[MongoDBSwitchMasterWatch]mongodb switch master fail.")
                            .status(StatusConstant.FAIL);
                }
            }

            if (Objects.equals(state, "pending")) {
                long nowTime = new Date().getTime();
                // 耗时超过30分钟不成功就回滚
                if ((nowTime - startTime) > 30 * 60 * 1000) {
                    log.info("[MongoDBSwitchMasterWatch] mongodb 切换为主进行中，请稍后...");
                    result.msg("mongodb switching master ...");
                } else {
                    result.msg("mongodb switch master fail.");
                    result.stopJob(true).status(StatusConstant.FAIL);
                }
            }
            if (result.isStopped()) appService.handleWatchResult(app.getId(), result.isSuccessful());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return result.build();
    }

}
