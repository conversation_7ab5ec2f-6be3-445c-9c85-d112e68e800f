package cn.newdt.cloud.service;

import cn.newdt.cloud.common.CommonSearch;
import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.DatasourceConstant;
import cn.newdt.cloud.domain.CloudZone;
import cn.newdt.cloud.domain.KubeConfig;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.filter.ElResourceChangeLog;
import cn.newdt.cloud.mapper.CloudZoneMapper;
import cn.newdt.cloud.mapper.KubeConfigMapper;
import cn.newdt.cloud.utils.CustPreconditions;
import cn.newdt.cloud.utils.MybatisUtil;
import cn.newdt.cloud.vo.CloudZoneVO;
import cn.newdt.cloud.vo.KubeConfigVO;
import cn.newdt.commons.utils.UserUtil;
import com.google.common.collect.ImmutableMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.Min;
import java.util.List;
import java.util.Objects;

@Service
@DependsOn(value = "springBeanUtil")
public class CloudZoneService {
    @Autowired
    private CloudZoneMapper mapper;
    @Autowired
    private KubeConfigMapper configMapper;

    @Transactional
    public int save(CloudZone zone) {
        if (zone.isDefaultZone()) {
            CloudZone defaultZone = mapper.selectDefaultForUpdate(UserUtil.getSchema(), DatasourceConstant.CLOUD_ZONE);
            if ( defaultZone != null && !Objects.equals(defaultZone.getId(), zone.getId())) {
                defaultZone.setDefaultZone(false);
                mapper.updateByPrimaryKeySelective(UserUtil.getSchema(), DatasourceConstant.CLOUD_ZONE, defaultZone);
            }
        }
        if (zone.getId() != null) {
            return mapper.updateByPrimaryKeySelective(UserUtil.getSchema(), DatasourceConstant.CLOUD_ZONE, zone);
        }
        return mapper.insert(UserUtil.getSchema(), DatasourceConstant.CLOUD_ZONE, zone);
    }

    @ElResourceChangeLog(action = ActionEnum.DELETE_ZONE, kind = "Zone", id = "#zoneId", name = "@cloudZoneService.getById(#zoneId).name",
        msg = "'删除可用区'")
    @Transactional
    public int delete(@Min(value = 1, message = "Invalid argument, zoneId must be greater than 0") int zoneId) {
        CustPreconditions.checkState(zoneId != CloudZone.defaultID, "内置可用区不可删除");
        List<KubeConfig> kubeConfigs = configMapper.listAllColumnByMap(UserUtil.getSchema(),
                DatasourceConstant.CLOUD_KUBE_CONFIG_TABLE, ImmutableMap.of("zoneId", zoneId));
        for (KubeConfig kubeConfig : kubeConfigs) {
            kubeConfig.setZoneId(null);
            configMapper.updateByPrimaryKey(UserUtil.getSchema(),
                    DatasourceConstant.CLOUD_KUBE_CONFIG_TABLE, kubeConfig);
        }
        return mapper.deleteByPrimaryKey(UserUtil.getSchema(), DatasourceConstant.CLOUD_ZONE, zoneId);
    }

    public CloudZone getById(int zoneId) {
        return mapper.selectByPrimaryKey(UserUtil.getSchema(), DatasourceConstant.CLOUD_ZONE, zoneId);
    }

    public CloudZone getDefault() {
        return list(null).stream().filter(zone->zone.isDefaultZone()).findFirst()
                .orElseThrow(() -> new IllegalStateException("no default zone configured"));
    }

    public List<CloudZone> list(PageDTO pageDTO) {
        List<CommonSearch> conditions = pageDTO == null
                ? null
                : MybatisUtil.getJavaPropMapDBColumn(pageDTO.getCondition(),
                "CloudZone" + MybatisUtil.ORM_RESULT_MAP, "kind","id","crName");
        String sortClause = MybatisUtil.sortClause(pageDTO, "CloudZone" + MybatisUtil.ORM_RESULT_MAP);

        return mapper.list(UserUtil.getSchema(), DatasourceConstant.CLOUD_ZONE, conditions, sortClause, "name");
    }

    @Transactional
    @ElResourceChangeLog(action = ActionEnum.UPDATE_ZONE, kind = "Zone", id = "#zone.id", name = "#zone.name",
            msg = "'设置可用区' + #zone.name + '关联集群'")
    public void updateKubeZone(CloudZoneVO zone) {
        // batch update kubeconfig zoneId
        List<KubeConfig> kubes = zone.getKubes();
        for (KubeConfig kube : kubes) {
            kube.setZoneId(zone.getId());
            configMapper.updateSelective(UserUtil.getSchema(), DatasourceConstant.CLOUD_KUBE_CONFIG_TABLE,
                    kube);
        }
    }

}
