package cn.newdt.cloud.domain.alert;

import cn.newdt.cloud.domain.alert.json.Resource;
import cn.newdt.cloud.domain.alert.json.RuleSet;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.alert.AlertConfigSyncService;
import cn.newdt.cloud.utils.JsonUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;

/**
 * wrapper of everything needed to complete work of an alert configuration
 */
@Data
@NoArgsConstructor
public class AlertConfig {
    private RuleSet ruleSet;
    /**
     * @deprecated prometheus was at federated k8s
     */
    private Integer kubeId;
    private AlertRuleResource configInstance;
    // no used
    private String checksum;
    /**
     * 全局配置项
     */
    private Map<String, String> globalOptions;

    private Map<String, AlertMetric> metricMetaStore;
    private KubeClient kubeClient;
    private Map<String, AlertConfigSyncService.ChannelConfig> channelOptions;

    public AlertConfig(RuleSet ruleSet, Integer kubeId, AlertRuleResource configInstance, String checksum) {
        this.ruleSet = ruleSet;
        this.kubeId = kubeId;
        this.configInstance = configInstance;
        this.checksum = checksum;
        if (ruleSet.getResources() == null) {
            Resource resource = new Resource().copyFrom(configInstance);
            ruleSet.setResources(Collections.singletonList(resource));
        }
    }

    public Optional<AlertNotifyChannel.EmailConfig> checkNullableEmailChannelConfig() {
        if (this.channelOptions != null) {
            if (channelOptions.containsKey("email")) {
                AlertChannel email = this.channelOptions.get("email").getChannelSetting();
                AlertNotifyChannel.EmailConfig emailConfig = JsonUtil.toObject(AlertNotifyChannel.EmailConfig.class, email.getConfig());
                return Optional.ofNullable(emailConfig);
            }
        }
        return Optional.empty();
    }

}
