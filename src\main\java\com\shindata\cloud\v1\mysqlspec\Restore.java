package com.shindata.cloud.v1.mysqlspec;

@com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)
@com.fasterxml.jackson.annotation.JsonPropertyOrder({"fullSource","includeLocalLog","incrementalSource","lastId","lastTimestamp","logPath","remote"})
@com.fasterxml.jackson.databind.annotation.JsonDeserialize(using = com.fasterxml.jackson.databind.JsonDeserializer.None.class)
public class Restore implements io.fabric8.kubernetes.api.model.KubernetesResource {

    @com.fasterxml.jackson.annotation.JsonProperty("fullSource")
    @io.fabric8.generator.annotation.Required()
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String fullSource;

    public String getFullSource() {
        return fullSource;
    }

    public void setFullSource(String fullSource) {
        this.fullSource = fullSource;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("includeLocalLog")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Boolean includeLocalLog;

    public Boolean getIncludeLocalLog() {
        return includeLocalLog;
    }

    public void setIncludeLocalLog(Boolean includeLocalLog) {
        this.includeLocalLog = includeLocalLog;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("incrementalSource")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String incrementalSource;

    public String getIncrementalSource() {
        return incrementalSource;
    }

    public void setIncrementalSource(String incrementalSource) {
        this.incrementalSource = incrementalSource;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("lastId")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String lastId;

    public String getLastId() {
        return lastId;
    }

    public void setLastId(String lastId) {
        this.lastId = lastId;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("lastTimestamp")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String lastTimestamp;

    public String getLastTimestamp() {
        return lastTimestamp;
    }

    public void setLastTimestamp(String lastTimestamp) {
        this.lastTimestamp = lastTimestamp;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("logPath")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String logPath;

    public String getLogPath() {
        return logPath;
    }

    public void setLogPath(String logPath) {
        this.logPath = logPath;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("remote")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.shindata.cloud.v1.mysqlspec.restore.Remote remote;

    public com.shindata.cloud.v1.mysqlspec.restore.Remote getRemote() {
        return remote;
    }

    public void setRemote(com.shindata.cloud.v1.mysqlspec.restore.Remote remote) {
        this.remote = remote;
    }
}

