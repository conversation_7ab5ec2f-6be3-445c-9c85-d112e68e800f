package cn.newdt.cloud.web;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.domain.CloudZone;
import cn.newdt.cloud.domain.KubeConfig;
import cn.newdt.cloud.domain.alert.AlertChannel;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.filter.ElResourceChangeLog;
import cn.newdt.cloud.service.CloudZoneService;
import cn.newdt.cloud.service.KubeConfigService;
import cn.newdt.cloud.service.impl.TenantService;
import cn.newdt.cloud.vo.CloudZoneVO;
import cn.newdt.cloud.vo.TenantClusterVO;
import com.google.common.collect.ImmutableMap;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("")
public class CloudZoneController {
    @Autowired
    private CloudZoneService cloudZoneService;
    @Autowired
    private KubeConfigService kubeConfigService;
    @Autowired
    private TenantService tenantService;

    @ApiOperation("")
    @PostMapping("cloudmanage/zone")
    @ElResourceChangeLog(action = ActionEnum.CREATE_ZONE, kind = "Zone", id = "#zone.id", name = "#zone.name",
            msg = "'创建可用区' + #zone.name")
    public Object createChannel(@RequestBody CloudZone zone) {
        return cloudZoneService.save(zone);
    }

    @PutMapping("cloudmanage/zone")
    @ElResourceChangeLog(action = ActionEnum.UPDATE_ZONE, kind = "Zone", id = "#zone.id", name = "#zone.name",
            msg = "'编辑可用区' + #zone.name")
    public Object updateChannel(@RequestBody CloudZone zone) {
        return cloudZoneService.save(zone);
    }

    @ApiOperation("")
    @DeleteMapping("cloudmanage/zone")
    public Object deleteZone(int zoneId) {
        for (KubeConfig kubeConfig : kubeConfigService.list(ImmutableMap.of("zoneId", zoneId))) {
            kubeConfig.setZoneId(null);
            kubeConfigService.update(kubeConfig);
        }
        return cloudZoneService.delete(zoneId);
    }

    @ApiOperation("")
    @PostMapping(value = {"cloudmanage/zone/list", "cloudnormal/zone/list"})
    public Object list(@RequestBody PageDTO pageDTO, @RequestParam(required = false) Integer tenantId) {
        List<CloudZone> zones = cloudZoneService.list(pageDTO);
        if (tenantId != null) {
            List<TenantClusterVO> kubes = tenantService.getOwnedClustersOfTenant(tenantId);
            zones = zones.stream().filter(zone ->
                kubes.stream().anyMatch(kube-> zone.getId().equals(kube.getZoneId()))
            ).collect(Collectors.toList());
        }
        return zones;
    }

    @ApiOperation("批量修改集群所属可用区")
    @PutMapping("cloudmanage/zone/kubes/batch-update")
    public void updateKubeZone(@RequestBody List<CloudZoneVO> zoneVOList) {
        for (CloudZoneVO zoneVO : zoneVOList) {
            String name = cloudZoneService.getById(zoneVO.getId()).getName();
            zoneVO.setName(name);
            cloudZoneService.updateKubeZone(zoneVO);
        }
    }

}
