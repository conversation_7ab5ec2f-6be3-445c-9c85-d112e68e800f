package cn.newdt.cloud.constant;

import cn.newdt.cloud.domain.AppMetadata;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.cr.*;
import cn.newdt.cloud.dto.CloudInstDTO;
import cn.newdt.cloud.utils.JsonUtil;
import cn.newdt.cloud.vo.CloudAppVO;
import com.google.common.collect.ImmutableList;
import com.pingcap.v1alpha1.TidbCluster;
import com.shindata.clickhouse.v1.ClickHouseInstallation;
import cn.newdt.cloud.dto.Label;
import cn.newdt.cloud.utils.AppUtil;
import cn.newdt.cloud.utils.MGRUtil;
import cn.newdt.cloud.utils.MongoUtil;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.enums.DBTypeEnum;
import cn.newdt.commons.exception.CustomException;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.shindata.cloud.v1.Vastbase;
import com.shindata.mysql.v1.MySQLHA;
import com.shindata.opengauss.v1.OpenGaussCluster;
import com.shindata.postgre.v1.PostgreSql;
import com.shindata.redis.v1.RedisCluster;
import io.fabric8.kubernetes.client.CustomResource;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.v1beta1.FlinkDeployment;
import org.apache.flink.v1beta1.FlinkDeploymentStatus;

import javax.annotation.Nonnull;
import java.io.File;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static cn.newdt.cloud.constant.CloudAppConstant.*;
import static cn.newdt.commons.response.ResponseStatusCode.SQL_EXECUTE_EXCEPTIONS;

// todo refact cr 分层抽象
/**
 * product 应用产品类型
 * kind 应用组件类型
 */

public enum AppKind {
    /**
     * mysql ha
     */
    MYSQL_HA("MySQL", "MySQL", "HA", "mysql", 3306, MySQLHA.class, "mysql-operator-controller-manager") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{
                    new Label(CustomLabels.APP, "mysql"), 
                    new Label(CustomLabels.ARCHITECTURE, "mysqlha"), 
                    new Label(CustomLabels.APP_NAME, crName)};
        }

        @Override
        public String instClass() {
            return DmpInstClass.DB;
        }

        @Override
        public String instType() {
            return DBTypeEnum.MYSQL.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, CloudApp.IpNode[] ipNodes) {
            return ipNodes != null
                    ? Arrays.stream(ipNodes).map(ip -> String.format("mysqlha-%s-%s", app.getCrName(), ip.getIp().replace(".", "-"))).collect(Collectors.toList())
                    : Collections.emptyList();
        }

        @Override
        public String getAlertStatus(CustomResource cr) {
            return AppUtil.getAlertStatus(cr);
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            //[root@localhost testal]# pwd
            ///mnt/cloud-nfs/mysql/mysqlha/yan-dev/testal
            //[root@localhost testal]# ls
            //20250526.144143.full.xbstream  20250526.144143.inc.20250526.145543.xbstream
            return String.join(File.separator,
                    getKind().toLowerCase(), "mysqlha", namespace, crName, backupFileName);
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "mysqlha-" + name + "-svc";
        }

        @Override
        public String getReadServiceName(String name) {
            return "mysqlha-" + name + "-svc-read";
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return 2;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return Collections.singletonList("mysqlha-" + app.getCrName() + "-");
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage, ImageKindEnum.Percona_XtraBackup,
                    ImageKindEnum.Exporter, ImageKindEnum.Filebeat, ImageKindEnum.FTP);
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("backup", Pattern.compile("mysqlha-" + app.getCrName() +"-pvc-backup(-[0-9]{1,3}){4}"),
                    "data", Pattern.compile("mysqlha-" + app.getCrName() +"-pvc(-[0-9]{1,3}){4}"));
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put("^mysqlha-.*-\\d+$", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            HashMap<String, String> grafanaLabelMap = new HashMap<>();
            grafanaLabelMap.put(GraganaKeys.GRAFANA_URL, grafanaServer + "/grafana/d/ZVzcvcKIk/ndt-mysql-dashboard?orgId=1&var-interval=\\$__auto_interval_interval&theme=light&kiosk=embeb");
            grafanaLabelMap.put(GraganaKeys.VAR_NAMESPACE, app.getNamespace());
            grafanaLabelMap.put(GraganaKeys.VAR_JOB, "mysqlha-" + app.getCrName());
            return JsonUtil.toJson(grafanaLabelMap);
        }
    },
    /**
     * mongodb replica set
     */
    MongoDB("MongoDB", "MongoDB", "ReplicaSet", "mongod", 27017, MongoDBCommunity.class, "mongodb-kubernetes-operator") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{
                    new Label(LABEL_MONGO_NAME, crName + "-svc"),
                    new Label(CustomLabels.APP, getKind().toLowerCase()),
                    new Label(CustomLabels.APP_COMPONENT, getKind().toLowerCase()),
                    new Label(CustomLabels.APP_NAME, crName)};
        }

        @Override
        public String instClass() {
            return DmpInstClass.DB;
        }

        @Override
        public String instType() {
            return DBTypeEnum.MONGODB.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, CloudApp.IpNode[] ipNodes) {
            return MongoUtil.getPodNames(app.getCrName(), app.getMembers());
        }

        @Override
        public String getAlertStatus(CustomResource cr) {
            return cr.getStatus() != null ? ((MongoDBCommunity) cr).getStatus().getMessage() : null;
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            //[root@localhost 20250605181420]# pwd
            ///mnt/cloud-nfs/mongodb/replicaset/lianzb-m/monrpbak/20250605181420
            //[root@localhost 20250605181420]# ls
            //20250605181420.tar.gz  admin
            return String.join(File.separator,
                    getKind().toLowerCase(), getArch().toLowerCase(),
                    namespace, crName, backupFileName, backupFileName + ".tar.gz");
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return  name + "-mongo" + "-primary-svc";
        }

        @Override
        public String getReadServiceName(String name) {
            return null;
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return 1;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return Collections.singletonList(app.getCrName() + "-\\\\d");
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage, ImageKindEnum.FTP, ImageKindEnum.Filebeat,
                    ImageKindEnum.MongoRS_Agent_RP,
                    ImageKindEnum.MongoRS_BK_HostPre,
                    ImageKindEnum.MongoRS_Mongod_PH,
                    ImageKindEnum.MongoRS_Agent,
                    ImageKindEnum.Exporter);
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put("-\\d$", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            HashMap<String, String> grafanaLabelMap = new HashMap<>();
            grafanaLabelMap.put(GraganaKeys.GRAFANA_URL, grafanaServer + "/grafana/d/YKxJk3KIz/ndt-mongodb-dashboard?orgId=1&var-interval=\\$__auto_interval_interval&theme=light&kiosk=embeb");
            grafanaLabelMap.put(GraganaKeys.VAR_NAMESPACE, app.getNamespace());
            grafanaLabelMap.put(GraganaKeys.VAR_JOB, app.getCrName());
            return JsonUtil.toJson(grafanaLabelMap);
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("backup", Pattern.compile("backup-volume-" + app.getCrName() +"-\\d+"),
                    "data", Pattern.compile("(data|logs)-volume-" + app.getCrName() +"-\\d+"));
        }
    },
    /**
     * mongodb cluster
     */
    MongoDB_Cluster("MongoDB", "MongoDB", "Cluster", "mongodb", 27017, MongoDBCluster.class, "mongodb-operator-controller-manager") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{
                    new Label(CustomLabels.APP_NAME, crName),
                    new Label(CustomLabels.APP, "mongodb"), 
                    new Label(CustomLabels.ARCHITECTURE, "cluster")};
        }

        @Override
        public String instClass() {
            return DmpInstClass.DB;
        }

        @Override
        public String instType() {
            return DBTypeEnum.MONGODB.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, CloudApp.IpNode[] ipNodes) {
            String yaml = StringUtils.isNotEmpty(app.getCr()) ? app.getCr() : app.getCrRun();
            MongoDBCluster mg = YamlEngine.unmarshal(yaml, MongoDBCluster.class);
            List<String> shards = mg.getSpec().getShardServers().getIpList().stream().map(n -> "mc-" + app.getCrName() + "-shard-" + n.replace(".", "-"))
                    .collect(Collectors.toList());
            List<String> configs = mg.getSpec().getConfigServers().getIpList().stream().map(n -> "mc-" + app.getCrName() + "-config-" + n.replace(".", "-"))
                    .collect(Collectors.toList());
            List<String> routers = mg.getSpec().getRouterServers().getIpList().stream().map(n -> "mc-" + app.getCrName() + "-router-" + n.replace(".", "-"))
                    .collect(Collectors.toList());
            shards.addAll(configs);
            shards.addAll(routers);
            return shards;
        }

        @Override
        public String getAlertStatus(CustomResource cr) {
            return AppUtil.getAlertStatus(cr);
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            //[root@localhost mdc1]# pwd
            ///mnt/cloud-nfs/mongodb/cluster/default/mdc1
            //[root@localhost mdc1]# ls
            //2025-06-05T09:42:38Z  2025-06-05T09:42:38Z.pbm.json  2025-06-05T09-42-38Z.tgz  pbmPitr
            return String.join(File.separator, getKind().toLowerCase(), getArch().toLowerCase(),
                    namespace, crName, backupFileName.replaceAll(":","-") + ".tgz");
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "mc-" + name + "-service";
        }

        @Override
        public String getReadServiceName(String name) {
            return null;
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return 1;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            String yaml = StringUtils.isNotEmpty(app.getCr()) ? app.getCr() : app.getCrRun();
            MongoDBCluster mg = YamlEngine.unmarshal(yaml, MongoDBCluster.class);
            List<String> shards = mg.getSpec().getShardServers().getIpList().stream().map(n -> "mc-" + app.getCrName() + "-shard-" + n.replace(".", "-"))
                    .collect(Collectors.toList());
            List<String> configs = mg.getSpec().getConfigServers().getIpList().stream().map(n -> "mc-" + app.getCrName() + "-config-" + n.replace(".", "-"))
                    .collect(Collectors.toList());
            List<String> routers = mg.getSpec().getRouterServers().getIpList().stream().map(n -> "mc-" + app.getCrName() + "-router-" + n.replace(".", "-"))
                    .collect(Collectors.toList());
            shards.addAll(configs);
            shards.addAll(routers);
            return shards;
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(
                    ImageKindEnum.MainImage,
                    ImageKindEnum.FTP,
                    ImageKindEnum.Filebeat,
                    ImageKindEnum.Exporter,
                    ImageKindEnum.MongoDBCluster_PBM);
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB,
                    ComponentKindEnum.CONFIG_SERVER,
                    ComponentKindEnum.MONGOS);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put("^mc-.*-shard-(\\d+(-\\d+)*)$", this.getContainerName());
            // result.put("^mc-.*-config-(\\d+(-\\d+)*)$", this.getContainerName());
            // result.put("^mc-.*-router-(\\d+(-\\d+)*)$", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB,
                    ComponentKindEnum.CONFIG_SERVER);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            HashMap<String, String> grafanaLabelMap = new HashMap<>();
            grafanaLabelMap.put(GraganaKeys.GRAFANA_URL, grafanaServer + "/grafana/d/YKxJk3KIz/ndt-mongodb-dashboard?orgId=1&var-interval=\\$__auto_interval_interval&theme=light&kiosk=embeb");
            grafanaLabelMap.put(GraganaKeys.VAR_NAMESPACE, app.getNamespace());
            grafanaLabelMap.put(GraganaKeys.VAR_JOB, "mc-" + app.getCrName());
            return JsonUtil.toJson(grafanaLabelMap);
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("backup", Pattern.compile("mc-" + app.getCrName() +"-(config|shard)(-[0-9]{1,3}){4}" + "-backup-pvc"),
                    "data", Pattern.compile("mc-" + app.getCrName() +"-(config|router|shard)(-[0-9]{1,3}){4}" + "-data-pvc"));
        }
    },
    /**
     * opengauss replica set
     */
    OpenGauss("openGauss", "openGauss", "HA", "og", 5432, OpenGaussCluster.class, "opengauss-operator-controller-manager") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{new Label(LABEL_OPENGAUSS_NAME, crName), new Label(CustomLabels.APP, "opengauss")};
        }

        @Override
        public String instClass() {
            return DmpInstClass.DB;
        }

        @Override
        public String instType() {
            return DBTypeEnum.OPENGAUSS.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, CloudApp.IpNode[] ipNodes) {
//            return AppUtil.generateOgPodName(app.getCrName(), Arrays.asList(ipNodes))
//                    .stream().map(d -> d.getPodName()).collect(Collectors.toList());
            return ImmutableList.of("og-" + app.getCrName() + "-pod" + "(-[0-9]+){4}");
        }

        @Override
        public String getAlertStatus(CustomResource cr) {
            return AppUtil.getAlertStatus(cr);
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            //[root@localhost backup]# pwd
            ///mnt/cloud-nfs/opengauss/backups/lianzb-m/test
            //[root@localhost test]# ls
            //pg_probackup.conf  SXBTT1  SXBTT1.tgz  SXCZZ7  SXCZZ7.tgz
            return String.join(File.separator, getKind().toLowerCase(), "backups",
                    namespace, crName, backupFileName + ".tgz");
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "og-" + name + "-svc";
        }

        @Override
        public String getReadServiceName(String name) {
            return "og-" + name + "-svc-read";
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return 2;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return Collections.singletonList("og-" + app.getCrName() + "-pod-");
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage,ImageKindEnum.Sidecar,ImageKindEnum.FTP);
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("backup", Pattern.compile("og-" + app.getCrName() + "-pod(-[0-9]{1,3}){4}-backup-pvc"),
                    "data", Pattern.compile("og-" + app.getCrName() + "-pod(-[0-9]{1,3}){4}-data-pvc"),
                    "log", Pattern.compile("og-" + app.getCrName() + "-pod(-[0-9]{1,3}){4}-log-pvc"));
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put("^og-.*-pod-(\\d+(-\\d+)*)$", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            HashMap<String, String> grafanaLabelMap = new HashMap<>();
            grafanaLabelMap.put(GraganaKeys.GRAFANA_URL, grafanaServer + "/grafana/d/6kAcvitSz/ndt-opengauss-dashboard?orgId=1&var-interval=\\$__auto_interval_interval&theme=light&kiosk=embeb");
            grafanaLabelMap.put(GraganaKeys.VAR_NAMESPACE, app.getNamespace());
            grafanaLabelMap.put(GraganaKeys.VAR_JOB, "og-" + app.getCrName());
            return JsonUtil.toJson(grafanaLabelMap);
        }
    },
    /**
     * redis / sentinel
     */
    Redis("Redis", "Redis", "HA-Sentinel", "redis", 6379, com.shindata.redis.v1.Redis.class, "redis-operator-controller-manager") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{
                    new Label(CustomLabels.APP_NAME, crName),
                    new Label(CustomLabels.APP, "redis"),
                    new Label(CustomLabels.ARCHITECTURE, "ha")
            };
        }

        @Override
        public String instClass() {
            return DmpInstClass.DB;
        }

        @Override
        public String instType() {
            return DBTypeEnum.REDIS.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, CloudApp.IpNode[] ipNodes) {
//            return AppUtil.generateRedisPodName(app.getCrName(), Arrays.asList(ipNodes))
//                    .stream().map(d -> d.getPodName()).collect(Collectors.toList());
            return ImmutableList.of("redis-" + app.getCrName() + "(-[0-9]+){4}", "redis-"+app.getCrName()+"-sentinel" + "(-[0-9]{4})");
        }

        @Override
        public String getAlertStatus(CustomResource cr) {
            return AppUtil.getAlertStatus(cr);
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            //[root@localhost ll]# pwd
            ///mnt/cloud-nfs/redis/ha/yan-dev/ll
            //[root@localhost ll]# ls
            //dump_20250409180503.rdb
            return String.join(File.separator,
                    getKind().toLowerCase(), "ha", namespace, crName, backupFileName);
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "redis-" + name + "-" + ip.replace(".", "-") + "-service";
        }

        @Override
        public String getReadServiceName(String name) {
            return null;
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return members;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return ImmutableList.of("redis-" + app.getCrName() + "-", "redis-" + app.getCrName() + "-sentinel-");
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage,ImageKindEnum.Exporter, ImageKindEnum.Filebeat,
                    ImageKindEnum.FTP, ImageKindEnum.Redis_Shake);
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("data", Pattern.compile("redis-" + app.getCrName() + "(-sentinel)?" + "(-[0-9]{1,3}){4}-data-pvc"),
                    "backup", Pattern.compile("redis-" + app.getCrName() + "(-[0-9]{1,3}){4}-backup-pvc"));
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB, ComponentKindEnum.Redis_Sentinel, ComponentKindEnum.Redis_Shake);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put("^redis-.*-\\d+$", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            HashMap<String, String> grafanaLabelMap = new HashMap<>();
            grafanaLabelMap.put(GraganaKeys.GRAFANA_URL, grafanaServer + "/grafana/d/NlBTD5FSk/ndt-redis-dashboard?orgId=1&var-interval=\\$__auto_interval_interval&theme=light&kiosk=embeb");
            grafanaLabelMap.put(GraganaKeys.VAR_NAMESPACE, app.getNamespace());
            grafanaLabelMap.put(GraganaKeys.VAR_JOB, "redis-" + app.getCrName());
            grafanaLabelMap.put(GraganaKeys.SENTINEL_JOB, app.getCrName());
            return JsonUtil.toJson(grafanaLabelMap);
        }
    },
    /**
     * sentinel
     */
    Sentinel("Redis", "Redis-Sentinel", "Cluster", "redis", 26379, com.shindata.redis.v1.Sentinel.class, "redis-operator-controller-manager") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{new Label(CustomLabels.APP_NAME, crName),
                    new Label(CustomLabels.APP, "redis"),
                    new Label(CustomLabels.APP_COMPONENT, "sentinel")};
        }

        @Override
        public String instClass() {
            return DmpInstClass.MW;
        }

        @Override
        public String instType() {
            return DBTypeEnum.REDIS.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, @Nonnull CloudApp.IpNode[] ipNodes) {
            return ImmutableList.of("sentinel-" + app.getCrName() + "(-[0-9]+){4}");
        }

        @Override
        public String getAlertStatus(CustomResource cr) {
            return AppUtil.getAlertStatus(cr);
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            return null;
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "sentinel-" + name + "-service";
        }

        @Override
        public String getReadServiceName(String name) {
            return null;
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return 1;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return ImmutableList.of("sentinel-" + app.getCrName() + "(-[0-9]+){4}");
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage, ImageKindEnum.Filebeat, ImageKindEnum.Exporter);
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put("^sentinel-.*-\\d+$", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            return null;
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("data", Pattern.compile("sentinel-" + app.getCrName() + "(-[0-9]{1,3}){4}-data-pvc"));
        }
    },
    /**
     * Zookeeper
     */
    Zookeeper("Kafka", "Zookeeper", "Cluster", "zookeeper", 2181, cn.newdt.cloud.domain.cr.Zookeeper.class, "zookeeper-operator-controller-manager") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{new Label(LABEL_ZOOKEEPER_NAME, crName), new Label(CustomLabels.APP, "zookeeper")};
        }

        @Override
        public String instClass() {
            return DmpInstClass.MW;
        }

        @Override
        public String instType() {
            return DBTypeEnum.KAFKA.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, @Nonnull CloudApp.IpNode[] ipNodes) {
            return ImmutableList.of("zk-" + app.getCrName() + "(-[0-9]+){4}");
        }

        @Override
        public String getAlertStatus(CustomResource cr) {
            return AppUtil.getAlertStatus(cr);
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            return null;
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "zk-" + name;
        }

        @Override
        public String getReadServiceName(String name) {
            return null;
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return 0;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return Collections.singletonList("zk-" + app.getCrName() + "-");
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage, ImageKindEnum.Exporter, ImageKindEnum.Filebeat);
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put("^zk-.*-\\d+$", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            return null;
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("data", Pattern.compile("zk-" + app.getCrName() + "-pvc(-[0-9]{1,3}){4}"));
        }
    },
    /**
     * Kafka
     */
    Kafka("Kafka", "Kafka", "Cluster", "kafka", 9092, com.shindata.kafka.v1.Kafka.class, "kafka-operator-controller-manager") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{new Label(LABEL_KAFKA_NAME, crName), new Label(CustomLabels.APP, "kafka")};
        }

        @Override
        public String instClass() {
            return DmpInstClass.MW;
        }

        @Override
        public String instType() {
            return DBTypeEnum.KAFKA.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, @Nonnull CloudApp.IpNode[] ipNodes) {
            return ImmutableList.of("kafka-" + app.getCrName() + "(-[0-9]+){4}", "zk-" + app.getCrName() + "(-[0-9]+){4}");

        }

        @Override
        public String getAlertStatus(CustomResource cr) {
            return AppUtil.getAlertStatus(cr);
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            return null;
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "kafka-" + name + "-service-" + ip.replace(".", "-");
        }

        @Override
        public String getReadServiceName(String name) {
            return null;
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return members + Zookeeper.getServiceManagerNum(serviceType, 0); // + 1 zookeeper 端口
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return ImmutableList.of("kafka-" + app.getCrName(), "zk-" + app.getCrName());
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage,ImageKindEnum.Exporter, ImageKindEnum.Filebeat, ImageKindEnum.ZK_Component, ImageKindEnum.ZK_Component_Exporter);
        }


        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB, ComponentKindEnum.Zookeeper, ComponentKindEnum.Kafka_MirrorMaker);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
             result.put("^kafka-.*-kafka-.+$", this.getContainerName());
            result.put("^kafka-.*-zookeeper-.+$", "zookeeper");
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            HashMap<String, String> grafanaLabelMap = new HashMap<>();
            grafanaLabelMap.put(GraganaKeys.GRAFANA_URL, grafanaServer + "/grafana/d/jwPKIsniz/ndt-kafka-dashboard?orgId=1&var-interval=\\$__auto_interval_interval&theme=light&kiosk=embeb");
            grafanaLabelMap.put(GraganaKeys.VAR_NAMESPACE, app.getNamespace());
            grafanaLabelMap.put(GraganaKeys.VAR_JOB, "kafka-" + app.getCrName());
            grafanaLabelMap.put(GraganaKeys.ZK_JOB, app.getCrName());
            return JsonUtil.toJson(grafanaLabelMap);
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("data", Pattern.compile("(kafka|zk)-" + app.getCrName() + "-pvc(-[0-9]{1,3}){4}"));
        }
    },
    /**
     * NameServer
     */
    NameServer("RocketMQ", "NameServer", "Cluster", "nameservice", 9876, NameServer.class, "rocketmq-operator-controller-manager") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{
                    new Label(CustomLabels.APP, "rocketmq"),
                    new Label(CustomLabels.APP_COMPONENT, "nameserver"),
                    new Label(CustomLabels.APP_NAME, crName)};
        }

        @Override
        public String instClass() {
            return DmpInstClass.MW;
        }

        @Override
        public String instType() {
            return DBTypeEnum.ROCKETMQ.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, @Nonnull CloudApp.IpNode[] ipNodes) {
            return ImmutableList.of("rocketmq-" + app.getCrName() + "-nameserver(-[0-9]+){4}");
        }

        @Override
        public String getAlertStatus(CustomResource cr) {
            return AppUtil.getAlertStatus(cr);
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            return null;
        }

        @Override
        public String getContainerName(){
            return "nameserver";
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "rocketmq-" + name + "-nameserver-svc";
        }

        @Override
        public String getReadServiceName(String name) {
            return null;
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return 1;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return ImmutableList.of("rocketmq-" + app.getCrName() + "-nameserver(-[0-9]+){4}");

        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage,ImageKindEnum.Exporter, ImageKindEnum.Filebeat);
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put("^rocketmq-.*-nameserver(-[0-9]+){4}$", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            return null;
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("data", Pattern.compile("rocketmq-" + app.getCrName() + "-nameserver-pvc(-[0-9]{1,3}){4}"));
        }
    },
    /**
     * Broker
     */
    Broker("RocketMQ", "Broker", "Cluster", "broker", 10911, cn.newdt.cloud.domain.cr.Broker.class, "rocketmq-operator-controller-manager") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{
                    new Label(CustomLabels.APP, "rocketmq"),
                    new Label(CustomLabels.APP_COMPONENT, "broker"),
                    new Label(CustomLabels.APP_NAME, crName)};
        }

        @Override
        public String instClass() {
            return DmpInstClass.MW;
        }

        @Override
        public String instType() {
            return DBTypeEnum.ROCKETMQ.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, @Nonnull CloudApp.IpNode[] ipNodes) {
            return ImmutableList.of("rocketmq-" + app.getCrName() + "-broker(-[0-9]+){4}");
        }

        @Override
        public String getAlertStatus(CustomResource cr) {
            return AppUtil.getAlertStatus(cr);
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            return null;
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "rocketmq-" + name + "-broker-service-" + ip.replace(".", "-");
        }

        @Override
        public String getReadServiceName(String name) {
            return null;
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return members;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return ImmutableList.of("rocketmq-" + app.getCrName() + "-broker(-[0-9]+){4}");
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage, ImageKindEnum.Filebeat);
        }


        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put("^rocketmq-.*-broker(-[0-9]+){4}$", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            HashMap<String, String> grafanaLabelMap = new HashMap<>();
            grafanaLabelMap.put(GraganaKeys.GRAFANA_URL, grafanaServer + "/grafana/d/h4_3CAUnz/ndt-rocketmq-dashboard?orgId=1&var-interval=\\$__auto_interval_interval&theme=light&kiosk=embeb");
            grafanaLabelMap.put(GraganaKeys.VAR_NAMESPACE, app.getNamespace());
            grafanaLabelMap.put(GraganaKeys.VAR_CLUSTER, app.getCrName());
            grafanaLabelMap.put(GraganaKeys.NAMESVR_JOB, app.getCrName());
            return JsonUtil.toJson(grafanaLabelMap);
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("data", Pattern.compile("rocketmq-" + app.getCrName() + "-broker-pvc(-[0-9]{1,3}){4}"));
        }
    },
    /*
     *
     * Redis-Cluster枚举
     * @param Redis
     * @param Cluster 架构类型
     * @param redis  容器名
     * @param 6379 默认端口号
     * @param RdisCluster实体类
     * @param redis-operator-system_redis-operator-controller-manager 命名空间
     * */
    Redis_Cluster("Redis", "Redis", "Cluster", "redis", 6379, RedisCluster.class, "redis-operator-controller-manager") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{new Label(CustomLabels.APP_NAME, crName),
                    new Label(CustomLabels.APP, "redis"),
                    new Label(CustomLabels.ARCHITECTURE, "cluster")};
        }

        @Override
        public String instClass() {
            return DmpInstClass.DB;
        }

        @Override
        public String instType() {
            return DBTypeEnum.REDIS.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, @Nonnull CloudApp.IpNode[] ipNodes) {
            return ImmutableList.of("rc-" + app.getCrName() + "(-[0-9]+){4}");
        }

        @Override
        public String getAlertStatus(CustomResource cr) {
            return AppUtil.getAlertStatus(cr);
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            //[root@localhost 20250528152730]# pwd
            ///mnt/cloud-nfs/redis/cluster/lianzb-m/backuprc/20250528152730
            //[root@localhost 20250528152730]# ls
            //dump_10922.rdb  dump_16383.rdb  dump_5461.rdb 20250528152730.tar.gz
            return String.join(File.separator, getKind().toLowerCase(), getArch().toLowerCase(),
                    namespace, crName, backupFileName, backupFileName + ".tar.gz");
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "rc-" + name + "-" + ip.replace(".", "-") + "-service";
        }

        @Override
        public String getReadServiceName(String name) {
            return null;
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            if (ServiceType.NODE_PORT.equals(serviceType)) {
                return members * 2;
            } else if (ServiceType.LOAD_BALANCER.equals(serviceType)) {
                return members;
            }
            return 0;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return ImmutableList.of("rc-" + app.getCrName() + "(-[0-9]+){4}");
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage,ImageKindEnum.Exporter, ImageKindEnum.Filebeat, ImageKindEnum.FTP);
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("data", Pattern.compile("rc-" + app.getCrName() + "(-[0-9]{1,3}){4}-data-pvc"),
                    "backup", Pattern.compile("rc-" + app.getCrName() + "(-[0-9]{1,3}){4}-backup-pvc"));
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put("^rc-.*-\\d+$", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            HashMap<String, String> grafanaLabelMap = new HashMap<>();
            grafanaLabelMap.put(GraganaKeys.GRAFANA_URL, grafanaServer + "/grafana/d/NlBTD5FSk/ndt-redis-dashboard?orgId=1&var-interval=\\$__auto_interval_interval&theme=light&kiosk=embeb");
            grafanaLabelMap.put(GraganaKeys.VAR_NAMESPACE, app.getNamespace());
            grafanaLabelMap.put(GraganaKeys.VAR_JOB, "rc-" + app.getCrName());
            return JsonUtil.toJson(grafanaLabelMap);
        }
    },

    Elasticsearch("Elasticsearch", "Elasticsearch", "Cluster", "elasticsearch", 9200, Elasticsearch.class, "elastic-operator") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{
                    new Label(CustomLabels.APP, getKind().toLowerCase() ),
                    new Label(CustomLabels.APP_NAME, crName),
                    new Label(CustomLabels.APP_COMPONENT, getKind().toLowerCase()),
                    new Label(ES_APP_NAME, crName)};
        }

        @Override
        public String instClass() {
            return DmpInstClass.MW;
        }

        @Override
        public String instType() {
            return DBTypeEnum.ELASTICSEARCH.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, @Nonnull CloudApp.IpNode[] ipNodes) {
            String yaml = StringUtils.isNotEmpty(app.getCr()) ? app.getCr() : app.getCrRun();
            Elasticsearch es = YamlEngine.unmarshal(yaml, Elasticsearch.class);
            List<String> podNames = new ArrayList<>();
            // 格式: [crname]-es-[nodename]-[seq num] e.g. estestlian-es-data-0
            for (cn.newdt.cloud.domain.cr.Elasticsearch.ElasticsearchNodeSet nodeSet : es.getSpec().getNodeSets()) {
                List<String> podsOfThisNode = IntStream.range(0, nodeSet.getCount())
                        .mapToObj(i -> app.getCrName() + "-es-" + nodeSet.getName() + "-" + i)
                        .collect(Collectors.toList());
                podNames.addAll(podsOfThisNode);
            }
            return podNames;
        }

        @Override
        public String getAlertStatus(CustomResource cr) {
            return cr.getStatus() == null ? "unknown" : ((Elasticsearch) cr).getStatus().getHealth();
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            //[root@localhost lianes]# pwd
            ///mnt/cloud-nfs/elasticsearch/lianz/lianes
            //[root@localhost lianes]# ls
            //dump-20240807174241.tar.gz
            return String.join(File.separator,
                    getKind().toLowerCase(), namespace, crName, backupFileName);
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "es-" + name;
        }

        @Override
        public String getReadServiceName(String name) {
            return null;
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return 1;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            String yaml = StringUtils.isNotEmpty(app.getCr()) ? app.getCr() : app.getCrRun();
            Elasticsearch es = YamlEngine.unmarshal(yaml, Elasticsearch.class);
            return Arrays.stream(es.getSpec().getNodeSets()).map(n -> app.getCrName() + "-es-" + n.getName() + "-")
                    .collect(Collectors.toList());
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage,ImageKindEnum.Exporter, ImageKindEnum.Filebeat, ImageKindEnum.Dump, ImageKindEnum.FTP);
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("backup", Pattern.compile("es-dump-data-" + app.getCrName() + "-es-data-\\d+"),
                    "data", Pattern.compile("elasticsearch-data-" + app.getCrName() + "-es-(data|masters)-\\d+"));
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put("-es-masters-\\d+$", this.getContainerName());
            // result.put("-es-data-\\d+$", this.getContainerName());
            // result.put(".+-es-exporter-.+", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            HashMap<String, String> grafanaLabelMap = new HashMap<>();
            grafanaLabelMap.put(GraganaKeys.GRAFANA_URL, grafanaServer + "/grafana/d/dqWkb7Q7k/ndt-elasticsearch-dashboard?orgId=1&var-interval=\\$__auto_interval_interval&theme=light&kiosk=embeb");
            grafanaLabelMap.put(GraganaKeys.VAR_NAMESPACE, app.getNamespace());
            grafanaLabelMap.put(GraganaKeys.VAR_CLUSTER, app.getCrName());
            grafanaLabelMap.put(GraganaKeys.VAR_NAME, app.getCrName());
            return JsonUtil.toJson(grafanaLabelMap);
        }
    },
    Kibana("Elasticsearch","Kibana","Cluster", "kibana",5601, cn.newdt.cloud.domain.cr.Kibana.class,"elastic-operator") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{new Label(LABEL_KIBANA_NAME, crName)};
        }

        @Override
        public String instClass() {
            return DmpInstClass.MW;
        }

        @Override
        public String instType() {
            return DBTypeEnum.ELASTICSEARCH.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, CloudApp.IpNode[] ipNodes) {
            return Collections.singletonList(app.getName());
        }

        @Override
        public String getAlertStatus(CustomResource cr) {
            return cr.getStatus() != null ? ((cn.newdt.cloud.domain.cr.Kibana) cr).getStatus().getHealth() : null;
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            return null;
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return name + "-kb-svc";
        }

        // TODO  未定
        @Override
        public String getReadServiceName(String name) {
            return null;
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return 1;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return Collections.singletonList(  app.getCrName() + "-kb-");
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage);
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put(".+-kb-.+", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            return null;
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return null;
        }
    },
    /**
     * MYSQL_MGR
     */
    MYSQL_MGR("MySQL", "MySQL", "MGR", "mysql", 3306, InnoDBCluster.class, "mysql-operator") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{
                    // mgr operator 固定使用了 mysql-innodbcluster-mysql-server，故不能用 APP_NAME 判断 cr
                    new Label(CloudAppConstant.CustomLabels.APP_NAME, "mysql-innodbcluster-mysql-server"),
                    new Label("mysql.oracle.com/cluster", crName)
            };
        }

        @Override
        public String instClass() {
            return DmpInstClass.DB;
        }

        @Override
        public String instType() {
            return DBTypeEnum.MYSQL.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, CloudApp.IpNode[] ipNodes) {
            return MGRUtil.getPodNames(app.getCrName(), app.getMembers());
        }

        @Override
        public String getAlertStatus(CustomResource cr) {
            InnoDBCluster mgr = ((InnoDBCluster) cr);
            if (mgr.getStatus() != null){
                if (mgr.getStatus().getCluster() != null) {
                    InnoDBCluster.InnoDBClusterStatusEnum status = mgr.getStatus().getCluster().getStatus();
                    if (InnoDBCluster.InnoDBClusterStatusEnum.ONLINE == status) {
                        return null;
                    } else
                        return status.toString();
                }
            }
            return null;
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            //[root@localhost chenyj-mgr]# pwd
            ///mnt/cloud-nfs/mysql/mgr/foryongj/chenyj-mgr
            //[root@localhost chenyj-mgr]# ls
            //20250429.135247.full.xbstream                 20250429.135247.inc.20250430.010002.xbstream
            return String.join(File.separator,
                    getKind().toLowerCase(), "mgr", namespace, crName, backupFileName);
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return name + "-service";
        }

        @Override
        public String getReadServiceName(String name) {
            return name + "-read-service";
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return 2;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return Collections.singletonList( app.getCrName() + "-");
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage, ImageKindEnum.Sidecar, ImageKindEnum.Filebeat, ImageKindEnum.Percona_XtraBackup, ImageKindEnum.Exporter);
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put("-\\d$", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            HashMap<String, String> grafanaLabelMap = new HashMap<>();
            grafanaLabelMap.put(GraganaKeys.GRAFANA_URL, grafanaServer + "/grafana/d/ZVzcvcKIk/ndt-mysql-dashboard?orgId=1&var-interval=\\$__auto_interval_interval&theme=light&kiosk=embeb");
            grafanaLabelMap.put(GraganaKeys.VAR_NAMESPACE, app.getNamespace());
            grafanaLabelMap.put(GraganaKeys.VAR_JOB, "innodbcluster-" + app.getCrName());
            return JsonUtil.toJson(grafanaLabelMap);
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("data", Pattern.compile("datadir-" + app.getCrName() + "-\\d+"),
                    "backup", Pattern.compile("backupdir-" + app.getCrName()));
        }
    },
    PostgreSQL("PostgreSQL", "PostgreSQL", "HA", "postgre", 5432, PostgreSql.class, "postgresql-operator-controller-manager") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{new Label(CustomLabels.APP, "postgresql"), new Label(CustomLabels.APP_COMPONENT, "postgresql"), new Label(CustomLabels.APP_NAME, crName)};
        }

        @Override
        public String instClass() {
            return DmpInstClass.DB;
        }

        @Override
        public String instType() {
            return DBTypeEnum.POSTGRESQL.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, CloudApp.IpNode[] ipNodes) {
            // "pg-" + app.getCrName() + "-" + [ip]
//            return Arrays.stream(ipNodes).map(in -> "pg-" + app.getCrName() + "-" + (in.getIp().replaceAll("\\.", "-")))
//                    .collect(Collectors.toList());
            return ImmutableList.of("pg-" + app.getCrName() + "(-[0-9]+){4}");
        }

        @Override
        public String getAlertStatus(CustomResource cr) {
            return AppUtil.getAlertStatus(cr);
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            //[root@localhost bakpg]# pwd
            ///mnt/cloud-nfs/postgresql/backup/lianzb-m/bakpg
            //[root@localhost bakpg]# ls
            //20250605-134813F  20250605-134813F_20250605-135405I
            // 20250605-134813F_20250605-135405I.tgz  20250605-134813F.tgz
            // backup.history  backup.info  backup.info.copy  latest
            return String.join(File.separator, getKind().toLowerCase(), "backup",
                    namespace, crName, backupFileName + ".tgz");
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "pg-".concat(name).concat("-svc");
        }

        @Override
        public String getReadServiceName(String name) {
            return "pg-".concat(name).concat("-svc-read");
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return 2;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return Collections.singletonList("pg-" + app.getCrName() + "-");
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage, ImageKindEnum.Filebeat, ImageKindEnum.FTP, ImageKindEnum.Exporter);
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("backup", Pattern.compile("pg-" + app.getCrName() +"(-[0-9]{1,3}){4}-backup-pvc"),
                    "data", Pattern.compile("pg-" + app.getCrName() +"(-[0-9]{1,3}){4}-data-pvc"));
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put("^pg-.*-(\\d+(-\\d+)*)$", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            HashMap<String, String> grafanaLabelMap = new HashMap<>();
            grafanaLabelMap.put(GraganaKeys.GRAFANA_URL, grafanaServer + "/grafana/d/5oPQ5cFIz/ndt-postgresql-dashboard?orgId=1&var-interval=\\$__auto_interval_interval&theme=light&kiosk=embeb");
            grafanaLabelMap.put(GraganaKeys.VAR_NAMESPACE, app.getNamespace());
            grafanaLabelMap.put(GraganaKeys.VAR_JOB, "pg-" + app.getCrName());
            return JsonUtil.toJson(grafanaLabelMap);
        }
    },
    Flink("Flink", "Flink", "Cluster", "flink-main-container", 8081, FlinkDeployment.class, "flink-kubernetes-operator") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            Label[] podLabels = labelOfPod(app);
            // 创建新的标签列表，并添加原有标签和新标签
            Label[] resultLabels = Arrays.copyOf(podLabels, podLabels.length + 1);
            resultLabels[podLabels.length] = new Label("component", "jobmanager");

            // 将列表转换为数组返回
            return resultLabels;
        }

        @Override
        public Label[] labels(String crName) {
//            return new Label[]{new Label("type", "flink-standalone-kubernetes"), new Label("app", crName)};
            return new Label[]{new Label(CustomLabels.APP, "flink"),
                    new Label(CustomLabels.APP_NAME, crName)};

        }

        @Override
        public String instClass() {
            return DmpInstClass.MW;
        }

        @Override
        public String instType() {
            return DBTypeEnum.FLINK.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, CloudApp.IpNode[] ipNodes) {
            return ImmutableList.of(app.getCrName() + "-.{10}-.{5}", app.getCrName() + "-taskmanager-.{10}-.{5}");
        }

        @Override
        public String getAlertStatus(@Nonnull CustomResource cr) {
            if (cr == null || cr.getStatus() == null)
                return null;
            FlinkDeploymentStatus.LifecycleState reconciliationStatus = ((FlinkDeployment) cr).getStatus().getLifecycleState();
            String value = Optional.ofNullable(reconciliationStatus)
                    .map(state -> state.getValue())
                    .orElse("empty state");
            if ("STABLE".equals(value)) {
                return null;
            } else
                return value;
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            return null;
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "flink-" + name + "-svc";
        }

        @Override
        public String getReadServiceName(String name) {
            return null;
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return 1;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return Collections.singletonList(app.getCrName() + "-");
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage, ImageKindEnum.Filebeat);
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Stream.
                    of(ComponentKindEnum.JOB_MANAGER, ComponentKindEnum.TASK_MANAGER).
                    collect(Collectors.toList());
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put("-\\d$", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Stream.
                    of(ComponentKindEnum.JOB_MANAGER, ComponentKindEnum.TASK_MANAGER).
                    collect(Collectors.toList());
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            HashMap<String, String> grafanaLabelMap = new HashMap<>();
            grafanaLabelMap.put(GraganaKeys.GRAFANA_URL, grafanaServer + "/grafana/d/pWpBdowIz/ndt-flink-dashboard?orgId=1&var-interval=\\$__auto_interval_interval&theme=light&kiosk=embeb");
            grafanaLabelMap.put(GraganaKeys.VAR_NAMESPACE, app.getNamespace());
            grafanaLabelMap.put(GraganaKeys.VAR_JOB, "flink-" + app.getCrName());
            return JsonUtil.toJson(grafanaLabelMap);
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return null;
        }
    },
    /**
     * Zookeeper
     */
    ClickHouse_Zookeeper("ClickHouse", "ClickHouse-Zookeeper", "Cluster", "zookeeper", 2181, cn.newdt.cloud.domain.cr.Zookeeper.class, "zookeeper-operator-controller-manager") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{new Label(LABEL_ZOOKEEPER_NAME, crName), new Label(CustomLabels.APP, "zookeeper")};
        }

        @Override
        public String instClass() {
            return DmpInstClass.MW;
        }

        @Override
        public String instType() {
            return DBTypeEnum.CLICKHOUSE.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, @Nonnull CloudApp.IpNode[] ipNodes) {
            return AppUtil.generateZookeeperPodName(app.getCrName(), Arrays.asList(ipNodes))
                    .stream().map(d -> d.getPodName()).collect(Collectors.toList());
        }

        @Override
        public String getAlertStatus(CustomResource cr) {
            return AppUtil.getAlertStatus(cr);
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            return null;
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "zk-" + name;
        }

        @Override
        public String getReadServiceName(String name) {
            return null;
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return 0;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return Collections.singletonList("zk-" + app.getCrName() + "-");
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage, ImageKindEnum.Exporter, ImageKindEnum.Filebeat);
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put("^zk-.*-\\d+$", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            return null;
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("data", Pattern.compile("zk-" + app.getCrName() + "-pvc(-[0-9]{1,3}){4}"));
        }
    },
    Clickhouse("ClickHouse", "ClickHouse", "Cluster", "clickhouse", 8123, ClickHouseInstallation.class, "clickhouse-operator") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{
                    new Label(CustomLabels.APP, getKind().toLowerCase()),
                    new Label(CustomLabels.APP_NAME, crName),
                    new Label(CustomLabels.APP_COMPONENT, getKind().toLowerCase()),
                    new Label(CLICKHOUSE_APP, "chop"),
                    new Label(CLICKHOUSE_APP_NAME, crName)};
        }

        @Override
        public String instClass() {
            return DmpInstClass.DB;
        }

        @Override
        public String instType() {
            return DBTypeEnum.CLICKHOUSE.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, CloudApp.IpNode[] ipNodes) {
            String yaml = StringUtils.isNotEmpty(app.getCr()) ? app.getCr() : app.getCrRun();
            ClickHouseInstallation clickHouse = YamlEngine.unmarshal(yaml, ClickHouseInstallation.class);
            return AppUtil.generateClickHousePodName(app.getCrName(), clickHouse.getSpec().getConfiguration().getClusters().get(0).getLayout());
        }

        @Override
        public String getAlertStatus(@Nonnull CustomResource cr) {
            return null;
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            //[root@localhost back]# pwd
            ///mnt/cloud-nfs/ClickHouse/Cluster/lianzb-m/back
            //[root@localhost back]# ls
            //back-shard-0-1747792832232.tar.gz
            return String.join(File.separator, getKind(), getArch(), namespace, crName, backupFileName);
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "clickhouse-service-" + name;
        }

        @Override
        public String getReadServiceName(String name) {
            return "clickhouse-service-" + name;
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return 1;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return Collections.singletonList("chi" + "-" + app.getCrName() + "-");
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage, ImageKindEnum.Filebeat, ImageKindEnum.Clickhouse_Backup);
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(ComponentKindEnum.DB);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put("^chi-.*-(\\d+(-\\d+)*)$", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("data", Pattern.compile("clickhouse-datavolume-storage-chi-" + app.getCrName() + "-" + app.getCrName() + "(-[0-9]{1,3}){3}"));
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            HashMap<String, String> grafanaLabelMap = new HashMap<>();
            grafanaLabelMap.put(GraganaKeys.GRAFANA_URL, grafanaServer + "/grafana/d/mPwRrWlIz/ndt-clickhouse-dashboard?orgId=1&var-interval=\\$__auto_interval_interval&theme=light&kiosk=embeb");
            grafanaLabelMap.put(GraganaKeys.VAR_NAMESPACE, app.getNamespace());
            grafanaLabelMap.put(GraganaKeys.VAR_JOB, app.getCrName());
            return JsonUtil.toJson(grafanaLabelMap);
        }
    },
    Vastbase("Vastbase", "Vastbase", "Cluster", "vastbase", 5432, Vastbase.class, "vastbase-operator") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            return labelOfPod(app);
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{new Label(CustomLabels.APP, getKind().toLowerCase()), new Label(CustomLabels.APP_NAME, crName)};
        }

        @Override
        public String instClass() {
            return DmpInstClass.DB;
        }

        @Override
        public String instType() {
            return DBTypeEnum.VASTBASE.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, @Nonnull CloudApp.IpNode[] ipNodes) {
            // 格式: vastbase-sample2-dcs-jghg2g-0  vastbase-sample2-vastbase-0ggv38-0
            return ImmutableList.of("vastbase-" + app.getCrName() + "-(dcs|vastbase)-[a-z0-9]{6}-0");
        }

        @Override
        public String getAlertStatus(CustomResource cr) {
            if (null != ((Vastbase) cr).getStatus().getHealth()) {
                return cr.getStatus() == null ? "unknown" : ((Vastbase) cr).getStatus().getHealth().getState();
            } else {
                return "unknown";
            }
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            //[root@localhost hdvb-1111]# pwd
            ///mnt/cloud-nfs/vastbase/backups/hdbumen/hdvb-1111
            //[root@localhost hdvb-1111]# ls
            //pg_probackup.conf  SPPM51 SPPM51.tgz
            return String.join(File.separator, getKind().toLowerCase(), "backups",
                    namespace, crName, backupFileName + ".tgz");
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "vastbase-" + name + "-vastbase-readwrite-svc";
        }

        @Override
        public String getReadServiceName(String name) {
            return "vastbase-" + name + "-vastbase-readonly-svc";
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return 2;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return ImmutableList.of("vastbase-" + app.getCrName());
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(ImageKindEnum.MainImage, ImageKindEnum.Dcs);
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("vastbase", Pattern.compile("data-vastbase-" + app.getCrName() + "-(dcs|vastbase)-[a-z0-9]{6}-0"));
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            result.put("-" + ComponentKindEnum.DCS.name().toLowerCase() + "-[a-z0-9]{6}-0", "dcs");
            result.put("-" + ComponentKindEnum.VASTBASE.name().toLowerCase() + "-[a-z0-9]{6}-0", "vastbase");
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.DB);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            HashMap<String, String> grafanaLabelMap = new HashMap<>();
            grafanaLabelMap.put(GraganaKeys.GRAFANA_URL, grafanaServer + "/grafana/d/TuXA5rSNz/ndt-vastbase-dashboard?orgId=1&var-interval=\\$__auto_interval_interval&theme=light&kiosk=embeb");
            grafanaLabelMap.put(GraganaKeys.VAR_NAMESPACE, app.getNamespace());
            grafanaLabelMap.put(GraganaKeys.VAR_JOB, "vastbase-" + app.getCrName() + "-vastbase");
            return JsonUtil.toJson(grafanaLabelMap);
        }
    },
    Dameng("Dameng", "Dameng", "DataWatch", "dm", 5236, com.shindata.cloud.dameng.v1.Dameng.class, "damengoperator-controller-manager") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            Label[] podLabels = labelOfPod(app);
            // 创建新的标签列表，并添加原有标签和新标签
            Label[] resultLabels = Arrays.copyOf(podLabels, podLabels.length + 1);
            resultLabels[podLabels.length] = new Label(CustomLabels.APP_COMPONENT, "database");

            // 将列表转换为数组返回
            return resultLabels;
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{new Label(CustomLabels.APP, "dameng"), new Label(CustomLabels.APP_NAME, crName)};
        }

        @Override
        public String instClass() {
            return DmpInstClass.DB;
        }

        @Override
        public String instType() {
            return DBTypeEnum.DAMENG.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, CloudApp.IpNode[] ipNodes) {
            return ImmutableList.of("dm-" + app.getCrName() + "-.{8}-0", "dm-" + app.getCrName() + "-monitor-\\\\d+");
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            // [root@localhost 20250407064028_FULL]# pwd
            ///mnt/cloud-nfs/DAMENG/yan-dev/eeqr/20250407064028_FULL
            //[root@localhost 20250407064028_FULL]# ls
            //bakFiles.tar  extra  info.txt  list.txt
            return String.join(File.separator, instType(), namespace, crName, backupFileName, "bakFiles.tar");
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "dm-" + name + "-write";
        }

        @Override
        public String getReadServiceName(String name) {
            return "dm-" + name + "-read";
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return 2;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            return ImmutableList.of("dm-" + app.getCrName());
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return ImmutableList.of(ImageKindEnum.MainImage, ImageKindEnum.Filebeat, ImageKindEnum.Exporter, ImageKindEnum.Mount);
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return ImmutableList.of(ComponentKindEnum.DB, ComponentKindEnum.MONITOR);
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            // result.put("^dm-.*", this.getContainerName());
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return ImmutableList.of(ComponentKindEnum.DB, ComponentKindEnum.MONITOR);
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            HashMap<String, String> grafanaLabelMap = new HashMap<>();
            grafanaLabelMap.put(GraganaKeys.GRAFANA_URL, grafanaServer + "/grafana/d/zHoDTwnHk/ndt-dameng-dashboard?orgId=1&var-interval=\\$__auto_interval_interval&theme=light&kiosk=embeb");
            grafanaLabelMap.put(GraganaKeys.VAR_NAMESPACE, app.getNamespace());
            grafanaLabelMap.put(GraganaKeys.VAR_JOB, app.getCrName());
            return JsonUtil.toJson(grafanaLabelMap);
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("data", Pattern.compile("dm-data-dm-" + app.getCrName() + "-[a-f0-9]{8}-0"));
        }

        @Override
        public CloudInstDTO applyToDMPDTO(CloudAppVO app) {
            CloudInstDTO cloudInstDTO = super.applyToDMPDTO(app);
            cloudInstDTO.setArch("SINGLE"); // 区分架构和灾备架构, Datawatch 为灾备架构，单节点和主备架构都属于SINGLE，其他架构还有DSC,MPP
            cloudInstDTO.setDrMgr("DW");
            cloudInstDTO.setDrRole("P"); // 默认纳管主
            cloudInstDTO.setDrClusterName(app.getCrName());
//            cloudInstDTO.setUsername(app.getUsername());
//            cloudInstDTO.setPassword(app.getPassword());
//            cloudInstDTO.setInstName(); // 主实例名
            return cloudInstDTO;
        }
    },

    /**
     * tidb 类型
     */
    TIDB("TiDB", "TiDB", "Cluster", "tidb", 4000, TidbCluster.class, "tidb-controller-manager") {
        @Override
        public Label[] labelOfPod(CloudApp app) {
            return labels(app.getCrName());
        }

        @Override
        public Label[] labelOfService(CloudApp app) {
            Label[] podLabels = labelOfPod(app);
            // 创建新的标签列表，并添加原有标签和新标签
            Label[] resultLabels = Arrays.copyOf(podLabels, podLabels.length + 1);
            resultLabels[podLabels.length] = new Label(CustomLabels.APP_COMPONENT, "tidb");

            // 将列表转换为数组返回
            return resultLabels;
        }

        @Override
        public Label[] labels(String crName) {
            return new Label[]{
                    new Label(CustomLabels.INSTANCE, crName),
                    // tidb operator 固定使用了 tidb-cluster，故不能用 APP_NAME 判断 cr
                    new Label(CustomLabels.APP_NAME, "tidb-cluster"),
                    new Label(TIDB_REF, "tidb-operator")};
        }

        @Override
        public String instClass() {
            return null;
        }

        @Override
        public String instType() {
            return DBTypeEnum.TIDB.getValue();
        }

        @Override
        public List<String> genPodNames(AppMetadata app, CloudApp.IpNode[] ipNodes) {
            return AppUtil.generateTidbPodName(app);
        }

        @Override
        public String getAlertStatus(@Nonnull CustomResource cr) {
            return null;
        }

        @Override
        public String getBackupFilePath(String namespace, String crName, String backupFileName) {
            //[root@localhost tidb-backup-file-2025-06-06-1749195577850]# pwd
            ///mnt/cloud-nfs/TiDB/Cluster/lianzb-m/newbakti/tidb-backup-file-2025-06-06-1749195577850
            //[root@localhost tidb-backup-file-2025-06-06-1749195577850]# ls
            //1  12  13  backup.lock  backupmeta  backupmeta.datafile.000000001  backupmeta.schema.000000002
            // checkpoints  tidb-backup-file-2025-06-06-1749195577850.tar.gz
            return String.join(File.separator, getKind(), getArch(), namespace, crName, backupFileName, backupFileName + ".tar.gz");
        }

        @Override
        public String getWriteServiceName(String name, String ip) {
            return "tidb-service-" + name;
        }

        @Override
        public String getReadServiceName(String name) {
            return "tidb-service-" + name;
        }

        @Override
        public int getServiceManagerNum(String serviceType, int members) {
            return 1;
        }

        @Override
        public List<String> getPodPattern(CloudApp app) {
            String crName = app.getCrName();
            String[] podPatternArr = new String[]{crName + "-" + ComponentKindEnum.PD.name().toLowerCase() + "-",
                    crName + "-" + ComponentKindEnum.TIDB.name().toLowerCase() + "-",
                    crName + "-" + ComponentKindEnum.TIKV.name().toLowerCase() + "-"};
            return Arrays.asList(podPatternArr);
        }

        @Override
        public List<ImageKindEnum> getContainerImages() {
            return Lists.newArrayList(
                    ImageKindEnum.MainImage,
                    ImageKindEnum.Tidb_PD,
                    ImageKindEnum.Tidb_Tikv,
                    ImageKindEnum.Filebeat
            );
        }

        @Override
        public List<ComponentKindEnum> getComponentKinds() {
            return null;
        }

        @Override
        public Map<String, String> getContainersByPodName() {
            Map<String, String> result = new HashMap<>();
            result.put("-" + ComponentKindEnum.PD.name().toLowerCase() + "-\\d+$", "pd");
            result.put("-" + ComponentKindEnum.TIDB.name().toLowerCase() + "-\\d+$", "tidb");
            result.put("-" + ComponentKindEnum.TIKV.name().toLowerCase() + "-\\d+$", "tikv");
            result.put("-" + ComponentKindEnum.DISCOVERY.name().toLowerCase() + "-.+$", "discovery");
            return result;
        }

        @Override
        public List<ComponentKindEnum> getDbParamTemplateComponentKinds() {
            return Lists.newArrayList(
                    ComponentKindEnum.PD,
                    ComponentKindEnum.TIDB,
                    ComponentKindEnum.TIKV
            );
        }

        @Override
        public String getGrafanaUrl(String grafanaServer, CloudAppVO app) {
            HashMap<String, String> grafanaLabelMap = new HashMap<>();
            grafanaLabelMap.put(GraganaKeys.GRAFANA_URL, grafanaServer + "/grafana/d/W-UgvC4Hz/ndt-tidb-dashboard?orgId=1&var-interval=\\$__auto_interval_interval&theme=light&kiosk=embeb");
            grafanaLabelMap.put(GraganaKeys.VAR_NAMESPACE, app.getNamespace());
            grafanaLabelMap.put(GraganaKeys.VAR_TIDB_CLUSTER, app.getCrName());
            return JsonUtil.toJson(grafanaLabelMap);
        }

        @Override
        public Map<String, Pattern> getPvcPatterns(CloudApp app) {
            return ImmutableMap.of("data", Pattern.compile("(pd|tikv)-" + app.getCrName() + "-(pd|tikv)-\\d+"));
        }
    },;

    AppKind(String product, String kind, String arch, String containerName, int dbPort, Class<? extends CustomResource> crClass, String operatorName) {
        this.product = product;
        this.kind = kind;
        this.arch = arch;
        this.containerName = containerName;
        this.dbPort = dbPort;
        this.crClass = crClass;
        this.operatorName = operatorName;
    }

    private final String product;
    private final String kind;
    private final String arch;
    private final String containerName;
    private final int dbPort;
    private final Class<? extends CustomResource> crClass;
    private final String operatorName;
    private static Map<String, AppKind> map = new HashMap<>();

    /**
     * sideApp 或关联应用类型集合，如zk对于kafka
     */
    public static EnumSet<AppKind> sideAppKindSet() {
        return EnumSet.of(Zookeeper, NameServer, Kibana, Sentinel);
    }

    public String getProduct() {
        return product;
    }

    public String getKind() {
        return kind;
    }

    public String getArch() {
        return arch;
    }

    public int getDbPort() {
        return dbPort;
    }

    public Class<? extends CustomResource> getCrClass() {
        return crClass;
    }

    public String getContainerName() {
        return containerName;
    }

    /**
     * 根据类型和架构返回AppKind实例
     *
     * @param kind db类型
     * @param arch db架构
     */
    public static AppKind valueOf(String kind, String arch) {
        AppKind appKind = map.get(kind.toLowerCase() + "_" + (arch == null ? "" : arch.toLowerCase()));
        if (appKind == null) {
            String collect = Arrays.stream(AppKind.values()).map(AppKind::getKind).collect(Collectors.joining(","));
            throw new RuntimeException(kind + "," + arch + " not implemented, expected " + collect);
        }
        return appKind;
    }

    /**
     * 根据类型回AppKind实例
     *
     * @param kind db类型
     */
    public static AppKind valueOfDMPKind(String kind) {
        return Arrays.stream(values()).filter(appKind -> kind.equalsIgnoreCase(appKind.getKind())).findAny().orElse(null);
    }


    /**
     * 根据类型和架构返回AppKind实例
     *
     * @param kind db类型
     */
    public static List<AppKind> valueOfKind(String kind) {
        List<AppKind> appKinds=new ArrayList<>();
        map.forEach((key, value) -> {
            if (key.toUpperCase().contains(kind.toUpperCase())){
                appKinds.add(value);
            }
        });

        if (appKinds == null) {
            String collect = Arrays.stream(AppKind.values()).map(AppKind::getKind).collect(Collectors.joining(","));
            throw new RuntimeException(kind + " not implemented, expected " + collect);
        }
        return appKinds;
    }

    public static List<AppKind> valueOfProduct(String product) {
        List<AppKind> appKinds=new ArrayList<>();
        for (AppKind appKind : values()) {
            if (product.equalsIgnoreCase(appKind.getProduct()))
                appKinds.add(appKind);
        }

        return appKinds;
    }


    static {
        for (AppKind value : AppKind.values()) {
            map.put(value.kind.toLowerCase()+"_"+value.arch.toLowerCase(), value);
        }
    }

    /**
     * return product of given appkind
     */
    public static String productOf(final String product) {
        for (AppKind each : AppKind.values()) {
            if ((each.product).equals(product)) {
                return each.product;
            }
        }
        throw new CustomException(SQL_EXECUTE_EXCEPTIONS, String.format("不支持的应用类型: %s", product));
    }

    public abstract Label[] labelOfPod(CloudApp app);

    public abstract Label[] labelOfService(CloudApp app);

    public abstract Label[] labels(String crName);

    public abstract String instClass();

    public abstract String instType();

    public abstract List<String> genPodNames(AppMetadata app, CloudApp.IpNode[] ipNodes);

    public String getAlertStatus(@Nonnull CustomResource cr) {
        return AppUtil.getAlertStatus(cr);
    };

    /**
     * 备份存储中的备份路径
     */
    public abstract String getBackupFilePath(String namespace, String crName, String backupFileName);


    /**
     * 写|服务 类型的Service
     * @param name crname
     * @param ip 固定ip且需要暴露每个节点的实例类型需要传递ip，否则传递 null
     * @return service的 name
     */
    public abstract String getWriteServiceName(String name, String ip);

    /**
     * 读 Service
     * @param name crname
     * @return service的 name
     */
    public abstract String getReadServiceName(String name);


    /**
     * 根据节点数和 ServiceType 获取所需的ServiceManager数量，如果不提供对外访问的实例类型则返回 0
     * @param serviceType service 类型
     * @param members 节点数
     * @return
     */
    public abstract int getServiceManagerNum(String serviceType, int members);

    /**
     * redis: redis-[crname]-ip
     * mysql: crname-ip
     * og: og-[cr-name]-pod-ip
     * mongo: [crname]-序号
     * sentinel: sentinel-[cr-name]-ip
     * kibana: [crname]-kb
     * 目前用作集群视图资源使用折线图，对于ip结尾的pod名称，可以反应出pod删减后应用整体占用资源的变化
     * @param app @return 给定crName的pod名称前缀
     * @return
     * @see cn.newdt.cloud.service.alert.AlertConfigSyncService#getPodNamePrefix(String, AppKind)
     */
    public abstract List<String> getPodPattern(CloudApp app);

    public String getOperatorName() {
        return operatorName;
    }

    public List<String> getPodPatternWithNamespace(CloudApp app) {
        return getPodPattern(app).stream().map(n -> app.getNamespace() + "/" + n).collect(Collectors.toList());
    }

    public abstract List<ImageKindEnum> getContainerImages();

    public abstract List<ComponentKindEnum> getComponentKinds();

    /**
     * 主要是为了解决多组件不同容器问题，
     * 用来根据pod名称 去匹配 相关的 容器名称
     * 若是只有一个container，则返回null即可
     * @return
     */
    public abstract Map<String, String> getContainersByPodName();

    public abstract List<ComponentKindEnum> getDbParamTemplateComponentKinds();

    public abstract String getGrafanaUrl(String grafanaServer, CloudAppVO app);

    // todo 将应用相关的所有pattern汇总到一个集合里，方便检索、修改
    public abstract Map<String, Pattern> getPvcPatterns(CloudApp app);

    public CloudInstDTO applyToDMPDTO(CloudAppVO app) {
        CloudInstDTO cloudInstDTO = new CloudInstDTO();
        cloudInstDTO.setArch(app.getArch());
        return cloudInstDTO;
    }
    /**
     *
     * @param podLabelMap
     * @param crName
     * @return
     */
    public static AppKind getAppKindByLabels(Map<String, String> podLabelMap, String crName){
        // 1.获取所有的appkind，并获取所有的labels
        AppKind[] appKinds = values();

        // 2.将pod的labels转换为list
        List<Label> podLabelList = new ArrayList<>();
        for(String key : podLabelMap.keySet()){
            Label label = new Label(key, podLabelMap.get(key));
            podLabelList.add(label);
        }

        //2. 遍历匹配
        for (AppKind appKind : appKinds) {
            List<Label> appKindLabel = Arrays.asList(appKind.labels(crName));
            if(podLabelList.containsAll(appKindLabel)){
                //匹配成功返回对应类型
                return appKind;
            }
        }
        return null;
    }
}