package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.domain.CloudSysConfig;
import cn.newdt.cloud.domain.KubeConfig;
import cn.newdt.cloud.filter.ResourceChangeLog;
import cn.newdt.cloud.mapper.CloudSysConfigMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.alert.RetentionConfigureService;
import cn.newdt.cloud.service.csi.CSILoader;
import cn.newdt.cloud.utils.ESUtil;
import cn.newdt.cloud.utils.JsonUtil;
import cn.newdt.cloud.utils.PropertyUtil;
import cn.newdt.cloud.vo.CloudTenantVO;
import cn.newdt.commons.exception.ArgumentException;
import cn.newdt.commons.exception.CustomException;
import com.alibaba.fastjson.JSONObject;
import io.fabric8.kubernetes.api.model.ConfigMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.DependsOn;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.ActionEnum.MODIFY_SYSCONFIG;
import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgCategory.*;
import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgName.*;
import static cn.newdt.cloud.constant.DatasourceConstant.CLOUD_SYS_CONFIG;
import static cn.newdt.cloud.constant.DatasourceConstant.SCHEMA;
@Service
@Slf4j
@DependsOn({"CSILoader", "kubeConfigServiceImpl"})
public class SysConfigService implements InitializingBean {

    @Autowired
    CloudSysConfigMapper mapper;

    @Autowired
    ESUtil esUtil;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private KubeConfigService kubeConfigService;

    @Autowired
    private RetentionConfigureService retentionConfigureService;

    @Autowired
    private TenantService tenantService;

    @Autowired
    private KubeClientService kubeClientService;

    // 每个请求3+n个并发, 最大支持并发 = (maxinumPoolSize + queueCapacity) / (3+n)
    private ThreadPoolExecutor executor = new ThreadPoolExecutor(5,
            5,
            60L, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(500));

    public List<CloudSysConfig> find(String category) {
        return mapper.findList(SCHEMA, CLOUD_SYS_CONFIG, Collections.singletonMap("category", category));
    }

    public Map<String, String> findMap(String category) {
        return find(category)
                .stream().collect(Collectors.toMap(CloudSysConfig::getName, CloudSysConfig::getData));
    }


    public List<CloudSysConfig> find(String... categories) {
        return Arrays.stream(categories).flatMap(category -> find(category).stream()).collect(Collectors.toList());
    }

    public List<CloudSysConfig> findList(Map<String, Object> param) {
        return mapper.findList(SCHEMA, CLOUD_SYS_CONFIG, param);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("Start loading sysconfig");
        String csiInstalled = findOne(CLUSTER_MANAGEMENT, "csiInstalled");
        if (StringUtils.isNotEmpty(csiInstalled)) {
            for (String csi : csiInstalled.split(",")) {
                if (CSILoader.appendDynamicType(csi)) {
                    log.info("found dynamic csi type [{}] registered", csi);
                }
            }
        }
    }

    public String findOne(String category, String name) {
        Optional<CloudSysConfig> cfg = find(category).stream().filter(e -> e.getName().equals(name)).findFirst();
        return cfg.map(CloudSysConfig::getData).orElse(null);
    }

    @ResourceChangeLog(action = MODIFY_SYSCONFIG)
    public int updateSysConfigByCategoryAndName(CloudSysConfig sysconfig) {
        String config = findOne(sysconfig.getCategory(), sysconfig.getName());
        if (StringUtils.isEmpty(config)) {
            throw new ArgumentException("config is illegal");
        }
        sysconfig.setUpdateTime(LocalDateTime.now());
        int count = mapper.updateSysConfigByCategoryAndName(
                SCHEMA, CLOUD_SYS_CONFIG, sysconfig);

        return count;
    }

    public static class ObserverRegistry {
        // key is [category], or [category].[name],
        Map<String, List<Consumer<List<CloudSysConfig>>>> updateObservers = new ConcurrentHashMap<>();

        private ObserverRegistry() {
            // 注册
            updateObservers.put(OVER_ALLOCATION_RATIO,
                    Collections.singletonList(ResourceHelper.getInstance()::updateRatioOnAll));
            updateObservers.put(K8S_SYS_CONFIG_CM_DATA,
                    Collections.singletonList(GlobalConfigMap.getInstance()::loadData));
        }

        public void handleUpdateByCategory(@NotEmpty List<CloudSysConfig> sysConfig) {
            Optional.ofNullable(updateObservers.get(sysConfig.get(0).getCategory()))
                    .ifPresent(handlers -> handlers.forEach(observer -> observer.accept(sysConfig)));
        }

        public void handleUpdate(CloudSysConfig sysConfig) {
            handleUpdateByCategory(Collections.singletonList(sysConfig));
        }
    }

    @ResourceChangeLog(action = MODIFY_SYSCONFIG)
    public void updateSysConfigByCategoryAndNames(List<CloudSysConfig> sysConfigList) {
        //获取现有配置
        HashMap<String, Object> condition = new HashMap<>();
        condition.put("isUpdate", 1);
        List<CloudSysConfig> oldSysConfigList = findList(condition);
        //创建承载prometheus相关配置的list用于修改
        List<RetentionConfigureService.Configuration> configurations = new ArrayList<>();
        //对比新旧配置，获取到哪些配置未被修改，获取的结果为：category|name 的list
        CompletableFuture<Void> alertRetentionCfgFuture = CompletableFuture.runAsync(() -> {
            List<RetentionConfigureService.Configuration> alertCfgs = sysConfigList.stream()
                    .filter(sysConfig -> ALERT_CFG.equalsIgnoreCase(sysConfig.getCategory()))
                    .map(sysConfig -> {
                        RetentionConfigureService.Configuration configuration = new RetentionConfigureService.Configuration();
                        configuration.setName(sysConfig.getName());
                        configuration.setSummary(sysConfig.getDescription());
                        configuration.setValue(sysConfig.getData());
                        return configuration;
                    })
                    .collect(Collectors.toList());
            if (!alertCfgs.isEmpty()) {
                retentionConfigureService.modifyRetentionConfiguration(alertCfgs);
            }
        });
        sysConfigList.stream()
                .filter(sysConfig -> !oldSysConfigList.contains(sysConfig))
                .map(sysConfig -> {
                    if (LOG_CONFIG.equalsIgnoreCase(sysConfig.getCategory()) && LOG_DELETE_DAYS.equalsIgnoreCase(sysConfig.getName())) {
                        //修改底层es日志保留日志时间
                        RestTemplateBuilder restTemplateBuilder = new RestTemplateBuilder();
                        restTemplate = restTemplateBuilder
                                .basicAuthentication(esUtil.getEsUsername(), esUtil.getEsPassword())
                                .build();
                        //发送请求获取当前policy
                        ResponseEntity<String> exchange = restTemplate.getForEntity("http://" + esUtil.getEsIp() + ":" + esUtil.getEsPort() + "/_ilm/policy/ndt_shindb_log_policy", String.class, (Object) null);
                        if (exchange.getStatusCodeValue() == 200) {
                            String policyJsonStr = exchange.getBody();
                            JSONObject policyJsonObj = JsonUtil.toObject(JSONObject.class, policyJsonStr);
                            //修改min_age数据
                            JSONObject policy = policyJsonObj.getJSONObject("ndt_shindb_log_policy");
                            policy
                                    .getJSONObject("policy")
                                    .getJSONObject("phases")
                                    .getJSONObject("delete")
                                    .put("min_age", sysConfig.getData() + "d");
                            policy.remove("version");
                            policy.remove("modified_date");
                            policyJsonStr = JsonUtil.toJson(policy);
                            //发送请求修改policy
                            String url = String.format("http://%s:%s@%s:%d/_ilm/policy/ndt_shindb_log_policy",
                                    esUtil.getEsUsername(), esUtil.getEsPassword(), esUtil.getEsIp(), esUtil.getEsPort());
                            HttpHeaders headers = new HttpHeaders();
                            headers.setContentType(MediaType.APPLICATION_JSON);
                            HttpEntity<String> requestEntity = new HttpEntity<>(policyJsonStr, headers);
                            ResponseEntity<String> updatePolicyRes = restTemplate.exchange(url, HttpMethod.PUT, requestEntity, String.class);
                            if (updatePolicyRes.getStatusCodeValue() != 200) {
                                log.error("修改policy失败, {}", updatePolicyRes);
                                throw new CustomException(600, "修改policy失败，状态码 " + exchange.getStatusCodeValue());
                            }

                            //遍历每个集群的每个namespace，修改filebeatCM中的cleanup.properties
                            List<KubeConfig> kubes = kubeConfigService.findEnabled(null);
                            if (!CollectionUtils.isEmpty(kubes)) {
                                List<Integer> kubeIds = kubes.stream().map(kube -> kube.getId()).collect(Collectors.toList());
                                kubeIds.stream().map(kubeId -> {
                                    KubeClient kubeClient = kubeClientService.get(kubeId);
                                    //获取namespace
                                    List<CloudTenantVO> cloudTenantVOS = tenantService.listTenantByKubeId(kubeId);
                                    if (!CollectionUtils.isEmpty(cloudTenantVOS)) {
                                        List<String> namespaces = cloudTenantVOS.stream()
                                                .map(CloudTenantVO::getNamespace)
                                                .collect(Collectors.toList());

                                        // 并行处理当前集群下的所有namespace
                                        List<CompletableFuture<Void>> futures = namespaces.stream()
                                                .map(namespace -> CompletableFuture.runAsync(() -> {
                                                    ConfigMap filebeatCM = kubeClient.getConfigMap("operator-filebeat-configmap", namespace);
                                                    if (!ObjectUtils.isEmpty(filebeatCM)) {
                                                        String cleanupPropertiesValue = "(30 0 * * *) /scripts/application-log-management.sh " + sysConfig.getData();
                                                        filebeatCM.getData().put("scriptrunner-config.ini", cleanupPropertiesValue);
                                                        kubeClient.applyConfigMap(filebeatCM, namespace);
                                                    }
                                                }, executor))
                                                .collect(Collectors.toList());

                                        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                                        return null;
                                    } else {
                                        return null;
                                    }
                                }).collect(Collectors.toList());
                            }
                        } else {
                            log.error("查询policy失败, {}", exchange);
                            throw new CustomException(600, "查询policy失败，状态码 " + exchange.getStatusCodeValue());
                        }
                    }
                    return null;
                }).collect(Collectors.toList());

        try {
            alertRetentionCfgFuture.join();
        } catch (CompletionException e) {
            throw new CustomException(600, e.getCause().getMessage());
        }

        //修改系统配置表
        mapper.updateSysConfigByCategoryAndNames(SCHEMA, CLOUD_SYS_CONFIG, sysConfigList);
    }
}
