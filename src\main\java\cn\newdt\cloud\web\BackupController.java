package cn.newdt.cloud.web;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.domain.BackupHis;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.dmp.BinlogBackupHis;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.service.AppMultiAZService;
import cn.newdt.cloud.service.BackupService;
import cn.newdt.cloud.service.CloudAppLogicService;
import cn.newdt.cloud.service.CloudAppService;
import cn.newdt.cloud.utils.BackupStorageUtils;
import cn.newdt.cloud.utils.BackupUtil;
import cn.newdt.cloud.utils.CustPreconditions;
import cn.newdt.cloud.utils.FTPUtils;
import cn.newdt.cloud.vo.BackupHisVO;
import cn.newdt.cloud.vo.CloudAppVO;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.response.ResponseResult;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.InputStream;
import java.sql.Timestamp;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("cloudnormal/backup")
@Slf4j
public class BackupController {

    @Autowired
    private BackupService backupService;
    @Autowired
    private CloudAppService cloudAppService;
    @Autowired
    private CloudAppLogicService cloudAppLogicService;

    @Autowired
    private AppMultiAZService appMultiAZService;

    @Autowired
    private FTPUtils ftpUtils;

    @Autowired
    private BackupUtil backupUtil;


    @ApiOperation("手动备份")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "backupHis", value = "备份历史", paramType = "body", dataType = "BackupHisVO"),
            @ApiImplicitParam(name = "dbType", value = "应用类型", paramType = "query", dataType = "String")
    })
    @PostMapping("manual")
    @PreAuthorize("@dataAuthorizeService.hasPermission(#backupHis.appLogicId, #backupHis.appId)")
    public ResponseResult manualBackup(@RequestBody BackupHisVO backupHis, @RequestParam("dbType")String dbType) throws Exception {
        if (backupHis.getAppId() == null && backupHis.getAppLogicId() != null) {
            backupHis.setAppId(appMultiAZService.selectBackupSourceForLogicApp(backupHis.getAppLogicId()));
        }
        CustPreconditions.checkNotNull(backupHis.getAppId(), "备份app id为空");

        Map map = backupService.backupConfig();
        CustPreconditions.checkNotNull(map.get("maxBackupDuration"), "备份超时时间为空");
        CustPreconditions.checkNotNull(map.get("backupSetRetention"), "备份文件保存时长为空");
        CustPreconditions.checkNotNull(map.get("backupLogRetention"), "高频备份文件保存时长为空");

        backupHis.setMaxBackupDuration((Integer)map.get("maxBackupDuration"));
        backupHis.setMaxBackupDuration((Integer)map.get("backupSetRetention"));
        backupHis.setMaxBackupDuration((Integer)map.get("backupLogRetention"));
        return ResponseResult.ok(backupService.manualBackup(backupHis,dbType),null);
    }

    @ApiOperation("备份提交校验")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用id", paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "appLogicId", value = "逻辑应用id", paramType = "query", dataType = "Integer")
    })
    @GetMapping("check")
    public ResponseResult backupCheck(@RequestParam(required = false) Integer appId,@RequestParam(required = false) Integer appLogicId) throws Exception {
        CustPreconditions.checkState(appId != null || appLogicId != null, "缺少参数-应用id");
        if (appId == null) {
            appId = appMultiAZService.selectBackupSourceForLogicApp(appLogicId);
        }
        return ResponseResult.ok(backupService.checkBackup(appId, null), null);

    }

    @ApiOperation("备份历史列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageDTO", value = "分页参数", paramType = "body", dataType = "PageDTO")
    })
    @PostMapping("list/page")
    public ResponseResult listPage(@RequestBody PageDTO pageDTO) {
        return ResponseResult.ok(backupService.listPage(pageDTO),null);
    }

    @ApiOperation("恢复提交校验")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用id", paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "appLogicId", value = "逻辑应用id", paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "backupHisId", value = "备份历史id", paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "restoreTime", value = "恢复时间", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "backupAppId", value = "来源应用id", paramType = "query", dataType = "Integer")
    })
    @GetMapping("restore/check")
    public ResponseResult restoreCheck(@RequestParam(required = false) Integer appId,
                                       @RequestParam(required = false) Integer appLogicId,
                                       @RequestParam(required = false) Integer backupHisId,
                                       @RequestParam(required = false) String restoreTime,
                                       @RequestParam(value = "backupAppId", required = false) Integer backupAppId) throws Exception {
        CustPreconditions.checkState(appId != null || appLogicId != null, "缺少参数-应用id");
        if (appId == null) {
            appId = appMultiAZService.selectBackupSourceForLogicApp(appLogicId);
        }
        if (restoreTime != null && backupAppId != null){
            //根据appLogicId查询appId
            backupAppId = cloudAppService.getCloudAppByLogicId(backupAppId).getId();
        }
        return ResponseResult.ok(backupService.checkRestore(appId, backupHisId, restoreTime, backupAppId), null);
    }

    @ApiOperation(value = "备份文件恢复", notes = "备份文件恢复")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "backupHisId", value = "备份历史id", paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "dbType", value = "应用类型", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appId", value = "应用id", paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "appLogicId", value = "逻辑应用id", paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "restoreTime", value = "恢复时间", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "backupAppId", value = "来源应用id", paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "backupFile", value = "备份文件名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "backupType", value = "备份类型", paramType = "query", dataType = "String")
    })
    @PostMapping("restore")
    @PreAuthorize("@dataAuthorizeService.hasPermission(#appLogicId, #appId)")
    public void restore(@RequestParam(value = "backupHisId", required = false) Integer backupHisId,
                        @RequestParam("dbType") String dbType,
                        @RequestParam(value = "appId", required = false) Integer appId,
                        @RequestParam(value = "appLogicId", required = false) Integer appLogicId,
                        @RequestParam(value = "restoreTime", required = false) String restoreTime,
                        @RequestParam(value = "backupAppId", required = false) Integer backupAppId,
                        @RequestParam(value = "backupFile", required = false) String backupFile,
                        @RequestParam(value = "backupType", required = false, defaultValue = "full") String backupType) throws Exception {
        if (restoreTime != null && backupAppId != null){
            //根据appLogicId查询appId
            backupAppId = cloudAppService.getCloudAppByLogicId(backupAppId).getId();
        }
        if (restoreTime == null && backupHisId == null && backupFile == null) throw new CustomException(600, "参数错误");
        if (backupHisId == null && backupFile != null && backupType == null) throw new CustomException(600, "参数错误");
        if (appLogicId != null) {
            // todo 多az支持
            appId = cloudAppLogicService.getPhysicApps(appLogicId).stream().map(i -> i.getId()).findFirst().orElseThrow(() -> new IllegalArgumentException("应用视图无子集群应用"));
        }
        if (appId == null) throw new CustomException(600, "参数错误");
        backupService.restore(dbType, appId, backupHisId, restoreTime, backupAppId, backupFile, backupType);
    }

    @ApiOperation(value = "获取任意一集群下的备份信息", notes = "获取备份信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "kubeId", value = "集群id", paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "namespace", value = "命名空间", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "dbType", value = "应用类型", paramType = "query", dataType = "String")
    })
    @GetMapping("backupHis")
    public List<BackupHis> listBackupHis(@RequestParam("kubeId") int kubeId,@RequestParam("namespace") String namespace,@RequestParam("dbType") String dbType){
        return cloudAppService.getBackupHisByKubeId(kubeId,namespace,dbType);
    }

    @ApiOperation(value = "根据appId获取开始和最后可选的恢复时间", notes = "根据appId获取开始和最后可选的恢复时间")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用id", paramType = "query", dataType = "Integer")
    })
    @GetMapping("getStartAndEndRestoreTime")
    public Map<String, Object> getLastRestoreTime(@RequestParam("appId") Integer appId) {
        return backupUtil.getLastRestoreTime(appId);
    }

    @ApiOperation(value = "根据appId获取开始可选的恢复时间", notes = "根据appId获取开始可选的恢复时间")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用id", paramType = "query", dataType = "Integer")
    })
    @GetMapping("getFirstRestoreTime")
    public Timestamp getFirstRestoreTime(@RequestParam("appId") Integer appId) {
        Timestamp firstRestoreTime = backupService.getFirstRestoreTime(appId);
        return firstRestoreTime;
    }

//    @ApiOperation(value = "获取已经备份过的应用列表", notes = "获取已经备份过的应用列表")
//    @GetMapping("findHaveBackupAppList")
//    public List<CloudAppVO> findHaveBackupAppList(String kubeName){
//        List<CloudAppVO> appList = backupService.findHaveBackupAppList(kubeName);
//        return appList;
//    }

    @ApiOperation(value = "查询有备份历史的应用", notes = "查询有备份历史的应用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "condition", value = "筛选条件", paramType = "body", dataType = "PageDTO"),
            @ApiImplicitParam(name = "kubeName", value = "集群名称", paramType = "query", dataType = "String")
    })
    @PostMapping("findHaveBackupAppList")
    public ResponseResult<PageInfo<CloudAppVO>> findHaveBackupAppList(@RequestBody PageDTO condition, String kubeName) {
        return ResponseResult.ok(backupService.searchPage(condition), null);
    }

    @ApiOperation(value = "查询ftp备份文件从ftp恢复", notes = "最终查询目录为 basePath + path")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "path", value = "ftp路径", paramType = "query", dataType = "String")
    })
    @GetMapping("ftpFiles")
    public ResponseResult<PageInfo<CloudAppVO>> listBackupFiles(@RequestParam(required = false) String path) {
        return ResponseResult.ok(backupService.listFtpBackupFiles(path), null);
    }

    @ApiOperation(value = "插入binlog备份历史", notes = "插入binlog备份历史")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "binlogBackupHis", value = "binlog备份历史", paramType = "body", dataType = "BinlogBackupHis")
    })
    @PostMapping("/mysql/insertBinlogBackupHis")
    public Integer insertBinlogBackupHis(@RequestBody BinlogBackupHis binlogBackupHis) {
        Integer binlogBackupHisId = backupService.insertBinlogBackupHis(binlogBackupHis);
        return binlogBackupHisId;
    }

    @Deprecated
    @GetMapping(value = "backup-file-download")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appType", value = "应用类型", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "fileName", value = "备份文件名称", paramType = "query", dataType = "String")
    })
    @ApiOperation("备份文件下载")
    public Object downloadRedisLog(String appType, String fileName) {
        try {
            if (!fileName.contains("/")){
                throw new IllegalArgumentException("备份文件路径错误");
            }
            if (!"redis".equalsIgnoreCase(appType)) {
                throw new IllegalArgumentException("appType " + appType + " 不支持");
            }
            int i = fileName.lastIndexOf("/");
            String path = fileName.substring(0, i);
            String name = fileName.substring(i + 1);
            InputStream is = ftpUtils.downloadForInputStream(path, name);
            if (is == null) {
                throw new IllegalArgumentException("文件不存在：" + fileName);
            }

            String shortName = fileName.replace("\\", "/");
            shortName = shortName.substring(shortName.lastIndexOf("/") + 1);

            HttpHeaders headers = new HttpHeaders();
            headers.set("content-disposition", "attachment;filename=" + shortName);
            headers.set("content-type", "application/octet-stream");
            return new ResponseEntity<>(FileCopyUtils.copyToByteArray(is), headers, HttpStatus.OK);
        } catch (Exception e) {
            throw new CustomException(600, e.getMessage());
        }
    }


    @GetMapping(value = "backupFile")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "backupHisId", value = "备份文件id", paramType = "query", dataType = "Integer")
    })
    @ApiOperation("备份文件下载")
    public Object downloadBackupFile(int backupHisId) {
        return backupService.downloadBackupFile(backupHisId);
    }


    @ApiOperation(value = "查询备份默认配置", notes = "查询备份默认配置")
    @GetMapping("backupConfig")
    public ResponseResult backupConfig() {
        return ResponseResult.ok(backupService.backupConfig(), null);
    }

    @ApiOperation(value = "提交备份默认配置", notes = "提交备份默认配置")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "backupSetRetention", value = "备份文件保留时长", paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "backupLogRetention", value = "binlog保留时长", paramType = "query", dataType = "Integer"),
            @ApiImplicitParam(name = "maxBackupDuration", value = "备份超时时间", paramType = "query", dataType = "Integer")
    })
    @PutMapping("backupConfig")
    public void backupConfig(Integer backupSetRetention, Integer backupLogRetention, Integer maxBackupDuration) {
        backupService.backupConfig(backupSetRetention, backupLogRetention, maxBackupDuration);
    }

    @ApiOperation("未配置备份策略的应用数")
    @PostMapping("metric")
    public ResponseResult getMetrics(@RequestBody PageDTO pageDTO) {
        return ResponseResult.ok(backupService.getMetrics(pageDTO), null);
    }
}
