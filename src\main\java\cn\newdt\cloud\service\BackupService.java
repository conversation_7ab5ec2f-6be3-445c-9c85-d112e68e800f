package cn.newdt.cloud.service;

import cn.newdt.cloud.domain.BackupHis;
import cn.newdt.cloud.domain.RestoreHis;
import cn.newdt.cloud.domain.dmp.BinlogBackupHis;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.vo.BackupHisVO;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface BackupService extends BaseService<BackupHis> {

    void restore(String dbType, Integer appId, Integer backupHisId, String restoreTime, Integer backupAppId, String ftpPath, String backupType);

    /**
     * 根据类型进行手动备份
     * @param backupHis
     * @return
     * @throws Exception
     */
    BackupHis manualBackup(BackupHisVO backupHis, String dbType) throws Exception;

    /**
     * 根据appId获取最后可选的恢复时间
     * @param appId
     * @return
     */
    Timestamp getLastRestoreTime(Integer appId);

    /**
     * 根据appId获取最初可选的恢复时间
     * @param appId
     * @return
     */
    Timestamp getFirstRestoreTime(Integer appId);

    /**
     * 根据kubeId获取备份过的应用信息
     * @return
     */
//    List<CloudAppVO> findHaveBackupAppList(String kubeName);

    HashMap<String, Object> searchPage(PageDTO condition);

//    /**
//     * 按时间点恢复
//     * @param backupHisId
//     * @param appId
//     * @param restoreTime
//     */
//    void restoreByTime(Integer backupHisId, String appId, String restoreTime);
    Optional<BackupHis> latestSince(Integer id, LocalDateTime sinceTime);

    /**
     * @param path list文件的目录. 追加在basepath后.
     * @return 文件名
     */
    List<String> listFtpBackupFiles(String path);

    /**
     * 新增binlog备份历史
     * @param binlogBackupHis binlog备份历史
     * @return 文件名
     */
    Integer insertBinlogBackupHis(BinlogBackupHis binlogBackupHis);

    /**
     * 根据应用id查询最新一条binlog记录
     * @param id
     * @return
     */
    BinlogBackupHis listBinlogBackupHis(Integer id);

    /**
     * 返回大于开始时间的第一条binlog记录
     * @param appId
     * @param startTime
     * @return
     */
    BinlogBackupHis getBinlogBackupHisGtStartTime(Integer appId,String startTime );

    Map<String, Map<String, Object>> checkBackup(Integer appId, PodDTO backupPod);

    /**
     * 恢复前校验，1. 校验备份目录是否有足够空间下载备份文件；2. 校验备份文件代表的数据大小是否超出恢复目标应用的数据存储
     *
     * @return [checkItem, result] K,V map
     */
    Map<String, Map<String, Object>> checkRestore(Integer appId, Integer backupHisId, String restoreTime, Integer backupAppId);

    Map backupConfig();

    void backupConfig(Integer backupSetRetention, Integer backupLogRetention, Integer maxBackupDuration);

    /**
     * 备份历史状态和最近条数统计
     */
    Object getMetrics(PageDTO params);


    void stopBackupJobHisForApp(int id);

    void commitRestoreHis(RestoreHis restoreHis);

    void updateBinlog(BinlogBackupHis binlogBackupHis);

    List<BinlogBackupHis> listBinlogHis(Integer appId);

    BinlogBackupHis getBinlogBackupHisById(Integer binlogHisId);

    Object downloadBackupFile(int backupHisId);
}
