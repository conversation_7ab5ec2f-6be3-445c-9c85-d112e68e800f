
2025-07-25 09:20:14.967       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-25 09:20:15.019       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-25 09:20:58.789       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-25 09:21:06.053       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 09:21:06.057       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 09:21:06.810       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-25 09:21:06.822       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-25 09:21:07.783       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 09:21:07.786       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 09:30:07.714       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .apm-custom-link, .tasks, .kibana_7.12.0_001, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 09:30:07.718       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-custom-link, .async-search, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .tasks, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 09:30:09.913       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 09:30:09.979       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 09:30:10.060       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 09:30:10.679       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 09:31:41.722       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-agent-configuration, .kibana_7.12.0_001, .tasks, .security-7, .apm-custom-link, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 09:46:01.706       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .security-7, .async-search, .apm-custom-link, .apm-agent-configuration, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 09:46:01.726       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .security-7, .tasks, .apm-custom-link, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 09:46:03.213       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 09:46:03.245       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 09:46:03.280       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 09:46:03.461       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 10:15:33.275       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_task_manager_7.12.0_001, .apm-custom-link, .tasks, .async-search, .security-7, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 10:37:24.112       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .security-7, .apm-agent-configuration, .apm-custom-link, .async-search, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 10:37:33.870       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-custom-link, .kibana_7.12.0_001, .tasks, .kibana_task_manager_7.12.0_001, .security-7, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 10:48:49.376       LEVEL:WARN  ThreadId:
				POSITION:io.fabric8.kubernetes.client.internal.VersionUsageUtils  LINE:60 
				MESSAGE:The client is using resource type 'alertmanagerconfigs' with unstable version 'v1alpha1'

2025-07-25 10:50:49.251       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-custom-link, .security-7, .tasks, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 10:51:12.689       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-custom-link, .async-search, .security-7, .kibana_7.12.0_001, .apm-agent-configuration, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 10:58:04.571       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .security-7, .tasks, .async-search, .apm-agent-configuration, .apm-custom-link, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 10:58:13.351       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_task_manager_7.12.0_001, .async-search, .kibana_7.12.0_001, .security-7, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:02:07.480       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .security-7, .async-search, .kibana_task_manager_7.12.0_001, .apm-custom-link, .tasks, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:03:20.844       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .kibana_task_manager_7.12.0_001, .security-7, .tasks, .apm-agent-configuration, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:22:33.210       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .security-7, .tasks, .apm-agent-configuration, .async-search, .apm-custom-link, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:22:33.210       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .security-7, .async-search, .apm-agent-configuration, .apm-custom-link, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:22:34.658       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:22:34.698       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:22:34.732       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:22:34.866       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:22:53.620       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .security-7, .tasks, .apm-agent-configuration, .kibana_7.12.0_001, .async-search, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:22:59.788       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .security-7, .kibana_task_manager_7.12.0_001, .async-search, .apm-custom-link, .tasks, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:23:00.005       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .apm-agent-configuration, .apm-custom-link, .async-search, .kibana_task_manager_7.12.0_001, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:23:02.397       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .async-search, .tasks, .kibana_task_manager_7.12.0_001, .security-7, .apm-agent-configuration, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:23:02.445       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .async-search, .kibana_7.12.0_001, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:23:03.900       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:23:03.931       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:23:03.957       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:23:04.123       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:23:18.298       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-custom-link, .async-search, .apm-agent-configuration, .tasks, .kibana_task_manager_7.12.0_001, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:23:21.500       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-agent-configuration, .apm-custom-link, .security-7, .kibana_7.12.0_001, .tasks, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:23:23.915       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-custom-link, .tasks, .async-search, .apm-agent-configuration, .security-7, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:23:29.651       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-custom-link, .apm-agent-configuration, .security-7, .kibana_task_manager_7.12.0_001, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:31:19.865       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-agent-configuration, .apm-custom-link, .async-search, .kibana_7.12.0_001, .tasks, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:31:21.989       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .security-7, .async-search, .apm-custom-link, .tasks, .kibana_task_manager_7.12.0_001, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:31:22.164       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .async-search, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .apm-custom-link, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:32:23.629       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .async-search, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .kibana_7.12.0_001, .tasks, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:32:32.256       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .async-search, .security-7, .tasks, .kibana_task_manager_7.12.0_001, .apm-custom-link, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:32:32.256       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_task_manager_7.12.0_001, .apm-custom-link, .tasks, .security-7, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:32:33.634       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:32:33.662       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:32:33.692       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:32:33.810       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:32:40.024       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-custom-link, .kibana_7.12.0_001, .tasks, .security-7, .apm-agent-configuration, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:33:05.306       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .tasks, .async-search, .apm-custom-link, .kibana_7.12.0_001, .security-7, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:34:43.859       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .async-search, .kibana_7.12.0_001, .tasks, .kibana_task_manager_7.12.0_001, .security-7, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:39:05.693       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-agent-configuration, .async-search, .tasks, .apm-custom-link, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:39:50.514       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .async-search, .apm-custom-link, .security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:39:56.634       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .security-7, .tasks, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-agent-configuration, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:40:00.761       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-agent-configuration, .security-7, .apm-custom-link, .kibana_task_manager_7.12.0_001, .async-search, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:40:53.151       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .kibana_7.12.0_001, .async-search, .apm-custom-link, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:40:56.029       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .async-search, .apm-agent-configuration, .kibana_7.12.0_001, .security-7, .tasks, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:41:18.064       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-custom-link, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .tasks, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:41:38.882       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-agent-configuration, .apm-custom-link, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .tasks, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:41:39.238       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_7.12.0_001, .apm-custom-link, .apm-agent-configuration, .tasks, .kibana_task_manager_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:41:50.738       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .async-search, .apm-agent-configuration, .security-7, .apm-custom-link, .tasks, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:41:51.753       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-custom-link, .tasks, .apm-agent-configuration, .async-search, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:41:54.392       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .tasks, .kibana_7.12.0_001, .apm-agent-configuration, .apm-custom-link, .kibana_task_manager_7.12.0_001, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:41:54.530       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .kibana_task_manager_7.12.0_001, .security-7, .apm-agent-configuration, .tasks, .kibana_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:42:36.738       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .security-7, .tasks, .async-search, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:42:45.744       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .security-7, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .async-search, .tasks, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:42:45.904       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .async-search, .apm-custom-link, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:43:26.388       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .async-search, .apm-agent-configuration, .kibana_7.12.0_001, .security-7, .tasks, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:43:26.557       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-custom-link, .tasks, .async-search, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:43:27.489       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .security-7, .async-search, .tasks, .apm-custom-link, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:43:29.328       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .security-7, .tasks, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:43:29.479       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-custom-link, .tasks, .security-7, .apm-agent-configuration, .kibana_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:43:30.660       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .security-7, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .kibana_7.12.0_001, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:43:31.584       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .kibana_7.12.0_001, .async-search, .kibana_task_manager_7.12.0_001, .security-7, .apm-custom-link, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:43:32.301       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .security-7, .tasks, .async-search, .apm-custom-link, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:43:33.624       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .kibana_7.12.0_001, .security-7, .async-search, .kibana_task_manager_7.12.0_001, .tasks, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:43:33.750       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .apm-agent-configuration, .security-7, .kibana_task_manager_7.12.0_001, .async-search, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:43:34.249       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-custom-link, .apm-agent-configuration, .kibana_7.12.0_001, .security-7, .kibana_task_manager_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:43:36.381       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-agent-configuration, .kibana_7.12.0_001, .security-7, .async-search, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:43:44.447       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_7.12.0_001, .tasks, .apm-custom-link, .async-search, .security-7, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:44:20.235       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-custom-link, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .async-search, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:45:26.044       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .async-search, .tasks, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-agent-configuration, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:45:45.771       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-custom-link, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .tasks, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:45:45.774       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .apm-custom-link, .async-search, .kibana_task_manager_7.12.0_001, .security-7, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:45:47.137       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:45:47.165       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:45:47.192       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:45:47.282       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:46:19.390       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .async-search, .kibana_7.12.0_001, .apm-agent-configuration, .security-7, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:46:26.647       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .async-search, .tasks, .security-7, .apm-custom-link, .apm-agent-configuration, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:47:31.179       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-agent-configuration, .apm-custom-link, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:47:31.180       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .security-7, .tasks, .apm-custom-link, .async-search, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:47:32.547       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:47:32.575       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:47:32.602       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:47:32.731       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 11:51:35.142       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-custom-link, .async-search, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .tasks, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 11:52:13.508       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-25 11:52:13.508       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-25 11:52:13.509       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-25 11:52:13.513       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-25 11:52:47.411       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-25 11:52:47.427       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-25 11:53:28.507       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-25 11:53:34.256       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 11:53:34.259       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 11:53:34.913       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-25 11:53:34.924       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-25 11:53:35.883       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 11:53:35.886       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 11:59:22.593       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .async-search, .apm-agent-configuration, .apm-custom-link, .kibana_task_manager_7.12.0_001, .tasks, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 12:01:31.297       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-25 12:01:31.297       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-25 12:01:31.300       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-25 12:01:31.303       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-25 12:01:51.299       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-25 12:01:51.304       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-25 12:02:31.393       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-25 12:02:36.066       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 12:02:36.070       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 12:02:37.311       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-25 12:02:37.351       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-25 12:02:39.309       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 12:02:39.325       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 12:02:58.372       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .apm-agent-configuration, .kibana_7.12.0_001, .async-search, .security-7, .tasks, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 13:43:00.757       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .tasks, .apm-custom-link, .security-7, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 13:43:27.349       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .security-7, .async-search, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 13:43:27.464       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .tasks, .apm-custom-link, .security-7, .async-search, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 13:43:34.216       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 13:43:34.248       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 13:43:34.279       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 13:43:34.630       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 13:45:00.457       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-custom-link, .security-7, .apm-agent-configuration, .kibana_7.12.0_001, .async-search, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 13:45:59.361       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_task_manager_7.12.0_001, .tasks, .kibana_7.12.0_001, .apm-agent-configuration, .async-search, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 13:46:47.032       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .async-search, .apm-custom-link, .kibana_7.12.0_001, .security-7, .kibana_task_manager_7.12.0_001, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 13:49:47.223       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .kibana_task_manager_7.12.0_001, .async-search, .kibana_7.12.0_001, .security-7, .apm-agent-configuration, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 13:54:00.321       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .async-search, .apm-agent-configuration, .security-7, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 14:13:42.001       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .async-search, .apm-custom-link, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 14:14:17.993       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .security-7, .apm-agent-configuration, .async-search, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 14:15:45.088       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-custom-link, .kibana_task_manager_7.12.0_001, .async-search, .apm-agent-configuration, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 14:18:34.608       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-custom-link, .kibana_7.12.0_001, .apm-agent-configuration, .async-search, .tasks, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 14:35:30.444       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_task_manager_7.12.0_001, .apm-custom-link, .kibana_7.12.0_001, .async-search, .tasks, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 14:36:32.249       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_task_manager_7.12.0_001, .apm-custom-link, .tasks, .apm-agent-configuration, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 14:38:36.180       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .async-search, .kibana_task_manager_7.12.0_001, .security-7, .apm-agent-configuration, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 14:38:36.196       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .security-7, .apm-custom-link, .async-search, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 14:38:38.432       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 14:38:38.525       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 14:38:38.645       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 14:38:39.207       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 14:38:41.320       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .kibana_7.12.0_001, .apm-custom-link, .kibana_task_manager_7.12.0_001, .async-search, .security-7, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 14:38:48.225       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .security-7, .kibana_task_manager_7.12.0_001, .apm-custom-link, .tasks, .async-search, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 14:46:52.284       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .security-7, .apm-agent-configuration, .tasks, .async-search, .kibana_task_manager_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:06:14.807       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .security-7, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .tasks, .apm-custom-link, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:08:32.467       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .async-search, .tasks, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .security-7, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:16:42.626       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .async-search, .security-7, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:18:53.785       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .kibana_task_manager_7.12.0_001, .apm-custom-link, .async-search, .kibana_7.12.0_001, .security-7, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:19:48.079       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-agent-configuration, .apm-custom-link, .kibana_7.12.0_001, .tasks, .async-search, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:20:20.256       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .apm-custom-link, .kibana_task_manager_7.12.0_001, .tasks, .async-search, .security-7, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:20:42.827       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-custom-link, .tasks, .async-search, .apm-agent-configuration, .security-7, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:20:45.591       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .async-search, .security-7, .kibana_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:21:37.406       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .async-search, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .tasks, .security-7, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:25:05.492       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .tasks, .apm-agent-configuration, .security-7, .kibana_7.12.0_001, .apm-custom-link, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:41:43.729       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .apm-custom-link, .security-7, .tasks, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:45:31.938       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .tasks, .kibana_task_manager_7.12.0_001, .security-7, .async-search, .apm-agent-configuration, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:54:25.646       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-custom-link, .security-7, .async-search, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:54:25.646       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .kibana_7.12.0_001, .async-search, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .apm-custom-link, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:54:27.591       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 15:54:27.621       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 15:54:27.693       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 15:54:27.953       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 15:55:04.375       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .security-7, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .apm-custom-link, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:55:43.347       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .async-search, .security-7, .apm-agent-configuration, .apm-custom-link, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:55:43.358       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .tasks, .apm-agent-configuration, .security-7, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 15:55:44.744       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 15:55:44.777       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 15:55:44.812       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 15:55:44.925       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 15:55:54.987       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":27}

2025-07-25 15:55:55.018       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":27}

2025-07-25 15:55:55.054       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":27}

2025-07-25 15:55:55.460       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":27}

2025-07-25 15:58:41.301       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-25 15:58:41.301       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-25 15:58:41.302       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-25 15:58:41.306       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-25 15:59:21.674       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-25 15:59:21.681       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-25 16:00:11.271       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-25 16:00:17.569       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 16:00:17.580       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 16:00:18.976       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-25 16:00:19.018       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-25 16:00:20.721       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 16:00:20.725       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 16:06:24.633       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-25 16:06:24.633       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-25 16:06:24.641       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-25 16:06:24.649       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-25 16:06:51.663       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-25 16:06:51.670       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-25 16:07:42.847       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-25 16:07:48.428       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 16:07:48.432       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 16:07:49.428       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-25 16:07:49.444       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-25 16:07:50.552       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 16:07:50.555       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 16:17:58.700       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-custom-link, .async-search, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-agent-configuration, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:18:27.190       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-custom-link, .async-search, .kibana_7.12.0_001, .tasks, .kibana_task_manager_7.12.0_001, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:18:44.395       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_7.12.0_001, .apm-custom-link, .kibana_task_manager_7.12.0_001, .tasks, .apm-agent-configuration, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:19:07.798       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .async-search, .apm-agent-configuration, .tasks, .kibana_7.12.0_001, .security-7, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:19:33.634       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .async-search, .kibana_7.12.0_001, .tasks, .apm-agent-configuration, .apm-custom-link, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:19:33.668       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .security-7, .kibana_task_manager_7.12.0_001, .apm-custom-link, .kibana_7.12.0_001, .apm-agent-configuration, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:19:35.961       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:19:36.014       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:19:36.078       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:19:36.314       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:19:36.343       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:19:36.391       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:19:36.429       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:19:36.570       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:20:47.311       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_7.12.0_001, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .apm-custom-link, .async-search, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:20:52.677       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .kibana_task_manager_7.12.0_001, .tasks, .apm-custom-link, .apm-agent-configuration, .security-7, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:21:03.612       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .async-search, .apm-agent-configuration, .security-7, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:21:12.434       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .async-search, .security-7, .tasks, .kibana_7.12.0_001, .apm-custom-link, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:21:28.224       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-agent-configuration, .tasks, .async-search, .security-7, .kibana_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:22:35.491       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .kibana_task_manager_7.12.0_001, .tasks, .apm-custom-link, .kibana_7.12.0_001, .security-7, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:22:59.473       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .tasks, .apm-agent-configuration, .apm-custom-link, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:23:01.524       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:23:01.544       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:23:01.557       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:23:01.589       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:23:01.598       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:23:01.636       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:23:01.843       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:23:01.846       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:23:09.458       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .apm-custom-link, .kibana_7.12.0_001, .async-search, .security-7, .kibana_task_manager_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:23:20.080       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-agent-configuration, .apm-custom-link, .async-search, .security-7, .tasks, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:24:40.173       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .security-7, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .apm-custom-link, .async-search, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:25:20.022       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:25:20.092       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:25:20.141       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .security-7, .kibana_task_manager_7.12.0_001, .async-search, .apm-custom-link, .tasks, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:25:20.160       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:25:20.503       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:26:00.491       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":27}

2025-07-25 16:26:00.517       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-custom-link, .async-search, .apm-agent-configuration, .tasks, .security-7, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:26:00.523       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":27}

2025-07-25 16:26:00.551       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":27}

2025-07-25 16:26:00.793       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":27}

2025-07-25 16:29:45.253       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .async-search, .security-7, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:29:47.185       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:29:47.231       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:29:47.231       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .tasks, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .async-search, .kibana_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:29:47.262       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:29:47.407       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:30:19.173       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .security-7, .apm-custom-link, .apm-agent-configuration, .async-search, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:30:19.230       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:30:19.326       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-custom-link, .async-search, .security-7, .kibana_7.12.0_001, .apm-agent-configuration, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:30:19.584       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:30:19.911       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:30:20.335       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:30:29.115       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-agent-configuration, .kibana_7.12.0_001, .security-7, .async-search, .apm-custom-link, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:30:29.129       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .async-search, .security-7, .apm-agent-configuration, .apm-custom-link, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:32:57.338       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:32:57.387       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:32:57.419       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:32:57.485       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-agent-configuration, .tasks, .apm-custom-link, .async-search, .kibana_7.12.0_001, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:32:57.512       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-agent-configuration, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .security-7, .async-search, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:32:57.577       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:34:06.696       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .async-search, .tasks, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-custom-link, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:34:20.652       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:177
				MESSAGE:异常ID:6e58b277-d911-40f6-9ad5-508bb8ed897d, 请求地址:http://192.168.2.217:8300/cloudnormal/backup/backupFile
cn.newdt.commons.exception.CustomException: 从备份存储获取文件流失败
	at cn.newdt.cloud.service.impl.BackupServiceImpl.downloadBackupFile(BackupServiceImpl.java:630)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$FastClassBySpringCGLIB$$f56eef78.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$EnhancerBySpringCGLIB$$9f5bdf01.downloadBackupFile(<generated>)
	at cn.newdt.cloud.web.BackupController.downloadBackupFile(BackupController.java:280)
	at cn.newdt.cloud.web.BackupController$$FastClassBySpringCGLIB$$d3348832.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.BackupController$$EnhancerBySpringCGLIB$$38d9d79.downloadBackupFile(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 16:34:24.433       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:177
				MESSAGE:异常ID:0f5da192-6ed9-4fc4-b624-77429650f113, 请求地址:http://192.168.2.217:8300/cloudnormal/backup/backupFile
cn.newdt.commons.exception.CustomException: 从备份存储获取文件流失败
	at cn.newdt.cloud.service.impl.BackupServiceImpl.downloadBackupFile(BackupServiceImpl.java:630)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$FastClassBySpringCGLIB$$f56eef78.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$EnhancerBySpringCGLIB$$9f5bdf01.downloadBackupFile(<generated>)
	at cn.newdt.cloud.web.BackupController.downloadBackupFile(BackupController.java:280)
	at cn.newdt.cloud.web.BackupController$$FastClassBySpringCGLIB$$d3348832.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.BackupController$$EnhancerBySpringCGLIB$$38d9d79.downloadBackupFile(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 16:35:14.272       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:177
				MESSAGE:异常ID:5f79b342-c5c5-413a-b39c-854f05a36958, 请求地址:http://192.168.2.217:8300/cloudnormal/backup/backupFile
cn.newdt.commons.exception.CustomException: 从备份存储获取文件流失败
	at cn.newdt.cloud.service.impl.BackupServiceImpl.downloadBackupFile(BackupServiceImpl.java:630)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$FastClassBySpringCGLIB$$f56eef78.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$EnhancerBySpringCGLIB$$9f5bdf01.downloadBackupFile(<generated>)
	at cn.newdt.cloud.web.BackupController.downloadBackupFile(BackupController.java:280)
	at cn.newdt.cloud.web.BackupController$$FastClassBySpringCGLIB$$d3348832.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.BackupController$$EnhancerBySpringCGLIB$$38d9d79.downloadBackupFile(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 16:40:21.487       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.scheduling.quartz.LocalDataSourceJobStore  LINE:3411
				MESSAGE:This scheduler instance (LAPTOP-OUTBTI751753430831660) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior.

2025-07-25 16:40:25.090       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:177
				MESSAGE:异常ID:2a456b53-0d80-46a0-8bc5-a4bff00565d0, 请求地址:http://192.168.2.217:8300/cloudnormal/backup/backupFile
cn.newdt.commons.exception.CustomException: 从备份存储获取文件流失败
	at cn.newdt.cloud.service.impl.BackupServiceImpl.downloadBackupFile(BackupServiceImpl.java:630)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$FastClassBySpringCGLIB$$f56eef78.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$EnhancerBySpringCGLIB$$9f5bdf01.downloadBackupFile(<generated>)
	at cn.newdt.cloud.web.BackupController.downloadBackupFile(BackupController.java:280)
	at cn.newdt.cloud.web.BackupController$$FastClassBySpringCGLIB$$d3348832.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.BackupController$$EnhancerBySpringCGLIB$$38d9d79.downloadBackupFile(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 16:40:53.428       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:177
				MESSAGE:异常ID:d3f4b58e-6029-48be-a540-fd6dfe8624a3, 请求地址:http://192.168.2.217:8300/cloudnormal/backup/backupFile
cn.newdt.commons.exception.CustomException: Unexpected internal error near index 1
\
	at cn.newdt.cloud.service.impl.BackupServiceImpl.downloadBackupFile(BackupServiceImpl.java:630)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$FastClassBySpringCGLIB$$f56eef78.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$EnhancerBySpringCGLIB$$9f5bdf01.downloadBackupFile(<generated>)
	at cn.newdt.cloud.web.BackupController.downloadBackupFile(BackupController.java:280)
	at cn.newdt.cloud.web.BackupController$$FastClassBySpringCGLIB$$d3348832.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.BackupController$$EnhancerBySpringCGLIB$$38d9d79.downloadBackupFile(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 16:41:36.889       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.scheduling.quartz.LocalDataSourceJobStore  LINE:3411
				MESSAGE:This scheduler instance (LAPTOP-OUTBTI751753430831660) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior.

2025-07-25 16:42:02.065       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:177
				MESSAGE:异常ID:f9ea4851-8b0e-4470-bcf9-08ccdf5ccb2f, 请求地址:http://192.168.2.217:8300/cloudnormal/backup/backupFile
cn.newdt.commons.exception.CustomException: Unexpected internal error near index 1
\
	at cn.newdt.cloud.service.impl.BackupServiceImpl.downloadBackupFile(BackupServiceImpl.java:630)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$FastClassBySpringCGLIB$$f56eef78.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$EnhancerBySpringCGLIB$$9f5bdf01.downloadBackupFile(<generated>)
	at cn.newdt.cloud.web.BackupController.downloadBackupFile(BackupController.java:280)
	at cn.newdt.cloud.web.BackupController$$FastClassBySpringCGLIB$$d3348832.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.BackupController$$EnhancerBySpringCGLIB$$38d9d79.downloadBackupFile(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 16:42:42.042       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.scheduling.quartz.LocalDataSourceJobStore  LINE:3411
				MESSAGE:This scheduler instance (LAPTOP-OUTBTI751753430831660) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior.

2025-07-25 16:42:42.055       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:177
				MESSAGE:异常ID:34c3d2e4-3990-4aff-84a9-4dec893e63ac, 请求地址:http://192.168.2.217:8300/cloudnormal/backup/backupFile
cn.newdt.commons.exception.CustomException: Unexpected internal error near index 1
\
	at cn.newdt.cloud.service.impl.BackupServiceImpl.downloadBackupFile(BackupServiceImpl.java:630)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$FastClassBySpringCGLIB$$f56eef78.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$EnhancerBySpringCGLIB$$9f5bdf01.downloadBackupFile(<generated>)
	at cn.newdt.cloud.web.BackupController.downloadBackupFile(BackupController.java:280)
	at cn.newdt.cloud.web.BackupController$$FastClassBySpringCGLIB$$d3348832.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.BackupController$$EnhancerBySpringCGLIB$$38d9d79.downloadBackupFile(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 16:43:17.219       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-custom-link, .async-search, .apm-agent-configuration, .kibana_7.12.0_001, .security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:51:44.781       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:51:44.814       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:51:44.854       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 16:51:45.106       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-agent-configuration, .async-search, .apm-custom-link, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:51:45.562       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_7.12.0_001, .tasks, .async-search, .apm-custom-link, .kibana_task_manager_7.12.0_001, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 16:51:45.949       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:03:21.206       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .tasks, .kibana_task_manager_7.12.0_001, .async-search, .apm-agent-configuration, .apm-custom-link, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:03:36.724       LEVEL:WARN  ThreadId:
				POSITION:io.fabric8.kubernetes.client.internal.VersionUsageUtils  LINE:60 
				MESSAGE:The client is using resource type 'alertmanagerconfigs' with unstable version 'v1alpha1'

2025-07-25 17:05:48.022       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:05:48.057       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:05:48.061       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_task_manager_7.12.0_001, .tasks, .security-7, .async-search, .apm-custom-link, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:05:48.062       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .security-7, .async-search, .tasks, .kibana_7.12.0_001, .apm-agent-configuration, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:05:48.088       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:05:48.222       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:06:25.392       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:06:25.558       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:06:25.693       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:06:25.698       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_7.12.0_001, .async-search, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:06:25.786       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-custom-link, .security-7, .tasks, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:06:25.869       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:07:58.335       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:07:58.351       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .tasks, .apm-agent-configuration, .apm-custom-link, .kibana_7.12.0_001, .async-search, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:07:58.353       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .async-search, .kibana_7.12.0_001, .apm-agent-configuration, .tasks, .kibana_task_manager_7.12.0_001, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:07:58.375       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:07:58.405       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:07:58.509       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:09:37.342       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:09:37.365       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-custom-link, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .async-search, .kibana_7.12.0_001, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:09:37.380       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:09:37.380       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .kibana_task_manager_7.12.0_001, .security-7, .apm-custom-link, .async-search, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:09:37.414       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:09:37.546       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:14:51.713       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:14:51.742       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_task_manager_7.12.0_001, .async-search, .tasks, .apm-custom-link, .security-7, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:14:51.749       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:14:51.749       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-agent-configuration, .security-7, .kibana_task_manager_7.12.0_001, .async-search, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:14:51.780       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:14:51.919       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:22:26.741       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .security-7, .apm-custom-link, .async-search, .apm-agent-configuration, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:27:04.561       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .tasks, .apm-custom-link, .security-7, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:27:36.270       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .kibana_task_manager_7.12.0_001, .apm-custom-link, .async-search, .security-7, .kibana_7.12.0_001, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:30:24.281       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .kibana_task_manager_7.12.0_001, .security-7, .kibana_7.12.0_001, .apm-agent-configuration, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:34:42.180       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:34:42.256       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:34:42.265       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .async-search, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-custom-link, .tasks, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:34:42.298       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .tasks, .apm-agent-configuration, .async-search, .apm-custom-link, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:34:42.313       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:34:42.631       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:36:06.466       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .tasks, .async-search, .apm-agent-configuration, .security-7, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:36:40.418       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .tasks, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-custom-link, .apm-agent-configuration, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:40:41.886       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-agent-configuration, .security-7, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:46:31.830       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:46:31.881       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:46:31.905       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .tasks, .apm-custom-link, .async-search, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:46:31.912       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-custom-link, .security-7, .tasks, .async-search, .kibana_7.12.0_001, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:46:31.917       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:46:32.170       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:47:02.318       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_task_manager_7.12.0_001, .async-search, .apm-custom-link, .security-7, .tasks, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:47:23.117       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_task_manager_7.12.0_001, .async-search, .tasks, .apm-custom-link, .security-7, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:53:00.740       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:53:00.776       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:53:00.779       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .tasks, .apm-agent-configuration, .apm-custom-link, .security-7, .kibana_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:53:00.784       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .tasks, .async-search, .apm-agent-configuration, .apm-custom-link, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 17:53:00.806       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:53:00.920       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 17:53:05.580       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-25 17:53:05.581       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-25 17:53:05.582       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-25 17:53:05.588       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-25 17:55:25.913       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service] & group[DEFAULT_GROUP]

2025-07-25 17:56:09.753       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-25 17:56:15.893       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 17:56:15.894       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 17:56:16.446       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-25 17:56:16.465       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-25 17:56:17.400       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 17:56:17.408       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 18:01:04.144       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-25 18:01:04.145       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-25 18:01:04.154       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-25 18:01:04.160       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-25 18:01:33.344       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-25 18:01:33.354       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-25 18:02:15.573       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-25 18:02:26.227       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 18:02:26.229       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 18:02:27.480       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-25 18:02:27.490       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-25 18:02:29.488       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 18:02:29.495       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 18:11:41.538       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-25 18:11:41.537       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-25 18:11:41.542       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-25 18:11:41.545       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-25 18:12:01.987       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-25 18:12:01.995       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-25 18:12:35.679       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-25 18:12:39.767       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 18:12:39.770       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 18:12:40.335       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-25 18:12:40.352       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-25 18:12:41.197       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 18:12:41.200       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 18:15:15.774       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:177
				MESSAGE:异常ID:26679941-bf44-42c3-8b1b-bef82d3eb437, 请求地址:http://192.168.2.217:8300/cloudnormal/backup/backupFile
cn.newdt.commons.exception.CustomException: 从备份存储获取文件流失败
	at cn.newdt.cloud.service.impl.BackupServiceImpl.downloadBackupFile(BackupServiceImpl.java:630)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$FastClassBySpringCGLIB$$f56eef78.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$EnhancerBySpringCGLIB$$d2a53694.downloadBackupFile(<generated>)
	at cn.newdt.cloud.web.BackupController.downloadBackupFile(BackupController.java:280)
	at cn.newdt.cloud.web.BackupController$$FastClassBySpringCGLIB$$d3348832.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.BackupController$$EnhancerBySpringCGLIB$$3d42a3ec.downloadBackupFile(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:16:19.646       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:177
				MESSAGE:异常ID:5910cc78-0071-40ed-ac12-33075c421562, 请求地址:http://192.168.2.217:8300/cloudnormal/backup/backupFile
cn.newdt.commons.exception.CustomException: 从备份存储获取文件流失败
	at cn.newdt.cloud.service.impl.BackupServiceImpl.downloadBackupFile(BackupServiceImpl.java:630)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$FastClassBySpringCGLIB$$f56eef78.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$EnhancerBySpringCGLIB$$d2a53694.downloadBackupFile(<generated>)
	at cn.newdt.cloud.web.BackupController.downloadBackupFile(BackupController.java:280)
	at cn.newdt.cloud.web.BackupController$$FastClassBySpringCGLIB$$d3348832.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.BackupController$$EnhancerBySpringCGLIB$$3d42a3ec.downloadBackupFile(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:17:03.852       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:177
				MESSAGE:异常ID:9d3852f7-6ed1-4a27-b56b-f542953de32a, 请求地址:http://192.168.2.217:8300/cloudnormal/backup/backupFile
cn.newdt.commons.exception.CustomException: 从备份存储获取文件流失败
	at cn.newdt.cloud.service.impl.BackupServiceImpl.downloadBackupFile(BackupServiceImpl.java:630)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$FastClassBySpringCGLIB$$f56eef78.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$EnhancerBySpringCGLIB$$d2a53694.downloadBackupFile(<generated>)
	at cn.newdt.cloud.web.BackupController.downloadBackupFile(BackupController.java:280)
	at cn.newdt.cloud.web.BackupController$$FastClassBySpringCGLIB$$d3348832.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.BackupController$$EnhancerBySpringCGLIB$$3d42a3ec.downloadBackupFile(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:18:29.053       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:177
				MESSAGE:异常ID:64a1ce03-98fe-41bd-96e0-ec44f56886aa, 请求地址:http://192.168.2.217:8300/cloudnormal/backup/backupFile
cn.newdt.commons.exception.CustomException: 从备份存储获取文件流失败
	at cn.newdt.cloud.service.impl.BackupServiceImpl.downloadBackupFile(BackupServiceImpl.java:630)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$FastClassBySpringCGLIB$$f56eef78.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$EnhancerBySpringCGLIB$$d2a53694.downloadBackupFile(<generated>)
	at cn.newdt.cloud.web.BackupController.downloadBackupFile(BackupController.java:280)
	at cn.newdt.cloud.web.BackupController$$FastClassBySpringCGLIB$$d3348832.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.BackupController$$EnhancerBySpringCGLIB$$3d42a3ec.downloadBackupFile(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:19:07.974       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:177
				MESSAGE:异常ID:e608449b-cb72-4537-99fd-58b4249e15cc, 请求地址:http://192.168.2.217:8300/cloudnormal/backup/backupFile
cn.newdt.commons.exception.CustomException: 从备份存储获取文件流失败
	at cn.newdt.cloud.service.impl.BackupServiceImpl.downloadBackupFile(BackupServiceImpl.java:630)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$FastClassBySpringCGLIB$$f56eef78.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$EnhancerBySpringCGLIB$$d2a53694.downloadBackupFile(<generated>)
	at cn.newdt.cloud.web.BackupController.downloadBackupFile(BackupController.java:280)
	at cn.newdt.cloud.web.BackupController$$FastClassBySpringCGLIB$$d3348832.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.BackupController$$EnhancerBySpringCGLIB$$3d42a3ec.downloadBackupFile(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:22:02.110       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 18:22:02.147       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 18:22:02.176       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 18:22:02.313       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 18:22:02.356       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .async-search, .apm-agent-configuration, .kibana_7.12.0_001, .tasks, .security-7, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:22:02.356       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .security-7, .tasks, .apm-custom-link, .async-search, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:22:03.604       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .tasks, .apm-custom-link, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:22:03.604       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .async-search, .security-7, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:22:07.811       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .apm-agent-configuration, .tasks, .kibana_7.12.0_001, .async-search, .kibana_task_manager_7.12.0_001, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:22:20.153       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-custom-link, .kibana_task_manager_7.12.0_001, .async-search, .tasks, .apm-agent-configuration, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:22:22.015       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-agent-configuration, .async-search, .tasks, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:22:28.131       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .tasks, .async-search, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:22:49.552       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .tasks, .async-search, .apm-custom-link, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:22:52.011       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-agent-configuration, .tasks, .apm-custom-link, .async-search, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:23:04.171       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_task_manager_7.12.0_001, .security-7, .async-search, .apm-custom-link, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:23:04.318       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .tasks, .security-7, .kibana_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:23:07.421       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-agent-configuration, .async-search, .security-7, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:23:50.972       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .security-7, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .tasks, .apm-agent-configuration, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:23:51.118       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_7.12.0_001, .async-search, .apm-custom-link, .security-7, .tasks, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:23:53.027       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-agent-configuration, .tasks, .kibana_task_manager_7.12.0_001, .async-search, .kibana_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:23:53.245       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .apm-custom-link, .kibana_7.12.0_001, .security-7, .kibana_task_manager_7.12.0_001, .async-search, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:23:54.265       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .security-7, .apm-agent-configuration, .tasks, .kibana_7.12.0_001, .async-search, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:23:56.955       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .apm-agent-configuration, .kibana_7.12.0_001, .async-search, .kibana_task_manager_7.12.0_001, .security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:23:57.423       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-custom-link, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .tasks, .security-7, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:23:59.167       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .apm-custom-link, .security-7, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:24:00.379       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .apm-custom-link, .tasks, .kibana_task_manager_7.12.0_001, .security-7, .kibana_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:24:01.431       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .async-search, .security-7, .apm-custom-link, .kibana_7.12.0_001, .tasks, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:24:13.165       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-agent-configuration, .apm-custom-link, .security-7, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:24:17.534       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .apm-custom-link, .tasks, .security-7, .kibana_task_manager_7.12.0_001, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:24:22.839       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:177
				MESSAGE:异常ID:1a76b6b0-7e37-4c0e-90ff-1650f0d95b1d, 请求地址:http://192.168.2.217:8300/cloudnormal/backup/backupFile
cn.newdt.commons.exception.CustomException: 从备份存储获取文件流失败
	at cn.newdt.cloud.service.impl.BackupServiceImpl.downloadBackupFile(BackupServiceImpl.java:630)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$FastClassBySpringCGLIB$$f56eef78.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$EnhancerBySpringCGLIB$$d2a53694.downloadBackupFile(<generated>)
	at cn.newdt.cloud.web.BackupController.downloadBackupFile(BackupController.java:280)
	at cn.newdt.cloud.web.BackupController$$FastClassBySpringCGLIB$$d3348832.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.BackupController$$EnhancerBySpringCGLIB$$3d42a3ec.downloadBackupFile(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:24:23.254       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .tasks, .apm-custom-link, .security-7, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:24:23.254       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-agent-configuration, .tasks, .security-7, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:24:25.631       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .async-search, .apm-custom-link, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:24:25.639       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-agent-configuration, .security-7, .kibana_task_manager_7.12.0_001, .apm-custom-link, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:27:15.215       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 18:27:15.316       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 18:27:15.347       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .security-7, .kibana_7.12.0_001, .apm-agent-configuration, .tasks, .async-search, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:27:15.361       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .security-7, .kibana_7.12.0_001, .apm-custom-link, .kibana_task_manager_7.12.0_001, .tasks, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:27:15.432       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 18:27:15.684       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 18:27:39.840       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:177
				MESSAGE:异常ID:ded7dc57-5c5e-475a-92c3-d16ea07d06b7, 请求地址:http://192.168.2.217:8300/cloudnormal/backup/backupFile
cn.newdt.commons.exception.CustomException: 从备份存储获取文件流失败
	at cn.newdt.cloud.service.impl.BackupServiceImpl.downloadBackupFile(BackupServiceImpl.java:630)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$FastClassBySpringCGLIB$$f56eef78.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$EnhancerBySpringCGLIB$$d2a53694.downloadBackupFile(<generated>)
	at cn.newdt.cloud.web.BackupController.downloadBackupFile(BackupController.java:280)
	at cn.newdt.cloud.web.BackupController$$FastClassBySpringCGLIB$$d3348832.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.BackupController$$EnhancerBySpringCGLIB$$3d42a3ec.downloadBackupFile(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:28:29.834       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:177
				MESSAGE:异常ID:1737c650-9af5-4788-b139-cb01529fb55c, 请求地址:http://192.168.2.217:8300/cloudnormal/backup/backupFile
cn.newdt.commons.exception.CustomException: 从备份存储获取文件流失败
	at cn.newdt.cloud.service.impl.BackupServiceImpl.downloadBackupFile(BackupServiceImpl.java:630)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$FastClassBySpringCGLIB$$f56eef78.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$EnhancerBySpringCGLIB$$d2a53694.downloadBackupFile(<generated>)
	at cn.newdt.cloud.web.BackupController.downloadBackupFile(BackupController.java:280)
	at cn.newdt.cloud.web.BackupController$$FastClassBySpringCGLIB$$d3348832.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.BackupController$$EnhancerBySpringCGLIB$$3d42a3ec.downloadBackupFile(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:29:29.597       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:177
				MESSAGE:异常ID:8dcee5d2-b108-4fd5-843e-0e2f563d8e32, 请求地址:http://192.168.2.217:8300/cloudnormal/backup/backupFile
cn.newdt.commons.exception.CustomException: 从备份存储获取文件流失败
	at cn.newdt.cloud.service.impl.BackupServiceImpl.downloadBackupFile(BackupServiceImpl.java:630)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$FastClassBySpringCGLIB$$f56eef78.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$EnhancerBySpringCGLIB$$d2a53694.downloadBackupFile(<generated>)
	at cn.newdt.cloud.web.BackupController.downloadBackupFile(BackupController.java:280)
	at cn.newdt.cloud.web.BackupController$$FastClassBySpringCGLIB$$d3348832.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.BackupController$$EnhancerBySpringCGLIB$$3d42a3ec.downloadBackupFile(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:31:18.740       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:177
				MESSAGE:异常ID:12399f26-7a6f-402b-a8ce-9222f5ca3e71, 请求地址:http://192.168.2.217:8300/cloudnormal/backup/backupFile
cn.newdt.commons.exception.CustomException: 从备份存储获取文件流失败
	at cn.newdt.cloud.service.impl.BackupServiceImpl.downloadBackupFile(BackupServiceImpl.java:630)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$FastClassBySpringCGLIB$$f56eef78.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.impl.BackupServiceImpl$$EnhancerBySpringCGLIB$$d2a53694.downloadBackupFile(<generated>)
	at cn.newdt.cloud.web.BackupController.downloadBackupFile(BackupController.java:280)
	at cn.newdt.cloud.web.BackupController$$FastClassBySpringCGLIB$$d3348832.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.BackupController$$EnhancerBySpringCGLIB$$3d42a3ec.downloadBackupFile(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:48:20.619       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-25 18:48:20.619       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-25 18:48:20.621       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-25 18:48:20.636       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-25 18:48:41.514       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-25 18:48:41.522       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-25 18:49:42.749       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-25 18:49:47.911       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 18:49:47.914       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-25 18:49:48.784       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-25 18:49:48.830       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-25 18:49:51.219       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 18:49:51.224       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-25 18:59:24.401       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .async-search, .tasks, .security-7, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 18:59:59.615       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-agent-configuration, .tasks, .kibana_7.12.0_001, .security-7, .apm-custom-link, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 19:00:15.509       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .tasks, .apm-custom-link, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .async-search, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 19:08:34.013       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .tasks, .kibana_7.12.0_001, .async-search, .security-7, .kibana_task_manager_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 19:16:53.184       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-agent-configuration, .tasks, .kibana_task_manager_7.12.0_001, .async-search, .kibana_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 19:16:53.184       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .tasks, .async-search, .apm-agent-configuration, .kibana_7.12.0_001, .apm-custom-link, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 19:16:53.308       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 19:16:53.347       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 19:16:53.391       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 19:16:53.698       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-25 19:17:00.507       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-agent-configuration, .async-search, .kibana_7.12.0_001, .tasks, .kibana_task_manager_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 19:17:20.136       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .kibana_task_manager_7.12.0_001, .security-7, .kibana_7.12.0_001, .apm-custom-link, .apm-agent-configuration, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 19:17:27.345       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .tasks, .kibana_7.12.0_001, .apm-custom-link, .security-7, .kibana_task_manager_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 19:18:20.067       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.scheduling.quartz.LocalDataSourceJobStore  LINE:3411
				MESSAGE:This scheduler instance (LAPTOP-OUTBTI751753440541541) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior.

2025-07-25 19:23:53.805       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .kibana_7.12.0_001, .apm-agent-configuration, .security-7, .async-search, .kibana_task_manager_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-25 19:24:27.279       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-25 19:24:27.280       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-25 19:24:27.280       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-25 19:24:27.284       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end
