package cn.newdt.cloud.repository;

import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.vo.MetricVO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.*;
import java.util.regex.Pattern;

public interface MetricClient {
    // metric label constant
    String CLUSTER_CPU_USAGE="cluster_cpu_usage";
    String CLUSTER_CPU_CAPACITY="cluster_cpu_capacity";
    String CLUSTER_MEMORY_USAGE="cluster_memory_usage";
    String CLUSTER_MEMORY_CAPACITY="cluster_memory_capacity";
    String CLUSTER_STORAGE_USAGE="cluster_storage_usage";
    String CLUSTER_STORAGE_CAPACITY="cluster_storage_capacity";
    String PVC_INFO ="pv_info"; // 这里的pv_info实际上是pvc_info，常量名称是对的。由于历史原因，cloud_monitor_metric表中记录为pv_info，值沿用pv_info。
    String PVC_USAGE="pvc_usage";
    String PVC_CAP="pvc_cap";
    String MEMORY_CAP="memory_cap";
    String MEMORY_USAGE="memory_usage";
    String CPU_USAGE="cpu_usage";
    String CPU_CAPACITY="cpu_capacity";
    String NODE_CPU_CAPACITY="node_cpu_capacity";
    String NODE_CPU_REQUEST="node_cpu_request";
    String NODE_MEM_CAPACITY="node_mem_capacity";
    String NODE_MEM_REQUEST="node_mem_request";
    String NODE_DISK_ALAIL = "node_disk_avail";
    String NODE_DISK_CAP = "node_disk_cap";
    String NODE_CPU_MEM_CAP = "node_cpu_mem_capacity";
    String NODE_VOLUME_REQUEST = "node_disk_request";
    String PVC_INFO_2 = "pvc_info";
    String CPU_USAGE_CONTAINER = "cpu_usage_container";
    String MEMORY_USAGE_CONTAINER = "memory_usage_container";
    String TENANT_CPU_CAP = "cpu_capacity_namespace";
    String TENANT_MEM_CAP = "memory_capacity_namespace";
    String TENANT_STORAGE_CAP = "storage_capacity_namespace";
    /**
     * 节点每个核心(非idle)的使用率汇总
     */
    String NODE_CPU_USAGE = "node_cpu_usage";
    String NODE_MEM_USAGE = "node_mem_usage";

    String getMetricSrcLabel(int kubeId, String label);

    MetricVO queryDisk(long timestamp, int kubeId, Set<String> podNames);

    MetricVO queryMemory(long timestamp, String kind, int kubeId, Set<String> podNames);

    MetricVO queryCpu(long timestamp, String kind, int kubeId, Set<String> podNames);

    /**
     * 时点查询
     */
    @Deprecated
    Response<VectorMetric> queryInstant(String query, long timestamp, int kubeId);

    Double queryInstantV2(String query, long timestamp, int kubeId);

    List<MetricVO.ValueVO> queryCpuByPods(long timestamp, Set<String> podNames, int kubeId, String metricName);

    List<MetricVO.ValueVO> queryMemoryByPods(long timestamp, Set<String> podNames, int kubeId, String metricName);

    @Deprecated
    List<MetricVO.ValueVO> queryDiskByPods(long timestamp, Set<String> podNames, int kubeId, String metricName);

    /**
     * 根据pod名称筛选对应的pvc指标
     * @return Map key-pvcName, value-podName
     */
    List<Map<String, Object>> queryPvcsByPods(long timestamp, Set<String> podNames, int kubeId, String metricName);

    /**
     * 根据pvc名称筛选对应的pv指标
     */
    List<MetricVO.ValueVO> queryPvsByPvc(long timestamp, Set<String> objects, int kubeId, String pvcUsage);
    /**
     * 范围查询，指定精度
     * @param query PromQL 查询表达式
     * @param start 开始时间戳
     * @param end 结束时间戳 unix_timestamp
     * @param step 精度,单位:s
     * @param kubeId
     */
    @Deprecated
    Response<MatrixMetric> queryRange(String query, long start, long end, int step, int kubeId);

    Object queryRangeCpuByPods(long start, long end, int step, Set<String> podNames, String kind, int kubeId);

    Object queryRangeMemoryByPods(long start, long end, int step, Set<String> podNames, String kind, int kubeId);

    Object queryRangeMetricPVCOfPod(long start, long end, int kubeId, Set<String> queryPodNames, Pattern pattern);

    Object queryRangeCpuMemoryPerPod(long start, long end, int step, String kind, int kubeId, Set<String> podNameFilter, String metricLabel);

    /**
     * 查询应用所有实例挂载的数据存储的使用量和
     */
    Object queryRangeDiskByPodsData(long start, long end, int step, Set<String> podNames, String kind, int kubeId, CloudApp app);
    /**
     * 查询应用所有实例挂载的备份存储的使用量和
     */
    Object queryRangeDiskByPodsBackup(long start, long end, int step, Set<String> podNames, String kind, int kubeId, CloudApp app);
    /**
     * 查询应用所有实例挂载的所有持久存储的使用量和
     */
    Object queryRangeDiskByPods(long start, long end, int step, Set<String> podNames, String kind, int kubeId);
    /**
     * 查询应用所有实例挂载的所有持久存储的使用量，按 pod 返回
     */
    Object queryRangeDiskPerPod(long start, long end, int step, String kind, Integer kubeId, HashSet<String> strings, String metricType, Pattern pattern);

    MetricVO queryNodeCpu(Integer kubeId, String nodeName, long timestamp);

    MetricVO queryNodeMem(Integer kubeId, String nodeName, long timestamp);

    Map<String, Double> queryClusterMetric(int kubeId, Set<String> nodeNames);

    /**
     * 检查给定Prometheus URL的健康状态
     */
    boolean checkHealth(int kubeId);
    /**
     * 检查给定集群ID下Prometheus的健康状态
     */
    boolean checkHealth(String url);

    /**
     * @return 查询节点宿主机磁盘已使用容量
     */
    List<MetricVO.ValueVO> queryNodeDiskAvail(int kubeId, List<String> nullableNodeNames);

    /**
     * @return 查询节点宿主机磁盘总容量
     */
    List<MetricVO.ValueVO> queryNodeDiskCap(int kubeId, List<String> nullableNodeNames);

    List<MetricVO.ValueVO> queryNodesCpuMemoryCap(int kubeId, long timestamp);

    List<MetricVO.ValueVO> queryNodesCpuMemoryUsage(int kubeId, long timestamp);

    /**
     * @return 查询指定集群节点上所有pvc容量指标：kubelet_volume_stats_capacity_bytes
     */
    List<MetricVO.ValueVO> queryNodeVolumeRequest(int kubeId, Collection<String> nodeNames);

    /**
     * @return 查询指定类型的所有pvc名称: kube_persistentvolumeclaim_info{storageclass=~"%s"}
     */
    List<MetricVO.ValueVO> queryVolumeByStorageClass(int kubeId, String includeStorageClass, String excludeStorageClass);

    Object queryRangeOfContainer(Integer kubeId, String podName, long start, long end, int step, String metricLabel, String kind);

    Response queryMetric(String metric, long epochSecond, String server);


    @Getter
    @Setter
    @ToString
    class Response<T extends Metric>{
        private String status;
        private Data<T> data;
    }
    @Getter @Setter
    class Data<T extends Metric>{
        private String resultType;
        private List<T> result;
    }
    @Getter @Setter
    class Metric{
        private Map<String, Object> metric; // 指标标签
    }
    @Getter @Setter
    class VectorMetric extends Metric{
        private Object[] value; // index:0 时间戳, index:1 指标值
    }
    @Getter @Setter
    class MatrixMetric extends Metric{
        private Object[][] values; // VectorMetric.value的时间序列
    }
}
