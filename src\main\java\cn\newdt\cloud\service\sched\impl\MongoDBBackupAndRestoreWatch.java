package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.StatusConstant;
import cn.newdt.cloud.domain.BackupHis;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.RestoreHis;
import cn.newdt.cloud.domain.cr.MongoDBCommunity;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.mapper.BackupMapper;
import cn.newdt.cloud.mapper.RestoreMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.CloudAppService;
import cn.newdt.cloud.service.KubeClientService;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.utils.BackupUtil;
import cn.newdt.cloud.utils.JsonUtil;
import cn.newdt.cloud.utils.KubeClientUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.DatasourceConstant.CLOUD_BACKUP_HIS;
import static cn.newdt.cloud.constant.DatasourceConstant.CLOUD_RESTORE_HIS;
import static cn.newdt.cloud.constant.DatasourceConstant.SCHEMA;

@Slf4j
public class MongoDBBackupAndRestoreWatch implements OpsPostProcessor<MongoDBCommunity> {
//    @Value("${backupTimemout.mongodb:1440}")
//    private String backupTimeout;
    @Autowired
    private CloudAppService cloudAppService;
    @Autowired
    private KubeClientService kubeClientService;
    @Autowired
    private BackupMapper backupMapper;
    @Autowired
    private RestoreMapper restoreMapper;
    @Autowired
    private BackupUtil backupUtil;

    /**
     * 备份回调逻辑
     * <p>1.判断Pod备份目录下是否存在指定库备份文件，不存在则备份失败。每个库会有 *.bson、*.metadata.json 两个文件
     * <p>2.备份成功上传到ftp服务器
     * 恢复回调逻辑
     * @param triggerHis 触发器历史
     * @return 回调结果
     * @throws Exception
     */
    @Override
    public OpsResultDTO postProcess(TriggerHis triggerHis) throws Exception {
        //创建返回结果
        OpsResultDTO.Builder result = OpsResultDTO.builder().stopJob(false);
        //根据appId获取app对象
        Map<String, String> jobDataMap = triggerHis.returnMergedJobDataMap();
        String appId = jobDataMap.get("appId");
        CloudApp app = cloudAppService.get(Integer.valueOf(appId));
        //获取操作类型
        String triggerName = triggerHis.getTriggerName();
        String handType = triggerName.substring(0, triggerName.lastIndexOf("_"));
        //创建k8s客户端
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        if (ActionEnum.BACKUP.toString().equalsIgnoreCase(handType)) {
            // 备份节点
            String backupPodName = jobDataMap.get("backupPodName");
            // 备份文件名
            String backupFilename = jobDataMap.get("backupFilename");
            // 总collection数量
//            String allCollectionsNumStr = jobDataMap.get("allCollectionsNum");
//            Integer allCollectionsNum = Integer.valueOf(allCollectionsNumStr);
            int allCollectionsNum =  0;


            //获取超时时间
            Integer backupTimeOut = backupUtil.getBackupTimeOut(app.getId());
            //查询备份历史
            Integer backupHisId = Integer.parseInt(jobDataMap.get("backupHisId"));
            BackupHis backupHis = backupMapper.getBackupHisById(SCHEMA, CLOUD_BACKUP_HIS, backupHisId);
            // 备份开始时间和当前时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String startDateStr = jobDataMap.get("startDateStr");
            Date startDate = sdf.parse(startDateStr);
            Date nowDate = new Date();
            // 备份宿主机目录
            String backupNodePath = jobDataMap.get("backupNodePath");
            // 备份宿主机ip
            String backupIp = jobDataMap.get("backupIp");
            // 备份操作记录Id
            Integer changeId = Integer.valueOf(jobDataMap.get("changeId"));
            // 备份数据库集合
            List<String> dbs = JSONArray.parseArray(jobDataMap.get("dbs"), String.class);
            // pod中的备份目录 /mnt/share/mongodb/replicaset/lianzb-m/monrpbak/20250605163258
            String backupPodPath = jobDataMap.get("backupPodPath");
            // ftp服务器上的mongodb备份目录
            String backupFtpPath = jobDataMap.get("backupFtpPath");
            // 记录备份信息
            JSONObject messageObj = new JSONObject();
            messageObj.put("backupIp", backupIp);
            messageObj.put("backupNodePath", backupNodePath);
            messageObj.put("backupPodPath", backupPodPath);
            messageObj.put("dbNames", dbs);
            messageObj.put("backupFtpPath", backupFtpPath);
            messageObj.put("backupFilename", backupFilename);
            boolean isTimeout = backupUtil.checkBackupAndRestoreTimeout(startDate, nowDate, backupTimeOut);
            if (isTimeout) {
                // 补全备份历史
                messageObj.put("msg", "备份超时！当前操作：上传备份文件！");
                backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "备份超时！当前操作：上传备份文件！");
                result.stopJob(true).msg("备份超时！当前操作：上传备份文件！").status(StatusConstant.FAIL);
                OpsResultDTO dto = result.build();
                cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                return dto;
            } else {
                result.stopJob(false).msg("备份中...").status(StatusConstant.RUNNING);
            }

            // 查询备份结果
            try {
                String backupResultStr = kubeClient.execCmd(app.getNamespace(), backupPodName,
                        AppKind.MongoDB.getContainerName(), "sh", "-c", "test -f /tmp/"
                                + startDateStr + "_backup.log"
                                + " && cat /tmp/" + startDateStr + "_backup.log || echo \"File is not exist\"");
                if (backupResultStr.contains("Failed")) {
                    // 补全备份历史
                    messageObj.put("msg", "备份失败！");
                    backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "备份失败！");
                    result.stopJob(true).msg("备份失败！").status(StatusConstant.FAIL);
                    OpsResultDTO dto = result.build();
                    cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                    return dto;
                } else if (backupResultStr.contains("File is exist")){
                    result.stopJob(false).msg("备份中...").status(StatusConstant.RUNNING);
                } else {
                    allCollectionsNum = Integer.valueOf(backupResultStr.replace("\n", ""));

                    // 遍历寻找备份文件
                    ArrayList<String> backupFileList = new ArrayList<>();
                    for (String everyDb : dbs) {
                        // 寻找备份文件命令
                        String findDumpScript = "ls " + backupPodPath + "/" + everyDb;
                        String backupSubFileName = kubeClient.execCmd(app.getNamespace(), backupPodName, AppKind.MongoDB.getContainerName(), "sh", "-c", findDumpScript);
                        // 该字段为文件名称字段
                        // 添加到所有备份文件的list
                        backupFileList.addAll(Arrays.stream(backupSubFileName.split("\n"))
                                .map(name -> everyDb.concat("/").concat(name))
                                .collect(Collectors.toList()));
                    }

                    log.info("应该有文件数量：" + (allCollectionsNum * 2 + 4) + "实际备份后文件数量" + backupFileList.size());

                    // 判断备份文件数是否正确：总集合数 * 2 + 4,总集合数不包括admin库，admin库会备份出4个文件，所以最后+4
                    //开启慢日志查询后，初始情况可能备份文件中没有  system.profile  集合的备份文件信息
                    if ((allCollectionsNum * 2 + 4) == backupFileList.size() || (allCollectionsNum * 2 + 2) == backupFileList.size()) {
                        // 取消挂载
                        String umountCmd = "umount -lf /mnt/share";
                        try {
                            kubeClient.execCmd(app.getNamespace(), backupPodName, AppKind.MongoDB.getContainerName(), "sh", "-c", umountCmd);
                        } catch (Exception e) {
                        }
                        messageObj.put("msg", "备份成功！");
                        result.stopJob(true).msg("备份成功！").status(StatusConstant.SUCCESS);
                        long dataSize = KubeClientUtil.dfUsage(kubeClient, backupHis.getPodName(), AppKind.MongoDB.getContainerName(), app.getNamespace(), CloudAppConstant.MONGODB_RS_DATA_MOUNT_PATH);
                        messageObj.put("dataSize", Collections.singletonMap(backupHis.getPodName(), dataSize));
                        // 补全备份历史
                        backupHis.setMessage(messageObj.toJSONString());
                        backupUtil.backupReturn(backupHis, changeId, StatusConstant.SUCCESS, backupFilename, "备份成功！");
                    }
                }
            } catch (Exception e) {
                log.error("备份失败！", e);
                messageObj.put("msg", "备份失败！");
                backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "备份失败！");
                result.stopJob(true).msg("备份失败！").status(StatusConstant.FAIL);
                OpsResultDTO dto = result.build();
                cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                return dto;
            }
        } else if (ActionEnum.RESTORE.toString().equalsIgnoreCase(handType)) {
            //恢复历史id
            String restoreHisId = jobDataMap.get("restoreHisId");
            Integer restoreId = Integer.valueOf(restoreHisId);
            Integer.valueOf(restoreHisId);
            //恢复pod名称
            String restorePodName = jobDataMap.get("restorePodName");
            String changeIdStr = jobDataMap.get("changeId");
            Integer changeId = Integer.valueOf(changeIdStr);

            //获取恢复历史
            RestoreHis restoreHisById = restoreMapper.getRestoreHisById(SCHEMA, CLOUD_RESTORE_HIS, restoreId);
            //获取恢复结果
            String restoreResult = "cat restore.log |grep \"document(s) failed to restore\"";
            try {
                String restoreResultStr = kubeClient.execCmd(app.getNamespace(), restorePodName, AppKind.MongoDB.getContainerName(), "sh", "-c", restoreResult);
                List resultList = Arrays.asList(restoreResultStr.split(" "));
                List<Integer> resIndexList = new ArrayList<>();
                for (int i = 0; i < resultList.size(); i++) {
                    if (resultList.get(i).equals("failed")) {
                        resIndexList.add(i - 2);
                    }
                }
                //获取失败数量
                Integer failNum = 0;
                for (Integer index : resIndexList) {
                    failNum += Integer.valueOf(resultList.get(index).toString());
                }
                if (failNum == 0) {
                    //成功
                    backupUtil.restoreReturn(restoreHisById, changeId, "恢复成功！", StatusConstant.SUCCESS);
                    result.stopJob(true).msg("恢复成功！").status(StatusConstant.SUCCESS);
                    OpsResultDTO dto = result.build();
                    if (dto.getStopJob()) {
                        cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                    }
                    return dto;
                } else {
                    //失败
                    backupUtil.restoreReturn(restoreHisById, changeId, "MongoDB恢复失败，信息为：" + failNum, StatusConstant.FAIL);
                    result.stopJob(true).msg("MongoDB恢复失败，失败数量为为：" + failNum).status(StatusConstant.FAIL);
                    OpsResultDTO dto = result.build();
                    if (dto.getStopJob()) {
                        cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                    }
                    return dto;
                }
            } catch (Exception e) {
                backupUtil.restoreReturn(restoreHisById, changeId, "MongoDB恢复失败，信息为：" + e.getLocalizedMessage(), StatusConstant.FAIL);
                result.stopJob(true).msg("MongoDB恢复失败，信息为：" + e.getLocalizedMessage()).status(StatusConstant.FAIL);
                OpsResultDTO dto = result.build();
                if (dto.getStopJob()) {
                    cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                }
                return dto;
            }
        }

        if (result.isStopped()) {
            cloudAppService.handleWatchResult(app.getId(), result.isSuccessful());
        }
        return result.build();
    }

}