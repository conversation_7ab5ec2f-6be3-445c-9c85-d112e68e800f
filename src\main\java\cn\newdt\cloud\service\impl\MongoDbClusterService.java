package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.common.OpLogContext;
import cn.newdt.cloud.config.CloudRequestContext;
import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.domain.cr.MongoDBCluster;
import cn.newdt.cloud.dto.*;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.cni.BocCniHelperFactory;
import cn.newdt.cloud.service.cni.BocRestfulHelper;
import cn.newdt.cloud.service.cni.CniConfig;
import cn.newdt.cloud.service.csi.CSIUtil;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.service.sched.impl.*;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.*;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.bean.MysqlParamRules;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.SymmetricEncryptionUtil;
import cn.newdt.commons.utils.UserUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import io.fabric8.kubernetes.api.model.NodeAffinity;
import io.fabric8.kubernetes.api.model.Toleration;
import io.fabric8.kubernetes.client.CustomResource;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.quartz.SchedulerException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.security.SecureRandom;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgCategory.PARAM_FORMULA;
import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgCategory.PARAM_PROHIBIT;
import static cn.newdt.cloud.constant.ImageKindEnum.*;

@Slf4j
@Service
public class MongoDbClusterService extends DefaultAppKindService<MongoDBCluster> implements ServiceManageOperation {

    @Value("${autoManagement:true}")
    protected boolean autoManagement;

    @Autowired
    private AppOperationHandler appOperationHandler;
    @Autowired
    private KubeClientService kubeClientService;

    @Autowired
    private CloudDatabaseUserService dbUserService;

    @Autowired
    private NetworkService networkService;

    @Autowired
    private IpPoolConfigService ipPoolConfigService;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private CloudAppConfigService appConfigService;

    @Autowired
    private CloudDbParamTemplateService cloudDbParamTemplateService;

    @Autowired
    private ResourceManagerService resourceManagerService;

    @Autowired
    private OperationUtil operationUtil;

    @Autowired
    private MongoDBPodService mongoDBPodService;

    public Map<String, String> getMongoDbRoleMap(KubeClient kubeClient, String namespace, String crName, String podName) {
        //查询主从
        Map<String, String> podNameRoleMap = new HashMap<>();
        try {
            String pbmStr = kubeClient.execCmd(namespace, podName, "pbm", "sh", "-c", "pbm status");
            String[] pbmArr = pbmStr.split("\n");
            for (String everypod : pbmArr) {
                if (-1 != everypod.indexOf("[P]") && -1 != everypod.indexOf("rs")) {
                    //主
                    podNameRoleMap.put(everypod, "primary");
                } else if (-1 != everypod.indexOf("[S]") && -1 != everypod.indexOf("rs")) {
                    //从
                    podNameRoleMap.put(everypod, "secondary");
                }
            }
            //根据ip赋予角色
            return podNameRoleMap;
        } catch (Exception e) {
            log.error("查询MongoDBCluster的pbm状态失败！");
            return podNameRoleMap;
        }
    }


    //=============================运维操作==================================

    @Override
    public AppKind getKind() {
        return AppKind.MongoDB_Cluster;
    }

    @Override
    public Class<? extends OpsPostProcessor> getProcessorClass(ActionEnum action) {
        switch (action) {
            case CREATE:
                return MongoDbClusterInstallWatch.class;
            case UPDATE:
                return MongoDbClusterWatch.class;
            case SCALE_OUT:
                return MongoDbClusterScaleWatch.class;
            case SCALE_IN:
                return MongoDbClusterScaleWatch.class;
            case MODIFY_PARAM:
                return MongoDBClusterModifyParamsWatch.class;
            case SWITCH_MASTER:
                return MongoDBClusterSwitchMasterWatch.class;
            case CREATE_SERVICE:
            case UPDATE_SERVICE:
            case DELETE_SERVICE:
                return MongoDBClusterSvcWatch.class;
            default:
                return super.getProcessorClass(action);
        }
    }

    @Override
    public boolean nodePolicy() {
        return false;
    }

    @Override
    protected boolean supportIPAM(CloudAppVO app) {
//        return !CloudAppConstant.K8sCNIType.CALICO.equals(app.getCniType());
        return false;
    }

    @Override
    public void update(Integer id, OverrideSpec overrideSpec) throws Exception {
        if (overrideSpec instanceof MongoDbClusterService.MongoDBClusterOverrideSpec) {
            MongoDbClusterResourceDTO patch = new MongoDbClusterResourceDTO();
            patch.setDisk(overrideSpec.getDisk());
            patch.setMemory(overrideSpec.getMemory());
            patch.setCpu(overrideSpec.getCpu());
            patch.setBackupDisk(overrideSpec.getBackupDisk());
            patch.setConfigServersCpu(((MongoDbClusterService.MongoDBClusterOverrideSpec) overrideSpec).getConfigServersCpu());
            patch.setConfigServersMemory(((MongoDbClusterService.MongoDBClusterOverrideSpec) overrideSpec).getConfigServersMemory());
            patch.setConfigServersDisk(((MongoDbClusterService.MongoDBClusterOverrideSpec) overrideSpec).getConfigServersDisk());
            patch.setConfigServersBackupDisk(((MongoDbClusterService.MongoDBClusterOverrideSpec) overrideSpec).getConfigServersBackupDisk());
            patch.setRouterServersCpu(((MongoDbClusterService.MongoDBClusterOverrideSpec) overrideSpec).getRouterServersCpu());
            patch.setRouterServersMemory(((MongoDbClusterService.MongoDBClusterOverrideSpec) overrideSpec).getRouterServersMemory());
            patch.setRouterServersDisk(((MongoDbClusterService.MongoDBClusterOverrideSpec) overrideSpec).getRouterServersDisk());
            patch.setAppId(id);
            patch.setId(id);
            update(patch);
        }
    }

    @Override
    public void update(ResourceDTO updatable) throws Exception {
        MongoDbClusterResourceDTO updatableMg = (MongoDbClusterResourceDTO) updatable;
        Consumer<MongoDBCluster> cpuMemMod = expCr -> {
            expCr.getSpec().getShardServers().setCpu(updatableMg.getCpu());
            expCr.getSpec().getShardServers().setMemory(updatableMg.getMemory());
            expCr.getSpec().getConfigServers().setCpu(updatableMg.getConfigServersCpu());
            expCr.getSpec().getConfigServers().setMemory(updatableMg.getConfigServersMemory());
            expCr.getSpec().getRouterServers().setCpu(updatableMg.getRouterServersCpu());
            expCr.getSpec().getRouterServers().setMemory(updatableMg.getRouterServersMemory());
        };
        Consumer<MongoDBCluster> storageMod = expCr -> {
            MongoDBCluster.MongoDBClusterSpec spec = expCr.getSpec();
            if (MetricUtil.lessAndNotZero(spec.getShardServers().getDataStorage().getSize(), updatableMg.getDisk()))
                spec.getShardServers().getDataStorage().setSize(updatableMg.getDisk());
//            if (MetricUtil.lessAndNotZero(spec.getShardServers().getBackupStorage().getSize(), updatableMg.getBackupDisk()))
//                spec.getShardServers().getBackupStorage().setSize(updatableMg.getBackupDisk());
            if (MetricUtil.lessAndNotZero(spec.getConfigServers().getDataStorage().getSize(), updatableMg.getConfigServersDisk()))
                spec.getConfigServers().getDataStorage().setSize(updatableMg.getConfigServersDisk());
//            if (MetricUtil.lessAndNotZero(spec.getConfigServers().getBackupStorage().getSize(), updatableMg.getConfigServersBackupDisk()))
//                spec.getConfigServers().getBackupStorage().setSize(updatableMg.getConfigServersBackupDisk());
            if (MetricUtil.lessAndNotZero(spec.getRouterServers().getDataStorage().getSize(), updatableMg.getRouterServersDisk()))
                spec.getRouterServers().getDataStorage().setSize(updatableMg.getRouterServersDisk());
        };

        appOperationHandler.handleUpdate(updatableMg, cpuMemMod, this, MongoDBCluster.class, storageMod);
    }

    @Override
    public MongoDBCluster doInstall(CloudAppVO vo, List ips) throws Exception {
        MongoDBClusterVO mongoDBClusterVO = (MongoDBClusterVO) vo;
        //获取app
        CloudApp app = appService.get(mongoDBClusterVO.getId());
        app.setStorageClassName(StringUtils.isEmpty(mongoDBClusterVO.getHostpathRoot()) ? mongoDBClusterVO.getCsiType() : null);
//        app.setBackupStorageclassname(StringUtils.isEmpty(mongoDBClusterVO.getBackupHostpathRoot())?mongoDBClusterVO.getBackupCsiType() : "");
        appService.update(app);
        KubeClient kubeClient = kubeClientService.get(mongoDBClusterVO.getKubeId());

        // 1.获取mongodb、exporter、pbm、filebeat、ftp的镜像信息
        Map<ImageKindEnum, String> imageManifest = appConfigService.getImageManifest(getKind(), mongoDBClusterVO.getVersion());
        String mongoDbImage = imageManifest.get(MainImage);
        String exporterImage = imageManifest.get(Exporter);
        String ftpImage = imageManifest.get(FTP);
        String pbmImage = imageManifest.get(MongoDBCluster_PBM);
        String filebeatImage = imageManifest.get(Filebeat);
        String mountImage = imageManifest.get(Mount);

        // 2.构建routerServers、configServers、shardServers的ipList
        List<String> allIpList = networkService.allocateIp(app, mongoDBClusterVO.getConfigServersMembers() + mongoDBClusterVO.getRouterServersMembers() + (mongoDBClusterVO.getMasterSize() * (mongoDBClusterVO.getSpareSize() + 1)), getIpOwnerKind(), getIpReservationTarget());
        //分割ip
        List<String> routerServersIpList = allIpList.subList(0, mongoDBClusterVO.getRouterServersMembers());
        List<String> configServersIpList = allIpList.subList(mongoDBClusterVO.getRouterServersMembers(), mongoDBClusterVO.getRouterServersMembers() + mongoDBClusterVO.getConfigServersMembers());
        List<String> shardServersIpList = allIpList.subList(mongoDBClusterVO.getRouterServersMembers() + mongoDBClusterVO.getConfigServersMembers(), allIpList.size());

        // 3.创建管理员secret，账号：admin   密码：Passw0rd
        //创建互信的字符串
        byte[] bytes = new byte[512];
        new SecureRandom().nextBytes(bytes);
        String mongodbToken = new String(Base64.encodeBase64(bytes));
        MongoDBCluster.MongoDBClusterSpec.Secret secret = new MongoDBCluster.MongoDBClusterSpec.Secret();
        //创建secret
        String adminUserKey = "username";
        String adminPwdKey = "password";
        //判断是否设置密码
        try {
            secret.setUsernameKey(adminUserKey);
            secret.setPasswordKey(adminPwdKey);
            secret.setSecretName(getSecretName(mongoDBClusterVO.getCrName()));
            secret.setKeyfileKey("mongotoken");
            //StringData
            Map<String, String> mongodbclusterSecretData = new HashMap<>();
            mongodbclusterSecretData.put(adminUserKey, CloudAppConstant.UsernameAndPassword.mongoDBUsername);
            mongodbclusterSecretData.put(adminPwdKey, CloudAppConstant.UsernameAndPassword.mongoDBPassword);
            mongodbclusterSecretData.put("mongotoken", mongodbToken);
            //创建secret
            kubeClient.createSecret(mongoDBClusterVO.getNamespace(), getSecretName(mongoDBClusterVO.getCrName()), null, mongodbclusterSecretData);
            dbUserService.createUser(mongoDBClusterVO.getId(), CloudAppConstant.UsernameAndPassword.mongoDBUsername, CloudAppConstant.UsernameAndPassword.mongoDBPassword, "admin");
        } catch (Exception e) {
            throw new CustomException(600, "创建secret失败！错误信息：" + e.getMessage());
        }

        // 4.构建cr
        // todo refact
//        String configBackupStorageClass = null;
//        if (CSIUtil.isSharedType(mongoDBClusterVO.getBackupCsiType())) {
//            String finBackupStorageClassName = getBackupStorageClassName(mongoDBClusterVO.getBackupCsiType());
//            mongoDBClusterVO.setBackupStorageclassname(finBackupStorageClassName);
//        }
//        if (CSIUtil.isSharedType(mongoDBClusterVO.getConfigServersBackupCsiType())) {
//            if (mongoDBClusterVO.getConfigServersBackupCsiType().equalsIgnoreCase(mongoDBClusterVO.getBackupCsiType()))
//                configBackupStorageClass = mongoDBClusterVO.getBackupStorageclassname();
//            else configBackupStorageClass = getBackupStorageClassName(mongoDBClusterVO.getConfigServersBackupCsiType());
//        }
        MongoDBCluster.MongoDBClusterSpec.Remote remote = new MongoDBCluster.MongoDBClusterSpec.Remote();
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
            String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
            remote.setAddress(mountPath);
            remote.setType(CloudAppConstant.OperatorStorageType.NFS);
        } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
            //获取operator的namespace，因为所有operator都相同，所以统一获取mysql的operatornamespace
            String operatorConfig = sysConfigService.findOne("operator.name", "MySQL");
            String operatorNamespace = operatorConfig.split("/")[0];
            remote.setType(CloudAppConstant.StorageType.S3);
            remote.setAddress(cloudBackupStorageVO.getServer());
            remote.setBucket(cloudBackupStorageVO.getBucket());
            remote.setRegion(cloudBackupStorageVO.getRegion());
            remote.setSecret(operatorNamespace + ":backupstorage-secret");
        } else {
            throw new CustomException(600, "备份失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
        }

        MongoDBCluster.MongoDBClusterSpec spec = MongoDBCluster.MongoDBClusterSpec.builder()
                .image(mongoDbImage)
                .exporterImage(exporterImage)
                .pbm(MongoDBCluster.MongoDBClusterSpec.Pbm.builder()
                        .image(pbmImage).build())
                .filebeat(MongoDBCluster.MongoDBClusterSpec.Filebeat.builder()
                        .image(filebeatImage)
                        .configMap(getFilebeatCMName())
                        .configFile("mongodb-cluster-filebeat.yaml").build())
//                .ftp(MongoDBCluster.MongoDBClusterSpec.Ftp.builder()
//                        .image(ftpImage)
//                        .url(ftpUtil.getURL()).build())
                .routerServers(MongoDBCluster.MongoDBClusterSpec.RouterServers.builder()
                        .dataStorage(MongoDBCluster.MongoDBClusterSpec.DataStorage.builder()
                                .hostpathRoot(mongoDBClusterVO.getRouterServersHostpathRoot())
                                .storageClass(CSIUtil.getStorageClassName(mongoDBClusterVO.getRouterServersCsiType()))
                                .size(mongoDBClusterVO.getRouterServersDisk()).build())
                        .cpu(mongoDBClusterVO.getRouterServersCpu())
                        .memory(mongoDBClusterVO.getRouterServersMemory())
                        .ipList(routerServersIpList).build())
                .configServers(MongoDBCluster.MongoDBClusterSpec.ConfigServers.builder()
                        .dataStorage(MongoDBCluster.MongoDBClusterSpec.DataStorage.builder()
                                .hostpathRoot(mongoDBClusterVO.getConfigServersHostpathRoot())
                                .storageClass(CSIUtil.getStorageClassName(mongoDBClusterVO.getConfigServersCsiType()))
                                .size(mongoDBClusterVO.getConfigServersDisk()).build())
//                        .backupStorage(MongoDBCluster.MongoDBClusterSpec.BackupStorage.builder()
//                                .hostpathRoot(mongoDBClusterVO.getConfigServersBackupHostpathRoot())
//                                .storageClass(configBackupStorageClass)
//                                .size(mongoDBClusterVO.getConfigServersBackupDisk())
//                                .isShared(CSIUtil.isSharedType(mongoDBClusterVO.getConfigServersBackupCsiType()))
//                                .build())
                        .cpu(mongoDBClusterVO.getConfigServersCpu())
                        .memory(mongoDBClusterVO.getConfigServersMemory())
                        .ipList(configServersIpList).build())
                .shardServers(MongoDBCluster.MongoDBClusterSpec.ShardServers.builder()
                        .dataStorage(MongoDBCluster.MongoDBClusterSpec.DataStorage.builder()
                                .hostpathRoot(mongoDBClusterVO.getHostpathRoot())
                                .storageClass(mongoDBClusterVO.getStorageClassName())
                                .size(mongoDBClusterVO.getDisk()).build())
//                        .backupStorage(MongoDBCluster.MongoDBClusterSpec.BackupStorage.builder()
//                                .hostpathRoot(mongoDBClusterVO.getBackupHostpathRoot())
//                                .storageClass(mongoDBClusterVO.getBackupStorageclassname())
//                                .size(mongoDBClusterVO.getBackupDisk())
//                                .isShared(CSIUtil.isSharedType(mongoDBClusterVO.getBackupCsiType()))
//                                .build())
                        .cpu(mongoDBClusterVO.getCpu())
                        .memory(mongoDBClusterVO.getMemory())
                        .shardCount(mongoDBClusterVO.getMasterSize())
                        .ipList(shardServersIpList).build())
                .schedule(MongoDBCluster.MongoDBClusterSpec.Schedule.builder()
                        .antiAffinityRequired(mongoDBClusterVO.getAntiAffinityRequired())
                        .nodeAffinity(convertCRNodeAffinity(mongoDBClusterVO.getSelector(), NodeAffinity.class))
                        .tolerations(convertCRTolerations(mongoDBClusterVO.getToleration(), Toleration.class))
                        .build())
                .remote(remote)
                .build();
        if (!StringUtils.isEmpty(secret.getUsernameKey()) && !StringUtils.isEmpty(secret.getPasswordKey()) && !StringUtils.isEmpty(secret.getSecretName()) && !StringUtils.isEmpty(secret.getKeyfileKey())) {
            //放入spec
            spec.setSecret(secret);
        }

        // 5.创建filebeat cm
//        String configMapYaml = sysConfigService.findOne("operator.config", "MongoDBCluster.config");
//        String configMapName = getFilebeatCMName();
        //判断是否已经存在cm
//        ConfigMap execConfigMap = kubeClient.getConfigMap(configMapName, mongoDBClusterVO.getNamespace());
//        if (Objects.isNull(execConfigMap)) {
//            //若不存在，则创建
//            //替换yaml中的属性，大小写敏感，故不会替换掉大写的环境变量placeholder
//            Map<String, String> param = new HashMap<>();
//            param.put("namespace", mongoDBClusterVO.getNamespace());
//            param.put("name", configMapName);
//            param.put("es_host", esUtil.getEsIp() + ":" + esUtil.getEsPort());
//            param.put("es_username", esUtil.getEsUsername());
//            param.put("es_pwd", esUtil.getEsPassword());
//            param.put("es_protocol", esUtil.getProtocol());
//            configMapYaml = YamlUtil.evaluateTemplate(configMapYaml, param);
//            kubeClient.applyYaml(configMapYaml, mongoDBClusterVO.getNamespace());
//        }

        // 7.填充每个组件的参数模板
        createConfig(spec, mongoDBClusterVO);
        return new MongoDBCluster(mongoDBClusterVO.getCrName(), mongoDBClusterVO.getNamespace(), spec);
    }

    @Override
    protected void validateStorageClass(CloudAppVO app) {
        MongoDBClusterVO mongoDBClusterVO = (MongoDBClusterVO) app;
        // 设置shard的存储类型
        super.validateStorageClass(mongoDBClusterVO);
        // config和router 的csi为空时使用shard的存储类型
        if (StringUtils.isEmpty(mongoDBClusterVO.getConfigServersCsiType())) {
            mongoDBClusterVO.setConfigServersCsiType(mongoDBClusterVO.getCsiType());
        }
        if (StringUtils.isEmpty(mongoDBClusterVO.getRouterServersCsiType())) {
            mongoDBClusterVO.setRouterServersCsiType(mongoDBClusterVO.getCsiType());
        }
        if (StringUtils.isEmpty(mongoDBClusterVO.getConfigServersBackupCsiType())) {
            mongoDBClusterVO.setConfigServersBackupCsiType(mongoDBClusterVO.getBackupCsiType());
        }

        if (CSIUtil.isHostpath(mongoDBClusterVO.getConfigServersCsiType())) {
            if (StringUtils.isEmpty(mongoDBClusterVO.getConfigServersHostpathRoot())) {
                throw new CustomException(600, "未指定configserver hostpath根目录");
            }
        } else {
            mongoDBClusterVO.setConfigServersHostpathRoot(""); // go lang string init val
        }
        if (CSIUtil.isHostpath(mongoDBClusterVO.getConfigServersBackupCsiType())) {
            if (StringUtils.isEmpty(mongoDBClusterVO.getConfigServersBackupHostpathRoot())) {
                throw new CustomException(600, "未指定configserver 备份存储hostpath根目录");
            }
        } else {
            mongoDBClusterVO.setConfigServersBackupHostpathRoot(""); // go lang string init val
        }
        if (CSIUtil.isHostpath(mongoDBClusterVO.getRouterServersCsiType())) {
            if (StringUtils.isEmpty(mongoDBClusterVO.getRouterServersHostpathRoot())) {
                throw new CustomException(600, "未指定router hostpath根目录");
            }
        } else {
            mongoDBClusterVO.setRouterServersHostpathRoot(""); // go lang string init val
        }

    }

    private static String getFilebeatCMName() {
        return "operator-filebeat-configmap";
    }

    @Override
    protected void deletePvc(CloudAppVO app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        for (int i = 0; i < app.getMembers(); i++) {
            kubeClient.deletePvc(app.getNamespace(), "data-volume-" + app.getCrName() + "-" + i);
            kubeClient.deletePvc(app.getNamespace(), "logs-volume-" + app.getCrName() + "-" + i);
            if (app instanceof CloudAppVO && CSIUtil.isHostpath(((CloudAppVO) app).getCsiType())) {
                kubeClient.deletePv("data-volume-" + app.getCrName() + "-" + i);
                kubeClient.deletePv("logs-volume-" + app.getCrName() + "-" + i);
            }
        }
    }

    @Override
    public String getIpReservationTarget() {
        return "pod";
    }

    @Override
    protected void setInstallExtData(CloudAppVO vo) {
        vo.appendExtInstallKVData("username", vo.getUsername());
        vo.appendExtInstallKVData("password", vo.getPassword());
    }


    public int handleWatchResult(Integer appId, boolean success) {
//        CloudApp app = appCommonService.get(appId);
//        // ip address management
//        List<String> ips = networkService.getAllocatedIpOfApp(app);
//        Set<String> curIps = new HashSet<>(ips);
//        CloudApp.IpNode[] ipNodes = app.getIpNode();
//        Set<String> prevIps = Arrays.stream(ipNodes).map(i -> i.getIp()).collect(Collectors.toSet());
//        curIps.addAll(prevIps); // 避免ipinfo记录不全
//        ArrayList<String> difference = new ArrayList<>(Sets.difference(curIps, prevIps));
//        if (success) {
//            // 缩容成功更新ip信息
//            if (prevIps.size() > curIps.size()) {
////                networkService.reserveLocalIp(difference, app);
////                networkService.releaseLocalIp(difference, app);
//            }
//        } else {
//            // 扩容失败回滚ip信息
//            if (curIps.size() > prevIps.size()) {
//                // ip stay as reserved
////                networkService.releaseLocalIp(difference, app);
//            }
//        }

        return appService.handleWatchResult(appId, success);
    }

    @Override
    protected void completeInstanceProperty(AppInstanceVO appInstanceVO, PodDTO pod, CloudApp app, KubeClient kubeClient) {
        //查询主从
        try {
//            String pbmStr = kubeClient.execCmd(app.getNamespace(), pod.getPodName(), "mongodb", "sh", "-c", "mongo --port $MONGO_PORT --eval \"db.runCommand({hello: 1})\" | grep isWritablePrimary |awk -F ':' '{print $2}'");
            appInstanceVO.setRole(pod.getLabel(CloudAppConstant.CustomLabels.ROLE));
       } catch (Exception e) {
            return;
        }
        //判断插件类型
        appInstanceVO.setComponentKind(MongoUtil.getComponentKind(pod, app));
    }

    @Override
    public void scale(int id, OverrideSpec vo, ActionEnum action) throws Exception {
        if (action == ActionEnum.SCALE_OUT) {
            scaleOut(id, vo.getMasterSize(), vo.getSpareSize(), vo.getConfigServersMembers(), vo.getRouterServersMembers());
        } else if (action == ActionEnum.SCALE_IN) {
            scaleDown(id, vo.getMasterSize(), vo.getSpareSize(), vo.getConfigServersMembers(), vo.getRouterServersMembers());
        }
    }

    @Override
    public PageInfo<MongoDbClusterService.MongoDBClusterVO> searchPage(PageDTO page) {
        PageInfo<? extends CloudAppVO> cloudAppVOPageInfo = super.searchPage(page);
        return PageUtil.page2PageInfo(cloudAppVOPageInfo.getList(), MongoDbClusterService.MongoDBClusterVO.class, cloudAppVO -> {
            MongoDbClusterService.MongoDBClusterVO mongoDBClusterVO = new MongoDbClusterService.MongoDBClusterVO();
            BeanUtils.copyProperties(cloudAppVO, mongoDBClusterVO);
            if (AppKind.MongoDB_Cluster.getArch().equalsIgnoreCase(mongoDBClusterVO.getArch())) {
                //创建拓扑结构的map
                HashMap<Integer, List<String>> topoMap = new HashMap<>();
                KubeClient kubeClient = clientService.get(mongoDBClusterVO.getKubeId());
                MongoDBCluster cr = kubeClient.listCustomResource(MongoDBCluster.class, mongoDBClusterVO.getCrName(), mongoDBClusterVO.getNamespace());
                //设置分片数
                if (null != cr) {
                    mongoDBClusterVO.setMasterSize(cr.getSpec().getShardServers().getShardCount());
                    //重新设置从节点数
                    mongoDBClusterVO.setSlaveCount(mongoDBClusterVO.getMembers() - cr.getSpec().getShardServers().getShardCount());
                }
                String expCrStr = mongoDBClusterVO.getCr();
                if (null != expCrStr) {
                    MongoDBCluster expCr = YamlEngine.unmarshal(expCrStr, MongoDBCluster.class);
                    //查询configServers和routerServers的当前节点数，用于扩缩容
                    int configServersMembers = expCr.getSpec().getConfigServers().getIpList().size();
                    int routerServersMembers = expCr.getSpec().getRouterServers().getIpList().size();
                    mongoDBClusterVO.setConfigServersMembers(configServersMembers);
                    mongoDBClusterVO.setRouterServersMembers(routerServersMembers);

                    //查询configServers和routerServers的当前资源
                    String configServersCpu = expCr.getSpec().getConfigServers().getCpu();
                    String configServersMemory = expCr.getSpec().getConfigServers().getMemory();
                    String configServersSize = expCr.getSpec().getConfigServers().getDataStorage().getSize();
                    String routerServersCpu = expCr.getSpec().getRouterServers().getCpu();
                    String routerServersMemory = expCr.getSpec().getRouterServers().getMemory();
                    String routerServersSize = expCr.getSpec().getRouterServers().getDataStorage().getSize();
                    mongoDBClusterVO.setConfigServersCpu(configServersCpu);
                    mongoDBClusterVO.setConfigServersMemory(configServersMemory);
                    mongoDBClusterVO.setConfigServersDisk(configServersSize);
                    mongoDBClusterVO.setRouterServersCpu(routerServersCpu);
                    mongoDBClusterVO.setRouterServersMemory(routerServersMemory);
                    mongoDBClusterVO.setRouterServersDisk(routerServersSize);

                    //查询分片结构
                    if (null != cr) {
                        MongoDBCluster.MongoDBClusterStatus status = cr.getStatus();
                        if (null != status) {
                            List<MongoDBCluster.MongoDBClusterStatus.Instance> instances = cr.getStatus().getInstances();
                            for (int j = 0; j < instances.size(); j++) {
                                if (instances.get(j).getName().contains("rs")) {
                                    //获取分片
                                    List<String> nodes = instances.get(j).getNodes();
                                    //放入拓扑map
                                    topoMap.put(j, nodes);
                                }
                            }
                            mongoDBClusterVO.setClusterIpMap(topoMap);
                        }
                    }
                }
            }

            return mongoDBClusterVO;
        });
    }


    /**
     * 扩容
     *
     * @param appId
     * @param masterSize
     * @param spareSize
     * @param configServersMembers
     * @param routerServersMembers
     * @throws Exception
     */
    private void scaleOut(int appId, Integer masterSize, Integer spareSize, Integer configServersMembers, Integer routerServersMembers) throws Exception {
        //判断参数
        if (0 == appId) {
            throw new CustomException(600, "未获取到应用id！");
        }
        //获取app
        CloudApp app = appService.get(appId);
        //获取cr
        MongoDBCluster expCr = YamlEngine.unmarshal(app.getCr(), MongoDBCluster.class);
        List<String> scaledIpListAll = new ArrayList<>();
        // 一.shardServers扩容
        //总iplist
        List<String> ips = expCr.getSpec().getShardServers().getIpList();
        //获取主实例数
        int shardCount = expCr.getSpec().getShardServers().getShardCount();
        //获取备库数
        int crSpareSize = expCr.getSpec().getShardServers().getIpList().size() - shardCount;
        //处理分片数和备库数，如果为null，则使用实际数量
        int oneShardSize = ips.size() / shardCount - 1;
        masterSize = masterSize == null ? shardCount : masterSize;
        spareSize = spareSize == null ? masterSize * oneShardSize : spareSize;
        //扩容总个数
        int scaleOutNum = (masterSize + spareSize) - ips.size();
        //判断是否需要扩缩容
        if (0 != scaleOutNum) {
            //根据扩容实例总个数，分配ip
            List<String> scaleOutIps = networkService.allocateIp(app, scaleOutNum, getIpOwnerKind(), getIpReservationTarget());
            ips.addAll(scaleOutIps);
            scaledIpListAll.addAll(scaleOutIps);
            //最终结果
            String[] ipsArr = ips.toArray(new String[ips.size()]);

            //设置cr的iplist
            expCr.getSpec().getShardServers().setIpList(ips);
            if (null != masterSize) {
                expCr.getSpec().getShardServers().setShardCount(masterSize);
            }
        }

        // 二.configServers扩容
        //判断是否需要扩容操作
        if (null != configServersMembers) {
            int configServersScaleOutNum = configServersMembers - expCr.getSpec().getConfigServers().getIpList().size();
            if (configServersScaleOutNum > 0) {
                //根据扩容实例总个数，分配ip
                List<String> configServersScaleOutIps = networkService.allocateIp(app, configServersScaleOutNum, getIpOwnerKind(), getIpReservationTarget());
                scaledIpListAll.addAll(configServersScaleOutIps);
                //设置cr的iplist
                expCr.getSpec().getConfigServers().getIpList().addAll(configServersScaleOutIps);
            }
        }

        // 三.routerServers扩容
        //判断是否需要扩容操作
        if (null != routerServersMembers) {
            int routerServersScaleOutNum = routerServersMembers - expCr.getSpec().getRouterServers().getIpList().size();
            if (routerServersScaleOutNum > 0) {
                //根据扩容实例总个数，分配ip
                List<String> routerServersScaleOutIps = networkService.allocateIp(app, routerServersScaleOutNum, getIpOwnerKind(), getIpReservationTarget());
                scaledIpListAll.addAll(routerServersScaleOutIps);
                //设置cr的iplist
                expCr.getSpec().getRouterServers().getIpList().addAll(routerServersScaleOutIps);
            }
        }


        List<PodDTO> podDTOS = kubeClientService.get(app.getKubeId()).listPod(app.getNamespace(), AppKind.MongoDB_Cluster.labelOfPod(app));
        List<String> podList = podDTOS.stream().map(e -> e.getPodName()).collect(Collectors.toList());
        Map<String, Object> map = new HashMap<>();
        map.put("app", app);
        map.put("podList", podList);

        String applyYaml = YamlEngine.marshal(expCr);
        OpLogContext.instance().IP(scaledIpListAll).YAML("CR", applyYaml, app.getCr());

        appService.callScheduler(app, applyYaml, map,
                ActionEnum.SCALE_OUT, MongoDbClusterScaleWatch.class);
        clientService.get(app.getKubeId()).updateCustomResource(expCr, MongoDBCluster.class);
    }

    /**
     * 切换版本
     *
     * @param appid
     * @param version
     * @throws Exception
     */
    @Override
    public void upgrade(int appid, String version) throws Exception {
        //获取应用
        CloudApp app = appService.get(appid);
        MongoDBCluster curCr = YamlEngine.unmarshal(app.getCr(), MongoDBCluster.class);
        //设置镜像
        Map<ImageKindEnum, String> imageManifest = appConfigService.getImageManifest(getKind(), version);
        String image = imageManifest.get(ImageKindEnum.MainImage);
        //如果镜像未发生改变，则停止执行
        if (curCr.getSpec().getImage().equals(image)) {
            return;
        }
        //获取第一条
        curCr.getSpec().setImage(image);
        String applyYaml = YamlEngine.marshal(curCr);
        OpLogContext.instance().YAML("CR", applyYaml, app.getCr());
        Map<String, String> map = new HashMap<>();
        map.put("version", version);
        appService.callScheduler(app, applyYaml, map, ActionEnum.UPGRADE, MongoDbClusterWatch.class);
        //修改资源
        clientService.get(app.getKubeId()).updateCustomResource(curCr, MongoDBCluster.class);
    }

    /**
     * 缩容
     *
     * @param appId
     * @param masterSize
     * @param spareSize
     * @param configServersMembers
     * @param routerServersMembers
     * @throws Exception
     */
    @Override
    public void scaleDown(int appId, Integer masterSize, Integer spareSize, Integer configServersMembers, Integer routerServersMembers) throws Exception {
        //判断参数
        if (0 == appId) {
            throw new CustomException(600, "未获取到应用id！");
        }
        // 一.shardservers缩容
        //获取app
        CloudApp app = appService.get(appId);
        KubeClient kubeClient = clientService.get(app.getKubeId());
        //获取cr
        String crStr = app.getCr();
        MongoDBCluster expCr = YamlEngine.unmarshal(crStr, MongoDBCluster.class);
        MongoDBCluster actCr = kubeClient.listCustomResource(MongoDBCluster.class, app.getCrName(), app.getNamespace());
        //获取status中的instances
        List<MongoDBCluster.MongoDBClusterStatus.Instance> instances = actCr.getStatus().getInstances();
        List<MongoDBCluster.MongoDBClusterStatus.Instance> rsList = instances.stream().filter(instance -> -1 != instance.getName().indexOf("rs"))
                .sorted(Comparator.comparing(MongoDBCluster.MongoDBClusterStatus.Instance::getName).reversed()).collect(Collectors.toList());
        //缩容后的iplist
        List<String> ips = new ArrayList<>();
        //获取实际的masterSize
        Integer shardCount = expCr.getSpec().getShardServers().getShardCount();
        // 获取一个分片中实例个数，包括祝主和备
        int oneShardCount = expCr.getSpec().getShardServers().getIpList().size() / shardCount;
        // 去空
        masterSize = null == masterSize ? shardCount : masterSize;
        spareSize = spareSize == null ? masterSize * (oneShardCount - 1) : spareSize;
        //1. 缩容分片
        if (null != masterSize && null != spareSize) {
            //分片变更个数
            int changeMasterSize = shardCount - masterSize;
            //备库变更个数
            int rmShards = (oneShardCount - 1) - spareSize / (masterSize == null ? shardCount : masterSize);
            //判断扩容还是缩容
            if (masterSize <= 0) {
                //缩容后分片数小于1
                throw new CustomException(600, "分片数不能小于1！");
            } else if (spareSize <= 0) {
                //缩容后备库数小于1
                throw new CustomException(600, "备库数不能小于1！");
            }

            //是否缩主了
            if (0 != changeMasterSize) {
//                rsList = rsList.subList(0, rsList.size() - changeMasterSize);
                //计算需要缩的序号
                IntStream st = IntStream.range(rsList.size() - changeMasterSize + 1, rsList.size() + 1);
                List<Integer> rsNumList = st.boxed().collect(Collectors.toList());
                rsList = rsList.stream().filter(rs -> !rsNumList.contains(Integer.valueOf(rs.getName().substring(rs.getName().indexOf("rs") + 2)))).collect(Collectors.toList());
            }
            if (0 != rmShards) {
                //缩备
                for (int i = 0; i < rsList.size(); i++) {
                    MongoDBCluster.MongoDBClusterStatus.Instance instance = rsList.get(i);
                    List<String> newNodes = instance.getNodes().subList(0, instance.getNodes().size() - rmShards);
                    instance.setNodes(newNodes);
                    rsList.set(i, instance);
                }
            }
            for (MongoDBCluster.MongoDBClusterStatus.Instance instance : rsList) {
                ips.addAll(instance.getNodes());
            }
        }

        //3.缩容完成
        //设置cr的iplist
        expCr.getSpec().getShardServers().setIpList(ips);
        if (null != masterSize) {
            expCr.getSpec().getShardServers().setShardCount(masterSize);
        }

        // 二.configServers缩容
        //判断是否需要扩容操作
        if (0 != configServersMembers && expCr.getSpec().getConfigServers().getIpList().size() - configServersMembers > 0) {
            //根据扩容实例总个数，释放ip
            //当前ipList
            List<String> rmConfigIpList = expCr.getSpec().getConfigServers().getIpList().subList(0, expCr.getSpec().getConfigServers().getIpList().size() - configServersMembers);
            List<String> finConfigIpList = expCr.getSpec().getConfigServers().getIpList().subList(expCr.getSpec().getConfigServers().getIpList().size() - configServersMembers, expCr.getSpec().getConfigServers().getIpList().size());
            networkService.releaseIp(app, rmConfigIpList, getIpReservationTarget());
            //设置cr的iplist
            expCr.getSpec().getConfigServers().setIpList(finConfigIpList);
        }

        // 三.routerServers缩容
        //判断是否需要扩容操作
        if (0 != routerServersMembers && expCr.getSpec().getRouterServers().getIpList().size() - routerServersMembers > 0) {
            //根据扩容实例总个数，释放ip
            //当前ipList
            List<String> rmRouterIpList = expCr.getSpec().getRouterServers().getIpList().subList(0, expCr.getSpec().getRouterServers().getIpList().size() - routerServersMembers);
            List<String> finRouterIpList = expCr.getSpec().getRouterServers().getIpList().subList(expCr.getSpec().getRouterServers().getIpList().size() - routerServersMembers, expCr.getSpec().getRouterServers().getIpList().size());
            networkService.releaseIp(app, rmRouterIpList, getIpReservationTarget());
            //设置cr的iplist
            expCr.getSpec().getRouterServers().setIpList(finRouterIpList);
        }

        List<PodDTO> podDTOS = kubeClientService.get(app.getKubeId()).listPod(app.getNamespace(), AppKind.MongoDB_Cluster.labelOfPod(app));
        List<String> podList = podDTOS.stream().map(e -> e.getPodName()).collect(Collectors.toList());
        Map<String, Object> map = new HashMap<>();
        map.put("app", app);
        map.put("podList", podList);
        String applyYaml = YamlEngine.marshal(expCr);
        OpLogContext.instance().YAML("CR", applyYaml, app.getCr());
        appService.callScheduler(app, applyYaml, map,
                ActionEnum.SCALE_IN, MongoDbClusterScaleWatch.class);
        clientService.get(app.getKubeId()).updateCustomResource(expCr, MongoDBCluster.class);
    }

    /**
     * 扩容分配Ip
     */
    private boolean updateIpAllocation(CloudApp app, String cniType, int expectedMembers) {
        if (CloudAppConstant.K8sCNIType.FABRIC.equals(cniType)) {

            IpPoolConfig ipConfig = ipPoolConfigService.selectByKubeIdAndKind(app.getKubeId(), app.getKind());
            BocRestfulHelper cniHelper = (BocRestfulHelper) BocCniHelperFactory.getInstance()
                    .create(app.getKubeId(), new CniConfig() {{
                        setNetwork(ipConfig.getNetwork());
                    }});
            // check if current reservation was enough
            String curReservedIps = cniHelper.getReservationIps(app.getNamespace(), app.getCrName(), "statefulset")
                    .orElseThrow(() -> new CustomException(600, "未找到ip已分配信息"));
            String curIps = String.join(",", networkService.getAllocatedIpOfApp(app));
            log.info("MongoDB scale out update ip allocation, ip info before update- " +
                    "ipam used:{}, cni reserved:{}, expected:{}", curIps, curReservedIps, expectedMembers);
            String[] split = curReservedIps.split(",");
            int curMembers = split.length;

            if (curMembers >= expectedMembers) return false;

            try {
                networkService.updateIpAllocation(app, cniType, Arrays.asList(split),
                        expectedMembers, cniHelper, null); // todo 应用类型到ip pool的映射关系
                log.info("reservation info after update--" + curIps);
                return true;
            } catch (Exception e) {
                throw new RuntimeException("update ip failed", e);
            }
        }
        return false;
    }

    /**
     * 查询镜像列表
     *
     * @return
     */
    public List<String> imageList(Integer kubeId, String version) {
        //查询镜像
        List<CloudAppConfig> list = appConfigService.get(getKind());
        //对当前版本进行切割
        String[] curVersion = version.split("\\.");
        //筛选出比当前版本高的镜像信息
        List<String> resList = list.stream().filter(item -> {
            if (version.equals(item.getVersion())) {
                return false;
            }
            String[] everyVersion = item.getVersion().split("\\.");
            for (int i = 0; i < everyVersion.length && i < curVersion.length; i++) {
                if (Integer.parseInt(curVersion[i]) > Integer.parseInt(everyVersion[i])) {
                    return false;
                } else if (Integer.parseInt(curVersion[i]) < Integer.parseInt(everyVersion[i])) {
                    return true;
                }
            }
            return false;
        }).map(CloudAppConfig::getVersion).collect(Collectors.toList());
        return resList;
    }

    @Autowired
    private BackupService backupService;

    @Override
    public CloudAppVO overrideSpec(CloudAppLogic logicApp, Integer kubeId, InstallAppVo<? extends OverrideSpec> vo) {
        CloudAppVO cloudAppVO = super.overrideSpec(logicApp, kubeId, vo);
        MongoDBClusterVO mongoDBCluster = new MongoDBClusterVO();

        BeanUtils.copyProperties(cloudAppVO, mongoDBCluster);
        if (vo.getOverrideSpecs().get(kubeId) instanceof MongoDBClusterOverrideSpec) {
            MongoDBClusterOverrideSpec overrideSpec = (MongoDBClusterOverrideSpec) vo.getOverrideSpecs().get(kubeId);
            mongoDBCluster.setCsiType(overrideSpec.getCsiType());
            mongoDBCluster.setDisk(overrideSpec.getDisk());
            mongoDBCluster.setHostpathRoot(overrideSpec.getHostpathRoot());
            mongoDBCluster.setMasterSize(overrideSpec.getMasterSize());
            mongoDBCluster.setSpareSize(overrideSpec.getSpareSize());
            mongoDBCluster.setBackupHostpathRoot(overrideSpec.getBackupHostpathRoot());
            mongoDBCluster.setBackupCsiType(overrideSpec.getBackupCsiType());
//            mongoDBCluster.setBackupDisk(overrideSpec.getBackupDisk());
            mongoDBCluster.setDbParamTemplateId(vo.getDbParamTemplateId());

            mongoDBCluster.setConfigServersCpu(overrideSpec.getConfigServersCpu());
            mongoDBCluster.setConfigServersMemory(overrideSpec.getConfigServersMemory());
            mongoDBCluster.setConfigServersHostpathRoot(overrideSpec.getConfigServersHostpathRoot());
            mongoDBCluster.setConfigServersCsiType(overrideSpec.getConfigServersCsiType());
            mongoDBCluster.setConfigServersDisk(overrideSpec.getConfigServersDisk());
            mongoDBCluster.setConfigServersBackupHostpathRoot(overrideSpec.getConfigServersBackupHostpathRoot());
            mongoDBCluster.setConfigServersBackupCsiType(overrideSpec.getConfigServersBackupCsiType());
//            mongoDBCluster.setConfigServersBackupDisk(overrideSpec.getConfigServersBackupDisk());
            mongoDBCluster.setConfigServersMembers(overrideSpec.getConfigServersMembers());
            mongoDBCluster.setConfigParamTemplateId(vo.getConfigParamTemplateId());
            mongoDBCluster.setConfigTemplateTmpParam(vo.getConfigTemplateTmpParam());

            mongoDBCluster.setRouterServersCpu(overrideSpec.getRouterServersCpu());
            mongoDBCluster.setRouterServersMemory(overrideSpec.getRouterServersMemory());
            mongoDBCluster.setRouterServersHostpathRoot(overrideSpec.getRouterServersHostpathRoot());
            mongoDBCluster.setRouterServersCsiType(overrideSpec.getRouterServersCsiType());
            mongoDBCluster.setRouterServersDisk(overrideSpec.getRouterServersDisk());
            mongoDBCluster.setRouterServersMembers(overrideSpec.getRouterServersMembers());
            mongoDBCluster.setRouterParamTemplateId(vo.getRouterParamTemplateId());
        }
        return mongoDBCluster;
    }

    @Override
    public OverrideSpec reviewSpec(CloudApp app) {
        OverrideSpec overrideSpec = super.reviewSpec(app);
        MongoDBClusterOverrideSpec mongoDBClusterOverrideSpec = new MongoDBClusterOverrideSpec();
        BeanUtils.copyProperties(overrideSpec, mongoDBClusterOverrideSpec);

        String yaml = app.getCr();
        MongoDBCluster cr = YamlEngine.unmarshal(yaml, MongoDBCluster.class);

        // sharding server
        mongoDBClusterOverrideSpec.setCsiType(CSIUtil.translateSC(cr.getSpec().getShardServers().getDataStorage().getStorageClass()));
        mongoDBClusterOverrideSpec.setMasterSize(cr.getSpec().getShardServers().getShardCount());
        mongoDBClusterOverrideSpec.setSpareSize(cr.getSpec().getShardServers().getIpList().size() - mongoDBClusterOverrideSpec.getMasterSize());
        // config server
        mongoDBClusterOverrideSpec.setConfigServersMembers(cr.getSpec().getConfigServers().getIpList().size());
        mongoDBClusterOverrideSpec.setConfigServersCpu(cr.getSpec().getConfigServers().getCpu());
        mongoDBClusterOverrideSpec.setConfigServersMemory(cr.getSpec().getConfigServers().getMemory());
        mongoDBClusterOverrideSpec.setConfigServersDisk(cr.getSpec().getConfigServers().getDataStorage().getSize());
        mongoDBClusterOverrideSpec.setConfigServersCsiType(CSIUtil.translateSC(cr.getSpec().getConfigServers().getDataStorage().getStorageClass()));
//        mongoDBClusterOverrideSpec.setConfigServersBackupDisk(cr.getSpec().getConfigServers().getBackupStorage().getSize());
//        mongoDBClusterOverrideSpec.setConfigServersBackupCsiType(CSIUtil.translateSC(cr.getSpec().getConfigServers().getBackupStorage().getStorageClass()));
        // route server
        mongoDBClusterOverrideSpec.setRouterServersMembers(cr.getSpec().getRouterServers().getIpList().size());
        mongoDBClusterOverrideSpec.setRouterServersCpu(cr.getSpec().getRouterServers().getCpu());
        mongoDBClusterOverrideSpec.setRouterServersMemory(cr.getSpec().getRouterServers().getMemory());
        mongoDBClusterOverrideSpec.setRouterServersDisk(cr.getSpec().getRouterServers().getDataStorage().getSize());
        mongoDBClusterOverrideSpec.setRouterServersCsiType(CSIUtil.translateSC(cr.getSpec().getRouterServers().getDataStorage().getStorageClass()));
        return mongoDBClusterOverrideSpec;
    }

    public Map<String, Object> checkMastersize(Integer backupHisId, Integer restoreAppId, Integer restoreMastersize) {
        //判断参数
        if (null == backupHisId) {
            throw new CustomException(500, "备份历史id为空！");
        }
        if (null == restoreAppId && null == restoreMastersize) {
            throw new CustomException(500, "恢复应用id为空或分片数为空！");
        }

        //返回结果
        Map<String, Object> res = new HashMap<>();
        //查询备份历史，获取备份适用的分片数
        BackupHis backupHis = backupService.get(backupHisId);
        String mes = backupHis.getMessage();
        JSONObject mesObj = JSONObject.parseObject(mes);
        Integer backupMastersize = mesObj.getInteger("masterSize");
        if (null == backupMastersize) {
            throw new CustomException(500, "未找到当前备份文件适用分片数！");
        }

        //查询恢复应用的分片数
        if (null == restoreMastersize) {
            CloudApp app = appService.get(restoreAppId);
            MongoDBCluster actCr = YamlEngine.unmarshal(app.getCr(), MongoDBCluster.class);
            if (null == actCr) {
                throw new CustomException(500, "未找到恢复应用cr！");
            }
            restoreMastersize = actCr.getSpec().getShardServers().getShardCount();

        }
        if (backupMastersize <= restoreMastersize) {
            res.put("result", true);
            res.put("masterSize", backupMastersize);
        } else {
            res.put("result", false);
            res.put("masterSize", backupMastersize);
        }
        return res;
    }

    @Override
    public List<ServiceManager> createService(
            String serviceType, CloudAppVO vo, List<?> serviceResources, CustomResource installCr) {
        if (serviceResources.isEmpty()) return Collections.emptyList();

        AppKind kind = getKind();
        int serviceManagerNum = kind.getServiceManagerNum(serviceType, vo.getMembers());
        if (CollectionUtils.isEmpty(serviceResources) || serviceResources.size() != serviceManagerNum) {
            throw new CustomException(600, "节点端口类型必须指定 " + serviceManagerNum
                    + " 个端口, 实际数量为 " + serviceResources.size() + ", 类型为 " + serviceType);
        }
        List<ServiceManager> svms = new ArrayList<>();
        MongoDBCluster cr = (MongoDBCluster) installCr;
        MongoDBCluster.MongoDBClusterSpec spec = cr.getSpec();

        Object serviceResource = serviceResources.get(0);
        String serviceName = getKind().getWriteServiceName(vo.getCrName(), null);
        Integer nodePort;
        int dbPort = getKind().getDbPort();

        //2.3 初始化 ServiceManager
        ServiceManager serviceManager = new ServiceManager();
        // 没有读写分离，且只需要一个 service
        serviceManager.setPurpose(CloudAppConstant.ServicePurpose.WRITE);
        serviceManager.setServiceName(serviceName);
        serviceManager.setServiceType(serviceType);

        //2.4 根据 ServiceType 填充 NodePort 和 lb
        if (CloudAppConstant.ServiceType.NODE_PORT.equals(serviceType)) {
            //nodeport 方式，serviceResources 结构为 List<Integer>，进行分配 NodePort，只需要一个 NodePort
            nodePort = (Integer) serviceResource;
            serviceManager.setPort(nodePort);
            spec.setServicePort(nodePort);
        } else if (CloudAppConstant.ServiceType.LOAD_BALANCER.equals(serviceType)) {
            //lb 方式，serviceResources 结构为 List<String>，进行分配 lbip，只需要一个 lbip，端口为 dbport
            serviceManager.setPort(dbPort);
            String lbip = (String) serviceResource;
            serviceManager.setExternalIp(lbip);
            spec.setServiceExternalIP(lbip);
        }

        svms.add(serviceManager);
        return svms;
    }

    @Override
    public void updateService(List<ServiceManager> svcMgrs, CloudApp app, Object oldServiceResource) throws Exception {
        ServiceManager serviceManager = svcMgrs.get(0);
        String serviceType = serviceManager.getServiceType();
        Map<String, String> data = new HashMap<>();
        data.put("oldServiceResource", oldServiceResource + "");
        Consumer<MongoDBCluster> modifier = (cr) -> {
            if (serviceType.equalsIgnoreCase(CloudAppConstant.ServiceType.NODE_PORT)) {
                cr.getSpec().setServicePort(serviceManager.getPort());
            } else if (serviceType.equalsIgnoreCase(CloudAppConstant.ServiceType.LOAD_BALANCER)) {
                cr.getSpec().setServiceExternalIP(serviceManager.getExternalIp());
            }
        };
        appOperationHandler.handleService(
                app, svcMgrs, modifier, MongoDBCluster.class, this, data, ActionEnum.UPDATE_SERVICE);
    }

    @Getter
    @Setter
    public static class MongoDBClusterOverrideSpec extends OverrideSpec {
        private Integer masterSize;
        private Integer spareSize;
        private String csiType;
        private String backupDisk;
        private String backupCsiType;
        private String backupHostpathRoot;

        private String configServersCpu;
        private String configServersMemory;
        private String configServersHostpathRoot;
        private String configServersCsiType;
        private String configServersDisk;
        private String configServersBackupHostpathRoot;
        private String configServersBackupCsiType;
        private String configServersBackupDisk;
        private int configServersMembers;

        private String routerServersCpu;
        private String routerServersMemory;
        private String routerServersHostpathRoot;
        private String routerServersCsiType;
        private String routerServersDisk;
        private int routerServersMembers;
    }

    @Data
    public static class MongoDBClusterVO extends CloudAppVO {

        private Integer shardCount;
        private String configServersCpu;
        private String configServersMemory;
        private String configServersHostpathRoot;
        private String configServersCsiType;
        private String configServersDisk;
        private String configServersBackupHostpathRoot;
        private String configServersBackupCsiType;
        private String configServersBackupDisk;
        private Integer configServersMembers;
        private Integer configParamTemplateId;
        private Map<String, String> configTemplateTmpParam;
        private String routerServersCpu;
        private String routerServersMemory;
        private String routerServersHostpathRoot;
        private String routerServersCsiType;
        private String routerServersDisk;
        private Integer routerServersMembers;
        private Integer routerParamTemplateId;

        //拓扑结构使用
        private Map<Integer, List<String>> clusterIpMap;
    }

//    @Override
//    protected void validateStorageClass(CloudAppVO app) {
//        // sentinel 没有存储 不校验
//        if("topolvm".equalsIgnoreCase(app.getCsiType())){
//            app.setCsiType("topolvm-provisioner");
//        }
//    }

    @Override
    public InstallAppVo<MongoDBClusterOverrideSpec> parseInstallVo(String data) {
        InstallAppVo<MongoDBClusterOverrideSpec> vo = JsonUtil.toObject(data, new com.fasterxml.jackson.core.type.TypeReference<InstallAppVo<MongoDBClusterOverrideSpec>>() {
        });
        if (vo != null) {
            if (vo.getSpec() != null && vo.getSpec().getMembers() == 0) {
                MongoDBClusterOverrideSpec spec = vo.getSpec();
                spec.setMembers(spec.getMasterSize() * (spec.getSpareSize() + 1));
            }
            if (!CollectionUtils.isEmpty(vo.getOverrideSpecs())) {
                for (MongoDBClusterOverrideSpec spec : vo.getOverrideSpecs().values()) {
                    if (spec.getMembers() == 0 && spec.getMasterSize() != null && spec.getSpareSize() != null)
                        spec.setMembers(spec.getMasterSize() * (spec.getSpareSize() + 1));
                }
            }
        }

        return vo;
    }

    private String getSecretName(String crName) {
        return crName + "-user";
    }


    @Override
    public void updatePassword(Password password, int appId) throws Exception {
        //获取密码
        String newPassword = password.getNewPassword();
        //获取应用
        CloudApp app = appService.get(appId);
        //判断是否已经存在密码
        List<CloudDatabaseUser> users = dbUserService.findDbUser(app.getId(), CloudAppConstant.UserRole.ADMIN);
        if (CollectionUtils.isEmpty(users)) {
            throw new CustomException(600, "当前应用在创建时未设置密码！");
        }
        //获取自定义的管理员用户名
        List<CloudDatabaseUser> userList = dbUserService.findDbUser(appId, "admin");
        List<CloudDatabaseUser> userFind = userList.stream().filter(user -> !"admin".equalsIgnoreCase(user.getUsername())).collect(Collectors.toList());
        if (null == userFind || 0 == userFind.size()) {
            throw new CustomException(600, "当前应用在创建时未设置密码！");
        }
        CloudDatabaseUser cloudDatabaseUser = userFind.get(0);
        String username = cloudDatabaseUser.getUsername();
        //理想cr
        MongoDBCluster cr = YamlEngine.unmarshal(app.getCr(), MongoDBCluster.class);

        if (null == newPassword || "".equalsIgnoreCase(newPassword)) {
            throw new CustomException(600, "新密码不能为空！");
        }

        //对密码进行解密
        //对密码进行解密
        String deNewPassword = decryptPassword(password.getNewPassword());
        newPassword = deNewPassword;

        // 1.获取router实例
        String routerIp = cr.getSpec().getRouterServers().getIpList().get(0);
        String routerIpStr = routerIp.replace(".", "-");
        String routerName = "mc-" + app.getCrName() + "-router-" + routerIpStr;

        // 2.将cr中的密码修改为新密码
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        kubeClient.execCmd(app.getNamespace(), routerName, "mongodb", "sh", "-c",
                "mongo --port ${MONGO_PORT} --eval \"var adminDB = db.getSiblingDB('admin');adminDB.changeUserPassword('" + username + "', '" + newPassword + "')\" --username " + CloudAppConstant.UsernameAndPassword.mongoDBUsername + " --password " + CloudAppConstant.UsernameAndPassword.mongoDBPassword);
        kubeClient.execCmd(app.getNamespace(), routerName, "mongodb", "sh", "-c",
                "mongo --port ${MONGO_PORT} --eval \"var adminDB = db.getSiblingDB('admin');adminDB.changeUserPassword('" + MongoUtil.DMP_ADMIN + "', '" + newPassword + "')\" --username " + CloudAppConstant.UsernameAndPassword.mongoDBUsername + " --password " + CloudAppConstant.UsernameAndPassword.mongoDBPassword);
        //修改操作历史
        ResourceChangeHis his = appService.getResourceChangeHis(app, ActionEnum.UPDATE_PASSWORD, StatusConstant.SUCCESS, app.getCr(), null);
        his.setLastEndTimestamp(System.currentTimeMillis());
        his.setUpdateTime(new Timestamp(System.currentTimeMillis() + 2000));
        resourceChangeHisService.add(his);
        app.setStatus(CloudAppConstant.AppStatus.SUCCESS);
        appService.update(app);
        if (autoManagement) {
            //纳管密码
            CloudAppVO vo = new CloudAppVO();
            vo.setId(appId);
            vo.setLogicAppId(app.getLogicAppId());
            vo.setPassword(SymmetricEncryptionUtil.getEncryptInstance().encrypt(newPassword));
            operationUtil.alterApp(vo);
        }
    }

    public void createConfig(MongoDBCluster.MongoDBClusterSpec spec, CloudAppVO vo) {
        Integer paramTemplateId = vo.getDbParamTemplateId();
        MongoDBClusterVO mongoDBClusterVO = (MongoDBClusterVO) vo;
        Integer configParamTemplateId = mongoDBClusterVO.getConfigParamTemplateId();
        Map<String, String> shardConfig = new HashMap<>();
        if (null != paramTemplateId) {
            shardConfig = getConfig(shardConfig, paramTemplateId);
        }
        overWriteCnfParam(mongoDBClusterVO, shardConfig, "shard");
        spec.getShardServers().setConfig(shardConfig);

        Map<String, String> configConfig = new HashMap<>();
        if (null != configParamTemplateId) {
            configConfig = getConfig(configConfig, configParamTemplateId);
        }
        overWriteCnfParam(mongoDBClusterVO, configConfig, "config");
        spec.getConfigServers().setConfig(configConfig);

    }

    private Map<String, String> getConfig(Map<String, String> configMap, Integer configId) {
        if (null != configMap) {
            // 将参数模板中的参数写入cr中
            MySQLParamTemplateDTO mysqlParamTemplate = cloudDbParamTemplateService.getMysqlParamTemplate(configId);
            List<MysqlParamRules> paramRules = mysqlParamTemplate.getMysqlParamRules();
            configMap = paramRules.stream().collect(Collectors.toMap(e -> e.getParaname(), e -> e.getParavalue()));
        } else {
            configMap = new HashMap<>();
        }
        return configMap;
    }

    @Override
    public void modifyConfigParam(Map<String, String> params, Integer appId, String componentKind) {
        log.info("[modifyParam] mongoDB修改参数 应用ID:{}，参数{}", appId, JSONObject.toJSONString(params));
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        CloudApp app = appService.get(appId);
        String crStr = app.getCr();
        MongoDBCluster oldCr = YamlEngine.unmarshal(crStr, MongoDBCluster.class);
        Map<String, String> oldConfig = null;
        if ("DB".equalsIgnoreCase(componentKind)) {
            oldConfig = oldCr.getSpec().getShardServers().getConfig();
            if (null == oldConfig) {
                oldCr.getSpec().getShardServers().setConfig(new HashMap<String, String>(params));
            } else {
                Map<String, String> userConfig = new HashMap<>(oldConfig);
                params.forEach((key, val) -> {
                    this.prohibitParam(key);
                    userConfig.put(key, val);
                });
                oldCr.getSpec().getShardServers().setConfig(userConfig);
            }
        } else if ("CONFIG".equalsIgnoreCase(componentKind)) {
            oldConfig = oldCr.getSpec().getConfigServers().getConfig();
            if (null == oldConfig) {
                oldCr.getSpec().getConfigServers().setConfig(new HashMap<String, String>(params));
            } else {
                Map<String, String> userConfig = new HashMap<>(oldConfig);
                params.forEach((key, val) -> {
                    this.prohibitParam(key);
                    userConfig.put(key, val);
                });
                oldCr.getSpec().getConfigServers().setConfig(userConfig);
            }
        } else if (CloudAppConstant.MongoDB.MONGOS.equalsIgnoreCase(componentKind)) {
            oldConfig = oldCr.getSpec().getRouterServers().getConfig();
            if (null == oldConfig) {
                oldCr.getSpec().getRouterServers().setConfig(new HashMap<String, String>(params));
            } else {
                Map<String, String> userConfig = new HashMap<>(oldConfig);
                params.forEach((key, val) -> {
                    this.prohibitParam(key);
                    userConfig.put(key, val);
                });
                oldCr.getSpec().getRouterServers().setConfig(userConfig);
            }
        }


        Map<Object, Object> map = new HashMap<>();
        map.put("params", params);
        map.put("appId", appId);
        map.put("startTime", new Date().getTime());
        map.put("componentKind", componentKind);

        KubeClient client = clientService.get(app.getKubeId());
        try {
            String applyYaml = YamlEngine.marshal(oldCr);
            OpLogContext.instance().CR(applyYaml, app.getCr());
            client.updateCustomResource(oldCr, MongoDBCluster.class);
            appService.callScheduler(app, applyYaml, map, ActionEnum.MODIFY_PARAM, MongoDBClusterModifyParamsWatch.class);
        } catch (SchedulerException | JsonProcessingException e) {
            // 回滚
            if ("DB".equalsIgnoreCase(componentKind)) {
                oldCr.getSpec().getShardServers().setConfig(new HashMap<String, String>(oldConfig));
            } else if ("CONFIG".equalsIgnoreCase(componentKind)) {
                oldCr.getSpec().getConfigServers().setConfig(new HashMap<String, String>(oldConfig));
            } else if (CloudAppConstant.MongoDB.MONGOS.equalsIgnoreCase(componentKind)) {
                oldCr.getSpec().getRouterServers().setConfig(new HashMap<String, String>(oldConfig));
            }
            client.updateCustomResource(oldCr, MongoDBCluster.class);
            throw new CustomException(600, "MongoDB 修改参数失败，" + e.getMessage());
        }
    }

    /**
     * 切换指定节点为主，需要在主节点上执行。
     *
     * @param appId   应用
     * @param podName 下一任主节点
     */
    public void switchMasterPlanned(Integer appId, String podName) throws Exception {
        if (appId == null || StringUtils.isBlank(podName)) {
            throw new CustomException(600, "切换为主参数错误");
        }
        // 如果当前存在切换任务则返回
        ResourceChangeHis latestRcs = resourceChangeHisService.getLatestByAppId(appId);
        CustPreconditions.checkState(!StatusConstant.RUNNING.equals(latestRcs.getStatus()), "当前应用正在执行 " + latestRcs.getCommand() + " 操作，请稍候再试");
        CloudApp app = appService.get(appId);
        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
        KubeClient kubeClient = clientService.get(app.getKubeId());
        String loginStatement = "mongo -u " + CloudAppConstant.UsernameAndPassword.mongoDBUsername + " -p " + CloudAppConstant.UsernameAndPassword.mongoDBPassword + " --port ${MONGO_PORT} --authenticationDatabase admin --quiet --eval ";
        //将podName转换为podIp
        String podIp = podName.replace("mc-" + app.getCrName() + "-shard-", "").replace("-", ".");
        // 1.查询出当前选择pod所处分片
        MongoDBCluster cr = kubeClient.listCustomResource(MongoDBCluster.class, app.getCrName(), app.getNamespace());
        MongoDBCluster expCr = YamlEngine.unmarshal(app.getCr(), MongoDBCluster.class);

        if (null == cr || null == cr.getStatus())
            throw new CustomException(600, "未查询到相关的cr信息");

        List<MongoDBCluster.MongoDBClusterStatus.Instance> instances = cr.getStatus().getInstances();

        if (CollectionUtils.isEmpty(instances))
            throw new CustomException(600, "未查询到相关的mongo 实例信息");

        List<String> podIps = instances.stream().filter(instance -> instance.getName().contains("rs") && instance.getNodes().contains(podIp)).findFirst().orElseThrow(() -> new CustomException(600, "未查询到pod所在分片信息不存在！")).getNodes();

        // 2.获取目前主节点
        String masterIp = podIps.get(0);
        //将主节点的podIp我转换为podName
        String masterName = "mc-" + app.getCrName() + "-shard-" + masterIp.replace(".", "-");

        // 3.获取status信息
        //获取相应的Status信息
        List<Map<String, String>> mongoDbStatusMembers = mongoDBPodService
                .getMongoDbStatusMembers(kubeClient, app.getNamespace(), app.getCrName(), podName, appKind);
        if (CollectionUtils.isEmpty(mongoDbStatusMembers)) {
            throw new CustomException(600, "角色获取失败");
        }

        // 4.分别获取主与从的oplog的最后事务时间，判断是否一致
        Map<String, String> mongoDbOpTimeByRoleMap = getMongoDbOpTimeByRoleMap(mongoDbStatusMembers);
        if (!checkMasterClusterOplogIsSame(masterIp, podIp, mongoDbOpTimeByRoleMap)) {
            throw new CustomException(600, "当前节点数据与主节点数据不一致，请一致后再进行操作");
        }

        // 5.查询当前集群配置,并寻找下任master在集群中的位置
        String cmd1 = loginStatement + "'JSON.stringify(rs.conf().members)'";
        String result = kubeClient.execCmd(app.getNamespace(), masterName, AppKind.MongoDB_Cluster.getContainerName(), "sh", "-c", cmd1);
        Map<String, String>[] maps = JSONArray.parseObject(result, new TypeReference<Map<String, String>[]>() {});
        int targetPodIndex = 1;
        for (int i = 0; i < maps.length; i++) {
            String host = maps[i].get("host");
            if (host.split(":")[0].equals(podIp)) {
                targetPodIndex = i;
                break;
            }
        }

        try {
            //暂时取消开启维护模式
            // expCr.getSpec().setMaintenance(true);
            kubeClient.updateCustomResource(expCr, MongoDBCluster.class);
        } catch (Exception e) {
            throw new CustomException(600, "主从切换开启维护模式失败！");
        }
        // 切换主从
        String cmd2 = loginStatement + "'var cfg = rs.conf();cfg.members[" + targetPodIndex + "].priority = 10;rs.reconfig(cfg);rs.stepDown(30)'";
        kubeClient.execCmd(app.getNamespace(), masterName, AppKind.MongoDB_Cluster.getContainerName(), "sh", "-c", cmd2);

        Map<String, Object> map = new HashMap<>();
        map.put("podName", podName);
        map.put("startTime", new Date().getTime());
        appService.callScheduler(app, YamlEngine.marshal(expCr), map, ActionEnum.SWITCH_MASTER, getProcessorClass(ActionEnum.SWITCH_MASTER), 10);
    }

    /**
     * 提取 RoleMap中的主从key信息
     */
    public Map<String, String> getMongoDbRoleMap(List<Map<String, String>> maps) {
        Map<String, String> roles = maps.stream().collect(Collectors.toMap(key -> key.get("name").split("\\.")[0], val -> {
            String stateStr = val.get("stateStr");
            if ("PRIMARY".equals(stateStr)) {
                return "primary";
            }
            if ("SECONDARY".equals(stateStr)) {
                return "secondary";
            }
            return "";
        }));
        return roles;
    }


    /**
     * 从MongoDB的状态信息中获取到相应节点的optime信息
     */
    public Map<String, String> getMongoDbOpTimeByRoleMap(List<Map<String, String>> maps) {
        return maps.stream().collect(Collectors.toMap(key -> key.get("name").split(":")[0], val -> {
            //获取到optime中的字符串
            return String.valueOf(JSONPath.read(val.get("optime"), "ts.$timestamp.t"));
        }));
    }

    private Boolean checkMasterClusterOplogIsSame(String masterName, String clusterName, Map<String, String> mongoDbOpTimeByRoleMap) {
        String masterDate = mongoDbOpTimeByRoleMap.get(masterName);
        String clusterDate = mongoDbOpTimeByRoleMap.get(clusterName);
        return (StringUtils.isNotEmpty(masterDate) && StringUtils.isNotEmpty(clusterDate)) && masterDate.equals(clusterDate);
    }

    /**
     * 临时修改参数
     *
     * @param vo     提交参数
     * @param config 参数模板内容
     */
    protected void overWriteCnfParam(CloudAppVO vo, Map<String, String> config, String componentKind) {
        MongoDBClusterVO mongoDBClusterVO = (MongoDBClusterVO) vo;
        if ("shard".equalsIgnoreCase(componentKind)) {
            if (!CollectionUtils.isEmpty(mongoDBClusterVO.getTemplateTmpParam())) {
                // 用户临时指定的参数
                for (Map.Entry<String, String> entry : mongoDBClusterVO.getTemplateTmpParam().entrySet()) {
                    this.prohibitParam(entry.getKey());
                    if ("-deleted-".equals(entry.getValue())) {
                        config.remove(entry.getKey());
                    } else {
                        config.put(entry.getKey(), entry.getValue());
                    }
                }
            }
        } else if ("config".equalsIgnoreCase(componentKind)) {
            if (!CollectionUtils.isEmpty(mongoDBClusterVO.getConfigTemplateTmpParam())) {
                // 用户临时指定的参数
                for (Map.Entry<String, String> entry : mongoDBClusterVO.getConfigTemplateTmpParam().entrySet()) {
                    this.prohibitParam(entry.getKey());
                    if ("-deleted-".equals(entry.getValue())) {
                        config.remove(entry.getKey());
                    } else {
                        config.put(entry.getKey(), entry.getValue());
                    }
                }
            }
        }

        // 自动计算的最佳配置
        String paramFormulaStr = sysConfigService.findOne(PARAM_FORMULA, getKind().getProduct() + "Cluster");
        if (StringUtils.isBlank(paramFormulaStr)) {
            return;
        }
        Map<String, String> paramFormulaMap = JSONObject.parseObject(paramFormulaStr).toJavaObject(Map.class);
        if (StringUtils.isBlank(String.valueOf(paramFormulaMap.get(componentKind) == null ? "" : paramFormulaMap.get(componentKind)))) {
            return;
        }
        Map<String, String> finMap = JSONObject.parseObject(String.valueOf(paramFormulaMap.get(componentKind) == null ? "" : paramFormulaMap.get(componentKind))).toJavaObject(Map.class);
        for (Map.Entry<String, String> entry : finMap.entrySet()) {
            if (!config.containsKey(entry.getKey())) {
                String val = calculate(vo, String.valueOf(entry.getValue()), ImmutableMap.of("cpu", vo.getCpu(), "memory", vo.getMemory(), "storage", vo.getDisk()));
                config.putIfAbsent(entry.getKey(), val);
            }
        }
    }

    @Override
    public void prohibitParam(String param) {
        String prohibitContent = sysConfigService.findOne(PARAM_PROHIBIT, getKind().getProduct() + "Cluster");
        if (StringUtils.isAnyEmpty(param, prohibitContent)) {
            return;
        }
        Set<String> result = new HashSet<>();
        String[] params = param.split(",");
        List<String> prohibited = Arrays.asList(prohibitContent.split(","));
        for (String prop : params) {
            if (prohibited.contains(prop)) {
                result.add(prop);
            }
        }
        if (!result.isEmpty()) {
            String join = StringUtils.join(result, "、");
            log.error("[prohibitParam] 参数 {} 禁止修改，请移除", join);
            throw new CustomException(600, "参数 " + join + " 禁止修改，请移除");
        }
    }

    protected String calculate(CloudAppVO vo, String expRaw, Map<String, String> resourceList)  {
        // 如果expRaw不含变量，而是参数值，直接返回
        String calculateExp = expRaw.toUpperCase();
        if (!calculateExp.contains("MEMORY") && !calculateExp.contains("CPU") && !calculateExp.contains("DISK")
                && !calculateExp.contains("CONFIGSERVERSDISK")) {
            return expRaw;
        }
        calculateExp = calculateExp.replaceAll("MAX", "max");
        calculateExp = calculateExp.replaceAll("MIN", "min");
        calculateExp = calculateExp.replaceAll("MEMORY", "#MEMORY");// ＃variableName
        calculateExp = calculateExp.replaceAll("CPU", "#CPU");
        // DISK 需要匹配完整词，避免匹配CONFIGSERVERSDISK中的DISK
        calculateExp = calculateExp.replaceAll("\\bDISK\\b", "#DISK");
        calculateExp = calculateExp.replaceAll("CONFIGSERVERSDISK", "#CONFIGSERVERSDISK");
        ExpressionParser parser = new SpelExpressionParser();
        StandardEvaluationContext context = new StandardEvaluationContext(Math.class);
        // required
        Double cpuCore = MetricUtil.getCpuCores(resourceList.get("cpu"));
        context.setVariable("CPU", new Double(cpuCore).intValue());
        Long memory = MetricUtil.getLongValueOfUnit(resourceList.get("memory"), 'M');
        context.setVariable("MEMORY", new Double(memory).longValue());
        Long disk = MetricUtil.getLongValueOfUnit(resourceList.get("storage"), 'M');
        context.setVariable("DISK", new Double(disk).longValue());
        // option
        MongoDbClusterService.MongoDBClusterVO mongoDBClusterVO = (MongoDbClusterService.MongoDBClusterVO) vo;
        if (StringUtils.isNoneEmpty(mongoDBClusterVO.getConfigServersDisk())){
            Long csDisk = MetricUtil.getLongValueOfUnit(mongoDBClusterVO.getConfigServersDisk(), 'M');
            context.setVariable("CONFIGSERVERSDISK", new Double(csDisk).longValue());
        }
        return parser.parseExpression(calculateExp).getValue(context, Double.class).longValue() + "";
    }


    @Override
    public void deleteCrControlledResources(CloudApp app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        kubeClient.deleteSecret(app.getCrName() + "-user", app.getNamespace());
    }

    /**
     * 进行集群组调度的时候使用：
     * 需要获取到 每个应用类型 各个组件的 资源信息
     */
    @Override
    public List<ResourceDTO> getAppComponentResource(InstallAppVo<? extends OverrideSpec> vo) {
        List<ResourceDTO> result = new ArrayList<>();
        MongoDBClusterOverrideSpec spec = (MongoDBClusterOverrideSpec) vo.getSpec();
        ResourceDTO masterResourceDto = new ResourceDTO();
        masterResourceDto.setCpu(spec.getCpu());
        masterResourceDto.setMemory(spec.getMemory());
        masterResourceDto.setReplicas(spec.masterSize);
        result.add(masterResourceDto);

        ResourceDTO configResourceDTO = new ResourceDTO();
        configResourceDTO.setCpu(spec.getConfigServersCpu());
        configResourceDTO.setMemory(spec.getConfigServersMemory());
        configResourceDTO.setReplicas(spec.configServersMembers);
        result.add(configResourceDTO);

        ResourceDTO routerResourceDTO = new ResourceDTO();
        routerResourceDTO.setCpu(spec.getRouterServersCpu());
        routerResourceDTO.setMemory(spec.getRouterServersMemory());
        routerResourceDTO.setReplicas(spec.routerServersMembers);
        result.add(routerResourceDTO);
        return result;
    }

    @Override
    public void restore(BackupHis backupHis, Integer appId, String restoreTime, String ftpFilename, String backupType) {
        // 0. 创建还原的对象
        RestoreHis restoreHis = new RestoreHis();
//        restoreTime = restoreTime.replace("T", " ").replace("Z", "");
        restoreHis.setStatus(StatusConstant.RUNNING);
        Date startDate = new Date();
//        String finRestoreTime = "";
//        if(null != restoreTime){
//            //时间转换，目标样例：2023-06-01T18:35:08Z
//            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            String restoreTimeStr = sdf.format(restoreTime);
//            finRestoreTime = restoreTimeStr.replace(" ", "T");
//            finRestoreTime += "Z";
//        }

        //操作记录
        ResourceChangeHis resourceChangeHis = null;
        Integer changeId = null;
        try {
            Timestamp startTime = new Timestamp(System.currentTimeMillis());
            restoreHis.setStartTime(startTime);
            // 1. 插入基础恢复历史和操作记录
            CloudApp goalApp = appService.get(appId);
            restoreHis.setAppId(appId);
            restoreHis.setAppName(goalApp.getName());
            restoreHis.setAppType(goalApp.getKind());
            KubeClient kubeClient = kubeClientService.get(goalApp.getKubeId());
            MongoDBCluster cr = YamlEngine.unmarshal(StringUtils.isEmpty(goalApp.getCr()) ? goalApp.getCrRun() : goalApp.getCr(), MongoDBCluster.class);

            //应用所属集群
            KubeConfig byId = kubeConfigService.get(goalApp.getKubeId());
            if (null == byId) {
                log.error("未获取到集群！");
                backupUtil.restoreReturn(restoreHis, changeId, "未获取到集群！", StatusConstant.FAIL);
                return;
            }
            restoreHis.setKubeName(byId.getName());
            restoreHis.setMessage("恢复中...");
            restoreHis.setFileDeleted(false);
            restoreHis.setPodName(backupUtil.getRestorePodNameMongoCluster(goalApp, null));
            String backDirInPod = "/backup/";
            restoreHis.setRestoreDir(backDirInPod);
            if(null != backupHis){
                restoreHis.setFileName(backupHis.getFileName());
            }
            backupService.commitRestoreHis(restoreHis);
            //插入操作记录
            resourceChangeHis = new ResourceChangeHis() {{
                setInsertTime(startTime);
                setKind(goalApp.getKind());
                setKubeId(goalApp.getKubeId());
                setNamespace(goalApp.getNamespace());
                setCommand("恢复");
                setStatus("2");
                setAction(ActionEnum.RESTORE.getActionType());
                setMsg("恢复中...");
                setAppId(goalApp.getId());
                setAppName(goalApp.getName());
                setUserName(UserUtil.getAsyncUserinfo().getUsername());
                setUserIp(CloudRequestContext.getContext().getUserIp());
                setKubeName(byId.getName());
                setYaml(goalApp.getCr());
                setLastEndTimestamp(System.currentTimeMillis());
                setAppLogicId(goalApp.getLogicAppId());
            }};
            changeId = backupUtil.insertResourceChangeHis(resourceChangeHis);

            // 2.修改cr的Spec.Restore.FullSource以及Spec.Restore.LastTimestamp属性
            CloudApp backupApp = appService.get(backupHis.getAppId());
//            Map<String, String> backupStorageInfo = getBackupStorageInfo();
            MongoDBCluster.MongoDBClusterSpec.Restore restore = new MongoDBCluster.MongoDBClusterSpec.Restore();
            restore.setFullSource(backupApp.getNamespace() + "/" + backupApp.getCrName() + "/" + backupHis.getFileName());
//            restore.setAddress(backupStorageInfo.get("url") + AppKind.MongoDB_Cluster.getKind() + "/");
//            restore.setType(backupStorageInfo.get("type"));
            cr.getSpec().setRestore(restore);

            //提交cr
            kubeClient.updateCustomResource(cr, MongoDBCluster.class);
            //创建定时轮询备份结果
            Map map = new HashMap();
            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
            map.put("restoreStartDateSDF", sdf1.format(startDate));
            map.put("restoreHisId", restoreHis.getRestoreHisId());
            map.put("resourceChangeId", changeId);
            appService.callScheduler(goalApp, YamlEngine.marshal(cr),map,ActionEnum.RESTORE,MongoDBClusterBackupAndRestoreWatch.class,resourceChangeHis);
        }catch (Exception e){
            log.error("提交恢复出错！信息为：" + e);
            backupUtil.restoreReturn(restoreHis, changeId, e.getMessage(), StatusConstant.FAIL);
        }
    }
}
