<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
<modelVersion>4.0.0</modelVersion>
<groupId>cn.newdt</groupId>
<artifactId>cloud-service</artifactId>
<version>6.4.0</version>

<properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
<!--    <jackson.version>2.13.0</jackson.version>-->
    <okhttp.version>4.11.0</okhttp.version>
    <fabric.version>5.10.2</fabric.version>
    <spring-retry.version>1.3.4</spring-retry.version>
    <dcp-commons.version>10.3.0</dcp-commons.version>
    <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
</properties>

<parent>
    <groupId>cn.newdt</groupId>
    <artifactId>shindcp-dependencies</artifactId>
    <version>1.2.0</version>
</parent>

<!--    <dependencyManagement>-->
<!--        <dependencies>-->
<!--            <dependency>-->
<!--                <groupId>cn.newdt</groupId>-->
<!--                <artifactId>shindcp-dependencies</artifactId>-->
<!--                <version>1.2.0</version>-->
<!--                <type>pom</type>-->
<!--                <scope>import</scope>-->
<!--            </dependency>-->
<!--        </dependencies>-->
<!--    </dependencyManagement>-->

<dependencies>
    <!--   override parent -->
    <!-- PageHelper -->
    <dependency>
        <groupId>com.github.pagehelper</groupId>
        <artifactId>pagehelper-spring-boot-starter</artifactId>
        <version>2.0.0</version>
        <exclusions>
            <exclusion>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
            </exclusion>
        </exclusions>
    </dependency>
    <dependency>
        <groupId>org.yaml</groupId>
        <artifactId>snakeyaml</artifactId>
        <version>1.28</version>
    </dependency>
    <dependency>
        <groupId>org.springframework.retry</groupId>
        <artifactId>spring-retry</artifactId>
        <version>${spring-retry.version}</version>
    </dependency>
    <!--    新 commons-->
    <dependency>
        <groupId>cn.newdt</groupId>
        <artifactId>dcp-commons</artifactId>
        <version>${dcp-commons.version}</version>
        <exclusions>
            <exclusion>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
            </exclusion>
            <exclusion>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
            </exclusion>
        </exclusions>
    </dependency>

    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <scope>test</scope>
    </dependency>
    <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <scope>test</scope>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
    <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
    </dependency>

    <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-yaml</artifactId>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jsr310</artifactId>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
    </dependency>

    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>8.0.11</version>
    </dependency>
<!--    override parent -->
    <dependency>
        <groupId>org.mybatis.spring.boot</groupId>
        <artifactId>mybatis-spring-boot-starter</artifactId>
        <version>1.3.2</version>
    </dependency>

<!--    cloud-service 依赖-->
    <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
        <version>42.3.6</version>
    </dependency>

    <dependency>
        <groupId>org.opengauss</groupId>
        <artifactId>opengauss-jdbc</artifactId>
        <version>3.0.0</version>
    </dependency>

    <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>4.5.2</version>
    </dependency>

    <dependency>
        <groupId>com.google.code.gson</groupId>
        <artifactId>gson</artifactId>
    </dependency>

    <dependency>
        <groupId>org.eclipse.jgit</groupId>
        <artifactId>org.eclipse.jgit</artifactId>
        <version>4.8.0.201706111038-r</version>
    </dependency>

    <dependency>
        <groupId>io.kubernetes</groupId>
        <artifactId>client-java</artifactId>
        <version>10.0.1</version>
        <scope>compile</scope>
    </dependency>

    <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct</artifactId>
        <version>${org.mapstruct.version}</version>
    </dependency>

<!--    <dependency>-->
<!--        <groupId>io.prometheus</groupId>-->
<!--        <artifactId>simpleclient_common</artifactId>-->
<!--        <version>0.16.0</version> &lt;!&ndash; parse exporter metric &ndash;&gt;-->
<!--    </dependency>-->

    <dependency>
        <groupId>org.mybatis.spring.boot</groupId>
        <artifactId>mybatis-spring-boot-starter-test</artifactId>
        <version>2.2.0</version>
    </dependency>
    <!-- kubernetes client of fabric-->
    <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-client</artifactId>
<!--            upgrade from 5.4 https://github.com/fabric8io/kubernetes-client/issues/3561 -->
        <version>${fabric.version}</version>
    </dependency>
    <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp</artifactId>
        <version>${okhttp.version}</version>
    </dependency>
    <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>logging-interceptor</artifactId>
        <version>${okhttp.version}</version>
    </dependency>
    <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>1.2.83</version>
    </dependency>
    <!-- ES-->
    <dependency>
        <groupId>org.elasticsearch</groupId>
        <artifactId>elasticsearch</artifactId>
        <version>7.4.2</version>
    </dependency>
    <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>elasticsearch-rest-client</artifactId>
        <version>7.4.2</version>
        <scope>compile</scope>
    </dependency>
<!--        <dependency>-->
<!--            <groupId>co.elastic.clients</groupId>-->
<!--            <artifactId>elasticsearch-java</artifactId>-->
<!--            <version>8.12.0</version>-->
<!--        </dependency>-->
    <!-- Java High Level REST Client-->
    <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>elasticsearch-rest-high-level-client</artifactId>
        <version>7.4.2</version>
    </dependency>
    <!-- apache beanutils-->
    <dependency>
        <groupId>commons-beanutils</groupId>
        <artifactId>commons-beanutils</artifactId>
        <version>1.9.4</version>
    </dependency>
    <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>4.5.1</version>
        <scope>compile</scope>
    </dependency>
    <!-- quartz -->
    <dependency>
        <groupId>org.quartz-scheduler</groupId>
        <artifactId>quartz</artifactId>
        <version>2.3.2</version>
    </dependency>
    <dependency>
        <groupId>org.quartz-scheduler</groupId>
        <artifactId>quartz-jobs</artifactId>
        <version>2.3.2</version>
    </dependency>

    <!-- ssh2 -->
    <dependency>
        <groupId>com.trilead</groupId>
        <artifactId>trilead-ssh2</artifactId>
        <version>1.0.0-build217</version>
    </dependency>

    <dependency>
        <groupId>ch.ethz.ganymed</groupId>
        <artifactId>ganymed-ssh2</artifactId>
        <version>262</version>
    </dependency>
    <dependency>
        <groupId>commons-dbutils</groupId>
        <artifactId>commons-dbutils</artifactId>
        <version>1.7</version>
    </dependency>

    <!-- https://github.com/alibaba/transmittable-thread-local-->
    <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>transmittable-thread-local</artifactId>
        <version>2.12.0</version>
    </dependency>

    <!-- secret -->
    <dependency>
        <groupId>com.github.ulisesbocchio</groupId>
        <artifactId>jasypt-spring-boot-starter</artifactId>
        <version>1.18</version>
    </dependency>

    <dependency>
        <groupId>com.github.promeg</groupId>
        <artifactId>tinypinyin</artifactId>
        <version>2.0.3</version>
    </dependency>
    <!--<dependency>-->
    <!--<groupId>org.junit.jupiter</groupId>-->
    <!--<artifactId>junit-jupiter</artifactId>-->
    <!--<version>5.9.0</version>-->
    <!--<scope>test</scope>-->
    <!--</dependency>-->

    <!-- https://mvnrepository.com/artifact/com.github.docker-java/docker-java-core -->
    <dependency>
        <groupId>com.github.docker-java</groupId>
        <artifactId>docker-java-core</artifactId>
        <version>3.2.13</version>
    </dependency>
    <!-- https://mvnrepository.com/artifact/com.github.docker-java/docker-java-transport-httpclient5 -->
    <dependency>
        <groupId>com.github.docker-java</groupId>
        <artifactId>docker-java-transport-httpclient5</artifactId>
        <version>3.2.13</version>
    </dependency>

    <!--kafka-->
    <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka_2.11</artifactId>
        <version>1.0.0</version>
    </dependency>

    <!--  rocketmq  -->
    <dependency>
        <groupId>org.apache.rocketmq</groupId>
        <artifactId>rocketmq-tools</artifactId>
        <version>4.9.3</version>
        <exclusions>
            <exclusion>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
            </exclusion>
        </exclusions>
    </dependency>

    <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-text</artifactId>
        <version>1.9</version>
    </dependency>


    <!--mongo-->
    <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongo-java-driver</artifactId>
        <version>3.9.0</version>
    </dependency>
    <!--redis-->
    <dependency>
        <groupId>redis.clients</groupId>
        <artifactId>jedis</artifactId>
        <version>3.3.0</version>
    </dependency>

    <dependency>
        <groupId>com.clickhouse</groupId>
        <artifactId>clickhouse-jdbc</artifactId>
        <version>0.4.2</version>
    </dependency>

    <!--  swagger  -->
    <dependency>
        <groupId>com.spring4all</groupId>
        <artifactId>swagger-spring-boot-starter</artifactId>
        <version>1.9.1.RELEASE</version>
    </dependency>
    <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>generator-annotations</artifactId>
        <version>6.12.0</version>
    </dependency>

    <dependency>
        <groupId>com.moandjiezana.toml</groupId>
        <artifactId>toml4j</artifactId>
        <version>0.7.2</version>
    </dependency>

    <!-- websocket -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-websocket</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-webflux</artifactId> <!-- Provides reactive WebSocket support -->
    </dependency>
    <!-- s3 client -->
    <dependency>
        <groupId>io.minio</groupId>
        <artifactId>minio</artifactId>
        <version>8.5.17</version>
    </dependency>


</dependencies>

<repositories>
    <repository>
        <id>spring-milestones</id>
        <name>Spring Milestones</name>
        <url>https://repo.spring.io/milestone</url>
        <snapshots>
            <enabled>false</enabled>
        </snapshots>
    </repository>
</repositories>

<build>
    <finalName>cloud-service</finalName>
    <plugins>
        <!-- 依赖抽取-->

        <!--<plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-dependency-plugin</artifactId>-->
<!--                <version>3.2.0</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>copy-dependencies</id>-->
<!--                        <phase>package</phase>-->
<!--                        <goals>-->
<!--                            <goal>copy-dependencies</goal>-->
<!--                        </goals>-->
<!--                        <configuration>-->
<!--                            <outputDirectory>${project.build.directory}/lib</outputDirectory>-->
<!--                            <overWriteReleases>false</overWriteReleases>-->
<!--                            <overWriteSnapshots>false</overWriteSnapshots>-->
<!--                            <overWriteIfNewer>true</overWriteIfNewer>-->
<!--                            <excludeTransitive>false</excludeTransitive>-->
<!--                        </configuration>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--            </plugin>-->
<!--            <plugin>-->
<!--                <groupId>org.sonarsource.scanner.maven</groupId>-->
<!--                <artifactId>sonar-maven-plugin</artifactId>-->
<!--                <version>3.3.0.603</version>-->
<!--            </plugin>-->
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jar-plugin</artifactId>
            <configuration>
                <excludes>
                    <exclude>*.yml</exclude>
                    <exclude>*.xml</exclude>
                    <exclude>*.properties</exclude>
                    <exclude>kubernetes/</exclude>
                    <exclude>grafana-dashboard/</exclude>
                </excludes>
                <archive>
                    <addMavenDescriptor>false</addMavenDescriptor>
                </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <mainClass>cn.newdt.cloud.CloudApplication</mainClass>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <compilerArgs>
                        <arg>-Xlint:deprecation</arg>
                    </compilerArgs>
                    <compilerArguments>
                        <bootclasspath>${java.home}/lib/rt.jar${path.separator}${java.home}/lib/jce.jar</bootclasspath>
                    </compilerArguments>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>java-generator-maven-plugin</artifactId>
                <version>6.12.0</version>
                <executions>
                    <execution>
                        <goals>
<!--                            <goal>generate</goal>-->
                        </goals>
                    </execution>
                </executions>
                <configuration>
<!--                    <source>src/main/resources/kubernetes/flinksessionjobs.flink.apache.org.yaml</source>-->
                    <source>src/main/resources/kubernetes/mysql.cloud.shindata.com.yaml</source>
                    <target>src/main/java</target>
                    <existingJavaTypes>
                        <com.pingcap.v1alpha1.tidbclusterspec.tidb.AdditionalContainers>
                            io.fabric8.kubernetes.api.model.Container
                        </com.pingcap.v1alpha1.tidbclusterspec.tidb.AdditionalContainers>
                        <com.pingcap.v1alpha1.tidbclusterspec.pd.AdditionalContainers>
                            io.fabric8.kubernetes.api.model.Container
                        </com.pingcap.v1alpha1.tidbclusterspec.pd.AdditionalContainers>
                         <com.pingcap.v1alpha1.tidbclusterspec.tikv.AdditionalContainers>
                            io.fabric8.kubernetes.api.model.Container
                        </com.pingcap.v1alpha1.tidbclusterspec.tikv.AdditionalContainers>

                        <com.pingcap.v1alpha1.tidbclusterspec.tidb.AdditionalVolumeMounts>
                            io.fabric8.kubernetes.api.model.VolumeMount
                        </com.pingcap.v1alpha1.tidbclusterspec.tidb.AdditionalVolumeMounts>
                        <com.pingcap.v1alpha1.tidbclusterspec.pd.AdditionalVolumeMounts>
                            io.fabric8.kubernetes.api.model.VolumeMount
                        </com.pingcap.v1alpha1.tidbclusterspec.pd.AdditionalVolumeMounts>
                        <com.pingcap.v1alpha1.tidbclusterspec.tikv.AdditionalVolumeMounts>
                            io.fabric8.kubernetes.api.model.VolumeMount
                        </com.pingcap.v1alpha1.tidbclusterspec.tikv.AdditionalVolumeMounts>

                        <com.pingcap.v1alpha1.tidbclusterspec.tidb.AdditionalVolumes>
                            io.fabric8.kubernetes.api.model.Volume
                        </com.pingcap.v1alpha1.tidbclusterspec.tidb.AdditionalVolumes>
                        <com.pingcap.v1alpha1.tidbclusterspec.pd.AdditionalVolumes>
                            io.fabric8.kubernetes.api.model.Volume
                        </com.pingcap.v1alpha1.tidbclusterspec.pd.AdditionalVolumes>
                        <com.pingcap.v1alpha1.tidbclusterspec.tikv.AdditionalVolumes>
                            io.fabric8.kubernetes.api.model.Volume
                        </com.pingcap.v1alpha1.tidbclusterspec.tikv.AdditionalVolumes>
                        <com.shindata.kafka.v1.kafkaspec.mm2.Resources>
                            io.fabric8.kubernetes.api.model.ResourceRequirements
                        </com.shindata.kafka.v1.kafkaspec.mm2.Resources>
                        <com.shindata.kafka.v1.kafkastatus.spec.mm2.Resources>
                            io.fabric8.kubernetes.api.model.ResourceRequirements
                        </com.shindata.kafka.v1.kafkastatus.spec.mm2.Resources>
                        <com.shindata.kafka.v1.kafkaspec.mm2.clusters.SecurityRef>
                            com.shindata.kafka.v1.kafkaspec.Secret
                        </com.shindata.kafka.v1.kafkaspec.mm2.clusters.SecurityRef>
                        <com.shindata.kafka.v1.kafkastatus.spec.mm2.clusters.SecurityRef>
                            com.shindata.kafka.v1.kafkaspec.Secret
                        </com.shindata.kafka.v1.kafkastatus.spec.mm2.clusters.SecurityRef>
                        <com.shindata.kafka.v1.kafkaspec.zookeeper.Storage>
                            com.shindata.kafka.v1.kafkaspec.Storage
                        </com.shindata.kafka.v1.kafkaspec.zookeeper.Storage>
                        <com.shindata.kafka.v1.kafkaspec.zookeeper.Secret>
                            com.shindata.kafka.v1.kafkaspec.Secret
                        </com.shindata.kafka.v1.kafkaspec.zookeeper.Secret>
                        <com.shindata.kafka.v1.kafkaspec.zookeeper.Schedule>
                            com.shindata.kafka.v1.kafkaspec.Schedule
                        </com.shindata.kafka.v1.kafkaspec.zookeeper.Schedule>
                        <com.shindata.kafka.v1.kafkaspec.zookeeper.Filebeat>
                            com.shindata.kafka.v1.kafkaspec.Filebeat
                        </com.shindata.kafka.v1.kafkaspec.zookeeper.Filebeat>
                        <com.shindata.kafka.v1.kafkastatus.spec.zookeeper.Storage>
                            com.shindata.kafka.v1.kafkaspec.Storage
                        </com.shindata.kafka.v1.kafkastatus.spec.zookeeper.Storage>
                        <com.shindata.kafka.v1.kafkastatus.spec.zookeeper.Secret>
                            com.shindata.kafka.v1.kafkaspec.Secret
                        </com.shindata.kafka.v1.kafkastatus.spec.zookeeper.Secret>
                        <com.shindata.kafka.v1.kafkastatus.spec.zookeeper.Schedule>
                            com.shindata.kafka.v1.kafkaspec.Schedule
                        </com.shindata.kafka.v1.kafkastatus.spec.zookeeper.Schedule>
                        <com.shindata.kafka.v1.kafkastatus.spec.zookeeper.Filebeat>
                            com.shindata.kafka.v1.kafkaspec.Filebeat
                        </com.shindata.kafka.v1.kafkastatus.spec.zookeeper.Filebeat>
                        <com.shindata.redis.v1.redisstatus.Spec>
                            com.shindata.redis.v1.RedisSpec
                        </com.shindata.redis.v1.redisstatus.Spec>
                        <com.shindata.redis.v1.sentinelstatus.Spec>
                            com.shindata.redis.v1.SentinelSpec
                        </com.shindata.redis.v1.sentinelstatus.Spec>
                        <com.shindata.redis.v1.redisspec.schedule.NodeAffinity>
                            io.fabric8.kubernetes.api.model.NodeAffinity
                        </com.shindata.redis.v1.redisspec.schedule.NodeAffinity>
                        <com.shindata.redis.v1.redisspec.schedule.Tolerations>
                            io.fabric8.kubernetes.api.model.Toleration
                        </com.shindata.redis.v1.redisspec.schedule.Tolerations>
<!--                        <com.shindata.redis.v1.redisclusterspec.schedule.NodeAffinity>-->
<!--                            io.fabric8.kubernetes.api.model.NodeAffinity-->
<!--                        </com.shindata.redis.v1.redisclusterspec.schedule.NodeAffinity>-->
<!--                        <com.shindata.redis.v1.redisclusterspec.schedule.Tolerations>-->
<!--                            io.fabric8.kubernetes.api.model.Toleration-->
<!--                        </com.shindata.redis.v1.redisclusterspec.schedule.Tolerations>-->
                        <com.shindata.redis.v1.sentinelspec.schedule.NodeAffinity>
                            io.fabric8.kubernetes.api.model.NodeAffinity
                        </com.shindata.redis.v1.sentinelspec.schedule.NodeAffinity>
                        <com.shindata.redis.v1.sentinelspec.schedule.Tolerations>
                            io.fabric8.kubernetes.api.model.Toleration
                        </com.shindata.redis.v1.sentinelspec.schedule.Tolerations>
<!--                        redis cluster shake share with redis-->
                        <com.shindata.redis.v1.redisclusterspec.RedisShake>
                            com.shindata.redis.v1.redisspec.RedisShake
                        </com.shindata.redis.v1.redisclusterspec.RedisShake>
                        <com.shindata.redis.v1.redisclusterstatus.spec.RedisShake>
                            com.shindata.redis.v1.redisspec.RedisShake
                        </com.shindata.redis.v1.redisclusterstatus.spec.RedisShake>
<!--                        mysql ha & mgr of sts version-->
                        <com.shindata.cloud.v1.mysqlstatus.Spec>
                            com.shindata.cloud.v1.MySQLSpec
                        </com.shindata.cloud.v1.mysqlstatus.Spec>
                        <com.shindata.cloud.v1.mysqlspec.Options>
                            com.shindata.common.spec.Options
                        </com.shindata.cloud.v1.mysqlspec.Options>
                        <com.shindata.cloud.v1.mysqlspec.Entries>
                            com.shindata.common.spec.Entries
                        </com.shindata.cloud.v1.mysqlspec.Entries>
                        <com.shindata.cloud.v1.mysqlspec.Secrets>
                            com.shindata.common.spec.Secrets
                        </com.shindata.cloud.v1.mysqlspec.Secrets>
                    </existingJavaTypes>
                    <generatedAnnotations>false</generatedAnnotations>
<!--                    <extraAnnotations>false</extraAnnotations>-->
                </configuration>
            </plugin>
            <!-- https://mvnrepository.com/artifact/org.mybatis.generator/mybatis-generator-maven-plugin -->
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.4.0</version>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>false</overwrite>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>8.0.11</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>2.2.3</version>
            </plugin>
        </plugins>
    </build>

</project>