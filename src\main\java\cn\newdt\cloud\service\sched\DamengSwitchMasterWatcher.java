package cn.newdt.cloud.service.sched;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.StatusConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.ResourceChangeHis;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.sched.impl.DamengResourceWatch;
import cn.newdt.cloud.service.sched.impl.OpsProcessorContext;
import cn.newdt.cloud.utils.JsonUtil;
import cn.newdt.commons.exception.CustomException;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

import static cn.newdt.cloud.constant.ScheduleConstant.JOB_DATA_KEY_CHANGE_ID;

@Slf4j
public class DamengSwitchMasterWatcher extends DamengResourceWatch {

    @Override
    public OpsResultDTO postProcess(TriggerHis triggerHis) throws Exception {
        ResourceChangeHis his = resourceChangeHisService.get(Integer.parseInt(triggerHis.getJobDataMap().get(JOB_DATA_KEY_CHANGE_ID)));
        Map<String, Object> mutableDataMap = his.mutableDataMap();
        Map<String, String> jobDataMap = triggerHis.returnMergedJobDataMap();
        String appIdData = jobDataMap.get("appId");
        String newSourceIP = jobDataMap.get("newSourceIP");
        int appId = Integer.parseInt(appIdData);
        CloudApp app = appService.get(appId);
        KubeClient client = kubeClientService.get(app.getKubeId());

        OpsResultDTO.Builder status = OpsResultDTO.builder().stopJob(false);
        List<PodDTO> pods = client.listPod(app.getNamespace(), AppKind.Dameng.labelOfPod(app));
        if (scriptExecuted(client, newSourceIP, pods).run(app, mutableDataMap))
            if (labelChanged(newSourceIP, pods).run(app, mutableDataMap)) {
                status = OpsResultDTO.builder().stopJob(true).status(StatusConstant.SUCCESS);
            }
        his.setDataMap(JsonUtil.toJson(mutableDataMap));
        resourceChangeHisService.update(his);
        status.msg(mutableDataMap.toString());
        if (status.isStopped())
            appService.handleWatchResult(app.getId(), status.isSuccessful());

        return status.build();
    }

    // refact invoke super.evalOpsResult
    private Step labelChanged(String newSourceIP, List<PodDTO> pods) {
        return new Step() {
            @Override
            public String markName() {
                return "Check role";
            }

            @Override
            public boolean execute(CloudApp app) {
                PodDTO newSource = pods.stream()
                        .filter(i -> newSourceIP.equals(i.getPodIp())).findFirst().orElseThrow(() -> new RuntimeException("new source not found"));
                if (CloudAppConstant.ROLE_PRIMARY.equals(newSource.getLabel(CloudAppConstant.CustomLabels.ROLE)))
                    if (pods.stream().anyMatch(pod -> CloudAppConstant.ROLE_STANDBY
                            .equals(pod.getLabel(CloudAppConstant.CustomLabels.ROLE)))) {
                        return true;
                    } else {
                        log.info("standby instance not found yet");
                    }
                else
                    log.info("new source role is not primary yet");
                return false;
            }
        };
    }

    Step scriptExecuted(KubeClient client, String newSourceIP, final List<PodDTO> pods) {
        return new Step() {
            @Override
            public String markName() {
                return "Execute switch";
            }

            @Override
            public boolean execute(CloudApp app) {
                PodDTO standbyPod = pods.stream()
                        .filter(i -> newSourceIP.equals(i.getPodIp()) && CloudAppConstant.ROLE_STANDBY
                                .equals(i.getLabels().get(CloudAppConstant.CustomLabels.ROLE)))
                        .findAny().orElseThrow(() -> new CustomException(600, "此实例不是从节点"));

                String[] podNameArr = standbyPod.getPodName().split("-");
                String podCode = podNameArr[podNameArr.length - 2];
                String podCodeUp = podCode.toUpperCase();
                //获取monitor节点ip
                PodDTO monitorPod = pods.stream().
                        filter(i -> "monitor".equals(i.getLabels().get("app.kubernetes.io/component"))).
                        findAny().orElseThrow(() -> new CustomException(600, "未找到monitor节点"));
                String monitorPodIp = monitorPod.getPodIp();
                //执行切主脚本
                String cmd = "/scripts/switchover.sh --monitorip " + monitorPodIp + " --switchmode switchover --switchnode DM_" + podCodeUp;
                try {
                    client.execCmd(app.getNamespace(), monitorPod.getPodName(), "dm", "sh", "-c", cmd);
                    return true;
                } catch (Exception e) {

                    return false;
                }
            }
        };
    }

    interface Step {
        String markName();

        boolean execute(CloudApp app);

        default boolean isDone(Map<String, Object> rsDataMap) {
            return Boolean.parseBoolean(String.valueOf(rsDataMap.getOrDefault(markName(), false)));
        }

        default void done(Map<String, Object> rsDataMap, boolean success) {
            rsDataMap.put(markName(), success);
        }

        default boolean run(CloudApp app, Map<String, Object> rsDataMap) {
            if (!isDone(rsDataMap)) {
                boolean done = execute(app);
                done(rsDataMap, done);
                return done;
            }
            return true;
        }
    }
}
