package cn.newdt.cloud.service.mysql;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.CloudAppConfig;
import cn.newdt.cloud.domain.CloudAppLogic;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.vo.GenericSpec;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.fabric8.kubernetes.client.CustomResource;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter @Setter
public class OperationOptionContext {
    private ActionEnum actionEnum;
    /**
     * 用户请求的该操作的spec
     * todo 不同操作有不同的 request spec.
     */
    private GenericSpec opsRequestSpec;

    @JsonIgnore
    private KubeClient kubeClient;

    @Setter
    private CloudApp app;
    @Setter
    private CloudAppConfig appConfig;

    @Setter
    @JsonIgnore
    private CustomResource cr;
    @Setter
    private CloudAppLogic logicApp;

    // 其他上下文属性，不同步骤间传递
    Map<String, Object> additionTransmitContext;

    public OperationOptionContext(ActionEnum actionEnum, GenericSpec opsRequestSpec, KubeClient kubeClient) {
        this.actionEnum = actionEnum;
        this.opsRequestSpec = opsRequestSpec;
        this.kubeClient = kubeClient;
        this.additionTransmitContext = new HashMap<>();
    }

    public OperationOptionContext() {
    }
}
