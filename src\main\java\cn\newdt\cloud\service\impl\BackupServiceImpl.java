package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.domain.dmp.BinlogBackupHis;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.filter.ResourceView;
import cn.newdt.cloud.mapper.BackupMapper;
import cn.newdt.cloud.mapper.BackupTimerMapper;
import cn.newdt.cloud.mapper.RestoreMapper;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.BackupHisVO;
import cn.newdt.cloud.vo.CloudAppVO;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.UserUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.DatasourceConstant.*;

@Slf4j
@Service
public class BackupServiceImpl implements BackupService {

    @Autowired
    private BackupMapper backupMapper;

    @Autowired @Lazy
    private BackupUtil backupUtil;

    @Autowired
    private RestoreMapper restoreMapper;

    @Autowired
    private CloudAppService appService;

    @Autowired
    private KubeConfigService kubeConfigService;

    @Autowired
    private CloudFTPUtil ftpUtil;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private SysConfigSyncService sysConfigSyncService;

    @Autowired
    private BackupTimerService backupTimerService;

    @Autowired
    private CloudAppService cloudAppService;

    @Autowired
    private BackupStorageUtils backupStorageUtils;


    /**
     * 以两种方式恢复. 1. 选备份文件恢复 2. 按时间点恢复(自动选择备份文件)
     * 1. 从备份历史选择或直接提供备份文件的ftp地址. 后者要指定备份类型.
     *    backupHisId != null时从备份历史选
     *    ftpPath != null 时从ftp服务器选
     * 2. restoreTime != null时按时间点恢复, 要指定backupAppId todo
     * @param backupHisId nullable
     * @param dbType 数据库类型
     * @param appId 目标id
     * @param restoreTime 指定恢复时间(binlog)找最近的backupHisId
     * @param backupAppId 指定恢复时间时 指定备份源id
     */
    @Override
    public void restore(String dbType, Integer appId, Integer backupHisId, String restoreTime, Integer backupAppId, String backupFile, String backupType) {
        //校验参数
        if(appId == null){
            throw new CustomException(600, "执行恢复目标应用id为空！");
        }
        //判断应用是否已经被删除
        CloudApp cloudApp = appService.get(appId);
        Boolean deleted = cloudApp.getDeleted();
        if(deleted){
            log.error("应用已被删除！appId：" + appId);
            throw new CustomException(600, "应用已被删除！");
        }
        cloudAppService.appCanToAction(cloudApp, ActionEnum.RESTORE);
        //根据appId判断当前应用是否有正在执行的恢复，有则返回提示信息
        RestoreHis runningByAppId = restoreMapper.getRunningByAppId(SCHEMA, CLOUD_RESTORE_HIS, appId);
        if (runningByAppId != null) {
            throw new CustomException(600, "存在正在执行的恢复，请勿重复提交！");
        }

        //判断是否为binlog恢复，如果有恢复时间，则为binlog恢复
        if(null != restoreTime){
            Map<String, Object> lastRestoreTime = backupUtil.getLastRestoreTime(appId);
            AppKind appKind = AppKind.valueOf(cloudApp.getKind(), cloudApp.getArch());
            if ((appKind == AppKind.MYSQL_HA || appKind == AppKind.MongoDB_Cluster) &&
                    ObjectUtils.isEmpty(lastRestoreTime.get("startTime")) && ObjectUtils.isEmpty(lastRestoreTime.get("endTime"))) {
                throw new CustomException(600, "至少做过两次备份才能根据时间恢复！");
            }
            if(backupAppId == null){
                throw new CustomException(600, "执行恢复源应用id为空！");
            }
            //根据恢复时间查询之前最近的一次备份历史
            String restoreTimeStr = restoreTime.replaceAll("_", " ").replaceAll("T", " ").replaceAll("Z", "");
            BackupHis closeBackupHis = backupMapper.getCloseBackupHisBeforeRestoreTime(SCHEMA, CLOUD_BACKUP_HIS, backupAppId, restoreTimeStr);
            if(null == closeBackupHis){
                //不存在恢复历史,原因可能为时间插件选择时分秒部分，比备份时间更早
                throw new CustomException(600, "备份历史不存在，恢复时间点应在首次备份时间后  ！");
            }
            backupHisId = closeBackupHis.getBackupHisId();
        }

        CloudApp app = cloudApp;
//        if (AppKind.Redis_Cluster.equals(AppKind.valueOf(app.getKind(), app.getArch()))){
//            dbType = dbType+"-cluster";
//        }
//        if (AppKind.MongoDB_Cluster.equals(AppKind.valueOf(app.getKind(), app.getArch()))){
//            dbType = dbType+"-cluster";
//        }
        //获取备份历史
        BackupHis backupHis = null;
        if (backupHisId != null) {
            backupHis = backupMapper.getBackupHisById(SCHEMA, CLOUD_BACKUP_HIS, backupHisId);
        }

        //放入用户信息
        UserUtil.setAsyncUserInfo(UserUtil.getCurrentUser());
        // 校验备份存储剩余空间是否足够恢复，以及数据存储空间是否足够备份文件代表的数据文件大小
        // 在各自的restore执行时校验

        AppKindService appKindService = AppServiceLoader.getInstance(AppKind.valueOf(app.getKind(), app.getArch()));
        appKindService.restore(backupHis, appId, restoreTime, backupFile, backupType);

        // AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
        // if (appKind == AppKind.MYSQL_MGR)
        //     backupUtil.mgrRestore(backupHis,appId,restoreTime);
        // else if (appKind == AppKind.MYSQL_HA) {
        //     backupUtil.restore(backupHis, appId, restoreTime, backupFile, backupType);
        // } else if (appKind == AppKind.MongoDB) {
        //     backupUtil.mongoRestore(backupHis,appId);
        // } else if (appKind == AppKind.Redis) {
        //     backupUtil.redisRestore(backupHis,appId);
        // } else if (appKind == AppKind.OpenGauss) {
        //     backupUtil.ogRestore(backupHis,appId);
        // } else if (appKind == AppKind.Redis_Cluster) {
        //     backupUtil.redisClusterRestore(backupHis,appId);
        // } else if (appKind == AppKind.Elasticsearch) {
        //     backupUtil.elasticSearchRestore(backupHis, appId);
        // } else if (appKind == AppKind.PostgreSQL) {
        //     backupUtil.postgresqlRestore(backupHis, appId);
        // } else if (appKind == AppKind.MongoDB_Cluster) {
        //     backupUtil.mongodbClusterRestore(backupHis, appId, restoreTime);
        // }else if (appKind == AppKind.Flink){
        //     backupUtil.flinkRestore(backupHis, appId, restoreTime);
        // } else if (appKind == AppKind.Clickhouse) {
        //     backupUtil.clickhouseClusterRestore(backupHis, appId, restoreTime);
        // } else if (appKind == AppKind.Vastbase) {
        //     backupUtil.vastbaseRestore(backupHis, appId);
        // } else if (appKind == AppKind.Dameng){
        //     backupUtil.damengRestore(backupHis, appId, restoreTime);
        // } else if (appKind == AppKind.TIDB) {
        //     backupUtil.tidbRestore(backupHis, appId, restoreTime);
        // } else {
        //     throw new CustomException(500, "当前数据库类型未存在恢复操作！");
        // }
    }

    /**
     * 根据类型进行手动备份
     * @param backupHis
     * @return
     * @throws Exception
     */
    @Override
    public BackupHis manualBackup(BackupHisVO backupHis, String dbType) {
        // 0. 判断应用是否已经被删除
        CloudApp app = appService.get(backupHis.getAppId());
        Boolean deleted = app.getDeleted();
        if(deleted){
            log.error("应用已被删除！appId：" + backupHis.getAppId());
            throw new CustomException(600, "应用已被删除！");
        }
        cloudAppService.appCanToAction(app, ActionEnum.BACKUP);
        // 1. 根据appId判断当前应用是否有正在执行的备份，有则返回提示信息
        BackupHis runningBackupHis = backupMapper.getRunningByAppId(SCHEMA, CLOUD_BACKUP_HIS, backupHis.getAppId());
        if (runningBackupHis != null) {
            throw new CustomException(600, "存在正在执行的备份，请勿重复提交！");
        }

        //设置备份类型为“手动”
        backupHis.setPerformType(ScheduleConstant.MANUAL);

        //放置用户信息
        UserUtil.setAsyncUserInfo(UserUtil.getCurrentUser());
        // 检查备份存储空间是否足够，仅记录作参考用，记录在操作历史
        Map messageObj = new HashMap();
//        backupHis.setMessage(backupUtil.serialize(backupUtil.checkBackup(backupHis.getAppId())));
        String messageJsonStr = JsonUtil.toJson(messageObj);
        backupHis.setMessage(messageJsonStr);
        // 2. 根据数据库类型进行备份
        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
        if (appKind == AppKind.MYSQL_MGR) {
            backupHis.setBackupDbLog(true);
            backupUtil.mgrBackup(backupHis);
        } else if (appKind == AppKind.MYSQL_HA) {
            backupHis.setBackupDbLog(true);
            backupUtil.performBackup(backupHis);
        } else if (appKind == AppKind.MongoDB) {
            backupHis.setBackupDbLog(false);
            backupUtil.mongoBackup(backupHis);
        } else if (appKind == AppKind.Redis) {
            backupHis.setBackupDbLog(false);
            backupUtil.redisBackup(backupHis);
        } else if (appKind == AppKind.OpenGauss) {
            backupHis.setBackupDbLog(false);
            backupUtil.ogBackup(backupHis);
        } else if (appKind == AppKind.Redis_Cluster) {
            backupHis.setBackupDbLog(false);
            backupUtil.redisClusterBackup(backupHis);
        } else if (appKind == AppKind.Elasticsearch) {
            backupHis.setBackupDbLog(false);
            backupUtil.elasticSearchBackup(backupHis);
        } else if (appKind == AppKind.MongoDB_Cluster) {
            backupHis.setBackupDbLog(false);
            backupUtil.mongodbClusterBackup(backupHis);
        } else if (appKind == AppKind.PostgreSQL) {
            backupHis.setBackupDbLog(false);
            backupUtil.postgresqlBackup(backupHis);
        } else if (appKind == AppKind.Flink) {
            backupHis.setBackupDbLog(false);
            backupUtil.flinkBackup(backupHis);
        } else if (appKind == AppKind.Clickhouse) {
            backupHis.setBackupDbLog(false);
            backupUtil.clickHouseBackup(backupHis);
        } else if (appKind == AppKind.Dameng) {
            backupHis.setBackupDbLog(true);
            backupUtil.damengBackup(backupHis);
        } else if (appKind == AppKind.TIDB) {
            backupUtil.tidbBackup(backupHis);
        } else if (appKind == AppKind.Vastbase) {
            backupHis.setBackupDbLog(true);
            backupUtil.vastbaseBackup(backupHis);
        } else {
            throw new CustomException(500, "当前数据库类型未存在备份操作！");
        }
        return backupHis;
    }


    @Override
    public Optional<BackupHis> latestSince(Integer id, LocalDateTime sinceTime) {
        return Optional.ofNullable(backupMapper.latestSince(SCHEMA, CLOUD_BACKUP_HIS, id, sinceTime));
    }

    @Override
    public List<String> listFtpBackupFiles(String path) {
        try {
            if (StringUtils.isEmpty(path)) {
                path = backupUtil.getStoreDir();
            }
            return ftpUtil.listFiles(path, null);
        } catch (Exception e) {
            log.error("", e);
            return Collections.emptyList();
        }
    }

    @Override
    public Timestamp getLastRestoreTime(Integer appId) {
        BinlogBackupHis binlogBackupHis = backupMapper.getLastRestoreTime(UserUtil.getSchema(), DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, appId);
        if(null != binlogBackupHis){
            return binlogBackupHis.getStartTime();
        }else{
            return null;
        }
    }

    @Override
    public Timestamp getFirstRestoreTime(Integer appId) {
        BinlogBackupHis binlogBackupHis = backupMapper.getFirstRestoreTime(UserUtil.getSchema(), DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, appId);
        if(null != binlogBackupHis){
            return binlogBackupHis.getStartTime();
        }else{
            return null;
        }
    }

//    @Override
//    public List<CloudAppVO> findHaveBackupAppList(String kubeName) {
//        List<CloudAppVO> appList = backupMapper.findHaveBackupAppList(SCHEMA, CLOUD_BACKUP_HIS, kubeName);
//        return appList;
//    }

    @Override
    public HashMap<String, Object> searchPage(PageDTO page) {
        Integer kubeId = Integer.valueOf(page.getCondition().get("kubeId").toString());
        String kubeName = kubeConfigService.get(kubeId).getName();
        kubeConfigService.get(kubeId).getName();
        //记录分页规则
        Integer offset = null == page.getPageNum() ? 0 : page.getPageNum();
        Integer limit = null == page.getPageSize() ? 15 : page.getPageSize();
        page.setPageNum(0);
        page.setPageSize(null);
//        page.getCondition().put("kind", "MySQL");
        PageInfo<CloudAppVO> cloudAppVOPageInfo = appService.searchPage(page);
        List<CloudAppVO> appList = cloudAppVOPageInfo.getList();
        //查询备份过的应用id
        List<Integer> appIdList = backupMapper.getAppListHaveBackupByKubeName(SCHEMA, CLOUD_BACKUP_HIS, kubeName);
        appList.stream().filter(everyApp -> appIdList.contains(everyApp.getId())).collect(Collectors.toList());
        //进行分页

        PageHelper.offsetPage(null == offset?0:offset, null == limit?15:limit);
        //总条数
        int total = appList.size();
        //开始条数
        int startIndex = null == offset ? 0 : offset;
        //结束条数
        int endIndex = Math.min(startIndex + limit, total);
        //添加数据到page对象
        List<CloudAppVO> pageList = appList.subList(startIndex, endIndex);

        // 5.构造前端需要的结构
        HashMap<String, Object> resMap = new HashMap<>();
        resMap.put("total",appList.size());
        resMap.put("list",pageList);
        return resMap;
    }

//    /**
//     * 按时间点恢复
//     * @param backupHisId
//     * @param appIdStr
//     * @param restoreTime
//     */
//    @Override
//    public void restoreByTime(Integer backupHisId, String appIdStr, String restoreTime) {
//        //校验参数
//        if(StringUtils.isEmpty(appIdStr)){
//            throw new CustomException(600, "执行恢复目标应用id为空！");
//        }
//        Integer appId = Integer.valueOf(appIdStr);
//
//        //根据appId判断当前应用是否有正在执行的恢复，有则返回提示信息
//        RestoreHis runningByAppId = restoreMapper.getRunningByAppId(SCHEMA, CLOUD_RESTORE_HIS, appId);
//        if (runningByAppId != null) {
//            throw new CustomException(600, "存在正在执行的恢复，请勿重复提交！");
//        }
//        //获取备份历史
//        BackupHis backupHis = backupMapper.getBackupHisById(SCHEMA, CLOUD_BACKUP_HIS, backupHisId);
//        backupUtil.restore(backupHis, appId);
//    }

    @Override
    public BackupHis get(Integer id) {
        return backupMapper.getBackupHisById(SCHEMA, CLOUD_BACKUP_HIS, id);
    }

    @Override
    public Integer add(BackupHis data) {
        return backupMapper.commitBackup(SCHEMA, CLOUD_BACKUP_HIS, data);
    }

    @Override
    public Integer delete(Integer id) {
        return null;
    }

    @Override
    public Integer update(BackupHis data) {
        return backupMapper.updateBackupHis(SCHEMA, CLOUD_BACKUP_HIS, data);
    }

    @Override
    @ResourceView
    public PageInfo<BackupHis> listPage(PageDTO pageDTO) {
        List<BackupHis> results;
        // 为空查所有
        if (pageDTO.getPageNum() == null && pageDTO.getPageSize() == null && pageDTO.getCondition().get("appId") == null) {
            results = backupMapper.listByMap(SCHEMA, CLOUD_BACKUP_HIS, pageDTO.getCondition());
        } else {
            if (!ObjectUtils.isEmpty(pageDTO.getCondition().get("appLogicId"))) {
                pageDTO.getCondition().put("appLogicId", Integer.valueOf(String.valueOf(pageDTO.getCondition().get("appLogicId"))));
            }
            results = backupMapper.listByMap(SCHEMA, CLOUD_BACKUP_HIS, pageDTO.getCondition());
        }
        results.forEach(item -> {
            //放入应用版本
            String message = item.getMessage();
            if (StringUtils.isBlank(item.getVersion()) && StringUtils.isNotBlank(message) && message.contains("备份成功") && message.contains("version")) {
                String version = (String)JsonUtil.toObject(Map.class, message).get("version");
                item.setVersion(version);
            }
        });
        return new PageInfo<>(results);
    }

    /**
     * 插入binlog备份历史
     * @param binlogBackupHis
     * @return
     */
    @Override
    public Integer insertBinlogBackupHis(BinlogBackupHis binlogBackupHis) {
        int binlogBackupHisId = backupMapper.insertBinlogBackupHis(SCHEMA, DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, binlogBackupHis);
        return binlogBackupHisId;
    }

    @Override
    public BinlogBackupHis listBinlogBackupHis(Integer id) {
        BinlogBackupHis binlogBackupHis = backupMapper.listBinlogBackupHis(UserUtil.getSchema(), DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, id);
        return binlogBackupHis;
    }

    @Override
    public BinlogBackupHis getBinlogBackupHisGtStartTime(Integer appId,String startTime ) {
        BinlogBackupHis binlogBackupHis = backupMapper.getBinlogBackupHisGtStartTime(UserUtil.getSchema(), DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, appId, startTime);
        return binlogBackupHis;
    }

    @Override
    public Map<String, Map<String, Object>> checkBackup(Integer appId, PodDTO backupPod) {
        return backupUtil.checkBackup(appId);
    }

    @Override
    public Map<String, Map<String, Object>> checkRestore(Integer appId, Integer backupHisId, String restoreTime, Integer backupAppId) {
        //判断应用是否已经被删除
        Boolean deleted = appService.get(appId).getDeleted();
        if(deleted){
            log.error("应用已被删除！appId：" + appId);
            throw new CustomException(600, "应用已被删除！");
        }
        CloudApp app = appService.get(appId);
//        if (AppKind.MongoDB_Cluster.getKind().equalsIgnoreCase(app.getKind()) && AppKind.MongoDB_Cluster.getArch().equalsIgnoreCase(app.getArch())) {
//            Map<String, Map<String, Object>> result = new HashMap<>();
//            String k1 = "name", v1 = "备份存储空间";
//            Map<String, Object> cond1 = ImmutableMap.of("bool", true, k1, v1);
//            String k2 = "name", v2 = "数据存储空间";
//            Map<String, Object> cond2 = ImmutableMap.of("bool", true, k2, v2);
//            result.put("backupSizeSufficientCondition", cond1);
//            result.put("dataSizeSufficientCondition", cond2);
//            return result;
//        }
        //判断是否为binlog恢复，如果有恢复时间，则为binlog恢复
        if (null != restoreTime) {
            if (backupAppId == null) {
                throw new CustomException(600, "执行恢复源应用id为空！");
            }
            //根据恢复时间查询之前最近的一次备份历史
            String restoreTimeStr = restoreTime.replaceAll("_", " ").replaceAll("T", " ").replaceAll("Z", "");
            BackupHis closeBackupHis = backupMapper.getCloseBackupHisBeforeRestoreTime(SCHEMA, CLOUD_BACKUP_HIS, backupAppId, restoreTimeStr);
            if (null == closeBackupHis) {
                //不存在恢复历史,原因可能为时间插件选择时分秒部分，比备份时间更早
                throw new CustomException(600, "备份历史不存在，恢复时间点应在首次备份时间后  ！");
            }
            backupHisId = closeBackupHis.getBackupHisId();
        }

        CustPreconditions.checkNotNull(backupHisId, "校验失败，备份历史不存在（未传递或无法自动确认）");

        BackupHis backupHis = backupMapper.getBackupHisById(SCHEMA, CLOUD_BACKUP_HIS, backupHisId);

        return backupUtil.checkRestore(app, backupHis, null);
    }

    @Override
    public Map backupConfig() {
        String defaultConfig = sysConfigService.findOne(CloudAppConstant.SysCfgCategory.BACKUP_MANAGEMENT, "defaultConfig");
        if(!StringUtils.isBlank(defaultConfig)){
            return JsonUtil.toObject(Map.class, defaultConfig);
        } else {
            throw new CustomException(600, "未获取到备份默认配置！");
        }
    }

    @Override
    public void backupConfig(Integer backupSetRetention, Integer backupLogRetention, Integer maxBackupDuration) {
        Map dataMap = new HashMap<>();
        dataMap.put("backupSetRetention", backupSetRetention);
        dataMap.put("backupLogRetention", backupLogRetention);
        dataMap.put("maxBackupDuration", maxBackupDuration);
        CloudSysConfig cloudSysConfig = new CloudSysConfig();
        cloudSysConfig.setName("defaultConfig");
        cloudSysConfig.setCategory(CloudAppConstant.SysCfgCategory.BACKUP_MANAGEMENT);
        cloudSysConfig.setData(JsonUtil.toJson(dataMap));
        cloudSysConfig.setUpdateTime(LocalDateTime.now());
        List<CloudSysConfig> cloudSysConfigs = new ArrayList<>();
        cloudSysConfigs.add(cloudSysConfig);
        sysConfigSyncService.updateSysConfigByCategoryAndNames(cloudSysConfigs);
    }

   @Resource
    private CloudAppLogicService logicAppService;
    @Override
    @ResourceView
    public Object getMetrics(PageDTO pageDTO) {
        ImmutableMap.Builder<Object, Object> builder = ImmutableMap.builder();

        Map<String, Object> condition = pageDTO.getCondition();
        Object kind = condition.get("kind");

        HashMap<String, Object> backupCondition = new HashMap<>(condition);
        if (kind != null && StringUtils.isNotEmpty(kind + ""))
            backupCondition.put("appType", kind);
        List<BackupTimer> backupTimers = backupTimerService.listMap(backupCondition);
        Long total = getTotalOfApp(condition, kind);
        builder.put("missing_config_numbers", Math.max(total - backupTimers.size(), 0));

//        condition1.put("endTimeLow",
//                Optional.ofNullable(condition.get("endTimeLow")).orElse(LocalDate.now().minusDays(7)));
        backupCondition.put("status", StatusConstant.FAIL);
        // 分页，默认显示first 10
        PageHelper.offsetPage(Optional.ofNullable(pageDTO.getPageNum()).orElse(0),
                Optional.ofNullable(pageDTO.getPageSize()).orElse(10));
        List<BackupHis> his = backupMapper.searchLatestFailedHis(SCHEMA, CLOUD_BACKUP_HIS, backupCondition);
        long totalFailedAppNumbers = ((Page) his).getTotal();
        builder.put("total_failed_numbers", totalFailedAppNumbers).put("latest_failed_his", his);
        return builder.build();
    }

    private Long getTotalOfApp(Map<String, Object> condition, Object kind) {
        PageDTO emptyPageDto = new PageDTO();
        emptyPageDto.setCondition(new HashMap<>());
        emptyPageDto.getCondition().put("deleted", 0);
        if (condition.containsKey("ownerTenant")) {
            emptyPageDto.getCondition().put("ownerTenant", condition.get("ownerTenant"));
        }
        if (condition.containsKey("ownerUser"))
            emptyPageDto.getCondition().put("ownerUser", condition.get("ownerUser"));
        if (kind != null && kind != "")
            emptyPageDto.getCondition().put("kind", kind);
        else
            emptyPageDto.getCondition().put("excludeKinds",
                    AppKind.sideAppKindSet().stream().map(ak -> ak.getKind()).collect(Collectors.toList()));
        return logicAppService.countByMap(emptyPageDto.getCondition());
    }

    private static class Result {
        public final HashMap<String, Object> condition1;
        public final List<BackupTimer> backupTimers;

        public Result(HashMap<String, Object> condition1, List<BackupTimer> backupTimers) {
            this.condition1 = condition1;
            this.backupTimers = backupTimers;
        }
    }

    @Autowired
    private BackupTimerMapper backupTimerMapper;

    @Override
    public void stopBackupJobHisForApp(int id) {
        //为当前所有执行中的备份操作修改为失败
        Map<String, Object> condition = new HashMap<>();
        condition.put("status", "2");
        condition.put("appIdStr", id + "");
        List<BackupHis> backupHisList = backupMapper.listBackupHisByAppIdsAndStatus(UserUtil.getSchema(), DatasourceConstant.CLOUD_BACKUP_HIS, condition);
        if (!CollectionUtils.isEmpty(backupHisList)) {
            BackupHis backupHis = backupHisList.get(0);
            backupHis.setStatus("1");
            backupHis.setMessage("应用已被删除！");
            backupMapper.updateBackupHis(UserUtil.getSchema(), DatasourceConstant.CLOUD_BACKUP_HIS, backupHis);
        }
    }

    @Override
    public void commitRestoreHis(RestoreHis restoreHis) {
        restoreMapper.commitRestore(UserUtil.getSchema(), CLOUD_RESTORE_HIS, restoreHis);
    }

    @Override
    public void updateBinlog(BinlogBackupHis binlogBackupHis) {
        backupMapper.updateBinlogBackupHis(UserUtil.getSchema(), DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, binlogBackupHis);
    }

    @Override
    public List<BinlogBackupHis> listBinlogHis(Integer appId) {
        return backupMapper.listBinlogBackupHisByMap(UserUtil.getSchema(), DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, Collections.singletonMap("clusterId", appId+""));
    }

    @Override
    public BinlogBackupHis getBinlogBackupHisById(Integer binlogHisId) {
        return backupMapper.listBinlogBackupHisByMap(UserUtil.getSchema(), CLOUD_BINLOG_BACKUP_HIS, Collections.singletonMap("id", binlogHisId + ""))
                .stream().findFirst().orElse(null);
    }

    @Override
    public Object downloadBackupFile(int backupHisId) {
        //获取备份历史
        BackupHis backupHis = get(backupHisId);

        try (InputStream is = backupStorageUtils.downloadBackupFileForInputStream(backupHis)) {
            //获取压缩文件名
            CloudApp app = cloudAppService.get(backupHis.getAppId());
            String namespace = app.getNamespace();
            String crName = app.getCrName();
            AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
            String filePath = appKind.getBackupFilePath(namespace, crName, backupHis.getFileName());
            String[] pathSegments = filePath.split(File.separator);
            String fileName = pathSegments.length > 0 ? pathSegments[pathSegments.length - 1] : filePath;

            HttpHeaders headers = new HttpHeaders();
            headers.set("content-disposition", "attachment;filename=" + fileName);
            headers.set("content-type", "application/octet-stream");
            return new ResponseEntity<>(FileCopyUtils.copyToByteArray(is), headers, HttpStatus.OK);
        } catch (Exception e) {
            throw new CustomException(600, e.getMessage());
        }
    }
}
