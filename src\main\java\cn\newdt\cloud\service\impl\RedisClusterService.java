package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.common.OpLogContext;
import cn.newdt.cloud.common.TriFunction;
import cn.newdt.cloud.config.CloudRequestContext;
import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.dto.*;
import cn.newdt.cloud.dto.mapper.RedisClusterMapper;
import cn.newdt.cloud.mapper.NodePortInfoMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.service.sched.impl.*;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.*;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.bean.MetaVO;
import cn.newdt.commons.bean.MysqlParamRules;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.SymmetricEncryptionUtil;
import cn.newdt.commons.utils.UserUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import com.shindata.redis.v1.Redis;
import com.shindata.redis.v1.RedisCluster;
import com.shindata.redis.v1.RedisClusterSpec;
import com.shindata.redis.v1.redisclusterspec.Secret;
import com.shindata.redis.v1.redisclusterspec.*;
import com.shindata.redis.v1.redisclusterstatus.Shards;
import com.shindata.redis.v1.redisspec.RedisShake;
import com.shindata.redis.v1.redisspec.redisshake.Reader;
import com.sun.tools.javac.util.Pair;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.client.CustomResource;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.SchedulerException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import redis.clients.jedis.Jedis;

import javax.annotation.Nullable;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.CloudAppConstant.*;
import static cn.newdt.cloud.constant.ActionEnum.SCALE_IN;
import static cn.newdt.cloud.constant.CloudAppConstant.CustomResourceState.MAINTAINING;
import static cn.newdt.cloud.constant.CloudAppConstant.PodPhase.NOT_READY;
import static cn.newdt.cloud.constant.CloudAppConstant.PodPhase.RUNNING;
import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgCategory.PARAM_PROHIBIT;
import static cn.newdt.cloud.service.impl.RedisService.NDT_REDIS_FILEBEAT_CM;
import static cn.newdt.cloud.utils.DateUtil.parseUTC;
import static java.util.stream.Collectors.partitioningBy;
import static java.util.stream.Collectors.toList;

@Service
@Slf4j
public class RedisClusterService extends DefaultAppKindService<RedisCluster> implements
        StartStopOperation, CleanDataOperation, BigKeysOperation, ServiceManageOperation, MultiAZService<RedisCluster> {
    private final String SECRET_PASSWORD_KEY = "password";
    @Autowired
    private KubeConfigService configService;

    @Autowired
    private RedisPodService redisPodService;

    @Autowired
    private BackupUtil backupUtil;

    @Autowired
    private AccessManagementService accessManagementService;

    @Autowired
    private NodePortInfoMapper nodePortInfoMapper;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private CloudDbParamTemplateService cloudDbParamTemplateService;

    @Autowired
    private CloudAppConfigService appConfigService;

    @Autowired
    private RedisService redisService;


    @Override
    public AppKind getKind() {
        return AppKind.Redis_Cluster;
    }

    @Override
    public boolean nodePolicy() {
        return false;
    }

    @Override
    public Class<? extends OpsPostProcessor> getProcessorClass(ActionEnum action) {
        switch (action) {
            case SWITCH_AZ_ROLE:
                return RedisSwitchAZRoleWatch.class;
            case CREATE: return RedisClusterInstallWatch.class;
            case MODIFY_PARAM:
            case UPDATE_PASSWORD:
            case UPDATE: return RedisClusterWatch.class;
            case SCALE_OUT:
            case SCALE_IN:
                return RedisClusterScaleWatch.class;
            case START:
            case STOP:
            case STOP_POD:
            case START_POD:
                return RedisClusterStartStopWatch.class;
            case UPGRADE:
                return RedisClusterUpgradeWatch.class;
            case UPDATE_SERVICE:
            case DELETE_SERVICE:
            case CREATE_SERVICE:
                return RedisClusterServiceWatch.class;
            default:
                return super.getProcessorClass(action);
        }
    }

    @Override
    protected boolean supportIPAM(CloudAppVO app) {
        return true;
    }

    @Override
    public String getIpReservationTarget() {
        return "pod";
    }

    @Override
    public RedisCluster doInstall(CloudAppVO vo, List<String> ips) throws Exception {
        //将参数转换为RedisClusterVO类型数据
        RedisClusterVO redisVo = (RedisClusterVO) vo;


        String crName = redisVo.getCrName();
        String namespace = redisVo.getNamespace();
        String cpu = redisVo.getCpu();
        String memory = redisVo.getMemory();
        Integer masterSize = redisVo.masterSize;
        Map<ImageKindEnum, String> imageManifest = redisVo.getImageConfig();
        String storageClassName = redisVo.getStorageClassName();
        String disk = redisVo.getDisk();
        String hostpathRoot = redisVo.getHostpathRoot();
        Map<String, String> config = setupDbParamConfig(vo);
        Optional<String> filebeatYaml = Optional.ofNullable(sysConfigService.findOne(CloudAppConstant.SysCfgCategory.OPERATOR_CONFIG, "redis.config"));
        KubeClient kubeClient = clientService.get(redisVo.getKubeId());
        String password = redisVo.getPassword();
        //==============================================================================

        String filebeatImage = imageManifest.getOrDefault(ImageKindEnum.Filebeat,"");
        String ftpImage = imageManifest.getOrDefault(ImageKindEnum.FTP,"");
        String exporterImage = imageManifest.getOrDefault(ImageKindEnum.Exporter,"");
        //构造cr对象
        RedisCluster cr = new RedisCluster();
        cr.setMetadata(new ObjectMetaBuilder().withName(crName).withNamespace(namespace).build());
        RedisClusterSpec spec = new RedisClusterSpec();
        cr.setSpec(spec);

        Filebeat filebeat = new Filebeat();
        filebeat.setImage(filebeatImage);
        filebeat.setConfigMap(NDT_REDIS_FILEBEAT_CM);
        filebeat.setConfigFile("redis-cluster-filebeat.yaml");
//        if (filebeatYaml.isPresent()) {
//            kubeClient.applyYaml(YamlUtil.evaluateTemplate(filebeatYaml.get(),
//                    new HashMap<String, String>(){
//                        {
//                            put("name", NDT_REDIS_FILEBEAT_CM);
//                            put("namespace", redisVo.getNamespace());
//                            put("es_host", esUtil.getEsIp() + ":" + esUtil.getEsPort());
//                            put("es_username", esUtil.getEsUsername());
//                            put("es_pwd", esUtil.getEsPassword());
//                            put("es_protocol", esUtil.getProtocol());
//                            put("APP_TYPE", getKind().getKind());
//                        }}));
//        }

        //获取备份文件全路径
        // if(null != vo.getBackupHisId()){
        //     BackupHis backupHis = backupMapper.getBackupHisById(SCHEMA, CLOUD_BACKUP_HIS, Integer.valueOf(vo.getBackupHisId()));
        //     //备份文件名称
        //     String backupFileName = backupHis.getFileName();
        //     Map<String, Object> condition = new HashMap<>();
        //     condition.put("fileName", backupFileName);
        //     List<BackupHis> backupHisByFileName = backupMapper.listByMap(SCHEMA, CLOUD_BACKUP_HIS, condition);
        //     if (CollectionUtils.isEmpty(backupHisByFileName)) {
        //         throw new CustomException(600, "安装失败！未找到对应的备份历史！");
        //     }
        //     com.shindata.redis.v1.redisclusterspec.Restore restore = new com.shindata.redis.v1.redisclusterspec.Restore();
        //     String fullSourceName = redisVo.getNamespace() + "/" + backupHisByFileName.get(0).getAppName() + "/" + backupFileName;
        //     restore.setFullSource(fullSourceName);
        //     //获取备份存储信息
        //     CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        //     HashMap<String, String> remote = new HashMap<>();
        //     if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
        //         remote.put("type", CloudAppConstant.OperatorStorageType.NFS);
        //         remote.put("address", cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath());
        //     } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
        //         remote.put("type", CloudAppConstant.StorageType.S3);
        //         remote.put("address", cloudBackupStorageVO.getServer());
        //         remote.put("bucket", cloudBackupStorageVO.getBucket());
        //         remote.put("region", cloudBackupStorageVO.getRegion());
        //         //获取operator的namespace，因为所有operator都相同，所以统一获取mysql的operatornamespace
        //         String operatorConfig = sysConfigService.findOne("operator.name", "MySQL");
        //         String operatorNamespace = operatorConfig.split("/")[0];
        //         remote.put("secret", operatorNamespace + ":backupstorage-secret");
        //     } else {
        //         throw new CustomException(600, "恢复失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
        //     }
        //     restore.setRemote(remote);
        //     spec.setRestore(restore);
        // }

        spec.setIpList(ips.stream().map(ip -> {
            IpList ipList_ = new IpList();
            ipList_.setIp(ip);
            return ipList_;
        }).collect(Collectors.toList()));
        spec.setCpu(cpu);
        spec.setMemory(memory);
        DataStorage dataStorage = new DataStorage();
        dataStorage.setStorageClass(storageClassName);
        dataStorage.setSize(disk);
        dataStorage.setHostpathRoot(hostpathRoot);
        spec.setDataStorage(dataStorage);
        spec.setMasterSize(masterSize);
        spec.setImage(imageManifest.get(ImageKindEnum.MainImage));
        Ftp ftp = new Ftp();
        ftp.setImage(ftpImage);
        ftp.setUrl(ftpUtils.getURL());
        spec.setFtp(ftp);
        spec.setExporterImage(exporterImage);
        spec.setFilebeat(filebeat);
        spec.setConfig(config);
        spec.setPort(getKind().getDbPort());

        Schedule schedule = new Schedule();
        schedule.setAntiAffinityRequired(vo.getAntiAffinityRequired());
        schedule.setNodeAffinity(convertCRNodeAffinity(vo.getSelector(), com.shindata.redis.v1.redisclusterspec.schedule.NodeAffinity.class));
        schedule.setTolerations(convertCRTolerations(vo.getToleration(), com.shindata.redis.v1.redisclusterspec.schedule.Tolerations.class));
        spec.setSchedule(schedule);

        if (StringUtils.isNotEmpty(password)) {
            Secret secret = new Secret();
            secret.setPasswordKey(SECRET_PASSWORD_KEY);
            String secretName = getSecretName(redisVo.getCrName(), redisVo.getUsername());
            secret.setSecretName(secretName);
            spec.setSecret(secret);
            kubeClient.createSecret(secretName, password, namespace); // todo add owner reference
        }

        //创建redis的filebeat configmap
//        createConfigMap(vo);
        appService.update(vo);

        // 将参数模板中的参数写入cr中
        if(Objects.nonNull(vo.getDbParamTemplateId())) {
            createConfig(spec, vo.getDbParamTemplateId());
        }
        overWriteCnfParam(vo, spec.getConfig());

        if (AppMultiAZService.DeployType.isParallel(vo.getDeployType()))
            configureMultiAZ(cr, vo);
        return cr;
    }

    @Override
    protected void createCrControlResource(RedisCluster cr, CloudAppVO vo) {
        final String crName = vo.getCrName();
        final String namespace = vo.getNamespace();
        KubeClient kubeClient = clientService.get(vo.getKubeId());
        createAgentRbac(kubeClient, namespace);
    }

    private String getSecretName(String crName, String username) {
         return "rc-" + crName + "-password";
    }

    @Override
    public void deleteCrControlledResources(CloudApp app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        kubeClient.deleteSecret(getSecretName(app.getCrName(), null), app.getNamespace());
    }

    @Override
    public void completeInstanceProperty(List<AppInstanceVO> vos, int appId) {
        //todo 更新 redis 的 crd，使用 pod.status.podstate来进行状态确认
        CloudApp app = appService.get(appId);
        KubeClient kubeClient = clientService.get(app.getKubeId());
        List<PodDTO> pods = kubeClient.listPod(app.getNamespace(), AppUtil.getPodLabel(app));
        // 选择一个运行中的redis
        Optional<PodDTO> redisClusterPod = pods.stream().filter(pod -> RUNNING.equals(pod.getStatus()) &&
                redisPodService.checkRedisLiveness(pod.getNamespace(), pod.getPodName(), app.getKubeId(), app)).findFirst();
        try {
            RedisCluster rc = kubeClient.listCustomResource(RedisCluster.class, app.getCrName(), app.getNamespace());
            //key:podname,value:role
            Map<String, String> roleMap = new HashMap<>();
            if (redisClusterPod.isPresent()) {
                String pwd = getPassword(app);
                String password = StringUtils.isEmpty(pwd) ? "" : " -a ${REDIS_PASSWORD}";
                // cluster nodes Output format: <id> <ip:port> <flags> <master> <pings> <pongs> <epoch> <link> <slot>
                String redisNodesCMD = "redis-cli " + password + " cluster nodes | awk -F'[ @]' '{print $2,$4}' | sed -e 's/myself,//g'";
                String result = kubeClient.execCmd(app.getNamespace(), redisClusterPod.get().getPodName(), getKind().getContainerName(), "sh", "-c", redisNodesCMD);
                if (StringUtils.isNoneEmpty(result)){
                    roleMap = Arrays.stream(result.split("\n")).map(line -> {
                        String[] items = line.split(" ");
                        String podId = redisPodService.announceIpMapping(rc, items[0]);
                        return new Pair<>(podId, items[1]);
                    }).filter(pair -> StringUtils.isNotEmpty(pair.fst))
                      .collect(Collectors.toMap(pair -> pair.fst, pair -> pair.snd));
                }
            }
            Map<String, String> finalRoleMap = roleMap;
            vos.forEach(podDTO -> {
                if (RUNNING.equals(podDTO.getStatus())) {
                    podDTO.setLiveness(1);
                    if (finalRoleMap.containsKey(podDTO.getIp())) {
                        String flags = finalRoleMap.get(podDTO.getIp());
                        if (flags.contains("fail")) {
                            podDTO.setLiveness(0);
                            if (MAINTAINING.equals(rc.getStatus().getState())) {
                                podDTO.setStatus(CloudAppConstant.PodStatus.STOPPED);
                            } else {
                                podDTO.setStatus(CloudAppConstant.PodStatus.NOT_READY);
                            }
                        }
                        if (flags.contains(CloudAppConstant.ROLE_MASTER)) {
                            podDTO.setRole(ROLE_PRIMARY);
                        } else if (flags.contains(CloudAppConstant.ROLE_SLAVE)) {
                            podDTO.setRole(ROLE_SECONDARY);
                        }
                    }
                } else if (MAINTAINING.equals(rc.getStatus().getState()) && NOT_READY.equals(podDTO.getStatus())) {
                    podDTO.setLiveness(0);
                    podDTO.setStatus(CloudAppConstant.PodStatus.STOPPED);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            log.error("[completeInstanceProperty]获取实例角色失败。 {}", e.getMessage());
        }
    }

    @Override
    public PageInfo<RedisClusterVO> searchPage(PageDTO page) {
        PageInfo<? extends CloudAppVO> cloudAppVOPageInfo = super.searchPage(page);
        //获取kubeClient
        return PageUtil.page2PageInfo(cloudAppVOPageInfo.getList(), RedisClusterVO.class, cloudAppVO -> {
            RedisClusterVO redisVO = new RedisClusterVO();
            BeanUtils.copyProperties(cloudAppVO, redisVO);
            //判断是否为redisCluster
            try {
                if(AppKind.Redis_Cluster.getArch().equalsIgnoreCase(redisVO.getArch())){
                    KubeClient kubeClient = clientService.get(redisVO.getKubeId());
                    RedisCluster cr = kubeClient.listCustomResource(RedisCluster.class, redisVO.getCrName(), redisVO.getNamespace());
                    if(null != cr && null != cr.getStatus() && "ready".equalsIgnoreCase(cr.getStatus().getState())){
                        //创建分片ip的map，key是分片序号，value是ip的字符串
                        HashMap<String, Object> masterMap = new HashMap<>();
                        List<Shards> shards = cr.getStatus().getShards();
                        if(null != shards){
                            for(Shards everyShard : shards){
                                List<String> ips = everyShard.getNodes().stream().map(announceIp ->
                                        redisPodService.announceIpMapping(cr, announceIp)).collect(toList());
                                masterMap.put(everyShard.getSlotMin().toString(), ips);
                            }
                        }
                        //设置新字段
                        redisVO.setClusterIpMap(masterMap);
                    }
                    //设置分片数
                    if(null != cr){
                        redisVO.setMasterSize(cr.getSpec().getMasterSize());
                        //重新设置从节点数
                        redisVO.setSlaveCount(redisVO.getMembers() - redisVO.getMasterSize());
                    }
                    RedisOverrideSpec spec = reviewSpec(cloudAppVO);
                    redisVO.setShakeCpu(spec.getShakeCpu());
                    redisVO.setShakeMemory(spec.getShakeMemory());

                }else{
                    redisVO.setSentinel(getRedisSentinelName(cloudAppVO));
                }
            } catch (Exception e) {
                log.error("complete redis vo error", e);
            }
            return redisVO;
        });
    }

    private String getRedisSentinelName(CloudApp app) {
        Redis unmarshal = YamlEngine.unmarshal(
                StringUtils.isEmpty(app.getCr()) ? app.getCrRun() : app.getCr(), Redis.class);
        return unmarshal.getSpec().getSentinel();
    }

    @Override
    @Transactional
    public void update(ResourceDTO patch) throws Exception {
        Consumer<RedisCluster> modifier = (current) -> {
            RedisClusterSpec spec = current.getSpec();
            spec.setCpu(patch.getCpu());
            spec.setMemory(patch.getMemory());
            spec.getDataStorage().setSize(patch.getDisk());
//            spec.getBackupStorage().setSize(patch.getBackupDisk());
        };
        Consumer<RedisCluster> storageModifier = (current) -> {
            RedisClusterSpec spec = current.getSpec();
            if (MetricUtil.lessAndNotZero(spec.getDataStorage().getSize(), patch.getDisk()))
                spec.getDataStorage().setSize(patch.getDisk());
//            if (MetricUtil.lessAndNotZero(spec.getBackupStorage().getSize(), patch.getBackupDisk()))
//                spec.getBackupStorage().setSize(patch.getBackupDisk());
        };

        operationHandler.handleUpdate(patch, modifier, this, RedisCluster.class, storageModifier);
    }

    @Override
    public void scale(int id, OverrideSpec vo, ActionEnum action) throws Exception {
        if (action == ActionEnum.SCALE_OUT) {
            scaleOut(id, vo.getMasterSize(), vo.getSpareSize());
        } else if (action == SCALE_IN) {
            scaleDown(id, vo.getMasterSize(), vo.getSpareSize()); // todo scaleDown 没必要单写一个方法
        }
    }

    @Transactional(rollbackFor = Exception.class )
    void scaleOut(int appId, Integer masterSize, Integer spareSize) throws Exception {
        CloudApp app = appService.get(appId);
        Integer kubeId = app.getKubeId();
        String serviceType = accessManagementService.usedServiceList(appId).get(0).getServiceType();
        RedisClusterSpec spec = YamlEngine.unmarshal(app.getCr(), RedisCluster.class).getSpec();
        int currentTotal = spec.getIpList().size();
        int currentMaster = spec.getMasterSize();
        masterSize = masterSize == null ? currentMaster : masterSize;
        // 当spareSize为空时，代表仅扩容分片数，仍需算出扩容后总备库数。((当前总数-当前分片数) / 当前分片数 * 扩容后分片数== 单个分片上备库数  * 扩容后分片数
        spareSize = spareSize == null ? ((currentTotal - currentMaster) / currentMaster) * masterSize : spareSize;
        // 扩容节点数=扩容后分片数 * 扩容后单个分片上master+slave节点数 - 当前总节点数
        int scaleOutNum = (masterSize * (spareSize/masterSize + 1 )) - currentTotal;
        Integer finalMasterSize = masterSize;
        TriFunction<CloudApp, List<String>, RedisCluster, RedisCluster> modifier = (app_, scaledIPs, current) -> {
            List<ServiceManager> serviceManagerList = new ArrayList<>();
            RedisClusterSpec currentSpec = current.getSpec();
            currentSpec.setMasterSize(finalMasterSize);
            List<IpList> currentIpList = currentSpec.getIpList();
            List serviceResourceList =
                    accessManagementService.genServiceResourceByAppKindMember(scaleOutNum, getKind(), kubeId, serviceType);
            int index = 0;
            for (int i = 0; i < scaleOutNum; i++) {
                IpList newIpList = new IpList();
                newIpList.setIp(scaledIPs.get(i));
                if (serviceType.equals(CloudAppConstant.ServiceType.NODE_PORT)) {
                    //NodePort 的话一个实例需要两个端口
                    for (int j = 0; j < 2; j++) {
                        int nodePort = (Integer) serviceResourceList.get(index ++);
                        ServiceManager serviceManager = new ServiceManager();
                        serviceManager.setServiceName(getKind().getWriteServiceName(app.getCrName(), scaledIPs.get(i)));
                        serviceManager.setPort(nodePort);
                        if (j == 0) {
                            newIpList.setPort(nodePort);
                            serviceManager.setPurpose(CloudAppConstant.ServicePurpose.WRITE);
                        } else {
                            newIpList.setBusPort(nodePort);
                            serviceManager.setPurpose(CloudAppConstant.ServicePurpose.REDIS_BUS_PORT);
                        }
                        serviceManagerList.add(serviceManager);
                    }
                } else if (serviceType.equals(CloudAppConstant.ServiceType.LOAD_BALANCER)) {
                    newIpList.setExternalIP(String.valueOf(serviceResourceList.get(i)));
                    newIpList.setPort(getKind().getDbPort());
                    ServiceManager serviceManager = new ServiceManager();
                    serviceManager.setServiceName(getKind().getWriteServiceName(app.getCrName(), scaledIPs.get(i)));
                    serviceManager.setPurpose(CloudAppConstant.ServicePurpose.WRITE);
                    serviceManagerList.add(serviceManager);
                }
                currentIpList.add(newIpList);
            }
            // 记录操作日志
            if (serviceType.equals(CloudAppConstant.ServiceType.NODE_PORT)) {
                OpLogContext.instance().PORT(serviceResourceList);
            } else if (serviceType.equals(CloudAppConstant.ServiceType.LOAD_BALANCER)) {
                OpLogContext.instance().LBIP(serviceResourceList);
            }
            accessManagementService.initServices(app, serviceManagerList, serviceType);
            return current;
        };
        operationHandler.handleScaleUp(
                appId, scaleOutNum, RedisCluster.class, this,
                getIpOwnerKind(), getIpReservationTarget(), modifier);
    }

    /**
     *
     * @param appId
     * @param masterSize 缩容后分片数
     * @param spareSize 缩容后副本数(总)
     * @throws Exception
     */

    @Override
    public void scaleDown(int appId, Integer masterSize, Integer spareSize) throws Exception {
        //1.判断参数
        if(0 == appId){
            throw new CustomException(600, "未获取到应用id！");
        }

        CloudApp app = appService.get(appId);
        KubeClient client = clientService.get(app.getKubeId());
        //获取预期的CR
        RedisCluster extCr = YamlEngine.unmarshal(app.getCr(), RedisCluster.class);
        //获取实际的CR
        RedisCluster actCr = client.listCustomResource(RedisCluster.class, app.getCrName(), app.getNamespace());

        // 当前的集群分片结构
        final int currentMasterSize = actCr.getSpec().getMasterSize();
        final int finalMasterSize = masterSize == null ? currentMasterSize : masterSize;
        final int currentReplicaSize = actCr.getSpec().getIpList().size() / currentMasterSize - 1;
        final int finalReplicaSizePerShard = (spareSize == null ? currentReplicaSize : finalMasterSize) / finalMasterSize; // per shard
        //2. 根据计算后的分片数 和 副本数更新IP列表.
        List<Shards> shards = actCr.getStatus().getShards();
        Set<String> finalIpStrings = shards.stream()
                .limit(finalMasterSize)
                .map(ss -> ss.getNodes().subList(0, finalReplicaSizePerShard + 1))
                .flatMap(Collection::stream)
                .map(node -> redisPodService.announceIpMapping(actCr, node)) // 根据nodeIP:nodePort返回ipList中的podIP
                .collect(Collectors.toSet());
        List<IpList> ipList = extCr.getSpec().getIpList().stream().filter(ip -> finalIpStrings.contains(ip.getIp()))
                .collect(toList());

        //3.缩容完成
        //设置cr的iplist
        RedisCluster curCr = YamlEngine.unmarshal(app.getCr(), RedisCluster.class);
        curCr.getSpec().setIpList(ipList);
        curCr.getSpec().setMasterSize(finalMasterSize);


        Map<String, Object> map = new HashMap<>();
        //4.挑选出要释放的 svm
        List<ServiceManager> serviceManagerList = accessManagementService.usedServiceList(appId);
        Set<String> serviceNameSet = finalIpStrings.stream()
                .map(ip -> getKind().getWriteServiceName(app.getCrName(), ip)).collect(Collectors.toSet());
        // 和最终 ip 的 servicename 进行对比，如果相同则移除
        serviceManagerList.removeIf(serviceManager -> serviceNameSet.contains(serviceManager.getServiceName()));
        map.put("scaleDownServiceManagerList", JsonUtil.toJson(serviceManagerList));

        //5.记录操作用于回滚
        String applyYaml = YamlEngine.marshal(curCr);
        OpLogContext.instance().YAML("CR", applyYaml, app.getCr());

        //6.触发调度并提交yaml
        clientService.get(app.getKubeId()).updateCustomResource(curCr, RedisCluster.class);
        appService.callScheduler(app, applyYaml, map,
                ActionEnum.SCALE_IN, getProcessorClass(ActionEnum.SCALE_IN));
    }

    @Override
    public void upgrade(int appId, String version) throws Exception {
        BiFunction<Map<ImageKindEnum, String>, RedisCluster, RedisCluster> modifier = (imageManifest, cur) -> {
            cur.getSpec().setImage(imageManifest.get(ImageKindEnum.MainImage));
            return cur;
        };
        operationHandler.handleUpgrade(appId, version, modifier, RedisCluster.class, this);
    }

//    /**
//     * 修改镜像
//     * @param version
//     * @param appId
//     * @return
//     */
//    @Transactional
//    public void switchVersion(String version, Integer appId) throws JsonProcessingException, SchedulerException {
//        //获取应用
//        CloudApp app = appService.get(appId);
//        RedisCluster crInK8s = appService.getCustomResource(app, RedisCluster.class);
//        RedisCluster curCr = YamlEngine.unmarshal(app.getCr(), RedisCluster.class);
//        //设置镜像
//        Map<ImageKindEnum, String> imageManifest = appConfigService.getImageManifest(getKind(), version);
//        String image = imageManifest.get(ImageKindEnum.MainImage);
//        //获取第一条
//        curCr.getSpec().setImage(image);
//        //如果镜像未发生改变，则停止执行
//        if(crInK8s.getSpec().getImage().equals(image)){
//            return;
//        }
//        appService.callScheduler(app, YamlEngine.marshal(curCr), null, ActionEnum.UPGRADE, RedisClusterWatch.class);
//        //修改资源
//        clientService.get(app.getKubeId()).updateCustomResource(curCr, RedisCluster.class);
//        //修改app表的版本
//        app.setVersion(version);
//        appService.update(app);
//    }

    /**
     * 查询镜像列表
     * @return
     */
    public List<String> imageList(Integer kubeId, String version) {
        //查询
        List<CloudAppConfig> list = appConfigService.get(getKind());
         //对当前版本进行切割
        String[] curVersion = version.split("\\.");

        //筛选出比当前版本高的镜像信息
        List<String> resList = list.stream().filter(item -> {
            if (version.equals(item.getVersion())) {
                return false;
            }
            String[] everyVersion = item.getVersion().split("\\.");
            for (int i = 0; i < everyVersion.length && i < curVersion.length; i++) {
                if (Integer.valueOf(curVersion[i]) > Integer.valueOf(everyVersion[i])) {
                    return false;
                } else if (Integer.parseInt(curVersion[i]) < Integer.parseInt(everyVersion[i])){
                    return true;
                }
            }
            return false;
        }).map(CloudAppConfig :: getVersion).collect(Collectors.toList());
        return resList;
    }


    /*
     * 开启/关闭维护模式
     * */
    @Transactional
    public void switchMaintenance(Integer appId, Boolean maintenance) throws JsonProcessingException, SchedulerException {
        //获取应用
        CloudApp app = appService.get(appId);
        RedisCluster crInK8s = appService.getCustomResource(app, RedisCluster.class);
        RedisCluster curCr = YamlEngine.unmarshal(app.getCr(), RedisCluster.class);
        crInK8s.getSpec().setMaintenance(maintenance);
        curCr.getSpec().setMaintenance(maintenance);
        clientService.get(app.getKubeId()).updateCustomResource(crInK8s, RedisCluster.class);
    }


    @Transactional
    public Object applyAction(Integer appId, String podName, String action) throws Exception {
        //判断应用是否存在
        CloudApp app = appService.get(appId);
        CustPreconditions.checkNotNull(app, "应用不存在");

        action += "_pod";
        //判断redis是否处于启动状态，false为关闭，true为启动
        Boolean liveness = redisPodService.checkRedisLiveness(app.getNamespace(), podName, app.getKubeId(), app);
        //判断操作
        if (ActionEnum.START_POD.name().equalsIgnoreCase(action)) {
            //启动
            if (liveness.equals(true)) {
                throw new CustomException(600, "实例已启动");
            }

            //执行启动脚本
            redisPodService.startDb(app.getKubeId(), app.getNamespace(), podName);
            //修改操作历史
            ResourceChangeHis his =appService.getResourceChangeHis(app,ActionEnum.START_POD, StatusConstant.SUCCESS,app.getCrRun(),null);
            his.setLastEndTimestamp(System.currentTimeMillis());
            his.setUpdateTime(new Timestamp(System.currentTimeMillis() + 2000));
            resourceChangeHisService.add(his);

            //获取所有的实例，判断是否还有实例处于关闭状态，如果有，则不能取消维护模式
            List<PodDTO> pods = clientService.get(app.getKubeId()).listPod(app.getNamespace(), AppKind.valueOf(app.getKind(), app.getArch()).labelOfPod(app));
            List<String> podNames = pods.stream().map(pod -> pod.getPod().getMetadata().getName()).collect(Collectors.toList());
            //是否结束维护模式
            boolean isCloseMaintenance = true;
            for(String everyPodName : podNames){
                Boolean everyLiveness = redisPodService.checkRedisLiveness(app.getNamespace(), everyPodName, app.getKubeId(), app);
                if(everyLiveness.equals(false)){
                    isCloseMaintenance = false;
                    break;
                }
            }
            if(isCloseMaintenance){
                switchMaintenance(appId,false);
//                //修改实例状态
//                app.setStatus(CloudAppConstant.AppStatus.SUCCESS);
//                appService.update(app);
            }
        } else if (ActionEnum.STOP_POD.name().equalsIgnoreCase(action)) {
            //停止
            if (liveness.equals(false)) {
                throw new CustomException(600, "实例已停止");
            }
            //执行停止脚本
            redisPodService.stopDb(app.getKubeId(), app.getNamespace(), podName);
            //修改操作记录
            ResourceChangeHis his =appService.getResourceChangeHis(app,ActionEnum.STOP_POD, StatusConstant.SUCCESS,app.getCrRun(),null);
            his.setLastEndTimestamp(System.currentTimeMillis());
            his.setUpdateTime(new Timestamp(System.currentTimeMillis() + 2000));
            resourceChangeHisService.add(his);

            switchMaintenance(appId,true);
//            //修改实例状态
//            app.setStatus(CloudAppConstant.AppStatus.STOPPED);
//            appService.update(app);
        }
        return null;
    }

    public List<Map<String, String>> listPvc(Integer kubeId, String namespace, Map<String, String> labels) {
        //如果kubeId == null
        if(kubeId == null) {
            List<Map<String, String>> pvcProps = new LinkedList<>();
            //获取所有的k8s配置信息
            List<KubeConfig> configList = configService.list(null);
            for (KubeConfig config : configList) {
                pvcProps.addAll(pvcListByKube(config.getId(),null,null));
            }
            return pvcProps;
        }
        return pvcListByKube(kubeId, namespace, labels);
    }

    /**
     * rediscluster启停
     * @param appId
     * @param action
     * @return
     * @throws Exception
     */
    @Transactional
    public Object applyAppAction(Integer appId, String action) throws Exception {
        //1. 判断应用是否存在
        CloudApp app = appService.get(appId);
        CustPreconditions.checkNotNull(app, "应用不存在");

        //2. 遍历所有pod判断redis是否处于启动状态，所有pod的状态是false为关闭，所有pod的状态是true为启动
        //实例存活状态list
        List<Boolean> isLivenessList = new ArrayList<>();
        //主节点list,装有podName
        List<String> masterList = new ArrayList<>();
        //从节点list,装有podName
        List<String> slaveList = new ArrayList<>();
        //主节点IPlist,装有主节点的IP
        List<String> masterIPList = new ArrayList<>();

        //获取所有pod
        List<PodDTO> pods = clientService.get(app.getKubeId()).listPod(app.getNamespace(), AppKind.valueOf(app.getKind(), app.getArch()).labelOfPod(app));
        //获取cr
        RedisCluster cr = appService.getCustomResource(app, RedisCluster.class);
        List<Shards> shards = cr.getStatus().getShards();
        //获取所有主的ip
        for(Shards shard : shards){
            String masterPodName = "rc-" + app.getCrName() + "-" + shard.getNodes().get(0).replace(".", "-");
            masterIPList.add(masterPodName);
        }
        //对状态list、主从list进行分装
        pods.parallelStream().forEach(i -> {
            //加入状态序列
            Boolean everyLiveness = redisPodService.checkRedisLiveness(app.getNamespace(), i.getPodName(), app.getKubeId(), app);
            isLivenessList.add(everyLiveness);
            //获取角色，分别放入主从两个list
            if(-1 == masterIPList.indexOf(i.getPodName())){
                slaveList.add(i.getPodName());
            }else{
                masterList.add(i.getPodName());
            }
        });
        //统计处于启动还是停止状态
        Map<Boolean, Long> countMap = isLivenessList.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
        Long trueNum = countMap.get(true);
        if(null == trueNum){
            trueNum = 0L;
        }
        //判断启动数量是否等于pod数量
        boolean isStart = trueNum == pods.size() ? true : false;

        //判断操作
        if (ActionEnum.START.name().equalsIgnoreCase(action)) {
            //启动
            if (isStart) {
                throw new CustomException(600, "应用已启动");
            }

            //依次执行启动脚本,先启动主节点，再启动从节点
            //启动主节点
            masterList.parallelStream().forEach(podName -> {
                try {
                    redisPodService.startDb(app.getKubeId(), app.getNamespace(), podName);
                } catch (Exception e) {
                    log.error("[rediscluster启动失败]主节点启动失败！  " + podName);
                    throw new CustomException(600, "主节点启动失败！  " + podName);
                }
            });
            //启动从节点
            slaveList.parallelStream().forEach(podName -> {
                try {
                    redisPodService.startDb(app.getKubeId(), app.getNamespace(), podName);
                } catch (Exception e) {
                    log.error("[rediscluster启动失败]从节点启动失败！  " + podName);
                    throw new CustomException(600, "从节点启动失败！  " + podName);
                }
            });

            //获取所有的实例，判断是否还有实例处于关闭状态，如果有，则不能取消维护模式
            List<String> podNames = pods.stream().map(pod -> pod.getPod().getMetadata().getName()).collect(Collectors.toList());
            //是否结束维护模式
            boolean isCloseMaintenance = true;
            for(String everyPodName : podNames){
                Boolean everyLiveness = redisPodService.checkRedisLiveness(app.getNamespace(), everyPodName, app.getKubeId(), app);
                if(everyLiveness.equals(false)){
                    isCloseMaintenance = false;
                    break;
                }
            }
            if(isCloseMaintenance){
                switchMaintenance(appId,false);
            }
            //修改操作历史
            ResourceChangeHis his =appService.getResourceChangeHis(app,ActionEnum.START, StatusConstant.SUCCESS,app.getCrRun(),null);
            his.setLastEndTimestamp(System.currentTimeMillis());
            his.setUpdateTime(new Timestamp(System.currentTimeMillis() + 2000));
            resourceChangeHisService.add(his);
            app.setStatus(CloudAppConstant.AppStatus.SUCCESS);
            appService.update(app);
        } else if (ActionEnum.STOP.name().equalsIgnoreCase(action)) {
            //停止
            if (!isStart) {
                throw new CustomException(600, "应用已停止");
            }
            //开启维护模式
            switchMaintenance(appId,true);
            //判断是否已经开启了维护模式
            String crState = null;
            int findTimes = 0;
            while(!"maintaining".equals(crState)){
                //获取状态
                RedisCluster curCr = appService.getCustomResource(app, RedisCluster.class);
                crState = curCr.getStatus().getState();
                if(0 != findTimes){
                    Thread.sleep(1000);
                }
                findTimes++;
                if(10 == findTimes){
                    log.error("[rediscluster停止失败]维护模式开启失败！");
                    throw new CustomException(600, "维护模式开启失败！");
                }
            }
            //依次执行停止脚本,先停止从节点，再停止主节点
            //关闭从节点
            slaveList.parallelStream().forEach(podName -> {
                try {
                    redisPodService.stopDb(app.getKubeId(), app.getNamespace(), podName);
                } catch (Exception e) {
                    log.error("[rediscluster停止失败]从节点启动失败！  " + podName);
                    throw new CustomException(600, "从节点启动失败！  " + podName);
                }
            });
            //关闭主节点
            masterList.parallelStream().forEach(podName -> {
                try {
                    redisPodService.stopDb(app.getKubeId(), app.getNamespace(), podName);
                } catch (Exception e) {
                    log.error("[rediscluster停止失败]主节点关闭失败！  " + podName);
                    throw new CustomException(600, "主节点关闭失败！  " + podName);
                }
            });
            //修改操作记录
            ResourceChangeHis his =appService.getResourceChangeHis(app,ActionEnum.STOP, StatusConstant.SUCCESS,app.getCrRun(),null);
            his.setLastEndTimestamp(System.currentTimeMillis());
            his.setUpdateTime(new Timestamp(System.currentTimeMillis() + 2000));
            resourceChangeHisService.add(his);
            app.setStatus(CloudAppConstant.AppStatus.STOPPED);
            appService.update(app);
        }
        return null;
    }

    public List<Map<String, String>> pvcListByKube(Integer kubeId, String namespace, Map<String, String> labels) {
        List<Map<String, String>> pcvProps = new LinkedList<>();
        if (kubeId == null) {
            pcvProps = null;
        }
        if(labels == null || labels.size() == 0) {
            labels = new HashMap<>();
            labels.put(CloudAppConstant.CustomLabels.APP,"redis");
        }
        PersistentVolumeClaimList persistentVolumeClaimList;
        final Map<String, List<String>> crNameAndNamespaceList;
        try {
            //通过labels获取所有的redisPvc
            persistentVolumeClaimList = clientService.get(kubeId).listPvc(namespace, labels);
            //因为存在不同nameSpace下有相同crName的情况，所以这里也获取到namespace
            crNameAndNamespaceList = getCrNameListByRedis(kubeId);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("redis pvc failed", kubeId, e);
            return pcvProps;
        }
        List<PersistentVolumeClaim> pvcList = new LinkedList<>();
        List<PersistentVolumeClaim> delPvcList = new LinkedList<>();
        for (PersistentVolumeClaim pvc : persistentVolumeClaimList.getItems()) {
            //根据app.kubernetes.io/name: ${CR_NAME}取出CR名称判断CR是否存在
            String cr_name = pvc.getMetadata().getLabels().get(CloudAppConstant.CustomLabels.APP_NAME);
            String nameSpace = pvc.getMetadata().getNamespace();
            if(!StringUtils.isEmpty(cr_name)) {
                //如果pvc的crName在crNameAndNamespaceList不存在，即是可以删除
                List<String> crNameList = crNameAndNamespaceList.get(nameSpace).stream().filter(s -> s.equals(cr_name)).collect(Collectors.toList());
                if (crNameList.isEmpty()) {
                    delPvcList.add(pvc);
                } else {
                    pvcList.add(pvc);
                }
            }
        }

        for (PersistentVolumeClaim pvc : delPvcList) {
            HashMap<String, String> prop = new HashMap<>();
            ObjectMeta metadata = pvc.getMetadata();
            prop.put("name", metadata.getName());
            Date date = parseUTC(metadata.getCreationTimestamp());
            prop.put("createTimestamp", date == null ? null : date.getTime() + "");
            prop.put("namespace", metadata.getNamespace());
            PersistentVolumeClaimSpec spec = pvc.getSpec();
            prop.put("accessModes", Arrays.toString(spec.getAccessModes().toArray()));
            Quantity storage = spec.getResources().getRequests().get("storage");
            prop.put("resourceRequest", storage == null ? null : storage.getAmount());
            prop.put("storageClassName", spec.getStorageClassName());
            prop.put("volumeMode", spec.getVolumeMode());
            prop.put("volumeName", spec.getVolumeName());
            prop.put("status", pvc.getStatus().getPhase());
            prop.put("kubeId", kubeId + "");
            prop.put("isDel","true");
            pcvProps.add(prop);
        }

        for (PersistentVolumeClaim pvc : pvcList) {
            HashMap<String, String> prop = new HashMap<>();
            ObjectMeta metadata = pvc.getMetadata();
            prop.put("name", metadata.getName());
            Date date = parseUTC(metadata.getCreationTimestamp());
            prop.put("createTimestamp", date == null ? null : date.getTime() + "");
            prop.put("namespace", metadata.getNamespace());
            PersistentVolumeClaimSpec spec = pvc.getSpec();
            prop.put("accessModes", Arrays.toString(spec.getAccessModes().toArray()));
            Quantity storage = spec.getResources().getRequests().get("storage");
            prop.put("resourceRequest", storage == null ? null : storage.getAmount());
            prop.put("storageClassName", spec.getStorageClassName());
            prop.put("volumeMode", spec.getVolumeMode());
            prop.put("volumeName", spec.getVolumeName());
            prop.put("status", pvc.getStatus().getPhase());
            prop.put("kubeId", kubeId + "");
            prop.put("isDel","false");
            pcvProps.add(prop);
        }
        return pcvProps;
    }

    public Map<String, List<String>> getCrNameListByRedis(Integer kubeId) {
        Map<String, List<String>> crNameAndNamespaceMap = new HashMap<>();
        List<RedisCluster> redisList = clientService.get(kubeId).listCustomResource(RedisCluster.class);
        for (RedisCluster redis : redisList) {
            List<String> crNameList = new LinkedList<>();
            String namespace = redis.getMetadata().getNamespace();
            String crName = redis.getMetadata().getName();
            if(crNameAndNamespaceMap.isEmpty()) {
                crNameList.add(crName);
                crNameAndNamespaceMap.put(namespace, crNameList);
            } else {
                List<String> valueList = crNameAndNamespaceMap.get(namespace);
                if(valueList == null || valueList.size() == 0) {
                    crNameList.add(crName);
                    crNameAndNamespaceMap.put(namespace, crNameList);
                } else {
                    List<String> remove = crNameAndNamespaceMap.remove(namespace);
                    remove.add(crName);
                    crNameAndNamespaceMap.put(namespace, remove);
                }
            }
        }
        return crNameAndNamespaceMap;
    }

    public List<Map<String, String>> listPv(Integer kubeId, Map<String, String> labels) {
        //如果kubeId == null
        if(kubeId == null) {
            List<Map<String, String>> pvcProps = new LinkedList<>();
            //获取所有的k8s配置信息
            List<KubeConfig> configList = configService.list(null);
            for (KubeConfig config : configList) {
                pvcProps.addAll(pvListByKube(config.getId(), null));
            }
            return pvcProps;
        }
        return pvListByKube(kubeId, labels);
    }

    public List<Map<String, String>> pvListByKube(Integer kubeId, Map<String, String> labels) {
        List<Map<String, String>> pcvProps = new LinkedList<>();
        if (kubeId == null) {
            pcvProps = null;
        }
        if(labels == null || labels.size() == 0) {
            labels = new HashMap<>();
            labels.put(CloudAppConstant.CustomLabels.APP, "redis");
        }
        //通过labels获取所有的redisPv
        PersistentVolumeList persistentVolumeList;
        final List<Map<String, String>> pvcList;
        try {
            persistentVolumeList = clientService.get(kubeId).listPv(labels);
            pvcList = pvcListByKube(kubeId, null, labels);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("redis pvc failed", kubeId, e);
            return pcvProps;
        }
        List<PersistentVolume> pvList = new LinkedList<>();
        for (PersistentVolume pv : persistentVolumeList.getItems()) {
            //根据app.kubernetes.io/name: ${CR_NAME}取出CR名称判断CR是否存在
            String cr_name = pv.getMetadata().getLabels().get(CloudAppConstant.CustomLabels.APP_NAME);
            String name = pv.getMetadata().getName();
            if(!StringUtils.isEmpty(cr_name)) {
                for (Map<String, String > pvc : pvcList) {
                    if (name.equalsIgnoreCase(pvc.get("volumeName"))) {
                        pvList.add(pv);
                    }
                }
            }
        }

        for (PersistentVolume pv : pvList) {
            HashMap<String, String> prop = new HashMap<>();
            ObjectMeta metadata = pv.getMetadata();
            prop.put("name", metadata.getName());
            Date date = parseUTC(metadata.getCreationTimestamp());
            prop.put("createTimestamp", date == null ? null : date.getTime() + "");
            prop.put("namespace", metadata.getNamespace());
            PersistentVolumeSpec spec = pv.getSpec();
            prop.put("pvc", spec.getClaimRef().getName());
            prop.put("accessModes", Arrays.toString(spec.getAccessModes().toArray()));
            prop.put("reclaimPolicy", spec.getPersistentVolumeReclaimPolicy());
            prop.put("storageClassName", spec.getStorageClassName());
            prop.put("volumeMode", spec.getVolumeMode());
            Quantity storage = spec.getCapacity().get("storage");
            prop.put("capacity", storage == null ? null : storage.getAmount());
            prop.put("status", pv.getStatus().getPhase());
            prop.put("kubeId", kubeId + "");
            pcvProps.add(prop);
        }
        return pcvProps;
    }

    /**
     * redis清除实例数据
     * cluster 架构，通过执行 redis-cli --cluster call localhost:6379 flushall; redis-cli --cluster call localhost:6379 save 两条命令实现
     * @param appId
     * @throws Exception
     */
    public void cleanData(Integer appId) throws Exception {
        CloudApp app = appService.get(appId);
        ResourceChangeHis his = new ResourceChangeHis();
        his.setAppId(app.getId());
        his.setAppName(app.getName());
        his.setKubeId(app.getKubeId());
        his.setNamespace(app.getNamespace());
        his.setKind(app.getKind());
        his.setCommand(ActionEnum.CLEAN_DATA.getAppOperation());
        his.setAction(ActionEnum.CLEAN_DATA.getActionType());
        his.setUserId(UserUtil.getCurrentUser().getUserid());
        his.setUserName(UserUtil.getCurrentUser().getUsername());
        his.setUserIp(CloudRequestContext.getContext().getUserIp());
        his.setInsertTime(Timestamp.valueOf(LocalDateTime.now()));
        his.setAppLogicId(app.getLogicAppId());

        try{
            KubeClient kubeClient = clientService.get(app.getKubeId());
            List<PodDTO> pods = kubeClient.listPod(app.getNamespace(), AppKind.Redis_Cluster.labelOfPod(app));
            // 选择一个运行中的redis
            Optional<PodDTO> first = pods.stream().filter(pod -> RUNNING.equals(pod.getStatus()) &&
                    redisPodService.checkRedisLiveness(pod.getNamespace(), pod.getPodName(), app.getKubeId(), app)).findFirst();
            if (!first.isPresent()){
                return;
            }
            // 获取密码
            String password = getPassword(app);
            password = StringUtils.isEmpty(password) ? "" : " -a '" + password + "'";
            String flushAllCMD = "redis-cli "  + password + " --cluster call localhost:6379 flushall;" +
                    "redis-cli "  + password + " --cluster call localhost:6379 save";
            kubeClient.execCmd(app.getNamespace(), first.get().getPodName(), getKind().getContainerName(), "sh", "-c", flushAllCMD);

            his.setStatus("0");
            his.setMsg("clean data success");
        } catch (Exception e) {
            log.error("cleanData error{} ", e.getMessage());
            his.setStatus("2");
            his.setMsg(e.getMessage());
        } finally {
            his.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
            his.setLastEndTimestamp(System.currentTimeMillis());
            resourceChangeHisService.add(his);
        }

    }

    public void createConfig(RedisClusterSpec spec, Integer id) {
        // 将参数模板中的参数写入cr中
        MySQLParamTemplateDTO mysqlParamTemplate = cloudDbParamTemplateService.getMysqlParamTemplate(id);
        List<MysqlParamRules> paramRules = mysqlParamTemplate.getMysqlParamRules();
        Map<String, String> config = paramRules.stream().collect(Collectors.toMap(e -> e.getParaname(), e -> e.getParavalue()));
        spec.setConfig(config);
    }

    public String bigKeys(Integer appId) {
        StringBuilder bigKeys = new StringBuilder();
        CloudApp app = appService.get(appId);
        KubeClient kubeClient = clientService.get(app.getKubeId());
        //1.查询实例列表
        List<AppInstanceVO> instanceList = findInstanceList(appId, null, null);
        if (CollectionUtils.isEmpty(instanceList)) {
            return null;
        }
        //2.过滤出master
        List<AppInstanceVO> masterInstanceList =
                instanceList.stream().filter(
                        instance -> "primary".equals(instance.getRole())).collect(Collectors.toList());
        //3.连接到各个master去执行redis-cli --bigkeys
        for (AppInstanceVO vo : masterInstanceList) {
            //3.1 获取密码
            String password = getPassword(app);
            password = StringUtils.isEmpty(password) ? "" : " -a ${REDIS_PASSWORD}";
            final String bigKeysCommand = "redis-cli " + password + " --bigkeys";
            String bigKey = vo.getPodName() + "查询bigKey失败";
            try {
                bigKey = kubeClient.execCmd(
                        vo.getNamespace(), vo.getPodName(), "redis", "sh", "-c", bigKeysCommand);
            } catch (Exception e) {
            }
            //4.整合各个master的结果到返回值
            bigKeys.append(vo.getPodName()).append("\r").append(bigKey).append("\r");
        }

        return bigKeys.toString();
    }

    @Override
    public void extendSetup(CloudAppVO vo) {
        CustPreconditions.checkNotNull(vo.getMasterSize(), "分片数不能为空");
        CustPreconditions.checkNotNull(vo.getSpareSize(), "备库数不能为空");
        int members = vo.getMasterSize() * (vo.getSpareSize() + 1);
        vo.setMembers(members);
    }
    @Autowired
    private BackupService backupService;

    /*@Override
    public BackupHis backup(int appId) throws Exception {
        CloudApp app = appService.get(appId);
        return backupService.manualBackup(new BackupHisVO() {{
            setAppId(app.getId());
            setAppName(app.getCrName());
            setAppType(app.getKind());
            setBackupType("full");
        }}, "redis-cluster");

    }*/

    @Override
    public CloudAppVO overrideSpec(CloudAppLogic logicApp, Integer kubeId, InstallAppVo<? extends OverrideSpec> vo) {
        CloudAppVO cloudAppVO = super.overrideSpec(logicApp, kubeId, vo);
        RedisClusterVO redisVO = new RedisClusterVO();

        BeanUtils.copyProperties(cloudAppVO, redisVO);
        if (vo.getOverrideSpecs().get(kubeId) instanceof RedisOverrideSpec) {
            RedisOverrideSpec overrideSpec = (RedisOverrideSpec)vo.getOverrideSpecs().get(kubeId);
            redisVO.setSentinel(overrideSpec.getSentinel());
            redisVO.setRestoreFileDir(overrideSpec.getRestoreFile());
            redisVO.setBackupPath(overrideSpec.getBackupPath());
            redisVO.setMasterSize(overrideSpec.getMasterSize());
            redisVO.setSpareSize(overrideSpec.getSpareSize());
            redisVO.setClusterIpMap(overrideSpec.getClusterIpMap());
            redisVO.setMembers(redisVO.getMasterSize() * (redisVO.getSpareSize() + 1));
            redisVO.setConfig(vo.getConfig());
            redisVO.setBackupHisId(overrideSpec.getBackupHisId() == null ? cloudAppVO.getBackupHisId() : String.valueOf(overrideSpec.getBackupHisId()));
            //判断是否存在备份历史，若存在则查询出备份文件全路径并放置于restoreFileDir属性

            // shake
            redisVO.setShakeCpu(overrideSpec.getShakeCpu());
            redisVO.setShakeMemory(overrideSpec.getShakeMemory());
        }
        return redisVO;
    }

    @Override
    public InstallAppVo<RedisOverrideSpec> parseInstallVo(String data) {
        InstallAppVo<RedisOverrideSpec> vo = JsonUtil.toObject(data, new TypeReference<InstallAppVo<RedisOverrideSpec>>() {
        });
        if (vo != null) {
            if (vo.getSpec() != null && vo.getSpec().getMembers() == 0) {
                RedisOverrideSpec spec = vo.getSpec();
                spec.setMembers(spec.getMasterSize() * (spec.getSpareSize() + 1));
            }
            if (!CollectionUtils.isEmpty(vo.getOverrideSpecs())) {
                for (RedisOverrideSpec spec : vo.getOverrideSpecs().values()) {
                    if (spec.getMembers() == 0 && spec.getMasterSize() != null && spec.getSpareSize() != null)
                        spec.setMembers(spec.getMasterSize() * (spec.getSpareSize() + 1));
                }
            }
        }

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyConfigParam(Map<String, String> params, Integer appId, String componentKind) throws Exception {
        log.info("[modifyParam] redis修改参数 应用ID:{}，参数{}", appId, JSONObject.toJSONString(params));
        if (CollectionUtils.isEmpty(params)){
            return;
        }
        CloudApp app = appService.get(appId);
        RedisCluster cr = YamlEngine.unmarshal(app.getCr(), RedisCluster.class);
        Map<String, String> config = cr.getSpec().getConfig();
        Map<String, String> oldConfig = null;
        if (config == null) {
            cr.getSpec().setConfig(params);
        } else {
            oldConfig = new HashMap<>(config);
            params.forEach((key, val) -> {
                this.prohibitParam(key);
                config.put(key, val);
            });
        }

        KubeClient client = clientService.get(app.getKubeId());
        HashMap<Object, Object> map = new HashMap<>();
        map.put("params", params);
        map.put("appId", appId);
        RedisCluster k8sCr = client.listCustomResource(RedisCluster.class, app.getCrName(), app.getNamespace());
        map.put("lastUpdateTime", k8sCr.getStatus().getLastUpdateTime());

        String applyYaml = YamlEngine.marshal(cr);

        try {
            OpLogContext.instance().YAML("CR", applyYaml, YamlEngine.marshal(k8sCr));
            client.updateCustomResource(cr, RedisCluster.class);
            app.setCrRun(applyYaml);
            appService.update(app);
            appService.callScheduler(app, applyYaml, map, ActionEnum.MODIFY_PARAM, RedisClusterModifyParamsWatch.class);
        } catch (SchedulerException | JsonProcessingException e) {
            // 回滚
            cr.getSpec().setConfig(oldConfig);
            client.updateCustomResource(cr, RedisCluster.class);
            throw new CustomException(600, "redis修改参数失败，" + e.getMessage());
        }
    }

    @Override
    public void updatePassword(Password password, int appId) throws Exception {
        //对密码进行解密
        String deNewPassword = decryptPassword(password.getNewPassword());
        password.setNewPassword(deNewPassword);
        // 去除redis原密码验证
/*        if(!StringUtils.isBlank(password.getOldPassword())){
            String oldDecrypt = rsaUtil.decrypt(password.getOldPassword());
            password.setOldPassword(oldDecrypt);
        }*/
        String passwordKey = DigestUtils.md5Hex(password.getNewPassword());
        BiConsumer<io.fabric8.kubernetes.api.model.Secret, RedisCluster> update = (newsecret, currentCr) -> {
            Secret secret = Optional.ofNullable(currentCr.getSpec().getSecret()).orElse(new Secret());
            secret.setPasswordKey(passwordKey);
            secret.setSecretName(newsecret.getMetadata().getName());
            currentCr.getSpec().setSecret(secret);
        };
        operationHandler.handleUpdatePassword(appId, password, passwordKey, this, RedisCluster.class, update,
                cr -> getSecretName(cr.getMetadata().getName(), null),
                cr -> cr.getSpec().getSecret().getPasswordKey());
    }


    @Override
    public void start(int appId, @Nullable String instanceName) throws Exception {
        // 这个脚本应该是同步执行的，保证主库先启动再启动从库
        String cmd = "bash /scripts/redis-start.sh";
        CloudApp app = appService.get(appId);
        RedisCluster rc = clientService.get(app.getKubeId()).listCustomResource(RedisCluster.class, app.getCrName(), app.getNamespace());
        List<String> masterIps = rc.getStatus().getShards().stream().map(shards -> shards.getNodes().get(0).replaceAll(":\\d+$", "")).collect(Collectors.toList());
        // start master first
        operationHandler.handleStart(appId, instanceName, this, cmd,
                i -> masterIps.contains(i.getIp()), null);
    }

    @Override
    public void stop(int appId, String instanceName) throws Exception {
        // 这个脚本应该是同步执行的，保证从库先停止再停止主库
        String cmd = "bash /scripts/redis-shutdown.sh";
        Consumer<RedisCluster> turnOnMaintain = cr -> {
            cr.getSpec().setMaintenance(true);
        };
        operationHandler.handleStop(appId, instanceName, this, cmd, turnOnMaintain,
                RedisCluster.class, null);
    }

    /**
     * 检查多个rediscluster的分片数是否相同
     * @param backupHisId
     * @param restoreAppId
     * @return
     */
    public Map<String, Object> checkMastersize(Integer backupHisId, Integer restoreAppId, Integer restoreMastersize) {
        //判断参数
        if(null == backupHisId){
            throw new CustomException(500, "备份历史id为空！");
        }
        if(null == restoreAppId && null == restoreMastersize){
            throw new CustomException(500, "恢复应用id为空或分片数为空！");
        }

        //返回结果
        Map<String, Object> res = new HashMap<>();
        //查询备份历史，获取备份适用的分片数
        BackupHis backupHis = backupService.get(backupHisId);
        String mes = backupHis.getMessage();
        JSONObject mesObj = JSONObject.parseObject(mes);
        Integer backupMastersize = mesObj.getInteger("masterSize");
        if(null == backupMastersize){
            throw new CustomException(500, "未找到当前备份文件适用分片数！");
        }

        //查询恢复应用的分片数
        if(null == restoreMastersize){
            CloudApp app = appService.get(restoreAppId);
            RedisCluster actCr = clientService.get(app.getKubeId()).listCustomResource(RedisCluster.class, app.getCrName(), app.getNamespace());
            if(null == actCr){
                throw new CustomException(500, "未找到恢复应用cr！");
            }
            restoreMastersize = actCr.getSpec().getMasterSize();

        }
        if(backupMastersize == restoreMastersize){
            res.put("result", true);
            res.put("masterSize", backupMastersize);
        }else{
            res.put("result", false);
            res.put("masterSize", backupMastersize);
        }
        return res;
    }

    public List<Map<String, Object>> getParamsInfo(Integer logicid) {
        //获取应用信息
        CloudApp app = appService.getCloudAppByLogicId(logicid);
        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
        //获取client
        //设置主要信息
        MetaVO metaVO = getMetaVOByApp(app, appKind);
        //进行查询
        List<Map<String, Object>> redisConfigList = sqlExecutor(metaVO, "REDIS_CONFIG");
        String prohibitContent = sysConfigService.findOne(PARAM_PROHIBIT, getKind().getProduct());
        if (StringUtils.isEmpty(prohibitContent)) {
            return redisConfigList;
        }
        Set<String> prohibited = new HashSet<>(Arrays.asList(prohibitContent.split(",")));
        List<Map<String, Object>> resultList = redisConfigList.parallelStream().map(tempMap -> {
            String configKey = tempMap.get("CONFIG_KEY").toString();
            if (configKey.equals("requirepass") || configKey.equals("masterauth")) {
                tempMap.put("CONFIG_VALUE", "******");
            }
            Boolean isupdate = prohibited.contains(configKey) ? Boolean.FALSE : Boolean.TRUE;
            tempMap.put("isupdate", isupdate);
            return tempMap;
        }).collect(Collectors.toList());
        return resultList;
    }

    public List<Map<String, Object>> sqlExecutor(MetaVO metaVO, String funcName) {
        try (Jedis jedis = DBConnectManager.getInstance().getJedis(metaVO)) {
            List<Map<String, Object>> redisList = new ArrayList<>();

            if (jedis == null) {
                return redisList;
            }
            funcName = Objects.isNull(funcName) ? "" : funcName;
            String info = jedis.info().toUpperCase();
            String[] strs;
            if (org.apache.commons.lang.StringUtils.isNotEmpty(info) && info.contains("\r\n")) {
                strs = info.split("\r\n");
            } else {
                strs = info.split("\n");
            }

            if (strs != null && strs.length > 0) {
                if ("REDIS_CONFIG".equals(funcName)) {
                    List<String> list = jedis.configGet("*");
                    String key = "";
                    for (int i = 0; i < list.size(); i++) {

                        if ((i & 1) == 0) {
                            key = list.get(i);
                        } else {
                            Map<String, Object> map = new HashMap<>();
                            map.put("CONFIG_KEY", key);
                            map.put("CONFIG_VALUE", list.get(i));
                            redisList.add(map);
                        }
                    }
                } else {
                    throw new CustomException(600, "暂不支持redis该类别数据的查询");
                }
            }
            return redisList;
        } catch (Exception e) {
            log.warn("查询redis参数信息失败" + e);
            throw new CustomException(600, "查询redis参数信息失败" + e);
        }
    }

    /**
     * 拼凑 metavo信息
     *
     * @return
     */
    private MetaVO getMetaVOByApp(CloudApp app, AppKind appKind) {
        MetaVO metaVO = super.getMetaVOByApp(app, null);
        //获取用户名密码
        String secretName = AppKind.Redis.equals(appKind) ?
                redisService.getSecretName(app.getCrName(), "") : getSecretName(app.getCrName(), "");
        String crStr = StringUtils.isNotEmpty(app.getCr()) ? app.getCr() : app.getCrRun();

        String passwordKey = "";
        //当redis没有密码时候
        if (AppKind.Redis_Cluster.equals(appKind)) {
            RedisClusterSpec spec = YamlEngine.unmarshal(crStr, RedisCluster.class).getSpec();
            Secret secret = spec.getSecret();
            passwordKey = secret == null ? passwordKey : secret.getPasswordKey();
        } else {
            com.shindata.redis.v1.redisspec.Secret secret = YamlEngine.unmarshal(crStr, Redis.class).getSpec().getSecret();
            passwordKey = secret == null ? passwordKey : secret.getPasswordKey();
        }

        io.fabric8.kubernetes.api.model.Secret secret =
                clientService.get(app.getKubeId()).getSecret(app.getNamespace(), secretName);
        SymmetricEncryptionUtil ss = SymmetricEncryptionUtil.getEncryptInstance();

        String passWord = (null == secret ||
                StringUtils.isEmpty(secret.getData().get(passwordKey))) ?
                "" : ss.encrypt(new String(Base64.getDecoder().decode(secret.getData().get(passwordKey).getBytes())));
        metaVO.setPassword(passWord);

        return metaVO;
    }

    /**
     * @return password parsed from secret by password key, null if not exist
     */
    public String getPassword(CloudApp app) {
        io.fabric8.kubernetes.api.model.Secret secret = clientService.get(app.getKubeId()).getSecret(app.getNamespace(), getSecretName(app.getCrName(), null));
        // 优先使用crRun ，防止在修改密码操作期间获取到旧密码
        String crYaml = StringUtils.isEmpty(app.getCrRun()) ? app.getCr() : app.getCrRun();
        com.shindata.redis.v1.redisclusterspec.Secret secret1 = YamlEngine.unmarshal(crYaml, RedisCluster.class).getSpec().getSecret();
        if (secret1 == null || secret == null) return null;
        String passwordKey = secret1.getPasswordKey();
        return new String(Base64.getDecoder().decode(secret.getData().get(passwordKey).getBytes()));
    }

    @Override
    public RedisOverrideSpec reviewSpec(CloudApp app) {
        OverrideSpec overrideSpec = super.reviewSpec(app);
        RedisOverrideSpec redisOverrideSpec = new RedisOverrideSpec();
        BeanUtils.copyProperties(overrideSpec, redisOverrideSpec);
        String yaml = app.getCr();
        RedisCluster cr = YamlEngine.unmarshal(yaml, RedisCluster.class);
        redisOverrideSpec.setMasterSize(cr.getSpec().getMasterSize());
        redisOverrideSpec.setSpareSize(cr.getSpec().getIpList().size() / redisOverrideSpec.getMasterSize() - 1);
        RedisShake redisShake;
        if ((redisShake = cr.getSpec().getRedisShake()) != null) {
            redisOverrideSpec.setShakeCpu(redisShake.getCpu());
            redisOverrideSpec.setShakeMemory(redisShake.getMemory());
        }
        return redisOverrideSpec;
    }

    @Override
    public List<ServiceManager> createService(
            String serviceType, CloudAppVO vo, List<?> serviceResources, CustomResource installCr) {
        if (serviceResources.isEmpty()) return Collections.emptyList();

        AppKind kind = getKind();
        int serviceManagerNum = kind.getServiceManagerNum(serviceType, vo.getMembers());
        if (CollectionUtils.isEmpty(serviceResources) || serviceResources.size() != serviceManagerNum) {
            throw new CustomException(600, "节点端口类型必须指定 " + serviceManagerNum
                    + " 个端口, 实际数量为 " + serviceResources.size() + ", 类型为 " + serviceType);
        }

        RedisCluster cr = (RedisCluster) installCr;
        List<IpList> ipList = cr.getSpec().getIpList();
        List<ServiceManager> svms = new ArrayList<>();

        if (CloudAppConstant.ServiceType.NODE_PORT.equalsIgnoreCase(serviceType)) {
            @SuppressWarnings("unchecked")
            List<Integer> nodePorts = (List<Integer>) serviceResources;
            int index = 0;
            for (int i = 0; i < ipList.size(); i++) {
                String serviceName = getKind().getWriteServiceName(vo.getCrName(), ipList.get(i).getIp());
                for (int j = 0; j < 2; j++) {
                    ServiceManager serviceManager = new ServiceManager();
                    serviceManager.setServiceType(serviceType);
                    serviceManager.setServiceName(serviceName);
                    Integer port = nodePorts.get(index++);
                    serviceManager.setPort(port);

                    if (j == 0) {
                        serviceManager.setPurpose(CloudAppConstant.ServicePurpose.WRITE);
                        ipList.get(i).setPort(port);
                    } else {
                        serviceManager.setPurpose(CloudAppConstant.ServicePurpose.REDIS_BUS_PORT);
                        ipList.get(i).setBusPort(port);
                    }

                    svms.add(serviceManager);
                }
            }
        } else if (CloudAppConstant.ServiceType.LOAD_BALANCER.equalsIgnoreCase(serviceType)) {
            //lb 的 serviceResources 结构为 List<String>
            @SuppressWarnings("unchecked")
            List<String> externalIps = (List<String>) serviceResources;
            int dbPort = kind.getDbPort();

            for (int i = 0; i < externalIps.size(); i++) {
                IpList ipListSpec = ipList.get(i);
                String serviceName = getKind().getWriteServiceName(vo.getCrName(), ipListSpec.getIp());
                String externalIp = externalIps.get(i);

                // cr 设置 lbip
                ipListSpec.setExternalIP(externalIp);
                // ServiceManager 设置信息
                ServiceManager serviceManager = new ServiceManager();
                serviceManager.setServiceType(serviceType);
                serviceManager.setPort(dbPort);
                serviceManager.setExternalIp(externalIp);
                serviceManager.setServiceName(serviceName);
                serviceManager.setPurpose(CloudAppConstant.ServicePurpose.WRITE);
                //加入 svmList
                svms.add(serviceManager);
            }
        }

        return svms;
    }

    @Override
    public void updateService(
            List<ServiceManager> svcMgrs, CloudApp app, Object oldServiceResource) throws Exception {
        ServiceManager serviceManager = svcMgrs.get(0);
        String serviceType = serviceManager.getServiceType();
        String lbip = serviceManager.getExternalIp();
        int port = serviceManager.getPort();
        Integer purpose = serviceManager.getPurpose();
        Map<String, String> data = new HashMap<>();
        data.put("oldServiceResource", oldServiceResource + "");
        Consumer<RedisCluster> modifier = rc -> {
            List<IpList> ipList = rc.getSpec().getIpList();
            for (IpList entry : ipList) {
                if (serviceType.equalsIgnoreCase(CloudAppConstant.ServiceType.NODE_PORT)) {
                    if (purpose.equals(CloudAppConstant.ServicePurpose.WRITE)
                            && entry.getPort().equals(oldServiceResource)) {
                        entry.setPort(port);
                        break;
                    } else if (purpose.equals(CloudAppConstant.ServicePurpose.REDIS_BUS_PORT)
                            && entry.getPort().equals(oldServiceResource)) {
                        entry.setBusPort(port);
                        break;
                    }
                } else if (serviceType.equalsIgnoreCase(CloudAppConstant.ServiceType.LOAD_BALANCER)) {
                    // lb 模式没有 busport
                    if (purpose.equals(CloudAppConstant.ServicePurpose.WRITE)
                            && entry.getExternalIP().equals(oldServiceResource)) {
                        entry.setExternalIP(lbip);
                        break;
                    }
                }
            }
        };
        operationHandler.handleService(
                app, svcMgrs, modifier, RedisCluster.class, this, data, ActionEnum.UPDATE_SERVICE);
    }


    @Override
    public boolean isParallel() {
        return false;
    }

    @Override
    public void configureMultiAZ(RedisCluster current, RedisCluster remote, CloudAppVO currentApp) {
        RedisClusterVO vo = (RedisClusterVO) currentApp;
        if (!"standby".equals(currentApp.getRole()))
            return;

        RedisShake redisShake = current.getSpec().getRedisShake();
        if (redisShake == null) {
            redisShake = new RedisShake();
            current.getSpec().setRedisShake(redisShake);
        }

        Optional.ofNullable(vo.getShakeCpu()).ifPresent(redisShake::setCpu);
        Optional.ofNullable(vo.getShakeMemory()).ifPresent(redisShake::setMemory);
        Optional.ofNullable(currentApp.getImageConfig())
                .map(imageInfos -> imageInfos.get(ImageKindEnum.Redis_Shake))
                .ifPresent(redisShake::setImage);

        Reader reader = redisShake.getReader();
        if (reader == null) {
            reader = new Reader();
            redisShake.setReader(reader);
        }
        reader.setCluster(true);
        reader.setIpList(remote.getSpec().getIpList().stream().map(ip->ip.getIp()).collect(toList()));

        // use secret to access remote redis. currently both side have same auth info.
        // so there is no need to create the new secret
        com.shindata.redis.v1.redisspec.redisshake.reader.Secret secret =
                new com.shindata.redis.v1.redisspec.redisshake.reader.Secret();
        secret.setSecretName(remote.getSpec().getSecret().getSecretName());
        reader.setSecret(secret);

    }

    @Override
    public void switchAZRole(int logicID, int currentPrimaryAppId) {
        Map<Boolean, List<CloudApp>> collect = appLogicService.getPhysicApps(logicID).stream()
                .collect(partitioningBy(p -> p.getId().equals(currentPrimaryAppId)));
        List<RedisCluster> previousStandby = new ArrayList<>(); // propagate current standby spec to new standby;
        collect.get(false) // previous standby
                .stream().findFirst().ifPresent(app -> {
                    previousStandby.add(YamlEngine.unmarshal(app.getCr(), RedisCluster.class));
                    RedisCluster update = YamlEngine.unmarshal(app.getCr(), RedisCluster.class);
                    update.getSpec().setRedisShake(null);
                    KubeClient client = clientService.get(app.getKubeId());
                    client.updateCustomResource(update, RedisCluster.class);
                });
        collect.get(true) // previous primary
                .stream().findFirst().ifPresent(app -> {
                    CloudApp currentPrimary = SerializationUtils.clone(app);
                    RedisCluster cur = YamlEngine.unmarshal(app.getCr(), RedisCluster.class);
                    RedisCluster ps = previousStandby.get(0);
                    cur.getSpec().setRedisShake(ps.getSpec().getRedisShake());
                    currentPrimary.setRole(CloudAppConstant.ROLE_STANDBY);
                    RedisClusterVO redisVO = new RedisClusterVO();
                    BeanUtils.copyProperties(currentPrimary, redisVO);
                    configureMultiAZ(cur, ps, redisVO);
                    KubeClient client = clientService.get(currentPrimary.getKubeId());

                    try {
                        appService.callScheduler(currentPrimary, YamlEngine.marshal(cur), null,
                                ActionEnum.SWITCH_AZ_ROLE, getProcessorClass(ActionEnum.SWITCH_AZ_ROLE));
                    } catch (SchedulerException | JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                    client.updateCustomResource(cur, RedisCluster.class);
                });

    }

    private RedisCommonService redisCommonService = new RedisCommonService();

    @Override
    public Map<String, Object> getAZSummary(CloudApp app) {
        return redisCommonService.getAZSummary(
                app,
                clientService.get(app.getKubeId()),
                getPassword(app),
                appService.findInstances(app.getId()));
    }

    @Autowired
    private RedisClusterMapper redisClusterMapper;
    @Override
    public RedisClusterMapper getMapper() {
        return redisClusterMapper;
    }

    @Data
    public static class RedisClusterVO extends CloudAppVO {
        private Integer masterSize;
        private String restoreFileDir;
        private String backupPath;
        private Map<String, Object> clusterIpMap;
        private String sentinel;

        private String shakeCpu;
        private String shakeMemory;
    }

    @Getter
    @Setter
    public static class RedisOverrideSpec extends OverrideSpec {
        private String sentinel;
        private String restoreFile;
        private String backupPath;
        // cluster
        private Integer masterSize;
        private Integer spareSize;
        private String restoreFileDir;
        private Map<String, Object> clusterIpMap;

        private String shakeCpu;
        private String shakeMemory;
    }

    @Override
    public void restore(BackupHis backupHis, Integer appId, String restoreTime, String ftpFilename, String backupType) {
        // 1. 保存恢复基本信息
        // 2. 将备份文件存放在对应集群的所有节点上
        // 3. 修改对应cr的RestoreFile属性为备份文件名称

        //检查备份文件是否适用于目标应用

        // 0. 创建还原的对象
        RestoreHis restoreHis = new RestoreHis();
        restoreHis.setStatus(StatusConstant.RUNNING);
        Date startDate = new Date();
        //时间转换
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
        //操作记录
        ResourceChangeHis resourceChangeHis = null;
        Integer changeId = null;
        try{
            Timestamp startTime = new Timestamp(System.currentTimeMillis());
            restoreHis.setStartTime(startTime);
            // 1. 插入基础恢复历史和操作记录
            Integer backupAppId = backupHis.getAppId();
            //到这里了
            CloudApp goalApp = appService.get(appId);
            CloudApp backupApp = appService.get(backupAppId);
            restoreHis.setAppId(appId);
            restoreHis.setAppName(goalApp.getName());
            restoreHis.setAppType(goalApp.getKind());
            KubeClient kubeClient = clientService.get(goalApp.getKubeId());
            RedisCluster cr = kubeClient.listCustomResource(RedisCluster.class, goalApp.getCrName(), goalApp.getNamespace());
            //获取备份时的podName,实际是随机选择节点进行恢复
            //String podName = backupHis.getPodName();
            kubeClient = clientService.get(goalApp.getKubeId()); //获取所有节点
            //获取cr，根据cr获取到status属性中的node数组，取到每个数组中第一个ip作为主节点执行恢复脚本
            restoreHis.setPodName(backupUtil.getRestorePodNameRedisCluster(goalApp, kubeClient));
            //应用所属集群
            KubeConfig byId = kubeConfigService.get(goalApp.getKubeId());
            if(null == byId){
                log.error("未获取到集群！");
//                backupFailUpdateHis(backupHis, "未获取到集群！");
                backupUtil.restoreReturn(restoreHis, changeId, "未获取到集群！", StatusConstant.FAIL);
                return;
            }
            restoreHis.setKubeName(byId.getName());
            restoreHis.setMessage("恢复中...");
            restoreHis.setFileName(backupHis.getFileName());
            String backDirInPod = "/backup/";
            restoreHis.setRestoreDir(backDirInPod);
            restoreHis.setFileDeleted(false);
            //插入基本信息
            backupService.commitRestoreHis(restoreHis);
            //插入操作记录
            resourceChangeHis = new ResourceChangeHis(){{
                setInsertTime(startTime);
                setKind(goalApp.getKind());
                setKubeId(goalApp.getKubeId());
                setNamespace(goalApp.getNamespace());
                setCommand("恢复");
                setStatus("2");
                setAction(ActionEnum.RESTORE.getActionType());
                setMsg("恢复中...");
                setAppId(goalApp.getId());
                setAppName(goalApp.getName());
                setKubeName(byId.getName());
                setYaml(goalApp.getCr());
                setUserName(UserUtil.getAsyncUserinfo().getUsername());
                setUserIp(CloudRequestContext.getContext().getUserIp());
                setLastEndTimestamp(System.currentTimeMillis());
                setAppLogicId(goalApp.getLogicAppId());
            }};
            changeId = backupUtil.insertResourceChangeHis(resourceChangeHis);

            backupUtil.checkRestoreAndRecord(resourceChangeHis, goalApp, backupHis, restoreHis);
            goalApp.setStatus(CloudAppConstant.AppStatus.PENDING);
            appService.update(goalApp);

            // 3. 获取备份文件名称,修改对应的cr属性：RestoreFile
            //备份文件名称
            String backupFileName = backupHis.getFileName();
            //构造cr
            RedisClusterSpec spec = cr.getSpec();

            // 设置restore属性
            com.shindata.redis.v1.redisclusterspec.Restore restore = new com.shindata.redis.v1.redisclusterspec.Restore();
            String fullSourceName = backupApp.getNamespace() + "/" + backupApp.getCrName() + "/" + backupFileName;
            restore.setFullSource(fullSourceName);
            //获取备份存储信息
            CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
            HashMap<String, String> remote = new HashMap<>();
            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                remote.put("type", CloudAppConstant.OperatorStorageType.NFS);
                remote.put("address", cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath());
            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                remote.put("type", CloudAppConstant.StorageType.S3);
                remote.put("address", cloudBackupStorageVO.getServer());
                remote.put("bucket", cloudBackupStorageVO.getBucket());
                remote.put("region", cloudBackupStorageVO.getRegion());
                //获取operator的namespace，因为所有operator都相同，所以统一获取mysql的operatornamespace
                String operatorConfig = sysConfigService.findOne("operator.name", "MySQL");
                String operatorNamespace = operatorConfig.split("/")[0];
                remote.put("secret", operatorNamespace + ":backupstorage-secret");
            } else {
                throw new CustomException(600, "恢复失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
            }
            restore.setRemote(remote);
            spec.setRestore(restore);
            cr.setSpec(spec);
            kubeClient.updateCustomResource(cr, RedisCluster.class);
            //创建定时轮询备份结果
            Map map = new HashMap();
            map.put("restoreStartDateSDF", sdf.format(startDate));
            map.put("restoreHisId", restoreHis.getRestoreHisId());
            map.put("resourceChangeId", changeId);
            map.put("crLastUpdateTime", cr.getStatus().getLastUpdateTime());
            appService.callScheduler(goalApp,YamlEngine.marshal(cr),map,ActionEnum.RESTORE,RedisClusterBackupAndRestoreWatch.class,resourceChangeHis);
        }catch (Exception e){
            backupUtil.restoreReturn(restoreHis, changeId, e.getMessage(), StatusConstant.FAIL);
        }
    }

    @Override
    public void delete(CloudApp app) {
        super.delete(app);
        KubeClient client = clientService.get(app.getKubeId());
        client.scaleSts(String.format("rc-%s-redisshake", app.getCrName()), app.getNamespace(), 0);
    }
}