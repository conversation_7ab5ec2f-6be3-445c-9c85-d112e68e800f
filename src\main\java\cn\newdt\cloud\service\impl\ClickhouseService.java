package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.common.OpLogContext;
import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.domain.cr.Node;
import cn.newdt.cloud.dto.*;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.service.sched.impl.*;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.*;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.exception.CustomException;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.shindata.clickhouse.v1.ClickHouseInstallation;
import com.shindata.clickhouse.v1.ClickHouseInstallationSpec;
import com.shindata.clickhouse.v1.clickhouseinstallationspec.Configuration;
import com.shindata.clickhouse.v1.clickhouseinstallationspec.Defaults;
import com.shindata.clickhouse.v1.clickhouseinstallationspec.configuration.Clusters;
import com.shindata.clickhouse.v1.clickhouseinstallationspec.configuration.Zookeeper;
import com.shindata.clickhouse.v1.clickhouseinstallationspec.configuration.clusters.Layout;
import com.shindata.clickhouse.v1.clickhouseinstallationspec.configuration.zookeeper.Nodes;
import com.shindata.clickhouse.v1.clickhouseinstallationspec.defaults.Templates;
import com.shindata.clickhouse.v1.clickhouseinstallationspec.templates.PodTemplates;
import com.shindata.clickhouse.v1.clickhouseinstallationspec.templates.ServiceTemplates;
import com.shindata.clickhouse.v1.clickhouseinstallationspec.templates.VolumeClaimTemplates;
import io.fabric8.kubernetes.api.model.ConfigMap;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.client.CustomResource;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.MapListHandler;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Connection;
import java.sql.Driver;
import java.sql.Statement;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cn.newdt.cloud.constant.ActionEnum.CREATE_APP_USER;
import static cn.newdt.cloud.constant.CloudAppConstant.CLICKHOUSE_DATA_BACKUP_PATH;
import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgCategory.*;

@Slf4j
@Service
public class ClickhouseService extends DefaultAppKindService<ClickHouseInstallation> implements ServiceManageOperation, ComposedAppService {
    private static final String CLICKHOUSE_USER_NAME = "ckAdmin";
    private static final String CLICKHOUSE_PASS_WORD = "ckAdmin";

    //用户相关设置
    private final String ACCESS_MANAGEMENT_ENABLED = "1";
    private final String PROFILE_DEFAULT = "default";
    private final String ALLOWED_IP = "::/0";

    //Service模版名称
    private final String CLICKHOUSE_SERVICE_TEMPLATE_NAME = "clickhouse-service-template";
    //pod-模版名称
    private final String CLICKHOUSE_POD_RESOURCE_TEMPLATE_NAME = "clickhouse-pod-resource-template";
    //存储-模板名称
    public static final String CLICKHOUSE_DATAVOLUME_TEMPLATE_NAME = "clickhouse-datavolume-storage";

    //备份路径
    public final String CLICKHOUSE_BACKUP_PATH = "/shindata/backup/";

    //s3 path
    public static final String CLICKHOUSE_S3_PATH = "/mnt";


    //filebeat name
    private final String CLICKHOUSE_FILEBEAT_CONFIGMAP_NAME = "operator-filebeat-configmap";
    private final String CLICKHOUSE_SCRIPTS_CONFIGMAP_NAME = "clickhouse-scripts-config";

    //driver name
    private final String CLICKHOUSE_DRIVER_NAME = "com.clickhouse.jdbc.ClickHouseDriver";

    @Autowired
    private KubeClientService kubeClientService;
    @Autowired
    private CloudDatabaseUserService dbUserService;


    @Override
    protected boolean supportIPAM(CloudAppVO app) {
        return false;
    }

    @Override
    protected void setInstallExtData(CloudAppVO vo) {
        Map adminUserParamMap = new HashMap();
        adminUserParamMap.put("username", vo.getUsername());
        adminUserParamMap.put("password", vo.getPassword());
        vo.setExtInstallData(adminUserParamMap);
    }

    public Connection getDbConnection(AppDBVO dbvo) throws Exception {
        // 加载JDBC驱动
        Driver driver = (Driver) Class.forName(CLICKHOUSE_DRIVER_NAME).newInstance();
        return driver.connect(getConnectionUrl(dbvo.getIp(), dbvo.getPort()), getConnectProperties());
    }

    //连接参数、用户名、密码登
    private Properties getConnectProperties() {
        Properties props = new Properties();
        props.put("user", CLICKHOUSE_USER_NAME);
        props.put("password", CLICKHOUSE_PASS_WORD);
        return props;
    }

    private String getConnectionUrl(String ip, Integer port) {
        return new StringBuilder("jdbc:clickhouse://").append(ip).append(":").append(port).append("/default").toString();
    }

    @Override
    public List<ServiceManager> createService(
            String serviceType, CloudAppVO vo, List<?> serviceResources, CustomResource installCr) {
        return openSourceKindServiceBuilder(serviceType, vo, serviceResources, null);
    }

    @Override
    public String getAppSystemName(InstallAppVo<? extends OverrideSpec> vo) {
        return appService.getAppByCrName(
                vo.getCrName(), vo.getNamespace(), AppKind.ClickHouse_Zookeeper).getAppSystemName();
    }

    @Override
    public void deleteCrControlledResources(CloudApp app) {
        //查询出相关的 sts 类型并删除，加快 应用 删除速度
        KubeClient kubeClient = clientService.get(app.getKubeId());
        String namespace = app.getNamespace();
        kubeClient.listStatefulSetWithLables(namespace, AppKind.Clickhouse.labels(app.getCrName()))
                .parallelStream().forEach(sts -> {
                    kubeClient.deleteStatefulset(sts.getMetadata().getName(), namespace);
                });
    }

    @Override
    public void updateService(List<ServiceManager> svcMgrs, CloudApp app, Object oldServiceResource) throws Exception {
        openSourceKindUpdateServiceBuilder(svcMgrs, app, oldServiceResource);
    }

    @Override
    public void update(ResourceDTO patch) throws Exception {
        Consumer<ClickHouseInstallation> modifier = (current) -> {
            current.getSpec().getTemplates().getPodTemplates().get(0).getSpec().getContainers().stream()
                    .filter(c -> c.getName().equals("clickhouse")).findFirst().get()
                    .setResources(ResourceHelper.getInstance().resourceRequirements(patch));
        };
        Consumer<ClickHouseInstallation> storageModifier = (current) -> {
            current.getSpec().getTemplates().getVolumeClaimTemplates().stream().filter(volumeClaimTemplate -> {
                return CLICKHOUSE_DATAVOLUME_TEMPLATE_NAME.equals(volumeClaimTemplate.getName());
            }).findFirst().get().getSpec().getResources().setRequests(
                    new HashMap<String, Quantity>() {
                        {
                            put("storage", new Quantity(patch.getDisk()));
                        }
                    }
            );
        };
        operationHandler.handleUpdate(patch, modifier, this, ClickHouseInstallation.class, storageModifier);
    }


    @Override
    public CloudApp referringApp(CloudApp app) {
        String crYaml = StringUtils.isEmpty(app.getCr()) ? app.getCrRun() : app.getCr();
        ClickHouseInstallation cr = YamlEngine.unmarshal(crYaml, ClickHouseInstallation.class);
        String zkCrName = cr.getMetadata().getLabels().get(CloudAppConstant.CustomLabels.ZK_REF);
        return appService.getAppByCrName(zkCrName, app.getNamespace(), AppKind.ClickHouse_Zookeeper);
    }

    @Override
    public String referringAppName(CloudApp app) {
        return ComposedAppService.super.referringAppName(app);
    }

    @Override
    public AppKind getKind() {
        return AppKind.Clickhouse;
    }


    @Override
    public boolean nodePolicy() {
        return false;
    }

    @Override
    public ClickHouseInstallation doInstall(CloudAppVO vo, List<String> ips) throws Exception {
        Map<String, String> config = setupDbParamConfig(vo);
        ClickhouseClusterVO clickhouseClusterVO = (ClickhouseClusterVO) vo;
        KubeClient kubeClient = kubeClientService.get(vo.getKubeId());
        //创建 filebeat-cm信息
//        ensureFilebeatCM(kubeClient, vo.getCrName(), vo.getNamespace());
        //创建 clickhouse 脚本 cm 挂载
        ensureClickHouseScriptsCM(kubeClient, vo.getCrName(), vo.getNamespace());
        //构建exporter svc
        ensureExporterSvc(kubeClient, vo.getCrName(), vo.getNamespace());
        //构建 Clickhouse-cr信息 <pod:clickhouse、filebeat、clickhouse-backup>
        return buildClickhouseCr(clickhouseClusterVO, ips, config);
    }

    /**
     * 构建exporter svc
     */
    private void ensureExporterSvc(KubeClient kubeClient, String crName, String namespace) {
        String svc_yaml = "apiVersion: v1\n" +
                "kind: Service\n" +
                "metadata:\n" +
                "  labels:\n" +
                "    app.kubernetes.io/component: clickhouse\n" +
                "    app.kubernetes.io/name: " + crName + "\n" +
                "    clickhouse.altinity.com/namespace: " + namespace + "\n" +
                "  name: " + crName + "\n" +
                "  namespace: " + namespace + "\n" +
                "spec:\n" +
                "  internalTrafficPolicy: Cluster\n" +
                "  ipFamilies:\n" +
                "  - IPv4\n" +
                "  ipFamilyPolicy: SingleStack\n" +
                "  ports:\n" +
                "  - name: exporter\n" +
                "    port: 9363\n" +
                "    protocol: TCP\n" +
                "    targetPort: 9363\n" +
                "  selector:\n" +
                "    app.kubernetes.io/component: clickhouse\n" +
                "    app.kubernetes.io/name: " + crName + "\n" +
                "    clickhouse.altinity.com/namespace: " + namespace + "\n" +
                "  sessionAffinity: None\n" +
                "  type: ClusterIP";

        kubeClient.applyYaml(svc_yaml, namespace);
    }

    @Override
    public void delete(CloudApp app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        //查询出相关的 sts 类型并缩容为0
        String namespace = app.getNamespace();
        kubeClient.listStatefulSetWithLables(namespace, AppKind.Clickhouse.labels(app.getCrName()))
                .parallelStream().forEach(sts -> {
            kubeClient.scaleSts(sts.getMetadata().getName(), namespace, 0);
        });
        ClickHouseInstallation cr = kubeClient.listCustomResource(ClickHouseInstallation.class, app.getCrName(), app.getNamespace());
        if (null == cr) return;
        cr.getSpec().setStop(ClickHouseInstallationSpec.Stop.TRUE);
        kubeClientService.get(app.getKubeId()).updateCustomResource(cr, ClickHouseInstallation.class);
    }

    @Override
    public void recreate(CloudApp app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        //查询出相关的 sts 类型并扩容为1
        String namespace = app.getNamespace();
        kubeClient.listStatefulSetWithLables(namespace, AppKind.Clickhouse.labels(app.getCrName()))
                .parallelStream().forEach(sts -> {
            kubeClient.scaleSts(sts.getMetadata().getName(), namespace, 1);
        });
        ClickHouseInstallation cr = kubeClient.listCustomResource(ClickHouseInstallation.class, app.getCrName(), app.getNamespace());
        if (null == cr) return;
        cr.getSpec().setStop(ClickHouseInstallationSpec.Stop.FALSE);
        kubeClientService.get(app.getKubeId()).updateCustomResource(cr, ClickHouseInstallation.class);
    }

    @Override
    public void scale(int id, OverrideSpec vo, ActionEnum action) throws Exception {
        ClickhouseOverrideSpec clickhouseSpec = (ClickhouseOverrideSpec) vo;
        scale(id, clickhouseSpec.getShardsSize(), clickhouseSpec.getReplicasSize());
    }

    /**
     * 扩缩容统一接口
     *
     * @param appId        应用id
     * @param shardsSize   变化后的数量
     * @param replicasSize 变化后的数量
     */
    @Transactional
    public void scale(int appId, Integer shardsSize, Integer replicasSize) throws Exception {
        CustPreconditions.checkState(shardsSize > 0, "分片数量不能<1");
        CustPreconditions.checkState(replicasSize > 0, "副本数量不能<1");
        CustPreconditions.checkNotNull(appId, "appId not be null!");

        //拿到相关的 分片、副本数量 判断是 扩容还是缩容
        CloudApp app = appService.get(appId);
        ClickHouseInstallation cr = kubeClientService.get(app.getKubeId()).listCustomResource(ClickHouseInstallation.class, app.getCrName(), app.getNamespace());
        Layout layout = cr.getSpec().getConfiguration().getClusters().get(0).getLayout();
        Long shardsCount = null == layout ? 1 : layout.getShardsCount();
        Long replicasCount = null == layout ? 1 : layout.getReplicasCount();

        if (shardsSize.equals(shardsCount) && replicasSize.equals(replicasCount)) {
            log.warn("分片数、副本数与当前一致，未进行扩缩容操作！");
            throw new Exception("分片数、副本数与当前一致，未进行扩缩容操作！");
        }
        int shardResult = Long.compare(shardsSize, shardsCount);
        int replicasResult = Long.compare(replicasSize, replicasCount);

        Map<String, Object> map = new HashMap<>();
        map.put("app", app);

        ActionEnum actionEnum = (shardResult == 1 || replicasResult == 1) ? ActionEnum.SCALE_OUT : ActionEnum.SCALE_IN;
        map.put("type", actionEnum);

        layout = layout == null ? new Layout() : layout;
        layout.setShardsCount(Long.valueOf(shardsSize));
        layout.setReplicasCount(Long.valueOf(replicasSize));
        cr.getSpec().getConfiguration().getClusters().get(0).setLayout(layout);

        app.setCrRun(YamlEngine.marshal(cr));
        String applyYaml = YamlEngine.marshal(cr);

        OpLogContext.instance().YAML("CR", applyYaml, app.getCr());
        appService.callScheduler(app, applyYaml, map, actionEnum, ClickhouseScaleWatch.class);
        clientService.get(app.getKubeId()).updateCustomResource(cr, ClickHouseInstallation.class);

    }

    @Override
    public void modifyConfigParam(Map<String, String> params, Integer appId, String componentKind) throws Exception {
        CustPreconditions.checkState(!org.springframework.util.CollectionUtils.isEmpty(params), "要修改的参数列表为空");

        String prohibitContent = sysConfigService.findOne(PARAM_PROHIBIT, getKind().getProduct());
        final Set<String> prohibited = prohibitContent == null ? new HashSet<>() : new HashSet<>(Arrays.asList(prohibitContent.split(",")));

        CloudApp app = appService.get(appId);
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());

        ClickHouseInstallation crInK8s = kubeClient.listCustomResource(ClickHouseInstallation.class, app.getCrName(), app.getNamespace());

        // 要回滚的cr信息
        String oldCrYaml = YamlEngine.marshal(crInK8s);

        //禁止修改参数校验
        Optional<String> prohibit = params.keySet().stream().filter(key -> prohibited.contains(key)).findFirst();
        if (prohibit.isPresent())
            throw new CustomException(600, "存在禁止修改的参数，请移除");

        Map<String, String> settings = crInK8s.getSpec().getConfiguration().getSettings();

        settings = CollectionUtils.isEmpty(settings) ? new HashMap<>() : settings;
        settings.putAll(params);

        crInK8s.getSpec().getConfiguration().setSettings(settings);

        HashMap<Object, Object> map = new HashMap<>();
        map.put("params", params);
        map.put("appId", appId);

        //修改后的cr信息
        String applyYaml = YamlEngine.marshal(crInK8s);
        app.setCrRun(applyYaml);

        OpLogContext.instance().YAML("CR", applyYaml, oldCrYaml);
        appService.callScheduler(app, YamlEngine.marshal(crInK8s), map, ActionEnum.MODIFY_PARAM, getProcessorClass(ActionEnum.MODIFY_PARAM));
        kubeClient.updateCustomResource(crInK8s, ClickHouseInstallation.class);
    }


    private ClickHouseInstallation buildClickhouseCr(ClickhouseClusterVO vo, List<String> ips, Map<String, String> config) {
        ClickHouseInstallation clickHouseCr = new ClickHouseInstallation();
        String crName = vo.getCrName();
        //需要手动添加 固定 label 以及 需要添加 一个 引用的 zookeeper 的 crname label信息
        Map<String, String> labels = Label.toMap(AppKind.Clickhouse.labels(crName));
        labels.put(CloudAppConstant.CustomLabels.ZK_REF, crName);

        //构建label
        String namespace = vo.getNamespace();
        clickHouseCr.setMetadata(new ObjectMetaBuilder().withName(crName)
                .withNamespace(namespace)
                .withLabels(labels)
                .build());

        //构建spec
        ClickHouseInstallationSpec clickHOuseCrSpec = new ClickHouseInstallationSpec();
        //构建配置信息
        Configuration configuration = new Configuration();
        //构建相关的参数配置
        config.put("prometheus/asynchronous_metrics", "true");
        config.put("prometheus/endpoint", "/metrics");
        config.put("prometheus/events", "true");
        config.put("prometheus/metrics", "true");
        config.put("prometheus/port", "9363");
        config.put("prometheus/status_info", "true");

        configuration.setSettings(config);
        //构建用户信息
        configuration.setUsers(buildConfigurationUsers());
        //构建集群
        configuration.setClusters(
                Collections.singletonList(
                        buildConfigurationClusters(vo.getShardsSize(), vo.getReplicasSize(),
                                ClickhouseUtil.getClickhouseClusterName())));
        //构建 zookeeper 配置信息
        configuration.setZookeeper(buildConfigurationZookeeper(crName, namespace));
        clickHOuseCrSpec.setConfiguration(configuration);
        clickHOuseCrSpec.setDefaults(buildDefaultTemplates());
        clickHOuseCrSpec.setTemplates(buildTemplates(vo, ips));
        clickHouseCr.setSpec(clickHOuseCrSpec);
        return clickHouseCr;
    }

    private Map<String, Object> buildConfigurationUsers() {
        //用户
        Map<String, Object> usersMap = new HashMap<>();
        setUserData(usersMap, CLICKHOUSE_USER_NAME, CLICKHOUSE_PASS_WORD);
        return usersMap;
    }

    private Clusters buildConfigurationClusters(Integer shardsCount, Integer replicasCount, String crName) {
        Clusters clusters = new Clusters();
        Layout layout = new Layout();
        layout.setShardsCount(Long.valueOf(shardsCount));
        layout.setReplicasCount(Long.valueOf(replicasCount));
        clusters.setName(crName);
        clusters.setLayout(layout);
        return clusters;
    }

    private Zookeeper buildConfigurationZookeeper(String crName, String namespace) {
        CloudApp zookeeperApp = appService.getAppByCrName(crName, namespace, AppKind.ClickHouse_Zookeeper);
        Zookeeper zookeeper = new Zookeeper();
//        zookeeper.setIdentity(CLICKHOUSE_USER_NAME + ":" + CLICKHOUSE_PASS_WORD);
        zookeeper.setOperation_timeout_ms(10000L);
        zookeeper.setSession_timeout_ms(30000L);
        //构建 zookeeper host
        List<String> zookeeperHostList = JSONObject.parseArray(zookeeperApp.getIpList(), Node.class).stream().map(Node::getIp).map(ip -> buildZookeeperHost(ip, zookeeperApp.getNamespace())).collect(Collectors.toList());
        List<Nodes> zookeeperNodes = zookeeperHostList.stream().map(zookeeperHost -> {
            Nodes nodes = new Nodes();
            nodes.setHost(zookeeperHost);
            nodes.setPort(2181L);
            return nodes;
        }).collect(Collectors.toList());
        zookeeper.setNodes(zookeeperNodes);
        return zookeeper;
    }

    private Defaults buildDefaultTemplates() {
        Defaults defaults = new Defaults();
        Templates templates = new Templates();
        templates.setPodTemplate(CLICKHOUSE_POD_RESOURCE_TEMPLATE_NAME);
        //此处不在创建Service，迁移到整体创建Service代码中
//        templates.setServiceTemplate(CLICKHOUSE_SERVICE_TEMPLATE_NAME);
//        templates.setDataVolumeClaimTemplate(CLICKHOUSE_DATAVOLUME_TEMPLATE_NAME);
        defaults.setTemplates(templates);
        return defaults;
    }

    //暂时不支持指定ip 保留参数：iplist
    private com.shindata.clickhouse.v1.clickhouseinstallationspec.Templates buildTemplates(ClickhouseClusterVO vo, List<String> ips) {
        com.shindata.clickhouse.v1.clickhouseinstallationspec.Templates templates = new com.shindata.clickhouse.v1.clickhouseinstallationspec.Templates();
        //此处不在创建Service，迁移到整体创建Service代码中
//        templates.setServiceTemplates(Collections.singletonList(buildServiceTemplates(vo.getWritePort(vo.getCrName()))));
        templates.setPodTemplates(Collections.singletonList(buildPodTemplates(vo)));
        templates.setVolumeClaimTemplates(Collections.singletonList(buildVolumeClaimTemplates(vo.getDisk(), vo.getStorageClassName())));
        return templates;
    }

    private ServiceTemplates buildServiceTemplates(Integer writePort, String crnmae) {
        ServiceTemplates serviceTemplates = new ServiceTemplates();
        serviceTemplates.setName(CLICKHOUSE_SERVICE_TEMPLATE_NAME);
        serviceTemplates.setGenerateName(getKind().getWriteServiceName(crnmae, null));
        ServiceSpec serviceSpec = new ServiceSpecBuilder().withType(CloudAppConstant.ServiceType.NODE_PORT)
                .withPorts(
                        new ServicePortBuilder().withName("http").withPort(8123).withNodePort(writePort).build(),
                        new ServicePortBuilder().withName("tcp").withPort(9000).build()
                ).build();
        serviceTemplates.setSpec(serviceSpec);
        return serviceTemplates;
    }

    private PodTemplates buildPodTemplates(ClickhouseClusterVO vo) {
        String crname = vo.getCrName();
        String version = vo.getVersion();
        SelectorDTO[] selector = vo.getSelector();
        TolerationDTO[] toleration = vo.getToleration();
        Boolean antiAffinityRequired = vo.getAntiAffinityRequired();
        // 查找image
        Map<ImageKindEnum, String> imageManifest = appConfigService.getImageManifest(getKind(), version);
        String clickhouseImage = imageManifest.get(ImageKindEnum.MainImage);
        if (StringUtils.isEmpty(clickhouseImage)) {
            throw new CustomException(600, "Broker应用未找到对应的镜像信息！镜像版本：" + version);
        }

        // imageManifest的key 是cr中镜像属性名
        String filebeatImage = imageManifest.get(ImageKindEnum.Filebeat);
        String clickhouseBackupImage = imageManifest.get(ImageKindEnum.Clickhouse_Backup);
        String clickhouseS3FsImage = imageManifest.get(ImageKindEnum.Clickhouse_S3FS);

        String filebeat_volume_name = "clickhouse-filebeat-volume";
        String clickhouse_script_volume_name = "clickhouse-script-volume-name";
        String clickhouse_s3_name = "backup-mnt-shared";
        PodTemplates podTemplates = new PodTemplates();
        podTemplates.setName(CLICKHOUSE_POD_RESOURCE_TEMPLATE_NAME);
        podTemplates.setMetadata(new ObjectMetaBuilder().withLabels(Label.toMap(AppKind.Clickhouse.labels(crname))).build());
        PodSpec podSpec = new PodSpecBuilder()
                .withVolumes(
                        new VolumeBuilder().withName(CloudAppConstant.Volumes.TIMEZONE_NAME).withNewHostPath(CloudAppConstant.Volumes.TIMEZONE_HOSTPATH, "").build(),
                        new VolumeBuilder().withName(filebeat_volume_name).withConfigMap(new ConfigMapVolumeSourceBuilder().withName(CLICKHOUSE_FILEBEAT_CONFIGMAP_NAME).withDefaultMode(416).build()).build(),
                        new VolumeBuilder().withName(clickhouse_script_volume_name).withConfigMap(new ConfigMapVolumeSourceBuilder().withName(CLICKHOUSE_SCRIPTS_CONFIGMAP_NAME).withDefaultMode(416).build()).build(),
                        new VolumeBuilder().withName(clickhouse_s3_name).withNewEmptyDir().and().build()
                ).withContainers(
                        new ContainerBuilder().withName("clickhouse")
                                .withImage(clickhouseImage)
                                .withImagePullPolicy("IfNotPresent")
                                .withResources(ResourceHelper.getInstance().resourceRequirements(
                                        new ResourceDTO() {{
                                            setMemory(vo.getMemory());
                                            setCpu(vo.getCpu());
                                        }}
                                ))
                                .withVolumeMounts(
                                        new VolumeMountBuilder().withName(CloudAppConstant.Volumes.TIMEZONE_NAME).withMountPath(CloudAppConstant.VolumeMounts.TIMEZONE_MOUNTPATH).build(),
                                        new VolumeMountBuilder().withName(CLICKHOUSE_DATAVOLUME_TEMPLATE_NAME).withMountPath("/var/lib/clickhouse").build()
                                ).build(),
                        new ContainerBuilder().withName("clickhouse-backup")
                                .withImage(clickhouseBackupImage)
                                .withImagePullPolicy("IfNotPresent")
                                .withCommand("bash", "-xc", "/bin/clickhouse-backup server")
                                .withEnv(
                                        new EnvVarBuilder().withName("CLICKHOUSE_USERNAME").withValue(CLICKHOUSE_USER_NAME).build(),
                                        new EnvVarBuilder().withName("CLICKHOUSE_PASSWORD").withValue(CLICKHOUSE_PASS_WORD).build(),
                                        new EnvVarBuilder().withName("APP_TYPE").withValue(vo.getKind()).build(),
                                        new EnvVarBuilder().withName("ARCH_TYPE").withValue(vo.getArch()).build(),
                                        new EnvVarBuilder().withName("APP_NAMESPACE").withValueFrom(new EnvVarSourceBuilder().withFieldRef(new ObjectFieldSelectorBuilder().withApiVersion("v1").withFieldPath("metadata.namespace").build()).build()).build(),
                                        new EnvVarBuilder().withName("APP_NAME").withValue(crname).build()
//                                        new EnvVarBuilder().withName("CLICKHOUSE_DISK_MAPPING").withValue("default:" + CLICKHOUSE_DATA_BACKUP_PATH).build(),
//                                        new EnvVarBuilder().withName("BACKUP_DIR").withValue(CLICKHOUSE_DATA_BACKUP_PATH).build()
                                )
                                .withSecurityContext(new SecurityContextBuilder().withPrivileged(true).withRunAsUser(0L).build())
                                .withVolumeMounts(
                                        new VolumeMountBuilder().withName(CloudAppConstant.Volumes.TIMEZONE_NAME).withMountPath(CloudAppConstant.VolumeMounts.TIMEZONE_MOUNTPATH).build(),
                                        new VolumeMountBuilder().withName(CLICKHOUSE_DATAVOLUME_TEMPLATE_NAME).withMountPath("/var/lib/clickhouse/").build(),
                                        new VolumeMountBuilder().withName(clickhouse_s3_name).withMountPath(CLICKHOUSE_S3_PATH).withMountPropagation("Bidirectional").build(),
                                        new VolumeMountBuilder().withName(clickhouse_script_volume_name).withMountPath("/scripts").withReadOnly(Boolean.TRUE).build()
                                )
                                .build(),
                        new ContainerBuilder().withName("filebeat")
                                .withImage(filebeatImage)
                                .withImagePullPolicy("IfNotPresent")
                                .withCommand("bash").withArgs("/etc/filebeat/filebeat-entrypoint.sh")
                                .withEnv(
                                        new EnvVarBuilder().withName("APP_NAMESPACE").withValueFrom(new EnvVarSourceBuilder().withFieldRef(new ObjectFieldSelectorBuilder().withApiVersion("v1").withFieldPath("metadata.namespace").build()).build()).build(),
                                        new EnvVarBuilder().withName("POD_NAME").withValueFrom(new EnvVarSourceBuilder().withFieldRef(new ObjectFieldSelectorBuilder().withApiVersion("v1").withFieldPath("metadata.name").build()).build()).build(),
                                        new EnvVarBuilder().withName("POD_IP").withValueFrom(new EnvVarSourceBuilder().withFieldRef(new ObjectFieldSelectorBuilder().withApiVersion("v1").withFieldPath("status.podIP").build()).build()).build(),
                                        new EnvVarBuilder().withName("CR_NAME").withValue(crname).build(),
                                        new EnvVarBuilder().withName("FILEBEAT_CFG_FILE").withValue("clickhouse-cluster-filebeat.yaml").build()
                                )
                                .withSecurityContext(new SecurityContextBuilder().withRunAsUser(0L).build())
                                .withVolumeMounts(
                                        new VolumeMountBuilder().withName(CloudAppConstant.Volumes.TIMEZONE_NAME).withMountPath(CloudAppConstant.VolumeMounts.TIMEZONE_MOUNTPATH).build(),
                                        new VolumeMountBuilder().withName(filebeat_volume_name).withMountPath("/etc/filebeat").build(),
                                        new VolumeMountBuilder().withName(CLICKHOUSE_DATAVOLUME_TEMPLATE_NAME).withMountPath("/var/lib/clickhouse/").build(),
                                        new VolumeMountBuilder().withName(clickhouse_script_volume_name).withMountPath("/scripts").withReadOnly(Boolean.TRUE).build()
                                ).build(),
                        new ContainerBuilder().withName("s3fs-container")
                                .withImage(clickhouseS3FsImage)
                                .withImagePullPolicy("IfNotPresent")
                                .withCommand("sh", "-c").withArgs("tail -f /dev/null")
                                .withSecurityContext(new SecurityContextBuilder().withPrivileged(true).withRunAsUser(0L).build())
                                .withLifecycle(new LifecycleBuilder().withPreStop(new HandlerBuilder().withExec(new ExecActionBuilder().withCommand(Arrays.asList("sh", "/scripts/mount-prestop.sh")).build()).build()).build())
                                .withVolumeMounts(
                                        new VolumeMountBuilder().withName(CloudAppConstant.Volumes.TIMEZONE_NAME).withMountPath(CloudAppConstant.VolumeMounts.TIMEZONE_MOUNTPATH).build(),
                                        new VolumeMountBuilder().withName(clickhouse_s3_name).withMountPath(CLICKHOUSE_S3_PATH).withMountPropagation("Bidirectional").build(),
                                        new VolumeMountBuilder().withName(clickhouse_script_volume_name).withMountPath("/scripts").withReadOnly(Boolean.TRUE).build()
                                )
                                .build()

                ).build();

        //设置 亲和、污点
        Affinity affinity = null;
        if (null != selector) {
            affinity = new AffinityBuilder().withNodeAffinity(convertCRNodeAffinity(selector, NodeAffinity.class)).build();
        }
        if (antiAffinityRequired) {
            // 强制pod反亲和
            PodAffinityTerm podAffinityTerm = new PodAffinityTermBuilder().withLabelSelector(new LabelSelectorBuilder()
                    .withMatchLabels(Label.toMap(AppKind.Clickhouse.labelOfPod(vo)))
                    .build()).withTopologyKey("kubernetes.io/hostname").build();
            PodAntiAffinity podAntiAffinity = new PodAntiAffinityBuilder().withRequiredDuringSchedulingIgnoredDuringExecution(Lists.newArrayList(podAffinityTerm)).build();
            if (affinity == null) affinity = new AffinityBuilder().withPodAntiAffinity(podAntiAffinity).build();
            else affinity.setPodAntiAffinity(podAntiAffinity);
        }
        podSpec.setAffinity(affinity);
        podSpec.setTolerations(convertCRTolerations(toleration, Toleration.class));
        podTemplates.setSpec(podSpec);
        return podTemplates;
    }

    private VolumeClaimTemplates buildVolumeClaimTemplates(String disk, String storageClassName) {
        VolumeClaimTemplates volumeClaimTemplates = new VolumeClaimTemplates();
        volumeClaimTemplates.setName(CLICKHOUSE_DATAVOLUME_TEMPLATE_NAME);
        PersistentVolumeClaimSpec pvcSpec = new PersistentVolumeClaimSpecBuilder()
                .withAccessModes("ReadWriteOnce")
                .withStorageClassName(storageClassName)
                .withNewResources().withRequests(Collections.singletonMap("storage", new Quantity(disk))).endResources()
                .build();
        volumeClaimTemplates.setSpec(pvcSpec);
        return volumeClaimTemplates;
    }


    private void setUserData(Map<String, Object> additionalProperties, String username, String password) {
        additionalProperties.put(username + "/networks/ip", ALLOWED_IP);
        additionalProperties.put(username + "/password", password);
        additionalProperties.put(username + "/profile", PROFILE_DEFAULT);
        additionalProperties.put(username + "/access_management", ACCESS_MANAGEMENT_ENABLED);
        additionalProperties.put(username + "/named_collection_control", ACCESS_MANAGEMENT_ENABLED);
        additionalProperties.put(username + "/show_named_collections", ACCESS_MANAGEMENT_ENABLED);
        additionalProperties.put(username + "/show_named_collections_secrets", ACCESS_MANAGEMENT_ENABLED);
    }

    private String buildZookeeperHost(String ip, String nameSpace) {
        return ip.replace(".", "-").replace(":", "-") + "." + nameSpace + ".pod.cluster.local";
    }

    /**
     * 此为模板, 一个ns下的pod共用一个. mount时替换fields属性
     */
//    private void ensureFilebeatCM(KubeClient kubeClient, String cmName, String namespace) {
//        ConfigMap cm = kubeClient.getConfigMap(cmName, namespace);
//        if (cm == null) {
//            String cmYaml = sysConfigService.findOne(OPERATOR_CONFIG, "Clickhouse.config");
//            Map<String, String> param = new HashMap<>();
//            param.put("NAMESPACE", namespace);
//            param.put("NAME", CLICKHOUSE_FILEBEAT_CONFIGMAP_NAME);
//            param.put("es_host", esUtil.getEsIp() + ":" + esUtil.getEsPort());
//            param.put("es_username", esUtil.getEsUsername());
//            param.put("es_pwd", esUtil.getEsPassword());
//            param.put("es_protocol", esUtil.getProtocol());
//            cmYaml = YamlUtil.evaluateTemplate(cmYaml, param);
//            kubeClient.applyYaml(cmYaml, namespace);
//        }
//    }

    /**
     * 创建 clickhouse 的脚本 cm
     */
    private void ensureClickHouseScriptsCM(KubeClient kubeClient, String cmName, String namespace) {
        ConfigMap cm = kubeClient.getConfigMap(cmName, namespace);
        if (cm == null) {
            String cmYaml = sysConfigService.findOne(CONFIGMAP_TEMPLATE, "Clickhouse_Scripts_CM");
            Map<String, String> param = new HashMap<>();
            param.put("NAMESPACE", namespace);
            param.put("NAME", CLICKHOUSE_SCRIPTS_CONFIGMAP_NAME);
            cmYaml = YamlUtil.evaluateTemplate(cmYaml, param);
            kubeClient.applyYaml(cmYaml, namespace);
        }
    }


    @Override
    public InstallAppVo<ClickhouseOverrideSpec> parseInstallVo(String data) {
        InstallAppVo<ClickhouseOverrideSpec> vo = JsonUtil.toObject(data, new com.fasterxml.jackson.core.type.TypeReference<InstallAppVo<ClickhouseOverrideSpec>>() {
        });
        if (vo != null) {
            if (vo.getSpec() != null && vo.getSpec().getMembers() == 0) {
                ClickhouseOverrideSpec spec = vo.getSpec();
                spec.setMembers(spec.getShardsSize() * (spec.getReplicasSize()));
            }
            if (!CollectionUtils.isEmpty(vo.getOverrideSpecs())) {
                for (ClickhouseOverrideSpec spec : vo.getOverrideSpecs().values()) {
                    if (spec.getMembers() == 0 && spec.getShardsSize() != null && spec.getReplicasSize() != null)
                        spec.setMembers(spec.getShardsSize() * (spec.getReplicasSize()));
                }
            }
        }
        return vo;
    }

    public void operationUser(DatabaseUserOperatorVO databaseUserOperatorVO) throws Exception {
        //查询应用信息
        CloudApp app = appService.getCloudAppByLogicId(databaseUserOperatorVO.getLogicid());
        String operationType = databaseUserOperatorVO.getOperationType();
        String password = databaseUserOperatorVO.getPassword();
        ActionEnum operationTypeEnum = ActionEnum.actionTypeOf(operationType);


        ResourceChangeHis resourceChangeHis = appService.getResourceChangeHis(app, operationTypeEnum, StatusConstant.RUNNING, "", null);
        resourceChangeHisService.add(resourceChangeHis);
        String initSql = "";
        String flushSql = "";
        switch (operationTypeEnum) {
            case CREATE_APP_USER:
                if (StringUtils.isEmpty(password)) {
                    updateResourceChangeHis(resourceChangeHis, StatusConstant.FAIL, (operationTypeEnum + "失败:" + "未填写新增用户的密码"));
                    throw new CustomException(600, operationTypeEnum + "失败:" + "未填写新增用户的密码");
                }
                initSql = "Create user IF NOT EXISTS %s on cluster \'%s\' identified by \'%s\';";
                break;
            case DELETE_APP_USER:
                initSql = "drop user IF EXISTS %s on cluster \'%s\'";
                break;
            case RESET_APP_PASSWORD:
                if (StringUtils.isEmpty(password)) {
                    updateResourceChangeHis(resourceChangeHis, StatusConstant.FAIL, (operationTypeEnum + "失败:" + "未填写新增用户的密码"));
                    throw new CustomException(600, operationTypeEnum + "失败:" + "未填写新的密码");
                }
                initSql = "alter user %s on cluster \'%s\' identified by \'%s\';";
                break;
            default:
                throw new CustomException(600, "Unexpected operation type : " + operationType);
        }
        initSql = String.format(initSql, databaseUserOperatorVO.getUsername(), ClickhouseUtil.getClickhouseClusterName(), StringUtils.isEmpty(password) ? "" : decryptPassword(password));
        try {
            List<String> execSqls = new ArrayList<>();
            execSqls.add(initSql);
            if (CREATE_APP_USER.equals(operationTypeEnum) && !CollectionUtils.isEmpty(databaseUserOperatorVO.getDataBaseAuthVOS())) {
                //做修改权限操作
                execSqls.addAll(buildUserAuthSql(databaseUserOperatorVO.getDataBaseAuthVOS(), Boolean.TRUE, ClickhouseUtil.getClickhouseClusterName()));
            }
            runExecSql(app, operationTypeEnum, execSqls, flushSql);
            updateResourceChangeHis(resourceChangeHis, StatusConstant.SUCCESS, operationTypeEnum.getAppOperation() + "成功！");
        } catch (Exception e) {
            log.error("用户修改操作失败" + e.getMessage());
            updateResourceChangeHis(resourceChangeHis, StatusConstant.FAIL, operationTypeEnum.getAppOperation() + "失败:" + e.getMessage());
            throw new CustomException(600, "用户修改操作失败");
        }

    }


    public void runExecSql(CloudApp app, ActionEnum operationTypeEnum, List<String> execSqlList, String flushSql) {
        //获取写连接
        try (Connection connection = getDbConnection(
                accessManagementService.getFirstWriteServiceAddressByAppIdAndKubeId(
                        app.getId(), app.getKubeId()))) {
            Statement stmt = connection.createStatement();

            if (!CollectionUtils.isEmpty(execSqlList)) {
                execSqlList.stream().forEach(
                        execsql -> {
                            try {
                                stmt.execute(execsql);
                            } catch (Exception e) {
                                throw new CustomException(600, e.getMessage());
                            }
                        }
                );
            }
            if (!StringUtils.isEmpty(flushSql)) {
                stmt.execute(flushSql);
            }
        } catch (Exception e) {
            log.error("sql : 执行 用户权限sql报错：" + e.getMessage());
            throw new CustomException(600, operationTypeEnum.getAppOperation() + "失败");
        }
    }

    public List<Map<String, Object>> runExecQuerySql(CloudApp app, String execSql) {
        //获取主节点信息
        try (Connection connection = getDbConnection(
                accessManagementService.getFirstWriteServiceAddressByAppIdAndKubeId(
                        app.getId(), app.getKubeId()))) {
            if (null == connection)
                throw new CustomException(600, "获取相关驱动失败");

            QueryRunner queryRunner = new QueryRunner();
            if (!StringUtils.isEmpty(execSql)) {
                List<Map<String, Object>> result = queryRunner.query(connection, execSql, new MapListHandler());
                return result;
            }
        } catch (Exception e) {
            log.error("sql : 执行 用户权限sql报错：" + e.getMessage());
            throw new CustomException(600, "失败");
        }
        return new ArrayList<>();
    }

    public List<Map<String, Object>> getPdbPrivilegesInfo(Integer logicid) {
        //查询应用信息
        CloudApp app = appService.getCloudAppByLogicId(logicid);

        //查询用户<id,用户名，类型，白名单，>
        String selectUsrsSql = "select id , name , storage , host_ip as hostIp from system.users;";

        //获取用户信息
        return runExecQuerySql(app, selectUsrsSql).stream().filter(map -> !getbuildInUsers().contains(map.get("name"))).collect(Collectors.toList());
    }

    /**
     * 查询用户权限信息
     *
     * @param logicid
     * @param username
     * @return
     */
    public List<Map<String, Object>> getPriAuthForuser(Integer logicid, String username) {
        //查询应用信息
        CloudApp app = appService.getCloudAppByLogicId(logicid);
        //权限类型， 数据库 表  权限是否被撤销<0：没有，1：有>  是否可以将当前权限授予给其他用户<1：可以，0：不可以>
        String selectUserSql = "select access_type as accessType , database , table , is_partial_revoke as isPartialRevoke , grant_option as grantOption from system.grants where user_name=\'%s\';";
        return runExecQuerySql(app, String.format(selectUserSql, username));
    }

    private Set<String> getbuildInUsers() {
        Set<String> result = new HashSet<>();
        result.add("default");
        result.add("clickhouse_operator");
        result.add(CLICKHOUSE_USER_NAME);
        return result;
    }

    public void updateDBAuth(List<DataBaseAuthVO> dataBaseAuthVOS) {
        //查询应用信息
        CloudApp app = appService.getCloudAppByLogicId(dataBaseAuthVOS.get(0).getLogicid());
        Boolean isAddAuth = dataBaseAuthVOS.get(0).getIsAddAuth();
        ResourceChangeHis resourceChangeHis = appService.getResourceChangeHis(app, isAddAuth ? ActionEnum.CREATE_APP_AUTH : ActionEnum.DELETE_APP_AUTH, StatusConstant.RUNNING, "", null);
        resourceChangeHisService.add(resourceChangeHis);

        List<String> execSql = buildUserAuthSql(dataBaseAuthVOS, isAddAuth, ClickhouseUtil.getClickhouseClusterName());
        try {
            runExecSql(app, isAddAuth ? ActionEnum.CREATE_APP_AUTH : ActionEnum.DELETE_APP_AUTH, execSql, "");
            updateResourceChangeHis(resourceChangeHis, StatusConstant.SUCCESS, (isAddAuth ? ActionEnum.CREATE_APP_AUTH.getAppOperation() : ActionEnum.DELETE_APP_AUTH.getAppOperation()) + "成功");

        } catch (Exception e) {
            log.error("更新权限失败" + e.getMessage());
            updateResourceChangeHis(resourceChangeHis, StatusConstant.FAIL, (isAddAuth ? ActionEnum.CREATE_APP_AUTH.getAppOperation() : ActionEnum.DELETE_APP_AUTH.getAppOperation()) + "失败:" + e.getMessage());
            throw new CustomException(600, "更新权限失败");
        }
    }

    private List<String> buildUserAuthSql(List<DataBaseAuthVO> dataBaseAuthVOS, Boolean isAddAuth, String clusterName) {
        String resultSql = isAddAuth ? "grant on cluster \'%s\' %s on %s to %s " : "revoke on cluster \'%s\' %s on %s from  %s ";
        return dataBaseAuthVOS.stream().map(dataBaseAuthVO -> {
            String tempSql = dataBaseAuthVO.getIsAdmin() && dataBaseAuthVO.getIsAddAuth() ? resultSql + " with grant option" : resultSql;
            return String.format(tempSql, clusterName, dataBaseAuthVO.getAuthStr(), dataBaseAuthVO.getSchemaTable(), dataBaseAuthVO.getUsername());
        }).collect(Collectors.toList());
    }


    public List<String> getPdbSideBarSchemaList(Integer logicid) {
        //查询应用信息
        CloudApp app = appService.getCloudAppByLogicId(logicid);

        //构建查询sql
        String sql = "SELECT SCHEMA_NAME FROM information_schema.SCHEMATA WHERE schema_name NOT IN ('information_schema','system','INFORMATION_SCHEMA') ORDER BY SCHEMA_NAME ASC;";

        try {
            List<Map<String, Object>> query = runExecQuerySql(app, sql);
            if (!CollectionUtils.isEmpty(query))
                return query.stream().map(map -> {
                    return map.get("SCHEMA_NAME").toString();
                }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询schema失败" + e);
            throw new CustomException(600, "查询schema 失败");
        }
        return new ArrayList<>();
    }

    public List<String> getDataObjectNamesByType(Integer logicid, String schemaName) {
        //查询应用信息
        CloudApp app = appService.getCloudAppByLogicId(logicid);

        //构建查询sql
        String sql = "SHOW FULL TABLES from %s;";

        //设置主要信息
        try {
            List<Map<String, Object>> query = runExecQuerySql(app, String.format(sql, schemaName));
            return query.stream().map(map -> {
                return map.get("name").toString();
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询schema失败" + e);
            throw new CustomException(600, "查询schema 失败");
        }
    }

    public List<Map<String, Object>> getPdbGlobalVariablesInfo(Integer logicid) {
        //查询应用信息
        CloudApp app = appService.getCloudAppByLogicId(logicid);

        //构建查询sql<名称、值、是否改变、描述、最小值、最大值、默认值、是否过时>
        String sql = "SELECT name as Variable_name , value as Value , changed , description , 'min' , 'max' , default , is_obsolete as isObsolete FROM system.server_settings;";

        //设置主要信息
        try {
            List<Map<String, Object>> resultMaps = runExecQuerySql(app, sql);
            resultMaps.parallelStream().forEach(tempMap -> {
                tempMap.put("isupdate", Boolean.TRUE);
            });
            //查询不可修改的变量
            String prohibitContent = sysConfigService.findOne(PARAM_PROHIBIT, getKind().getProduct());
            if (StringUtils.isEmpty(prohibitContent)) {
                return resultMaps;
            }
            Set<String> prohibited = new HashSet<>(Arrays.asList(prohibitContent.split(",")));
            //判断是否可修改
            return resultMaps.parallelStream().map(tempMap -> {
                String variable_name = tempMap.get("Variable_name").toString();
                if (prohibited.contains(variable_name)) ;
                tempMap.put("isupdate", Boolean.FALSE);
                return tempMap;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询schema失败" + e);
            throw new CustomException(600, "查询schema 失败");
        }
    }

    @Getter
    @Setter
    @ToString
    public static class ClickhouseOverrideSpec extends OverrideSpec {
        //分片数量
        private Integer shardsSize;
        //副本数量
        private Integer replicasSize;
    }

    @Data
    public static class ClickhouseClusterVO extends CloudAppVO {
        //分片数量
        private Integer shardsSize;
        //副本数量
        private Integer replicasSize;
        private Map<Integer, List<String>> clusterIpMap;
    }

    @Override
    public CloudAppVO overrideSpec(CloudAppLogic logicApp, Integer kubeId, InstallAppVo<? extends OverrideSpec> vo) {
        CloudAppVO appVO = super.overrideSpec(logicApp, kubeId, vo);
        ClickhouseClusterVO clusterVO = new ClickhouseClusterVO();
        BeanUtils.copyProperties(appVO, clusterVO);
        if (vo.getOverrideSpecs().get(kubeId) instanceof ClickhouseOverrideSpec) {
            ClickhouseOverrideSpec overrideSpec = (ClickhouseOverrideSpec) vo.getOverrideSpecs().get(kubeId);
            clusterVO.setShardsSize(overrideSpec.getShardsSize());
            clusterVO.setReplicasSize(overrideSpec.getReplicasSize());
        }
        return clusterVO;
    }

    @Override
    public Class<? extends OpsPostProcessor> getProcessorClass(ActionEnum action) {
        switch (action) {
            case CREATE:
                return ClickhouseInstallWatch.class;
            case UPDATE:
                return ClickhouseUpdateWatch.class;
            case UPDATE_SERVICE:
            case DELETE_SERVICE:
            case CREATE_SERVICE:
                return ClickhouseServiceWatch.class;
            case MODIFY_PARAM:
                return ClickhouseWatch.class;
            default:
                return super.getProcessorClass(action);
        }
    }

    @Override
    public PageInfo<ClickhouseClusterVO> searchPage(PageDTO page) {
        PageInfo<? extends CloudAppVO> pageInfo = super.searchPage(page);
        return PageUtil.page2PageInfo(pageInfo.getList(), ClickhouseClusterVO.class, vo -> {
            ClickhouseClusterVO clusterVO = new ClickhouseClusterVO();
            BeanUtils.copyProperties(vo, clusterVO);

            String crYaml = StringUtils.isEmpty(clusterVO.getCr()) ? clusterVO.getCrRun() : clusterVO.getCr();
            if (!StringUtils.isEmpty(crYaml)) {
                ClickHouseInstallation clickHOuseCr = YamlEngine.unmarshal(crYaml, ClickHouseInstallation.class);
                Layout layout = clickHOuseCr.getSpec().getConfiguration().getClusters().get(0).getLayout();
                clusterVO.setShardsSize(null == layout ? 1 : layout.getShardsCount().intValue());
                clusterVO.setReplicasSize(null == layout ? 1 : layout.getReplicasCount().intValue());

            }
            CloudApp zookeeper = appService.getAppByCrName(referringAppName(clusterVO), clusterVO.getNamespace(), AppKind.ClickHouse_Zookeeper);

            if (zookeeper != null) {
                clusterVO.setZookeeperName(zookeeper.getName());
                clusterVO.setZookeeperId(zookeeper.getId());
            }

            //拓扑图所需
            //查询出所有的pod信息，根据pod-name进行pod的构建
            KubeClient kubeClient = kubeClientService.get(clusterVO.getKubeId());
            ClickHouseInstallation cr = kubeClient.listCustomResource(ClickHouseInstallation.class, clusterVO.getCrName(), clusterVO.getNamespace());
            Map<String, List<String>> clusterIpMap = new HashMap<>();
            if (null != cr && null != cr.getStatus()) {
                List<String> podIps = cr.getStatus().getPodIps();
                List<String> pods = cr.getStatus().getPods();
                Map<String, String> podNameMap = getPodNameByIpMap(podIps, pods);
                clusterIpMap = getShardIpMap(podNameMap);
            }


            HashMap<Integer, List<String>> shardMap = new HashMap<>();
            AtomicInteger shard = new AtomicInteger(1);
            clusterIpMap.entrySet().stream().forEach(entry -> shardMap.put(shard.getAndIncrement(), entry.getValue()));

            clusterVO.setClusterIpMap(shardMap);

            return clusterVO;
        });
    }

    /**
     * 构建shard的对应pod列表
     *
     * @param podNameMap
     * @return
     */
    private Map<String, List<String>> getShardIpMap(Map<String, String> podNameMap) {
        Map<String, List<String>> clusterIpMap = podNameMap.entrySet().stream()
                .collect(Collectors.groupingBy(
                        entry -> getClickhouseShardName(entry.getKey()),
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())
                ));
        return clusterIpMap;
    }

    /**
     * 构建 podname-podid的映射
     *
     * @param podIps
     * @param pods
     * @return
     */
    private Map<String, String> getPodNameByIpMap(List<String> podIps, List<String> pods) {
        if (CollectionUtils.isEmpty(podIps) || CollectionUtils.isEmpty(pods)) {
            return new HashMap<>();
        }
        Map<String, String> podNameMap = IntStream.range(0, podIps.size())
                .boxed()
                .collect(Collectors.toMap(
                        pods::get,
                        podIps::get,
                        (oldValue, newValue) -> oldValue,
                        HashMap::new
                ));
        return podNameMap;
    }

    private String getClickhouseShardName(String podName) {
        String[] split = podName.split("-");
        return Arrays.stream(split).limit(split.length - 2).collect(Collectors.joining("-"));
    }

    @Override
    public void restore(BackupHis backupHis, Integer appId, String restoreTime, String ftpFilename, String backupType) {
        // 获取备份存储的资源列表
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        if (null == cloudBackupStorageVO)
            throw new CustomException(600, "未查询到相关的备份存储配置信息！");

        //1、保存基本信息
        RestoreHis restoreHis = new RestoreHis();
        // 0. 创建还原的对象
        restoreHis.setStatus(StatusConstant.RUNNING);
        Date startDate = new Date();
        //时间转换
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");

        Timestamp startTime = new Timestamp(System.currentTimeMillis());
        restoreHis.setStartTime(startTime);
        // 1. 插入基础恢复历史和操作记录
        CloudApp goalApp = appService.get(appId);
        CloudApp backupApp = appService.get(backupHis.getAppId());

        // 判断备份应用和恢复应用
//        if (!goalApp.getCrName().equals(backupApp.getCrName()))
//            throw new CustomException(600, "仅支持在备份集群基础上做恢复操作或同名集群做恢复操作！");

        restoreHis.setAppId(appId);
        restoreHis.setAppName(goalApp.getName());
        restoreHis.setAppType(goalApp.getKind());
        KubeClient kubeClient = kubeClientService.get(goalApp.getKubeId());
        String namespace = goalApp.getNamespace();
        String goalCrName = goalApp.getCrName();
        ClickHouseInstallation cr = kubeClient.listCustomResource(ClickHouseInstallation.class, goalCrName, namespace);
        List<String> goalAppList = cr.getStatus().getPods();
        //获取pod名称
        restoreHis.setPodName(goalAppList.stream().collect(Collectors.joining(",")));

        //应用所属集群
        KubeConfig kubeConfig = kubeConfigService.get(goalApp.getKubeId());
        if (null == kubeConfig) {
            log.error("未获取到集群！");
            backupUtil.restoreReturn(restoreHis, null, "未获取到集群！", StatusConstant.FAIL);
            return;
        }
        restoreHis.setKubeName(kubeConfig.getName());
        restoreHis.setMessage("恢复中...");
        restoreHis.setFileName(backupHis.getFileName());
        restoreHis.setRestoreDir(CLICKHOUSE_DATA_BACKUP_PATH);
        restoreHis.setFileDeleted(false);
        //插入基本信息
        backupService.commitRestoreHis(restoreHis);

        //插入操作记录
        ResourceChangeHis resourceChangeHis = backupUtil.createBasicHis(goalApp, startTime, kubeConfig);
        resourceChangeHis.setCommand("恢复");
        resourceChangeHis.setStatus("2");
        resourceChangeHis.setAction(ActionEnum.RESTORE.getActionType());
        resourceChangeHis.setMsg("恢复中...");
        Integer changeId = backupUtil.insertResourceChangeHis(resourceChangeHis);

        backupUtil.checkRestoreAndRecord(resourceChangeHis, goalApp, backupHis, restoreHis);
        goalApp.setStatus(CloudAppConstant.AppStatus.PENDING);
        appService.update(goalApp);

        //备份文件名称
        String backupFileName = backupHis.getFileName();
        String[] backupFileNameArr = backupFileName.split(",");
        //判断分片数 与 备份的数据库 的分片数是否一致
        if (backupFileNameArr.length != cr.getSpec().getConfiguration().getClusters().get(0).getLayout().getShardsCount().intValue()) {
            log.error("当前进行恢复操作的 clickhouse 集群与要进行 恢复的 clickhouse 集群的分片数不一致！");
            backupUtil.restoreReturn(restoreHis, changeId, "当前进行恢复操作的 clickhouse 集群与要进行 恢复的 clickhouse 集群的分片数不一致！！", StatusConstant.FAIL);
            return;
        }

        //进行路径挂载
        goalAppList.parallelStream().forEach(podName -> {
            try {
                //进行 相关文件移动到 /var/lib/clickhouse/backup，执行命令 clickhouse-backup restore yljtest_back_up_002 >> /var/log/clickhouse-backup.log 2>&1 && echo 0 > temp1.log || echo 1 > temp1.log  进行恢复
                String shardNum = podName.split(goalCrName + "-" + ClickhouseUtil.getClickhouseClusterName() + "-")[1].split("-")[0];
                //获取当前分片对应的备份文件
                List<String> shardFileName = Arrays.stream(backupFileNameArr).filter(fileName -> fileName.split(backupApp.getCrName() + "-shard-")[1].split("-")[0].equals(shardNum)).collect(Collectors.toList());
                String restoreScript = "";
                //判断备份存储类型 进行 手动 路径挂载
                //把备份文件压缩包去除压缩后缀，方便脚本处理
                String targetBackupFileDir = shardFileName.get(0).split("\\.")[0];
                if (CloudAppConstant.StorageType.NAS.equalsIgnoreCase(cloudBackupStorageVO.getStorageType())) {
                    String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
                    restoreScript = "bash /scripts/clickhouse-restore.sh " + targetBackupFileDir + " " + backupUtil.buildRestorePath(backupApp) + " true " + CloudAppConstant.OperatorStorageType.NFS + " " + mountPath;
                } else if (CloudAppConstant.StorageType.S3.equalsIgnoreCase(cloudBackupStorageVO.getStorageType())) {
                    //执行 手动 mount 操作
                    kubeClient.execCmdOneway(namespace, podName, "s3fs-container", "sh", "-c", backupUtil.getS3MountCommand(cloudBackupStorageVO));
                    restoreScript = "bash /scripts/clickhouse-restore.sh " + targetBackupFileDir + " " + backupUtil.buildRestorePath(backupApp) + " false ";
                }
                log.info("[clickhouse恢复]，执行的语句为：sh -c " + restoreScript);
                kubeClient.execCmdOneway(namespace, podName, "clickhouse-backup", "sh", "-c", restoreScript);
            } catch (Exception e) {
                backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "clickhouse恢复失败！错误信息：" + e.getMessage());
                return;
            }
        });
        try {
            //创建定时轮询备份结果
            Map map = new HashMap();
            map.put("restoreStartDateSDF", sdf.format(startDate));
            map.put("restoreHisId", restoreHis.getRestoreHisId());
            map.put("resourceChangeId", changeId);
            map.put("restorePodName", goalAppList.stream().collect(Collectors.joining(",")));
            appService.callScheduler(goalApp, YamlEngine.marshal(cr), map, ActionEnum.RESTORE, ClickhouseBackupAndRestoreWatch.class, resourceChangeHis);
        } catch (Exception e) {
            backupUtil.restoreReturn(restoreHis, changeId, e.getMessage(), StatusConstant.FAIL);
        }
    }

}
