package com.pingcap.v1alpha1.tidbclusterspec;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.ComponentKindEnum;
import cn.newdt.cloud.constant.ImageKindEnum;
import cn.newdt.cloud.service.csi.CSILoader;
import cn.newdt.cloud.service.impl.TidbService;
import cn.newdt.cloud.utils.TidbUtil;
import com.pingcap.v1alpha1.TidbCompentBasic;
import io.fabric8.kubernetes.api.model.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)
@com.fasterxml.jackson.annotation.JsonPropertyOrder({"additionalContainers", "additionalVolumeMounts", "additionalVolumes", "affinity", "annotations", "baseImage", "config", "configUpdateStrategy", "dataSubDir", "dnsConfig", "dnsPolicy", "enableNamedStatusPort", "env", "envFrom", "evictLeaderTimeout", "failover", "hostNetwork", "image", "imagePullPolicy", "imagePullSecrets", "initContainers", "labels", "limits", "logTailer", "maxFailoverCount", "mountClusterClientSecret", "nodeSelector", "podManagementPolicy", "podSecurityContext", "priorityClassName", "privileged", "raftLogVolumeName", "readinessProbe", "recoverFailover", "replicas", "requests", "rocksDBLogVolumeName", "scalePolicy", "schedulerName", "separateRaftLog", "separateRocksDBLog", "serviceAccount", "statefulSetUpdateStrategy", "storageClassName", "storageVolumes", "storeLabels", "suspendAction", "terminationGracePeriodSeconds", "tolerations", "topologySpreadConstraints", "version", "waitLeaderTransferBackTimeout"})
@com.fasterxml.jackson.databind.annotation.JsonDeserialize(using = com.fasterxml.jackson.databind.JsonDeserializer.None.class)
public class Tikv extends TidbCompentBasic implements io.fabric8.kubernetes.api.model.KubernetesResource {

    @com.fasterxml.jackson.annotation.JsonProperty("additionalContainers")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.List<io.fabric8.kubernetes.api.model.Container> additionalContainers;

    public java.util.List<io.fabric8.kubernetes.api.model.Container> getAdditionalContainers() {
        return additionalContainers;
    }

    public void setAdditionalContainers(java.util.List<io.fabric8.kubernetes.api.model.Container> additionalContainers) {
        this.additionalContainers = additionalContainers;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("additionalVolumeMounts")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.List<io.fabric8.kubernetes.api.model.VolumeMount> additionalVolumeMounts;

    public java.util.List<io.fabric8.kubernetes.api.model.VolumeMount> getAdditionalVolumeMounts() {
        return additionalVolumeMounts;
    }

    public void setAdditionalVolumeMounts(java.util.List<io.fabric8.kubernetes.api.model.VolumeMount> additionalVolumeMounts) {
        this.additionalVolumeMounts = additionalVolumeMounts;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("additionalVolumes")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.List<io.fabric8.kubernetes.api.model.Volume> additionalVolumes;

    public java.util.List<io.fabric8.kubernetes.api.model.Volume> getAdditionalVolumes() {
        return additionalVolumes;
    }

    public void setAdditionalVolumes(java.util.List<io.fabric8.kubernetes.api.model.Volume> additionalVolumes) {
        this.additionalVolumes = additionalVolumes;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("affinity")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.pingcap.v1alpha1.tidbclusterspec.tikv.Affinity affinity;

    public com.pingcap.v1alpha1.tidbclusterspec.tikv.Affinity getAffinity() {
        return affinity;
    }

    public void setAffinity(com.pingcap.v1alpha1.tidbclusterspec.tikv.Affinity affinity) {
        this.affinity = affinity;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("annotations")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.Map<java.lang.String, String> annotations;

    public java.util.Map<java.lang.String, String> getAnnotations() {
        return annotations;
    }

    public void setAnnotations(java.util.Map<java.lang.String, String> annotations) {
        this.annotations = annotations;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("baseImage")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String baseImage = "pingcap/tikv";

    public String getBaseImage() {
        return baseImage;
    }

    public void setBaseImage(String baseImage) {
        this.baseImage = baseImage;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("config")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String config;

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("configUpdateStrategy")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String configUpdateStrategy;

    public String getConfigUpdateStrategy() {
        return configUpdateStrategy;
    }

    public void setConfigUpdateStrategy(String configUpdateStrategy) {
        this.configUpdateStrategy = configUpdateStrategy;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("dataSubDir")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String dataSubDir;

    public String getDataSubDir() {
        return dataSubDir;
    }

    public void setDataSubDir(String dataSubDir) {
        this.dataSubDir = dataSubDir;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("dnsConfig")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.pingcap.v1alpha1.tidbclusterspec.tikv.DnsConfig dnsConfig;

    public com.pingcap.v1alpha1.tidbclusterspec.tikv.DnsConfig getDnsConfig() {
        return dnsConfig;
    }

    public void setDnsConfig(com.pingcap.v1alpha1.tidbclusterspec.tikv.DnsConfig dnsConfig) {
        this.dnsConfig = dnsConfig;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("dnsPolicy")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String dnsPolicy;

    public String getDnsPolicy() {
        return dnsPolicy;
    }

    public void setDnsPolicy(String dnsPolicy) {
        this.dnsPolicy = dnsPolicy;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("enableNamedStatusPort")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Boolean enableNamedStatusPort;

    public Boolean getEnableNamedStatusPort() {
        return enableNamedStatusPort;
    }

    public void setEnableNamedStatusPort(Boolean enableNamedStatusPort) {
        this.enableNamedStatusPort = enableNamedStatusPort;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("env")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.Env> env;

    public java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.Env> getEnv() {
        return env;
    }

    public void setEnv(java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.Env> env) {
        this.env = env;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("envFrom")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.EnvFrom> envFrom;

    public java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.EnvFrom> getEnvFrom() {
        return envFrom;
    }

    public void setEnvFrom(java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.EnvFrom> envFrom) {
        this.envFrom = envFrom;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("evictLeaderTimeout")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String evictLeaderTimeout;

    public String getEvictLeaderTimeout() {
        return evictLeaderTimeout;
    }

    public void setEvictLeaderTimeout(String evictLeaderTimeout) {
        this.evictLeaderTimeout = evictLeaderTimeout;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("failover")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.pingcap.v1alpha1.tidbclusterspec.tikv.Failover failover;

    public com.pingcap.v1alpha1.tidbclusterspec.tikv.Failover getFailover() {
        return failover;
    }

    public void setFailover(com.pingcap.v1alpha1.tidbclusterspec.tikv.Failover failover) {
        this.failover = failover;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("hostNetwork")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Boolean hostNetwork;

    public Boolean getHostNetwork() {
        return hostNetwork;
    }

    public void setHostNetwork(Boolean hostNetwork) {
        this.hostNetwork = hostNetwork;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("image")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String image;

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("imagePullPolicy")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String imagePullPolicy;

    public String getImagePullPolicy() {
        return imagePullPolicy;
    }

    public void setImagePullPolicy(String imagePullPolicy) {
        this.imagePullPolicy = imagePullPolicy;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("imagePullSecrets")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.ImagePullSecrets> imagePullSecrets;

    public java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.ImagePullSecrets> getImagePullSecrets() {
        return imagePullSecrets;
    }

    public void setImagePullSecrets(java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.ImagePullSecrets> imagePullSecrets) {
        this.imagePullSecrets = imagePullSecrets;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("initContainers")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.InitContainers> initContainers;

    public java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.InitContainers> getInitContainers() {
        return initContainers;
    }

    public void setInitContainers(java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.InitContainers> initContainers) {
        this.initContainers = initContainers;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("labels")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.Map<java.lang.String, String> labels;

    public java.util.Map<java.lang.String, String> getLabels() {
        return labels;
    }

    public void setLabels(java.util.Map<java.lang.String, String> labels) {
        this.labels = labels;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("limits")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.Map<java.lang.String, io.fabric8.kubernetes.api.model.IntOrString> limits;

    public java.util.Map<java.lang.String, io.fabric8.kubernetes.api.model.IntOrString> getLimits() {
        return limits;
    }

    public void setLimits(java.util.Map<java.lang.String, io.fabric8.kubernetes.api.model.IntOrString> limits) {
        this.limits = limits;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("logTailer")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.pingcap.v1alpha1.tidbclusterspec.tikv.LogTailer logTailer;

    public com.pingcap.v1alpha1.tidbclusterspec.tikv.LogTailer getLogTailer() {
        return logTailer;
    }

    public void setLogTailer(com.pingcap.v1alpha1.tidbclusterspec.tikv.LogTailer logTailer) {
        this.logTailer = logTailer;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("maxFailoverCount")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Integer maxFailoverCount;

    public Integer getMaxFailoverCount() {
        return maxFailoverCount;
    }

    public void setMaxFailoverCount(Integer maxFailoverCount) {
        this.maxFailoverCount = maxFailoverCount;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("mountClusterClientSecret")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Boolean mountClusterClientSecret;

    public Boolean getMountClusterClientSecret() {
        return mountClusterClientSecret;
    }

    public void setMountClusterClientSecret(Boolean mountClusterClientSecret) {
        this.mountClusterClientSecret = mountClusterClientSecret;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("nodeSelector")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.Map<java.lang.String, String> nodeSelector;

    public java.util.Map<java.lang.String, String> getNodeSelector() {
        return nodeSelector;
    }

    public void setNodeSelector(java.util.Map<java.lang.String, String> nodeSelector) {
        this.nodeSelector = nodeSelector;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("podManagementPolicy")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String podManagementPolicy;

    public String getPodManagementPolicy() {
        return podManagementPolicy;
    }

    public void setPodManagementPolicy(String podManagementPolicy) {
        this.podManagementPolicy = podManagementPolicy;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("podSecurityContext")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.pingcap.v1alpha1.tidbclusterspec.tikv.PodSecurityContext podSecurityContext;

    public com.pingcap.v1alpha1.tidbclusterspec.tikv.PodSecurityContext getPodSecurityContext() {
        return podSecurityContext;
    }

    public void setPodSecurityContext(com.pingcap.v1alpha1.tidbclusterspec.tikv.PodSecurityContext podSecurityContext) {
        this.podSecurityContext = podSecurityContext;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("priorityClassName")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String priorityClassName;

    public String getPriorityClassName() {
        return priorityClassName;
    }

    public void setPriorityClassName(String priorityClassName) {
        this.priorityClassName = priorityClassName;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("privileged")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Boolean privileged;

    public Boolean getPrivileged() {
        return privileged;
    }

    public void setPrivileged(Boolean privileged) {
        this.privileged = privileged;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("raftLogVolumeName")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String raftLogVolumeName;

    public String getRaftLogVolumeName() {
        return raftLogVolumeName;
    }

    public void setRaftLogVolumeName(String raftLogVolumeName) {
        this.raftLogVolumeName = raftLogVolumeName;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("readinessProbe")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.pingcap.v1alpha1.tidbclusterspec.tikv.ReadinessProbe readinessProbe;

    public com.pingcap.v1alpha1.tidbclusterspec.tikv.ReadinessProbe getReadinessProbe() {
        return readinessProbe;
    }

    public void setReadinessProbe(com.pingcap.v1alpha1.tidbclusterspec.tikv.ReadinessProbe readinessProbe) {
        this.readinessProbe = readinessProbe;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("recoverFailover")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Boolean recoverFailover;

    public Boolean getRecoverFailover() {
        return recoverFailover;
    }

    public void setRecoverFailover(Boolean recoverFailover) {
        this.recoverFailover = recoverFailover;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("replicas")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Integer replicas;

    public Integer getReplicas() {
        return replicas;
    }

    public void setReplicas(Integer replicas) {
        this.replicas = replicas;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("requests")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.Map<java.lang.String, io.fabric8.kubernetes.api.model.IntOrString> requests;

    public java.util.Map<java.lang.String, io.fabric8.kubernetes.api.model.IntOrString> getRequests() {
        return requests;
    }

    public void setRequests(java.util.Map<java.lang.String, io.fabric8.kubernetes.api.model.IntOrString> requests) {
        this.requests = requests;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("rocksDBLogVolumeName")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String rocksDBLogVolumeName;

    public String getRocksDBLogVolumeName() {
        return rocksDBLogVolumeName;
    }

    public void setRocksDBLogVolumeName(String rocksDBLogVolumeName) {
        this.rocksDBLogVolumeName = rocksDBLogVolumeName;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("scalePolicy")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.pingcap.v1alpha1.tidbclusterspec.tikv.ScalePolicy scalePolicy;

    public com.pingcap.v1alpha1.tidbclusterspec.tikv.ScalePolicy getScalePolicy() {
        return scalePolicy;
    }

    public void setScalePolicy(com.pingcap.v1alpha1.tidbclusterspec.tikv.ScalePolicy scalePolicy) {
        this.scalePolicy = scalePolicy;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("schedulerName")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String schedulerName;

    public String getSchedulerName() {
        return schedulerName;
    }

    public void setSchedulerName(String schedulerName) {
        this.schedulerName = schedulerName;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("separateRaftLog")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Boolean separateRaftLog;

    public Boolean getSeparateRaftLog() {
        return separateRaftLog;
    }

    public void setSeparateRaftLog(Boolean separateRaftLog) {
        this.separateRaftLog = separateRaftLog;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("separateRocksDBLog")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Boolean separateRocksDBLog;

    public Boolean getSeparateRocksDBLog() {
        return separateRocksDBLog;
    }

    public void setSeparateRocksDBLog(Boolean separateRocksDBLog) {
        this.separateRocksDBLog = separateRocksDBLog;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("serviceAccount")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String serviceAccount;

    public String getServiceAccount() {
        return serviceAccount;
    }

    public void setServiceAccount(String serviceAccount) {
        this.serviceAccount = serviceAccount;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("statefulSetUpdateStrategy")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String statefulSetUpdateStrategy;

    public String getStatefulSetUpdateStrategy() {
        return statefulSetUpdateStrategy;
    }

    public void setStatefulSetUpdateStrategy(String statefulSetUpdateStrategy) {
        this.statefulSetUpdateStrategy = statefulSetUpdateStrategy;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("storageClassName")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String storageClassName;

    public String getStorageClassName() {
        return storageClassName;
    }

    public void setStorageClassName(String storageClassName) {
        this.storageClassName = storageClassName;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("storageVolumes")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.StorageVolumes> storageVolumes;

    public java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.StorageVolumes> getStorageVolumes() {
        return storageVolumes;
    }

    public void setStorageVolumes(java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.StorageVolumes> storageVolumes) {
        this.storageVolumes = storageVolumes;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("storeLabels")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.List<String> storeLabels;

    public java.util.List<String> getStoreLabels() {
        return storeLabels;
    }

    public void setStoreLabels(java.util.List<String> storeLabels) {
        this.storeLabels = storeLabels;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("suspendAction")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.pingcap.v1alpha1.tidbclusterspec.tikv.SuspendAction suspendAction;

    public com.pingcap.v1alpha1.tidbclusterspec.tikv.SuspendAction getSuspendAction() {
        return suspendAction;
    }

    public void setSuspendAction(com.pingcap.v1alpha1.tidbclusterspec.tikv.SuspendAction suspendAction) {
        this.suspendAction = suspendAction;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("terminationGracePeriodSeconds")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Long terminationGracePeriodSeconds;

    public Long getTerminationGracePeriodSeconds() {
        return terminationGracePeriodSeconds;
    }

    public void setTerminationGracePeriodSeconds(Long terminationGracePeriodSeconds) {
        this.terminationGracePeriodSeconds = terminationGracePeriodSeconds;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("tolerations")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.Tolerations> tolerations;

    public java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.Tolerations> getTolerations() {
        return tolerations;
    }

    public void setTolerations(java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.Tolerations> tolerations) {
        this.tolerations = tolerations;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("topologySpreadConstraints")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.TopologySpreadConstraints> topologySpreadConstraints;

    public java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.TopologySpreadConstraints> getTopologySpreadConstraints() {
        return topologySpreadConstraints;
    }

    public void setTopologySpreadConstraints(java.util.List<com.pingcap.v1alpha1.tidbclusterspec.tikv.TopologySpreadConstraints> topologySpreadConstraints) {
        this.topologySpreadConstraints = topologySpreadConstraints;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("version")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String version;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("waitLeaderTransferBackTimeout")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String waitLeaderTransferBackTimeout;

    public String getWaitLeaderTransferBackTimeout() {
        return waitLeaderTransferBackTimeout;
    }

    public void setWaitLeaderTransferBackTimeout(String waitLeaderTransferBackTimeout) {
        this.waitLeaderTransferBackTimeout = waitLeaderTransferBackTimeout;
    }

    @Override
    public void setCompentBaseImage(String baseImage) {
        this.setBaseImage(baseImage);
    }

    @Override
    public void setCompentConfig(String config) {
        this.setConfig(config);
    }

    @Override
    public void setCompentLimits(Map<String, IntOrString> limits) {
        this.setLimits(limits);
    }

    @Override
    public void setCompentRequests(Map<String, IntOrString> requests) {
        this.setRequests(requests);
    }

    @Override
    public void setCompentReplicas(Integer compentReplicas) {
        this.setReplicas(compentReplicas);
    }

    @Override
    public void setCompentMaxFailoverCount(Integer compentMaxFailoverCount) {
        this.setMaxFailoverCount(compentMaxFailoverCount);
    }

    @Override
    public void setAdditionalCompentContainers(List<Container> additionalCompentContainers, TidbCompentBasic.TidbBasicVo tidbBasicVo) {
        //tikv 需要额外添加两个 container <br 与 nas 挂载>
        Map<ImageKindEnum, String> imageConfig = tidbBasicVo.getImageConfig();
        // 添加 备份 容器
        additionalCompentContainers.add(
                new ContainerBuilder().withName(TidbUtil.TIDB_BACKUP_CONTAINER_NAME)
                        .withImage(imageConfig.get(ImageKindEnum.Tidb_BR))
                        .withImagePullPolicy("IfNotPresent")
                        .withCommand("sh", "-c").withArgs("tail -f /dev/null")
                        .withSecurityContext(new SecurityContextBuilder().withPrivileged(true).withRunAsUser(0L).build())
                        .withEnv(
                                new EnvVarBuilder().withName("APP_TYPE").withValue(AppKind.TIDB.getKind()).build(),
                                new EnvVarBuilder().withName("ARCH_TYPE").withValue(AppKind.TIDB.getArch()).build(),
                                new EnvVarBuilder().withName("APP_NAMESPACE").withValueFrom(new EnvVarSourceBuilder().withFieldRef(new ObjectFieldSelectorBuilder().withApiVersion("v1").withFieldPath("metadata.namespace").build()).build()).build(),
                                new EnvVarBuilder().withName("APP_NAME").withValue(tidbBasicVo.getCrname()).build()
                        )
                        .withVolumeMounts(
                                new VolumeMountBuilder().withName(TidbUtil.TIDB_BACKUP_VOLUME_NAME).withMountPath(TidbUtil.TIDB_BACKUP_PATH).build(),
                                new VolumeMountBuilder().withName(TidbUtil.TIDB_MNT_VOLUME_NAME).withMountPath(TidbUtil.TIDB_MNT_PATH).withMountPropagation("Bidirectional").build(),
                                new VolumeMountBuilder().withName(TidbUtil.TIDB_SCRIPT_VOLUME_NAME).withMountPath("/scripts").withReadOnly(Boolean.TRUE).build()
                        ).build()
        );

        //添加 nas挂载容器
        additionalCompentContainers.add(
                new ContainerBuilder().withName(TidbUtil.TIDB_MNT_CONTAINER_NAME)
                        .withImage(imageConfig.get(ImageKindEnum.S3FS))
                        .withImagePullPolicy("IfNotPresent")
                        .withCommand("sh", "-c").withArgs("tail -f /dev/null")
                        .withSecurityContext(new SecurityContextBuilder().withPrivileged(true).withRunAsUser(0L).build())
                        .withVolumeMounts(
                                // new VolumeMountBuilder().withName(TidbUtil.TIDB_BACKUP_VOLUME_NAME).withMountPath(TidbUtil.TIDB_BACKUP_PATH).build(),
                                new VolumeMountBuilder().withName(TidbUtil.TIDB_MNT_VOLUME_NAME).withMountPath(TidbUtil.TIDB_MNT_PATH).withMountPropagation("Bidirectional").build(),
                                new VolumeMountBuilder().withName(TidbUtil.TIDB_SCRIPT_VOLUME_NAME).withMountPath("/scripts").withReadOnly(Boolean.TRUE).build()
                        )
                        .withLifecycle(new LifecycleBuilder().withPreStop(new HandlerBuilder().withExec(new ExecActionBuilder().withCommand("sh", "-c", TidbUtil.UMOUNT_SCRIPT).build()).build()).build())
                        .build()
        );

        this.setAdditionalContainers(additionalCompentContainers);
    }

    @Override
    public void setAdditionalCompentVolumeMounts(List<VolumeMount> additionalCompentVolumeMounts) {
        this.setAdditionalVolumeMounts(additionalCompentVolumeMounts);
    }

    @Override
    public void setAdditionalCompentVolumes(List<Volume> additionalCompentVolumes) {
        // 添加备份恢复相关 卷组
        additionalCompentVolumes.add(new VolumeBuilder().withName(TidbUtil.TIDB_MNT_VOLUME_NAME).withEmptyDir(new EmptyDirVolumeSource()).build());
        additionalCompentVolumes.add(new VolumeBuilder().withName(TidbUtil.TIDB_SCRIPT_VOLUME_NAME).withConfigMap(new ConfigMapVolumeSourceBuilder().withName(TidbUtil.TIDB_SCRIPTS_CONFIGMAP_NAME).withDefaultMode(416).build()).build());
        this.setAdditionalVolumes(additionalCompentVolumes);
    }

    @Override
    public void setCompentStorageClassName(String storageClassName) {
        this.setStorageClassName(storageClassName);
    }

    @Override
    public void setCompentLabels(Map<String, String> labels) {
        this.setLabels(labels);
    }

    @Override
    public TidbBasicVo buildComponetParam(TidbService.TidbClusterVO vo) {
        Map<ImageKindEnum, String> imageConfig = vo.getImageConfig();

        return TidbCompentBasic.TidbBasicVo.builder()
                .compentBaseImage(imageConfig.get(ImageKindEnum.Tidb_Tikv))
                // .compentConfig(buildComponentConfigParam(vo, vo.getPdParamTemplateId(), vo.getPdTemplateTmpParam(), ComponentKindEnum.TIKV))
                .compentLimits(buildLimitsAndRequests(vo, Boolean.TRUE))
                .compentRequests(buildLimitsAndRequests(vo, Boolean.FALSE))
                .storageClassName(CSILoader.getCsi(vo.getTikvCsiType()).getStorageClassName())
                .compentReplicas(vo.getTikvReplicas())
                .additionalCompentContainers(buildFilebeatContainers(imageConfig.get(ImageKindEnum.Filebeat), vo.getCrName(), ComponentKindEnum.TIKV))
                .additionalCompentVolumeMounts(buildFilebeatVolumeMounts())
                .additionalCompentVolumes(buildFilebeatVolumes())
                .imageConfig(imageConfig)
                .crname(vo.getCrName())
                .build();
    }

    @Override
    public Map<String, IntOrString> buildLimitsAndRequests(TidbService.TidbClusterVO tidbClusterVO, Boolean isLimits) {
        Map<String, IntOrString> limitsAndRequests = new HashMap<>();
        limitsAndRequests.put(CloudAppConstant.ResourceName.CPU, new IntOrString(tidbClusterVO.getTikvCpu()));
        limitsAndRequests.put(CloudAppConstant.ResourceName.MEMORY, new IntOrString(tidbClusterVO.getTikvMemory()));
        if (!isLimits)
            limitsAndRequests.put(CloudAppConstant.ResourceName.STORAGE, new IntOrString(tidbClusterVO.getTikvDisk()));
        return limitsAndRequests;
    }

}

