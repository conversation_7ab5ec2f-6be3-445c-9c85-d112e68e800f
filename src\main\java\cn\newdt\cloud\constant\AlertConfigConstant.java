package cn.newdt.cloud.constant;

public interface AlertConfigConstant {
    public static final String RESOURCE_TYPE = "resourceType";
    public static final String PRODUCT = "product";
    public static final String RESOURCE_NAME = "resourceName";
    public static final String RESOURCE_ID = "resourceId";
    String RESOURCE_TENANT = "resourceTenantId";
    String RESOURCE_OWNER = "resourceOwnerId";
    public static final String SEVERITY_LABEL = "severity";

    // sys config name ->
    public static final String EMAIL_SECRET_NAME = "amcfg_receiver_email_secret_name";
    public static final String EWECHAT_SECRET_NAME = "amcfg_receiver_ewechat_secret_name";
    public static final String NAMESPACE_NAME = "am_namespace_name";
    public static final String PM_NAMESPACE_NAME = "pm_namespace_name";
    public static final String AMCFG_MATCH_LABELS = "amcfg_match_labels";
    public static final String AMCFG_NAMESPACE_MATCH_LABELS = "amcfg_namespace_match_labels";
    public static final String PROMETHEUSRULE_MATCH_LABELS = "prometheusrule_match_labels";
    public static final String GLOBAL_ALERTMANAGER_YAML = "global_alertmanager_yaml";
    public static final String NOTIFY_TEMPLATE_PREFIX = "notify_template.";
    String PROMETHEUS_EVALUATE_INTERVAL_SECONDS = "prometheus_evaluate_interval_seconds";
    String FEDERATE_KUBE_ID = "federate_kube_id";

    /** 告警历史索引 ilm policy */
    String NDT_ALERTLOG_ILM_POLICY = "ndt_alertlog_ilm_policy";

    /** 监控告警数据清理配置项 */
    String PROMETHEUS_RETENTION = "prometheus_retention";
    String PROMETHEUS_RETENTION_SIZE = "prometheus_retentionSize";
    String ALERTMANAGER_RETENTION = "alertmanager_retention";
    String ALERTLOG_RETENTION = "alertlog_retention";
    String ALERTLOG_RETENTION_THRESHOLD = "alertlog_retention_threshold";

    interface SEVERITY_ENUM {
        String INFO = "info";
        String WARN = "warn";
        String CRITICAL = "critical ";
    }
}
