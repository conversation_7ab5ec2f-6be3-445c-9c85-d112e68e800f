package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.common.OpLogContext;
import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.domain.cr.InnoDBCluster;
import cn.newdt.cloud.dto.Label;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.dto.ResourceDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.CloudImageConfigService;
import cn.newdt.cloud.service.DefaultAppKindService;
import cn.newdt.cloud.service.ResourceHelper;
import cn.newdt.cloud.service.ServiceManageOperation;
import cn.newdt.cloud.service.csi.CSIUtil;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.service.sched.impl.*;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.AppInstanceVO;
import cn.newdt.cloud.vo.CloudAppVO;
import cn.newdt.cloud.vo.OverrideSpec;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.bean.MetaVO;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.SymmetricEncryptionUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.api.model.apps.StatefulSet;
import io.fabric8.kubernetes.client.CustomResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.Connection;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static cn.newdt.cloud.utils.MGRUtil.Constants.*;
import static java.util.stream.Collectors.toMap;

/**
 * @Author: houjun
 * @Date: 2022/6/28 - 21:08
 * @Description:
 */
@Slf4j
@Service
public class InnoDBClusterService extends DefaultAppKindService<InnoDBCluster> implements ServiceManageOperation {
    private static final String SECRET_USERNAME_KEY = "rootUser";
    private static final String SECRET_PASSWORD_KEY = "rootPassword";
    private static final String SECRET_BACKUP_USERNAME_KEY = "clusterAdminUsername";
    private static final String SECRET_BACKUP_PASSWORD_KEY = "clusterAdminPassword";
    private static final String DEFAULT_USER = "root";

    private static final String DEFAULT_PASSWORD = "k8sadmin";
    private static final String NDT_MGR_FILEBEAT_CM = "mgr-filebeat-cm";
    public static final String DATADIR = "datadir";
    @Autowired
    private CloudImageConfigService cloudImageConfigService;
    @Override
    public AppKind getKind() {
        return AppKind.MYSQL_MGR;
    }

    /**
     * 支持获取node节点名
     *
     * @return
     */
    @Override
    public boolean nodePolicy() {
        return false;
    }

    @Override
    public InnoDBCluster doInstall(CloudAppVO vo, List<String> ips) {
        String crName = vo.getCrName();
        String namespace = vo.getNamespace();
        String version = vo.getVersion();
        String cpu = vo.getCpu();
        String memory = vo.getMemory();
        Integer members = vo.getMembers();
        if (StringUtils.isEmpty(vo.getUsername())) vo.setUsername(DEFAULT_USER);
        if (StringUtils.isEmpty(vo.getPassword())) vo.setPassword(DEFAULT_PASSWORD);
        String username = vo.getUsername();
        Map<ImageKindEnum, String> images = vo.getImageConfig();
        String storageClassName = vo.getStorageClassName();
        String disk = vo.getDisk();
        Integer kubeSchedulerId = vo.getKubeSchedulerId();
        KubeScheduler kubeScheduler = kubeSchedulerService.queryById(kubeSchedulerId);
        Map<String, String> config = setupDbParamConfig(vo);
//        Optional<String> filebeatYaml = Optional.ofNullable(sysConfigService.findOne(CloudAppConstant.SysCfgCategory.OPERATOR_CONFIG, "mysql_mgr.config"));
        String registryUrl = cloudImageConfigService.getImageManageConfig().getRegistryUrl() + "mysql";
        KubeClient kubeClient = clientService.get(vo.getKubeId());
//        String backupPvcName = "backupdir" + "-" + crName; // todo should be shared storage
        String mgrScripts = "mgr_scripts";
        String mgrScriptCMName = "mgr-scripts";
        Optional<String> scriptsYaml = Optional.ofNullable(sysConfigService.findOne(CloudAppConstant.SysCfgCategory.CONFIGMAP_TEMPLATE, mgrScripts));
        // =======================================================================================
//        filebeatYaml.ifPresent(s -> kubeClient.applyYaml(YamlUtil.evaluateTemplate(s,
//                new HashMap<String, String>() {
//                    {
//                        put("NAME", NDT_MGR_FILEBEAT_CM);
//                        put("NAMESPACE", namespace);
//                        put("es_host", esUtil.getEsIp() + ":" + esUtil.getEsPort());
//                        put("es_username", esUtil.getEsUsername());
//                        put("es_pwd", esUtil.getEsPassword());
//                        put("es_protocol", esUtil.getProtocol());
//                        put("APP_TYPE", getKind().getKind());
//                    }
//                })));
        // config section
        String myCnf = MGRUtil.toMyCnf(config);

        ResourceRequirements request = ResourceHelper.getInstance().resourceRequirements(new ResourceDTO() {{
            setCpu(cpu);
            setMemory(memory);
        }});
        // 创建mgr 应用
        String replace = YamlUtil.evaluateTemplate(YAML_MGR, new HashMap<String, String>() {
            {
                put("crname", crName);
                put("namesapce", namespace);
                put("version", version);
                put("imageRepository", registryUrl);
                put("num", String.valueOf(members));
                put("cpuLimit", cpu);
                put("memoryLimit", memory);
                put("cpuRequest", request.getRequests().get("cpu").toString());
                put("memoryRequest", request.getRequests().get("memory").toString());
                put("dataDirSize", disk);
                put("storageClassName", storageClassName);
                put("mysqlImage", images.get(ImageKindEnum.MainImage));
                put("filebeatImage", images.get(ImageKindEnum.Filebeat));
                put("rootSecret", getSecretName(crName, username)); // also used as backup user
                put("myCnf", myCnf.replace("\n", StringUtils.rightPad("\n", 5)));
                put("backupSecret", crName + "-privsecrets");
                put("userKey", SECRET_USERNAME_KEY);
                put("passwordKey", SECRET_PASSWORD_KEY);
                put("backupUserKey", SECRET_BACKUP_USERNAME_KEY);
                put("backupPasswordKey", SECRET_BACKUP_PASSWORD_KEY);
                put("xbkImage", images.get(ImageKindEnum.Percona_XtraBackup));
                put("exporterImage", images.get(ImageKindEnum.Exporter));
                put("DB_PORT", getKind().getDbPort() + "");
//                put("backupPvcName", backupPvcName);
                put("scriptCMName", mgrScriptCMName);
                put("resourceVersion", vo.getResourceVersion());
            }
        });
        InnoDBCluster cr = YamlEngine.unmarshal(replace, InnoDBCluster.class);

        // affinity
        if (kubeScheduler != null) {
            Affinity k8sAffinity = kubeScheduler.toK8sAffinity(vo);
            List<Toleration> k8sTolerance = kubeScheduler.toK8sTolerance();
            cr.getSpec().getPodSpec().setAffinity(k8sAffinity);
            cr.getSpec().getPodSpec().setTolerations(k8sTolerance);
        }
        // create data hostpath pvc
        if (CSIUtil.isHostpath(vo.getCsiType())) {
            MGRUtil.createHostPath(vo, kubeClient);
        }
        Map<String, String> labelMap = Label.toMap(getKind().labels(crName));
        labelMap.put(CloudAppConstant.CustomLabels.RESOURCE_VERSION, vo.getResourceVersion());
//        kubeClient.createPvc(
//                KubeClientUtil.provideSharedCsiPVC(backupPvcName, namespace, backupDisk, labelMap, backupStorageclassname),
//                namespace);
        // create script cm
        scriptsYaml.ifPresent(yaml -> {
            kubeClient.applyYaml(YamlUtil.evaluateTemplate(yaml, ImmutableMap.of("NAME", mgrScriptCMName)), namespace);
        });
        return cr;
    }

    @Override
    protected void createCrControlResource(InnoDBCluster cr, CloudAppVO vo) {
        KubeClient client = clientService.get(vo.getKubeId());
        if (StringUtils.isNotEmpty(vo.getPassword())) {
            Map<String, String> secretData = new HashMap<>();
            secretData.put(SECRET_USERNAME_KEY, StringUtils.isEmpty(vo.getUsername()) ? DEFAULT_USER : vo.getUsername());
            secretData.put(SECRET_PASSWORD_KEY, vo.getPassword());
            client.createSecret(vo.getNamespace(), getSecretName(vo.getCrName(), vo.getUsername()), null, secretData, cr);
        }
        // 解决跨应用恢复密码错误的问题
        Map<String, String> secretData = new HashMap<>();
        secretData.put(SECRET_BACKUP_USERNAME_KEY, DEFAULT_PASSWORD);
        secretData.put(SECRET_BACKUP_PASSWORD_KEY, DEFAULT_PASSWORD);
        client.createSecret(vo.getNamespace(), cr.getMetadata().getName() + "-privsecrets", null, secretData, cr);

        // 创建exporter上报数据使用cluster svc
        createExporterService(vo);
    }

    private String getSecretName(String crName, String user) {
        return "mgr-" + crName + "-" + user;
    }

    @Override
    protected void completeInstanceProperty(AppInstanceVO appInstanceVO, PodDTO pod, CloudApp app, KubeClient client) {
        if (ROLE_PRIMARY.equals(pod.getLabels().get(ROLE_LABEL))) {
            appInstanceVO.setRole(CloudAppConstant.ROLE_PRIMARY);
        }
        if (ROLE_SECONDARY.equals(pod.getLabels().get(ROLE_LABEL))) {
            appInstanceVO.setRole(CloudAppConstant.ROLE_SECONDARY);
        }
        // liveness
        try {
            String livenessProbe = "sh livenessprobe.sh";
            String data = client.execCmd(app.getNamespace(), pod.getPodName(), getKind().getContainerName(),  "sh", "-c", livenessProbe);
            data = data != null ? data.trim() : null;
            appInstanceVO.setLiveness("mysqld is alive".equals(data) ? 1 : 0);
        } catch (Exception e) {
            log.error("liveness probe error " + e.getMessage());
            appInstanceVO.setLiveness(2);
        }
    }

    @Override
    public Class<? extends OpsPostProcessor> getProcessorClass(ActionEnum action) {
        switch (action) {
            case CREATE:
                return InnoDBClusterInstallWatch.class;
            case SCALE_OUT:
                return InnoDBClusterScaleOutWatch.class;
            case SCALE_IN:
                return InnoDBClusterScaleInWatch.class;
            case UPDATE:
                return InnoDBClusterWatch.class;
            case CREATE_SERVICE:
            case UPDATE_SERVICE:
            case DELETE_SERVICE:
                return InnoDBClusterSvcWatch.class;
            case MODIFY_PARAM:
                return InnoDBClusterModifyParamsWatch.class;
            default:
                return super.getProcessorClass(action);
        }
    }

    /**
     * 支持分配IP地址
     *
     * @param app
     * @return
     */
    @Override
    protected boolean supportIPAM(CloudAppVO app) {
        return false;
    }

    @Override
    @Transactional
    public void update(ResourceDTO patch) throws Exception {
        Consumer<InnoDBCluster> modifier = cr -> {
            cr.getSpec().getPodSpec().getContainers()
                    .stream().filter(c -> c.getName().equals(getKind().getContainerName()))
                    .findFirst().ifPresent(c -> {
                        c.setResources(ResourceHelper.getInstance().resourceRequirements(patch));
                    });
        };
        CloudApp app = appService.get(patch.getId());
        KubeClient client = clientService.get(app.getKubeId());
        Map dataMap = ImmutableMap.of(MGRUtil.REVISION_JOB_DATA_KEY, MGRUtil.getStsRevision(app, client));
        Consumer<InnoDBCluster> storageModifier = cr -> {
            Map<String, Quantity> requests = cr.getSpec().getDatadirVolumeClaimTemplate().getResources().getRequests();
            String storage = requests.get("storage").toString();
            if (MetricUtil.lessAndNotZero(storage, patch.getDisk()))
                requests.put("storage", new Quantity(patch.getDisk()));
        };

        operationHandler.handleUpdate(patch, modifier, this, InnoDBCluster.class, storageModifier, dataMap);

        // update statefulset
        String stsName = MGRUtil.getStsName(app);
        String namespace = app.getNamespace();
        StatefulSet oldSts = client.getStatefulSet(stsName, namespace);

        oldSts.getSpec().getTemplate().getSpec().getContainers().stream()
                .filter(container -> container.getName().equals(getKind().getContainerName()))
                .forEach(container -> {
                    container.setResources(ResourceHelper.getInstance().resourceRequirements(patch));
                });
        if (MetricUtil.lessAndNotZero(app.getDisk(), patch.getDisk())) {
            // recreate sts for resize pvc
            client.deleteStatefulset(stsName, namespace);
            // recreate (operator won't recreate automatically)
            oldSts.getSpec().getVolumeClaimTemplates().stream()
                            .filter(persistentVolumeClaim -> persistentVolumeClaim.getMetadata().getName().equals(DATADIR))
                                    .forEach(pvc -> pvc.getSpec().getResources().getRequests().put("storage", new Quantity(patch.getDisk())));
            client.createOrReplaceSts(oldSts);
            // update pvc manually
            client.listPvc(namespace, MGRUtil.getPVCLabels(app)).getItems()
                    .parallelStream()
                    .forEach(pvc -> {
                        client.updatePvcCapacity(pvc.getMetadata().getName(), namespace, patch.getDisk());
                    });
        } else {
            client.patchStatefulSet(oldSts);
        }
        // fixme transaction
    }

    @Override
    @Transactional
    public void scale(int appId, OverrideSpec overrideSpec, ActionEnum actionEnum) throws Exception {
        BiConsumer<CloudApp, InnoDBCluster> modifier = (app, cr) -> {
            cr.getSpec().setInstances(overrideSpec.getMembers());
        };
        operationHandler.handleScale(appId, InnoDBCluster.class, this, modifier, actionEnum);
        // 需要在cr 提交后修改sts
        CloudApp app = appService.get(appId);
        KubeClient client = clientService.get(app.getKubeId());
        StatefulSet sts = client.getStatefulSet(app.getName(), app.getNamespace());
        sts.getSpec().setReplicas(overrideSpec.getMembers());
        client.patchStatefulSet(sts);
    }




    //    @Override
//    public void uninstall(int id) throws Exception {
//        CloudApp app = appCommonService.get(id);
//        if (app == null) {
//            throw new RuntimeException("app of id did not exist," + id);
//        }
//        // 删除 secret
//        clientService.get(app.getKubeId()).deleteSecret("mgr-" + app.getCrName(), app.getNamespace());
//        clientService.get(app.getKubeId()).deleteSecret("mgr-" + app.getCrName() + "-backup", app.getNamespace());
//        clientService.get(app.getKubeId()).deleteSecret("mgr-" + app.getCrName() + "-privsecrets", app.getNamespace());
//        clientService.get(app.getKubeId()).deleteSecret("mgr-" + app.getCrName() + "-router", app.getNamespace());
//        // 删除 cr
//        clientService.get(app.getKubeId()).deleteCustomResource(new InnoDBCluster(app.getCrName(), app.getNamespace(), null), InnoDBCluster.class);
//        // 删除 svc
//        clientService.get(app.getKubeId()).deleteService(getMGRSvcName(app.getCrName()), app.getNamespace());
//        appCommonService.callScheduler(app, ActionEnum.DELETE, null, InnoDBClusterDeleteWatch.class);
//    }

    @Override
    protected void setInstallExtData(CloudAppVO vo) {
        Map<String,Object> adminUserParamMap = new HashMap<>();
        adminUserParamMap.put("username", vo.getUsername());
        adminUserParamMap.put("password", vo.getPassword());
        adminUserParamMap.put("rootPass", vo.getRootPassword());
        vo.setExtInstallData(adminUserParamMap);
    }

    public static final String YAML_MGR = "apiVersion: mysql.oracle.com/v2\n" +
            "kind: InnoDBCluster\n" +
            "metadata:\n" +
            "  name: ${crname}\n" +
            "  namespace: ${namesapce}\n" +
            "spec:\n" +
//            "  version: ${version}\n" +
//            "  podLabels: \n" +
//            "    app.kubernetes.io/version: ${resourceVersion}\n" +
            "  imageRepository: ${imageRepository}\n" +
            "  tlsUseSelfSigned: true\n" +
            "  imagePullPolicy: IfNotPresent\n" +
            "  instances: ${num}\n" +
            "  router:\n" +
            "    instances: 0\n" +
            "  secretName: ${rootSecret}\n" +
            "  datadirVolumeClaimTemplate:\n" +
            "    accessModes: \n" +
            "      - ReadWriteOnce\n" +
            "    resources:\n" +
            "      requests:\n" +
            "        storage: ${dataDirSize}\n" +
            "    storageClassName: ${storageClassName}\n" +
            "  mycnf: |\n" +
            "    ${myCnf}\n" +
            "  podSpec:\n" +
            "    containers:\n" +
            "    - name: mysql\n" +
            "      image: ${mysqlImage}\n" +
            "      args:\n" +
            "      - mysqld\n" +
            "      - --user=mysql\n" +
            "      - --log-error=/var/log/mysql/error.log\n" +
            "      - --slow_query_log_file=/var/log/mysql/slow.log\n" +
            "      resources:\n" +
            "        limits:\n" +
            "          cpu: ${cpuLimit}\n" +
            "          memory: ${memoryLimit}\n" +
            "        requests:\n" +
            "          cpu: ${cpuRequest}\n" +
            "          memory: ${memoryRequest}\n" +
            "      volumeMounts:\n" +
            "      - mountPath: /var/log/mysql\n" +
            "        name: logdir\n" +
            "      - name: timezone\n" +
            "        mountPath: /etc/localtime\n" +
            "    - name: filebeat\n" +
            "      image: ${filebeatImage}\n" +
            "      imagePullPolicy: IfNotPresent\n" +
            "      command:\n" +
            "      - bash\n" +
            "      args:\n" +
            "      - /etc/filebeat/filebeat-entrypoint.sh\n" +
            "      env:\n" +
            "      - name: APP_NAMESPACE\n" +
            "        valueFrom:\n" +
            "          fieldRef:\n" +
            "            apiVersion: v1\n" +
            "            fieldPath: metadata.namespace\n" +
            "      - name: APP_NAME\n" +
            "        value: ${crname}\n" +
            "      - name: POD_IP\n" +
            "        valueFrom:\n" +
            "          fieldRef:\n" +
            "            apiVersion: v1\n" +
            "            fieldPath: status.podIP\n" +
            "      - name: POD_NAME\n" +
            "        valueFrom:\n" +
            "          fieldRef:\n" +
            "            fieldPath: metadata.name\n" +
            "      - name: FILEBEAT_CFG_FILE\n" +
            "        value: mysql-mgr-filebeat.yaml\n" +
            "      volumeMounts:\n" +
            "      - mountPath: /var/log/mysql\n" +
            "        name: logdir\n" +
            "      - mountPath: /etc/filebeat\n" +
            "        name: filebeat\n" +
            "      - name: scripts\n" +
            "        mountPath: /scripts\n" +
            "      securityContext:\n" +
            "        runAsUser: 0\n" +
            "      #- mountPath: /etc/localtime\n" +
            "    - name: exporter\n" +
            "      image: ${exporterImage}\n" +
            "      command:\n" +
            "      - sh\n" +
            "      args:\n" +
            "      - -c\n" +
            "      - export DATA_SOURCE_NAME=\"${MYSQL_USER}:${MYSQL_PASSWORD}@(127.0.0.1:${PORT})/\" && /bin/mysqld_exporter\n" +
            "      env:\n" +
            "      - name: MYSQL_USER\n" +
            "        valueFrom:\n" +
            "          secretKeyRef:\n" +
            "            name: ${backupSecret}\n" +
            "            key: ${backupUserKey}\n" +
            "      - name: MYSQL_PASSWORD\n" +
            "        valueFrom:\n" +
            "          secretKeyRef:\n" +
            "            name: ${backupSecret}\n" +
            "            key: ${backupPasswordKey}\n" +
            "      - name: PORT\n" +
            "        value: ${DB_PORT}\n" +
            "      ports:\n" +
            "      - name: metrics\n" +
            "        containerPort: 9104\n" +
            "      volumeMounts:\n" +
            "      - name: timezone\n" +
            "        mountPath: /etc/localtime\n" +
            "      - name: rundir\n" +
            "        mountPath: /var/run/mysqld\n" +
            "    - name: xtrabackup\n" +
            "      image: ${xbkImage}\n" +
            "      command:\n" +
            "      - bash\n" +
            "      securityContext:\n" +
            "        runAsUser: 0\n" +
            "        privileged: true\n" +
            "      args:\n" +
            "      - \"/scripts/xtrabackup-entrypoint.sh\"\n" +
            "      env:\n" +
            "      - name: BACKUP_USER\n" +
            "        valueFrom:\n" +
            "          secretKeyRef:\n" +
            "            name: ${backupSecret}\n" +
            "            key: ${backupUserKey}\n" +
            "      - name: BACKUP_PASSWORD\n" +
            "        valueFrom:\n" +
            "          secretKeyRef:\n" +
            "            name: ${backupSecret}\n" +
            "            key: ${backupPasswordKey}\n" +
            "      - name: DB_PORT\n" +
            "        value: \"3306\"\n" +
            "      - name: POD_IP\n" +
            "        valueFrom:\n" +
            "          fieldRef:\n" +
            "            apiVersion: v1\n" +
            "            fieldPath: status.podIP\n" +
            "      - name: NAMESPACE\n" +
            "        valueFrom:\n" +
            "          fieldRef:\n" +
            "            fieldPath: metadata.namespace\n" +
            "      - name: POD_NAME\n" +
            "        valueFrom:\n" +
            "          fieldRef:\n" +
            "            fieldPath: metadata.name\n" +
            "      - name: CLUSTER_NAME\n" +
            "        valueFrom:\n" +
            "          fieldRef:\n" +
            "            fieldPath: metadata.labels['mysql.oracle.com/cluster']\n" +
            "      - name: MYSQL_UNIX_PORT\n" +
            "        value: /var/run/mysqld/mysql.sock\n" +
            "      volumeMounts:\n" +
//            "      - name: backupdir\n" +
//            "        mountPath: /backup\n" +
            "      - name: scripts\n" +
            "        mountPath: /scripts\n" +
            "      - name: datadir\n" +
            "        mountPath: /var/lib/mysql\n" +
            "      - name: mycnfdata\n" +
            "        mountPath: /etc/my.cnf\n" +
            "        subPath: my.cnf\n" +
            "      - name: mycnfdata\n" +
            "        mountPath: /etc/my.cnf.d\n" +
            "        subPath: my.cnf.d\n" +
            "      - name: logdir\n" +
            "        mountPath: /var/log/mysql\n" +
            "      - name: tmpdir\n" +
            "        mountPath: /data/tmp\n" +
            "      - name: rundir\n" +
            "        mountPath: /var/run/mysqld\n" +
            "      - name: timezone\n" +
            "        mountPath: /etc/localtime\n" +
            "      lifecycle:\n" +
            "        preStop:\n" +
            "          exec:\n" +
            "            command:\n" +
            "              - bash\n" +
            "              - /scripts/mount-prestop.sh\n" +
            "    volumes:\n" +
            "    - name: filebeat\n" +
            "      configMap:\n" +
            "        name: operator-filebeat-configmap\n" +
            "    - name: logdir\n" +
            "      emptyDir: {}\n" +
            "    - name: tmpdir\n" +
            "      emptyDir: {}\n" +
//            "    - name: backupdir\n" +
//            "      persistentVolumeClaim:\n" +
//            "        claimName: ${backupPvcName}\n" +
            "    - name: timezone\n" +
            "      hostPath:\n" +
            "        path: /usr/share/zoneinfo/Asia/Shanghai\n" +
            "    - name: scripts\n" +
            "      configMap:\n" +
            "        defaultMode: 0777\n" +
            "        name: ${scriptCMName}";
    public static final String YAML_MGR_SECRET = "apiVersion: v1\n" +
            "kind: Secret\n" +
            "metadata:\n" +
            "  name: mgr-${crname}\n" +
            "  namespace: ${ns}\n" +
            "stringData:\n" +
            "  rootUser: root\n" +
            "  rootHost: '%'\n" +
            "  rootPassword: ${password}\n";

    public static final String YAML_MGR_PVC = "apiVersion: v1\n" +
            "kind: PersistentVolumeClaim\n" +
            "metadata:\n" +
            "  name: datadir-${CRName}-${index}\n" +
            "  namespace: ${ns}\n" +
            "  labels: \n" +
            "    mysql.oracle.com/cluster: ${CRName} \n" +
            "spec:\n" +
            "  accessModes:\n" +
            "    - ReadWriteOnce\n" +
            "  resources:\n" +
            "    requests:\n" +
            "      storage: ${storage}\n" +
            "  storageClassName: ${storageClassName}\n" +
            "  volumeMode: Filesystem\n";

    @Override
    public List<ServiceManager> createService(
            String serviceType, CloudAppVO vo, List<?> serviceResources, CustomResource installCr) {

        //1.校验
        AppKind kind = getKind();
        if (serviceResources.isEmpty()) return Collections.emptyList();
        int serviceManagerNum = kind.getServiceManagerNum(serviceType, vo.getMembers());
        if (CollectionUtils.isEmpty(serviceResources) || serviceResources.size() != serviceManagerNum) {
            throw new CustomException(600, "节点端口类型必须指定 " + serviceManagerNum
                    + " 个端口, 实际数量为 " + serviceResources.size() + ", 类型为 " + serviceType);
        }

        List<ServiceManager> svms = new ArrayList<>();
        String namespace = vo.getNamespace();
        int dbPort = getKind().getDbPort();
        String crName = vo.getCrName();
        //2.2 声明构建所需的变量
        Map<String, String> labelMap = Arrays.stream(getKind().labels(vo.getCrName()))
                .collect(toMap(Label::getName, Label::getValue));
        Map<String, String> selectorLabelMap = Arrays.stream(getKind().labelOfService(vo))
                .collect(toMap(Label::getName, Label::getValue));
        Map<String, String> annotationMap = null;
        Map<String, Object> additionalMap = null;

        for (int i = 0; i < serviceResources.size(); i++) {
            Object serviceResource = serviceResources.get(i);
            //2. 通过 fabric8 构建 Service,再 set 到 ServiceManager 中

            //2.3 初始化 ServiceManager
            ServiceManager serviceManager = new ServiceManager();
            // 有读写分离，需要两个 service，且设置属性
            String serviceName;
            if (i == 0) {
                serviceManager.setPurpose(CloudAppConstant.ServicePurpose.WRITE);
                serviceName = kind.getWriteServiceName(crName, null);
                selectorLabelMap.put(ROLE_LABEL, ROLE_PRIMARY);
            } else {
                serviceManager.setPurpose(CloudAppConstant.ServicePurpose.READ);
                serviceName = kind.getReadServiceName(crName);
                selectorLabelMap.put(ROLE_LABEL, ROLE_SECONDARY);
            }
            serviceManager.setServiceName(serviceName);
            serviceManager.setServiceType(serviceType);
            Integer nodePort = null;

            //2.4 根据 ServiceType 填充 NodePort 和 lb注解map
            if (CloudAppConstant.ServiceType.NODE_PORT.equals(serviceType)) {
                //nodeport 方式，serviceResources 结构为 List<Integer>，进行分配 NodePort，只需要一个 NodePort
                nodePort = (Integer) serviceResource;
                serviceManager.setPort(nodePort);
            } else if (CloudAppConstant.ServiceType.LOAD_BALANCER.equals(serviceType)) {
                //lb 方式，serviceResources 结构为 List<String>，进行分配 lbip，只需要一个 lbip，端口为 dbport
                serviceManager.setPort(dbPort);
                String lbip = (String) serviceResource;
                serviceManager.setExternalIp(lbip);
                annotationMap = new HashMap<String, String>(){{
                    putAll(accessManagementService.buildLBAnnotationMap(lbip)); }};
                // LB注意要配置外部连接策略为 Local，否则默认为 Cluster，导致容器内无法获取外部真实 IP
                additionalMap = new HashMap<String, Object>(){{ put("externalTrafficPolicy", "Local");}};
            }
            svms.add(serviceManager);

            //3. 构建 Service
            String yaml = KubeClientUtil.buildServiceYaml(
                    namespace, serviceType, serviceName, nodePort, dbPort, null,
                    labelMap, annotationMap, selectorLabelMap, additionalMap);
            OpLogContext.instance().YAML("Service", yaml, "");
            // 开源的 svc 不属于 cr.spec 可配置属性，所以需要单独提交
            clientService.get(vo.getKubeId()).applyYaml(yaml, namespace);
        }

        return svms;
    }

    @Override
    public void updateService(List<ServiceManager> svcMgrs, CloudApp app, Object oldServiceResource) throws Exception {
        openSourceKindUpdateServiceBuilder(svcMgrs, app, oldServiceResource);
    }

    private HasMetadata copyToApply(io.fabric8.kubernetes.api.model.Service svcInK8s) {
        return new ServiceBuilder().withMetadata(new ObjectMetaBuilder()
                        .withLabels(svcInK8s.getMetadata().getLabels())
                        .withNamespace(svcInK8s.getMetadata().getNamespace())
                        .withName(svcInK8s.getMetadata().getName()).build())
                .withNewSpec().withPorts(svcInK8s.getSpec().getPorts())
                .withType(svcInK8s.getSpec().getType())
                .withSelector(svcInK8s.getSpec().getSelector())
                .endSpec().build();
    }


    private void createExporterService(CloudApp app) {
        // 获取serviceName
        String crName = app.getCrName();
        String svcName = MGRUtil.getMgrExporterSvcName(crName);
        String namespace = app.getNamespace();
        Map<String, String> labelMap = Arrays.stream(getKind().labels(crName))
                .collect(toMap(Label::getName, Label::getValue));
        Map<String, String> selectorLabelMap = Arrays.stream(getKind().labelOfService(app))
                .collect(toMap(Label::getName, Label::getValue));
        String serviceYaml = KubeClientUtil.buildServiceYaml(
                namespace, CloudAppConstant.ServiceType.CLUSTER_IP,
                svcName, null, 9104, MGRUtil.MGR_EXPORTER_ENDPOINT_NAME
                , labelMap, null, selectorLabelMap, null);

        clientService.get(app.getKubeId()).applyYaml(serviceYaml, namespace);
    }

    @Override
    public void modifyConfigParam(Map<String, String> params, Integer appId, String componentKind) throws Exception {
        log.info("[modifyParam] mgr修改参数 应用ID:{}，参数{}", appId, JSONObject.toJSONString(params));
        CloudApp app = appService.get(appId);
        //设置主要信息
        MetaVO metaVO = getMetaVOByApp(app, DEFAULT_PASSWORD,DEFAULT_PASSWORD);
        String executable = params.entrySet().stream().map(item -> String.format("set persist %s=%s", item.getKey(),
                item.getValue())).collect(Collectors.joining(";"));
        try (Connection conn = DBConnectManager.getInstance().getConnection(metaVO)) {
            QueryRunner queryRunner = new QueryRunner();
            if (StringUtils.isNotEmpty(executable))
                queryRunner.execute(conn, executable);
        } catch (Exception e) {
            throw new CustomException(600,  "mgr示例修改参数失败");
        }
        HashMap<Object, Object> map = new HashMap<>();
        map.put("params", params);
        map.put("appId", appId);
        map.put(SECRET_USERNAME_KEY, DEFAULT_PASSWORD);
        map.put(SECRET_PASSWORD_KEY, DEFAULT_PASSWORD);
        appService.callScheduler(app, app.getCr(), map, ActionEnum.MODIFY_PARAM, getProcessorClass(ActionEnum.MODIFY_PARAM), 10);
    }


    public MetaVO getMetaVOByApp(CloudApp app, String username, String password) {
        MetaVO metaVO = getMetaVOByApp(app, null);

        metaVO.setUsername(username);

        SymmetricEncryptionUtil ss = SymmetricEncryptionUtil.getEncryptInstance();
        String passWord = ss.encrypt(password);
        metaVO.setPassword(passWord);
        return metaVO;
    }

    @Override
    public MetaVO getMetaVOByApp(CloudApp app, String dbname) {
        MetaVO metaVO = super.getMetaVOByApp(app, null);
        metaVO.setDbproduct(CloudAppConstant.DbProductType.MYSQL);
        metaVO.setDbversion(app.getVersion());
        metaVO.setUsername(DEFAULT_PASSWORD);
        metaVO.setPassword(DEFAULT_PASSWORD);
        return metaVO;
    }

    protected String[] getStsOrDeployNames(CloudApp app) {
        return new String[]{app.getCrName()};
    }

    @Override
    public void recreate(CloudApp app) {
        String[] names = getStsOrDeployNames(app);
        if (names == null) throw new RuntimeException("statefulset name not specified");
        KubeClient kubeClient = clientService.get(app.getKubeId());
        kubeClient.scaleSts(names[0], app.getNamespace(), app.getMembers());
    }

    @Override
    public void deletePerm(CloudApp app) {
        super.deletePerm(app);
        KubeClient kubeClient = clientService.get(app.getKubeId());
        InnoDBCluster cr = kubeClient.listCustomResource(InnoDBCluster.class, app.getName(), app.getNamespace());
        if (cr != null) {
            cr.getMetadata().setFinalizers(null);
            kubeClient.updateCustomResource(cr, InnoDBCluster.class);
        }
    }

    @Override
    public void deleteCrControlledResources(CloudApp app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        //删除 exporter Service
        kubeClient.deleteService(MGRUtil.getMgrExporterSvcName(app.getCrName()), app.getNamespace());
    }

    @Override
    public void restore(BackupHis backupHis, Integer appId, String restoreTime, String ftpFilename, String backupType) {
        CustPreconditions.checkState(backupHis.getStatus().equals(StatusConstant.SUCCESS), "备份没有成功");
        CloudApp goalApp = appService.get(appId);
        RestoreHis restoreHis = backupUtil.insertRestoreHis(goalApp, Collections.singletonList(backupHis), "", "");
        String crRun = StringUtils.isNotEmpty(goalApp.getCrRun()) ? goalApp.getCrRun() : goalApp.getCr();
        if (StringUtils.isNotEmpty(crRun))
            crRun = crRun.replaceAll("!!\\S+", "");

        ResourceChangeHis resourceChangeHis = appService.getResourceChangeHis(goalApp, ActionEnum.RESTORE, StatusConstant.RUNNING, crRun, null);
        resourceChangeHis.setLastEndTimestamp(System.currentTimeMillis());
        resourceChangeHisService.add(resourceChangeHis);

        //创建定时轮询备份结果
        Map map = new HashMap();
        map.put("restoreHisId", restoreHis.getRestoreHisId());
        map.put("backupHisId", backupHis.getBackupHisId());
        map.put("restoreTime", restoreTime == null ? "" : restoreTime);

        try {
            appService.callScheduler(goalApp, crRun, map, ActionEnum.RESTORE, InnoDBClusterBackupRestoreWatch.class, resourceChangeHis);
        } catch (Exception e) {
            throw new CustomException(600, "提交恢复失败：" + e.getMessage());
        }
    }
}
