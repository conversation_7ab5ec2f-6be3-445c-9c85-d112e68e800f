package cn.newdt.cloud.domain.cr;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.ImageKindEnum;
import cn.newdt.cloud.dmp_common.utils.SpringBeanUtil;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.Updable;
import cn.newdt.cloud.dto.Label;
import cn.newdt.cloud.dto.ResourceDTO;
import cn.newdt.cloud.dto.SelectorDTO;
import cn.newdt.cloud.dto.TolerationDTO;
import cn.newdt.cloud.service.CloudAppConfigService;
import cn.newdt.cloud.service.ResourceHelper;
import cn.newdt.cloud.utils.MongoUtil;
import cn.newdt.cloud.vo.CloudAppVO;
import cn.newdt.commons.exception.CustomException;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.api.model.apps.StatefulSetSpec;
import io.fabric8.kubernetes.api.model.apps.StatefulSetSpecBuilder;
import io.fabric8.kubernetes.client.CustomResource;
import io.fabric8.kubernetes.model.annotation.Group;
import io.fabric8.kubernetes.model.annotation.Plural;
import io.fabric8.kubernetes.model.annotation.Version;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Group("mongodbcommunity.mongodb.com")
@Version("v1")
@Plural("mongodbcommunity")
public class MongoDBCommunity extends CustomResource<MongoDBCommunity.MongoDBSpec, MongoDBCommunity.MongoDBStatus> implements Namespaced {

    public static final String BACKUP_VOLUME = "backup-volume";
    public static final String DATA_VOLUME = "data-volume";
    public static final String LOGS_VOLUME = "logs-volume";
    public static final String LOG_PV_SIZE = "1Gi";
    public static final String ADMIN_SECRET_MOUNT_PATH = "/mnt/user/secrets/admin";


    public MongoDBCommunity() {
    }

    public MongoDBCommunity(String name, String namespace, MongoDBSpec spec) {
        this.setMetadata(new ObjectMetaBuilder().withName(name).withNamespace(namespace).build());
        this.setSpec(spec);
    }

    public MongoDBCommunity(CloudApp app) {
        // todo 从预定义的yaml转
        CloudAppVO vo = (CloudAppVO) app;
        MongoDBCommunity.MongoDBSpec mongoDBSpec = new MongoDBCommunity.MongoDBSpec();
        mongoDBSpec.setMembers(vo.getMembers());
        mongoDBSpec.setType(vo.getArch());
        mongoDBSpec.setVersion(vo.getVersion());
        mongoDBSpec.createSts(vo.getStorageClassName(), vo.getDisk(), vo.getCpu(), vo.getMemory(),
                app, vo.getAntiAffinityRequired(), vo.getSelector(),vo.getToleration());
        // 创建 userAdminAnyDatabase
        // 创建 admin user
        MongoDBUser admin = new MongoDBUser() {{
            setDb("admin");
            setName(MongoUtil.ADMIN);
            setRoles(new Role[]{
                    new Role("admin", "userAdminAnyDatabase"),
                    new Role("admin", "clusterAdmin"),
                    new Role("admin", "readWriteAnyDatabase"),
                    new Role("admin", "backup"),
                    new Role("admin", "restore"),
                    new Role("admin", "clusterMonitor")
            });
            setScramCredentialsSecretName(MongoUtil.getSecretName(app.getCrName(), MongoUtil.ADMIN));
            setPasswordSecretRef(new SecretKeyReference() {{
                setName(MongoUtil.getSecretName(app.getCrName(), MongoUtil.ADMIN));
                setKey("password");
            }});
        }};
        //dmp监控用户
        MongoDBUser dmpAdminUser = new MongoDBUser() {{
            setDb("admin"); // create user in db:admin
            setName(MongoUtil.DMP_ADMIN);
//                setRoles(new Role[]{new Role("admin", "dbAdmin"), new Role("admin", "readWrite")});
            // fix poc mongodb 纳管
            setRoles(new Role[]{new Role("admin", "userAdminAnyDatabase"), new Role("admin", "clusterAdmin"), new Role("admin", "readWriteAnyDatabase")});
            setPasswordSecretRef(new SecretKeyReference() {{
                setName(MongoUtil.getSecretName(app.getCrName(), MongoUtil.DMP_ADMIN));
                setKey("password");
            }});
            setScramCredentialsSecretName(MongoUtil.getSecretName(app.getCrName(), MongoUtil.DMP_ADMIN));
        }};
        final String username = vo.getUsername();
        if (StringUtils.isNotEmpty(username)) {
            MongoDBUser appUser = new MongoDBUser() {{
                setDb("admin"); // create user in db:admin
                setName(username);
//                setRoles(new Role[]{new Role("admin", "dbAdmin"), new Role("admin", "readWrite")});
                // fix poc mongodb 纳管
                setRoles(new Role[]{new Role("admin", "userAdminAnyDatabase"), new Role("admin", "clusterAdmin"), new Role("admin", "readWriteAnyDatabase")});
                setPasswordSecretRef(new SecretKeyReference() {{
                    setName(MongoUtil.getSecretName(app.getCrName(), username));
                    setKey("password");
                }});
                setScramCredentialsSecretName(MongoUtil.getSecretName(app.getCrName(), username));
            }};
            mongoDBSpec.setUsers(new MongoDBCommunity.MongoDBUser[]{admin, appUser, dmpAdminUser});
        } else {
            mongoDBSpec.setUsers(new MongoDBCommunity.MongoDBUser[]{admin, dmpAdminUser});
        }
        MongoDBCommunity.Security security = new MongoDBCommunity.Security();
        Security.Authentication authentication = new Security.Authentication() {{
            setModes(new String[]{"SCRAM"});
        }};
        security.setAuthentication(authentication);
        mongoDBSpec.setSecurity(security);

//      todo db template 转成spec中的 additionalMongodConfig
        this.setMetadata(new ObjectMetaBuilder().withName(vo.getCrName()).withNamespace(vo.getNamespace()).build());
        this.setSpec(mongoDBSpec);
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MongoDBSpec {
        /**
         * replSet 成员个数
         */
        private int members;
        /**
         * mongodb deployment type: 当前仅ReplicaSet
         */
        private String type;
        /**
         * mongodb version
         */
        private String version;
        /**
         * arbiters 个数,与member的区别仅参与选举, see https://docs.mongodb.com/manual/tutorial/add-replica-set-arbiter
         */
        private int arbiters;

        private String featureCompatibilityVersion;
        /**
         * see https://github.com/mongodb/mongodb-kubernetes-operator/pull/219
         */
        private Map<String, String>[] replicaSetHorizons;
        private Security security;
        private MongoDBUser[] users;
        private StatefulSetConfiguration statefulSet;
        private MongodConfiguration additionalMongodConfig;

        /**
         * @param sc
         * @param storage
         * @param cpuLimit
         * @param memoryLimit
         * @param antiAffinityRequired
         * @param nodeAffinityDTO
         * @param tolerationDTO
         */
        public void createSts(String sc, String storage, String cpuLimit, String memoryLimit, CloudApp app, Boolean antiAffinityRequired, SelectorDTO[] nodeAffinityDTO, TolerationDTO[] tolerationDTO) {
            PersistentVolumeClaim dataPvc = new PersistentVolumeClaimBuilder().withNewMetadata().withName(DATA_VOLUME).endMetadata()
                    .withNewSpec().withNewResources().withRequests(Collections.singletonMap("storage", new Quantity(storage)))
                    .endResources().withStorageClassName(sc).endSpec().build();
            PersistentVolumeClaim logsPvc = new PersistentVolumeClaimBuilder().withNewMetadata().withName(LOGS_VOLUME).endMetadata()
                    .withNewSpec().withNewResources().withRequests(Collections.singletonMap("storage", new Quantity(LOG_PV_SIZE))).endResources()
                    .withStorageClassName(sc).endSpec().build();
//            String backupPVCName = MongoUtil.getSharedBackupPVCName(app.getCrName(), BACKUP_VOLUME);
//            PersistentVolumeClaim backupPvc = new PersistentVolumeClaimBuilder().withNewMetadata().withName(backupPVCName).endMetadata()
//                    .withNewSpec().withNewResources().withRequests(Collections.singletonMap("storage", new Quantity(app.getBackupDisk())))
//                    .endResources().withStorageClassName(app.getBackupStorageclassname()).withAccessModes("ReadWriteOnce").endSpec().build();

            // fixme check if is shared storage
            PersistentVolumeClaim[] persistentVolumeClaims = {dataPvc, logsPvc}; // remove backupPvc because it's shared

            //读取镜像列表
            CloudAppConfigService appConfigService = SpringBeanUtil.getBean(CloudAppConfigService.class);
            AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
            Map<ImageKindEnum, String> imageManifest = appConfigService.getImageManifest(appKind, app.getVersion());
            if (imageManifest.size() != AppKind.MongoDB.getContainerImages().size()) {
                throw new CustomException(600,"MongoDB ReplicaSet的镜像不全");
            }

            StatefulSetConfiguration sts = new StatefulSetConfiguration();
            List<Container> initContainers = new ArrayList<>();
            initContainers.add(new ContainerBuilder().withName("mongod-posthook")
                    .withImage(imageManifest.get(ImageKindEnum.MongoRS_Mongod_PH))
                    .withImagePullPolicy("IfNotPresent").build());
            initContainers.add(new ContainerBuilder().withName("mongodb-agent-readinessprobe")
                    .withImage(imageManifest.get(ImageKindEnum.MongoRS_Agent_RP))
                    .withImagePullPolicy("IfNotPresent").build());
            // if 存储采用 hostpath 添加initcontainer 解决容器用户无法读写volume
            if (StringUtils.isEmpty(sc)) {
                Container hostPrepareInitContainer = new ContainerBuilder().withName("hostpath-prepare")
                        .withImage(imageManifest.get(ImageKindEnum.MongoRS_BK_HostPre))
                        .withImagePullPolicy("IfNotPresent")
                        .withCommand("sh", "-c").withArgs("chown -R 2000 /var/log; chown -R 2000 /data").withVolumeMounts(
                                new VolumeMountBuilder().withName("logs-volume").withMountPath("/var/log").build(),
                                new VolumeMountBuilder().withName("data-volume").withMountPath("/data").build()
                        ).build();
                initContainers.add(0, hostPrepareInitContainer);
            }
            // 共享备份存储时是备份存储路径
//            String backupRoot = MongoUtil.getBackupRootPath(app.getNamespace(), app.getCrName());
            // 解决无法读写/backup 目录
//            String backupInitArgs = "mkdir -p " + backupRoot // create '<arch>/<namespace>/<name>' under mountPath =
//                    + ";chown -R 2000 " + backupRoot;
//            Container backupHostPrepareInitContainer = new ContainerBuilder().withName("backup-hostpath-prepare")
//                    .withImage(imageManifest.get(ImageKindEnum.MongoDBCommunity_Backup_Hostpath_Prepare))
//                    .withImagePullPolicy("IfNotPresent")
//                    .withCommand("sh", "-c")
//                    .withArgs(backupInitArgs)
//                    .withVolumeMounts(
//                            new VolumeMountBuilder().withName("backup-volume").withMountPath(BACKUP_MOUNT_PATH).build()
//                    ).build();
//            initContainers.add(0, backupHostPrepareInitContainer);
            // 亲和
            Affinity affinity = null;
            if (nodeAffinityDTO != null) {
                List<NodeSelectorRequirement> requirementList = Arrays.stream(nodeAffinityDTO)
                        .filter(item -> StringUtils.isNotEmpty(item.getOperator()))
                        .map(selector -> new NodeSelectorRequirementBuilder()
                                .withKey(selector.getKey())
                                .withValues(selector.getValues().stream().filter(StringUtils::isNoneEmpty).collect(Collectors.toList()))
                                .withOperator(selector.getOperator()).build())
                        .collect(Collectors.toList());
                if (!requirementList.isEmpty()) {
                    NodeAffinity nodeAffinity = new NodeAffinityBuilder()
                            .withNewRequiredDuringSchedulingIgnoredDuringExecution()
                            .withNodeSelectorTerms(new NodeSelectorTerm(requirementList, null))
                            .endRequiredDuringSchedulingIgnoredDuringExecution()
                            .build();
                    affinity = new AffinityBuilder().withNodeAffinity(nodeAffinity).build();
                }
            }
            List<Toleration> tolerations = null;
            if (tolerationDTO != null) {
                tolerations = Arrays.stream(tolerationDTO)
                        .filter(item -> StringUtils.isNotEmpty(item.getOperator()))
                        .map(tole -> new TolerationBuilder().withKey(tole.getKey())
                                .withOperator(tole.getOperator())
                                .withValue(StringUtils.isEmpty(tole.getValue()) ? null : tole.getValue())
                                .withEffect(tole.getEffect()).withTolerationSeconds(tole.getTolerationSeconds()).build())
                        .collect(Collectors.toList());
            }
            if (antiAffinityRequired){
                // 强制pod反亲和
                PodAffinityTerm podAffinityTerm = new PodAffinityTermBuilder().withLabelSelector(new LabelSelectorBuilder()
                        .withMatchLabels(Label.toMap(AppKind.MongoDB.labelOfPod(app)))
                        .build()).withTopologyKey("kubernetes.io/hostname").build();
                PodAntiAffinity podAntiAffinity = new PodAntiAffinityBuilder().withRequiredDuringSchedulingIgnoredDuringExecution(Lists.newArrayList(podAffinityTerm)).build();
                if (affinity == null) affinity = new AffinityBuilder().withPodAntiAffinity(podAntiAffinity).build();
                else affinity.setPodAntiAffinity(podAntiAffinity);
            }
            String crName = app.getCrName();
            sts.spec = new StatefulSetSpecBuilder()
                    .withNewTemplate()
                    .withMetadata(new ObjectMetaBuilder().withLabels(Label.toMap(AppKind.MongoDB.labelOfPod(app))).build())
                    .withNewSpec()
                    .withContainers(
                            new ContainerBuilder().withName("cloud-agent")
                                    .withSecurityContext(new SecurityContextBuilder().withRunAsGroup(0L).withRunAsUser(0L).build())
                                    .withVolumeMounts(new VolumeMountBuilder().withName("healthstatus").withMountPath("/var/log/mongodb-mms-automation/healthstatus").build())
                                    .withImage(imageManifest.get(ImageKindEnum.MongoRS_Agent))
                                    .withImagePullPolicy("IfNotPresent")
                                    .withCommand("/bin/bash", "-c", "current_uid=$(id -u)\n" +
                                            "run_set_role_label() {\n" +
                                            "  echo \"label agent is running\"\n" +
                                            "  local stateFile=/var/log/mongodb-mms-automation/healthstatus/agent-health-status.json\n" +
                                            "  while true; do\n" +
                                            "    if [ -f $stateFile ]; then break; fi\n" +
                                            "    echo \"wait for state file generated\"\n" +
                                            "    sleep 10\n" +
                                            "  done\n" +
                                            "  while true; do\n" +
                                            "    if ! set_role_label; then\n" +
                                            "      echo \"$(date --rfc-3339=seconds) set role label with error\"\n" +
                                            "    else\n" +
                                            "      echo \"set role label successful\"\n" +
                                            "    fi\n" +
                                            "    sleep 10\n" +
                                            "  done\n" +
                                            "}\n" +
                                            "set_role_label() {\n" +
                                            "\n" +
                                            "  # local role=$(mongo -u admin -p $password --eval 'rs.status().members.filter(member => member.self)[0].stateStr' --quiet | tr '[:upper:]' '[:lower:]')\n" +
                                            "  local state=$(cat /var/log/mongodb-mms-automation/healthstatus/agent-health-status.json | grep -o '\"ReplicationStatus\":[0-9]*' | awk -F: '{print $2}')\n" +
                                            "  echo \"state is $state\"\n" +
                                            "  local role\n" +
                                            "  case $state in\n" +
                                            "    1)\n" +
                                            "      role=\"primary\"\n" +
                                            "      ;;\n" +
                                            "    2)\n" +
                                            "      role=\"secondary\"\n" +
                                            "      ;;\n" +
                                            "    7)\n" +
                                            "      role=\"arbiter\"\n" +
                                            "      ;;\n" +
                                            "    *)\n" +
                                            "      role=\"$state\"\n" +
                                            "      ;;\n" +
                                            "  esac\n" +
                                            "  local label=\"app.kubernetes.io/role\"\n" +
                                            "  local apiServer=https://kubernetes.default.svc:443\n" +
                                            "  local cert=/var/run/secrets/kubernetes.io/serviceaccount/ca.crt\n" +
                                            "  local token=$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)\n" +
                                            "  local podname=$HOSTNAME\n" +
                                            "  local namespace=$(cat /var/run/secrets/kubernetes.io/serviceaccount/namespace)\n" +
                                            "\n" +
                                            "  curl -s --cacert $cert -X PATCH -H \"Authorization: Bearer $token\" -H \"Accept: application/json\" -H \"Content-Type: application/merge-patch+json\"  $apiServer/api/v1/namespaces/$namespace/pods/$podname --data \\\n" +
                                            "  \"{\n" +
                                            "    \\\"metadata\\\": {\n" +
                                            "      \\\"labels\\\": {\n" +
                                            "        \\\"$label\\\": \\\"$role\\\"\n" +
                                            "      }\n" +
                                            "    }\n" +
                                            "  }\n" +
                                            "  \" 1>/dev/null\n" +
                                            "}\n" +
                                            "run_set_role_label\n"
                                            )
                                    .build(),
                            new ContainerBuilder().withName("mongodb-agent")
                                    .withImage(imageManifest.get(ImageKindEnum.MongoRS_Agent))
                                    .withImagePullPolicy("IfNotPresent")
                                    .withVolumeMounts(
                                            new VolumeMountBuilder().withName(CloudAppConstant.Volumes.TIMEZONE_NAME).withMountPath(CloudAppConstant.VolumeMounts.TIMEZONE_MOUNTPATH).build()
                                    )
                                    .build()
//                            new ContainerBuilder().withName("ftp")
//                                    .withImagePullPolicy("IfNotPresent")
//                                    //ftp镜像
//                                    .withImage(imageManifest.get(ImageKindEnum.FTP))
//                                    .withCommand("sh", "-c").withArgs("bash /scripts/ftp-entrypoint.sh")
//                                    .withVolumeMounts(
//                                            new VolumeMountBuilder().withName("backup-volume").withMountPath(mountPath).build(),
//                                            new VolumeMountBuilder().withName("ftp-volume").withMountPath("/scripts").build(),
//                                            new VolumeMountBuilder().withName("data-volume").withMountPath("/data").build()
//                                    )
//                                    .withEnv(new EnvVarBuilder().withName("NAMESPACE").withValue(app.getNamespace()).build(),
//                                            new EnvVarBuilder().withName("NAME").withValue(app.getCrName()).build())
//                                    .build()
                            ,
                            new ContainerBuilder().withName("filebeat")
                                    .withImagePullPolicy("IfNotPresent")
                                    // filebeat容器默认用户设置为root，解决没有权限读取日志问题
                                    .withSecurityContext(new SecurityContextBuilder().withRunAsGroup(0L).withRunAsUser(0L).build())
                                    //filebeat镜像
                                    .withImage(imageManifest.get(ImageKindEnum.Filebeat))
                                    .withCommand("bash").withArgs("/etc/filebeat/filebeat-entrypoint.sh")
                                    .withVolumeMounts(
                                            new VolumeMountBuilder().withName(CloudAppConstant.Volumes.TIMEZONE_NAME).withMountPath(CloudAppConstant.VolumeMounts.TIMEZONE_MOUNTPATH).build(),
                                            new VolumeMountBuilder().withName("mongodb-filebeat-volume").withMountPath("/etc/filebeat/").build(),
                                            new VolumeMountBuilder().withName("logs-volume").withMountPath("/var/log/mongodb-mms-automation").build(),
                                            new VolumeMountBuilder().withName("ftp-volume").withMountPath("/scripts").build()
                                    )
                                    .withResources(
                                            new ResourceRequirements(
                                                    new HashMap<String, Quantity>(){{
                                                        put("cpu", new Quantity("100m"));
                                                        put("memory", new Quantity("200Mi"));}},
                                                    new HashMap<String, Quantity>(){{
                                                        put("cpu", new Quantity("50m"));
                                                        put("memory", new Quantity("50Mi"));}}))
                                    .withEnv(new EnvVarBuilder().withName("NAMESPACE").withValue(app.getNamespace()).build(),
                                            new EnvVarBuilder().withName("CR_NAME").withValue(crName).build(),
                                            new EnvVarBuilder().withName("FILEBEAT_CFG_FILE").withValue("mongodb-replicaset-filebeat.yaml").build(),
                                            new EnvVarBuilder().withName("ARCH").withValue(app.getArch()).build(),
                                            new EnvVarBuilder().withName("POD_IP").withValueFrom(new EnvVarSourceBuilder()
                                                    .withFieldRef(new ObjectFieldSelectorBuilder().withApiVersion("v1").withFieldPath("status.podIP").build()).build()).build()
                                    )
                                    .build()
                            ,
                            new ContainerBuilder().withName("exporter")
                                    .withImagePullPolicy("IfNotPresent")
                                    //exporter镜像
                                    .withImage(imageManifest.get(ImageKindEnum.Exporter))
                                    .withCommand("sh").withArgs("-c", "bash /scripts/exporter-entrypoint.sh")
                                    .withVolumeMounts(
                                            new VolumeMountBuilder().withName(CloudAppConstant.Volumes.TIMEZONE_NAME).withMountPath(CloudAppConstant.VolumeMounts.TIMEZONE_MOUNTPATH).build(),
                                            new VolumeMountBuilder().withName("data-volume").withMountPath("/data").build(),
                                            new VolumeMountBuilder().withName("ftp-volume").withMountPath("/scripts").build()
                                    )
                                    .withResources(
                                            new ResourceRequirements(
                                                    new HashMap<String, Quantity>(){{
                                                        put("cpu", new Quantity("100m"));
                                                        put("memory", new Quantity("200Mi"));}},
                                                    new HashMap<String, Quantity>(){{
                                                        put("cpu", new Quantity("50m"));
                                                        put("memory", new Quantity("50Mi"));}}))
                                    .withEnv(new EnvVarBuilder().withName("NAMESPACE").withValue(app.getNamespace()).build(),
                                            new EnvVarBuilder().withName("CR_NAME").withValue(crName).build(),
                                            new EnvVarBuilder().withName("ARCH").withValue(app.getArch()).build(),
                                            new EnvVarBuilder().withName("MONITOR_USERNAME").withValue(MongoUtil.ADMIN).build(),
                                            new EnvVarBuilder().withName("MONITOR_PASSWORD").withValueFrom(
                                                    new EnvVarSourceBuilder().withSecretKeyRef(
                                                            new SecretKeySelector("password",
                                                                    MongoUtil.getSecretName(crName, MongoUtil.ADMIN), false))
                                                            .build()).build(),
                                            new EnvVarBuilder().withName("MONGO_PORT").withValue(String.valueOf(AppKind.MongoDB.getDbPort())).build(),
                                            new EnvVarBuilder().withName("POD_IP").withValueFrom(new EnvVarSourceBuilder()
                                                    .withFieldRef(new ObjectFieldSelectorBuilder().withApiVersion("v1").withFieldPath("status.podIP").build()).build()).build()
                                    )
                                    .withPorts(new ContainerPort(9216, null, null, "http-metrics", "TCP"))
                                    .build(),
                            new ContainerBuilder().withName("mongod")
                                    .withArgs("bash /scripts/mongodb-entrypoint.sh")
                                    .withImage(imageManifest.get(ImageKindEnum.MainImage))
                                    .withResources(
                                            ResourceHelper.getInstance().resourceRequirements(
                                                    new ResourceDTO() {{
                                                        setCpu(app.getCpu());
                                                        setMemory(app.getMemory());
                                                    }})
                                    ).withImagePullPolicy("IfNotPresent")
                                    .withLifecycle(new LifecycleBuilder().withPreStop(new HandlerBuilder().withExec(new ExecActionBuilder().withCommand(Arrays.asList("bash", "/scripts/mount-prestop.sh")).build()).build()).build())
                                    .withVolumeMounts(
                                            new VolumeMountBuilder().withName(CloudAppConstant.Volumes.TIMEZONE_NAME).withMountPath(CloudAppConstant.VolumeMounts.TIMEZONE_MOUNTPATH).build(),
                                            new VolumeMountBuilder().withName("backup-volume").withMountPath("/scripts").build()
                                    )
                                    .build(),
                            new ContainerBuilder().withName("logs")
                                    .withCommand("bash").withArgs("/scripts/mongodb-entrypoint.sh")
                                    .withImage(imageManifest.get(ImageKindEnum.MainImage))
                                    .withResources(
                                            new ResourceRequirements(
                                                    new HashMap<String, Quantity>() {{
                                                        put("cpu", new Quantity("100m"));
                                                        put("memory", new Quantity("200Mi"));
                                                    }},
                                                    new HashMap<String, Quantity>() {{
                                                        put("cpu", new Quantity("50m"));
                                                        put("memory", new Quantity("50Mi"));
                                                    }}))
                                    .withImagePullPolicy("IfNotPresent")
                                    .withLifecycle(new LifecycleBuilder().withPreStop(new HandlerBuilder().withExec(new ExecActionBuilder().withCommand(Arrays.asList("bash", "/scripts/mount-prestop.sh")).build()).build()).build())
                                    .withVolumeMounts(
                                            new VolumeMountBuilder().withName(CloudAppConstant.Volumes.TIMEZONE_NAME).withMountPath(CloudAppConstant.VolumeMounts.TIMEZONE_MOUNTPATH).build(),
                                            new VolumeMountBuilder().withName("backup-volume").withMountPath("/scripts").build()
                                    )
                                    .build()
                    )
                    .withInitContainers(
                            initContainers
                    )
                    .withVolumes(
//                            new VolumeBuilder().withName(BACKUP_VOLUME).withNewPersistentVolumeClaim(backupPvc.getMetadata().getName(), false).build(),
                            new VolumeBuilder().withName(CloudAppConstant.Volumes.TIMEZONE_NAME).withNewHostPath(CloudAppConstant.Volumes.TIMEZONE_HOSTPATH, "").build(),
                            new VolumeBuilder().withName("ftp-volume").withConfigMap(new ConfigMapVolumeSourceBuilder().withName(MongoUtil.getScriptsCMName(crName)).build()).build(),
                            new VolumeBuilder().withName("mongodb-filebeat-volume").withConfigMap(new ConfigMapVolumeSourceBuilder().withName("operator-filebeat-configmap").build()).build(),
                            new VolumeBuilder().withName("backup-volume").withConfigMap(new ConfigMapVolumeSourceBuilder().withName(MongoUtil.getBackupCMName(crName)).build()).build()
                    )
                    .withAffinity(affinity)
                    .withTolerations(tolerations)
//                    .withSecurityContext(new PodSecurityContextBuilder().withFsGroup(999L).build())
                    .endSpec().endTemplate()
                    .withVolumeClaimTemplates(persistentVolumeClaims)
                    .build();
            this.setStatefulSet(sts);
        }
    }



    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MongoDBStatus {
        private String mongoUri;
        private String phase;
        private int currentStatefulSetReplicas;
        private int currentMongoDBMembers;
        private String message;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Security {
        private Authentication authentication;
        private TLS tls;
        private CustomRole[] roles;

        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Authentication {
            private String[] modes;
            private Boolean ignoreUnknownUsers;
        }

        @Data
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class TLS {
            private boolean enabled;
            private CertificateKeySecretRef certificateKeySecretRef;
            private CaConfigMapRef caConfigMapRef;
        }

        private class CustomRole {
        }
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CertificateKeySecretRef {
        private String name;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CaConfigMapRef {
        private String name;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MongoDBUser {
        private String name;
        private String db;
        private SecretKeyReference passwordSecretRef;
        private Role[] roles;
        private String scramCredentialsSecretName;

        @Data
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class SecretKeyReference {
            private String name;
            private String key;
        }

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class Role {
            private String db;
            private String name;
        }
    }

    @Data
    public static class StatefulSetConfiguration {
        private StatefulSetSpec spec; // StatefulSetSpecWrapper operator中
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MongodConfiguration extends HashMap<String, String> {

        public MongodConfiguration() {
        }

        public MongodConfiguration(Map<? extends String, ? extends String> m) {
            super(m);
        }
    }

    public Updable toApp() {
        Updable app = new Updable();
        Map<String, Quantity> limits = this.getSpec().getStatefulSet().getSpec().getTemplate().getSpec().getContainers().stream().filter(c -> c.getName().equals(MongoUtil.DB_CONTAINER_NAME)).findFirst().get().getResources().getLimits();
        app.setMemory(limits.get("cpu").toString());
        app.setCpu(limits.get("memry").toString());
        return app;
//        app.setDisk();
    }

    public enum MongoPhase {
        Running, Failed, Pending;
    }

}
