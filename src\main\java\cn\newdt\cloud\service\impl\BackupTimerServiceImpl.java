package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.domain.cr.MongoDBCluster;
import cn.newdt.cloud.domain.dmp.BinlogBackupHis;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.filter.ResourceView;
import cn.newdt.cloud.mapper.BackupMapper;
import cn.newdt.cloud.mapper.BackupTimerMapper;
import cn.newdt.cloud.mapper.CloudBackupStorageMapper;
import cn.newdt.cloud.mapper.KubeConfigMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.CronTriggerMeta;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.sched.TriggerHisService;
import cn.newdt.cloud.service.sched.TriggerInfoService;
import cn.newdt.cloud.utils.BackupUtil;
import cn.newdt.cloud.utils.CloudFTPUtil;
import cn.newdt.cloud.utils.JsonUtil;
import cn.newdt.cloud.vo.AppInstanceVO;
import cn.newdt.cloud.vo.BackupHisVO;
import cn.newdt.cloud.vo.BackupTimerVO;
import cn.newdt.cloud.vo.CloudBackupStorageVO;
import cn.newdt.commons.bean.UserInfo;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.UserUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.shindata.mysql.v1.MySQLHA;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.quartz.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Paths;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.DatasourceConstant.*;
import static cn.newdt.cloud.constant.ScheduleConstant.JOB_DATA_KEY_CLASS;

@Service
public class BackupTimerServiceImpl implements BackupTimerService {

    @Value("${server.port}")
    private String port;

    @Value("${ftp.basePath:/tmp/}")
    private String basePath;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private BackupTimerMapper backupTimerMapper;

    @Autowired
    private TriggerInfoService triggerInfoService;

    @Autowired
    private TriggerHisService triggerHisService;

    @Autowired
    private BackupUtil backupUtil;

    @Autowired
    private KubeConfigMapper kubeConfigMapper;

    @Autowired
    private CloudAppService cloudAppService;

    @Autowired
    private BackupMapper backupMapper;

    @Autowired
    private ResourceManagerServiceImpl resourceManagerService;

    @Autowired
    private CloudFTPUtil ftpUtil;

    @Autowired(required = false)
    @Lazy
    private Scheduler scheduler;

    private Logger log = LoggerFactory.getLogger(BackupUtil.class);

    @Autowired
    private KubeClientService clientService;

    @Autowired
    private MongoDbClusterService mongoDbClusterService;
    @Autowired
    private FlinkJobService flinkJobService;

    @Autowired
    private CloudBackupStorageMapper cloudBackupStorageMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<BackupTimer> commitBackupTimer(BackupTimerVO backupTimer, String dbType) throws Exception {
        // 0.校验是否已经存在当前应用的备份策略
        List<BackupTimer> backupTimerByAppId = backupTimerMapper.getBackupTimerByAppId(SCHEMA, CLOUD_BACKUP_TIMER, backupTimer.getAppId());
        if(!ObjectUtils.isEmpty(backupTimerByAppId)){
            log.error("当前应用已存在备份策略！");
            throw new CustomException(5600, "当前应用已存在备份策略！！");
        }
        // 1.获取应用与集群配置
        CloudApp app = cloudAppService.get(backupTimer.getAppId());
        KubeConfig kubeConfig = kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, app.getKubeId());
        if (null == kubeConfig) {
            log.error("未获取到集群配置！");
            throw new CustomException(5600, "未获取到集群配置！");
        }

        // 2.解析定时备份周期字符串为cron
        boolean isFullFirstWeek = true;
        boolean isCumFirstWeek = true;
        Map<String, Object> cronMap = createCron(backupTimer, isFullFirstWeek, isCumFirstWeek);
        String fullCron = String.valueOf(cronMap.get(CloudAppConstant.BackupType.full));
        String cumCron = String.valueOf(cronMap.get(CloudAppConstant.BackupType.incre));
        isFullFirstWeek = (boolean) cronMap.get("isFullFirstWeek");
        isCumFirstWeek = (boolean) cronMap.get("isCumFirstWeek");

        // 3.根据类型创建trigger
        if (AppKind.Flink.getKind().equalsIgnoreCase(dbType)) {
            backupTimer.setCron(fullCron);
            flinkJobService.automaticSavepoint(backupTimer);
//            return ImmutableList.of(backupTimer);
        }
        if (CloudAppConstant.Kind.MYSQL.equalsIgnoreCase(dbType) || CloudAppConstant.Kind.VASTBASE.equalsIgnoreCase(dbType) || CloudAppConstant.Kind.OPENGAUSS.equalsIgnoreCase(dbType) || CloudAppConstant.Kind.POSTGRESQL.equalsIgnoreCase(dbType)
                || AppKind.Dameng.getKind().equalsIgnoreCase(dbType)
        ) {
            //有全备和增备
            String triggerName = "";
            if (!isFullFirstWeek) {
                String fullTriggerName = commitBackupTimer(backupTimer, dbType, fullCron, CloudAppConstant.BackupType.full, kubeConfig.getName());
                triggerName = fullTriggerName + ",";
            }
            if (!isCumFirstWeek) {
                String increTriggerName = commitBackupTimer(backupTimer, dbType, cumCron, CloudAppConstant.BackupType.incre, kubeConfig.getName());
                triggerName += increTriggerName + ",";
            }
            //处理定时备份对象的triggerName字符串
            backupTimer.setTriggerName(triggerName.substring(0, triggerName.length() - 1));
            //插入定时备份表
            //全备
            BackupTimer finalBackupTimer = new BackupTimer();
            BeanUtils.copyProperties(backupTimer, finalBackupTimer);
            finalBackupTimer.setBackupDbLog(true);
            backupTimerMapper.commitBackupTimer(SCHEMA, CLOUD_BACKUP_TIMER, finalBackupTimer);

            List<BackupTimer> backupTimerList = new ArrayList<>();
            backupTimerList.add(finalBackupTimer);
            return backupTimerList;
        } else {
            //只有全备
            commitBackupTimer(backupTimer, dbType, fullCron, CloudAppConstant.BackupType.full, kubeConfig.getName());
            //插入定时备份表
            //全备
            BackupTimer fullBackupTimer = new BackupTimer();
            BeanUtils.copyProperties(backupTimer, fullBackupTimer);
            fullBackupTimer.setBackupDbLog(false);
            backupTimerMapper.commitBackupTimer(SCHEMA, CLOUD_BACKUP_TIMER, fullBackupTimer);
            List<BackupTimer> backupTimerList = new ArrayList<>();
            backupTimerList.add(fullBackupTimer);
            return backupTimerList;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteBackupTimerById(Integer backupTimerId,String dbType) throws Exception {
        //获取定时信息先删除定时调度信息再删除定时备份信息
        BackupTimer backupTimer = backupTimerMapper.getBackupTimerById(SCHEMA, CLOUD_BACKUP_TIMER, backupTimerId);
        //拼接用来判断当前定时备份是否唯一的判断标识
        String triggerName = backupTimer.getTriggerName();
        if(!StringUtils.isEmpty(triggerName)){
            if(triggerName.indexOf(",") != -1){
                triggerInfoService.delete(triggerName.split(",")[0], backupTimer.getTriggerGroup());
                triggerInfoService.delete(triggerName.split(",")[1], backupTimer.getTriggerGroup());
            }else{
                triggerInfoService.delete(triggerName, backupTimer.getTriggerGroup());
            }
        }
        return backupTimerMapper.deleteBackupTimerById(SCHEMA, CLOUD_BACKUP_TIMER, backupTimerId);
    }

    @Override
    public BackupTimer get(Integer id) {
        return backupTimerMapper.getBackupTimerById(UserUtil.getSchema(), CLOUD_BACKUP_TIMER, id);
    }

    @Override
    public Integer add(BackupTimer data) {
        return backupTimerMapper.commitBackupTimer(SCHEMA, CLOUD_BACKUP_TIMER, data);
    }

    @Override
    public Integer delete(Integer id) {
        return null;
    }

    @Override
    public Integer update(BackupTimer data) {
        data.setUpdateTime(LocalDateTime.now());
        data.setUpdateUser(UserUtil.getCurrentUser().getName());
        return backupTimerMapper.updateBackupTimer(SCHEMA, CLOUD_BACKUP_TIMER, data);
    }

    @Override
    @ResourceView
    public PageInfo listPage(PageDTO pageDTO) {
        List<BackupTimer> results;
        // 为空查所有
        if (pageDTO.getPageNum() == null && pageDTO.getPageSize() == null && pageDTO.getCondition().get("appId") == null && pageDTO.getCondition().get("appLogicId") == null) {
            results = backupTimerMapper.listByMap(SCHEMA, CLOUD_BACKUP_TIMER, new HashMap<>());
        } else {
            if (!ObjectUtils.isEmpty(pageDTO.getCondition().get("appLogicId"))) {
                pageDTO.getCondition().put("appLogicId", Integer.valueOf(String.valueOf(pageDTO.getCondition().get("appLogicId"))));
            }
            results = backupTimerMapper.listByMap(SCHEMA, CLOUD_BACKUP_TIMER, pageDTO.getCondition());
        }
        List<BackupTimerVO> backupTimerVOList = results.stream().map(result -> {
            BackupTimerVO backupTimerVO = new BackupTimerVO();
            BeanUtils.copyProperties(result, backupTimerVO);
            String arch = cloudAppService.get(backupTimerVO.getAppId()).getArch();
            backupTimerVO.setArch(arch);
            return backupTimerVO;
        }).collect(Collectors.toList());
        return new PageInfo<>(backupTimerVOList);
    }

    @Override
    public void timerPerformBackup(TriggerHis triggerHis) {
        try {
            Map<String, String> dataMap = triggerHis.getJobDataMap();
            Integer appId = Integer.parseInt(dataMap.get("appId"));
            // 0. 判断应用是否已经被删除
            CloudApp app = cloudAppService.get(appId);
            if(app.getIsDeleted() != CloudAppConstant.AppDeleteStatus.NORMAL){
                log.error("应用已被删除！appId：" + appId);
                throw new CustomException(600, "应用已被删除！");
            }
            String appName = dataMap.get("appName");
            String backupType = dataMap.get("backupType");
            String appType = dataMap.get("appType");
            String dbType = dataMap.get("dbType");
            String backupDbLogStr = dataMap.get("backupDbLog");
            String backupSetRetention = dataMap.get("backupSetRetention");
            String backupLogRetention = dataMap.get("backupLogRetention");
            String maxBackupDuration = dataMap.get("maxBackupDuration");
            Boolean backupDbLog = Boolean.valueOf(backupDbLogStr);
            String userInfoStr = triggerHis.getStoreJobDataMap();
            Map userInfoMap = JsonUtil.toObject(Map.class, userInfoStr);
            String user_info = (String) userInfoMap.get("USER_INFO");
            UserInfo userInfo = JsonUtil.toObject(UserInfo.class, user_info);
            UserUtil.setAsyncUserInfo(userInfo);

            BackupHisVO backupHis = new BackupHisVO() {{
                setAppId(appId);
                setAppName(appName);
                setAppType(appType);
                setBackupType(backupType);
                setPerformType(ScheduleConstant.CRON);
                setBackupDbLog(backupDbLog);
                setBackupSetRetention(Integer.valueOf(backupSetRetention));
                setMaxBackupDuration(Integer.valueOf(maxBackupDuration));
            }};
            if(null != backupLogRetention){
                backupHis.setBackupLogRetention(Integer.valueOf(backupLogRetention));
            }
            AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
            if (appKind == AppKind.MYSQL_MGR) {
                backupUtil.mgrBackup(backupHis);
            } else if (appKind == AppKind.MYSQL_HA) {
                backupUtil.performBackup(backupHis);
            } else if (appKind == AppKind.MongoDB) {
                backupUtil.mongoBackup(backupHis);
            } else if (appKind == AppKind.Redis) {
                backupUtil.redisBackup(backupHis);
            } else if (appKind == AppKind.Redis_Cluster) {
                backupUtil.redisClusterBackup(backupHis);
            } else if (appKind == AppKind.OpenGauss) {
                backupUtil.ogBackup(backupHis);
            } else if (appKind == AppKind.MongoDB_Cluster) {
                backupUtil.mongodbClusterBackup(backupHis);
            } else if (appKind == AppKind.Elasticsearch) {
                backupUtil.elasticSearchBackup(backupHis);
            } else if (appKind == AppKind.PostgreSQL) {
                backupUtil.postgresqlBackup(backupHis);
            } else if (appKind == AppKind.Flink) {
                backupHis.setAppLogicId(app.getLogicAppId());
                backupUtil.flinkBackup(backupHis);
            } else if (appKind == AppKind.Clickhouse) {
                backupUtil.clickHouseBackup(backupHis);
            } else if (appKind == AppKind.TIDB) {
                backupUtil.tidbBackup(backupHis);
            } else if (appKind == AppKind.Dameng) {
                backupUtil.damengBackup(backupHis);
            } else if (appKind == AppKind.Vastbase) {
                backupUtil.vastbaseBackup(backupHis);
            } else {
                throw new CustomException(500, "当前数据库类型未存在备份操作！");
            }


            //任务调度回调
            triggerHis.setStatus(ScheduleConstant.TRIGGER_EXECUTE_SUCCESS);
            Timestamp endTime = new Timestamp(System.currentTimeMillis());
            triggerHis.setEndTime(endTime);
            triggerHis.setMsg("定时备份成功");
            triggerHisService.update(triggerHis);
        } catch (Exception e) {
            triggerHis.setStatus(ScheduleConstant.TRIGGER_EXECUTE_FAILED);
            Timestamp endTime = new Timestamp(System.currentTimeMillis());
            triggerHis.setEndTime(endTime);
            triggerHis.setMsg("定时备份失败！" + e.getMessage());
            triggerHisService.update(triggerHis);
        }
    }

    @Override
    public void cleanBackupData(TriggerHis triggerHis) throws IOException {
        //删除的备份历史idlist，最后用来修改备份历史状态为已删除
        List<Integer> delBackupHisIdList = new ArrayList<>();
        List<BackupTimer> backupTimers = backupTimerMapper.listByMap(SCHEMA, CLOUD_BACKUP_TIMER, new HashMap<>());

        //获取默认备份文件保留时间
        String defaultBackupConfig = sysConfigService.findOne("backup.config", "defaultConfig");
        if (StringUtils.isBlank(defaultBackupConfig)) {
            throw new CustomException(600, "未查询到默认备份配置！");
        }
        JSONObject defaultBackupConfigObj = JsonUtil.toObject(JSONObject.class, defaultBackupConfig);
        Integer defaultBackupSetRetention = defaultBackupConfigObj.getInteger("backupSetRetention");
        Integer defaultBackupLogRetention = defaultBackupConfigObj.getInteger("backupLogRetention");
        //appId与保留时长map
        HashMap<String, Integer> appIdAndBakupSetRetentionMap = new HashMap<>();

        // 1.获取备份历史
        Map<String, Object> condition = new HashMap<>();
        condition.put("status", "0");
        List<BackupHis> backupHisList = backupMapper.listBackupHisByAppIdsAndStatus(SCHEMA, CLOUD_BACKUP_HIS, condition);

        // 2.获取定时备份表中的记录，用来获取保留时长
        backupHisList.stream().map(backuphis -> {
            appIdAndBakupSetRetentionMap.put(backuphis.getAppId() + "-set", defaultBackupSetRetention);
            appIdAndBakupSetRetentionMap.put(backuphis.getAppId() + "-log", defaultBackupLogRetention);
            return null;
        }).collect(Collectors.toList());


        backupTimers.stream().map(backupTimer -> {
            if (null != backupTimer.getBackupSetRetention()) {
                appIdAndBakupSetRetentionMap.put(backupTimer.getAppId() + "-set", backupTimer.getBackupSetRetention());
            }
            if (null != backupTimer.getBackupLogRetention()) {
                appIdAndBakupSetRetentionMap.put(backupTimer.getAppId() + "-log", backupTimer.getBackupLogRetention());
            }
            return null;
        }).collect(Collectors.toList());

        //创建记录每个应用距离删除时间最前最近的一次全备的baseTime，结构为Map，key为appId，value为baseTime
        Map<Integer, String> appIdAndBaseTimeMap = new HashMap<>();
        // 3.清理前将容器云路径与nas服务器进行挂载
        //获取配置的备份存储信息
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
            String nasIp = cloudBackupStorageVO.getServer();
            String nasPath = cloudBackupStorageVO.getMountPath();
            boolean isMount = resourceManagerService.checkIsMount(nasIp, nasPath);
            if (!isMount) {
                //挂载
                resourceManagerService.mountToNas(nasIp, nasPath);
            }
        } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
            //挂载
            try {
                String mountCmd = "echo \"" + cloudBackupStorageVO.getAccessKey() + ":" + cloudBackupStorageVO.getSecretKey() + "\" > /opt/s3pwd;" +
                        "chmod 600 /opt/s3pwd;" +
                        "mkdir -p /opt/s3mountpath;" +
                        "s3fs -o passwd_file=/opt/s3pwd -o use_path_request_style -o endpoint=" + cloudBackupStorageVO.getRegion() + " -o url=" + cloudBackupStorageVO.getServer() + " -o allow_other -o no_check_certificate " + cloudBackupStorageVO.getBucket() + " " + CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH + "/ -o ssl_verify_hostname=0;" +
                        "sleep 1;" +
                        "df |grep " + CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH;
                ProcessBuilder processBuilder = new ProcessBuilder();
                String osName = System.getProperty("os.name");
                if (osName.contains("Windows")) {
                    processBuilder.command("CMD", "/c", "dir *.txt");
                } else {
                    processBuilder.command("sh", "-c", mountCmd);
                }
                Process process = processBuilder.start();
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                StringBuilder output = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line);
                }

                int exitCode = process.waitFor();

                log.info("mount Exit Code: " + exitCode);
                log.info("mount Output:\n" + output.toString());

                if (0 != exitCode) {
                    throw new CustomException(600, "挂载失败！错误信息为：" + output.toString());
                }
            } catch (Exception e) {
                throw new CustomException(600, "挂载失败！错误信息为：" + e.getMessage());
            }
        } else {
            throw new CustomException(600, "挂载失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
        }

        // 4.遍历备份历史获取需要删除的文件路径
        long currentTimeMillis = System.currentTimeMillis();
        SimpleDateFormat sdfCommon = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long nowTime = new Timestamp(currentTimeMillis).getTime();
        backupHisList.stream()
                .map(backupHis -> {
                    //获取超时时间
                    Integer backupSetRetention = appIdAndBakupSetRetentionMap.get(backupHis.getAppId() + "-set");
                    Integer backupLogRetention = appIdAndBakupSetRetentionMap.get(backupHis.getAppId() + "-log");
                    //备份文件名称
                    String backupFileName = backupHis.getFileName();
                    //获取应用
                    CloudApp app = cloudAppService.get(backupHis.getAppId());
                    //判断备份是否符合删除标准
                    if (backupSetRetention != null ? nowTime - backupHis.getEndTime().getTime() > 1000 * 60 * 60 * 24 * backupSetRetention : false) {
                        //容器云服务固定挂载路径：/opt/nfsmountpath
                        //根据类型删除备份文件
                        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
                        if (AppKind.MYSQL_HA == appKind) {
                            boolean isDelete = checkDelBackupHis(appIdAndBaseTimeMap, app, sdfCommon, currentTimeMillis, backupSetRetention, backupHis);
                            if (!isDelete) {
                                return null;
                            }
                            //删除xbk文件
                            String backupFilePath = "";
                            String xbkFilePath = "";
                            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + "/mysql/mysqlha/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                                xbkFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + "/mysql/mysqlha/" + app.getNamespace() + "/" + app.getCrName();
                            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH + "/mysql/mysqlha/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                                xbkFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH + "/mysql/mysqlha/" + app.getNamespace() + "/" + app.getCrName();
                            } else {
                                throw new CustomException(600, "删除备份文件失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                            }
                            deleteBackupFile(backupFilePath, backupHis.getBackupHisId(), delBackupHisIdList);
                            //删除xbk.文件
                            String xbkFileName = "";
                            if (-1 != backupFileName.indexOf(".full.")) {
                                xbkFileName = "xbk." + backupFileName.substring(0, backupFileName.indexOf(".full."));
                            } else {
                                xbkFileName = "xbk." + backupFileName.substring(backupFileName.indexOf(".inc.") + ".inc.".length(), backupFileName.indexOf(".xbstream"));
                            }
                            //获取到xbk文件名称，则删除
                            if (!StringUtils.isEmpty(xbkFileName)) {
                                xbkFilePath += "/" + xbkFileName;
                                deleteBackupFile(xbkFilePath, backupHis.getBackupHisId(), delBackupHisIdList);
                            }
                        } else if (AppKind.MYSQL_MGR == appKind) {
                            boolean isDelete = checkDelBackupHis(appIdAndBaseTimeMap, app, sdfCommon, currentTimeMillis, backupSetRetention, backupHis);
                            if (!isDelete) {
                                return null;
                            }
                            String backupFilePath = "";
                            String xbkFilePath = "";
                            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + "/mysql/mgr/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                                xbkFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + "/mysql/mgr/" + app.getNamespace() + "/" + app.getCrName();
                            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH + "/mysql/mgr/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                                xbkFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH + "/mysql/mgr/" + app.getNamespace() + "/" + app.getCrName();
                            } else {
                                throw new CustomException(600, "删除备份文件失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                            }
                            deleteBackupFile(backupFilePath, backupHis.getBackupHisId(), delBackupHisIdList);
                            //删除xbk.文件
                            String xbkFileName = "";
                            if (-1 != backupFileName.indexOf(".full.")) {
                                xbkFileName = "xbk." + backupFileName.substring(0, backupFileName.indexOf(".full."));
                            } else {
                                xbkFileName = "xbk." + backupFileName.substring(backupFileName.indexOf(".inc.") + ".inc.".length(), backupFileName.indexOf(".xbstream"));
                            }
                            //获取到xbk文件名称，则删除
                            if (!StringUtils.isEmpty(xbkFileName)) {
                                xbkFilePath += "/" + xbkFileName;
                                deleteBackupFile(xbkFilePath, backupHis.getBackupHisId(), delBackupHisIdList);
                            }
                        } else if (AppKind.MongoDB == appKind) {
                            String backupFilePath = "";
                            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + "/mongodb/replicaset/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH + "/mongodb/replicaset/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                            } else {
                                throw new CustomException(600, "删除备份文件失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                            }
                            deleteBackupFile(backupFilePath, backupHis.getBackupHisId(), delBackupHisIdList);
                        } else if (AppKind.MongoDB_Cluster == appKind) {
                            String backupFilePath = "";
                            String jsonFilePath = "";
                            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + "/mongodb/cluster/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                                jsonFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + "/mongodb/cluster/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName + ".pbm.json";
                            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH + "/mongodb/cluster/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                                jsonFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH + "/mongodb/cluster/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName + ".pbm.json";
                            } else {
                                throw new CustomException(600, "删除备份文件失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                            }
                            deleteBackupFile(backupFilePath, backupHis.getBackupHisId(), delBackupHisIdList);
                            deleteBackupFile(jsonFilePath, backupHis.getBackupHisId(), delBackupHisIdList);
                        } else if (AppKind.Redis == appKind) {
                            String backupFilePath = "";
                            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + "/redis/ha/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH + "/redis/ha/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                            } else {
                                throw new CustomException(600, "删除备份文件失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                            }
                            deleteBackupFile(backupFilePath, backupHis.getBackupHisId(), delBackupHisIdList);
                        } else if (AppKind.Redis_Cluster == appKind) {
                            String backupFilePath = "";
                            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + "/redis/cluster/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH + "/redis/cluster/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                            } else {
                                throw new CustomException(600, "删除备份文件失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                            }
                            deleteBackupFile(backupFilePath, backupHis.getBackupHisId(), delBackupHisIdList);
                        } else if (AppKind.OpenGauss == appKind) {
                            String backupFilePath = "";
                            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + "/opengauss/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH + "/opengauss/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                            } else {
                                throw new CustomException(600, "删除备份文件失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                            }
                            deleteBackupFile(backupFilePath, backupHis.getBackupHisId(), delBackupHisIdList);
                        } else if (AppKind.PostgreSQL == appKind) {
                            String backupFilePath = "";
                            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + "/postgresql/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH + "/postgresql/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                            } else {
                                throw new CustomException(600, "删除备份文件失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                            }
                            deleteBackupFile(backupFilePath, backupHis.getBackupHisId(), delBackupHisIdList);
                        } else if (AppKind.Elasticsearch == appKind) {
                            String backupFilePath = "";
                            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + "/elasticsearch/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH + "/elasticsearch/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                            } else {
                                throw new CustomException(600, "删除备份文件失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                            }
                            deleteBackupFile(backupFilePath, backupHis.getBackupHisId(), delBackupHisIdList);
                        } else if (AppKind.Clickhouse == appKind) {
                            String[] backupFileNameArr = backupFileName.split(",");
                            Arrays.stream(backupFileNameArr).parallel().forEach(tempBackupFileName -> {
                                String backupFilePath = getBackupFileDir(cloudBackupStorageVO) + File.separator + AppKind.Clickhouse.getBackupFilePath(app.getNamespace(), app.getCrName(), tempBackupFileName);
                                deleteBackupFile(backupFilePath, backupHis.getBackupHisId(), delBackupHisIdList);
                            });
                        } else if (AppKind.Vastbase == appKind) {
                            BackupHis fullBackupHis = backupMapper.getLastSuccessFullBackupHis(SCHEMA, CLOUD_BACKUP_HIS, app.getId());
                            Long time = (fullBackupHis.getStartTime().getTime() - backupHis.getStartTime().getTime()) / 1000;
                            if (time <= 0) {
                                return null;
                            }
                            String backupFilePath = "";
                            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + "/vastbase/backups/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                                backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH + "/vastbase/backups/" + app.getNamespace() + "/" + app.getCrName() + "/" + backupFileName;
                            } else {
                                throw new CustomException(600, "删除备份文件失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                            }
                            deleteBackupFile(backupFilePath, backupHis.getBackupHisId(), delBackupHisIdList);
                        } else if (AppKind.Dameng == appKind) {
                            String mountPath = getMountPath(cloudBackupStorageVO.getStorageType());
                            String backupFilePath = "";
                            String mediumPath = DamengService.Name.getBackupStorePathFormatted(app.getNamespace(), app.getCrName());
                            backupFilePath = Paths.get(mountPath, mediumPath, backupFileName).toString();
                            deleteBackupFile(backupFilePath, backupHis.getBackupHisId(), delBackupHisIdList);
                        }
                    }
                    //判断binlog是否符合删除标准
                    if (null != backupHis.getBackupDbLog() && backupHis.getBackupDbLog() && backupLogRetention != null ? nowTime - backupHis.getEndTime().getTime() > 1000 * 60 * 60 * 24 * backupLogRetention : false) {
                        //容器云服务固定挂载路径：/opt/nfsmountpath
                        //根据类型删除备份文件
                        if (AppKind.MYSQL_HA == AppKind.valueOf(app.getKind(), app.getArch())) {
                            boolean isDelete = checkDelBackupHis(appIdAndBaseTimeMap, app, sdfCommon, currentTimeMillis, backupLogRetention, backupHis);
                            if (!isDelete) {
                                return null;
                            }
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                            String binlogTime = sdf.format(backupHis.getStartTime());
                            String binlogPath = "";
                            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                                binlogPath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + "/mysql/mysqlha/" + app.getNamespace() + "/" + app.getCrName() + "/binlog/" + binlogTime;
                            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                                binlogPath = CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH + "/mysql/mysqlha/" + app.getNamespace() + "/" + app.getCrName() + "/binlog/" + binlogTime;
                            } else {
                                throw new CustomException(600, "清理MySQLbinlog失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                            }
                            ProcessBuilder processBuilder = new ProcessBuilder();
                            String osName = System.getProperty("os.name");
                            if (osName.contains("Windows")) {
                                processBuilder.command("CMD", "/c", "dir *.txt");
                            } else {
                                processBuilder.command("rm", "-rf", binlogPath);
                            }
                            try {
                                Process process = processBuilder.start();
                                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                                StringBuilder output = new StringBuilder();
                                String line;
                                while ((line = reader.readLine()) != null) {
                                    output.append(line);
                                }

                                int exitCode = process.waitFor();

                                log.info("cleanbinlog Exit Code: " + exitCode);
                                log.info("cleanbinlog Output:\n" + output.toString());

                                if (0 != exitCode) {
                                    throw new CustomException(600, "清理MySQLbinlog失败！错误信息为：" + output.toString());
                                }
                            } catch (Exception e) {
                                throw new CustomException(600, "清理MySQLbinlog失败！备份文件路径：" + binlogPath);
                            }
                        } else if (AppKind.MYSQL_MGR == AppKind.valueOf(app.getKind(), app.getArch())) {
                            boolean isDelete = checkDelBackupHis(appIdAndBaseTimeMap, app, sdfCommon, currentTimeMillis, backupSetRetention, backupHis);
                            if (!isDelete) {
                                return null;
                            }
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                            String binlogTime = sdf.format(backupHis.getStartTime());
                            String binlogPath = "";
                            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                                binlogPath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + "/mysql/mgr/" + app.getNamespace() + "/" + app.getCrName() + "/binlog/" + binlogTime;
                            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                                binlogPath = CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH + "/mysql/mgr/" + app.getNamespace() + "/" + app.getCrName() + "/binlog/" + binlogTime;
                            } else {
                                throw new CustomException(600, "清理MySQLbinlog失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                            }
                            ProcessBuilder processBuilder = new ProcessBuilder();
                            String osName = System.getProperty("os.name");
                            if (osName.contains("Windows")) {
                                processBuilder.command("CMD", "/c", "dir *.txt");
                            } else {
                                processBuilder.command("rm", "-rf", binlogPath);
                            }
                            try {
                                Process process = processBuilder.start();
                                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                                StringBuilder output = new StringBuilder();
                                String line;
                                while ((line = reader.readLine()) != null) {
                                    output.append(line);
                                }

                                int exitCode = process.waitFor();

                                log.info("cleanbinlog Exit Code: " + exitCode);
                                log.info("cleanbinlog Output:\n" + output.toString());

                                if (0 != exitCode) {
                                    throw new CustomException(600, "清理MySQLMGRbinlog失败！错误信息为：" + output.toString());
                                }
                            } catch (Exception e) {
                                throw new CustomException(600, "清理MySQLMGRbinlog失败！备份文件路径：" + binlogPath);
                            }
                        }
                    }
                    return null;
                }).collect(Collectors.toList());

        // 5.修改备份记录
        //修改所有已删除备份历史的状态
        if (!CollectionUtils.isEmpty(delBackupHisIdList)) {
            String delIdStr = delBackupHisIdList.toString().replace("[", "").replace("]", "").replace("\"", "");
            backupMapper.updateBackupHisStatusAfterDeleteFile(SCHEMA, CLOUD_BACKUP_HIS, delIdStr);
        }

        // 6.解除临时挂载
        if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
            String nasIp = cloudBackupStorageVO.getServer();
            String nasPath = cloudBackupStorageVO.getMountPath();
            resourceManagerService.umountToNas(nasIp, nasPath);
        } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
            //挂载
            try {
                String umountCmd = "umount -lf " + CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH;
                ProcessBuilder processBuilder = new ProcessBuilder();
                String osName = System.getProperty("os.name");
                if (osName.contains("Windows")) {
                    processBuilder.command("CMD", "/c", "dir *.txt");
                } else {
                    processBuilder.command("sh", "-c", umountCmd);
                }
                Process process = processBuilder.start();
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                StringBuilder output = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line);
                }

                int exitCode = process.waitFor();

                log.info("mount Exit Code: " + exitCode);
                log.info("mount Output:\n" + output.toString());

                if (0 != exitCode) {
                    throw new CustomException(600, "取消挂载失败！错误信息为：" + output.toString());
                }
            } catch (Exception e) {
                throw new CustomException(600, "取消挂载失败！错误信息为：" + e.getMessage());
            }
        } else {
            throw new CustomException(600, "取消挂载失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
        }
    }

    private static String getMountPath(String storageType) {
        String mountPath;
        if (storageType.equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
            mountPath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH;
        } else if (storageType.equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
            mountPath = CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH;
        } else {
            throw new CustomException(600, "删除备份文件失败！不支持当前备份存储类型:" + storageType);
        }
        return mountPath;
    }

    private String getBackupFileDir(CloudBackupStorageVO cloudBackupStorageVO) {
        String backupFilePath;
        if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
            backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH;
        } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
            backupFilePath = CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH;
        } else {
            throw new CustomException(600, "删除备份文件失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
        }
        return backupFilePath;
    }

    private void deleteBackupFile(String backupFilePath, Integer backupHisId, List<Integer> delBackupHisIdList) {
        ProcessBuilder processBuilder = new ProcessBuilder();
        String osName = System.getProperty("os.name");
        if (osName.contains("Windows")) {
            processBuilder.command("CMD", "/c", "dir *.txt");
        } else {
            processBuilder.command("rm", "-rf", backupFilePath);
        }
        try {
            Process process = processBuilder.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder output = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line);
            }

            int exitCode = process.waitFor();

            log.info("cleanbinlog Exit Code: " + exitCode);
            log.info("cleanbinlog Output:\n" + output.toString());

            if (0 != exitCode) {
                throw new CustomException(600, "清理备份文件失败！错误信息为：" + output.toString());
            }
            if (!delBackupHisIdList.contains(backupHisId)) {
                delBackupHisIdList.add(backupHisId);
            }
        } catch (Exception e) {
            throw new CustomException(600, "清理备份文件失败！备份文件路径：" + backupFilePath);
        }
    }

    @Override
    public void startStopBackupTimer(Integer id, boolean triggerState) {
        // 1.获取定时备份历史
        BackupTimer backupTimerById = backupTimerMapper.getBackupTimerById(SCHEMA, CLOUD_BACKUP_TIMER, id);
        // 2.获取trigger的name和group
        String triggerGroup = backupTimerById.getTriggerGroup();
        String triggerName = backupTimerById.getTriggerName();
        // 3.修改定时备份历史状态
        updateTrigger(triggerState, triggerName, triggerGroup);
        backupTimerById.setTriggerState(triggerState);
        backupTimerMapper.updateBackupTimer(SCHEMA, CLOUD_BACKUP_TIMER, backupTimerById);

    }

    private void updateTrigger(boolean triggerState, String triggerName, String triggerGroup){
        if(triggerState){
            try {
                scheduler.resumeTrigger(TriggerKey.triggerKey(triggerName,triggerGroup));
            } catch (SchedulerException e) {
                log.error("开启定时备份失败！信息为：" + e.getMessage());
                throw new CustomException(600, "开启定时备份失败！");
            }
        }else{
            try {
                scheduler.pauseTrigger(TriggerKey.triggerKey(triggerName,triggerGroup));
            } catch (SchedulerException e) {
                log.error("暂停定时备份失败！信息为：" + e.getMessage());
                throw new CustomException(600, "暂停定时备份失败！");
            }
        }
    }

    @Override
    public void binlogBackup(TriggerHis triggerHis) {
        // 1.筛选出需要执行binlog备份的记录
        Map<String, Object> condition = new HashMap<String, Object>();
        condition.put("backupDbLog", 1);
        //查询所有备份策略的应用id
        List<Integer> appIdList = backupTimerMapper.listAppByBackupTimer(SCHEMA, CLOUD_BACKUP_TIMER, CLOUD_APP, condition);
        //查询所有没有在执行定时备份的app
        List<CloudApp> appList = cloudAppService.listAppByNoBinlog(appIdList);
        // 2.遍历提交binlog备份，备份结果在另一个接口查询
        appList.stream().map(app -> {
            log.info("提交binlog备份开始");
            KubeClient kubeClient = clientService.get(app.getKubeId());
            //查询应用是否为ready状态
            String appStatus = CloudAppConstant.CustomResourceState.READY;
            if(app.getKind().equalsIgnoreCase(CloudAppConstant.Kind.MYSQL)){
                MySQLHA mySQLHA = kubeClient.listCustomResource(MySQLHA.class, app.getCrName(), app.getNamespace());
                if(ObjectUtils.isEmpty(mySQLHA)){
                    return null;
                }
                appStatus = mySQLHA.getStatus().getState();
            }else if(app.getKind().equalsIgnoreCase(CloudAppConstant.Kind.MONGODB)){
                appStatus = kubeClient.listCustomResource(MongoDBCluster.class, app.getCrName(), app.getNamespace()).getStatus().getState();
            }else{
                return null;
            }
            if(!CloudAppConstant.CustomResourceState.READY.equalsIgnoreCase(appStatus)){
                return null;
            }
            // 创建binlog备份历史
            BinlogBackupHis binlogBackupHis = new BinlogBackupHis();
            insertBinlogBackup(app, binlogBackupHis, "2");
            if(CloudAppConstant.Kind.MYSQL.equalsIgnoreCase(app.getKind())){
                //mysql备份
                //获取主实例
                PodDTO podDTO = backupUtil.getBackupPodMySQL(app, kubeClient);
                if (podDTO == null) {
                    updateBinlogBackup(app, binlogBackupHis, "1", "提交binlog备份：未获取到主实例！");
                    return null;
                }
                try {
                    backupUtil.performBinLogBackup(app, podDTO.getPodName(), binlogBackupHis);
                } catch (Exception e) {
                    log.error("", e);
                    updateBinlogBackup(app, binlogBackupHis, "1", "提交binlog备份：提交备份失败！信息为：" + e);
                    return null;
                }
            }else if(CloudAppConstant.Kind.MONGODB.equalsIgnoreCase(app.getKind()) && CloudAppConstant.Arch.CLUSTER.equalsIgnoreCase(app.getArch())){
                //mongodb备份
                List<String> increPods = new ArrayList<>();
                //遍历所有config和shard，上传pbmPitr
                List<AppInstanceVO> instanceList = mongoDbClusterService.findInstanceList(app.getId(), "role", "asc");
                for(AppInstanceVO ins : instanceList){
                    if(!(CloudAppConstant.MongoDB.MONGOS).equalsIgnoreCase(ins.getComponentKind())){
                        increPods.add(ins.getPodName());
                        //上传pbmPitr
                        String pbmPitrRes = null;
                        try {
                            String pbmPitrResStr = kubeClient.execCmd(app.getNamespace(), ins.getPodName(), "pbm", "sh", "-c", "if [[ -d /backup/pbmPitr ]]; then echo \"yes\"; else echo \"no\"; fi");
                            pbmPitrRes = pbmPitrResStr.replace("\n", "");
                        } catch (Exception e) {
                            log.error("", e);
                            updateBinlogBackup(app, binlogBackupHis, "1", "MongoDBCluster增备查询pbmpitr文件失败！错误信息：" + e.getMessage());
                            return null;
                        }
                        if("yes".equalsIgnoreCase(pbmPitrRes)){
                            try {
                                backupUtil.uploadBackupFile(kubeClient, app.getNamespace(), ins.getPodName(), backupUtil.getFtpBackupPath(app), "/backup/pbmPitr", app.getKind());
                                Map backupMes = JsonUtil.toObject(Map.class, binlogBackupHis.getMsg());
                                String isCommitUpload = (String) backupMes.get("isCommitUpload_pbmPitr" + ins.getPodName());
                                if(StringUtils.isEmpty(isCommitUpload) || !"Y".equalsIgnoreCase(isCommitUpload)){
                                    backupUtil.uploadBackupFile(app, ins.getPodName(), backupUtil.getFtpBackupPath(app), "/backup/pbmPitr", binlogBackupHis.getId(), "_pbmPitr");
                                    //对备份历史放入已提交上传的标记
                                    backupMes.put("isCommitUpload_pbmPitr" + ins.getPodName(),"Y");
                                    //插入mes
                                    binlogBackupHis.setMsg(JsonUtil.toJson(backupMes));
                                }
                            } catch (Exception e) {
                                log.error("", e);
                                updateBinlogBackup(app, binlogBackupHis, "1", "MongoDBCluster备增备上传文件失败！错误信息：" + e.getMessage());
                                return null;
                            }
                        }
                    }
                }
                backupMapper.updateBinlogBackupHis(SCHEMA, DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, binlogBackupHis);
            }
            return null;
        }).collect(Collectors.toList());
    }

    public void checkBinlogBackupState(TriggerHis triggerHis){
        // 1.获取所有binlog备份记录
        Map<String, String> condition = new HashMap<>();
        condition.put("status", "2");
        List<BinlogBackupHis> binlogBackupHisList = backupMapper.listBinlogBackupHisByMap(SCHEMA, DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, condition);

        // 2.遍历判断是否成功
        binlogBackupHisList.stream().map(binlogBackupHis -> {
            //获取应用
            CloudApp app = cloudAppService.get(binlogBackupHis.getClusterId());
            //根据应用类型判断
            if(CloudAppConstant.Kind.MYSQL.equalsIgnoreCase(app.getKind())){
                KubeClient kubeClient = clientService.get(app.getKubeId());
                //获取主实例
                PodDTO podDTO = backupUtil.getBackupPodMySQL(app, kubeClient);
                if (podDTO == null) {
                    updateBinlogBackup(app, binlogBackupHis, "1", "检查binlog备份状态：未获取到主实例！");
                    return null;
                }
                //判断binlog是否成功，根据日志
                String binlogBackupLog = "";
                try {
                    String catCmd = String.format("[ -r /data/tmp/binlogbackup_%s ] && cat /data/tmp/binlogbackup_%s  || echo '{}'", binlogBackupHis.getId(), binlogBackupHis.getId());
                    binlogBackupLog = kubeClient.execCmd(app.getNamespace(), podDTO.getPodName(), "mysql", "sh", "-c", catCmd);
                    log.info("[mysql备份]binlog备份日志为：" + binlogBackupLog);
                } catch (Exception e) {
                    updateBinlogBackup(app, binlogBackupHis, "1", "检查binlog备份状态：检查备份状态错误！信息为：" + e);
                    return null;
                }
                if (!org.springframework.util.StringUtils.isEmpty(binlogBackupLog)) {
                    Map binlogLog = JsonUtil.toObject(Map.class, binlogBackupLog);
                    String status = (String)binlogLog.get("status");
                    if("success".equals(status)){
                        //成功
                        //上传binlog
                        Map backupMes = JsonUtil.toObject(Map.class, binlogBackupHis.getMsg());
                        String isCommitUploadBinlog = (String) backupMes.get("isCommitUpload_Binlog");
                        if(StringUtils.isEmpty(isCommitUploadBinlog) || !"Y".equalsIgnoreCase(isCommitUploadBinlog)){
                            //上传到FTP
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                            String nowStr = sdf.format(LocalDateTime.now());
                            try {
                                backupUtil.uploadBackupFile(app, podDTO.getPodName(), basePath + "/" + CloudAppConstant.MYSQL_LOCAL_PATH + "/" + app.getNamespace() + "/" + app.getCrName() + "/binlog/", "/backup/binlog/" + nowStr, binlogBackupHis.getId(), "_binlog");
                            } catch (Exception e) {
                                updateBinlogBackup(app, binlogBackupHis, "1", "检查binlog备份状态：上传binlog文件失败！");
                                return null;
                            }
                            backupMes.put("isCommitUpload_Binlog","Y");
                            binlogBackupHis.setMsg(JsonUtil.toJson(backupMes));
                            backupMapper.updateBinlogBackupHis(SCHEMA, DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, binlogBackupHis);
                            return null;
                        }else{
                            //根据日志判断是否上传成功
                            BackupHis backupHis = new BackupHis();
                            backupHis.setBackupHisId(binlogBackupHis.getId());
                            String checkLogRes = backupUtil.checkUploadBackupFile(backupHis, app, "_binlog", podDTO.getPodName());
                            //判断是否上传成功
                            if(CloudAppConstant.UploadBackupFileState.success.equals(checkLogRes)){
                                //修改binlog备份历史的状态
                                updateBinlogBackup(app, binlogBackupHis, "0", "检查binlog备份状态：上传binlog成功！");
                                return null;
                            }else if(CloudAppConstant.UploadBackupFileState.failed.equals(checkLogRes)){
                                updateBinlogBackup(app, binlogBackupHis, "1", "检查binlog备份状态：上传binlog失败！");
                                return null;
                            }
                        }
                    }else if("failed".equals(status)){
                        updateBinlogBackup(app, binlogBackupHis, "1", "检查binlog备份状态：上传binlog失败！");
                        return null;
                    }
                }
            }else if(CloudAppConstant.Kind.MONGODB.equalsIgnoreCase(app.getKind()) && CloudAppConstant.Arch.CLUSTER.equalsIgnoreCase(app.getArch())){
                List<AppInstanceVO> instanceList = mongoDbClusterService.findInstanceList(app.getId(), "role", "asc");
                for(AppInstanceVO ins : instanceList) {
                    if (!(CloudAppConstant.MongoDB.MONGOS).equalsIgnoreCase(ins.getComponentKind())) {
                        Map backupMes = JsonUtil.toObject(Map.class, binlogBackupHis.getMsg());
                        String isCommitUpload = (String) backupMes.get("isCommitUpload_pbmPitr" + ins.getPodName());
                        if (!(StringUtils.isEmpty(isCommitUpload) || !"Y".equalsIgnoreCase(isCommitUpload))) {
                            //根据日志判断是否上传成功
                            BackupHis backupHis = new BackupHis();
                            backupHis.setBackupHisId(binlogBackupHis.getId());
                            String checkLogRes = backupUtil.checkUploadBackupFile(backupHis, app, "_pbmPitr", ins.getPodName());
                            //判断是否上传成功
                            if (CloudAppConstant.UploadBackupFileState.success.equals(checkLogRes)) {
                                //修改binlog备份历史的状态
                                updateBinlogBackup(app, binlogBackupHis, "0", "检查oplog备份状态：上传oplog成功！");
                                return null;
                            } else if (CloudAppConstant.UploadBackupFileState.failed.equals(checkLogRes)) {
                                updateBinlogBackup(app, binlogBackupHis, "1", "检查oplog备份状态：上传oplog失败！");
                                return null;
                            }
                        }
                    }
                }
            }

            return null;
        }).collect(Collectors.toList());
    }

    /**
     * 上传binlog到ftp
     * @param startTime
     * @param app
     * @param kubeClient
     * @param primaryPodName
     */
    private void uploadBinlogToFTP(Timestamp startTime, CloudApp app, KubeClient kubeClient, String primaryPodName){
        //在ftp容器中执行，上传binlog文件到ftp
        String url = ftpUtil.getURL();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String nowStr = sdf.format(startTime);
        String backupBinlogPath = CloudAppConstant.MYSQL_LOCAL_PATH;
        String uploadScript = "sh /scripts/ftp-upload.sh " + url + " /binlog/" + nowStr + " " + basePath + "/" + backupBinlogPath + "/" + app.getNamespace() + "/" + app.getCrName() + "/binlog/" + nowStr;
        log.info("[mysql备份]上传binlog的命令为：" + uploadScript);
        try {
            kubeClient.execCmdOneway(app.getNamespace(), primaryPodName, "ftp", "sh", "-c", uploadScript);
        } catch (Exception e) {
            log.error("上传binlog失败，信息为：" + e.getMessage() + "     详细信息为:" + e);
            throw new CustomException(600, "上传binlog失败！！");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBackupTimer(BackupTimerVO backupTimer, String dbType) {
        // 1.获取定时备份历史和集群配置
        if(!CloudAppConstant.Kind.MYSQL.equalsIgnoreCase(dbType) || CloudAppConstant.Kind.DAMENG.equalsIgnoreCase(dbType)){
            backupTimer.setBackupLogRetention(null);
        }
        CloudApp app = cloudAppService.get(backupTimer.getAppId());
        KubeConfig kubeConfig = kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, app.getKubeId());
        if (null == kubeConfig) {
            log.error("未获取到集群配置！");
            throw new CustomException(5600, "未获取到集群配置！");
        }
        BackupTimer oldBackupHis = backupTimerMapper.getBackupTimerById(UserUtil.getSchema(), CLOUD_BACKUP_TIMER, backupTimer.getBackupTimerId());

        // 2.判断原先是否存在全备与增备
        String triggerName = oldBackupHis.getTriggerName();
        String triggerGroup = oldBackupHis.getTriggerGroup();
        // 3.判断当前是否存在全备或增备
        boolean isFullFirstWeek = true;
        boolean isCumFirstWeek = true;
        Map<String, Object> cronMap = createCron(backupTimer, isFullFirstWeek, isCumFirstWeek);
        String fullCron = String.valueOf(cronMap.get(CloudAppConstant.BackupType.full));
        String cumCron = String.valueOf(cronMap.get(CloudAppConstant.BackupType.incre));
        isFullFirstWeek = (boolean) cronMap.get("isFullFirstWeek");
        isCumFirstWeek = (boolean) cronMap.get("isCumFirstWeek");
        if (dbType.equalsIgnoreCase(AppKind.Flink.getKind())) {
            backupTimer.setCron(fullCron);
            flinkJobService.automaticSavepoint(backupTimer);
//            return;
        }
        // 4.根据差异进行修改
        //最终的triggerName字符串
        if(triggerName.indexOf(",") != -1){
            //原来存在全备与增备
            //获取原先全备与增备的triggerName
            String fullTriggerName = triggerName.split(",")[0];
            String increTriggerName = triggerName.split(",")[1];
            if(!isFullFirstWeek){
                //当前也存在全备，修改
                try {
                    //修改trigger
                    updateTrigger(fullTriggerName, triggerGroup, fullCron);
                    //启停修改
                    updateTrigger(backupTimer.isTriggerState(), fullTriggerName, triggerGroup);
                } catch (Exception e) {
                    throw new CustomException(500, "修改定时备份失败！修改全备失败！" + e.getMessage());
                }
            }else{
                //当前不存在全备，删除之前全备
                try {
                    triggerInfoService.delete(fullTriggerName, triggerGroup);
                    //删除全备triggerName
                    triggerName = triggerName.replace(dbType + "-backup-timer-" + backupTimer.getAppId() + "-" + CloudAppConstant.BackupType.full, "").replace(",", "");
                } catch (SchedulerException e) {
                    throw new CustomException(500, "修改定时备份失败！删除全备失败！" + e.getMessage());
                }
            }
            if(!isCumFirstWeek){
                //当前也存在增备，修改
                try {
                    //修改trigger
                    updateTrigger(increTriggerName, triggerGroup, cumCron);
                    //启停修改
                    updateTrigger(backupTimer.isTriggerState(), increTriggerName, triggerGroup);
                } catch (Exception e) {
                    throw new CustomException(500, "修改定时备份失败！修改增备失败！" + e.getMessage());
                }
            }else{
                //当前不存在增备，删除之前增备
                try {
                    triggerInfoService.delete(increTriggerName, triggerGroup);
                    //删除增备triggerName
                    triggerName = triggerName.replace(dbType + "-backup-timer-" + backupTimer.getAppId() + "-" + CloudAppConstant.BackupType.incre, "").replace(",", "");
                } catch (SchedulerException e) {
                    throw new CustomException(500, "修改定时备份失败！删除增备失败！" + e.getMessage());
                }
            }
        }else if(triggerName.indexOf("full") != -1){
            //原来只存在全备
            if(!isFullFirstWeek){
                //当前也存在全备，修改
                try {
                    //修改trigger
                    updateTrigger(triggerName, triggerGroup, fullCron);
                    //启停修改
                    updateTrigger(backupTimer.isTriggerState(), triggerName, triggerGroup);
                } catch (Exception e) {
                    throw new CustomException(500, "修改定时备份失败！修改全备失败！" + e.getMessage());
                }
            }else{
                //当前不存在全备，删除之前全备
                try {
                    triggerInfoService.delete(triggerName, triggerGroup);
                    //删除全备triggerName
                    triggerName = triggerName.replace(dbType + "-backup-timer-" + backupTimer.getAppId() + "-" + CloudAppConstant.BackupType.full, "").replace(",", "");
                } catch (SchedulerException e) {
                    throw new CustomException(500, "修改定时备份失败！删除全备失败！" + e.getMessage());
                }
            }
            if(!isCumFirstWeek){
                //当前存在增备，新增
                commitBackupTimer(backupTimer, dbType, cumCron, CloudAppConstant.BackupType.incre, kubeConfig.getName());
                //添加增备triggerName
                if(StringUtils.isEmpty(triggerName)){
                    triggerName += dbType + "-backup-timer-" + backupTimer.getAppId() + "-" + CloudAppConstant.BackupType.incre;
                }else{
                    triggerName += "," + dbType + "-backup-timer-" + backupTimer.getAppId() + "-" + CloudAppConstant.BackupType.incre;
                }
            }
        }else if(triggerName.indexOf("incre") != -1){
            //原来只存在增备
            if(!isFullFirstWeek){
                //当前存在全备，新增
                commitBackupTimer(backupTimer, dbType, fullCron, CloudAppConstant.BackupType.full, kubeConfig.getName());
                //添加增备triggerName
                triggerName = dbType + "-backup-timer-" + backupTimer.getAppId() + "-" + CloudAppConstant.BackupType.full + "," + triggerName;
            }
            //获取增备triggerName
            String increTriggerName = triggerName;
            if(triggerName.indexOf(",") != -1){
                increTriggerName = triggerName.split(",")[1];
            }
            if(!isCumFirstWeek){
                //当前也存在增备，修改
                try {
                    //修改trigger
                    updateTrigger(increTriggerName, triggerGroup, cumCron);
                    //启停修改
                    updateTrigger(backupTimer.isTriggerState(), increTriggerName, triggerGroup);
                } catch (Exception e) {
                    throw new CustomException(500, "修改定时备份失败！修改增备失败！" + e.getMessage());
                }
            }else{
                //当前不存在增备，删除之前增备
                try {
                    triggerInfoService.delete(increTriggerName, triggerGroup);
                    //删除增备triggerName
                    triggerName = triggerName.replace(dbType + "-backup-timer-" + backupTimer.getAppId() + "-" + CloudAppConstant.BackupType.incre, "").replace(",", "");
                } catch (SchedulerException e) {
                    throw new CustomException(500, "修改定时备份失败！删除全备失败！" + e.getMessage());
                }
            }
        }else{
            throw new CustomException(500, "获取定时备份不正常！");
        }

        backupTimer.setTriggerName(triggerName);
        backupTimer.setUpdateTime(LocalDateTime.now());
        backupTimer.setUpdateUser(UserUtil.getCurrentUser().getName());

        // 5.修改定时备份历史
        backupTimerMapper.updateBackupTimer(UserUtil.getSchema(), CLOUD_BACKUP_TIMER, backupTimer);
    }

    /**
     * 获取星期对应的cron表达式
     * @param weekDay
     * @return
     */
    private String getCron(String weekDay){
        switch (weekDay) {
            case CloudAppConstant.WeekDay.MONDAY:
                return "2";
            case CloudAppConstant.WeekDay.TUESDAY:
                return  "3";
            case CloudAppConstant.WeekDay.WEDNESDAY:
                return  "4";
            case CloudAppConstant.WeekDay.THURSDAY:
                return  "5";
            case CloudAppConstant.WeekDay.FRIDAY:
                return  "6";
            case CloudAppConstant.WeekDay.SATURDAY:
                return  "7";
            case CloudAppConstant.WeekDay.SUNDAY:
                return  "1";
            default:
                return "";
        }
    }

    /**
     * 提交定时备份
     * @param backupTimer
     * @param dbType
     * @param cron
     * @param backupType
     * @param kubeName
     * @return
     */
    private String commitBackupTimer(BackupTimer backupTimer, String dbType, String cron, String backupType, String kubeName){
        //提交到任务到任务调度,通过group和name标识
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("url", ApiConstant.BACKUP_TIMER_URL);
        dataMap.put("appId", String.valueOf(backupTimer.getAppId()));
        dataMap.put("appName", String.valueOf(backupTimer.getAppName()));
        dataMap.put("appType", backupTimer.getAppType());
        dataMap.put("backupType", backupType);
        dataMap.put("dbType", dbType);
        dataMap.put(JOB_DATA_KEY_CLASS, "BackupJob.class");
        dataMap.put("USER_INFO", JsonUtil.toJson(UserUtil.getCurrentUser()));
        dataMap.put("backupSetRetention", String.valueOf(backupTimer.getBackupSetRetention()));
        //根据类型判断插入属性
        if (CloudAppConstant.Kind.MYSQL.equalsIgnoreCase(dbType) || AppKind.Dameng.getKind().equalsIgnoreCase(dbType)) {
            dataMap.put("backupDbLog", String.valueOf(true));
            dataMap.put("backupLogRetention", String.valueOf(backupTimer.getBackupLogRetention()));
        } else {
            dataMap.put("backupDbLog", "false");

            backupTimer.setBackupDbLog(false);
            backupTimer.setBackupLogRetention(null);
        }
        dataMap.put("maxBackupDuration", String.valueOf(backupTimer.getMaxBackupDuration()));
        CronTriggerMeta cronTriggerMeta = new CronTriggerMeta() {{
            setCron(cron);
            setDescription("定时备份-" + backupTimer.getAppId() + "-" + backupTimer.getAppName());
            setJobId(2);
            setMisfire(2);
            setPriority(0);
            setTriggerGroup("backup-timer");
            setTriggerName(dbType + "-backup-timer-" + backupTimer.getAppId() + "-" + backupType);
            setJobDataMap(dataMap);
        }};
        try {
            //创建trigger
            triggerInfoService.add(cronTriggerMeta, false);

            //定时备份插入trigger的name和group
            backupTimer.setCron(cron);
            backupTimer.setBackupType(backupType);
            backupTimer.setKubeName(kubeName);
//            backupTimer.setTriggerState(true);
            backupTimer.setCreateTime(LocalDateTime.now());
            backupTimer.setCreateUser(UserUtil.getCurrentUser().getName());
            backupTimer.setTriggerGroup("backup-timer");
            backupTimer.setTriggerName(dbType + "-backup-timer-" + backupTimer.getAppId() + "-" + backupType);

            //根据状态对trigger进行关闭
            if(!backupTimer.isTriggerState()){
                try {
                    scheduler.pauseTrigger(TriggerKey.triggerKey(dbType + "-backup-timer-" + backupTimer.getAppId() + "-" + backupType,"backup-timer"));
                } catch (SchedulerException e) {
                    log.error("提交暂停定时备份失败！信息为：" + e.getMessage());
                    throw new CustomException(600, "提交暂停定时备份失败！");
                }
            }
        } catch (Exception e) {
            if (e.getMessage().contains("will never fire")) {
                throw new CustomException(600, "执行时间在定时周期内无法至少触发一次备份!");
            }
            throw new CustomException(600, "添加trigger失败!" + e.getMessage());
        }
        return dbType + "-backup-timer-" + backupTimer.getAppId() + "-" + backupType;
    }

    private void updateBinlogBackup(CloudApp app, BinlogBackupHis binlogBackupHis, String status, String message){
        binlogBackupHis.setStatus(status);
        binlogBackupHis.setEndTime(Timestamp.valueOf(LocalDateTime.now()));
        String msg = "Y";
        if("0".equalsIgnoreCase(status) || "1".equalsIgnoreCase(status)){
            msg = message;
        }
        binlogBackupHis.setMsg(msg);
        //备份最终目录实例：/hdTest/backup/rongqiyun/mysql-mysqlhd-935-172x16x21x200/mysql/binlog， /hdTest/backup是sys_config表中配置的路径，后面则为/namespace/podName/containerName/固定值binlog
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String nowStr = sdf.format(Timestamp.valueOf(LocalDateTime.now()));
        binlogBackupHis.setBackupDir(basePath + "/" + CloudAppConstant.MYSQL_LOCAL_PATH + "/" + app.getNamespace() + "/" + app.getCrName() + "/binlog/" + nowStr);
        backupMapper.updateBinlogBackupHis(SCHEMA, DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, binlogBackupHis);

    }

    private void insertBinlogBackup(CloudApp app, BinlogBackupHis binlogBackupHis, String status){
        binlogBackupHis.setStartTime(Timestamp.valueOf(LocalDateTime.now()));
        binlogBackupHis.setClusterId(app.getId());
        binlogBackupHis.setClusterName(app.getName());
        binlogBackupHis.setStatus(status);
        String msg = "Y";
        if("0".equalsIgnoreCase(status) || "1".equalsIgnoreCase(status)){
            msg = "N";
        }
        binlogBackupHis.setMsg(msg);
        backupMapper.insertBinlogBackupHis(SCHEMA, DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, binlogBackupHis);

    }

    /**
     * 修改trigger
     * @param triggerName
     * @param triggerGroup
     * @param cron
     */
    public void updateTrigger(String triggerName, String triggerGroup, String cron){
        // 1.获取TriggerKey
        TriggerKey triggerKey = TriggerKey.triggerKey(triggerName, triggerGroup);
        // 2.获取CronTrigger
        Trigger trigger = null;
        try {
            trigger = scheduler.getTrigger(triggerKey);
        } catch (SchedulerException e) {
            throw new CustomException(500, "获取trigger失败！信息为：" + e.getMessage());
        }
        CronTrigger cronTrigger = (CronTrigger) trigger;
        if(Objects.isNull(cronTrigger)){
            throw new CustomException(500, "未获取到cronTrigger！");
        }
        // 3.获取执行器
        CronScheduleBuilder cronScheduleBuilder = CronScheduleBuilder.cronSchedule(cron).withMisfireHandlingInstructionDoNothing();
        // 4.关联新的执行器
        CronTrigger newCronTrigger = cronTrigger.getTriggerBuilder()
                .withIdentity(triggerKey)
                .withSchedule(cronScheduleBuilder)
                .build();
        // 5.重制job
        try {
            scheduler.rescheduleJob(triggerKey, newCronTrigger);
        } catch (SchedulerException e) {
            throw new CustomException(500, "重制作业失败！信息为：" + e.getMessage());
        }
    }

    /**
     * 构建cron
     * @param backupTimer
     * @return
     */
    private Map<String, Object> createCron(BackupTimer backupTimer, boolean isFullFirstWeek, boolean isCumFirstWeek){
        //解析定时备份周期字符串为cron
        //执行时间
        String cron = backupTimer.getCron();
        String[] crons = cron.split(" ");
        String timeCron = "";
        for (int j = 0; j < 3; j++) {
            timeCron += crons[j] + " ";
        }
        //全备、增备cron
        String fullCron = timeCron + "? *", cumCron = timeCron + "? *";
        JSONArray scheduleRepeatArr = JSONArray.parseArray(backupTimer.getScheduleRepeat());
        //创建变量判断是否为第一次拼接星期，如果是，则添加一个空格，遍历星期字符串
        for (int i = 0; i < scheduleRepeatArr.size(); i++) {
            //先判断类型
            if(CloudAppConstant.BackupType.full.equals(scheduleRepeatArr.getJSONObject(i).getString("type"))){
                //全备
                String weekDayCron = getCron(scheduleRepeatArr.getJSONObject(i).getString("day"));
                if(isFullFirstWeek){
                    fullCron += " " + weekDayCron + ",";
                    isFullFirstWeek = false;
                }else{
                    fullCron += weekDayCron + ",";
                }
            }else if(CloudAppConstant.BackupType.cum.equals(scheduleRepeatArr.getJSONObject(i).getString("type"))){
                //增备
                String weekDayCron = getCron(scheduleRepeatArr.getJSONObject(i).getString("day"));
                if(isCumFirstWeek){
                    cumCron += " " + weekDayCron + ",";
                    isCumFirstWeek = false;
                }else{
                    cumCron += weekDayCron + ",";
                }
            }
        }
        //处理cron
        if(!isFullFirstWeek){
            fullCron = fullCron.substring(0, fullCron.length() - 1);
        }
        if(!isCumFirstWeek){
            cumCron = cumCron.substring(0, cumCron.length() - 1);
        }
        Map<String, Object> resMap = new HashMap<>();
        resMap.put(CloudAppConstant.BackupType.full, fullCron);
        resMap.put(CloudAppConstant.BackupType.incre, cumCron);
        resMap.put("isFullFirstWeek", isFullFirstWeek);
        resMap.put("isCumFirstWeek", isCumFirstWeek);
        return resMap;
    }

    /**
     * 提交定时清理备份文件
     */
    public void commitCleanTimer() {
        //提交到任务到任务调度,通过group和name标识
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("url", "http://cloud-service/cloudnormal/backup/timer/cleanBackupData");
        dataMap.put("username", "admin");
        dataMap.put("USER_INFO", JsonUtil.toJson(UserUtil.getCurrentUser()));
        CronTriggerMeta cronTriggerMeta = new CronTriggerMeta() {{
            setCron("0 0 0 * * ?");
            setDescription("容器云定时清理备份文件");
            setJobId(2);
            setMisfire(1);
            setPriority(0);
            setTriggerGroup("cloud-clean");
            setTriggerName("cloud-clean-backup");
            setJobDataMap(dataMap);
        }};
        try {
            //创建trigger
            triggerInfoService.add(cronTriggerMeta, false);
        } catch (Exception e) {
            if (e.getMessage().contains("will never fire")) {
                throw new CustomException(600, "执行时间在定时周期内无法至少触发一次备份!");
            }
            throw new CustomException(600, "添加trigger失败!" + e.getMessage());
        }
    }

    @Override
    public List<BackupTimer> listMap(Map<String, Object> condition) {
        return backupTimerMapper.listByMap(SCHEMA, CLOUD_BACKUP_TIMER, condition);
    }

    /**
     * 检查是否可以删除备份历史
     *
     * @param appIdAndBaseTimeMap
     * @param app
     * @param sdfCommon
     * @param currentTimeMillis
     * @param backupSetRetention
     * @param backupHis
     * @return
     */
    private boolean checkDelBackupHis(Map<Integer, String> appIdAndBaseTimeMap, CloudApp app, SimpleDateFormat sdfCommon, long currentTimeMillis, Integer backupSetRetention, BackupHis backupHis) {
        //如果map中没有保存对应app信息，则获取删除时间线之前最近的一次全备
        if (!appIdAndBaseTimeMap.containsKey(app.getId())) {
            //最后的时间线
            String deleteTime = sdfCommon.format(new Date(currentTimeMillis - 1000 * 60 * 60 * 24 * backupSetRetention));
            BackupHis fullBackupHisBeforeDeleteTime = backupMapper.getFullBackupHisBeforeDeleteTime(SCHEMA, CLOUD_BACKUP_HIS, deleteTime, backupHis.getAppId());
            if (ObjectUtils.isEmpty(fullBackupHisBeforeDeleteTime)) {
                return false;
            }
            //记录在map中
            appIdAndBaseTimeMap.put(fullBackupHisBeforeDeleteTime.getAppId(), fullBackupHisBeforeDeleteTime.getBaseTime());
        }
        //判断当前备份历史是否可以删除
        String curBaseTime = appIdAndBaseTimeMap.get(app.getId());
        if (curBaseTime.equalsIgnoreCase(backupHis.getBaseTime())) {
            //与删除时间线前最后一次全备属于同一组的备份数据，不删除
            return false;
        } else {
            return true;
        }
    }
}
