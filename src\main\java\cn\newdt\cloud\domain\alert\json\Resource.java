/* Copyright 2024 freecodeformat.com */
package cn.newdt.cloud.domain.alert.json;

import cn.newdt.cloud.domain.alert.AlertRuleResource;

import static cn.newdt.cloud.service.alert.AlertConfigSyncService.SEPARATOR;

public class Resource {

    private String name;
    /**
     * job has a prefix over name, e.g. for a redis named r1, job is rc-r1;
     */
    private String job;
    /**
     * type = kind[separator]arch
     */
    private String type;
    private String kind;
    private String arch;
    private String namespace;
    private Integer kubeId;
    private Integer appId;
    /**
     * resource to build rule on
     */
    private int logicAppId;
    /**
     * resource to group the alerts from different app
      */
    private Integer primaryLogicId;
    private Integer tenantId;
    private Integer userId;

    public Resource copyFrom(AlertRuleResource i) {
        Resource resource = new Resource();
        resource.setName(i.getResourceName());
        resource.setNamespace(i.getResourceNamespace());
        resource.setType(i.getResourceType());
        resource.setLogicAppId(i.getResourceId());
        resource.setPrimaryLogicId(i.getPrimaryResourceId());
        resource.setKubeId(i.getKubeId());
        return resource;
    }

    public Resource copyFrom(Resource source) {
        if (source == null) {
            return this;  // Return current instance unchanged if source is null
        }

        // Copy all fields from source to this instance
        this.name = source.name;
        this.job = source.job;
        this.type = source.type;
        this.kind = source.kind;
        this.arch = source.arch;
        this.namespace = source.namespace;

        // Handle Integer fields (which can be null)
        this.kubeId = source.kubeId;
        this.appId = source.appId;
        this.primaryLogicId = source.primaryLogicId;
        this.tenantId = source.tenantId;

        // Handle primitive int field
        this.logicAppId = source.logicAppId;

        return this;  // Return this instance for method chaining if desired
    }

    public void setName(String name) {
         this.name = name;
     }
     public String getName() {
         return name;
     }

    public void setType(String type) {
         this.type = type;
     }
     public String getType() {
         return type;
     }

    public void setNamespace(String namespace) {
         this.namespace = namespace;
     }
     public String getNamespace() {
         return namespace;
     }

    public Integer getKubeId() {
        return kubeId;
    }

    public void setKubeId(Integer kubeId) {
        this.kubeId = kubeId;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    public String getArch() {
        return arch;
    }

    public void setArch(String arch) {
        this.arch = arch;
    }

    public int getLogicAppId() {
        return logicAppId;
    }

    public void setLogicAppId(int logicAppId) {
        this.logicAppId = logicAppId;
    }

    public void parseTypeToAppKind() {
        String[] type_arch = type.split(SEPARATOR);
        String kind = type_arch[0];
        String arch = null;
        if (type_arch.length == 2)
            arch = type_arch[1];
        this.kind = kind;
        this.arch = arch;
    }

    public Integer getTenantId() {
        return tenantId;
    }

    public void setTenantId(Integer tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getPrimaryLogicId() {
        return primaryLogicId;
    }

    public void setPrimaryLogicId(Integer primaryLogicId) {
        this.primaryLogicId = primaryLogicId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }
}