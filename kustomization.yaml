apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
#- deploy/cloud-nfs-pvc.yaml
# - deploy/namespace.yaml
resources:
- deploy/dbpaas-cloud-service-svc.yaml
- deploy/dbpaas-cloud-service-dep.yaml
#- deploy/cloud-service-db.yaml
namespace: cloud-dev
#nameSuffix: -custom-suffix
commonLabels:
  branch: develop
images:
- name: HARBOR_URL_PLACE_HOLDER/shindatacloud/cloud-service
  newName: harbor.shindata.com/shindatacloud/cloud-service
  newTag: 8afc07e1
configMapGenerator:
- behavior: create
  files:
  - config/logback-spring.xml
  - src/main/resources/bootstrap.properties
  - src/main/resources/application.yml
  name: dbpaas-cloud-service-configmap
nameSuffix: -develop
