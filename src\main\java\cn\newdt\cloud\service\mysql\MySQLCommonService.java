package cn.newdt.cloud.service.mysql;

import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.ImageKindEnum;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.CloudAppConfig;
import cn.newdt.cloud.domain.CloudAppLogic;
//import com.shindata.cloud.v1.MySQL;
import cn.newdt.cloud.domain.KubeScheduler;
import cn.newdt.cloud.service.DamengService;
import cn.newdt.cloud.service.ResourceHelper;
import cn.newdt.cloud.vo.GenericSpec;
import com.google.common.collect.ImmutableList;
import com.shindata.cloud.v1.MySQLSpec;
import com.shindata.common.spec.Entries;
import io.fabric8.kubernetes.api.model.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * implement mysql Addons, by ---
 * manage mysql app on k8s and cr controlled resource
 * implement provided operations : scale(horizontal&vertical), upgrade(version), start&stop, switchover, backup&restore
 * definition of : scripts, pod template
 * integrate with platform's ability : ops rollback
 */
public class MySQLCommonService {

    public void apply(OperationOptionContext opts) {
        CloudAppLogic logicApp = opts.getLogicApp();
        CloudAppConfig appConfig = opts.getAppConfig();
        CloudApp app = opts.getApp();
        String namespace = app.getNamespace();
        String crName = app.getCrName();

        com.shindata.cloud.v1.MySQL cr = new com.shindata.cloud.v1.MySQL();
        // implement common transformer
        cr.setMetadata(new ObjectMetaBuilder().withNamespace(namespace).withName(crName).build());

        MySQLSpec mysqlspec = new MySQLSpec();
        cr.setSpec(mysqlspec);
        List<Entries> entries = createEntries(opts);
        mysqlspec.setEntries(entries);
        mysqlspec.setArchType(typeMapper(app.getArch()));
        // configure clone
//        mysqlspec.setRestore(createClone());

//        mysqlspec.setSecrets(createSecrets());

        // configure drrole
        mysqlspec.setDisasterRecoveryRole(droleMapper(app.getRole()));
        mysqlspec.setPrimaryAddress(getPrimaryAddress());
    }

    // 每个运维操作仅修改部分属性
    public void patchSpec(OperationOptionContext ops) {
        // get current cr - select from cloud_app and then query apiserver.
        // because we need acquire lock for update
        com.shindata.cloud.v1.MySQL cr = (com.shindata.cloud.v1.MySQL) ops.getCr();

        GenericSpec opsRequestSpec = ops.getOpsRequestSpec();



    }

    private List<Entries> createEntries(OperationOptionContext opts) {
        Map<String, String> config = setupDbParamConfig(vo); // 包含默认参数，见paramformula

        GenericSpec opsRequestSpec = opts.getOpsRequestSpec();
        GenericSpec.ComponentList componentList = opsRequestSpec.getComponentList();
        CloudAppConfig appConfig = opts.getAppConfig();
        Map<ImageKindEnum, String> imageKindEnumStringMap = appConfig.parseImageTemplate();
        List<Entries> entries = new ArrayList<>();
        Entries database = new Entries();
        GenericSpec.ComponentSpec dbComponent = componentList.namedComponent("data");
        KubeScheduler kubeScheduler = dbComponent.getKubeScheduler();
        List<Toleration> k8sTolerance = kubeScheduler.toK8sTolerance();
        Affinity k8sAffinity = kubeScheduler.toK8sAffinity(appVO);

        database.setName(dbComponent.getName());
        database.setImage(imageKindEnumStringMap.get(ImageKindEnum.MainImage));
        database.setReplicas(dbComponent.getReplicas());
        database.setConfig(config);
        database.setPodTemplate(new PodBuilder()
                .withNewSpec()
                .withServiceAccountName(serviceAccount)
                .withAffinity(k8sAffinity)
                .withTolerations(k8sTolerance)
                .withContainers(
                        // main container
                        new ContainerBuilder()
                                .withName(getKind().getContainerName())
                                .withResources(ResourceHelper.getInstance().resourceRequirements(dbComponent.getLimits()))
                                .withVolumeMounts(
                                        new VolumeMountBuilder().withName("log-volume").withMountPath("/opt/dmdbms/log")
                                                .build(),
                                        new VolumeMountBuilder().withName("timezone").withMountPath("/etc/localtime")
                                                .build()
                                )
                                .build(),
                        // mount container. torefact
                        new ContainerBuilder()
                                .withName("mount")
                                .withImage(imageKindEnumStringMap.get(ImageKindEnum.Mount))
                                .build(),
                        // filebeat
                        getFilebeatContainer(imageKindEnumStringMap.get(ImageKindEnum.Filebeat)),
                        // exporter
                        new ContainerBuilder()
                                .withName("exporter")
                                .withImage(imageKindEnumStringMap.get(ImageKindEnum.Exporter))
                                .withPorts(new ContainerPortBuilder().withName(DamengService.Name.exporterPortName).withContainerPort(9200).build())
                                .build()
                )
                .withVolumes(ImmutableList.of(
                        new VolumeBuilder().withName("dameng-filebeat").withConfigMap(new ConfigMapVolumeSourceBuilder()
                                .withName(DamengService.Name.DAMENG_FILEBEAT).build()).build(),
                        new VolumeBuilder().withName("log-volume").withEmptyDir(new EmptyDirVolumeSource()).build(),
                        new VolumeBuilder().withName(CloudAppConstant.Volumes.TIMEZONE_NAME).withNewHostPath(CloudAppConstant.Volumes.TIMEZONE_HOSTPATH, "").build()
                ))
                .endSpec()
                .build());
        database.setVolumeTemplates(dbComponent.getStorage().toPVC(resourceVersion));

        return null;
    }

    private String getPrimaryAddress() {
        return null;
    }

    private MySQLSpec.DisasterRecoveryRole droleMapper(String role) {
        switch (role.toLowerCase()) {
            case "primary": return MySQLSpec.DisasterRecoveryRole.PRIMARY;
            case "slave":
                return MySQLSpec.DisasterRecoveryRole.STANDBY;
            case "standalone":
                return MySQLSpec.DisasterRecoveryRole.STANDALONE;
            default:
                throw new IllegalArgumentException("not a valid drole value-" + role);
        }

    }

    private MySQLSpec.ArchType typeMapper(String arch) {
        switch (arch.toLowerCase()) {
            case "ha": return MySQLSpec.ArchType.HA;
            case "mgr":
                return MySQLSpec.ArchType.MGR;
            default:
                throw new IllegalArgumentException("not a valid arch value-" + arch);
        }
    }

    /**
     * load and register the app definition
      */
    public void registerDefinition() {

    }

}
