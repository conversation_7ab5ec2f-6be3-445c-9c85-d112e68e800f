package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.cr.MongoDBCommunity;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
public class MongoDbInstallWatch extends MongodbResourceWatch implements OpsPostProcessor<MongoDBCommunity> {

    @Override
    void doStopWatchResource(CloudApp app, TriggerHis triggerHis, boolean successful, OpsResultDTO build) {
        if (successful) {
            mongoDbService.updatePvAffinity(app);
            serviceManageOperationWatcherHelper.doStopWatchSvc(app, triggerHis, successful, null);
            if (autoManagement) {
                build.setMsg(operationUtil.syncToDMP(app, triggerHis));
            }
            appMultiAZService.enableAlert(app);
        }
    }
}
