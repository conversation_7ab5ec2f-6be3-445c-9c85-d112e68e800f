package cn.newdt.cloud.service.alert;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.domain.KubeConfig;
import cn.newdt.cloud.domain.alert.json.Resource;
import cn.newdt.cloud.domain.alert.json.RuleSet;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.repository.impl.FabricKubeClientFactory;
import cn.newdt.cloud.utils.JsonUtil;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import cn.newdt.cloud.domain.alert.AlertConfig;
import org.mockito.Mock;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class AlertConfigSyncServiceTest {
    @Mock
    KubeClient client;

    @Before
    public void setUp() throws Exception {
        KubeConfig kubeConfig = new KubeConfig();
        kubeConfig.setConfig("apiVersion: v1\n" +
                "clusters:\n" +
                "- cluster:\n" +
                "    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUM1ekNDQWMrZ0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRc0ZBREFWTVJNd0VRWURWUVFERXdwcmRXSmwKY201bGRHVnpNQjRYRFRJek1URXlOREF6TkRjek1Gb1hEVE16TVRFeU1UQXpORGN6TUZvd0ZURVRNQkVHQTFVRQpBeE1LYTNWaVpYSnVaWFJsY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCQlFBRGdnRVBBRENDQVFvQ2dnRUJBTUlFClByVTZ2Wk14MGx5QkVLMzBadTdPNlB0RlR1cHNLN3ltQm5SNDk4TmQrWXlpNDBGUEVJSTR0WHM0VGRwWHFLTVoKZUxkRjdERzM2cE1kMHZZRG91NXpGblJKaUwrakJwMGZ6L0wvNGMrVzkwd1ZpWExhQUF2TXRmeDMzdUV1bURCaQpVNktCQVRuL3NsWFkwcVhaZ0ZmSnQrZG1mUFVnWmt4UlRNZUUwOGhudW1RdXVGYmRmY25uSzNlZ284M1ZBaEhhCjNubFNTb0ViVGlMRU9ZLzMzdTlWZTd1TXhQNXJXQ3V6NDE0QlNmNDlrT2dSdXdZRDk5R01URkxtRXp0TXduNVUKemllVFRPamtkTW80b3VPdkZNTDRxTEhScjUrajBOanZQMmJ3K2l4U0JoWHZtdXcwWFc4cGliZStmVXdWOVpudwpselVJZEx3NkZSM0E5TkFYSVZzQ0F3RUFBYU5DTUVBd0RnWURWUjBQQVFIL0JBUURBZ0trTUE4R0ExVWRFd0VCCi93UUZNQU1CQWY4d0hRWURWUjBPQkJZRUZOQm9raXhMRCtoNDFiWTk0VFNwVHJ3bWZKUlhNQTBHQ1NxR1NJYjMKRFFFQkN3VUFBNElCQVFDaUc3YW9kQmh6ZVNvNm5BWkR3Wm1mSU9zQ0ZLL1N5RFJkcjdRalN2Q1NtRGF4QTBOSwo3emtBWWFVNUM3cCszWUpMaDhMWjVhUDFCMDBnZXNDSHk2bjNCYXhYaTUrNW1VYmVJVEZ1eUg5NzNINVRhTGxPClpzWHNpUFpKdlJ3d0JvUjVEYXVSVkRhRlN0eWRFTlBlTWFXb1RBc1U5N1hXVGhLZnBzUG1aa04wNlNwMEkzV2gKbm9nT0pjelJScEFsdUpFQWhrM3hNVDhLczhEcUdYNzVOQTAvUW9GRytFMVVNR2EvQk5qckVTbG1uTkpYRUhsaApFa043UjJNREkra05rSmVPcGo1MTdQRHc4ZzZnMHVUZXY3THZveFlzQnNIYnZjK3JISVFCcnY1QndKY1dPUk82CnJRendvVHVEc0JqV3FhSldZd2F4L1dnTlJSR0s2Y1VIdjRtSQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==\n" +
                "    server: https://*************:6443\n" +
                "  name: kubernetes\n" +
                "contexts:\n" +
                "- context:\n" +
                "    cluster: kubernetes\n" +
                "    user: kubernetes-admin\n" +
                "  name: kubernetes-admin@kubernetes\n" +
                "current-context: kubernetes-admin@kubernetes\n" +
                "kind: Config\n" +
                "preferences: {}\n" +
                "users:\n" +
                "- name: kubernetes-admin\n" +
                "  user:\n" +
                "    client-certificate-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURFekNDQWZ1Z0F3SUJBZ0lJS3NkREhEc0hYM2N3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TXpFeE1qUXdNelEzTXpCYUZ3MHlOREV4TWpNd016UTNNekphTURReApGekFWQmdOVkJBb1REbk41YzNSbGJUcHRZWE4wWlhKek1Sa3dGd1lEVlFRREV4QnJkV0psY201bGRHVnpMV0ZrCmJXbHVNSUlCSWpBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVE4QU1JSUJDZ0tDQVFFQXdiTGg4TEx0WTRWYWwxclQKZlVId00zOGdnYmZqdFQxZGtqaVd3Q2svQkVvdk5qQTlWaWF1dWplRjJqZnRPYWFaVUZCc3FqelJnUGQrTm1HbgpDYVJhM0lyUGtBU21uNktsemlsU1hvRWdFK3RRbkYzSEhDZHBnaU05cFNEOTBXQUVBay9VZFAyOWh4SHU0cmlyCjZuVGRRWFdmbDQ2R1IyOVNuQ1dPUGN3L242S3ZCcExQeHN6SnkzOWc0VjBFOURXQ25pbHJ4UGYrc2lXVEJudDEKTWFqVXlVR3g5dUEyVzFOUnhGN2xxU0tRbTJub2grT0hlQ25IMUpTZERWTVhKd1dOSzVBZ0lVRWUyQ2pXTFpzRwpFRUV1eGRoaXhIdGZNa2Fmc0hlUk9zZFk2dmVuWk1HSHU3aldGS21ub2QzK2lldlkwTDhSMG5LOTltY3hNVG1PCmlhc01jd0lEQVFBQm8wZ3dSakFPQmdOVkhROEJBZjhFQkFNQ0JhQXdFd1lEVlIwbEJBd3dDZ1lJS3dZQkJRVUgKQXdJd0h3WURWUjBqQkJnd0ZvQVUwR2lTTEVzUDZIalZ0ajNoTktsT3ZDWjhsRmN3RFFZSktvWklodmNOQVFFTApCUUFEZ2dFQkFEcmdiQk1xVnJXMGgwQ2pJL3VYcll5U0doTm90cjUzMmkvMnlFV0lYVnVITmFKM1RXbVRnYlFGCmRybDdTQUhaamFwQ2pLaGtQdzFTYnlzZEFMc0xROSs5ZU1JNXZ2N0pOSG1Nc21mdjhBZ1JodVRiMHZuSG5WYUgKUUdEamZJRVdCSzZiOUlCamdweGFjRi85TUwrU3llWU5Ic1F6Kyt4RHJOWlUxU3NjbHRmNlAwbzNhWWduTFMvegpIUXdyTzlLTkNXdkdKYlR2Uy9zU1Y5T2tuQ3NWaU9GM3lqUDkvRlkrZzEvckhjKzF1eTlubHpPcDk3Q2hTeHZ3CkFVdjVVUDNlT3hvUk5sbUgwQ3lwQzRlMUxTdEFTNVExR3l5bXdZajhRTU41WmdrelN5WnBsdEh6aHo4RVRIVGUKbEU4OWZCT3ZMYXFsTGF5bUFRZ2o2UGxnalhKTklXWT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=\n" +
                "    client-key-data: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
        client = new FabricKubeClientFactory().createClient(kubeConfig);
        // init front webhook receiver

    }

    List<String> namespace_name_created = new ArrayList<>();

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void customRuleAtMultipleNamespaceAndName() {
        // 1st config
        String yaml = "{\n" +
                "    \"ruleSetName\": \"rds_redis\",\n" +
                "    \"resources\":\n" +
                "    [\n" +
                "        {\n" +
                "            \"name\": \"rc-rc\",\n" +
                "            \"type\": \"Redis\",\n" +
                "            \"namespace\": \"ydev\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"rules\":\n" +
                "    [\n" +
                "        {\n" +
                "            \"metricName\": \"redis_memory_usage\",\n" +
                "            \"escalations\":\n" +
                "            [\n" +
                "                {\n" +
                "                    \"severity\": \"p1\",\n" +
                "                    \"threshold\": 2609448,\n" +
                "                    \"times\": 2,\n" +
                "                    \"operator\": \"gte\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"severity\": \"p2\",\n" +
                "                    \"threshold\": 2097152,\n" +
                "                    \"times\": 2,\n" +
                "                    \"operator\": \"gte\"\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ],\n" +
                "    \"notifyStrategies\":\n" +
                "    {\n" +
                "        \"intervalTime\": 60,\n" +
                "        \"repeatIntervalTime\": 86400,\n" +
                "        \"effectiveIntervalTime\": \"\",\n" +
                "        \"channels\":\n" +
                "        [\n" +
                "            {\n" +
                "                \"channel\": \"email\",\n" +
                "                \"options\":\n" +
                "                {},\n" +
                "                \"concats\":\n" +
                "                [\n" +
                "                    {\n" +
                "                        \"id\": \"\",\n" +
                "                        \"groupId\": 1\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"default\": true\n" +
                "            }\n" +
                "        ],\n" +
                "        \"escalationStrategies\":\n" +
                "        [\n" +
                "            {\n" +
                "                \"severity\": \"p1\",\n" +
                "                \"channel\": \"email\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";

        RuleSet ruleSet = YamlEngine.unmarshal(yaml, RuleSet.class);
        AlertConfig alertConfig = new AlertConfig();
        alertConfig.setRuleSet(ruleSet);
        new AlertConfigSyncService().syncConfig(alertConfig, client);
    }

    @Test
    public void addFilter() {
        AlertConfigSyncService alertConfigSyncService = new AlertConfigSyncService();
        String s = alertConfigSyncService.addFilter("redis_memory_usage", "namespace", "ydev");
        s = alertConfigSyncService.addFilter(s, "job", "rc1");
        System.out.println(s);
        s = alertConfigSyncService.addFilter("redis_memory_usage{ node = \"jkiq\", p =~ \"jl\" }", "namespace", "ydev");
        System.out.println(s);
    }

    @Test
    public void test() {
        String json = "{\"ruleSetName\":\"rds_redis\",\"resources\":null,\"rules\":[{\"metricName\":\"redis_memory_usage_percent\",\"escalations\":[{\"severity\":\"p1\",\"threshold\":70,\"times\":1,\"operator\":\"gte\"},{\"severity\":\"p2\",\"threshold\":50,\"times\":1,\"operator\":\"gte\"}]}],\"notifyStrategy\":{\"intervalTime\":60,\"repeatIntervalTime\":300,\"effectiveIntervalTime\":\"\",\"channels\":[{\"name\":null,\"channel\":\"email\",\"options\":{},\"contacts\":[{\"id\":null,\"groupId\":1,\"password\":null,\"deptname\":null,\"nickname\":null,\"autho\":null,\"userid\":null,\"wechat\":null,\"ewechat\":null,\"defaultpwd\":null,\"phone\":null,\"dingtalk\":null,\"dblist\":null,\"bdblist\":null,\"email\":null,\"name\":null}],\"default\":true}],\"escalationStrategies\":[{\"severity\":\"p1\",\"channel\":\"email\"}]}}";
        RuleSet object = JsonUtil.toObject(RuleSet.class, json);
        System.out.println();
    }

    @Test
    public void expr() {
        final String origin = "((kubelet_volume_stats_capacity_bytes / on(namespace, persistentvolumeclaim) (kube_pod_spec_volumes_persistentvolumeclaims_info{pod=~\"#{pod}\", namespace=\"#{namespace}\"} / on(namespace, persistentvolumeclaim) kube_persistentvolumeclaim_info {namespace=\"#{namespace}\", storageclass!~\"#{shared-sc}\"} ) ) - on (namespace, persistentvolumeclaim) (kubelet_volume_stats_used_bytes / on(namespace, persistentvolumeclaim) (kube_pod_spec_volumes_persistentvolumeclaims_info{pod=~\"#{pod}\", namespace=\"#{namespace}\"} / on(namespace, persistentvolumeclaim) kube_persistentvolumeclaim_info {namespace=\"#{namespace}\", storageclass!~\"#{shared-sc}\"} ) ) ) / 1024/1024";
        String exprTpl = origin;
        // 模板替换
        Map<String, String> dataMap = returnValuesMap();
        dataMap.put("shared-sc", getSharedScList());
        exprTpl = new StringSubstitutor(dataMap, "#{", "}")
                .replace(exprTpl);
        System.out.println(exprTpl);

        dataMap.put("shared-sc", null);
        exprTpl = origin;
        exprTpl = new StringSubstitutor(dataMap, "#{", "}")
                .replace(exprTpl);
        exprTpl = exprTpl.replaceAll("\\w+\\s*[=!~]+\\s*\"#\\{[\\w-]+}\",?", "");

        System.out.println(exprTpl);
    }

    private String getSharedScList() {
        return "nfs-share | nfs-client";
    }

    private Map<String, String> returnValuesMap() {
        Map<String, String> map = new HashMap<>();
        map.put("namespace", "test-ns");
            map.put("container", "test-container");
            map.put("pod", "test-pod");
            map.put("job", "test-job");
            map.put("crName", "test-name");
        return map;
    }
}