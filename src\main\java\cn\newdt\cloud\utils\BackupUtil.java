package cn.newdt.cloud.utils;

import cn.newdt.cloud.config.CloudRequestContext;
import cn.newdt.cloud.config.KubeExecSimpleListener;
import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.domain.cr.MongoDBCluster;
import cn.newdt.cloud.domain.dmp.BinlogBackupHis;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.mapper.*;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.impl.*;
import cn.newdt.cloud.service.sched.impl.*;
import cn.newdt.cloud.service.sched.impl.pg.PostgresqlBackupRestoreWatch;
import cn.newdt.cloud.vo.AppInstanceVO;
import cn.newdt.cloud.vo.BackupHisVO;
import cn.newdt.cloud.vo.CloudBackupStorageVO;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.bean.UserInfo;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.UserUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.pingcap.v1alpha1.TidbCluster;
import com.pingcap.v1alpha1.tidbclusterstatus.pd.Leader;
import com.shindata.clickhouse.v1.ClickHouseInstallation;
import com.shindata.cloud.v1.Vastbase;
import com.shindata.cloud.v1.VastbaseSpec;
import com.shindata.cloud.v1.vastbasespec.restore.Remote;
import com.shindata.cloud.dameng.v1.Dameng;
import com.shindata.mysql.v1.MySQLHA;
import com.shindata.mysql.v1.MySQLHASpec;
import com.shindata.opengauss.v1.OpenGaussCluster;
import com.shindata.opengauss.v1.OpenGaussClusterSpec;
import com.shindata.postgre.v1.PostgreSql;
import com.shindata.postgre.v1.postgresqlspec.Restore;
import com.shindata.redis.v1.Redis;
import com.shindata.redis.v1.RedisCluster;
import com.shindata.redis.v1.RedisClusterSpec;
import com.shindata.redis.v1.RedisSpec;
import com.shindata.redis.v1.redisclusterstatus.Shards;
import io.fabric8.kubernetes.api.model.Pod;
import io.fabric8.kubernetes.api.model.VolumeMount;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.fabric8.kubernetes.client.dsl.ExecWatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.elasticsearch.common.collect.Tuple;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.net.URLEncoder;
import java.nio.file.Paths;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.CloudAppConstant.*;
import static cn.newdt.cloud.constant.DatasourceConstant.*;

@Component
@Slf4j
public class BackupUtil {

    @Autowired
    private BackupMapper backupMapper;

    @Autowired
    private RestoreMapper restoreMapper;

    @Autowired
    private CloudAppService cloudAppService;

    @Autowired
    private MongoDbClusterService mongoDbClusterService;

    @Autowired
    private KubeClientService kubeClientService;

    @Autowired
    private CloudAppMapper cloudAppMapper;

    @Autowired
    private MysqlV2Service mysqlService;

    @Autowired
    private CloudAppService appCommonService;

    @Autowired
    private KubeConfigService kubeConfigService;

    @Autowired
    private AsyncUtil asyncUtil;

    @Autowired
    private KubeConfigMapper kubeConfigMapper;

    @Autowired
    private ResourceChangeHisService resourceChangeHisService;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private CloudFTPUtil ftpUtil;
    @Autowired
    private RedisPodService redisPodService;

    @Autowired
    private CloudBackupStorageMapper cloudBackupStorageMapper;

    @Autowired
    private BackupService backupService;

    @Value("${ftp.basePath:/tmp/}")
    private String basePath;

    @Value("${ftp.ip:*************}")
    private String ip;

    @Value("${ftp.userName:username}")
    private String username;

    @Value("${ftp.password:password}")
    private String password;

    @Autowired
    private BackupTimerMapper backupTimerMapper;

    @Autowired
    private ResourceManagerService resourceManagerService;

//    // todo make final
//    private String storeDir;

    /**
     * mysql备份 备份实际执行方法，包含备份历史保存和更新. <br>
     * 异步执行。执行成功后备份文件默认保存在 ${backupdir}/{port}. backupdir通过脚本参数-BACKUPDIR指定为backup volume的mountpath.
     * @param backupHis(appId、appName、appType、backupType、performType)
     * @param backupHis
     */
    @Async
    public void performBackup(BackupHisVO backupHis) {
        BackupHis runningBackupHis = backupMapper.getRunningByAppId(SCHEMA, CLOUD_BACKUP_HIS, backupHis.getAppId());
        if (runningBackupHis != null) {
            throw new CustomException(600, "存在正在执行的备份，请勿重复提交！");
        }
        String checkStorageResult = backupHis.getMessage();
        Integer appId = backupHis.getAppId();
        CloudApp cloudApp = cloudAppService.get(appId);
        //修改应用状态
        cloudApp.setStatus(CloudAppConstant.AppStatus.PENDING);
        cloudAppService.update(cloudApp);
        //记录备份信息：数据库名称、备份路径、备份文件名称
        JSONObject messageObj = new JSONObject();
        messageObj.put("backupIp","");
        messageObj.put("backupPath","");
        messageObj.put("msg","备份中...");
        //1. 保存备份基本信息
        log.info("[mysql备份]保存备份基本信息开始");
        Timestamp startTime = new Timestamp(System.currentTimeMillis());
        backupHis.setStartTime(startTime);
        backupHis.setStatus(StatusConstant.RUNNING);
        backupHis.setMessage(JSON.toJSONString(messageObj));
        //应用所属集群
        KubeConfig byId = kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, cloudApp.getKubeId());
        if(null == byId){
            log.error("未获取到集群！");
            backupReturn(backupHis, null, StatusConstant.FAIL, "", "未获取到集群！");
            return;
        }
        backupHis.setKubeName(byId.getName());
        backupMapper.commitBackup(SCHEMA, CLOUD_BACKUP_HIS, backupHis);
        //插入操作记录
        log.info("[mysql备份]保存备份操作记录开始");
        UserInfo userInfo = UserUtil.getAsyncUserinfo();
        ResourceChangeHis resourceChangeHis = new ResourceChangeHis(){{
            setInsertTime(startTime);
            setKind(cloudApp.getKind());
            setKubeId(cloudApp.getKubeId());
            setNamespace(cloudApp.getNamespace());
            setCommand("备份");
            setStatus("2");
            setAction("Backup");
            setMsg("备份中...".concat("\n").concat(null == checkStorageResult?"":checkStorageResult));
            setAppId(cloudApp.getId());
            setAppLogicId(cloudApp.getLogicAppId());
            setAppName(cloudApp.getName());
            setKubeName(byId.getName());
            setYaml(cloudApp.getCr());
            setUserName(userInfo.getUsername());
            setUserIp(CloudRequestContext.getContext().getUserIp());
            setLastEndTimestamp(System.currentTimeMillis());
        }};
        Integer changeId = insertResourceChangeHis(resourceChangeHis);

        KubeClient client = kubeClientService.get(cloudApp.getKubeId());
        //2. 最终执行备份的库
        //所有实例
        log.info("[mysql备份]获取最终执行的主库");
        //查询主库的pod名称，执行binlog备份
        PodDTO podDTO = getBackupPodMySQL(cloudApp, client);
        if (podDTO == null) {
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "没有找到备份实例！");
            return;
        }
        String primaryPodName = podDTO.getPodName();
        log.info("[mysql备份]最终执行备份的pod名称为：" + primaryPodName);
        backupHis.setPodName(primaryPodName);

        // 3.执行备份 使用默认用户名密码：k8sadmin/k8sadmin
        log.info("[mysql备份]备份类型(全备或增备)为：" + backupHis.getBackupType());
        //获取备份存储信息
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        if ("incre".equals(backupHis.getBackupType())) {
            BackupHis fullBackupHis = backupMapper
                    .getLastSuccessFullBackupHis(SCHEMA, CLOUD_BACKUP_HIS, appId);
            if (fullBackupHis == null) {
                log.error("没有成功全备历史！");
                log.info("[mysql备份]没有成功全备历史！");
                backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "没有成功全备历史！");
                return;
            }
            backupHis.setBaseTime(fullBackupHis.getBaseTime());

            try {
                if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                    String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
                    client.execCmdOneway(cloudApp.getNamespace(), primaryPodName, "xtrabackup", "sh", "-c",
                            "bash /scripts/mysql-backup-inc.sh backup_" + backupHis.getBackupHisId()
                                    + " " + fullBackupHis.getBaseTime()
                                    + " " + CloudAppConstant.OperatorStorageType.NFS
                                    + " " + mountPath);
                } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                    client.execCmdOneway(cloudApp.getNamespace(), primaryPodName, "xtrabackup", "sh", "-c",
                            "bash /scripts/mysql-backup-inc.sh backup_" + backupHis.getBackupHisId()
                                    + " " + fullBackupHis.getBaseTime()
                                    + " " + CloudAppConstant.StorageType.S3
                                    + " " + cloudBackupStorageVO.getServer()
                                    + " " + cloudBackupStorageVO.getRegion()
                                    + " " + cloudBackupStorageVO.getBucket()
                                    + " " + cloudBackupStorageVO.getAccessKey()
                                    + " " + cloudBackupStorageVO.getSecretKey());
                } else {
                    throw new CustomException(600, "备份失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                }
            } catch (Exception e) {
                backupReturn(backupHis, changeId, StatusConstant.FAIL, "", e.getMessage());
                return;
            }
        } else {
            try {
                if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                    String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
                    client.execCmdOneway(cloudApp.getNamespace(), primaryPodName, "xtrabackup", "sh", "-c",
                            "bash /scripts/mysql-backup-full.sh backup_" + backupHis.getBackupHisId()
                                    + " " + CloudAppConstant.OperatorStorageType.NFS
                                    + " " + mountPath);
                } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                    String region = cloudBackupStorageVO.getRegion();
                    if (StringUtils.isEmpty(region)) {
                        region = "\"\"";
                    }
                    client.execCmdOneway(cloudApp.getNamespace(), primaryPodName, "xtrabackup", "sh", "-c",
                            "bash /scripts/mysql-backup-full.sh backup_" + backupHis.getBackupHisId()
                                    + " " + CloudAppConstant.StorageType.S3
                                    + " " + cloudBackupStorageVO.getServer()
                                    + " " + region
                                    + " " + cloudBackupStorageVO.getBucket()
                                    + " " + cloudBackupStorageVO.getAccessKey()
                                    + " " + cloudBackupStorageVO.getSecretKey());
                } else {
                    throw new CustomException(600, "备份失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                }
            } catch (Exception e) {
                backupReturn(backupHis, changeId, StatusConstant.FAIL, "", e.getMessage());
                return;
            }
        }

        //修改备份历史，将basetime插入
        backupMapper.updateBackupHis(SCHEMA, CLOUD_BACKUP_HIS, backupHis);

        BinlogBackupHis latestBinlogBackupHis = null;
        Integer binlogBackupHisId = 0;
        if (backupHis.getBackupDbLog()) {
            latestBinlogBackupHis = backupService.listBinlogBackupHis(appId);
            //判断是否执行binlog备份，需要执行则插入binlog的记录，并将是否已经触发binlog设为N
            BinlogBackupHis binlogBackupHis = new BinlogBackupHis();
            binlogBackupHis.setClusterId(appId);
            binlogBackupHis.setMsg("N");
            binlogBackupHisId = backupService.insertBinlogBackupHis(binlogBackupHis);
        }

        log.info("[mysql备份]开始创建定时");
        Map<String, String> jobDataMap = new HashMap<>();

        try {
            // 创建定时任务轮询执行结果
            jobDataMap.put("backupHisId", backupHis.getBackupHisId().toString());
            jobDataMap.put("latestBinlogCodes", latestBinlogBackupHis == null ? "" : latestBinlogBackupHis.getFileCodes());
            jobDataMap.put("kubeId", cloudApp.getKubeId().toString());
            jobDataMap.put("backupIp", podDTO.getPodIp());
            jobDataMap.put("changeId", String.valueOf(changeId));
            jobDataMap.put("backupFtpPath", this.getFtpBackupPath(cloudApp));
            jobDataMap.put("backupDbLog", String.valueOf(backupHis.getBackupDbLog()));
            jobDataMap.put("primaryPodName", primaryPodName);
            jobDataMap.put("appId", String.valueOf(appId));
            jobDataMap.put("binlogBackupHisId", String.valueOf(binlogBackupHisId));
            jobDataMap.put("backupTimeout", String.valueOf(backupHis.getMaxBackupDuration()));
//            jobDataMap.put("userInfo", JsonUtil.toJson(new UserInfo(){{setSchema(userInfo.getSchema());setUsername(userInfo.getUsername());setUserId(userInfo.getUserid());}}));
            jobDataMap.put("userInfo", JsonUtil.toJson(userInfo));
            log.info("备份定时用户信息：" + JsonUtil.toJson(userInfo));
            appCommonService.callScheduler(cloudApp,cloudApp.getCr(),jobDataMap,ActionEnum.BACKUP,MysqlBackupAndRestoreWatch.class, resourceChangeHis);
        } catch (Exception e) {
            log.error(e.getMessage());
            log.info("[mysql备份]创建定时失败");
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "mysql备份创建定时调度失败！错误信息：" + e.getMessage());
            return;
        }
    }

    public PodDTO getBackupPodMySQL(CloudApp cloudApp, KubeClient client) {
        List<PodDTO> pods = client.listPod(cloudApp.getNamespace(), AppKind.MYSQL_HA.labelOfPod(cloudApp));
        return pods.stream().filter(i -> CloudAppConstant.ROLE_SOURCE.equals(i.getLabels().get("app.kubernetes.io/role"))).collect(Collectors.toList())
                .stream().findFirst().orElse(null);
    }

    public List<PodDTO> getAllPodMySQL(CloudApp cloudApp, KubeClient client) {
        List<PodDTO> pods = client.listPod(cloudApp.getNamespace(), AppKind.MYSQL_HA.labelOfPod(cloudApp));
        return pods;
    }

    /**
     * 提交binlog
     * @param cloudApp
     * @param primaryPodName
     * @param binlogBackupHis
     * @param commitBackupResult
     * @param checkFileName
     * @param binlogBackupPodPath
     * @param container
     */
    public void performBinLogBackup(CloudApp cloudApp, String primaryPodName, BinlogBackupHis binlogBackupHis, String commitBackupResult, String checkFileName, String binlogBackupPodPath, String container) {
        //提交binlog备份
        //获取binlog信息，起始binlog名称、binlog的pos点
        /*
         返回实例：
         mysql  Ver 14.14 Distrib 5.7.27, for linux-glibc2.12 (x86_64) using  EditLine wrapper
         xtrabackup: recognized server arguments: --datadir=/var/lib/mysql
         xtrabackup version 2.4.24 based on MySQL server 5.7.35 Linux (x86_64) (revision id: b4ee263)
         mysql: [Warning] Using a password on the command line interface can be insecure.
         1
         1
         mysql: [Warning] Using a password on the command line interface can be insecure.
         [INFO_]:20220622.144604:root@mysql-hdversion-12-172x16x21x37:MySQL configuration file: /data/data/mysql/my3306.cnf
         [INFO_]:20220622.144604:root@mysql-hdversion-12-172x16x21x37:xtrabackup --defaults-file=/data/data/mysql/my3306.cnf --stream=xbstream --compress=quicklz ... > 20220622.144604.full.xbstream
         [INFO_]:20220622.144617:root@mysql-hdversion-12-172x16x21x37:127.0.0.1:3306 backup successful.
         [INFO_]:20220622.144617:root@mysql-hdversion-12-172x16x21x37:Get backup file size: du -sm ...
         [INFO_]:20220622.144617:root@mysql-hdversion-12-172x16x21x37:Backup file size: 28 MB
         [INFO_]:20220622.144617:root@mysql-hdversion-12-172x16x21x37:Get 'to_lsn' success.
         [RESULT_]:20220622.144617:root@mysql-hdversion-12-172x16x21x37:{"status":"success", "success":"Y", "fileName":"20220622.144604.full.xbstream", "fullBakFile":"20220622.144604.full.xbstream", "filePath":"/data/xbkfileHostpath/3306", "restoreDir":"/data/xbkfileHostpath/3306", "fileSize":28, "startTime":"2022-06-22 14:46:04", "endTime": "2022-06-22 14:46:17", "baseTime": "20220622144604", "binlogName": "mysql-bin.000005", "binlogPos": "154", "destDir": "/data/xbkfileHostpath/3306", "srcFiles": "/data/xbkfileHostpath/3306/20220622.144604.full.xbstream"}
        */
        //获取提交备份返回结果
        log.info("[mysql备份]提交备份命令的返回结果：" + commitBackupResult);
        //截取result_部分
//        String resJsonStr = commitBackupResult.substring(commitBackupResult.lastIndexOf(podName) + podName.length() + 1);
        JSONObject resJsonObj = JSONObject.parseObject(commitBackupResult);
        //起始binlog名称、pos点位、备份开始时间，binlog的备份开始时间需要设置为早于该开始时间(用于在选择恢复时间后进行对xbk)
        String binlogName = resJsonObj.getString("binlogName");
        String binlogPos = resJsonObj.getString("binlogPos");
        String backupStartTimeStr = resJsonObj.getString("startTime");
        //获取统一备份路径
        String backupBinlogPath = CloudAppConstant.MYSQL_LOCAL_PATH;
        log.info("[mysql备份]binlog文件统一放置路径：" + backupBinlogPath);
        //保存binlog备份历史到表
        log.info("[mysql备份]保存binlog历史基本信息");
        binlogBackupHis.setStartTime(Timestamp.valueOf(backupStartTimeStr));
        binlogBackupHis.setEndTime(Timestamp.valueOf(backupStartTimeStr));
        binlogBackupHis.setBackupLength(0);
        binlogBackupHis.setClusterId(cloudApp.getId());
        binlogBackupHis.setClusterName(cloudApp.getName());
        binlogBackupHis.setMsg("Y");
        //设置为运行中：   0：成功   1：失败   2：运行中
        binlogBackupHis.setStatus("2");
        //备份最终目录实例：/hdTest/backup/rongqiyun/mysql-mysqlhd-935-172x16x21x200/mysql/binlog， /hdTest/backup是sys_config表中配置的路径，后面则为/namespace/podName/containerName/固定值binlog
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String nowStr = sdf.format(Timestamp.valueOf(backupStartTimeStr));
        binlogBackupHis.setBackupDir(basePath + "/" + backupBinlogPath + "/" + cloudApp.getNamespace() + "/" + cloudApp.getCrName() + "/binlog/" + nowStr);
        try {
            backupMapper.updateBinlogBackupHis(SCHEMA, DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, binlogBackupHis);
        } catch (Exception e) {
            // 不影响主业务
            log.error("修改binlog备份历史异常");
            e.printStackTrace();
        }

        //执行备份binlog脚本命令
        /*
        返回日志实例：
        [INFO_]:20220624.153128:root@k8snew73:System release test passed: centos:7
[INFO_]:20220624.153130:root@k8snew73:start cp binlog.
[INFO_]:20220624.153132:root@k8snew73:flush logs done.
[INFO_]:20220624.153135:root@k8snew73:cp mysql-bin.000001 done.
[INFO_]:20220624.153136:root@k8snew73:cp mysql-bin.000002 done.
[INFO_]:20220624.153137:root@k8snew73:cp mysql-bin.000003 done.
[INFO_]:20220624.153138:root@k8snew73:cp mysql-bin.000004 done.
[INFO_]:20220624.153139:root@k8snew73:cp mysql-bin.000005 done.
[INFO_]:20220624.153140:root@k8snew73:cp mysql-bin.000006 done.
[INFO_]:20220624.153141:root@k8snew73:cp mysql-bin.000007 done.
[INFO_]:20220624.153142:root@k8snew73:cp mysql-bin.000008 done.
[INFO_]:20220624.153142:root@k8snew73:cp binlog success.

         */
        //在/backup下的目录
//        String binlogBackupPodPath = "/binlog/";
        //在mysql容器中执行，执行后，会在/backup下生成传参目录，然后将binlog放在其中
        String backupBinlogScript = "sh /scripts/mysql-binlog-backup.sh " + binlogBackupPodPath + nowStr + " " + binlogBackupHis.getId();
        log.info("[mysql备份]执行binlog备份的命令为：" + backupBinlogScript);
        KubeClient kubeClient = kubeClientService.get(cloudApp.getKubeId());
        try {
            kubeClient.execCmdOneway(cloudApp.getNamespace(), primaryPodName, container, "sh", "-c", backupBinlogScript);
            log.info("触发备份binlog成功");
        } catch (Exception e) {
            log.error("备份binlog失败，信息为：" + e.getMessage() + "     详细信息为:" + e);
            throw new CustomException(600, "备份binlog失败！");
        }
    }

    public void performBinLogBackup(CloudApp cloudApp, String primaryPodName, BinlogBackupHis binlogBackupHis, String commitBackupResult) {
        performBinLogBackup(cloudApp, primaryPodName, binlogBackupHis, commitBackupResult, "binlogbackup_" + binlogBackupHis.getId(),
                "/binlog/", "mysql");
    }
    /**
     * 提交binlog
     * @param cloudApp
     * @param primaryPodName
     * @param binlogBackupHis
     */
    public void performBinLogBackup(CloudApp cloudApp, String primaryPodName, BinlogBackupHis binlogBackupHis) {
        //起始binlog名称、pos点位、备份开始时间，binlog的备份开始时间需要设置为早于该开始时间(用于在选择恢复时间后进行对xbk)
        Timestamp startTime = binlogBackupHis.getStartTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String nowStr = sdf.format(startTime);
        //获取统一备份路径
        String backupBinlogPath = CloudAppConstant.MYSQL_LOCAL_PATH;
        log.info("[mysql备份]binlog文件统一放置路径：" + backupBinlogPath);
        //保存binlog备份历史到表
        log.info("[mysql备份]保存binlog历史基本信息");
        binlogBackupHis.setStartTime(startTime);
        binlogBackupHis.setEndTime(startTime);
        binlogBackupHis.setBackupLength(0);
        binlogBackupHis.setClusterId(cloudApp.getId());
        binlogBackupHis.setClusterName(cloudApp.getName());
        binlogBackupHis.setMsg("Y");
        //设置为运行中：   0：成功   1：失败   2：运行中
        binlogBackupHis.setStatus("2");
        //备份最终目录实例：/hdTest/backup/rongqiyun/mysql-mysqlhd-935-172x16x21x200/mysql/binlog， /hdTest/backup是sys_config表中配置的路径，后面则为/namespace/podName/containerName/固定值binlog
        binlogBackupHis.setBackupDir(basePath + "/" + backupBinlogPath + "/" + cloudApp.getNamespace() + "/" + cloudApp.getCrName() + "/binlog/" + nowStr);
        try {
            backupMapper.updateBinlogBackupHis(SCHEMA, DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, binlogBackupHis);
        } catch (Exception e) {
            // 不影响主业务
            log.error("修改binlog备份历史异常");
            e.printStackTrace();
        }

        //执行备份binlog脚本命令
        //在/backup下的目录
        String binlogBackupPodPath = "/binlog/";
        //在mysql容器中执行，执行后，会在/backup下生成传参目录，然后将binlog放在其中
        String backupBinlogScript = "sh /scripts/mysql-binlog-backup.sh " + binlogBackupPodPath + nowStr + " binlogbackup_" + binlogBackupHis.getId();
        log.info("[mysql备份]执行binlog备份的命令为：" + backupBinlogScript);
        KubeClient kubeClient = kubeClientService.get(cloudApp.getKubeId());
        try {
            kubeClient.execCmdOneway(cloudApp.getNamespace(), primaryPodName, "mysql", "sh", "-c", backupBinlogScript);
            log.info("触发备份binlog成功");
        } catch (Exception e) {
            log.error("备份binlog失败，信息为：" + e.getMessage() + "     详细信息为:" + e);
            throw new CustomException(600, "备份binlog失败！");
        }
    }

    /**
     * mongodb备份
     * 1. 插入备份基本信息和操作记录
     * 2. 获取到集群所有节点
     * 3. 从中获取到备份的从节点
     * 4. 在从节点中获取所有数据库的名称列表
     * 5. 创建定时调度，轮询备份结果
     * 6. 遍历执行备份所有库的命令
     *
     * @param backupHis
     */
    @Async
    public void mongoBackup(BackupHisVO backupHis) {
        BackupHis runningBackupHis = backupMapper.getRunningByAppId(SCHEMA, CLOUD_BACKUP_HIS, backupHis.getAppId());
        if (runningBackupHis != null) {
            throw new CustomException(600, "存在正在执行的备份，请勿重复提交！");
        }
        // 1. 插入备份历史
        //获取app信息
        CloudApp app = cloudAppMapper.selectByPrimaryKey(backupHis.getAppId(), DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_APP);

        String checkStorageResult = backupHis.getMessage();
        //开始备份时间
        Timestamp startTime = new Timestamp(System.currentTimeMillis());

        backupHis.setBackupType("full");    //设置全备
        backupHis.setStartTime(startTime);
        backupHis.setStatus(StatusConstant.RUNNING);
        //应用所属集群
        String kubeName = kubeConfigService.getName(app.getKubeId());
        backupHis.setKubeName(kubeName);
        //记录备份信息：数据库名称、备份路径、备份文件名称
        JSONObject messageObj = new JSONObject();
        messageObj.put("backupIp", "");
        messageObj.put("backupPath", "");
        messageObj.put("msg", "备份中...");
        backupHis.setMessage(JSON.toJSONString(messageObj));
        //插入备份历史
        backupMapper.commitBackup(SCHEMA, CLOUD_BACKUP_HIS, backupHis);
        //插入操作记录
        ResourceChangeHis resourceChangeHis = new ResourceChangeHis() {{
            setInsertTime(startTime);
            setKind(app.getKind());
            setKubeId(app.getKubeId());
            setNamespace(app.getNamespace());
            setCommand("备份");
            setStatus("2");
            setAction("Backup");
            setMsg("备份中...".concat("\n").concat(null == checkStorageResult ? "" : checkStorageResult));
            setAppId(app.getId());
            setAppLogicId(app.getLogicAppId());
            setAppName(app.getName());
            setKubeName(kubeName);
            setYaml(app.getCr());
            setUserName(UserUtil.getAsyncUserinfo().getUsername());
            setUserIp(CloudRequestContext.getContext().getUserIp());
            setLastEndTimestamp(System.currentTimeMillis());
        }};
        Integer changeId = insertResourceChangeHis(resourceChangeHis);


        // 2. 获取一个从库
        //创建k8s客户端
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        //所有pods
        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
        //是否是主节点执行备份操作
        Boolean isMaster = Boolean.FALSE;
//        PodDTO secondaryPod = getBackupPodMongoDB(app, kubeClient, appKind);

        //获取备份节点
        PodDTO secondaryPod = null;
        try {
            List<PodDTO> pods = kubeClient.listPod(app.getNamespace(), appKind.labelOfPod(app));
            if (CollectionUtils.isEmpty(pods)){
                log.error("mongodb备份未获取到节点信息！");
            }
            if (pods.size() == 1){
                secondaryPod = pods.get(0);
                isMaster = Boolean.TRUE;
            } else {
                Optional<PodDTO> first = pods.stream().filter(pod -> "SECONDARY".equals(getMongoDbRole(kubeClient,
                        pod.getNamespace(), pod.getPodName(), app.getCrName()))).findFirst();
                secondaryPod = first.orElseGet(() -> pods.get(0));
            }
        } catch (Exception e) {
            log.error("mongodb备份获取备份节点失败！", e);
        }

        if (secondaryPod == null) {
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "MongoDB没有可备份的库！");
            return;
        }

        //获取pod中的备份目录
        String backupPodPath = MongoUtil.getBackupRootPath(app.getNamespace(), app.getCrName()); // go into '<namespace>/<name>' under mountPath '/backup'

        // 3. 根据从节点的ip获取所有数据库
        //从节点ip
        String secondaryPodIP = secondaryPod.getPod().getStatus().getPodIP();
        //宿主机ip
        String hostIp = secondaryPod.getPod().getStatus().getHostIP();
        //数据库所有数据条数
        List<String> backupDBList = new ArrayList<>();
        String queryAlldb = "bash -c \"echo -e 'rs.secondaryOk()\nshow dbs\n' | mongo --host=" + secondaryPodIP
                + " --port=" + appKind.getDbPort() + " --authenticationDatabase=admin --username="
                + CloudAppConstant.UsernameAndPassword.mongoDBUsername
                + " --password=" + CloudAppConstant.UsernameAndPassword.mongoDBPassword + " --quiet\"";
        String backupPodName = secondaryPod.getPod().getMetadata().getName();
        try {
            String allDB = kubeClient.execCmd(secondaryPod.getPod().getMetadata().getNamespace(),
                    backupPodName, appKind.getContainerName(), "sh", "-c", queryAlldb);
            int gbIndex = allDB.lastIndexOf("GB");
            String allDBStr = allDB.substring(0, gbIndex);
            String[] gbs = allDBStr.split("GB\n");

            for (String everyDBStr : gbs) {
                String everyDBname = everyDBStr.substring(0, everyDBStr.indexOf(" "));
//                if ("config".equalsIgnoreCase(everyDBname) || "local".equalsIgnoreCase(everyDBname)
//                        || "admin".equalsIgnoreCase(everyDBname)){
//                    continue;
//                }
                if ("local".equalsIgnoreCase(everyDBname)
                        || "config".equalsIgnoreCase(everyDBname)) {
                    continue;
                }
                backupDBList.add(everyDBname);
            }
            //判断是否存在需要备份的数据库
            if (backupDBList.isEmpty()) {
                backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "MongoDB没有需要备份的库！");
                return;
            }
        } catch (Exception e) {
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "mongodb备份获取备份数据库失败！");
            return;
        }

        // 4. 时间戳
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        Date startDate = new Date();
        String fileTimeStamp = sdf.format(startDate);
        backupPodPath += "/" + fileTimeStamp;
        String backupNodePath = CloudAppConstant.MongoDBBackupPath + app.getArch() + "/" + app.getCrName() + "/" + fileTimeStamp;

        //修改历史
        backupHis.setPodName(backupPodName);
        backupHis.setBaseTime(fileTimeStamp);
        backupMapper.updateBackupHis(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_HIS, backupHis);

        /**
         * 注：mongo的备份生成的文件夹中：admin用户会有【"system.keys", "system.profile", "system.users", "system.version"】四个集合
         * 其中"system.keys"集合不会被备份
         */
        Boolean mongoEnableSlowQuery;
        try {
            //查看是否开启了慢日志分析
            mongoEnableSlowQuery = getMongoEnableSlowQuery(app.getNamespace(), kubeClient, backupPodName);
        } catch (Exception e) {
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "mongodb执行备份失败!查询Mongo慢日志分析出错" + e);
            return;
        }

        // 获取备份存储信息
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        // 5. 遍历备份所有库并同时记录所有库的collection数量
        int allCollectionsNum = mongoEnableSlowQuery ? 1 : 0;
        //处理backupDBList为字符串，例如：a,b,c
        String backupDBListStr = backupDBList.toString().replace("[", "").replace("]", "").replace("\"", "").replace(" ", "");
        //执行备份脚本  sh -x /data/mgbackup.sh http://*************:9000 hdtest hdd minioadmin minioadmin poc /mnt/share/mongodb/rep/hdtest/m2/67676767 1 false true
        String backupCmd = "";
        if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
            String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
            backupCmd = "bash /scripts/backup.sh "
                    + mountPath
                    + " " + backupDBListStr
                    + " " + backupPodPath
                    + " " + allCollectionsNum
                    + " " + isMaster
                    + " " + mongoEnableSlowQuery
                    + " " + CloudAppConstant.OperatorStorageType.NFS;
        } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
            String region = cloudBackupStorageVO.getRegion();
            if (StringUtils.isEmpty(region)) {
                region = "\"\"";
            }
            backupCmd = "bash /scripts/backup.sh "
                    + cloudBackupStorageVO.getServer()
                    + " " + backupDBListStr
                    + " " + backupPodPath
                    + " " + allCollectionsNum
                    + " " + isMaster
                    + " " + mongoEnableSlowQuery
                    + " " + CloudAppConstant.StorageType.S3
                    + " " + region
                    + " " + cloudBackupStorageVO.getBucket()
                    + " " + cloudBackupStorageVO.getAccessKey()
                    + " " + cloudBackupStorageVO.getSecretKey()
                    + " " + cloudBackupStorageVO.getBackupPath();
        } else {
            throw new CustomException(600, "备份失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
        }

        try {
            kubeClient.execCmdOneway(app.getNamespace(), backupPodName, appKind.getContainerName(), "sh", "-c", backupCmd);
//            String backupRes = kubeClient.execCmdOneway(app.getNamespace(), backupPodName, AppKind.MongoDB.getContainerName(), "sh", "-c", backupCmd);
//            String allCollectionsNumStr = backupRes.replace("\n", "");
//            allCollectionsNum = Integer.valueOf(allCollectionsNumStr);
        } catch (Exception e) {
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "mongodb备份失败！" + e.getMessage());
            return;
        }

        // 6.创建定时轮询备份结果
        Map<String, Object> map = new HashMap<>();
        try {
            map.put("dbs", backupDBList);
//            map.put("allCollectionsNum", allCollectionsNum);
            map.put("backupNodePath", backupNodePath);
            map.put("startDateStr", fileTimeStamp);
            map.put("backupHisId", backupHis.getBackupHisId());
            // backupPodPath 用于通过备份文件恢复时获取备份文件的路径，因为存在备份文件所属实例已经被清理情况
            ///mnt/share/mongodb/replicaset/lianzb-m/monrpbak/20250605163258
            map.put("backupPodPath", backupPodPath);
            map.put("backupIp", hostIp);
            map.put("changeId", changeId);
            map.put("backupPodName", backupPodName);
            map.put("backupFtpPath", getFtpBackupPath(app));
            map.put("backupFilename", fileTimeStamp);
            map.put("backupTimeout", backupHis.getMaxBackupDuration());
            appCommonService.callScheduler(app, app.getCr(), map, ActionEnum.BACKUP, MongoDBBackupAndRestoreWatch.class, resourceChangeHis);
        } catch (Exception e) {
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "mongodb备份生成定时调度失败！");
        }
    }

    /**
     * 获取Mongo是否开启了慢日志查询
     *
     * @param namespace
     * @param kubeClient
     * @param backupPodName
     * @return
     * @throws Exception
     */
    public Boolean getMongoEnableSlowQuery(String namespace, KubeClient kubeClient, String backupPodName) throws Exception{
        //判断是否开启了慢日志查询
        String slowLogQueryStr = "mongo --port \"${MONGO_PORT}\" --eval \" db.getProfilingStatus().was;\" --username " + CloudAppConstant.UsernameAndPassword.mongoDBUsername + " --password " + CloudAppConstant.UsernameAndPassword.mongoDBPassword;
        String slowLogQueryMsg = kubeClient.execCmd(namespace, backupPodName, AppKind.MongoDB.getContainerName(), "sh", "-c", slowLogQueryStr);
        log.info("查询Mongo是否开启慢日志查询信息为：" + slowLogQueryMsg);
        if (StringUtils.isEmpty(slowLogQueryMsg)) {
            log.error("未查询到Mongo相关的慢日志查询信息");
            throw new CustomException(600, "未查询到Mongo相关的慢日志查询信息");
        }
        String[] split = slowLogQueryMsg.split("\n");
        String s = split[split.length - 1];
        return "1".equals(s);
    }


    public PodDTO getBackupPodMongoDB(CloudApp app, KubeClient kubeClient, AppKind appKind) {
        PodDTO secondaryPod;
        try {
            List<PodDTO> pods = kubeClient.listPod(app.getNamespace(), appKind.labelOfPod(app));
            if (CollectionUtils.isEmpty(pods)){
                log.error("mongodb备份未获取到节点信息！");
                return null;
            }
            if (pods.size() == 1){
                secondaryPod = pods.get(0);
            } else {
                Optional<PodDTO> first = pods.stream().filter(pod -> "SECONDARY".equals(getMongoDbRole(kubeClient,
                        pod.getNamespace(), pod.getPodName(), app.getCrName()))).findFirst();
                secondaryPod = first.orElseGet(() -> pods.get(0));
            }
            return secondaryPod;
        } catch (Exception e) {
            log.error("mongodb备份获取备份节点失败！", e);
            return null;
        }
    }

    /**
     * 获取pod的角色信息
     */
    public String getMongoDbRole(KubeClient kubeClient, String namespace, String podName, String appName) {
        if (org.apache.commons.lang3.StringUtils.isAnyEmpty(namespace, podName, appName)) {
            return null;
        }
        AppKind appKind = AppKind.MongoDB;
        final String queryMongoRole = "mongo -u " + CloudAppConstant.UsernameAndPassword.mongoDBUsername
                + " -p " + CloudAppConstant.UsernameAndPassword.mongoDBPassword + " --authenticationDatabase admin" +
                " --quiet --eval 'rs.status().members.find(function(x){return x.name===\""
                + podName.trim() + "." + appName + "-svc."
                + namespace + ".svc.cluster.local:" + appKind.getDbPort() + "\"}).stateStr'";
        try {
            String role = kubeClient.execCmd(namespace, podName, appKind.getContainerName(), "sh", "-c", queryMongoRole);
            if (role.contains("Error")) {
                return null;
            }
            return org.apache.commons.lang3.StringUtils.trim(role);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * redis获取pod的主从信息
     * @param kubeClient
     * @param namespace
     * @param podName
     * @return
     */
    public String getRedisDbRole(KubeClient kubeClient, String namespace, String podName,String password) {
        if (podName == null || namespace == null) {
            return null;
        }
        password = StringUtils.isEmpty(password) ? "" : "-a '" + password + "'";
        final String queryMongoRole = "redis-cli " + password + " info replication 2>/dev/null | grep role | awk -F: '{print $2}'";
        try {
            String role = kubeClient.execCmd(namespace, podName, "redis", "sh", "-c", queryMongoRole);
            if (!role.contains("slave") && !role.contains("master")) {
                return null;
            }
            String resRole = role.replaceAll("\r\n", "");
            return resRole;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 恢复执行失败更新历史
     * @param restoreHis
     */
    public void restoreFailUpdateHis(RestoreHis restoreHis) {
        if (!StringUtils.isEmpty(restoreHis.getStatus())) {
            Timestamp endTime = new Timestamp(System.currentTimeMillis());
            restoreHis.setEndTime(endTime);
            Date endTDate = new Date();
            long interval = (endTDate.getTime() - restoreHis.getStartTime().getTime())/1000;
            String durationStr = String.valueOf(interval);
            int duration = Integer.parseInt(durationStr);
            restoreHis.setRestoreLength(duration);
            restoreMapper.updateRestoreHis(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_RESTORE_HIS, restoreHis);
        }
    }

    /**
     * 根据实例列表获取数据最新的实例，获取错误更新备份历史
     *
     * @param slaveInstanceList size > 1
     * @param backupHis
     * @return
     */
    private AppInstanceVO getMaxGTIDInstance(CloudApp app, List<AppInstanceVO> slaveInstanceList, BackupHis backupHis) {
        AppInstanceVO slaveInstance = null;
        //查询数据库GTID的SQL
        KubeClient client = kubeClientService.get(app.getKubeId());
        String queryGTIDSql = " mysql -u k8sadmin -pk8sadmin -N -e 'select @@global.gtid_executed as gtid' | grep '^\\S*'";
        List<Map<String, Object>> gtidList = new ArrayList<>();
        //遍历每一个Slave库查找数据最全的库
        for (AppInstanceVO cloudAppInstanceVO : slaveInstanceList) {
            try {
                // poc fix: pod ip is inaccessible outside the cluster
                String gtid = client.execCmd(cloudAppInstanceVO.getNamespace(), cloudAppInstanceVO.getPodName(), CloudAppConstant.MYSQL_CONTAINER_NAME, "sh", "-c", queryGTIDSql);
                Map<String, Object> gtidMap = new HashMap<>();
                gtidMap.put("gtid", gtid);
                gtidMap.put("instance", cloudAppInstanceVO);
                gtidList.add(gtidMap);
            } catch (Exception e) {
                log.error("查询gtid ", e);
                return null;
            }
        }
        //比较每一个Slave库的gtid
        String compareGTIDSql = "select gtid_subset(\"%s\", \"%s\") as result\\G";
        String cmd = "mysql -u${MYSQL_ROOT_USER} -p${MYSQL_ROOT_PASSWORD} -P${DB_PORT} -e '%s' | grep result";
        Pattern pattern = Pattern.compile("result:(\\s[1|0])");
        int i = 0; // gtid set最大的下标
        for (int j = 1; j < gtidList.size(); j ++) {
            String compareGTIDSqlTmp = compareGTIDSql;
            String gtid1 = (String) gtidList.get(i).get("gtid");
            String gtid2 = (String) gtidList.get(j).get("gtid");
            compareGTIDSqlTmp = String.format(compareGTIDSqlTmp, gtid1, gtid2);
            try {
                String data = client.execCmd(app.getNamespace(), slaveInstanceList.get(0).getPodName(), mysqlService.getKind().getContainerName(),
                        "sh", "-c", String.format(cmd, compareGTIDSqlTmp));
                // data is like
                // result: 1
                // or result: 0
                Matcher matcher ;
                if (data == null || !(matcher = pattern.matcher(data)).find()) {
                    log.error("比较数据库GTID失败！");
                    return null;
                }
                String result = matcher.group(1);
                if ("1".equals(result)) { // 表示gtid2 contains gtid1
                    i = j;
                }
            } catch (Exception e) {
//                    backupFailUpdateHis(backupHis, e.getMessage());
                log.error("比较gtid失败", e);
                return null;
            }
        }
        return slaveInstanceList.get(i);
    }

    public static void main(String[] args) {
//        System.setProperty("file.separator", "\\/");
        System.out.println(System.getProperty("file.separator"));
        File file = new File("/data/xbk/a.stream");

        System.out.println(file.getName());
        System.out.println(file.getPath());
        System.out.println(file.getParent());

    }

//    /**
//     * 备份成功更新备份历史
//     * @param backupHis
//     * @param message
//     */
//    public void backupSuccessUpdateHis(BackupHis backupHis, String message) {
//        String backupResultStr = "";
//        if (!StringUtils.isEmpty(message)) {
//            String regex = ".*\\[RESULT_\\].*(?<result>(\\{.*\\})).*";
//            Pattern pattern = Pattern.compile(regex);
//            Matcher matcher = pattern.matcher(message);
//            while (matcher.find()) {
//                backupResultStr = matcher.group(1);
//            }
//        }
//        //去除转义
//        backupResultStr = StringEscapeUtils.unescapeJava(backupResultStr);
//        JSONObject backupResultJson = null;
//        message = message.length() > 4000 ? message.substring(0, 4000) : message;
//        if (!StringUtils.isEmpty(backupResultStr)) {
//            backupResultJson = JSON.parseObject(backupResultStr);
//        } else {
//            backupHis.setMessage(message);
//            backupFailUpdateHis(backupHis, "解析执行结果失败！");
//        }
//        if (backupResultJson == null) {
//            backupHis.setMessage(message);
//            backupFailUpdateHis(backupHis, message);
//        } else {
//            backupHis.setStatus(StatusConstant.SUCCESS);
//            Timestamp startTime = Timestamp.valueOf(backupResultJson.getString("startTime"));
//            Timestamp endTime = Timestamp.valueOf(backupResultJson.getString("endTime"));
//            Long time = (endTime.getTime() - startTime.getTime()) / 1000;
//            String fileName = backupResultJson.getString("fileName");
//            String binlogName = backupResultJson.getString("binlogName");
//            String binlogPos = backupResultJson.getString("binlogPos");
//            String baseTime = backupResultJson.getString("baseTime");
//            backupHis.setStartTime(startTime);
//            backupHis.setEndTime(endTime);
//            backupHis.setDuration(time.intValue());
//            backupHis.setFileName(fileName);
//            backupHis.setBinlogName(binlogName);
//            backupHis.setBinlogPos(binlogPos);
//            if ("full".equals(backupHis.getBackupType())) {
//                backupHis.setBaseTime(baseTime);
//            }
//            backupHis.setMessage(message);
//            backupMapper.updateBackupHis(SCHEMA, CLOUD_BACKUP_HIS, backupHis);
//        }
//    }

    /**
     * mysql备份成功方法
     *  @param backupHis
     * @param result
     * @param dataSize
     */
    public void backupSuccessUpdateHis(BackupHis backupHis, Map result, String backupIp, Integer changeId, String success, long dataSize) {

        backupHis.setStatus(success);
        if (StatusConstant.SUCCESS.equalsIgnoreCase(success)) {
            Timestamp startTime = Timestamp.valueOf((String)result.get("startTime"));
            Timestamp endTime = Timestamp.valueOf((String)result.get("endTime"));
            Long time = (endTime.getTime() - startTime.getTime()) / 1000;
            String fileName = (String)result.get("fileName");
            String binlogName = (String)result.get("binlogName");
            String gtid = String.valueOf(result.get("gtid") == null ? "" : result.get("gtid"));
            String baseTime = (String)result.get("baseTime");
            String srcFiles = (String) result.get("filePath");
            String ftpBackupPath = (String)result.get(CloudAppConstant.BACKUP_PATH_KEY);
            backupHis.setStartTime(startTime);
            backupHis.setEndTime(endTime);
            backupHis.setDuration(time.intValue());
            backupHis.setFileName(fileName);
            backupHis.setBinlogName(binlogName);
            backupHis.setBinlogPos(gtid);
            //创建message对象
            JSONObject mesObj = new JSONObject();
            mesObj.put("backupIp", backupIp);
            mesObj.put("fileName", fileName);
            mesObj.put(CloudAppConstant.BACKUP_PATH_KEY, ftpBackupPath);
            mesObj.put("ftpBackupPath", srcFiles);
            mesObj.put("dataSize", Collections.singletonMap(backupHis.getPodName(), dataSize));
            mesObj.put("msg", JSON.toJSONString(result));

            String version = cloudAppService.get(backupHis.getAppId()).getVersion();
            mesObj.put("version", version);
            String mes = JSON.toJSONString(mesObj);
            backupHis.setMessage(mes);
            if ("full".equals(backupHis.getBackupType())) {
                backupHis.setBaseTime(baseTime);
            }
        }
        backupMapper.updateBackupHis(SCHEMA, CLOUD_BACKUP_HIS, backupHis);
        ResourceChangeHis resourceChangeHis = new ResourceChangeHis();
        resourceChangeHis.setId(changeId);
        resourceChangeHis.setStatus(success);
        resourceChangeHis.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
        insertResourceChangeHis(resourceChangeHis);
        if(null != success && !"2".equalsIgnoreCase(success)){
            cloudAppService.handleWatchResult(backupHis.getAppId(), StatusConstant.SUCCESS.equalsIgnoreCase(success));
        }
    }

    /**
     * mysql恢复. restoreJob仅支持单az应用
     */
    @Async
    public void restore(BackupHis backupHis, Integer goalAppId, String restoreTime, String ftpFilename, String backupType) {
        log.info("[mysql恢复]触发恢复");
        //校验恢复时间是否符合要求，勿删
//        if(!StringUtils.isEmpty(restoreTime)){
//            try {
//                checkRestoreTime(backupHis.getAppId(), restoreTime);
//            } catch (ParseException e) {
//                throw new CustomException(600, "校验恢复时间错误！");
//            }
//        }
        CloudApp app = cloudAppService.get(goalAppId);
        KubeClient client = kubeClientService.get(app.getKubeId());
        List<PodDTO> instanceList = getRestorePodMySQL(app, client);
        //查询主库的pod名称，执行binlog备份
        log.info("[mysql恢复]开始获取主节点");
        MySQLHA cr = client.listCustomResource(MySQLHA.class, app.getCrName(), app.getNamespace());
        String primaryPodName = instanceList.stream().filter(i -> i.getPodIp().equals(cr.getStatus().getSource())).map(i -> i.getPodName()).findAny().orElseThrow(()->new RuntimeException("未获取到主节点名称"));
        log.info("[mysql恢复]获取的主节点名称为：" + primaryPodName);
        //获取所有要恢复的podname
        List<String> podNames = instanceList.stream().map(instance -> instance.getPodName()).collect(Collectors.toList());
        log.info("[mysql恢复]获取的所有节点的名称为：" + podNames);
        if (backupHis != null) {
            ftpFilename = backupHis.getFileName();
            backupType = backupHis.getBackupType();
        }
        ftpFilename = getStoreDir() + "/" + ftpFilename; // 备份时默认上传到storeDir
        // 查找backup volume path
        String backupDir = instanceList.get(0).getPod().getSpec().getContainers().stream()
                .filter(c -> XTRABACKUP_CONTAINER_NAME.equalsIgnoreCase(c.getName()))
                .flatMap(c -> c.getVolumeMounts().stream())
                .filter(v -> CloudAppConstant.MYSQL_BACKUP_VOLUME_NAME.equalsIgnoreCase(v.getName()))
                .map(VolumeMount::getMountPath).findAny().orElse(null);
        restore(backupHis, app, restoreTime, primaryPodName, podNames, backupType, new File(ftpFilename), backupDir);
    }

    public List<PodDTO> getRestorePodMySQL(CloudApp app, KubeClient client) {
        List<PodDTO> instanceList = client.listPod(app.getNamespace(), mysqlService.getKind().labelOfPod(app));
        return instanceList;
    }

    /**
     * backupHis: fullFileName, (binlogName, binlogPosition, restoreTime)(binlog), (baseTime)(增备), backupType, appId
     * @param primaryPodName 恢复前sourcePodName
     * @param podNames 要执行恢复的pod's name
     * @param backupType full | incre
     * @param xbkFileFTPPath 备份文件在ftp上的文件路径
     * @param restoreDir 恢复脚本的workDir. 上传xbkFile时上传到该目录
     */
    @Async
    public void restore(BackupHis backupHis, CloudApp goalApp, String restoreTime, String primaryPodName, List<String> podNames, String backupType, File xbkFileFTPPath, String restoreDir) {
        // 1 获取应用的基本信息，并添加备份的基本信息
        int goalAppId = goalApp.getId();
        String xbkFilename = xbkFileFTPPath.getName();
        String xbkFilepath = xbkFileFTPPath.getPath().replaceAll("\\\\", "/");
        log.info("[mysql恢复]appId为：" + goalAppId);
        log.info("[mysql恢复]backupType为：" + backupType);
        log.info("[mysql恢复]xbkFilename为：" + xbkFilename);
        log.info("[mysql恢复]xbkFilepath为：" + xbkFilepath);
        log.info("[mysql恢复]开始修改基本信息");
        goalApp.setStatus(CloudAppConstant.AppStatus.PENDING);
        cloudAppService.update(goalApp);
        //操作记录
        ResourceChangeHis resourceChangeHis = null;
        Integer changeId = null;
        Timestamp startTime = new Timestamp(System.currentTimeMillis());
        //保存基本信息
        RestoreHis restoreHis = new RestoreHis() {{
            setAppId(goalAppId);
            setAppName(goalApp.getName());
            setStatus(StatusConstant.RUNNING);
            setStartTime(new Timestamp(System.currentTimeMillis()));
            setFileName(xbkFilename);
            setRestoreDir(restoreDir);
            setAppType(goalApp.getKind());
            setFileDeleted(false);
        }};
        //插入当前恢复步骤和信息
        JSONObject messageObj = new JSONObject();
        messageObj.put("message", "恢复中...");
        messageObj.put("step", "0");
        restoreHis.setMessage(JSONObject.toJSONString(messageObj));
        restoreMapper.commitRestore(SCHEMA, CLOUD_RESTORE_HIS, restoreHis);
        //集群配置
        KubeConfig kubeConfig = kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, goalApp.getKubeId());
        //插入操作记录
        resourceChangeHis = new ResourceChangeHis(){{
            setInsertTime(startTime);
            setKind(goalApp.getKind());
            setKubeId(goalApp.getKubeId());
            setNamespace(goalApp.getNamespace());
            setCommand("恢复");
            setStatus("2");
            setAction(ActionEnum.RESTORE.getActionType());
            setMsg("恢复中...");
            setAppId(goalApp.getId());
            setAppLogicId(goalApp.getLogicAppId());
            setAppName(goalApp.getName());
            setKubeName(kubeConfig != null ? kubeConfig.getName() : null);
            setYaml(goalApp.getCr());
            setLastEndTimestamp(System.currentTimeMillis());
            setUserIp(CloudRequestContext.getContext().getUserIp());
            setUserName(UserUtil.getCurrentUser() == null ? UserUtil.getAsyncUserinfo().getName() : UserUtil.getCurrentUser().getName());
            setUserId(UserUtil.getCurrentUser().getUserid());
        }};
        changeId = insertResourceChangeHis(resourceChangeHis);
        if(null == kubeConfig){
            log.error("未获取到集群！");
            restoreReturn(restoreHis, changeId, "未获取到集群！", StatusConstant.FAIL);
            return;
        }
        restoreHis.setKubeName(kubeConfig.getName());
        String podNamesStr = String.join(",", podNames);
        restoreHis.setPodName(podNamesStr);
        restoreMapper.updateRestoreHis(SCHEMA, CLOUD_RESTORE_HIS, restoreHis);
        KubeClient kubeClient = kubeClientService.get(goalApp.getKubeId());
        log.info("[mysql恢复]结束修改基本信息");

        CloudApp backupApp = cloudAppService.get(backupHis.getAppId());
        MySQLHA cr = kubeClient.listCustomResource(MySQLHA.class, goalApp.getCrName(), goalApp.getNamespace());
        MySQLHASpec spec = cr.getSpec();
        //获取备份存储信息
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        HashMap<String, String> remote = new HashMap<>();
        if ("incre".equals(backupType)) {
            //增备文件恢复需要判断全备
            String baseTime = backupHis.getBaseTime();
            //查询全备历史   BASE_TIME = #{baseTime} and APP_TYPE = #{appType} and APP_ID = #{appId} and BACKUP_TYPE = #{backupType}
            BackupHis fullBackupHis = backupMapper.getBackupHisByBaseTime(SCHEMA, CLOUD_BACKUP_HIS, baseTime, "MySQL", backupHis.getAppId(), "full");
            String fullBakFile = fullBackupHis.getFileName();
            //修改cr
            String fullSourceName = backupApp.getNamespace() + "/" + backupApp.getCrName() + "/" + fullBakFile;
            String increSourceName = backupApp.getNamespace() + "/" + backupApp.getCrName() + "/" + xbkFilename;
            com.shindata.mysql.v1.mysqlhaspec.Restore restore = new com.shindata.mysql.v1.mysqlhaspec.Restore();
            restore.setFullSource(fullSourceName);
            restore.setIncrementalSource(increSourceName);
            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                remote.put("type", CloudAppConstant.OperatorStorageType.NFS);
                remote.put("address", cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath());
            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                remote.put("type", CloudAppConstant.StorageType.S3);
                remote.put("address", cloudBackupStorageVO.getServer());
                remote.put("bucket", cloudBackupStorageVO.getBucket());
                remote.put("region", cloudBackupStorageVO.getRegion());
                //获取operator的namespace，因为所有operator都相同，所以统一获取mysql的operatornamespace
                String operatorConfig = sysConfigService.findOne("operator.name", "MySQL");
                String operatorNamespace = operatorConfig.split("/")[0];
                remote.put("secret", operatorNamespace + ":backupstorage-secret");
            } else {
                throw new CustomException(600, "恢复失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
            }
//            Map<String, String> backupStorageInfo = getBackupStorageInfo();
//            restore.setType(backupStorageInfo.get("type"));
//            restore.setAddress(backupStorageInfo.get("url") + AppKind.MYSQL_HA.getKind() + "/");
            restore.setRemote(remote);
            spec.setRestore(restore);
            cr.setSpec(spec);
            kubeClient.updateCustomResource(cr, MySQLHA.class);
        } else {
            //修改cr
            String fullSourceName = backupApp.getNamespace() + "/" + backupApp.getCrName() + "/" + xbkFilename;
            com.shindata.mysql.v1.mysqlhaspec.Restore restore = new com.shindata.mysql.v1.mysqlhaspec.Restore();
            restore.setFullSource(fullSourceName);
            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                remote.put("type", CloudAppConstant.OperatorStorageType.NFS);
                remote.put("address", cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath());
            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                remote.put("type", CloudAppConstant.StorageType.S3);
                remote.put("address", cloudBackupStorageVO.getServer());
                remote.put("bucket", cloudBackupStorageVO.getBucket());
                remote.put("region", cloudBackupStorageVO.getRegion());
                //获取operator的namespace，因为所有operator都相同，所以统一获取mysql的operatornamespace
                String operatorConfig = sysConfigService.findOne("operator.name", "MySQL");
                String operatorNamespace = operatorConfig.split("/")[0];
                remote.put("secret", operatorNamespace + ":backupstorage-secret");
            } else {
                throw new CustomException(600, "恢复失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
            }
            restore.setRemote(remote);
            spec.setRestore(restore);
            cr.setSpec(spec);
            kubeClient.updateCustomResource(cr, MySQLHA.class);
        }

        try {
            // 创建定时任务轮询执行结果
            Map<String, String> dataMap = new HashMap<>(5);
            dataMap.put("restoreHisId", restoreHis.getRestoreHisId().toString());
            dataMap.put("kubeId", goalApp.getKubeId().toString());
            dataMap.put("changeHisId", changeId+"");
            dataMap.put("restoreTime", restoreTime);
            dataMap.put("podNames", podNamesStr);
            dataMap.put("primaryPodName", primaryPodName);
            dataMap.put("restoreNums", String.valueOf(podNames.size()));
            dataMap.put("fileName", xbkFilename);
            if (backupHis != null) {
                dataMap.put("startBinlogName", backupHis.getBinlogName());
                dataMap.put("binlogPos", backupHis.getBinlogPos());
                dataMap.put("backupAppId", backupHis.getAppId() + "");
                dataMap.put("backupHisId", backupHis.getBackupHisId() + "");
            }
            appCommonService.callScheduler(goalApp, StringUtils.isEmpty(goalApp.getCr()) ? goalApp.getCrRun() : goalApp.getCr(), dataMap, ActionEnum.RESTORE, MysqlBackupAndRestoreWatch.class, resourceChangeHis);
        } catch (Exception e) {
            log.error("[恢复]恢复调度创建失败", e);
            restoreReturn(restoreHis, changeId, "mysql恢复创建定时调度失败！", StatusConstant.FAIL);
        }

    }

    public List<PodDTO> getRestorePodMongoDB(CloudApp app, KubeClient kubeClient) {
        PodDTO primaryPod;
        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
        List<PodDTO> pods = kubeClient.listPod(app.getNamespace(), appKind.labelOfPod(app));
        if (CollectionUtils.isEmpty(pods)) {
            return null;
        }
        Optional<PodDTO> first = pods.stream().filter(pod -> "PRIMARY".equals(getMongoDbRole(kubeClient,
                pod.getNamespace(), pod.getPodName(), app.getCrName()))).findFirst();
        primaryPod = first.orElseGet(() -> pods.get(0));
        return Collections.singletonList(primaryPod);
    }

    /**
     * @return {"success":"yes/no","result":"执行结果/错误信息"}
     */
    public Map<String, String> execCmd(KubeClient client, String namespace, String podName, String container, String... cmd) {
        Map<String, String> map = new HashMap<>();
        try {
            // default container
            if (StringUtils.isEmpty(container)) {
                Pod pod = client.getPod(namespace, podName);
                container = pod.getSpec().getContainers().get(0).getName();
            }
            String result = client.execCmd(namespace, podName, container, 10 * 60, cmd);

            map.put("success", "yes");
            map.put("result", result);
            return map;
        } catch (Exception e) {
            map.put("success", "no");
            map.put("result", e.toString());
            return map;
        }
    }
    /**
     *
     * @param client
     * @param pod
     * @param container
     * @param data
     * @param cmd
     * @return {"success":"yes/no","result":"执行结果/错误信息"}
     */
    public Map<String, String> execCmd(KubernetesClient client, Pod pod, String container, CompletableFuture<String> data, String... cmd) {
        Map<String, String> map = new HashMap<>();
        try {
            // default container
            if (StringUtils.isEmpty(container)) {
                container = pod.getSpec().getContainers().get(0).getName();
            }
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ByteArrayOutputStream baos_ = new ByteArrayOutputStream();
            ExecWatch result = client.pods()
                    .inNamespace(pod.getMetadata().getNamespace())
                    .withName(pod.getMetadata().getName())
                    .inContainer(container)
                    .writingOutput(baos)
                    .writingError(baos)
                    .usingListener(new KubeExecSimpleListener(data, baos, baos_))
                    .exec(cmd);
            map.put("success", "yes");
            map.put("result", data.get(10 * 60, TimeUnit.SECONDS));
            return map;
        } catch (Exception e) {
            map.put("success", "no");
            map.put("result", e.toString());
            return map;
        }
    }


    @Async
    public void redisClusterBackup(BackupHisVO backupHis) {
        BackupHis runningBackupHis = backupMapper.getRunningByAppId(SCHEMA, CLOUD_BACKUP_HIS, backupHis.getAppId());
        if (runningBackupHis != null) {
            throw new CustomException(600, "存在正在执行的备份，请勿重复提交！");
        }
        String checkStorageResult = backupHis.getMessage();

        /*
         * 1.拿到所有的从节点
         * 2.遍历三个从节点依次执行备份
         * 3.调度，通过创建时间和备份文件的个数（备份节点的个数）去判断是否文件缺少
         * 4.当文件不缺少时，将所有文件打成tar包，记录备份信息
         * */
        // 0. 定义变量
        //所有pod
//        List<PodDTO> pods = new ArrayList<PodDTO>();
        //从pod
//        PodDTO slavePod = null;
        //时间转换
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
        //创建备份开始时间
        Date backupStartDate = new Date();
        //获取app信息
        CloudApp app = cloudAppMapper.selectByPrimaryKey(backupHis.getAppId(), DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_APP);
        //操作记录
        ResourceChangeHis resourceChangeHis = null;
        Integer changeId = null;
        // 1. 保存备份历史基本信息
        //当前时间
        Timestamp startTime = new Timestamp(System.currentTimeMillis());
        //备份类型：全备或增备
        backupHis.setBackupType("full");
        //开始时间
        backupHis.setStartTime(startTime);
        //设置备份状态为运行
        backupHis.setStatus(StatusConstant.RUNNING);
        //应用所属集群
        KubeConfig byId = kubeConfigMapper.getById(DatasourceConstant.SCHEMA, CLOUD_KUBE_CONFIG_TABLE, app.getKubeId());
        if (null == byId) {
            log.error("未获取到集群！");
//            backupFailUpdateHis(backupHis, "未获取到集群！");
            backupReturn(backupHis, null, StatusConstant.FAIL, "", "redis备份未获取到集群！");
            return;
        }
        backupHis.setKubeName(byId.getName());
        //插入备份历史
        backupMapper.commitBackup(SCHEMA, CLOUD_BACKUP_HIS, backupHis);
        resourceChangeHis = new ResourceChangeHis() {{
            setInsertTime(startTime);
            setKind(app.getKind());
            setKubeId(app.getKubeId());
            setNamespace(app.getNamespace());
            setCommand("备份");
            setStatus("2");
            setAction("Backup");
            setMsg("备份中...".concat("\n").concat(null == checkStorageResult ? "" : checkStorageResult));
            setAppId(app.getId());
            setAppName(app.getName());
            setKubeName(byId.getName());
            setYaml(app.getCr());
            setUserName(UserUtil.getAsyncUserinfo().getUsername());
            setUserIp(CloudRequestContext.getContext().getUserIp());
            setLastEndTimestamp(System.currentTimeMillis());
            setAppLogicId(app.getLogicAppId());
        }};
        changeId = insertResourceChangeHis(resourceChangeHis);

        // 2. 根据appid获取kubeclient
        //创建k8s客户端
        KubeClient kubeClient = kubeClientService.get(app.getKubeId()); //获取所有节点
        // 3.获取cr，根据cr获取到shards属性，从每个nodes里面取一个ip执行备份
        String podNameListStr = getBackupPodRedisCluster(app, kubeClient).stream().map(PodDTO::getPodName).collect(Collectors.joining(","));
        if (StringUtils.isEmpty(podNameListStr)) {
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "未能确定进行备份地实例");
            return;
        }

        String backupDirName = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        log.info("[redis-cluster备份]：备份路径名称：" + backupDirName);
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        String backupScript = "";
        if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
            String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
            backupScript = "bash /scripts/redis-backup.sh " + backupDirName + " " + CloudAppConstant.OperatorStorageType.NFS + " " + mountPath;
        } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
            String region = cloudBackupStorageVO.getRegion();
            if (StringUtils.isEmpty(region)) {
                region = "\"\"";
            }
            backupScript = "bash /scripts/redis-backup.sh "
                    + backupDirName
                    + " " + CloudAppConstant.StorageType.S3
                    + " " + cloudBackupStorageVO.getServer()
                    + " " + region
                    + " " + cloudBackupStorageVO.getBucket()
                    + " " + cloudBackupStorageVO.getAccessKey()
                    + " " + cloudBackupStorageVO.getSecretKey();
        } else {
            throw new CustomException(600, "备份失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
        }

        log.info("[redis-cluster备份]：执行的备份命令：" + backupScript);
        for (String podName : podNameListStr.split(",")) {
            try {
                kubeClient.execCmdOneway(app.getNamespace(), podName, "redis", "sh", "-c", backupScript);
            } catch (Exception e) {
                backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "redis备份失败！错误信息：" + e.getMessage());
                return;
            }
        }

        try {
            //备份节点
            JSONObject messageObj = new JSONObject();
            backupHis.setPodName(podNameListStr);
            //基础时间
            String baseTime = sdf.format(backupStartDate).replaceAll(":", "").replaceAll("-", "").replaceAll(" ", "");
            backupHis.setBaseTime(baseTime);
            RedisCluster cr = kubeClient.listCustomResource(RedisCluster.class, app.getCrName(), app.getNamespace());
            Integer masterSize = cr.getSpec().getMasterSize();
            //记录备份信息：数据库名称、备份路径、备份文件名称
            messageObj.put("backupFtpPath", this.getFtpBackupPath(app));
            messageObj.put("masterSize", masterSize);
            String messageJsonStr = JSON.toJSONString(messageObj);
            backupHis.setMessage(messageJsonStr);
            backupMapper.updateBackupHis(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_HIS, backupHis);
            //创建定时轮询备份结果
            Map map = new HashMap();
            map.put("backupFilename", backupDirName);
            map.put("backupDirName", AppUtil.getBackupContainerPath(AppKind.Redis_Cluster));
            map.put("backupStartDateSDF", sdf.format(backupStartDate));
            map.put("backupHisId", backupHis.getBackupHisId());
            map.put("changeId", changeId);
            map.put("backupPodName", podNameListStr);
            map.put("backupFtpPath", this.getFtpBackupPath(app));
            appCommonService.callScheduler(app, app.getCr(), map, ActionEnum.BACKUP, RedisClusterBackupAndRestoreWatch.class, resourceChangeHis);
        } catch (Exception e) {
            //备份失败
//            backupFailUpdateHis(backupHis, e.toString());
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "redis备份失败！错误信息：" + e.getMessage());
            return;
        }

    }

    private List<PodDTO> getBackupPodRedisCluster(CloudApp app, KubeClient kubeClient) {
        //        List<String> salveList=new ArrayList<>();
//        List<String> masterList=new ArrayList<>();
        List<String> podIpList=new ArrayList<>();
        RedisCluster redisCluster = kubeClient.listCustomResource(RedisCluster.class, app.getCrName(), app.getNamespace());
        List<Shards> shards = redisCluster.getStatus().getShards();
        for (int i = 0; i < shards.size(); i++) {
            List<String> nodes = shards.get(i).getNodes();
            if (nodes.size() <= 1) {
                throw new IllegalStateException("cannot determine the slave server");
            }
            podIpList.add(redisPodService.announceIpMapping(redisCluster, nodes.get(1))); // 格式：ip:port
        }
//        try {
//            //根据命名空间和label获取所有pod
//            AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
//            pods = kubeClient.listPod(app.getNamespace(), appKind.labelOfPod(app));
//            if (pods == null || pods.size() == 0){
//                backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "当前app："+app.getName()+"没有pods！");
//                return;
//            }
//        } catch (Exception e) {
//            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "redis备份获取所有pods失败！");
//            return;
//        }
//        //根据pod和salveList过滤出从节点的pod
//        List<PodDTO> slavePods= pods.stream().filter(podDTO -> salveList.contains(podDTO.getPodIp())).collect(Collectors.toList());
//        //判断从节点是否为空
//        if(slavePods.size()==0){
////            backupFailUpdateHis(backupHis, "当前app："+app.getName()+"未获取到从节点！");
//            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "当前app："+app.getName()+"未获取到从节点！");
//            return;
//        }

        //遍历分片执行备份脚本
        //记录podName
        List<PodDTO> pods = kubeClient.listPod(app.getNamespace(), AppKind.Redis_Cluster.labelOfPod(app));
        return pods.stream().filter(podDTO -> podIpList.contains(podDTO.getPodIp())).collect(Collectors.toList());
    }

    private String agentIp(String str){
        String regular = "\\d{3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}";
        Pattern pattern = Pattern.compile(regular);
        Matcher matcher = pattern.matcher(str);
        if (matcher.find()){
            return matcher.group();
        }
        return "未匹配到agent中的ip";
    }


    /**
     * redis恢复
     * @param backupHis
     */
    @Async
    public void redisClusterRestore(BackupHis backupHis, Integer goalAppId) {
        // 1. 保存恢复基本信息
        // 2. 将备份文件存放在对应集群的所有节点上
        // 3. 修改对应cr的RestoreFile属性为备份文件名称

        //检查备份文件是否适用于目标应用

        // 0. 创建还原的对象
        RestoreHis restoreHis = new RestoreHis();
        restoreHis.setStatus(StatusConstant.RUNNING);
        Date startDate = new Date();
        //时间转换
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
        //操作记录
        ResourceChangeHis resourceChangeHis = null;
        Integer changeId = null;
        try{
            Timestamp startTime = new Timestamp(System.currentTimeMillis());
            restoreHis.setStartTime(startTime);
            // 1. 插入基础恢复历史和操作记录
            Integer backupAppId = backupHis.getAppId();
            //到这里了
            CloudApp goalApp = cloudAppMapper.selectByPrimaryKey(goalAppId, DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_APP);
            CloudApp backupApp = cloudAppMapper.selectByPrimaryKey(backupAppId, DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_APP);
            restoreHis.setAppId(goalAppId);
            restoreHis.setAppName(goalApp.getName());
            restoreHis.setAppType(goalApp.getKind());
            KubeClient kubeClient = kubeClientService.get(goalApp.getKubeId());
            RedisCluster cr = kubeClient.listCustomResource(RedisCluster.class, goalApp.getCrName(), goalApp.getNamespace());
            //获取备份时的podName,实际是随机选择节点进行恢复
            //String podName = backupHis.getPodName();
            kubeClient = kubeClientService.get(goalApp.getKubeId()); //获取所有节点
            //获取cr，根据cr获取到status属性中的node数组，取到每个数组中第一个ip作为主节点执行恢复脚本
            restoreHis.setPodName(getRestorePodNameRedisCluster(goalApp, kubeClient));
            //应用所属集群
            KubeConfig byId = kubeConfigMapper.getById(DatasourceConstant.SCHEMA, CLOUD_KUBE_CONFIG_TABLE, goalApp.getKubeId());
            if(null == byId){
                log.error("未获取到集群！");
//                backupFailUpdateHis(backupHis, "未获取到集群！");
                restoreReturn(restoreHis, changeId, "未获取到集群！", StatusConstant.FAIL);
                return;
            }
            restoreHis.setKubeName(byId.getName());
            restoreHis.setMessage("恢复中...");
            restoreHis.setFileName(backupHis.getFileName());
            String backDirInPod = "/backup/";
            restoreHis.setRestoreDir(backDirInPod);
            restoreHis.setFileDeleted(false);
            //插入基本信息
            int insertNum = restoreMapper.commitRestore(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_RESTORE_HIS, restoreHis);
            //插入操作记录
            resourceChangeHis = new ResourceChangeHis(){{
                setInsertTime(startTime);
                setKind(goalApp.getKind());
                setKubeId(goalApp.getKubeId());
                setNamespace(goalApp.getNamespace());
                setCommand("恢复");
                setStatus("2");
                setAction(ActionEnum.RESTORE.getActionType());
                setMsg("恢复中...");
                setAppId(goalApp.getId());
                setAppName(goalApp.getName());
                setKubeName(byId.getName());
                setYaml(goalApp.getCr());
                setUserName(UserUtil.getAsyncUserinfo().getUsername());
                setUserIp(CloudRequestContext.getContext().getUserIp());
                setLastEndTimestamp(System.currentTimeMillis());
                setAppLogicId(goalApp.getLogicAppId());
            }};
            changeId = insertResourceChangeHis(resourceChangeHis);

            checkRestoreAndRecord(resourceChangeHis, goalApp, backupHis, restoreHis);
            goalApp.setStatus(CloudAppConstant.AppStatus.PENDING);
            cloudAppService.update(goalApp);

            // 3. 获取备份文件名称,修改对应的cr属性：RestoreFile
            //备份文件名称
            String backupFileName = backupHis.getFileName();
            //构造cr
            RedisClusterSpec spec = cr.getSpec();

            // 设置restore属性
            com.shindata.redis.v1.redisclusterspec.Restore restore = new com.shindata.redis.v1.redisclusterspec.Restore();
            String fullSourceName = backupApp.getNamespace() + "/" + backupApp.getCrName() + "/" + backupFileName;
            restore.setFullSource(fullSourceName);
            //获取备份存储信息
            CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
            HashMap<String, String> remote = new HashMap<>();
            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                remote.put("type", CloudAppConstant.OperatorStorageType.NFS);
                remote.put("address", cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath());
            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                remote.put("type", CloudAppConstant.StorageType.S3);
                remote.put("address", cloudBackupStorageVO.getServer());
                remote.put("bucket", cloudBackupStorageVO.getBucket());
                remote.put("region", cloudBackupStorageVO.getRegion());
                //获取operator的namespace，因为所有operator都相同，所以统一获取mysql的operatornamespace
                String operatorConfig = sysConfigService.findOne("operator.name", "MySQL");
                String operatorNamespace = operatorConfig.split("/")[0];
                remote.put("secret", operatorNamespace + ":backupstorage-secret");
            } else {
                throw new CustomException(600, "恢复失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
            }
            restore.setRemote(remote);
            spec.setRestore(restore);
            cr.setSpec(spec);
            kubeClient.updateCustomResource(cr, RedisCluster.class);
            //创建定时轮询备份结果
            Map map = new HashMap();
            map.put("restoreStartDateSDF", sdf.format(startDate));
            map.put("restoreHisId", restoreHis.getRestoreHisId());
            map.put("resourceChangeId", changeId);
            appCommonService.callScheduler(goalApp,YamlEngine.marshal(cr),map,ActionEnum.RESTORE,RedisClusterBackupAndRestoreWatch.class,resourceChangeHis);
        }catch (Exception e){
            restoreReturn(restoreHis, changeId, e.getMessage(), StatusConstant.FAIL);
        }
    }

    public String getRestorePodNameRedisCluster(CloudApp goalApp, KubeClient kubeClient) {
        List<String> masterList=new ArrayList<>();
        RedisCluster redisCluster = kubeClient.listCustomResource(RedisCluster.class, goalApp.getCrName(), goalApp.getNamespace());
        List<Shards> shards = redisCluster.getStatus().getShards();
        for (int i = 0; i < shards.size(); i++) {
            masterList.add(redisPodService.announceIpMapping(redisCluster ,shards.get(i).getNodes().get(0)));
        }
        List<PodDTO> pods = new ArrayList<>();
        List<PodDTO> masterPods=new ArrayList<>();
        List<String> masterPodsName=new ArrayList<>();
        AppKind appKind = AppKind.valueOf(goalApp.getKind(), goalApp.getArch());
        pods = kubeClient.listPod(goalApp.getNamespace(), appKind.labelOfPod(goalApp));
        masterPods= pods.stream().filter(podDTO -> masterList.contains(podDTO.getPodIp())).collect(Collectors.toList());
        for (int i = 0; i < masterPods.size(); i++) {
            masterPodsName.add(masterPods.get(i).getPodName());
        }
        String podName = masterPodsName.toString().replaceAll("]", "").replaceAll("\\[", "");
        return podName;
    }

    /**
     * Redis备份
     *
     * @param backupHis
     */
    @Async
    public void redisBackup(BackupHisVO backupHis) {
        BackupHis runningBackupHis = backupMapper.getRunningByAppId(SCHEMA, CLOUD_BACKUP_HIS, backupHis.getAppId());
        if (runningBackupHis != null) {
            throw new CustomException(600, "存在正在执行的备份，请勿重复提交！");
        }
        String checkStorageResult = backupHis.getMessage();
        // 0. 定义变量
        //所有pod
        List<PodDTO> pods = new ArrayList<PodDTO>();
        //从pod
        PodDTO slavePod = null;
        //时间转换
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
        //创建备份开始时间
        Date backupStartDate = new Date();
        //获取app信息
        CloudApp app = cloudAppMapper.selectByPrimaryKey(backupHis.getAppId(), DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_APP);
        //操作记录
        ResourceChangeHis resourceChangeHis = null;
        Integer changeId = null;
        // 1. 保存备份历史基本信息
        //当前时间
        Timestamp startTime = new Timestamp(System.currentTimeMillis());
        //备份类型：全备或增备
        backupHis.setBackupType("full");
        //开始时间
        backupHis.setStartTime(startTime);
        //设置备份状态为运行
        backupHis.setStatus(StatusConstant.RUNNING);
        //应用所属集群
        KubeConfig byId = kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, app.getKubeId());
        if (null == byId) {
            log.error("未获取到集群！");
//            backupFailUpdateHis(backupHis, "未获取到集群！");
            backupReturn(backupHis, null, StatusConstant.FAIL, "", "redis备份未获取到集群！");
            return;
        }
        backupHis.setKubeName(byId.getName());
        //插入备份历史
        backupMapper.commitBackup(SCHEMA, CLOUD_BACKUP_HIS, backupHis);
        resourceChangeHis = new ResourceChangeHis() {{
            setInsertTime(startTime);
            setKind(app.getKind());
            setKubeId(app.getKubeId());
            setNamespace(app.getNamespace());
            setCommand("备份");
            setStatus("2");
            setAction("Backup");
            setMsg("备份中...".concat("\n").concat(null == checkStorageResult ? "" : checkStorageResult));
            setAppId(app.getId());
            setAppName(app.getName());
            setKubeName(byId.getName());
            setYaml(app.getCr());
            setUserName(UserUtil.getAsyncUserinfo().getUsername());
            setUserIp(CloudRequestContext.getContext().getUserIp());
            setLastEndTimestamp(System.currentTimeMillis());
            setAppLogicId(app.getLogicAppId());
        }};
        changeId = insertResourceChangeHis(resourceChangeHis);

        // 2. 根据appid获取kubeclient
        //创建k8s客户端
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());

        // 3. 获取从节点
        //获取所有节点
        slavePod = getBackupPodRedis(app, kubeClient);
        //判断是否获取了一个从节点
        if (null == slavePod) {
//            backupFailUpdateHis(backupHis, "当前app："+app.getName()+"未获取到从节点！");
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "当前app：" + app.getName() + "未获取到从节点！");
            return;
        }
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        String backupScript = "";
        if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
            String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
            backupScript = "bash /scripts/redis-backup.sh " + CloudAppConstant.OperatorStorageType.NFS + " " + mountPath;
        } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
            String region = cloudBackupStorageVO.getRegion();
            if (StringUtils.isEmpty(region)) {
                region = "\"\"";
            }
            backupScript = "bash /scripts/redis-backup.sh "
                    + CloudAppConstant.StorageType.S3
                    + " " + cloudBackupStorageVO.getServer()
                    + " " + region
                    + " " + cloudBackupStorageVO.getBucket()
                    + " " + cloudBackupStorageVO.getAccessKey()
                    + " " + cloudBackupStorageVO.getSecretKey();
        } else {
            throw new CustomException(600, "备份失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
        }
        //备份文件位置
        String backupFilePath = "";
//        String backupFilePath = "../data/"+app.getKind()+"/"+app.getNamespace()+"/"+ app.getCrName();
        //获取pod的主机地址
        String hostIP = slavePod.getPod().getStatus().getHostIP();
        //获取pod中的备份路径
        String backupPodPath = AppUtil.getBackupContainerPath(AppKind.Redis);
        if ("".equals(backupPodPath)) {
            //备份失败
//            backupFailUpdateHis(backupHis, "获取节点备份路径失败！");
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "获取节点备份路径失败！");
            return;
        }
//        List<Volume> volumes = slavePod.getPod().getSpec().getVolumes();
//        //遍历寻找
//        for (Volume everyVo : volumes) {
//            if("backup".equalsIgnoreCase(everyVo.getName())){
//                //是备份相关信息，获取备份路径
//                backupFilePath = everyVo.getHostPath().getPath();
//                break;
//            }
//        }
//        if("".equalsIgnoreCase(backupFilePath)){
////            backupFailUpdateHis(backupHis, "获取备份路径失败！");
//            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "redis备份获取备份路径失败！");
//            return;
//        }
//        //寻找文件的目录命令
//        String findBackupFileScript = "ls "+backupFilePath;
        try {

            // 4. 使用从节点执行备份操作
            //备份
            kubeClient.execCmdOneway(slavePod.getPod().getMetadata().getNamespace(), slavePod.getPod().getMetadata().getName(), "redis", "sh", "-c", backupScript);

            //判断信息中是否存在错误信息
//            if(!backupRes.contains("success")){
////                backupFailUpdateHis(backupHis, "Redis备份失败！");
//                backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "redis备份失败！");
//                return;
//            }
            //备份节点
            backupHis.setPodName(slavePod.getPod().getMetadata().getName());
            //基础时间
            String baseTime = sdf.format(backupStartDate).replaceAll(":", "").replaceAll("-", "").replaceAll(" ", "");
            backupHis.setBaseTime(baseTime);
            //记录备份信息：数据库名称、备份路径、备份文件名称
            JSONObject messageObj = new JSONObject();
            messageObj.put("backupIp", hostIP);
            messageObj.put("backupPath", backupFilePath);// k8s集群宿主机备份路径
            messageObj.put("backupFtpPath", this.getFtpBackupPath(app));
            String messageJsonStr = JSON.toJSONString(messageObj);
            backupHis.setMessage(messageJsonStr);
            backupMapper.updateBackupHis(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_HIS, backupHis);
            //创建定时轮询备份结果
            Map map = new HashMap();
            map.put("backupIp", hostIP);
            map.put("backupPodPath", backupPodPath);
            map.put("backupFtpPath", this.getFtpBackupPath(app));

//            map.put("masterUsername", masterUsername);
//            map.put("masterUsername", masterUsername);
//            map.put("masterPassword", masterPassword);
//            map.put("findBackupFileScript", findBackupFileScript);
            map.put("backupStartDateSDF", sdf.format(backupStartDate));
            map.put("backupHisId", backupHis.getBackupHisId());
            map.put("changeId", changeId);
            appCommonService.callScheduler(app, app.getCr(), map, ActionEnum.BACKUP, RedisBackupAndRestoreWatch.class, resourceChangeHis);
        } catch (Exception e) {
            //备份失败
//            backupFailUpdateHis(backupHis, e.toString());
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "redis备份失败！错误信息：" + e.getMessage());
            return;
        }
    }

    @Autowired
    @Lazy
    private RedisService redisService;

    private PodDTO getBackupPodRedis(CloudApp app, KubeClient kubeClient) {
        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
        List<PodDTO> pods = kubeClient.listPod(app.getNamespace(), appKind.labelOfPod(app));
        if (pods != null && pods.size() == 1) return pods.get(0);

        Optional<PodDTO> first = pods.stream().filter(everyPodDTO -> {
            //获取密码
            String userPassword = redisService.getPassword(app);
            String redisDbRole = getRedisDbRole(kubeClient, everyPodDTO.getPod().getMetadata().getNamespace(), everyPodDTO.getPod().getMetadata().getName(), userPassword);
            return "slave".equalsIgnoreCase(redisDbRole);
        }).findFirst();
        return first.orElseThrow(() -> new IllegalStateException("未能确定进行备份的从库"));
    }

    /**
     * redis恢复
     * @param backupHis
     */
    @Async
    public void redisRestore(BackupHis backupHis, Integer goalAppId) {
        // 1. 保存恢复基本信息
        // 2. 将备份文件存放在对应集群的所有节点上
        // 3. 修改对应cr的RestoreFile属性为备份文件名称

        // 0. 创建还原的对象
        RestoreHis restoreHis = new RestoreHis();
        restoreHis.setStatus(StatusConstant.RUNNING);
        Date startDate = new Date();
        //时间转换
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
        //操作记录
        ResourceChangeHis resourceChangeHis = null;
        Integer changeId = null;
        try{
            Timestamp startTime = new Timestamp(System.currentTimeMillis());
            restoreHis.setStartTime(startTime);
            // 1. 插入基础恢复历史和操作记录
            Integer backupAppId = backupHis.getAppId();
            //到这里了
            CloudApp goalApp = cloudAppMapper.selectByPrimaryKey(goalAppId, DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_APP);
            CloudApp backupApp = cloudAppMapper.selectByPrimaryKey(backupAppId, DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_APP);
            restoreHis.setAppId(goalAppId);
            restoreHis.setAppName(goalApp.getName());
            restoreHis.setAppType(goalApp.getKind());
            KubeClient kubeClient = kubeClientService.get(goalApp.getKubeId());
            Redis cr = kubeClient.listCustomResource(Redis.class, goalApp.getCrName(), goalApp.getNamespace());

            //应用所属集群
            KubeConfig byId = kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, goalApp.getKubeId());
            if(null == byId){
                log.error("未获取到集群！");
//                backupFailUpdateHis(backupHis, "未获取到集群！");
                restoreReturn(restoreHis, changeId, "未获取到集群！", StatusConstant.FAIL);
                return;
            }
            restoreHis.setKubeName(byId.getName());
            restoreHis.setMessage("恢复中...");
            restoreHis.setFileDeleted(false);
            String backDirInPod = "/backup/";
            restoreHis.setRestoreDir(backDirInPod);
            restoreHis.setFileName(backupHis.getFileName());

            //插入操作记录
            resourceChangeHis = new ResourceChangeHis(){{
                setInsertTime(startTime);
                setKind(goalApp.getKind());
                setKubeId(goalApp.getKubeId());
                setNamespace(goalApp.getNamespace());
                setCommand("恢复");
                setStatus("2");
                setAction(ActionEnum.RESTORE.getActionType());
                setMsg("恢复中...");
                setAppId(goalApp.getId());
                setAppName(goalApp.getName());
                setUserName(UserUtil.getAsyncUserinfo().getUsername());
                setUserIp(CloudRequestContext.getContext().getUserIp());
                setKubeName(byId.getName());
                setYaml(goalApp.getCr());
                setLastEndTimestamp(System.currentTimeMillis());
                setAppLogicId(goalApp.getLogicAppId());
            }};
            changeId = insertResourceChangeHis(resourceChangeHis);

            String podName = getRestorePodNameRedis(goalApp, kubeClient);
            if (podName == null) {
                restoreReturn(restoreHis, changeId, "当前app：" + goalApp.getName() + "获取恢复pod名称失败", StatusConstant.FAIL);
                return;
            }
            restoreHis.setPodName(podName);
            //插入基本信息
            restoreMapper.commitRestore(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_RESTORE_HIS, restoreHis);
            goalApp.setStatus(CloudAppConstant.AppStatus.PENDING);
            cloudAppService.update(goalApp);
            checkRestoreAndRecord(resourceChangeHis, goalApp, backupHis, restoreHis);

            // 3. 获取备份文件名称,修改对应的cr属性：RestoreFile
            //备份文件名称
            String backupFileName = backupHis.getFileName();
            //构造cr
            RedisSpec spec = cr.getSpec();
            //设置恢复文件在ftp的全路径
//            JSONObject mesObj = JSONObject.parseObject(backupHis.getMessage());
//            String backupFtpPath = mesObj.getString("ftpBackupPath");
//            spec.setRestoreFile(backupFtpPath + "/" + backupFileName);
//            spec.getFtp().setUrl(ftpUtil.getURL());

            String fullSourceName = backupApp.getNamespace() + "/" + backupApp.getCrName() + "/" + backupFileName;
            com.shindata.redis.v1.redisspec.Restore restore = new com.shindata.redis.v1.redisspec.Restore();
            restore.setFullSource(fullSourceName);
            //获取备份存储信息
            CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
            HashMap<String, String> remote = new HashMap<>();
            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                remote.put("type", CloudAppConstant.OperatorStorageType.NFS);
                remote.put("address", cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath());
            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                remote.put("type", CloudAppConstant.StorageType.S3);
                remote.put("address", cloudBackupStorageVO.getServer());
                remote.put("bucket", cloudBackupStorageVO.getBucket());
                remote.put("region", cloudBackupStorageVO.getRegion());
                //获取operator的namespace，因为所有operator都相同，所以统一获取mysql的operatornamespace
                String operatorConfig = sysConfigService.findOne("operator.name", "MySQL");
                String operatorNamespace = operatorConfig.split("/")[0];
                remote.put("secret", operatorNamespace + ":backupstorage-secret");
            } else {
                throw new CustomException(600, "恢复失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
            }
            restore.setRemote(remote);
            spec.setRestore(restore);
            cr.setSpec(spec);
            kubeClient.updateCustomResource(cr, Redis.class);
            //创建定时轮询备份结果
            Map map = new HashMap();
            map.put("restoreStartDateSDF", sdf.format(startDate));
            map.put("restoreHisId", restoreHis.getRestoreHisId());
            map.put("resourceChangeId", changeId);
            appCommonService.callScheduler(goalApp, YamlEngine.marshal(cr),map,ActionEnum.RESTORE,RedisBackupAndRestoreWatch.class,resourceChangeHis);
        }catch (Exception e){
            restoreReturn(restoreHis, changeId, e.getMessage(), StatusConstant.FAIL);
        }
    }

    public String getRestorePodNameRedis(CloudApp goalApp, KubeClient kubeClient) {
        // 2. 获取所有可能恢复的pod
        List<PodDTO> pods = new ArrayList<>();
        //获取所有节点
        AppKind appKind = AppKind.valueOf(goalApp.getKind(), goalApp.getArch());
        pods = kubeClient.listPod(goalApp.getNamespace(), appKind.labelOfPod(goalApp));
        return pods.stream().map(PodDTO::getPodName).collect(Collectors.joining(","));
    }

    /**
     * 检查备份与恢复是否超时，超时时间为5分钟
     * @param backupStartDate
     * @return
     */
    public boolean checkBackupAndRestoreTimeout(Date backupStartDate,Date backupEndDate, int outTimeHour){
        long second = (backupEndDate.getTime() - backupStartDate.getTime());
        log.info("开始时间与结束时间的计算值:  开始时间：" + backupStartDate + " 结束时间：" + backupEndDate + " 计算结果(毫秒)：" + second);
        return (outTimeHour * 60 * 60 * 1000) <= second;
    }

    /**
     * openGauss备份
     *
     * @param backupHis
     */
    public void ogBackup(BackupHisVO backupHis) {
        // 1.判断是否有正在备份的操作
        BackupHis runningBackupHis = backupMapper.getRunningByAppId(SCHEMA, CLOUD_BACKUP_HIS, backupHis.getAppId());
        if (runningBackupHis != null) {
            throw new CustomException(600, "存在正在执行的备份，请勿重复提交！");
        }
        //如果是增备，对是否做过全备做校验
        //根据baseTime递归检查全备以及增备是否存在
        Integer appId = backupHis.getAppId();
        if ("incre".equalsIgnoreCase(backupHis.getBackupType())) {
            checkBackupState(appId);
        }
        // 2.保存APP状态
        CloudApp cloudApp = cloudAppService.get(appId);
        //修改应用状态
        cloudApp.setStatus(CloudAppConstant.AppStatus.PENDING);
        cloudAppService.update(cloudApp);
        // 3.保存备份历史
        JSONObject messageObj = new JSONObject();
        messageObj.put("msg", "备份中...");
        Timestamp startTime = new Timestamp(System.currentTimeMillis());
        backupHis.setStartTime(startTime);
        backupHis.setStatus(StatusConstant.RUNNING);
        backupHis.setMessage(JSON.toJSONString(messageObj));
        //应用所属集群
        KubeConfig byId = kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, cloudApp.getKubeId());
        if (null == byId) {
            log.error("未获取到集群！");
            backupReturn(backupHis, null, StatusConstant.FAIL, "", "未获取到集群！");
            return;
        }
        backupHis.setKubeName(byId.getName());
        backupMapper.commitBackup(SCHEMA, CLOUD_BACKUP_HIS, backupHis);

        // 4.保存操作记录
        UserInfo userInfo = UserUtil.getAsyncUserinfo();
        ResourceChangeHis resourceChangeHis = new ResourceChangeHis() {{
            setInsertTime(startTime);
            setKind(cloudApp.getKind());
            setKubeId(cloudApp.getKubeId());
            setNamespace(cloudApp.getNamespace());
            setCommand("备份");
            setStatus("2");
            setAction("Backup");
            setMsg("备份中...");
            setAppId(cloudApp.getId());
            setAppLogicId(cloudApp.getLogicAppId());
            setAppName(cloudApp.getName());
            setKubeName(byId.getName());
            setYaml(cloudApp.getCr());
            setUserName(userInfo.getUsername());
            setUserIp(CloudRequestContext.getContext().getUserIp());
            setLastEndTimestamp(System.currentTimeMillis());
        }};
        Integer changeId = insertResourceChangeHis(resourceChangeHis);

        // 5.找到主节点
        KubeClient client = kubeClientService.get(cloudApp.getKubeId());
        PodDTO primaryPod = getBackupPodOpengauss(cloudApp, client);
        //判断是否找到了一个主节点
        if (null == primaryPod) {
            log.error("获取主节点失败！");
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "获取主节点失败！");
            return;
        } else {
            //保存podName
            BackupHis updateBackupHis = new BackupHis();
            updateBackupHis.setBackupHisId(backupHis.getBackupHisId());
            updateBackupHis.setPodName(primaryPod.getPod().getMetadata().getName());
            backupMapper.updateBackupHis(SCHEMA, CLOUD_BACKUP_HIS, updateBackupHis);
        }
        String primary = primaryPod.getPodName();

        // 6.mount目录到远程服务器
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        String mountCmd = "";
        if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
            mountCmd = "sh /scripts/mount-remote-storage.sh true /gaussdata/openGauss/backup "
                    + CloudAppConstant.OperatorStorageType.NFS
                    + " " + cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
        } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
            mountCmd = String.format("sh %s %s %s %s %s '%s' %s %s %s",
                    "/scripts/mount-remote-storage.sh", true, "/gaussdata/openGauss/backup",
                    CloudAppConstant.StorageType.S3,
                    cloudBackupStorageVO.getServer(),
                    cloudBackupStorageVO.getRegion(),
                    cloudBackupStorageVO.getBucket(),
                    cloudBackupStorageVO.getAccessKey(),
                    cloudBackupStorageVO.getSecretKey());

        }
        try {
            client.execCmd(cloudApp.getNamespace(), primary, "mount", "sh", "-c", mountCmd);
        } catch (Exception e) {
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "mount远程服务失败！错误信息为：" + e);
            return;
        }

        // 7.提交备份
        String backupType = "FULL";
        if ("incre".equalsIgnoreCase(backupHis.getBackupType())) {
            backupType = "PTRACK";
        }
        String backupCmd = "sh /scripts/sidecar-backup.sh " + backupType + " backup_" + backupHis.getBackupHisId();
        try {
            client.execCmdOneway(cloudApp.getNamespace(), primary, "sidecar", "sh", "-c", backupCmd);
        } catch (Exception e) {
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "提交备份失败！错误信息为：" + e);
            return;
        }

        // 8.创建定时轮询
        Map<String, String> jobDataMap = new HashMap<>();

        try {
            // 创建定时任务轮询执行结果
            jobDataMap.put("backupHisId", backupHis.getBackupHisId().toString());
            jobDataMap.put("kubeId", cloudApp.getKubeId().toString());
            jobDataMap.put("changeId", String.valueOf(changeId));
            jobDataMap.put("primaryPodName", primary);
            jobDataMap.put("appId", String.valueOf(appId));
            jobDataMap.put("backupTimeout", String.valueOf(backupHis.getMaxBackupDuration()));
            jobDataMap.put("userInfo", JsonUtil.toJson(userInfo));
            log.info("备份定时用户信息：" + JsonUtil.toJson(userInfo));
            appCommonService.callScheduler(cloudApp, cloudApp.getCr(), jobDataMap, ActionEnum.BACKUP, OpenGaussBackupAndRestoreWatch.class, resourceChangeHis);
        } catch (Exception e) {
            log.error(e.getMessage());
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "备份创建定时调度失败！错误信息：" + e.getMessage());
            return;
        }
    }

    public PodDTO getBackupPodOpengauss(CloudApp app, KubeClient kubeClient) {
        List<PodDTO> pods;
        //从pod
        PodDTO primaryPod = null;
        try {
            AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
            pods = kubeClient.listPod(app.getNamespace(), appKind.labelOfPod(app));
        } catch (Exception e) {
            log.error("获取openGauss所有pod失败！", e);
//            messageObj.put("mes","获取openGauss所有pod失败！");
//            updateBackupHis(backupHis.getBackupHisId(),startDate,new Date(),"", StatusConstant.FAIL,messageObj);
            return null;
        }
        //校验是否获取到pod
        if(null == pods){
            log.error("未获取到openGauss的pod信息！");
//            messageObj.put("mes","未获取到openGauss的pod信息！");
//            updateBackupHis(backupHis.getBackupHisId(),startDate,new Date(),"", StatusConstant.FAIL,messageObj);
            return null;
        }
        //判断是否只找到一个节点，如果是单节点则将该节点作为备份节点
        if(1 == pods.size()){
            primaryPod = pods.get(0);
        }else{
            //遍历所有pod找到一个主pod
            for(PodDTO everyPod : pods){
                String roleName = null;
                Map<String, String> labels = everyPod.getPod().getMetadata().getLabels();
                //获取role
                for(String labelKey : labels.keySet()){
                    if("opengauss.role".equalsIgnoreCase(labelKey)){
                        roleName = labels.get(labelKey);
                        break;
                    }
                }
                if ("primary".equalsIgnoreCase(roleName)) {
                    primaryPod = everyPod;
                    break;
                }
            }
        }
        return primaryPod;
    }

//    /**
//     * 修改备份历史
//     * @param backupHisId
//     * @param backupEndDate
//     * @param backupStartDate
//     * @param lastBackupFileName
//     */
//    public void updateBackupHis(Integer backupHisId, Date backupStartDate, Date backupEndDate, String lastBackupFileName,String isSuccess ,JSONObject messageObj){
//        //处理备份文件list字符串。如果为null，则设置为"";如果为[]，则设置为""；如果为[...]，则设置为...
//        if(null == lastBackupFileName){
//            lastBackupFileName = "";
//        }else if("[]".equals(lastBackupFileName)){
//            lastBackupFileName = "";
//        }else if(0 == lastBackupFileName.indexOf("[") && lastBackupFileName.length()-1 == lastBackupFileName.lastIndexOf("]")){
//            lastBackupFileName = lastBackupFileName.substring(1,lastBackupFileName.length()-1);
//        }
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
//        BackupHis backupHis = new BackupHis();
//        backupHis.setBackupHisId(backupHisId);
//        //用时
//        long interval = (backupEndDate.getTime() - backupStartDate.getTime())/1000;
//        String durationStr = String.valueOf(interval);
//        int duration = Integer.parseInt(durationStr);
//        //备份状态
//        backupHis.setStatus(isSuccess);
//        //备份结束时间
//        backupHis.setEndTime(new Timestamp(System.currentTimeMillis()));
//        //备份用时
//        backupHis.setDuration(duration);
//        //信息
//        String messageJsonStr = JSON.toJSONString(messageObj);
//        backupHis.setMessage(messageJsonStr);
//        //备份文件名称
//        if(!"".equalsIgnoreCase(lastBackupFileName)){
//            backupHis.setFileName(lastBackupFileName);
//        }
//        //基础时间
//        String baseTime = sdf.format(backupStartDate);
//        backupHis.setBaseTime(baseTime);
//        backupMapper.updateBackupHis(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_HIS, backupHis);
//    }

    /**
     * opengauss恢复
     * @param backupHis
     * @param appId
     */
    @Async
    public void ogRestore(BackupHis backupHis, Integer appId) {
        // 0.对恢复的底层库类型做校验
        Integer backupAppId = backupHis.getAppId();
        CloudApp goalApp = cloudAppMapper.selectByPrimaryKey(appId, DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_APP);
        CloudApp backupApp = cloudAppMapper.selectByPrimaryKey(backupAppId, DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_APP);
        KubeClient kubeClient = kubeClientService.get(goalApp.getKubeId());
        OpenGaussCluster cr = kubeClient.listCustomResource(OpenGaussCluster.class, goalApp.getCrName(), goalApp.getNamespace());
        String backupHisMessage = backupHis.getMessage();
        Map backupHisMessageObj = JsonUtil.toObject(Map.class, backupHisMessage);
        String compatibility = (String) backupHisMessageObj.get("compatibility");
        // 1.恢复历史插入
        RestoreHis restoreHis = new RestoreHis();
        restoreHis.setStatus(StatusConstant.RUNNING);
        Timestamp startTime = new Timestamp(System.currentTimeMillis());
        restoreHis.setStartTime(startTime);

        restoreHis.setAppId(appId);
        restoreHis.setAppName(goalApp.getName());
        restoreHis.setAppType(goalApp.getKind());

        //应用所属集群
        KubeConfig byId = kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, goalApp.getKubeId());
        restoreHis.setKubeName(byId.getName());
        restoreHis.setMessage("恢复中...");
        restoreHis.setFileDeleted(false);
        String backDirInPod = "/backup/";
        restoreHis.setRestoreDir(backDirInPod);
        restoreHis.setFileName(backupHis.getFileName());

        // 2.插入操作记录
        ResourceChangeHis resourceChangeHis = new ResourceChangeHis() {{
            setInsertTime(startTime);
            setKind(goalApp.getKind());
            setKubeId(goalApp.getKubeId());
            setNamespace(goalApp.getNamespace());
            setCommand("恢复");
            setStatus("2");
            setAction(ActionEnum.RESTORE.getActionType());
            setMsg("恢复中...");
            setAppId(goalApp.getId());
            setAppName(goalApp.getName());
            setUserName(UserUtil.getAsyncUserinfo().getUsername());
            setUserIp(CloudRequestContext.getContext().getUserIp());
            setKubeName(byId.getName());
            setYaml(goalApp.getCr());
            setLastEndTimestamp(System.currentTimeMillis());
            setAppLogicId(goalApp.getLogicAppId());
        }};
        Integer changeId = insertResourceChangeHis(resourceChangeHis);
        try {
            // 3.找到主节点
            KubeClient client = kubeClientService.get(goalApp.getKubeId());
            PodDTO primaryPod = getBackupPodOpengauss(goalApp, client);
            restoreHis.setPodName(primaryPod.getPodName());
            //插入基本信息
            restoreMapper.commitRestore(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_RESTORE_HIS, restoreHis);
            goalApp.setStatus(CloudAppConstant.AppStatus.PENDING);
            cloudAppService.update(goalApp);

            // 4.获取备份文件名称,修改对应的cr属性：RestoreFile
            //备份文件名称
            String backupFileName = backupHis.getFileName();
            //构造cr
            OpenGaussClusterSpec spec = cr.getSpec();
            String fullSourceName = backupApp.getNamespace() + "/" + backupApp.getCrName() + "/" + backupFileName;
            com.shindata.opengauss.v1.opengaussclusterspec.Restore restore = new com.shindata.opengauss.v1.opengaussclusterspec.Restore();
            restore.setFullSource(fullSourceName);
            //获取备份存储信息
            CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
            HashMap<String, String> remote = new HashMap<>();
            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                remote.put("type", CloudAppConstant.OperatorStorageType.NFS);
                remote.put("address", cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath());
            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                remote.put("type", CloudAppConstant.StorageType.S3);
                remote.put("address", cloudBackupStorageVO.getServer());
                remote.put("bucket", cloudBackupStorageVO.getBucket());
                remote.put("region", cloudBackupStorageVO.getRegion());
                //获取operator的namespace，因为所有operator都相同，所以统一获取mysql的operatornamespace
                String operatorConfig = sysConfigService.findOne("operator.name", "MySQL");
                String operatorNamespace = operatorConfig.split("/")[0];
                remote.put("secret", operatorNamespace + ":backupstorage-secret");
            } else {
                throw new CustomException(600, "恢复失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
            }
            restore.setRemote(remote);
            spec.setRestore(restore);
            cr.setSpec(spec);
            kubeClient.updateCustomResource(cr, OpenGaussCluster.class);
            //创建定时轮询备份结果
            Map map = new HashMap();
            map.put("restoreHisId", restoreHis.getRestoreHisId());
            map.put("resourceChangeId", changeId);
            appCommonService.callScheduler(goalApp, YamlEngine.marshal(cr), map, ActionEnum.RESTORE, OpenGaussBackupAndRestoreWatch.class, resourceChangeHis);
        } catch (Exception e) {
            restoreReturn(restoreHis, changeId, e.getMessage(), StatusConstant.FAIL);
        }
    }

    private PodDTO getRestorePodOpengauss(CloudApp goalApp, KubeClient kubeClient) {
        PodDTO primaryPod = null;
        List<PodDTO> pods = kubeClient.listPod(goalApp.getNamespace(), AppKind.OpenGauss.labelOfPod(goalApp));
        //3.获取主pod
        for (PodDTO everyPod : pods) {
            String roleName = null;
            Map<String, String> labels = everyPod.getPod().getMetadata().getLabels();
            for (String labelKey : labels.keySet()) {
                if ("opengauss.role".equalsIgnoreCase(labelKey)) {
                    roleName = labels.get(labelKey);
                    break;
                }
            }
            if ("primary".equalsIgnoreCase(roleName)) {
                primaryPod = everyPod;
            }
        }
        return primaryPod;
    }

    /**
     * 添加操作记录
     * @param resourceChangeHis
     */
    public Integer insertResourceChangeHis(ResourceChangeHis resourceChangeHis){
        //判断是修改还是添加记录
        if(null != resourceChangeHis.getId()){
            //修改
            resourceChangeHisService.update(resourceChangeHis);
        }else{
            //添加
            resourceChangeHisService.add(resourceChangeHis);
        }
        return resourceChangeHis.getId();
    }

    /**
     * 备份结果进行返回
     * @param backupHis
     * @param changeId
     * @param status
     * @param backupFile
     * @param message
     */
    public void backupReturn(BackupHis backupHis,Integer changeId, String status, String backupFile, String message) {
        //修改备份历史
        //处理备份文件list字符串。如果为null，则设置为"";如果为[]，则设置为""；如果为[...]，则设置为...
        if(null == backupFile){
            backupFile = "";
        }else if("[]".equals(backupFile)){
            backupFile = "";
        }else if(0 == backupFile.indexOf("[") && backupFile.length()-1 == backupFile.lastIndexOf("]")){
            backupFile = backupFile.substring(1,backupFile.length()-1);
        }
        backupHis.setFileName(backupFile);

        Timestamp endTime = new Timestamp(System.currentTimeMillis());
        backupHis.setEndTime(endTime);

        backupHis.setStatus(status);

        Long backupLength = (endTime.getTime() - backupHis.getStartTime().getTime()) / 1000;
        backupHis.setDuration(backupLength.intValue());

        //判断成功或失败来填充message
        if(StatusConstant.SUCCESS.equalsIgnoreCase(status)){
            //成功则生成完整json串的形式
            String backupHisMessage = backupHis.getMessage();
            JSONObject messageObj = JSON.parseObject(backupHisMessage);
            messageObj.put("msg", message);
            //放入版本
            String version = cloudAppService.get(backupHis.getAppId()).getVersion();
            messageObj.put("version", version);
            String backupHisMessageNew = JSON.toJSONString(messageObj);
            backupHis.setMessage(backupHisMessageNew);
        }else if(StatusConstant.FAIL.equalsIgnoreCase(status)){
            backupHis.setMessage(message);
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String baseTime = sdf.format(backupHis.getStartTime());
        if ("full".equalsIgnoreCase(backupHis.getBackupType()) && !backupHis.getAppType().equalsIgnoreCase(AppKind.Vastbase.getKind())) {
            backupHis.setBaseTime(baseTime);
        }

        backupMapper.updateBackupHis(SCHEMA, CLOUD_BACKUP_HIS, backupHis);

        //修改操作记录
        if(0 != changeId){
            ResourceChangeHis resourceChangeHis = resourceChangeHisService.get(changeId);
            resourceChangeHis.setStatus(status);
            resourceChangeHis.setMsg(appendLogMessage(message, resourceChangeHis.getMsg()));
            resourceChangeHis.setUpdateTime(endTime);
            resourceChangeHis.setUserName(UserUtil.getAsyncUserinfo().getUsername());
            resourceChangeHis.setUserIp(CloudRequestContext.getContext().getUserIp());
            insertResourceChangeHis(resourceChangeHis);
        }
        if(null != status && !"2".equalsIgnoreCase(status)){
            cloudAppService.handleWatchResult(backupHis.getAppId(), StatusConstant.SUCCESS.equals(status));
        }
    }

    private static String appendLogMessage(String appendMessage, String lastMessage) {
        return lastMessage != null ? lastMessage.concat("\n").concat(appendMessage) : appendMessage;
    }

    /**
     * 恢复结果进行返回
     * @param restoreHis
     * @param changeId
     */
    public void restoreReturn(RestoreHis restoreHis, Integer changeId, String mes, String status){
        log.info(mes);
        Timestamp endTime = new Timestamp(System.currentTimeMillis());
        //防止日志超出最大字段长度
        if (mes.length() > 999) {
            mes = mes.substring(0, 999);
        }
        restoreHis.setMessage(mes);
        restoreHis.setStatus(status);
        restoreFailUpdateHis(restoreHis);
        //修改操作记录
        if (0 != changeId) {
            ResourceChangeHis resourceChangeHis = new ResourceChangeHis();
            resourceChangeHis.setId(changeId);
            resourceChangeHis.setStatus(status);
            resourceChangeHis.setMsg(mes);
            resourceChangeHis.setUpdateTime(endTime);
            insertResourceChangeHis(resourceChangeHis);
        }
        if (null != status && !"2".equalsIgnoreCase(status))
            cloudAppService.handleWatchResult(restoreHis.getAppId(), StatusConstant.SUCCESS.equals(status));
    }

    public String readLogFile(KubeClient kubeClient, String namespace, String podName, String containerName, String logFilePattern) {
        //恢复执行失败
        String partialLogs = null;
        if (!StringUtils.isEmpty(logFilePattern))
            log.info("try fetch logs with pattern " + logFilePattern);
        try {
            String cmd = "ls " + logFilePattern + " | tail -n 1 | xargs tail -n 20";
            partialLogs = kubeClient.execCmd(namespace, podName, containerName, "sh", "-c", cmd);
        } catch (Exception e) {
            log.error("fetch extra logs error " + e.getMessage());
        }
        return partialLogs;
    }

    /**
     * @param kubeClient
     * @param namespace
     * @param podName
     * @param containerName
     * @param commandResult
     * @param logFilePattern 不为空时表示有需要额外检查的日志, 提供用来列举日志文件的模板. e.g.  ls /data/tmp/restore_*.log
     * @return
     */
    private void checkCommandResult(KubeClient kubeClient, String namespace, String podName, String containerName, Map<String, String> commandResult, String logFilePattern) {
        if (!"yes".equals(commandResult.get("success"))) {
            //恢复执行失败
            String partialLogs = null;
            if (!StringUtils.isEmpty(logFilePattern))
                log.info("try fetch logs with pattern " + logFilePattern);
                try {
                    String cmd = "ls " + logFilePattern + " | tail -n 1 | xargs tail -n 10";
                    partialLogs = kubeClient.execCmd(namespace, podName, containerName, "sh", "-c", cmd);
                } catch (Exception e) {
                    log.error("fetch extra logs error " + e.getMessage());
                }
            throw new RuntimeException(commandResult.get("result") + "\n Append script partial logs here \n" + partialLogs);
        }
    }


    /**
     * es备份
     *
     * @param backupHis
     */
    @Async
    public void elasticSearchBackup(BackupHisVO backupHis) {
        BackupHis runningBackupHis = backupMapper.getRunningByAppId(SCHEMA, CLOUD_BACKUP_HIS, backupHis.getAppId());
        if (runningBackupHis != null) {
            throw new CustomException(600, "存在正在执行的备份，请勿重复提交！");
        }
        // 1. 保存备份历史基本信息
        //获取app信息
        CloudApp app = cloudAppMapper.selectByPrimaryKey(backupHis.getAppId(), DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_APP);
        // 提交前校验备份的结果
        String checkStorageResult = backupHis.getMessage();

        //操作记录和操作记录id
        ResourceChangeHis resourceChangeHis = null;
        Integer changeId = null;
        //所有pod
        List<PodDTO> pods = new ArrayList<PodDTO>();
        //当前时间
        Timestamp startTime = new Timestamp(System.currentTimeMillis());
        //备份类型
        backupHis.setBackupType("full");
        //开始时间
        backupHis.setStartTime(startTime);
        //设置备份状态为运行
        backupHis.setStatus(StatusConstant.RUNNING);
        //应用所属集群
        KubeConfig byId = kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, app.getKubeId());
        if (null == byId) {
            log.error("未获取到集群！");
            backupReturn(backupHis, null, StatusConstant.FAIL, "", "es备份未获取到集群！");
            return;
        }
        backupHis.setKubeName(byId.getName());
        //插入备份历史
        backupMapper.commitBackup(SCHEMA, CLOUD_BACKUP_HIS, backupHis);
        resourceChangeHis = new ResourceChangeHis() {{
            setInsertTime(startTime);
            setKind(app.getKind());
            setKubeId(app.getKubeId());
            setNamespace(app.getNamespace());
            setCommand("备份");
            setStatus("2");
            setAction("Backup");
            setMsg("备份中...".concat("\n").concat(null == checkStorageResult ? "" : checkStorageResult));
            setAppId(app.getId());
            setAppLogicId(app.getLogicAppId());
            setAppName(app.getName());
            setKubeName(byId.getName());
            setYaml(app.getCr());
            setUserName(UserUtil.getAsyncUserinfo().getUsername());
            setUserIp(CloudRequestContext.getContext().getUserIp());
            setLastEndTimestamp(System.currentTimeMillis());
        }};
        changeId = insertResourceChangeHis(resourceChangeHis);
        app.setStatus(CloudAppConstant.AppStatus.PENDING);
        cloudAppService.update(app);
        // 2. 根据appid获取kubeclient
        //创建k8s客户端
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());

        // 3.选择一个data节点执行备份操作
        //获取所有节点
        PodDTO masterPod = getBackupPodEs(app, kubeClient);
        if (null == masterPod) {
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "当前app：" + app.getName() + "未获取到data节点！");
            return;
        }
        //将podname保存
        backupHis.setPodName(masterPod.getPodName());
        backupMapper.updateBackupHis(SCHEMA, CLOUD_BACKUP_HIS, backupHis);
        // 4. 在master节点中执行备份脚本
        String indexRes = null;
        String nowStr = null;
        Map<String, String> backupResMap = null;
        try {
            //密码，执行命令：kubectl get secret hdbk-4-es-elastic-user -n rongqiyun -o go-template='{{.data.elastic | base64decode}}'
            String password = kubeClient.getSecret(app.getNamespace(), app.getCrName() + "-es-elastic-user").getData().get("elastic");
            password = new String(Base64.getDecoder().decode(password));
            String encodePassword = URLEncoder.encode(password, "UTF-8");
            //获取index数量，用来判断备份是否成功，备份结果的备份文件数量应该为：index数量 × 4，获取到的index放入到定时中，到watch中进行判断
            Map<String, String> getIndexRes = execCmd(kubeClient, app.getNamespace(), masterPod.getPodName(), "dump", "sh", "-c", "sh /scripts/index_count.sh " + masterPod.getPodIp() + " " + AppKind.Elasticsearch.getDbPort() + " " + encodePassword);
            indexRes = getIndexRes.get("result").replaceAll("\n", "");
            log.info("[es备份]获取到的备份应用的索引：" + indexRes);
//        if(indexRes.contains("")){
//
//        }
            //备份命令
            //当前时间
            Date now = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            nowStr = sdf.format(now);
            String backupScript = "";
            CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
                backupScript = "bash /scripts/backup.sh "
                        + masterPod.getPodIp()
                        + " " + AppKind.Elasticsearch.getDbPort() + " "
                        + encodePassword
                        + " " + nowStr
                        + " " + CloudAppConstant.OperatorStorageType.NFS
                        + " " + mountPath;
            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                String region = cloudBackupStorageVO.getRegion();
                if (StringUtils.isEmpty(region)) {
                    region = "\"\"";
                }
                backupScript = "bash /scripts/backup.sh "
                        + masterPod.getPodIp()
                        + " " + AppKind.Elasticsearch.getDbPort() + " "
                        + encodePassword
                        + " " + nowStr
                        + " " + CloudAppConstant.StorageType.S3
                        + " " + cloudBackupStorageVO.getServer()
                        + " " + region
                        + " " + cloudBackupStorageVO.getBucket()
                        + " " + cloudBackupStorageVO.getAccessKey()
                        + " " + cloudBackupStorageVO.getSecretKey();
            } else {
                throw new CustomException(600, "备份失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
            }
            log.info("[es备份]执行的es备份命令:" + backupScript);
//            execCmd(kubeClient, app.getNamespace(), masterPod.getPodName(), "dump", "sh", "-c", backupScript);
            kubeClient.execCmdOneway(app.getNamespace(), masterPod.getPodName(), "dump", "sh", "-c", backupScript);
            log.info("[es备份]执行的es备份结果：" + backupResMap);
        } catch (Exception e) {
            log.error("", e);
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "准备执行备份脚本失败" + e.getMessage());
            return;
        }
        //触发备份成功
        // 5. 创建定时watch
        //定时传参
        Map map = new HashMap();
        map.put("backupIndex", indexRes);
        map.put("backupHisId", backupHis.getBackupHisId());
        map.put("changeId", changeId);
        map.put("backupTimestamp", nowStr);
        map.put("backupPodName", masterPod.getPodName());
        map.put("backupFtpPath", this.getFtpBackupPath(app));
        try {
            appCommonService.callScheduler(app, app.getCr(), map, ActionEnum.BACKUP, ElasticsearchBackupAndRestoreWatch.class, resourceChangeHis);
        } catch (SchedulerException e) {
            //备份失败
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "es备份提交定时失败！错误信息：" + e.getMessage());
            return;
        } catch (JsonProcessingException e) {
            //备份失败
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "es备份提交定时失败！错误信息：" + e.getMessage());
            return;
        }
    }

    public PodDTO getBackupPodEs(CloudApp app, KubeClient kubeClient) {
        List<PodDTO> pods;
        try {
            //根据命名空间和label获取所有pod
            AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
            pods = kubeClient.listPod(app.getNamespace(), appKind.labelOfPod(app));
            if (pods == null || pods.size() == 0){
//                backupFailUpdateHis(backupHis, "当前app："+app.getName()+"没有pods！");
                log.error("当前app："+ app.getName()+"没有pods！");
                return null;
            }
        } catch (Exception e) {
            log.error("es备份获取所有pods失败！", e);
//            backupFailUpdateHis(backupHis, "获取所有pods失败！");
            return null;
        }
        //获取一个data
        PodDTO masterPod = null;
        for(PodDTO pod : pods){
            if(pod.getPodName().contains("-data-")){ // todo 不可靠的判断
                masterPod = pod;
            }
        }
        return masterPod;
    }

    /**
     * es恢复
     * @param backupHis
     */
    @Async
    public void elasticSearchRestore(BackupHis backupHis, Integer goalAppId) {
        RestoreHis restoreHis = new RestoreHis();
        restoreHis.setStatus(StatusConstant.RUNNING);

        Date startDate = new Date();
        //所有pod
        List<PodDTO> pods = new ArrayList<PodDTO>();
        //时间转换
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
        //操作记录
        ResourceChangeHis resourceChangeHis = null;
        Integer changeId = null;
        try{
            Timestamp startTime = new Timestamp(System.currentTimeMillis());
            restoreHis.setStartTime(startTime);
            // 1. 插入基础恢复历史和操作记录
            Integer backupAppId = backupHis.getAppId();
            CloudApp goalApp = cloudAppMapper.selectByPrimaryKey(goalAppId, DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_APP);
            CloudApp backupApp = cloudAppMapper.selectByPrimaryKey(backupAppId, DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_APP);
            restoreHis.setAppId(goalAppId);
            restoreHis.setAppName(goalApp.getName());
            restoreHis.setAppType(goalApp.getKind());
            KubeClient kubeClient = kubeClientService.get(goalApp.getKubeId());
            //获取一个datapod执行恢复操作
            //获取所有节点
            PodDTO masterPod = getRestorePodEs(goalApp, kubeClient).get(0);
            if(null == masterPod){
                backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "当前app："+goalApp.getName()+"未获取到master节点！");
                return;
            }
            restoreHis.setPodName(masterPod.getPodName());

            //应用所属集群
            KubeConfig byId = kubeConfigMapper.getById(DatasourceConstant.SCHEMA, CLOUD_KUBE_CONFIG_TABLE, goalApp.getKubeId());
            if(null == byId){
                log.error("未获取到集群！");
                restoreReturn(restoreHis, changeId, "未获取到集群！", StatusConstant.FAIL);
                return;
            }
            restoreHis.setKubeName(byId.getName());
            restoreHis.setMessage("恢复中...");
            String restoreDir = AppUtil.getESBackupRootPath(goalApp.getNamespace(), goalApp.getCrName());
            restoreHis.setRestoreDir(restoreDir);
            restoreHis.setFileName(backupHis.getFileName());
            restoreHis.setFileDeleted(false);
            //插入基本信息
            int insertNum = restoreMapper.commitRestore(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_RESTORE_HIS, restoreHis);
            //插入操作记录
            resourceChangeHis = new ResourceChangeHis(){{
                setInsertTime(startTime);
                setKind(goalApp.getKind());
                setKubeId(goalApp.getKubeId());
                setNamespace(goalApp.getNamespace());
                setCommand("恢复");
                setStatus("2");
                setAction(ActionEnum.RESTORE.getActionType());
                setMsg("恢复中...");
                setAppId(goalApp.getId());
                setAppLogicId(goalApp.getLogicAppId());
                setAppName(goalApp.getName());
                setKubeName(byId.getName());
                setYaml(goalApp.getCr());
                setUserName(UserUtil.getAsyncUserinfo().getUsername());
                setUserIp(CloudRequestContext.getContext().getUserIp());
                setLastEndTimestamp(System.currentTimeMillis());
            }};
            changeId = insertResourceChangeHis(resourceChangeHis);
            goalApp.setStatus(CloudAppConstant.AppStatus.PENDING);
            cloudAppService.update(goalApp);
//            checkRestoreAndRecord(resourceChangeHis, goalApp, backupHis, restoreHis);

            //密码，执行命令：kubectl get secret hdbk-4-es-elastic-user -n rongqiyun -o go-template='{{.data.elastic | base64decode}}'
            String password = kubeClient.getSecret(goalApp.getNamespace(), goalApp.getCrName() + "-es-elastic-user").getData().get("elastic");
            password = new String(Base64.getDecoder().decode(password));
            String encodePassword = URLEncoder.encode(password, "UTF-8");
            Date now = new Date();
            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMddHHmmss");
            String nowStr = sdf1.format(now);

//            // 拷贝备份文件
//            if (!backupHis.getAppId().equals(goalAppId))
//                try {
//                    String backupFileFullName = AppUtil.getESBackupRootPath(backupApp.getNamespace(), backupApp.getCrName()) + "/" + backupHis.getFileName();
//                    String res = kubeClient.execCmd(goalApp.getNamespace(), masterPod.getPodName(), "dump",
//                            "sh", "-c", "cp -rf " + backupFileFullName + " " + restoreDir);
//                } catch (Exception e) {
//                    log.error("es恢复拷贝文件失败失败！");
//                    restoreReturn(restoreHis, changeId, "拷贝文件失败！" + e.getMessage(), StatusConstant.FAIL);
//                    return;
//                }

            //执行恢复命令
            //获取时间戳
            String restoreTimestamop = backupHis.getFileName().substring(backupHis.getFileName().indexOf("-") + 1, backupHis.getFileName().lastIndexOf(".tar.gz"));
            String restoreScript = "";
            CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
                restoreScript = "bash /scripts/restore.sh "
                        + masterPod.getPodIp()
                        + " " + AppKind.Elasticsearch.getDbPort() + " "
                        + encodePassword
                        + " " + restoreTimestamop
                        + " " + CloudAppConstant.OperatorStorageType.NFS
                        + " " + mountPath
                        + " " + backupApp.getCrName();
            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                String region = cloudBackupStorageVO.getRegion();
                if (StringUtils.isEmpty(region)) {
                    region = "\"\"";
                }
                restoreScript = "bash /scripts/restore.sh "
                        + masterPod.getPodIp()
                        + " " + AppKind.Elasticsearch.getDbPort() + " "
                        + encodePassword
                        + " " + restoreTimestamop
                        + " " + CloudAppConstant.StorageType.S3
                        + " " + cloudBackupStorageVO.getServer()
                        + " " + backupApp.getCrName()
                        + " " + region
                        + " " + cloudBackupStorageVO.getBucket()
                        + " " + cloudBackupStorageVO.getAccessKey()
                        + " " + cloudBackupStorageVO.getSecretKey();
            } else {
                throw new CustomException(600, "备份失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
            }
            log.info("[es恢复]执行的es恢复命令:" + restoreScript);
            Map<String, String> backupResMap = execCmd(kubeClient, goalApp.getNamespace(), masterPod.getPodName(), "dump", "sh", "-c", restoreScript);
            log.info("[es恢复]执行的es恢复结果：" + backupResMap);
            //创建定时轮询恢复结果
            Map map = new HashMap();
            map.put("restoreHisId", restoreHis.getRestoreHisId());
            map.put("resourceChangeId", changeId);
            map.put("restoreTimestamp", restoreTimestamop);
            map.put("restorePodName", masterPod.getPodName());
            map.put("restoreStartDateSDF", sdf.format(startDate));
            appCommonService.callScheduler(goalApp,goalApp.getCr(),map,ActionEnum.RESTORE,ElasticsearchBackupAndRestoreWatch.class,resourceChangeHis);
        }catch (Exception e){
            log.error("", e);
            restoreReturn(restoreHis, changeId, e.getMessage(), StatusConstant.FAIL);
        }
    }

    /**
     * 恢复校验存储空间是否足够并记录在操作记录
     */
    public void checkRestoreAndRecord(ResourceChangeHis resourceChangeHis, CloudApp goalApp, BackupHis backupHis, RestoreHis restoreHis) {
        try {
            Map<String, Map<String, Object>> stringMapMap = checkRestore(goalApp, backupHis, restoreHis.getPodName());
            resourceChangeHis.setMsg(appendLogMessage(serialize(stringMapMap), resourceChangeHis.getMsg()));
        } catch (Exception e) {
            log.error("check restore error", e);
            resourceChangeHis.setMsg(appendLogMessage("check restore error" + e.getMessage(), resourceChangeHis.getMsg()));
        }
        resourceChangeHisService.update(resourceChangeHis);
    }

    public List<PodDTO> getRestorePodEs(CloudApp goalApp, KubeClient kubeClient) {
        //根据命名空间和label获取所有pod
        AppKind appKind = AppKind.valueOf(goalApp.getKind(), goalApp.getArch());
        List<PodDTO> pods = kubeClient.listPod(goalApp.getNamespace(), appKind.labelOfPod(goalApp));

        //获取一个data
        PodDTO masterPod = null;
        for(PodDTO pod : pods){
            if(pod.getPodName().contains("-data-")){
                masterPod = pod;
            }
        }
        return ImmutableList.of(masterPod);
    }

    public String getStoreDir() {
        return sysConfigService.findOne(CloudAppConstant.SysCfgCategory.BACKUP_MANAGEMENT, "storeDir");
    }

    /**
     * Ftp上应用备份文件路径
     * @param app 备份应用
     * @return backupPath
     */
    public String getFtpBackupPath(CloudApp app) {
        String kind = app.getKind();
        if (AppKind.Redis_Cluster == AppKind.valueOf(app.getKind(), app.getArch())) {
            kind += "-Cluster";
            return "/" + kind.toLowerCase() + "/" + app.getNamespace() + "/" + app.getCrName();
        }
        if (AppKind.Redis == AppKind.valueOf(app.getKind(), app.getArch())) {
            return "/" + kind.toLowerCase() + "/" + app.getNamespace() + "/" + app.getCrName();
        }
        if (AppKind.MongoDB_Cluster == AppKind.valueOf(app.getKind(),app.getArch())){
            kind += "-Cluster";
        }
        return basePath + "/" + kind + "/" + app.getNamespace() + "/" + app.getCrName();
    }

    /**
     * 传输Pod内备份文件到 FTP服务器
     * @param kubeClient K8s客户端
     * @param namespace  应用所在命名空间
     * @param podName    应用所在Pod名
     * @param filename 备份文件文件名
     * @param ftpDir ftp服务器上备份文件路径
     * @throws Exception
     */
    public void uploadBackupFile(KubeClient kubeClient, String namespace, String podName, String ftpDir, String filename, String kind) throws Exception {
        String url = ftpUtil.getURL();
        log.info(String.format("[uploadBackupFile] 调用ftp 备份文件上传脚本，url:%s，ftpPath：%s，filename：%s", url, ftpDir, filename));
        String cmd = "";
        if (AppKind.Redis.getKind().equals(kind) || AppKind.Redis_Cluster.getKind().equals(kind)){
            cmd = String.format("bash /scripts/ftp-upload.sh %s %s %s", url, filename, ftpDir);
        } else if (AppKind.MYSQL_HA.getKind().equals(kind)){
            cmd = String.format("bash /scripts/ftp-upload.sh %s %s %s", url, filename, ftpDir);
        } else if (AppKind.Elasticsearch.getKind().equals(kind)){
            cmd = String.format("bash /es-scripts/ftp-upload.sh %s %s %s", url, filename, ftpDir);
        } else if (AppKind.MongoDB.getKind().equals(kind)){
            // pod内备份文件夹是 /backup
            cmd = String.format("bash /scripts/ftp-upload.sh %s %s %s", url, filename, ftpDir);
        }else if (AppKind.OpenGauss.getKind().equals(kind)){
            // pod内备份文件夹是 /gaussdata/backup
            cmd = String.format("bash /scripts/cm-mnt/ftp-upload.sh %s %s %s %s", url, "/gaussdata/backup/" + filename, ftpDir, false, "hd_testlog");
        }else if (AppKind.MongoDB_Cluster.getKind().equals(kind)){
            // pod内备份文件夹是 /backup
            cmd = String.format("bash /scripts/ftp-upload.sh %s %s %s", url, filename, ftpDir);
        }
        String msg = kubeClient.execCmd(namespace, podName, "ftp", true, 0, "sh", "-c", cmd);
        log.info("[uploadBackupFile] 调用ftp 备份文件上传脚本结果： " + cmd);
        if("1\n".equals(msg)){
            throw new CustomException(600,"从POD内上传备份文件到FTP服务器失败!");
        }
    }

    /**
     * 传输Pod内备份文件到 FTP服务器
     * @param app 应用
     * @param podName    应用所在Pod名
     * @param ftpDir ftp服务器上备份文件路径
     * @param filename 备份文件文件名
     * @param suffix
     * @throws Exception
     */
    public void uploadBackupFile(CloudApp app, String podName, String ftpDir, String filename, Integer backupHisId, String suffix) throws Exception {
        String url = ftpUtil.getURL();
        log.info(String.format("[uploadBackupFile] 调用ftp 备份文件上传脚本，url:%s，ftpPath：%s，filename：%s", url, ftpDir, filename));
        String cmd = "";
        if (AppKind.OpenGauss.getKind().equals(app.getKind())){
            // pod内备份文件夹是 /gaussdata/backup
            cmd = String.format("bash /scripts/cm-mnt/ftp-upload.sh %s %s %s %s %s", url, "/gaussdata/backup/" + filename, ftpDir, false, "uploadlog_" + backupHisId + suffix);
        }else if(AppKind.Elasticsearch.getKind().equals(app.getKind())){
            cmd = String.format("bash /es-scripts/ftp-upload.sh %s %s %s %s %s", url, filename, ftpDir, false, "uploadlog_" + backupHisId + suffix);
        }else {
            cmd = String.format("bash /scripts/ftp-upload.sh %s %s %s %s %s", url, filename, ftpDir, false, "uploadlog_" + backupHisId + suffix);
        }
        String msg = kubeClientService.get(app.getKubeId()).execCmd(app.getNamespace(), podName, "ftp", true, 0, "sh", "-c", cmd);
        log.info("[uploadBackupFile] 调用ftp 备份文件上传脚本结果： " + cmd);
    }

    /**
     * 传输FTP服务器备份文件到Pod内
     *
     * @param kubeClient        K8s客户端
     * @param namespace         应用所在命名空间
     * @param podName           应用所在Pod名
     * @param ftpDir            ftp服务器上备份文件路径
     * @param filename          备份文件文件名
     * @throws Exception
     */
    private void downloadBackupFile(KubeClient kubeClient, String namespace, String podName, String ftpDir, String filename, String kind) throws Exception {
        String url = ftpUtil.getURL();
        log.info(String.format("[downloadBackupFile] 调用ftp 备份文件下载脚本，url:%s，ftpPath：%s，filename：%s", url, ftpDir, filename));
        String cmd = "";
        String backupRootPath = null;
        if (AppKind.Redis.getKind().equals(kind) || AppKind.Redis_Cluster.getKind().equals(kind)){
            backupRootPath = "/backup";
            cmd = String.format("bash /redis-script/ftp-download.sh %s %s %s", url, backupRootPath, ftpDir + "/" + filename);
        } else if (AppKind.MYSQL_HA.getKind().equals(kind)){
            backupRootPath = "/backup";
            cmd = String.format("bash /scripts/ftp-download.sh %s %s %s", url, backupRootPath, ftpDir + "/" +  filename);
        } else if (AppKind.Elasticsearch.getKind().equals(kind)){
            backupRootPath = "/backup";
            cmd = String.format("bash /es-scripts/ftp-download.sh %s %s %s", url, backupRootPath, ftpDir + "/" + filename);
        } else if (AppKind.MongoDB.getKind().equals(kind)){
            // mongodb下载的是一个文件夹
            backupRootPath = "/backup";
            cmd = String.format("bash /scripts/ftp-download.sh %s %s %s", url, backupRootPath, ftpDir + "/" + filename);
        }else if (AppKind.OpenGauss.getKind().equals(kind)){
            // opengauss下载tar包
            backupRootPath = "/gaussdata/backup";
            cmd = String.format("bash /scripts/cm-mnt/ftp-download.sh %s %s %s", url, backupRootPath, ftpDir + "/" + filename);
        }
        try {
            String msg = kubeClient.execCmd(namespace, podName, "ftp", "sh", "-c", cmd);
            log.info("[downloadBackupFile] 调用ftp 备份文件下载脚本结果： " + msg);
            if ("1\n".equals(msg)) {
                throw new CustomException(600, "下载备份文件失败!");
            }
        } catch (Exception e) {
            log.error("[downloadBackupFile] 调用ftp 备份文件下载脚本异常： " + e.getMessage());
            throw new CustomException(600, "下载备份文件失败!");
        }
    }


    private static String getBackupHisFileName(BackupHis backupHis, CloudApp app) {
        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
        JSONObject backupMessage = JSON.parseObject(backupHis.getMessage());
        switch (appKind) {
            case Elasticsearch:
            case Redis:
            case Redis_Cluster:
            case OpenGauss:
            case MYSQL_HA:
            case MYSQL_MGR:
            case MongoDB:
            case MongoDB_Cluster:
            case PostgreSQL:
                return backupHis.getFileName().trim();
            // e.g. {"backupFtpPath":"/backup/MongoDB/yan-test/t0504", "backupFilename":"20230506094325"}
                // 备份文件实际在 backupFtpPath/backupFilename目录下，每个db一个子目录
            default:
                throw new UnsupportedOperationException();
        }
    }

    private String getBackFileFtpPath(BackupHis backupHis, CloudApp app) {
        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
        JSONObject backupMessage = JSON.parseObject(backupHis.getMessage());
        switch (appKind) {
            case Elasticsearch:
            case Redis:
            case Redis_Cluster:
            case OpenGauss:
                return getFtpBackupPath(appCommonService.get(backupHis.getAppId()));
            case MYSQL_MGR:
            case MYSQL_HA:
                return backupMessage.getString(CloudAppConstant.BACKUP_PATH_KEY);
            case MongoDB:
                // e.g. {"backupFtpPath":"/backup/MongoDB/yan-test/t0504", "backupFilename":"20230506094325"}
                // 备份文件实际在 backupFtpPath/backupFilename目录下，每个db一个子目录
                return backupMessage.getString("backupFtpPath");
            case MongoDB_Cluster:
            case PostgreSQL:
                return backupMessage.getString(CloudAppConstant.BACKUP_PATH_KEY);
            default:
                throw new UnsupportedOperationException();
        }
    }

    /**
     * @return [avail, expect]
     */
    private Tuple<Long, Long> checkBackupStorageSpaceForBackupFile(String ftpDir, String filename, String podName, KubeClient kubeClient, String namespace, String backupRootPath) throws Exception{
        long ftpFileTotalSize = Arrays.stream(filename.split(",")).parallel()
                .mapToLong(name -> {
                    try {
                        return ftpUtil.getFileSize(Paths.get(ftpDir + "/" + name.trim()));
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }).sum();
        long availableDiskSpace = KubeClientUtil.dfAvail(kubeClient, podName, "ftp", namespace, backupRootPath);
        return Tuple.tuple(availableDiskSpace, ftpFileTotalSize);

    }


    /**
     * @return [backupAvail, dataUsage(estimate need)]
     */
    public Tuple<Long, Long> checkBackupAndDataStorageSpace(Integer appId) {
        CloudApp app = appCommonService.get(appId);
        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        List<PodDTO> backupPod = null;
        switch (appKind) { // todo
            case MYSQL_HA:
                backupPod = Collections.singletonList(getBackupPodMySQL(app, kubeClient));
                break;
            case MYSQL_MGR:
                backupPod = Collections.singletonList(getBackupPodMySQL(app, kubeClient));
                break;
            case MongoDB:
                backupPod = Collections.singletonList(getBackupPodMongoDB(app, kubeClient, appKind));
                break;
            case Elasticsearch:
                backupPod = Collections.singletonList(getBackupPodEs(app, kubeClient));
                break;
            case OpenGauss:
                backupPod = Collections.singletonList(getBackupPodOpengauss(app, kubeClient));
                break;
            case PostgreSQL:
                backupPod = Collections.singletonList(getBackupPodPostgreSQL(app, kubeClient));
                break;
            case Redis:
                backupPod = Collections.singletonList(getBackupPodRedis(app, kubeClient));
                break;
            case Redis_Cluster:
                backupPod = getBackupPodRedisCluster(app, kubeClient);
                break;
            case MongoDB_Cluster:
                backupPod = getBackupPodMongoDBCluster(app, kubeClient);
                break;
            default:
                throw new UnsupportedOperationException();
        }
        log.info("check backup storage condition: {}", backupPod.stream().map(PodDTO::getPodName).collect(Collectors.joining(",")));
        // 获取备份pod pvc大小
//        List<MetricVO.ValueVO> diskMetrics = metricService.queryDiskByPods(Instant.now().toEpochMilli(), ImmutableSet.of(app.getNamespace() + "/" + backupPod.getPodName()), app.getKubeId());
        // 区分数据pvc和备份pvc
//        MetricVO.ValueVO backupMetric = diskMetrics.stream().filter(metric -> AppUtil.isBackupPVC(app, metric.getPvc())).findAny().orElseThrow(() -> new IllegalStateException("没有备份存储指标"));
//        MetricVO.ValueVO dataMetric = diskMetrics.stream().filter(metric -> AppUtil.isDataPVC(app, metric.getPvc())).findAny().orElseThrow(() -> new IllegalStateException("没有数据存储指标"));
        Tuple<Long, Long> result = null;
        ExecutorCompletionService<Tuple<Long, Long>> executorService = new ExecutorCompletionService<>(ThreadUtil.getExecutor());
        List<Future> futures = new ArrayList<>();
        for (PodDTO podDTO : backupPod) {
            Future<Tuple<Long, Long>> future = executorService.submit(() -> {
                long dataUsageSpace = KubeClientUtil.dfUsage(kubeClient, podDTO.getPodName(), appKind.getContainerName(), app.getNamespace(), AppUtil.getMainContainerPath(appKind));
                long backupAvailSpace = KubeClientUtil.dfAvail(kubeClient, podDTO.getPodName(), "ftp", app.getNamespace(), AppUtil.getBackupContainerPath(appKind));
                log.info("check backup storage condition: backup avail {}, data used {}", backupAvailSpace, dataUsageSpace);
                return Tuple.tuple(backupAvailSpace, dataUsageSpace);
            });
            futures.add(future);
        }
        for (int i = 0; i < futures.size(); i++) {
            try {
                result = executorService.take().get();
                if (result.v1() < result.v2()) {// if backupAvailSpace < dataUsageSpace
                    return result;
                }
            } catch (InterruptedException | ExecutionException ignore) {
                log.error("", ignore);
            }
        }
        for (Future f : futures) {
            if (!f.isDone())
                f.cancel(true);
        }
        return result;
    }

    public List<PodDTO> getBackupPodMongoDBCluster(CloudApp app, KubeClient kubeClient) {
        try {
            List<PodDTO> pods = kubeClient.listPod(app.getNamespace(), AppKind.MongoDB_Cluster.labelOfPod(app));
            List<String> increPods = new ArrayList<>();
            //遍历所有config和shard，上传pbmPitr
            for(PodDTO ins : pods){
                if(!(CloudAppConstant.MongoDB.MONGOS).equalsIgnoreCase(MongoUtil.getComponentKind(ins, app))){
                    increPods.add(ins.getPodName());
                }
            }
            List<PodDTO> podList = kubeClient.listPod(app.getNamespace(), AppKind.MongoDB_Cluster.labelOfPod(app));
            List<PodDTO> resList = podList.stream().filter(podDTO -> increPods.contains(podDTO.getPodName())).collect(Collectors.toList());
            return resList;
        } catch (Exception e) {
            log.error("mongodb备份获取备份节点失败！", e);
            return null;
        }
    }

    // TODO 重构, 1. 提取接口 2. 计算不准确，不同应用类型计算不同，对于分片应用，数据大小计算应该为所有分片大小和， 3. 依赖备份时记录，代码分散
    public Map<String, Map<String, Object>> checkRestore(CloudApp restoreApp, BackupHis backupHis, String podName) {
        Map<String, Map<String, Object>> result = new HashMap<>();
        String k1 = "name", v1 = "备份存储空间";
        Map<String, Object> cond1 = ImmutableMap.of("bool", true, k1, v1);
        String k2 = "name", v2 = "数据存储空间";
        Map<String, Object> cond2 = ImmutableMap.of("bool", true, k2, v2);
//        if (AppKind.MongoDB_Cluster.getKind().equalsIgnoreCase(restoreApp.getKind()) && AppKind.MongoDB_Cluster.getArch().equalsIgnoreCase(restoreApp.getArch())) {
//            result.put("backupSizeSufficientCondition", cond1);
//            result.put("dataSizeSufficientCondition", cond2);
//            return result;
//        }
        // 1. 校验备份目录是否有足够空间下载备份文件；
        String ftpDir = getBackFileFtpPath(backupHis, restoreApp);
        String filename = getBackupHisFileName(backupHis, restoreApp);

        KubeClient kubeClient = kubeClientService.get(restoreApp.getKubeId());
        if (podName == null) {
//            podName = backupHis.getPodName().split(",")[0]; // fixme
            AppKind appKind = AppKind.valueOf(restoreApp.getKind(), restoreApp.getArch());
            switch (appKind) {
                case MYSQL_HA:
                    podName = getRestorePodMySQL(restoreApp, kubeClient).stream().map(p -> p.getPodName()).collect(Collectors.joining(","));
                    break;
                case Elasticsearch:
                    podName = getRestorePodEs(restoreApp, kubeClient).stream().map(p -> p.getPodName()).collect(Collectors.joining(","));
                    break;
                case Redis:
                    podName = getRestorePodNameRedis(restoreApp, kubeClient);
                    break;
                case Redis_Cluster:
                    podName = getRestorePodNameRedisCluster(restoreApp, kubeClient);
                    break;
                case MongoDB:
                    podName = getRestorePodMongoDB(restoreApp, kubeClient).stream().map(p -> p.getPodName()).collect(Collectors.joining(","));;
                    break;
                case OpenGauss:
                    podName = getRestorePodOpengauss(restoreApp, kubeClient).getPodName();
                    break;
                case MongoDB_Cluster:
                    podName = getRestorePodNameMongoCluster(restoreApp, kubeClient);
                    break;
                default:
                    podName = kubeClient.listPod(restoreApp.getNamespace(), appKind.labelOfPod(restoreApp))
                            .stream().findFirst().map(p -> p.getPodName()).orElseThrow(IllegalStateException::new);
            }
        }
        try {
            cond1 = ImmutableMap.of("bool", true, k1, v1);
//            final String format = "Pod-%s 备份空间不足，需要 %s，实际 %s";
            for (String singlePodName : podName.split(",")) {
                Tuple<Long, Long> tuple = checkBackupStorageSpaceForBackupFile(ftpDir, filename,
                        singlePodName.trim(),
                        kubeClient, restoreApp.getNamespace(), AppUtil.getBackupContainerPath(AppKind.valueOf(restoreApp.getKind(), restoreApp.getArch())));
                log.info("[restore check] 校验备份存储空间, pod {} , 备份空间 {}, 备份文件 {}", singlePodName, tuple.v1(), tuple.v2());
                if (tuple.v1() < tuple.v2()) {
//                    formattedErrReport.add(String.format(format, singlePodName, tuple.v2(), tuple.v1()));
                    cond1 = ImmutableMap.of("bool", false, "msg", "小于备份文件大小 " + singlePodName, "free", tuple.v1(), "need(estimated)", tuple.v2(), k1, v1);
                    break;
                }
            }
        } catch (Exception e) {
            log.error("checkBackupStorageSpaceForBackupFile", e);
            cond1 = ImmutableMap.of("bool", false, "msg", "校验备份目录可用空间失败，" + e.getMessage(), k1, v1);
        }
        // 2. 校验备份文件代表的数据大小是否超出恢复目标应用的数据存储
        String disk = restoreApp.getDisk();
        // 从备份历史中解析记录的数据大小
        JSONObject backupMessage = JSON.parseObject(backupHis.getMessage());
        StringBuilder msg = new StringBuilder();
        if (StringUtils.isEmpty(backupMessage.getJSONObject("dataSize"))) {
            log.info("[restore check] 无法确定原数据大小-备份时未记录该信息");
//            cond2 = ImmutableMap.of("bool", false, "msg", "无法确定原数据大小-备份时未记录该信息", k2, v2);
        } else {
            long targetDiskSize = MetricUtil.getLongValue(disk);
            // 对于MongodbCluster, 数据大小为整个集群所有分片primary的和，
            if (AppKind.valueOf(restoreApp.getKind(), restoreApp.getArch()) == AppKind.MongoDB_Cluster) {
                MongoDBCluster cr = YamlEngine.unmarshal(restoreApp.getCr(), MongoDBCluster.class);
                Integer shardCount = cr.getSpec().getShardServers().getShardCount();
                targetDiskSize = targetDiskSize * shardCount;
            }
            Long dataSize = null;
            for (Map.Entry<String, Object> dataSizeItem : backupMessage.getJSONObject("dataSize").entrySet()) {
                dataSize = Long.valueOf(String.valueOf(dataSizeItem.getValue()));
                log.info("[restore check] 校验备份数据空间, pod {} , 数据空间 {}, 备份数据 {} ", dataSizeItem.getKey(), targetDiskSize, dataSizeItem);
                if (targetDiskSize < dataSize) {
                    msg.append("备份数据大小超出目标pod数据存储空间，pod名称-").append(dataSizeItem.getKey());
                    break;
                }
            }
            if (!StringUtils.isEmpty(msg) && msg.length() > 0)
                cond2 = ImmutableMap.of("bool", false, "msg", msg.toString(), "free", targetDiskSize, "need(estimated)", dataSize, k2, v2);
        }

        result.put("backupSizeSufficientCondition", cond1);
        result.put("dataSizeSufficientCondition", cond2);
        return result;
    }


    /**
     * @deprecated
     */
    public Map<String, Map<String, Object>> checkBackup(Integer appId) {
        Map<String, Object> cond1 = new HashMap<>(); // ImmutableMap.of("bool", true, "name", "备份存储空间");
        cond1.put("name", "备份存储空间");
        Tuple<Long, Long> tuple = null;
        try {
            tuple = checkBackupAndDataStorageSpace(appId);
            cond1.put("free", tuple.v1());
            cond1.put("need(estimated)", tuple.v2());
            if (tuple.v1() < tuple.v2()) {
                cond1.put("bool", false);
                cond1.put("msg", "小于数据大小");
            } else {
                cond1.put("bool", true);
            }
        } catch (Exception e) {
            log.error("checkBackupAndDataStorageSpace", e);
            cond1 = ImmutableMap.of("bool", false, "msg", "计算错误" + e.getMessage(), "name", "备份存储空间");
        }

        Map<String, Object> cond2 = new HashMap<>();
        cond2.put("name", "FTP存储空间");
        cond2.put("bool", true);
        try {
            Long free = ftpUtil.getDiskFreeSpace();
            if (tuple != null) {
                if (free < tuple.v2()) {
                    cond2.put("msg", "可能不足存放备份文件");
                } else {
                    cond2.put("bool", true);
                }
                cond2.put("free", free);
                cond2.put("need(estimated)", tuple.v2());
            } else {
                cond2.put("bool", false);
                cond2.put("msg", "无法计算出所需空间");
            }
        } catch (Exception e) {
            log.error("ftpSufficientCondition", e);
            // 抑制ftp的报错UI展示
            if (!(e instanceof UnsupportedOperationException)) {
                cond2.put("msg", "无法检查服务器可用空间，" + e.getMessage());
                cond2.put("bool", false);
            }
        }

        Map<String, Map<String, Object>> result = new HashMap<>();
        result.put("backupSizeSufficientCondition", cond1);
        result.put("ftpSufficientCondition", cond2);
        return result;
    }

    /**
     * @return 评估存储空间——[name}: 通过；[name]: 不足， [msg]
     */
    public String serialize(Map<String, Map<String, Object>> resultMap) {
        StringBuilder sb = new StringBuilder();
        sb.append("评估存储空间——\n");
        for (Map<String, Object> value : resultMap.values()) {
            sb.append(value.get("name")).append(", 是否充足:").append(value.get("bool"));
            if (!Boolean.parseBoolean(value.get("bool") + "")) {
                sb.append(", ").append(value.get("msg"))
                        .append("free: " + value.get("free") + ",")
                        .append("need: " + value.get("need(estimated)"));
            }
            sb.append("\n");
        }
        return sb.toString();
    }

    /**
     * 备份流程：全备-backup point-增备-backup point; 恢复 全备 + 增备之间的任何一个point
     * @param backupHis
     * @see BackupUtil#backupCleanup()
     */
    @Transactional
    public void postgresqlBackup(BackupHisVO backupHis) {
        // 1. base backup（热备）: 完成到当前point的全备
        // backup from primary(standby didn't have backup his file which is important to continuous backup
        // 2. 根据backup his file 中的信息
        // 全备的产出物有: basebackup的tar 以及补充的wal file
        // 3. 增备基于上次全备追加wal file
        // ========== others =============
        // pg_stat_progress_basebackup report the progress of backup

        Integer appId = backupHis.getAppId();
        CloudApp app = appCommonService.get(appId);

        //如果是增备，对是否做过全备做校验
        //根据baseTime递归检查全备以及增备是否存在
        if ("incre".equalsIgnoreCase(backupHis.getBackupType())) {
            checkBackupState(appId);
        }

        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        PodDTO backupInstance = getBackupPodPostgreSQL(app, kubeClient);
        insertBackupHis(backupHis, app, backupInstance.getPodName());
        String checkFilename = "check_backup_" + backupHis.getBackupHisId();
        try {
            appCommonService.callScheduler(app, app.getCr(), ImmutableMap.of("checkFile", checkFilename, "backupHisId", backupHis.getBackupHisId()), ActionEnum.BACKUP, PostgresqlBackupRestoreWatch.class);
        } catch (Exception e) {
            throw new CustomException(600, "提交备份失败：" + e.getMessage());
        }

        String backupType = "full";
        if ("incre".equalsIgnoreCase(backupHis.getBackupType())) {
            backupType = "incr";
        }
        try {
            pgBackupAsync(app, backupInstance, checkFilename, backupType);
        } catch (Exception e) {
            throw new CustomException(600, "执行备份失败: " + e.getMessage());
        }
    }

    private PodDTO getBackupPodPostgreSQL(CloudApp app, KubeClient kubeClient) {
        List<PodDTO> primaryList = kubeClient.listPod(app.getNamespace(), AppKind.PostgreSQL.labelOfPod(app))
                .stream().filter(podDTO -> ROLE_PRIMARY.equals(podDTO.getLabels().get(CloudAppConstant.CustomLabels.ROLE)))
                .collect(Collectors.toList());
        CustPreconditions.checkState(primaryList.size() == 1, "无法确定执行备份的主库，1 / " + primaryList.size());
        return primaryList.get(0);
    }

    private void insertBackupHis(BackupHis backupHis, CloudApp app, String backupPodName) {
        backupHis.setStatus(StatusConstant.RUNNING);
        backupHis.setPodName(backupPodName);
        backupHis.setStartTime(Timestamp.valueOf(LocalDateTime.now()));
        backupHis.setKubeName(kubeConfigService.getName(app.getKubeId()));
        backupHis.setArch(app.getArch());
        backupHis.setAppType(app.getKind());
        backupHis.setAppName(app.getName());
        backupHis.setAppId(app.getId());
        backupHis.setVersion(app.getVersion());
        HashMap<String, String> msgMap = new HashMap<>();
        msgMap.put("msg", "备份中...");
        backupHis.setMessage(JsonUtil.toJson(msgMap));
//        backupHis.setBackupType(backupType);
        backupMapper.commitBackup(SCHEMA, CLOUD_BACKUP_HIS, backupHis);
    }

    // backup script postgre-backup.sh backupName
    // 其他脚本内参数：backupRoot="backup" backupPath={backupRoot}/{backupName} walBackupPath="${backupRoot}/pg_wal"
    //
    private void pgBackupAsync(CloudApp app, PodDTO backupInstance, String checkFileName, String backupType) throws Exception {
        //获取配置的备份存储信息
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        //mount目录到远程服务器
        String mountCmd = "";
        if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
            mountCmd = "sh /scripts/mount-remote-storage.sh true /data/backup "
                    + CloudAppConstant.OperatorStorageType.NFS
                    + " " + cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
        } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
            mountCmd = String.format("sh %s %s %s %s %s '%s' %s %s %s",
                    "/scripts/mount-remote-storage.sh", true, "/data/backup",
                    CloudAppConstant.StorageType.S3,
                    cloudBackupStorageVO.getServer(),
                    cloudBackupStorageVO.getRegion(),
                    cloudBackupStorageVO.getBucket(),
                    cloudBackupStorageVO.getAccessKey(),
                    cloudBackupStorageVO.getSecretKey());
        }
        KubeClient client = kubeClientService.get(app.getKubeId());
        try {
            client.execCmd(app.getNamespace(), backupInstance.getPodName(), "mount", "sh", "-c", mountCmd);
        } catch (Exception e) {
            throw new CustomException(600, "备份失败！挂载备份目录失败:" + e);
        }

        String backupCmd = "sh /scripts/postgre-backup.sh " + checkFileName + " " + backupType;
        try {
            client.execCmdOneway(app.getNamespace(), backupInstance.getPodName(), AppKind.PostgreSQL.getContainerName(), "sh", "-c", backupCmd);
        } catch (Exception e) {
            throw new CustomException(600, "备份失败！提交备份失败:" + e);
        }
    }


    // restore script postgres-restore.sh restoreName useRemainWal walPath lsn
    // restoreName- 拼接备份文件路径 /backup/{restoreName}/base.tar.gz
    // todo pg restore in time
    @Transactional
    public void postgresqlRestore(BackupHis backupHis, Integer targetAppId) {
        // TODO 目前为全量恢复，未实现 restore in time point
        CustPreconditions.checkState(backupHis.getStatus().equals(StatusConstant.SUCCESS), "备份没有成功");
        CloudApp restoreApp = appCommonService.get(targetAppId);
        RestoreHis his = insertRestoreHis(restoreApp, Collections.singletonList(backupHis), "", "");
        String backupFileDir = backupHis.getFileName();
        CloudApp backupApp = cloudAppService.get(backupHis.getAppId());
        CustPreconditions.checkState(!StringUtils.isEmpty(backupFileDir), "未找到备份文件名");
        Map map = JsonUtil.toObject(Map.class, backupHis.getMessage());
        String fullSourceName = backupApp.getNamespace() + "/" + backupApp.getCrName() + "/" + backupFileDir;

        KubeClient kubeClient = kubeClientService.get(restoreApp.getKubeId());
        PostgreSql actualCr = kubeClient.listCustomResource(PostgreSql.class, restoreApp.getCrName(), restoreApp.getNamespace());
        PostgreSql cr = YamlEngine.unmarshal(restoreApp.getCr(), PostgreSql.class);
        cr.setSpec(actualCr.getSpec());
        Restore restore = new Restore();
        restore.setFullSource(fullSourceName);
        //获取nas信息
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        HashMap<String, String> remote = new HashMap<>();
        if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
            remote.put("type", CloudAppConstant.OperatorStorageType.NFS);
            remote.put("address", cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath());
        } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
            remote.put("type", CloudAppConstant.StorageType.S3);
            remote.put("address", cloudBackupStorageVO.getServer());
            remote.put("bucket", cloudBackupStorageVO.getBucket());
            remote.put("region", cloudBackupStorageVO.getRegion());
            //获取operator的namespace，因为所有operator都相同，所以统一获取mysql的operatornamespace
            String operatorConfig = sysConfigService.findOne("operator.name", "MySQL");
            String operatorNamespace = operatorConfig.split("/")[0];
            remote.put("secret", operatorNamespace + ":backupstorage-secret");
        } else {
            throw new CustomException(600, "恢复失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
        }
        restore.setRemote(remote);
//        restore.setType(backupStorageInfo.get("type"));
//        restore.setAddress(backupStorageInfo.get("url") + AppKind.PostgreSQL.getKind() + "/");
        cr.getSpec().setRestore(restore);

        try {
            appCommonService.callScheduler(restoreApp, YamlEngine.marshal(cr),
                    ImmutableMap.of("fullSourceName", fullSourceName, "restoreHisId", his.getRestoreHisId(), "backupHisId", backupHis.getBackupHisId()),
                    ActionEnum.RESTORE, PostgresqlBackupRestoreWatch.class);
        } catch (Exception e) {
            throw new CustomException(600, "提交备份失败：" + e.getMessage());
        }

        kubeClient.updateCustomResource(cr, PostgreSql.class);
    }

    public RestoreHis insertRestoreHis(CloudApp restoreApp, List<BackupHis> backupHis, String podName, String restoreDir) {
        RestoreHis his = new RestoreHis();
        his.setStatus(StatusConstant.RUNNING);
        his.setAppId(restoreApp.getId());
        his.setAppType(restoreApp.getKind());
        his.setAppName(restoreApp.getName());
        his.setKubeName(kubeConfigService.getName(restoreApp.getKubeId()));
        his.setPodName(podName);
        his.setStartTime(Timestamp.valueOf(LocalDateTime.now()));
        his.setFileName(backupHis.stream().map(b -> b.getFileName()).collect(Collectors.joining(",")));
        his.setRestoreDir(restoreDir);
        his.setFileDeleted(false);
        restoreMapper.commitRestore(SCHEMA, CLOUD_RESTORE_HIS, his);
        return his;
    }

    public void backupCleanup() {

    }

    public void appendBackupHisMessage(BackupHis backupHis, String key , Object value) {
        String backupHisMessage = backupHis.getMessage();
        JSONObject messageObj = JSON.parseObject(backupHisMessage);
        messageObj.put(key, value);
        backupHis.setMessage(JSON.toJSONString(messageObj));
        backupMapper.updateBackupHis(SCHEMA, CLOUD_BACKUP_HIS, backupHis);
    }

    /**
     * mongodbCluster备份
     *
     * @param backupHis
     */
    @Async
    public void mongodbClusterBackup(BackupHisVO backupHis) {
        BackupHis runningBackupHis = backupMapper.getRunningByAppId(SCHEMA, CLOUD_BACKUP_HIS, backupHis.getAppId());
        if (runningBackupHis != null) {
            throw new CustomException(600, "存在正在执行的备份，请勿重复提交！");
        }
        Integer changeId = null;
        try {
            // 获取应用
            CloudApp app = cloudAppService.get(backupHis.getAppId());
            //获取cr
            MongoDBCluster cr = YamlEngine.unmarshal(app.getCr(), MongoDBCluster.class);
            //分片数
            Integer shardCount = cr.getSpec().getShardServers().getShardCount();
            //操作记录和操作记录id
            ResourceChangeHis resourceChangeHis = null;

            //当前时间
            Timestamp startTime = new Timestamp(System.currentTimeMillis());
            //备份类型
//            backupHis.setBackupType("full");
            //开始时间
            backupHis.setStartTime(startTime);
            //设置备份状态为运行
            backupHis.setStatus(StatusConstant.RUNNING);
            //设置信息为备份中
            JSONObject bkMes = new JSONObject();
            bkMes.put("msg", "备份中...");
            bkMes.put("masterSize", cr.getSpec().getShardServers().getShardCount());
            String s = JSONObject.toJSONString(bkMes);
            backupHis.setMessage(s);
            backupHis.setVersion(app.getVersion());
            //应用所属集群
            KubeConfig byId = kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, app.getKubeId());
            if (null == byId) {
                log.error("未获取到集群！");
                backupReturn(backupHis, null, StatusConstant.FAIL, "", "es备份未获取到集群！");
                return;
            }
            backupHis.setKubeName(byId.getName());
            //插入备份历史
            backupMapper.commitBackup(SCHEMA, CLOUD_BACKUP_HIS, backupHis);
            resourceChangeHis = new ResourceChangeHis() {{
                setInsertTime(startTime);
                setKind(app.getKind());
                setKubeId(app.getKubeId());
                setNamespace(app.getNamespace());
                setCommand("备份");
                setStatus("2");
                setAction("Backup");
                setMsg("备份中...");
                setAppId(app.getId());
                setAppLogicId(app.getLogicAppId());
                setAppName(app.getName());
                setKubeName(byId.getName());
                setYaml(app.getCr());
                setUserName(UserUtil.getAsyncUserinfo().getUsername());
                setUserIp(CloudRequestContext.getContext().getUserIp());
                setLastEndTimestamp(System.currentTimeMillis());
            }};
            changeId = insertResourceChangeHis(resourceChangeHis);
            KubeClient kubeClient = kubeClientService.get(app.getKubeId());

            //判断增备还是全备
            List<AppInstanceVO> instanceList = mongoDbClusterService.findInstanceList(backupHis.getAppId(), "role", "asc");
            if (CloudAppConstant.BackupType.full.equalsIgnoreCase(backupHis.getBackupType())) {
                // 1.获取任意一个config实例
                String backupPodName = null;
                for (AppInstanceVO ins : instanceList) {
                    if (CloudAppConstant.MongoDB.CONFIG_NAME.equalsIgnoreCase(ins.getComponentKind())) {
                        backupPodName = ins.getPodName();
                        break;
                    }
                }
                if (null == backupPodName) {
                    log.error("MongoDBCluster未获取到备份的config实例！");
                    backupReturn(backupHis, null, StatusConstant.FAIL, "", "MongoDBCluster未获取到备份的config实例！");
                    return;
                } else {
                    backupHis.setPodName(backupPodName);
                    //修改备份历史，将podName插入
                    backupMapper.updateBackupHis(SCHEMA, CLOUD_BACKUP_HIS, backupHis);
                }
                SimpleDateFormat sdfBackup = new SimpleDateFormat("yyyyMMddHHmmss");
                String backupLogName = sdfBackup.format(startTime);
                try {
                    // 3.在任意一个config节点上执行备份脚本
                    kubeClient.execCmdOneway(app.getNamespace(), backupPodName, "pbm", "sh", "-c", "sh /scripts/pbm-backup.sh " + backupLogName);
                } catch (Exception e) {
                    backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "MongoDBCluster执行备份失败！错误信息：" + e.getMessage());
                    return;
                }

                // 4.创建定时回调参数
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("backupHisId", backupHis.getBackupHisId());
                dataMap.put("changeId", changeId);
                dataMap.put("backupPodName", backupPodName);
                dataMap.put("backupFtpPath", this.getFtpBackupPath(app));
                dataMap.put("logName", backupLogName);
                dataMap.put("backupType", CloudAppConstant.BackupType.full);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
                dataMap.put("backupStartDateSDF", sdf.format(startTime));
                // 创建定时回调
                try {
                    appCommonService.callScheduler(app, app.getCr(), dataMap, ActionEnum.BACKUP, MongoDBClusterBackupAndRestoreWatch.class, resourceChangeHis);
                } catch (Exception e) {
                    backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "MongoDBCluster备份提交定时失败！错误信息：" + e.getMessage());
                    return;
                }
            } else {
                //找到之前最近的一次成功的备份
                BackupHis closeBackupHisBeforeRestoreTime = backupMapper.getLastSuccessFullBackupHis(SCHEMA, CLOUD_BACKUP_HIS, app.getId());
                if(ObjectUtils.isEmpty(closeBackupHisBeforeRestoreTime)){
                    backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "MongoDBCluster执行备份失败！未获取到opLog基于的全备！");
                    return;
                }
                backupHis.setBaseTime(closeBackupHisBeforeRestoreTime.getBaseTime());
                List<String> increPods = new ArrayList<>();
                //遍历所有config和shard，上传pbmPitr
                for (AppInstanceVO ins : instanceList) {
                    if (!(CloudAppConstant.MongoDB.MONGOS).equalsIgnoreCase(ins.getComponentKind())) {
                        increPods.add(ins.getPodName());
                        //上传pbmPitr
                        String pbmPitrRes = null;
                        try {
                            String pbmPitrResStr = kubeClient.execCmd(app.getNamespace(), ins.getPodName(), "pbm", "sh", "-c", "if [[ -d /backup/pbmPitr ]]; then echo \"yes\"; else echo \"no\"; fi");
                            pbmPitrRes = pbmPitrResStr.replace("\n", "");
                        } catch (Exception e) {
                            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "MongoDBCluster增备查询pbmpitr文件失败！错误信息：" + e.getMessage());
                            return;
                        }
                        if ("yes".equalsIgnoreCase(pbmPitrRes)) {
                            try {
                                Map backupMes = JsonUtil.toObject(Map.class, backupHis.getMessage());
                                String isCommitUpload = (String) backupMes.get("isCommitUpload_pbmPitr" + ins.getPodName());
                                if (StringUtils.isEmpty(isCommitUpload) || !"Y".equalsIgnoreCase(isCommitUpload)) {
                                    uploadBackupFile(app, ins.getPodName(), this.getFtpBackupPath(app), "/backup/pbmPitr", backupHis.getBackupHisId(), "_pbmPitr");
                                    //对备份历史放入已提交上传的标记
                                    backupMes.put("isCommitUpload_pbmPitr" + ins.getPodName(), "Y");
                                    //插入mes
                                    backupHis.setMessage(JsonUtil.toJson(backupMes));
                                    backupMapper.updateBackupHis(SCHEMA, CLOUD_BACKUP_HIS, backupHis);
                                }
                            } catch (Exception e) {
                                backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "MongoDBCluster备增备上传文件失败！错误信息：" + e.getMessage());
                                return;
                            }
                        }
                    }
                }
                backupHis.setPodName(increPods.toString()
                        .replace("[", "")
                        .replace("]", "")
                        .replace("\"", ""));
                backupMapper.updateBackupHis(SCHEMA, CLOUD_BACKUP_HIS, backupHis);
                // 创建定时回调
                try {
                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("backupType", CloudAppConstant.BackupType.incre);
                    dataMap.put("backupHisId", backupHis.getBackupHisId());
                    dataMap.put("changeId", changeId);
                    dataMap.put("backupFtpPath", this.getFtpBackupPath(app));
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
                    dataMap.put("backupStartDateSDF", sdf.format(startTime));
                    appCommonService.callScheduler(app, app.getCr(), dataMap, ActionEnum.BACKUP, MongoDBClusterBackupAndRestoreWatch.class, resourceChangeHis);
                } catch (Exception e) {
                    backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "MongoDBCluster备份提交定时失败！错误信息：" + e.getMessage());
                    return;
                }
            }
        } catch (Exception e) {
            log.error("提交备份出错！信息为：" + e);
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "MongoDBCluster备份提交定时失败！错误信息：" + e.getMessage());
            return;
        }

    }

    /**
     * mongodbcluster恢复
     *
     * @param backupHis
     * @param appId
     * @param restoreTime
     */
    public void mongodbClusterRestore(BackupHis backupHis, Integer appId, String restoreTime) {
        // 0. 创建还原的对象
        RestoreHis restoreHis = new RestoreHis();
//        restoreTime = restoreTime.replace("T", " ").replace("Z", "");
        restoreHis.setStatus(StatusConstant.RUNNING);
        Date startDate = new Date();
//        String finRestoreTime = "";
//        if(null != restoreTime){
//            //时间转换，目标样例：2023-06-01T18:35:08Z
//            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            String restoreTimeStr = sdf.format(restoreTime);
//            finRestoreTime = restoreTimeStr.replace(" ", "T");
//            finRestoreTime += "Z";
//        }

        //操作记录
        ResourceChangeHis resourceChangeHis = null;
        Integer changeId = null;
        try {
            Timestamp startTime = new Timestamp(System.currentTimeMillis());
            restoreHis.setStartTime(startTime);
            // 1. 插入基础恢复历史和操作记录
            CloudApp goalApp = cloudAppMapper.selectByPrimaryKey(appId, DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_APP);
            restoreHis.setAppId(appId);
            restoreHis.setAppName(goalApp.getName());
            restoreHis.setAppType(goalApp.getKind());
            KubeClient kubeClient = kubeClientService.get(goalApp.getKubeId());
            MongoDBCluster cr = YamlEngine.unmarshal(goalApp.getCr(), MongoDBCluster.class);

            //应用所属集群
            KubeConfig byId = kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, goalApp.getKubeId());
            if (null == byId) {
                log.error("未获取到集群！");
                restoreReturn(restoreHis, changeId, "未获取到集群！", StatusConstant.FAIL);
                return;
            }
            restoreHis.setKubeName(byId.getName());
            restoreHis.setMessage("恢复中...");
            restoreHis.setFileDeleted(false);
            restoreHis.setPodName(getRestorePodNameMongoCluster(goalApp, null));
            String backDirInPod = "/backup/";
            restoreHis.setRestoreDir(backDirInPod);
            if(null != backupHis){
                restoreHis.setFileName(backupHis.getFileName());
            }
            restoreMapper.commitRestore(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_RESTORE_HIS, restoreHis);
            //插入操作记录
            resourceChangeHis = new ResourceChangeHis() {{
                setInsertTime(startTime);
                setKind(goalApp.getKind());
                setKubeId(goalApp.getKubeId());
                setNamespace(goalApp.getNamespace());
                setCommand("恢复");
                setStatus("2");
                setAction(ActionEnum.RESTORE.getActionType());
                setMsg("恢复中...");
                setAppId(goalApp.getId());
                setAppName(goalApp.getName());
                setUserName(UserUtil.getAsyncUserinfo().getUsername());
                setUserIp(CloudRequestContext.getContext().getUserIp());
                setKubeName(byId.getName());
                setYaml(goalApp.getCr());
                setLastEndTimestamp(System.currentTimeMillis());
                setAppLogicId(goalApp.getLogicAppId());
            }};
            changeId = insertResourceChangeHis(resourceChangeHis);

            // 2.修改cr的Spec.Restore.FullSource以及Spec.Restore.LastTimestamp属性
            CloudApp backupApp = cloudAppService.get(backupHis.getAppId());
//            Map<String, String> backupStorageInfo = getBackupStorageInfo();
            MongoDBCluster.MongoDBClusterSpec.Restore restore = new MongoDBCluster.MongoDBClusterSpec.Restore();
            restore.setFullSource(backupApp.getNamespace() + "/" + backupApp.getCrName() + "/" + backupHis.getFileName());
//            restore.setAddress(backupStorageInfo.get("url") + AppKind.MongoDB_Cluster.getKind() + "/");
//            restore.setType(backupStorageInfo.get("type"));
            cr.getSpec().setRestore(restore);

            //提交cr
            kubeClient.updateCustomResource(cr, MongoDBCluster.class);
            //创建定时轮询备份结果
            Map map = new HashMap();
            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
            map.put("restoreStartDateSDF", sdf1.format(startDate));
            map.put("restoreHisId", restoreHis.getRestoreHisId());
            map.put("resourceChangeId", changeId);
            appCommonService.callScheduler(goalApp, YamlEngine.marshal(cr),map,ActionEnum.RESTORE,MongoDBClusterBackupAndRestoreWatch.class,resourceChangeHis);
        }catch (Exception e){
            log.error("提交恢复出错！信息为：" + e);
            restoreReturn(restoreHis, changeId, e.getMessage(), StatusConstant.FAIL);
        }
    }

    public String getRestorePodNameMongoCluster(CloudApp restoreApp, KubeClient client) {
        List<AppInstanceVO> instanceList = mongoDbClusterService.findInstanceList(restoreApp.getId(), "role", "asc");
        List<String> podNames = instanceList.stream().filter(ins -> !CloudAppConstant.MongoDB.MONGOS.equalsIgnoreCase(ins.getComponentKind()))
                .map(ins -> ins.getPodName()).collect(Collectors.toList());
        return podNames.toString()
                .replace("[", "")
                .replace("]", "")
                .replace("\"", "");
    }

    /**
     * 查询上传结果
     * @param backupHis
     * @param app
     * @param suffix
     * @param podName
     * @return
     */
    public String checkUploadBackupFile(BackupHis backupHis, CloudApp app, String suffix, String podName) {
        // 1.到容器中判断是否存在日志文件
        //判断文件命令
        String checkLogIsExist = "";
        if(CloudAppConstant.Kind.OPENGAUSS.equalsIgnoreCase(app.getKind())){
            checkLogIsExist = "if [[ -f /gaussdata/openGauss/db1/uploadlog_" + backupHis.getBackupHisId() + suffix + " ]]; then echo \"yes\"; else echo \"no\"; fi";
        }else{
            checkLogIsExist = "if [[ -f /data/tmp/uploadlog_" + backupHis.getBackupHisId() + suffix + " ]]; then echo \"yes\"; else echo \"no\"; fi";
        }
        String checkLogIsExistRes = "";
        try {
            if(StringUtils.isEmpty(podName)){
                checkLogIsExistRes = kubeClientService.get(app.getKubeId()).execCmd(app.getNamespace(), backupHis.getPodName(), "ftp", "sh", "-c", checkLogIsExist);
            }else{
                checkLogIsExistRes = kubeClientService.get(app.getKubeId()).execCmd(app.getNamespace(), podName, "ftp", "sh", "-c", checkLogIsExist);
            }
            checkLogIsExistRes = checkLogIsExistRes.replace("\n", "");
        } catch (Exception e) {
            log.error("查询上传日志是否存在报错！信息为：" + e.getMessage());
            throw new CustomException(500, "查询上传日志是否存在报错！信息为：" + e.getMessage());
        }
        // 2.存在则判断日志文件结果
        if("yes".equalsIgnoreCase(checkLogIsExistRes)){
            //存在日志
            String catLog = "";
            if(CloudAppConstant.Kind.OPENGAUSS.equalsIgnoreCase(app.getKind())){
                catLog = "cat /gaussdata/openGauss/db1/uploadlog_" + backupHis.getBackupHisId() + suffix;
            }else{
                catLog = "cat /data/tmp/uploadlog_" + backupHis.getBackupHisId() + suffix;
            }
            String catLogRes = "";
            try {
                if(StringUtils.isEmpty(podName)){
                    catLogRes = kubeClientService.get(app.getKubeId()).execCmd(app.getNamespace(), backupHis.getPodName(), "ftp", "sh", "-c", catLog);
                }else{
                    catLogRes = kubeClientService.get(app.getKubeId()).execCmd(app.getNamespace(), podName, "ftp", "sh", "-c", catLog);
                }
            } catch (Exception e) {
                log.error("查询上传日志结果报错！信息为：" + e.getMessage());
                throw new CustomException(500, "查询上传日志结果报错！信息为：" + e.getMessage());
            }
            Map catLogResObj = JsonUtil.toObject(Map.class, catLogRes);
            String catLogResRes = (String) catLogResObj.get("status");
            System.out.println("检查结果：" + catLogResRes);
            return catLogResRes;
        }else{
            //不存在日志，说明还在上传中
            return CloudAppConstant.UploadBackupFileState.running;
        }
    }

    /**
     * 获取超时时间
     * @param appId
     * @return
     */
    public Integer getBackupTimeOut(Integer appId){
        //获取超时时间
        List<BackupTimer> backupTimerByAppId = backupTimerMapper.getBackupTimerByAppId(SCHEMA, CLOUD_BACKUP_TIMER, appId);
        Integer backupTimeOut = 1;
        if(!CollectionUtils.isEmpty(backupTimerByAppId)){
            backupTimeOut = backupTimerByAppId.get(0).getMaxBackupDuration();
        }
        return backupTimeOut;
    }

    @Transactional
    public void mgrBackup(BackupHisVO backupHis) {
        // xtrabackup 容器 执行 /scripts/mysql-backup-full.sh chkFile defaultsfile subPath
        // subPath={kind}/{namespace}/{clustername}
        // 备份产物位于 xtrabackup 容器 /backup/{kind}/{namespace}/{clustername}/backup/{file}
        // script log 在/backup/${subPath}/backup/{logFile}
        // checkFile 最终位于/data/tmp/checkFile
        Integer appId = backupHis.getAppId();
        CloudApp app = appCommonService.get(appId);

        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        PodDTO backupInstance = getBackupPodMgr(app, kubeClient).orElseThrow(() -> new CustomException(600, "查询主库进行备份"));
        insertBackupHis(backupHis, app, backupInstance.getPodName());
        String checkFilename = "check_backup_" + backupHis.getBackupHisId();
        Integer changeId;
        try {
            BinlogBackupHis latestBinlogBackupHis = backupService.listBinlogBackupHis(appId);
            Map<String, Object> jobDataMap = new HashMap<>();
            jobDataMap.put("checkFile", checkFilename);
            jobDataMap.put("backupHisId", backupHis.getBackupHisId());
            jobDataMap.put("binlogBackupFlag", backupHis.getBackupDbLog());
            jobDataMap.put("latestBinlogCodes", latestBinlogBackupHis == null ? "" : latestBinlogBackupHis.getFileCodes());
            changeId = appCommonService.callScheduler(app, app.getCr(), jobDataMap, ActionEnum.BACKUP, InnoDBClusterBackupRestoreWatch.class);
        } catch (Exception e) {
            throw new CustomException(600, "提交备份失败：" + e.getMessage());
        }

//        Map<String, String> backupStorageInfo = getBackupStorageInfo();
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        String cmd = "";
        if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
            String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
            if (CloudAppConstant.BackupType.full.equals(backupHis.getBackupType())) {
                // 异步执行备份脚本，watcher类 观测备份执行情况，备份结束后需返回备份的产出物，在watcher中上传到backupserver并在备份历史中记录
                try {
                    cmd = "sh /scripts/mysql-backup-full.sh "
                            + checkFilename + " "
                            + "/etc/my.cnf "
                            + CloudAppConstant.OperatorStorageType.NFS + " "
                            + mountPath;
                    kubeClientService.get(app.getKubeId()).execCmdOneway(app.getNamespace(), backupInstance.getPodName(), XTRABACKUP_CONTAINER_NAME,
                            "sh", "-c", cmd);
//                kubeClientService.get(app.getKubeId()).execCmd(app.getNamespace(), backupInstance.getPodName(), "xtrabackup",
//                        "sh", "-c", String.format("sh /scripts/mysql-backup-full.sh %s /etc/my.cnf", checkFilename));
                } catch (Exception e) {
                    throw new CustomException(600, "执行备份失败: " + e.getMessage());
                }
            } else if (CloudAppConstant.BackupType.incre.equals(backupHis.getBackupType())) { // 增备基于最后一次备份点进行备份
//            TODO pg 增备 increBackupAsync(app, backupInstance, fullBackupHisId); // 找到全备的备份历史
                BackupHis fullBackupHis = backupMapper
                        .getLastSuccessFullBackupHis(SCHEMA, CLOUD_BACKUP_HIS, appId);
                if (fullBackupHis == null) {
                    log.error("没有成功全备历史！");
                    log.info("[mgr备份]没有成功全备历史！");
                    backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "没有成功全备历史！");
                    return;
                }
                try {
                    cmd = "sh /scripts/mysql-backup-inc.sh "
                            + checkFilename + " "
                            + fullBackupHis.getBaseTime() + " "
                            + CloudAppConstant.OperatorStorageType.NFS + " "
                            + mountPath;
                    kubeClientService.get(app.getKubeId()).execCmdOneway(app.getNamespace(), backupInstance.getPodName(), XTRABACKUP_CONTAINER_NAME,
                            "sh", "-c", cmd);
                } catch (Exception e) {
                    throw new CustomException(600, "执行增备失败: " + e.getMessage());
                }

            }
        } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
            String region = cloudBackupStorageVO.getRegion();
            if (StringUtils.isEmpty(region)) {
                region = "\"\"";
            }
            if (CloudAppConstant.BackupType.full.equals(backupHis.getBackupType())) {
                // 异步执行备份脚本，watcher类 观测备份执行情况，备份结束后需返回备份的产出物，在watcher中上传到backupserver并在备份历史中记录
                try {
                    cmd = "sh /scripts/mysql-backup-full.sh "
                            + checkFilename + " "
                            + "/etc/my.cnf "
                            + CloudAppConstant.StorageType.S3 + " "
                            + cloudBackupStorageVO.getServer() + " "
                            + region + " "
                            + cloudBackupStorageVO.getBucket() + " "
                            + cloudBackupStorageVO.getAccessKey() + " "
                            + cloudBackupStorageVO.getSecretKey();
                    kubeClientService.get(app.getKubeId()).execCmdOneway(app.getNamespace(), backupInstance.getPodName(), XTRABACKUP_CONTAINER_NAME,
                            "sh", "-c", cmd);
//                kubeClientService.get(app.getKubeId()).execCmd(app.getNamespace(), backupInstance.getPodName(), "xtrabackup",
//                        "sh", "-c", String.format("sh /scripts/mysql-backup-full.sh %s /etc/my.cnf", checkFilename));
                } catch (Exception e) {
                    throw new CustomException(600, "执行备份失败: " + e.getMessage());
                }
            } else if (CloudAppConstant.BackupType.incre.equals(backupHis.getBackupType())) { // 增备基于最后一次备份点进行备份
//            TODO pg 增备 increBackupAsync(app, backupInstance, fullBackupHisId); // 找到全备的备份历史
                BackupHis fullBackupHis = backupMapper
                        .getLastSuccessFullBackupHis(SCHEMA, CLOUD_BACKUP_HIS, appId);
                if (ObjectUtils.isEmpty(fullBackupHis)) {
                    log.error("没有成功全备历史！");
                    log.info("[mgr备份]没有成功全备历史！");
                    backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "没有成功全备历史！");
                    return;
                }
                try {
                    cmd = "sh /scripts/mysql-backup-inc.sh "
                            + checkFilename + " "
                            + fullBackupHis.getBaseTime() + " "
                            + CloudAppConstant.StorageType.S3 + " "
                            + cloudBackupStorageVO.getServer() + " "
                            + region + " "
                            + cloudBackupStorageVO.getBucket() + " "
                            + cloudBackupStorageVO.getAccessKey() + " "
                            + cloudBackupStorageVO.getSecretKey();
                    kubeClientService.get(app.getKubeId()).execCmdOneway(app.getNamespace(), backupInstance.getPodName(), XTRABACKUP_CONTAINER_NAME,
                            "sh", "-c", cmd);
                } catch (Exception e) {
                    throw new CustomException(600, "执行增备失败: " + e.getMessage());
                }

            }
        } else {
            throw new CustomException(600, "备份失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
        }
    }

    public Optional<PodDTO> getBackupPodMgr(CloudApp app, KubeClient kubeClient) {
        return kubeClient.listPod(app.getNamespace(), AppKind.MYSQL_MGR.labelOfPod(app))
                .stream().filter(podDTO -> MGRUtil.Constants.ROLE_PRIMARY.equals(podDTO.getLabels().get(MGRUtil.Constants.ROLE_LABEL)))
                .findAny();
    }

    public String buildRestorePath(CloudApp backApp) {
        return ClickhouseService.CLICKHOUSE_S3_PATH + "/shared/" + AppKind.Clickhouse.getKind() + "/" + AppKind.Clickhouse.getArch() + "/" + backApp.getNamespace() + "/" + backApp.getCrName();
    }

    /**
     * @param backupHis
     */
    @Transactional
    public void clickHouseBackup(BackupHisVO backupHis) {
        //判断是否有在进行的备份
        Integer appId = backupHis.getAppId();
        BackupHis runningBackupHis = backupMapper.getRunningByAppId(SCHEMA, CLOUD_BACKUP_HIS, appId);
        if (runningBackupHis != null)
            throw new CustomException(600, "存在正在执行的备份，请勿重复提交！");

        // 获取备份存储的资源列表
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        if (null == cloudBackupStorageVO)
            throw new CustomException(600, "未查询到相关的备份存储配置信息！");

        String checkStorageResult = backupHis.getMessage();

        CloudApp cloudApp = appCommonService.get(appId);
        //创 备份历史
        //记录备份信息：数据库名称、备份路径、备份文件名称
        Timestamp startTime = new Timestamp(System.currentTimeMillis());
        buildBasicData(backupHis, startTime);
        //应用所属集群
        KubeConfig kubeConfig = kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, cloudApp.getKubeId());
        if(null == kubeConfig){
            log.error("未获取到集群！");
            backupReturn(backupHis, null, StatusConstant.FAIL, "", "未获取到集群！");
            return;
        }
        backupHis.setKubeName(kubeConfig.getName());
        backupMapper.commitBackup(SCHEMA, CLOUD_BACKUP_HIS, backupHis);

        ResourceChangeHis resourceChangeHis = createBasicHis(cloudApp, startTime, kubeConfig);
        resourceChangeHis.setCommand("备份");
        resourceChangeHis.setAction("Backup");
        resourceChangeHis.setMsg("备份中...".concat("\n").concat(null == checkStorageResult ? "" : checkStorageResult));
        Integer changeId = insertResourceChangeHis(resourceChangeHis);

        KubeClient kubeClient = kubeClientService.get(cloudApp.getKubeId());
        String namespace = cloudApp.getNamespace();
        ClickHouseInstallation clickHouseCluster = kubeClient.listCustomResource(ClickHouseInstallation.class, cloudApp.getCrName(), namespace);
        //需要进入每个分片中的第一个副本中执行 备份操作 <clickhouse-backup 容器>
        List<String> clickhousePods = clickHouseCluster.getStatus().getPods();

        int shardNum = clickHouseCluster.getSpec().getConfiguration().getClusters().get(0).getLayout().getShardsCount().intValue();
        List<String> backupPodList = clickhousePods.stream().filter(podName -> podName.split(cloudApp.getCrName() + "-" + ClickhouseUtil.getClickhouseClusterName() + "-")[1].split("-")[1].equals(String.valueOf("0"))).collect(Collectors.toList());

        backupHis.setPodName(backupPodList.stream().collect(Collectors.joining(",")));
        if (shardNum != backupPodList.size()) {
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "clikhouse备份失败！错误信息：查询到的执行备份操作 节点 数量与应该执行的备份操作数量不一致，查询到数量：" + backupPodList.size() + ",应该执行备份操作数量：" + shardNum);
            return;
        }

        Queue<String> backupFileNames = new ConcurrentLinkedQueue<>();
        if (CloudAppConstant.BackupType.full.equals(backupHis.getBackupType())) {
            backupPodList.parallelStream().forEach(podName -> {
                try {
                    String backupScript = "";
                    String backupFileName = buildBackupName(cloudApp, podName);
                    String compressBackupFileName = backupFileName + ".tar.gz";
                    backupFileNames.add(compressBackupFileName);
                        //判断备份存储类型 进行 手动 路径挂载
                    if (CloudAppConstant.StorageType.NAS.equalsIgnoreCase(cloudBackupStorageVO.getStorageType())) {
                        String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
                        backupScript = "bash /scripts/clickhouse-backup.sh " + backupFileName + " true " + CloudAppConstant.OperatorStorageType.NFS + " " + mountPath;
                    } else if (CloudAppConstant.StorageType.S3.equalsIgnoreCase(cloudBackupStorageVO.getStorageType())) {
                        //执行 手动 mount 操作
                        kubeClient.execCmdOneway(namespace, podName, "s3fs-container", "sh", "-c", getS3MountCommand(cloudBackupStorageVO));
                        backupScript = "bash /scripts/clickhouse-backup.sh " + backupFileName + " false ";
                    } else {
                        throw new CustomException(600, "备份失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
                    }
                    log.info("[clickhouse备份]，执行的语句为：sh -c " + backupScript);
                    //进行 clickhouse-backup create <备份名: shard 数> 备份操作 clickhouse-client -q "SELECT getMacro('shard')" 查看当前pod的分片数
                    kubeClient.execCmdOneway(namespace, podName, "clickhouse-backup", "sh", "-c", backupScript);
                } catch (Exception e) {
                    backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "clickhouse备份失败！错误信息：" + e.getMessage());
                    return;
                }
            });

        } else if (CloudAppConstant.BackupType.incre.equals(backupHis.getBackupType())) { // 增备基于全备名称进行
            //TODO clickhouse 增备 clickhouse-backup create --diff-from-remote <full_backup_name> <backup_name> // 找到全备的备份历史

        }

        backupMapper.updateBackupHis(SCHEMA, CLOUD_BACKUP_HIS, backupHis);

        log.info("[clickhouse备份]开始创建定时");
        Map<String, String> jobDataMap = new HashMap<>();
        try {
            // 创建定时任务轮询执行结果
            //时间转换
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
            jobDataMap.put("backupHisId", backupHis.getBackupHisId().toString());
            jobDataMap.put("changeId", String.valueOf(changeId));
            jobDataMap.put("podNames", backupPodList.stream().collect(Collectors.joining(",")));
            jobDataMap.put("appId", String.valueOf(appId));
            jobDataMap.put("backupStartDateSDF", sdf.format(startTime));
            jobDataMap.put("backupFilename", String.join(",", backupFileNames));
            jobDataMap.put("backupTimeout", String.valueOf(backupHis.getMaxBackupDuration()));
            appCommonService.callScheduler(cloudApp, cloudApp.getCr(), jobDataMap, ActionEnum.BACKUP, ClickhouseBackupAndRestoreWatch.class, resourceChangeHis);
        } catch (Exception e) {
            log.error("[clickhouse备份]创建定时失败" + e.getMessage());
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "clickhouse备份创建定时调度失败！错误信息：" + e.getMessage());
            return;
        }

    }

    /**
     * @param backupHis
     */
    @Transactional
    public void tidbBackup(BackupHisVO backupHis) {
        //判断是否有在进行的备份
        Integer appId = backupHis.getAppId();
        BackupHis runningBackupHis = backupMapper.getRunningByAppId(SCHEMA, CLOUD_BACKUP_HIS, appId);
        if (runningBackupHis != null)
            throw new CustomException(600, "存在正在执行的备份，请勿重复提交！");

        // 获取备份存储的资源列表
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        if (null == cloudBackupStorageVO)
            throw new CustomException(600, "未查询到相关的备份存储配置信息！");

        String checkStorageResult = backupHis.getMessage();

        CloudApp cloudApp = appCommonService.get(appId);
        //创 备份历史
        //记录备份信息：数据库名称、备份路径、备份文件名称
        Timestamp startTime = new Timestamp(System.currentTimeMillis());
        buildBasicData(backupHis, startTime);
        //应用所属集群
        KubeConfig kubeConfig = kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, cloudApp.getKubeId());
        if(null == kubeConfig){
            log.error("未获取到集群！");
            backupReturn(backupHis, null, StatusConstant.FAIL, "", "未获取到集群！");
            return;
        }
        backupHis.setKubeName(kubeConfig.getName());
        backupMapper.commitBackup(SCHEMA, CLOUD_BACKUP_HIS, backupHis);

        ResourceChangeHis resourceChangeHis = createBasicHis(cloudApp, startTime, kubeConfig);
        resourceChangeHis.setCommand("备份");
        resourceChangeHis.setAction("Backup");
        resourceChangeHis.setMsg("备份中...".concat("\n").concat(null == checkStorageResult ? "" : checkStorageResult));
        Integer changeId = insertResourceChangeHis(resourceChangeHis);

        KubeClient kubeClient = kubeClientService.get(cloudApp.getKubeId());
        String namespace = cloudApp.getNamespace();
        TidbCluster tidbCr = kubeClient.listCustomResource(TidbCluster.class, cloudApp.getCrName(), namespace);
        //进入某一个pod执行备份命令，所有pod都需要进行备份文件的转移
        Leader leader = tidbCr.getStatus().getPd().getLeader();
        if (null == leader) {
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "tidb备份失败，未查询到 pd 组件的 leader信息！");
            return;
        }
        // 获取pd的leader信息
        String pdClientURL = leader.getClientURL();

        // 获取需要的 备份操作 tikv
        List<String> tikvPodNames = tidbCr.getStatus().getTikv().getStores().values().stream().map(store -> store.getPodName()).collect(Collectors.toList());
        backupHis.setPodName(tikvPodNames.stream().collect(Collectors.joining(",")));
        String backupTikvPodName = tikvPodNames.get(0);

        // 构建备份文件名称
        String backUpFileStr = "tidb-backup-file-" + DateUtil.getTodayDate() + "-" + System.currentTimeMillis();

        //进行挂载操作
        if (CloudAppConstant.BackupType.full.equals(backupHis.getBackupType())) {
            // 获取挂载命令
            String containerBackupStorageRootDir = "/mnt/shared";
            String backupPath = "/var/lib/tikv/backup/" + backUpFileStr;
            String mountCommand = buildMountScriptCMD(
                    cloudBackupStorageVO, containerBackupStorageRootDir, "sh", true);
            log.info("[tidb备份]，执行的挂载语句为：sh -c " + mountCommand);

            List<CompletableFuture<Void>> futures = tikvPodNames.stream().map(podName ->
                    CompletableFuture.runAsync(() -> {
                        try {
                            // 挂载
                            kubeClient.execCmd(namespace, podName, TidbUtil.TIDB_MNT_CONTAINER_NAME,
                                    "sh", "-c", mountCommand);
                            kubeClient.execCmd(namespace, podName, TidbUtil.TIDB_BACKUP_CONTAINER_NAME,
                                    "sh", "-c", "mkdir -p " + backupPath + ";");
                        } catch (Exception e) {
                            throw new RuntimeException("[TiDB Backup] Mount Failed:" + podName, e);
                        }
                    })
            ).collect(Collectors.toList());

            // 遍历 futures，等待每个任务完成，并在发生异常时立即返回
            for (CompletableFuture<Void> future : futures) {
                try {
                    future.get(); // 等待任务完成，如果任务抛出异常，get() 会抛出 ExecutionException
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    backupReturn(backupHis, changeId, StatusConstant.FAIL, "",
                            "TiDB备份失败！错误信息：" + e.getMessage());
                    return;
                }
            }

            //开始执行备份脚本
            try {
                String backupCommand = "bash /scripts/tidb-backup.sh " + backUpFileStr + " " + pdClientURL;
                kubeClient.execCmdOneway(namespace, backupTikvPodName, TidbUtil.TIDB_BACKUP_CONTAINER_NAME, "sh", "-c", backupCommand);
                log.info("[tidb备份]，执行的备份语句为：sh -c " + backupCommand);
            } catch (Exception e) {
                backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "tidb备份失败！错误信息：" + e.getMessage());
                return;
            }

        }else if (CloudAppConstant.BackupType.incre.equals(backupHis.getBackupType())) { // 增备基于全备名称进行
            //TODO tidb 增备

        }

        //修改备份历史，将basetime插入
        backupMapper.updateBackupHis(SCHEMA, CLOUD_BACKUP_HIS, backupHis);

        log.info("[tidb备份]开始创建定时");
        Map<String, String> jobDataMap = new HashMap<>();
        try {
            // 创建定时任务轮询执行结果
            //时间转换
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
            jobDataMap.put("backupHisId", backupHis.getBackupHisId().toString());
            jobDataMap.put("changeId", String.valueOf(changeId));
            jobDataMap.put("podNames", tikvPodNames.stream().collect(Collectors.joining(",")));
            jobDataMap.put("backupPodName", backupTikvPodName);
            jobDataMap.put("appId", String.valueOf(appId));
            jobDataMap.put("backupStartDateSDF", sdf.format(startTime));
            jobDataMap.put("backupFilename", backUpFileStr);
            jobDataMap.put("backupTimeout", String.valueOf(backupHis.getMaxBackupDuration()));
            appCommonService.callScheduler(cloudApp, cloudApp.getCr(), jobDataMap, ActionEnum.BACKUP, TidbBackupAndRestoreWatch.class, resourceChangeHis);
        } catch (Exception e) {
            log.error("[tidb备份]创建定时失败" + e.getMessage());
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "tidb备份创建定时调度失败！错误信息：" + e.getMessage());
            return;
        }

    }

    private String buildBackupName(CloudApp cloudApp, String podName) {
        //crname + 分片数 + 日期
        return cloudApp.getCrName() + "-shard-" + podName.split(cloudApp.getCrName() + "-" + ClickhouseUtil.getClickhouseClusterName() + "-")[1].split("-")[0] + "-" + System.currentTimeMillis();
    }

    /**
     * 根据云备份存储信息生成挂载脚本命令
     *
     * @param cloudBackupStorageVO          云备份存储对象，包含存储类型、服务器地址等信息
     * @param containerBackupStorageRootDir 容器备份存储根目录
     * @param shellTool                     根据容器和脚本中的语法选择 shell 执行工具：sh 或 bash.脚本中 if 判断使用 == 相关用 shell
     * @param mountSwitch                   挂载开关，决定是否进行挂载操作
     * @return 返回生成的挂载脚本命令字符串
     */
    public String buildMountScriptCMD(CloudBackupStorageVO cloudBackupStorageVO,
                                      String containerBackupStorageRootDir, String shellTool, boolean mountSwitch) {
        // 判断 shellTool 是否合法
        if (!"sh".equals(shellTool) && !"bash".equals(shellTool)) {
            throw new IllegalArgumentException("[buildMountScriptCMD]Invalid shellTool: " + shellTool);
        }

        // 初始化挂载脚本命令字符串
        String mountScript = "";

        // 判断存储类型是否为NAS
        if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
            // 构造NAS类型的挂载路径
            String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
            // 拼接NAS挂载脚本命令
            mountScript = shellTool + " /scripts/mount-remote-storage.sh"
                    + " " + String.valueOf(mountSwitch)
                    + " " + containerBackupStorageRootDir
                    + " " + CloudAppConstant.OperatorStorageType.NFS
                    + " " + mountPath;
        } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
            // 获取S3存储类型的区域信息
            String region = cloudBackupStorageVO.getRegion();
            // 如果区域信息为空，则设置为两个双引号，表示空字符串
            if (StringUtils.isEmpty(region)) {
                region = "\"\"";
            }
            // 拼接S3挂载脚本命令，包含更多的参数如服务器地址、区域、桶、访问密钥等
            mountScript = shellTool + " /scripts/mount-remote-storage.sh"
                    + " " + mountSwitch
                    + " " + containerBackupStorageRootDir
                    + " " + CloudAppConstant.StorageType.S3
                    + " " + cloudBackupStorageVO.getServer()
                    + " " + region
                    + " " + cloudBackupStorageVO.getBucket()
                    + " " + cloudBackupStorageVO.getAccessKey()
                    + " " + cloudBackupStorageVO.getSecretKey();
        }
        // 返回构造好的挂载脚本命令
        return mountScript;
    }

    /**
     * 获取字符串的nas挂载命令
     */
    private String getNfsMountCommand(CloudBackupStorageVO cloudBackupStorageVO, String backupPath) {
        return new StringBuffer().append("mount -o nolock -t nfs ")
                .append(cloudBackupStorageVO.getServer())
                .append(":")
                .append(cloudBackupStorageVO.getMountPath())
                .append(" ")
                .append(backupPath).toString();
    }

    /**
     * 获取字符串的s3挂载命令
     */
    public String getS3MountCommand(CloudBackupStorageVO cloudBackupStorageVO) {
        //echo "${accessKey}:${secretKey}" > /your/file/path  需要授权  chmod 600 /your/file/path
        //"s3fs -o passwd_file=/data/s3pwd -o use_path_request_style -o endpoint=${region} -o url=${url} -o allow_other -o no_check_certificate ${bucket} /mnt/share/ -o ssl_verify_hostname=0";
        String s3pwd = ClickhouseService.CLICKHOUSE_S3_PATH + "/s3pwd";
        String backupDir = "/mnt/shared";
        return new StringBuffer()
                .append("mkdir -p " + backupDir + ";echo ")
                .append(cloudBackupStorageVO.getAccessKey() + ":" + cloudBackupStorageVO.getSecretKey())
                .append(" > " + s3pwd + ";")
                .append("chmod 600 " + s3pwd + ";")
                .append("s3fs -o passwd_file=")
                .append(s3pwd)
                .append(" -o use_path_request_style -o endpoint=")
                .append(cloudBackupStorageVO.getRegion())
                .append(" -o url=")
                .append(cloudBackupStorageVO.getServer())
                .append(" -o allow_other -o no_check_certificate ")
                .append(cloudBackupStorageVO.getBucket())
                .append(" ")
                .append(backupDir)
                .append(" -o ssl_verify_hostname=0")
                .toString();
    }

    private void buildBasicData(BackupHisVO backupHis, Timestamp startTime) {
        JSONObject messageObj = new JSONObject();
        messageObj.put("backupIp","");
        messageObj.put("backupPath","");
        messageObj.put("msg","备份中...");
        //1. 保存备份基本信息
        log.info("[clickhouse备份]保存备份基本信息开始");
        backupHis.setStartTime(startTime);
        backupHis.setStatus(StatusConstant.RUNNING);
        backupHis.setMessage(JSON.toJSONString(messageObj));
    }

    public ResourceChangeHis createBasicHis(CloudApp app, Timestamp startTime, KubeConfig kubeConfig) {
        ResourceChangeHis resourceChangeHis = new ResourceChangeHis() {{
            setInsertTime(startTime);
            setKind(app.getKind());
            setKubeId(app.getKubeId());
            setNamespace(app.getNamespace());
            setStatus(StatusConstant.RUNNING);
            setAppId(app.getId());
            setAppName(app.getName());
            setKubeName(kubeConfig.getName());
            setYaml(app.getCr());
            setUserName(UserUtil.getAsyncUserinfo().getUsername());
            setUserIp(CloudRequestContext.getContext().getUserIp());
            setLastEndTimestamp(System.currentTimeMillis());
            setAppLogicId(app.getLogicAppId());
        }};
        return resourceChangeHis;
    }

    public void checkRestoreTime(Integer appId, String restoreTime) throws ParseException {
        //两个时间格式化
        SimpleDateFormat binlogSdf = new SimpleDateFormat("yyyyMMddHHmmss");
        SimpleDateFormat restoreSdf = new SimpleDateFormat("yyyy-MM-dd_HH:mm:ss");

        //格式化恢复时间
        Date restoreDate = restoreSdf.parse(restoreTime);

        //当前记录的binlog时间
        String binlogTimeStrKey = "";

        //不连续的时段
        HashMap<Timestamp, Timestamp> invalidTimeMap = new HashMap<>();

        //记录当前的binlog时间与开始时间
        HashMap<String, Timestamp> startTimeAndBinlogTimeMap = new HashMap<>();

        //查询binlog列表中所有不连续的时间段
        HashMap<String, String> condition = new HashMap<>();
        condition.put("clusterId", String.valueOf(appId));
        List<BinlogBackupHis> binlogBackupHisList = backupMapper.listBinlogBackupHisByMap(SCHEMA, DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, condition);
        for (BinlogBackupHis binlogBackupHis : binlogBackupHisList) {
            Timestamp startTime = binlogBackupHis.getStartTime();
            if (ObjectUtils.isEmpty(startTime)) {
                continue;
            }
            String fileCodes = binlogBackupHis.getFileCodes();
            if (!StringUtils.isEmpty(fileCodes)) {
                String binlogTimeStr = fileCodes.substring(0, fileCodes.indexOf(".000001")).substring(fileCodes.substring(0, fileCodes.indexOf(".000001")).lastIndexOf(",") + 1, fileCodes.indexOf(".000001")).substring(0, fileCodes.substring(0, fileCodes.indexOf(".000001")).substring(fileCodes.substring(0, fileCodes.indexOf(".000001")).lastIndexOf(",") + 1, fileCodes.indexOf(".000001")).indexOf("/"));
                if (0 != startTimeAndBinlogTimeMap.size()) {
                    //比较binlog时间是否有变化
                    if (startTimeAndBinlogTimeMap.containsKey(binlogTimeStr)) {
                        //更新开始时间
                        startTimeAndBinlogTimeMap.put(binlogTimeStr, startTime);
                    } else {
                        //记录开始时间
                        invalidTimeMap.put(startTimeAndBinlogTimeMap.get(binlogTimeStrKey), startTime);
                        //更新startTimeAndBinlogTimeMap
                        startTimeAndBinlogTimeMap.put(binlogTimeStr, startTime);
                        binlogTimeStrKey = binlogTimeStr;
                    }
                } else {
                    //直接记录
                    startTimeAndBinlogTimeMap.put(binlogTimeStr, startTime);
                    binlogTimeStrKey = binlogTimeStr;
                }
            }
        }
    }

    /**
     * 获取备份存储信息
     * @deprecated use resourceManagerService.listBackupStorage() instead
     * @return
     */
    public Map<String, String> getBackupStorageInfo() {
        //获取配置的备份存储信息
        Map<String, Object> backupStorageCondition = new HashMap<>();
        List<CloudBackupStorage> backupStorageList = cloudBackupStorageMapper.listByMap(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_STORAGE, backupStorageCondition);
        //表中数据
        JSONObject storageConfig = JsonUtil.toObject(JSONObject.class, backupStorageList.get(0).getStorageConfig());
        String backupType = backupStorageList.get(0).getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS) ? "nfs" : "s3";
        Map<String, String> resInfoMap = new HashMap<>();
        if ("nfs".equalsIgnoreCase(backupType)) {
            //nfs
            String nasIp = storageConfig.getString("ip");
            String nasPath = storageConfig.getString("mountPath");
            String address = nasIp + ":" + nasPath + "/";
            String newAddress = address.replace("//", "/");
            resInfoMap.put("url", newAddress);
            resInfoMap.put("type", backupType);
        } else {
            //S3
            String url = storageConfig.getString("ip");
            String mountPath = storageConfig.getString("mountPath");
            String[] mountPathArr = mountPath.split(":");
            String bucket = mountPathArr[0];
            String backupPath = mountPath.replace(bucket, "").replace(":", "");
            String accessKey = storageConfig.getString("accessKey");
            String secretKey = storageConfig.getString("secretKey");
            resInfoMap.put("bucket", bucket);
            resInfoMap.put("backupPath", backupPath);
            resInfoMap.put("url", "http://" + url);
            resInfoMap.put("accessKey", accessKey);
            resInfoMap.put("secretKey", secretKey);
            resInfoMap.put("type", backupType);
        }
        return resInfoMap;
    }

    @Autowired @Lazy private FlinkJobService flinkJobService;
    public void flinkBackup(BackupHisVO backupHis) {
        CloudApp app = appCommonService.get(backupHis.getAppId());
        try {
            backupHis.setAppLogicId(app.getLogicAppId());
            KubeConfig byId = kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, app.getKubeId());
            backupHis.setKubeName(byId.getName());
            flinkJobService.savepoint(backupHis.getAppLogicId(), backupHis.getPerformType());
        } catch (Exception e) {
            if (ScheduleConstant.CRON.equals(backupHis.getPerformType())) {
                log.error("", e);
                backupHis.setStartTime(Timestamp.valueOf(LocalDateTime.now()));
                backupHis.setEndTime(Timestamp.valueOf(LocalDateTime.now()));
                backupHis.setStatus(StatusConstant.FAIL);
                backupHis.setMessage(e.getMessage());
                backupMapper.commitBackup(SCHEMA, CLOUD_BACKUP_HIS, backupHis);

                appCommonService.recordResourceChangeHis(app, ActionEnum.BACKUP, StatusConstant.FAIL, null, e.getMessage());
            } else {
                throw e;
            }
        }
    }

    public void flinkRestore(BackupHis backupHis, Integer appId, String restoreTime) {
        // todo not support restore to other app
        flinkJobService.restore(backupHis, backupHis.getPodName());
    }

    public Map<String, Object> getLastRestoreTime(Integer appId) {
        Map<String, Object> resMap = new HashMap<>();
        resMap.put("startTime", null);
        resMap.put("endTime", null);
        CloudApp app = cloudAppService.get(appId);
        String kind = app.getKind();
        if (AppKind.MYSQL_HA.getKind().equalsIgnoreCase(kind)) {
            Timestamp firstRestoreTime = backupService.getFirstRestoreTime(appId);
            Timestamp lastRestoreTime = backupService.getLastRestoreTime(appId);
            //判断开始与结束时间是否相同，相同则只备份过一次，不允许按时间恢复
            if (String.valueOf(firstRestoreTime).equals(String.valueOf(lastRestoreTime))) {
                return resMap;
            }
            //为结束的时间-1s，用于恢复
            Calendar c = Calendar.getInstance();
            c.setTime(lastRestoreTime);
            c.add(Calendar.SECOND, -1);
            lastRestoreTime = new Timestamp(c.getTimeInMillis());
            resMap.put("startTime", firstRestoreTime);
            resMap.put("endTime", lastRestoreTime);
        } else if (AppKind.MongoDB_Cluster.getKind().equalsIgnoreCase(kind)) {
            //获取当前应用第一次备份时间
            PageDTO pageDTO = new PageDTO();
            Map<String, Object> condition = new HashMap<>();
            condition.put("appType", "MongoDB");
            condition.put("podName", "mc-");
            condition.put("status", "0");
            condition.put("appId", app.getId());
            pageDTO.setCondition(condition);
            pageDTO.setPageSort("asc");
            pageDTO.setPageSortProp("endTime");
            pageDTO.setPageSize(1);
            PageInfo<BackupHis> firstBackupHisList = backupService.listPage(pageDTO);
            List<BackupHis> list = firstBackupHisList.getList();
            if (null == list || 0 == list.size()) {
                return resMap;
            }
            BackupHis firstBackupHis = list.get(0);

            //获取当前应用最后一次备份时间
            pageDTO.setPageSort("desc");
            pageDTO.setPageSortProp("startTime");
            PageInfo<BackupHis> lastBackupHisList = backupService.listPage(pageDTO);
            List<BackupHis> lastList = lastBackupHisList.getList();
            if (null == lastList || 0 == lastList.size()) {
                return resMap;
            }
            BackupHis lastBackupHis = lastList.get(0);

            //判断是否为同一次备份
            if (firstBackupHis.getBackupHisId().equals(lastBackupHis.getBackupHisId())) {
                return resMap;

            }
            resMap.put("startTime", firstBackupHis.getEndTime());
            resMap.put("endTime", lastBackupHis.getStartTime());
        } else if (AppKind.Dameng.getKind().equalsIgnoreCase(kind)) {
            // 取归档日志连续开始结束时间

            // 归档日志 开始时间=归档日志创建时间，结束时间=备份时间
            // 查询历次归档备份包含的日志范围(第一次的开始时间到最后一次的结束时间)，为可选择时间范围。
            PageDTO pageDTO = new PageDTO();
            pageDTO.getCondition().put("appId", appId);
            PageInfo<BackupHis> page = backupService.listPage(pageDTO);

            List<BackupHis> hisList = page.getList();
            hisList.sort(Comparator.comparing(BackupHis::getStartTime).reversed());// desc order
            String startTime = null, endTime = null;
            for (BackupHis his : hisList) {
                if (his.getStatus().equals("3")) // exit loop if his got cleaned
                    break;
                if (his.getStatus().equals(StatusConstant.SUCCESS)) {
                    DamengBackupRestoreWatch.ArchiveInfo archiveInfo = JsonUtil.toObject(DamengBackupRestoreWatch.ArchiveInfo.class, his.getMessage());
                    if (archiveInfo != null) {
                        if (startTime == null) {
                            startTime = archiveInfo.getArchBsetBeginTime();
                            endTime = archiveInfo.getArchBsetEndTime();
                        } else {
                            if (startTime.compareTo(archiveInfo.getArchBsetEndTime()) > 0)
                                startTime = archiveInfo.getArchBsetBeginTime();
                            else {
                                break; // 不连续
                            }
                        }
                    }
                }
            }
            if (startTime != null) {
                resMap.put("startTime", Timestamp.valueOf(LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"))));
                resMap.put("endTime", Timestamp.valueOf(endTime.trim()));
            }
        } else {
            // 第一次全备时间~最后一次binlog时
            Timestamp firstRestoreTime = backupService.getFirstRestoreTime(appId);
            Timestamp lastRestoreTime = backupService.getLastRestoreTime(appId);

            resMap.put("startTime", firstRestoreTime);
            resMap.put("endTime", lastRestoreTime);
            if (firstRestoreTime != null) {
                PageDTO pageDTO = new PageDTO();
                Map<String, Object> condition = new HashMap<>();
                condition.put("status", "0");
                condition.put("appId", app.getId());
                pageDTO.setCondition(condition);
                pageDTO.setPageSort("asc");
                pageDTO.setPageSortProp("startTime");
                pageDTO.setPageSize(1);
                PageInfo<BackupHis> firstBackupHisList = backupService.listPage(pageDTO);
                List<BackupHis> list = firstBackupHisList.getList();
                if (null == list || 0 == list.size()) {
                    return resMap;
                }
                BackupHis firstBackupHis = list.get(0);
                resMap.put("startTime", firstRestoreTime.before(firstBackupHis.getStartTime()) ? firstBackupHis.getStartTime() : firstRestoreTime);
            }
        }
        return resMap;
    }

    @Transactional
    public void damengBackup(BackupHisVO backupHis) {
        // verify pre-requisite
        // 1. based on primary or standby, if primary make sure it is running and opening, or check in-sync standby to perform backup
        // 2. backup path mounted, free space check

        Integer appId = backupHis.getAppId();
        CloudApp app = appCommonService.get(appId);
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        // verify backup pod
        PodDTO backupPod = getBackupPodDM(app, kubeClient);
        // verify for increment backup
        if (backupHis.getBackupType().equals(BackupType.incre)) { // 累积增量
            Integer baseId;
            BackupHis baseBackup;
            if ((baseId = backupHis.getBaseBackupHisId()) == null) {
                baseBackup = getLastFullBackup(backupHis);
            } else {
                baseBackup = backupService.get(baseId);
            }
            CustPreconditions.checkState(baseBackup != null && baseBackup.getStatus().equals(StatusConstant.SUCCESS), "没有成功全备历史");
            backupHis.setBaseBackupHisId(baseBackup.getBackupHisId());
        }

        // verify mounted and todo space
        final String SCRIPT_PATH = "/scripts/";
        final String mountPoint = DamengService.Name.MOUNT_POINT;
        final String mountContainerName = "mount";
        try {
            String cmd = mountCmd(SCRIPT_PATH, mountPoint);
            kubeClient.execCmd(app.getNamespace(), backupPod.getPodName(), mountContainerName, 0, "sh", "-c", cmd);
        } catch (Exception e) {
            throw new CustomException(600, "挂载远程备份存储失败", e);
        }
        insertBackupHis(backupHis, app, backupPod.getPodName());

        // 备份归档日志
        int binlogBackupHisId = 0;
        if (backupHis.getBackupDbLog() != null && backupHis.getBackupDbLog()) {
            BinlogBackupHis binlogBackupHis = new BinlogBackupHis();
            binlogBackupHis.setClusterId(appId);
            binlogBackupHis.setStatus(StatusConstant.RUNNING);
            binlogBackupHisId = backupService.insertBinlogBackupHis(binlogBackupHis);
        }
        
        String checkFilename = "check_backup_" + backupHis.getBackupHisId();
        try {
            ImmutableMap.Builder<Object, Object> imb = ImmutableMap.builder();
            imb.put("checkFile", checkFilename).put("backupHisId", backupHis.getBackupHisId());
            if (backupHis.getBaseBackupHisId() != null) { // 增量备份基备份历史id
                imb.put("baseBackupHisId", backupHis.getBaseBackupHisId());
            }
            if (binlogBackupHisId > 0)
                imb.put("binlogBackupHisId", binlogBackupHisId);
            appCommonService.callScheduler(app, app.getCr(), imb.build(),
                    ActionEnum.BACKUP, DamengBackupRestoreWatch.class);
        } catch (Exception e) {
            // ensure umount
            umount(kubeClient, app.getNamespace(), backupPod.getPodName(), SCRIPT_PATH, mountPoint, mountContainerName);
            throw new CustomException(600, "提交备份失败：" + e.getMessage());
        }

    }

    private BackupHis getLastFullBackup(BackupHisVO backupHis) {
        BackupHis fullBackupHis = backupMapper
                .getLastSuccessFullBackupHis(SCHEMA, CLOUD_BACKUP_HIS, backupHis.getAppId());
        return fullBackupHis;
    }

    public void umount(KubeClient kubeClient, String namespace, String podName,
                       String SCRIPT_PATH, String mountPoint, String mountContainerName) {
        // umount
        String cmd = String.format("%s/umount.sh %s", SCRIPT_PATH, mountPoint);
        try {
            kubeClient.execCmd(namespace, podName, mountContainerName, 0, "sh", "-c", cmd);
        } catch (Exception e) {
            log.error("umount failed " + e.getMessage());
        }
    }

    public String mountCmd(String SCRIPT_PATH, String mountPoint) {
        // mount cmd:   mount-nfs.sh mountpoint nfshost:path
        //              mount-s3.sh local=mountpoint address=$2 region=$3 bucket=$4 accessKey=$5 secretKey=$6
        // umount: umount.sh mountpoint
        // backup.sh -dbhost %s -dbuser %s -dbpassword %s -configfile %s -baklevel full -storetype share -tagName %s
        String cmd;

        // s3: bucket,backupPath,url,accessKey,secretKey,type
        // nfs: type, address
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();

        if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
            cmd = String.format("%s/%s %s %s:%s", SCRIPT_PATH, "mount-nfs.sh", mountPoint,
                    cloudBackupStorageVO.getServer(),
                    cloudBackupStorageVO.getMountPath());
        } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
            cmd = String.format("%s/%s %s %s '%s' %s %s %s", SCRIPT_PATH, "mount-s3.sh",
                    mountPoint,
                    cloudBackupStorageVO.getServer(),
                    cloudBackupStorageVO.getRegion(),
                    cloudBackupStorageVO.getBucket(),
                    cloudBackupStorageVO.getAccessKey(),
                    cloudBackupStorageVO.getSecretKey()
            );
        } else {
            throw new UnsupportedOperationException();
        }
        return cmd;
    }

    private PodDTO getBackupPodDM(CloudApp app, KubeClient kubeClient) {
        List<PodDTO> pods = kubeClient.listPod(app.getNamespace(), AppKind.Dameng.labels(app.getCrName()));
        if (pods.isEmpty()) {
            throw new CustomException(600, "实例当前没有可以执行备份的节点");
        }
        // ensure primary todo in sync standby
        final PodDTO[] primary = new PodDTO[1];
        Dameng cr = YamlEngine.unmarshal(app.getCr(), Dameng.class);
        Integer replicas = cr.getSpec().getEntries().stream().filter(e -> e.getName().equals(DamengService.Name.ENTRY_DATABASE))
                .map(e -> e.getReplicas()).findFirst().orElseThrow(() -> new IllegalArgumentException(""));
        if (replicas == 1) {
            primary[0] = pods.get(0);
        } else if (pods.stream().filter(pod -> ROLE_PRIMARY.equalsIgnoreCase(pod.getLabels().get(CustomLabels.ROLE)))
                .peek(pod -> primary[0] = pod)
                .count() != 1) {
            throw new CustomException(600, "无法确定当前达梦实例的主节点");
        }
        return primary[0];
    }

    public void damengRestore(BackupHis backupHis, Integer appId, String restoreTime) {
        CloudApp restoreAPP = appCommonService.get(appId);
        KubeClient kubeClient = kubeClientService.get(restoreAPP.getKubeId());

        // verify pre-requisite
        // 1. mount backup storage
        // 2. maintenance flag on

        List<PodDTO> podDTOS = kubeClient.listPod(restoreAPP.getNamespace(), AppKind.Dameng.labels(restoreAPP.getCrName()));
        podDTOS = podDTOS.stream().filter(pod -> DamengService.Name.ENTRY_DATABASE.equals(pod.getLabels().get(CustomLabels.APP_COMPONENT)))
                .collect(Collectors.toList());
        Dameng cr = YamlEngine.unmarshal(restoreAPP.getCr(), Dameng.class);
        Integer replicas = cr.getSpec().getEntries().get(0).getReplicas();
        Preconditions.checkArgument(!podDTOS.isEmpty() && podDTOS.size() == replicas,
                "目标实例节点列表为空或不满足期望副本数，请稍后重试");
        // turn on maintenance flag
        try {
            cr.getSpec().setMaintenance(true);
            kubeClient.updateCustomResource(cr, Dameng.class);
        } catch (Exception e) {
            throw new CustomException(600, "更新为维护状态发生错误 " + e.getMessage(), e);
        }

        // verify mounted and todo space
        final String SCRIPT_PATH = "/scripts/";
        final String mountPoint = DamengService.Name.MOUNT_POINT;
        final String mountContainerName = "mount";
        try {
            String cmd = mountCmd(SCRIPT_PATH, mountPoint);
            for (PodDTO podDTO : podDTOS) {
                kubeClient.execCmd(restoreAPP.getNamespace(), podDTO.getPodName(), mountContainerName, 0, "sh", "-c", cmd);
            }
        } catch (Exception e) {
            throw new CustomException(600, "挂载远程备份存储失败", e);
        }
        RestoreHis his = insertRestoreHis(restoreAPP, Collections.singletonList(backupHis),
                podDTOS.stream().map(PodDTO::getPodName).collect(Collectors.joining(",")), "");

        try {
            appCommonService.callScheduler(restoreAPP, restoreAPP.getCr(),
                    ImmutableMap.of("restoreHisId", his.getRestoreHisId(), "backupHisId", backupHis.getBackupHisId()),
                    ActionEnum.RESTORE, DamengBackupRestoreWatch.class);
        } catch (Exception e) {
            // ensure umount
            for (PodDTO podDTO : podDTOS) {
                umount(kubeClient, restoreAPP.getNamespace(), podDTO.getPodName(), SCRIPT_PATH, mountPoint, mountContainerName);
            }
            // ensure maintenance off
            cr.getSpec().setMaintenance(false);
            kubeClient.updateCustomResource(cr, Dameng.class);
            throw new CustomException(600, "提交备份失败：" + e.getMessage());
        }
    }

    /**
     * vastbase备份
     *
     * @param backupHis
     */
    public void vastbaseBackup(BackupHisVO backupHis) {
        // 1.判断是否有正在备份的操作
        BackupHis runningBackupHis = backupMapper.getRunningByAppId(SCHEMA, CLOUD_BACKUP_HIS, backupHis.getAppId());
        if (runningBackupHis != null) {
            throw new CustomException(600, "存在正在执行的备份，请勿重复提交！");
        }
        //如果是增备，对是否做过全备做校验
        //根据baseTime递归检查全备以及增备是否存在
        Integer appId = backupHis.getAppId();
        if ("incre".equalsIgnoreCase(backupHis.getBackupType())) {
            checkBackupState(appId);
        }
        // 2.保存APP状态
        CloudApp cloudApp = cloudAppService.get(appId);
        //修改应用状态
        cloudApp.setStatus(CloudAppConstant.AppStatus.PENDING);
        cloudAppService.update(cloudApp);
        // 3.保存备份历史
        JSONObject messageObj = new JSONObject();
        messageObj.put("msg", "备份中...");
        Timestamp startTime = new Timestamp(System.currentTimeMillis());
        backupHis.setStartTime(startTime);
        backupHis.setStatus(StatusConstant.RUNNING);
        backupHis.setMessage(JSON.toJSONString(messageObj));
        //应用所属集群
        KubeConfig byId = kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, cloudApp.getKubeId());
        if (null == byId) {
            log.error("未获取到集群！");
            backupReturn(backupHis, null, StatusConstant.FAIL, "", "未获取到集群！");
            return;
        }
        backupHis.setKubeName(byId.getName());
        backupMapper.commitBackup(SCHEMA, CLOUD_BACKUP_HIS, backupHis);

        // 4.保存操作记录
        UserInfo userInfo = UserUtil.getAsyncUserinfo();
        ResourceChangeHis resourceChangeHis = new ResourceChangeHis() {{
            setInsertTime(startTime);
            setKind(cloudApp.getKind());
            setKubeId(cloudApp.getKubeId());
            setNamespace(cloudApp.getNamespace());
            setCommand("备份");
            setStatus("2");
            setAction("Backup");
            setMsg("备份中...");
            setAppId(cloudApp.getId());
            setAppLogicId(cloudApp.getLogicAppId());
            setAppName(cloudApp.getName());
            setKubeName(byId.getName());
            setYaml(cloudApp.getCr());
            setUserName(userInfo.getUsername());
            setUserIp(CloudRequestContext.getContext().getUserIp());
            setLastEndTimestamp(System.currentTimeMillis());
        }};
        Integer changeId = insertResourceChangeHis(resourceChangeHis);

        // 5.找到主节点
        KubeClient client = kubeClientService.get(cloudApp.getKubeId());
        Vastbase vastbase = client.listCustomResource(Vastbase.class, cloudApp.getCrName(), cloudApp.getNamespace());
        String vastbasePrimary = vastbase.getStatus().getVastbasePrimary() + "-0";

        // 6.mount目录到远程服务器
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        String mountCmd = "";
        if (CloudAppConstant.StorageType.NAS.equalsIgnoreCase(cloudBackupStorageVO.getStorageType())) {
            String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
            mountCmd = "sh /scripts/mount-nfs.sh /data/backup " + mountPath;
        } else if (CloudAppConstant.StorageType.S3.equalsIgnoreCase(cloudBackupStorageVO.getStorageType())) {
            mountCmd = "sh /scripts/mount-s3.sh /data/backup " + cloudBackupStorageVO.getServer() + " '" + cloudBackupStorageVO.getRegion() + "' " + cloudBackupStorageVO.getBucket() + " " + cloudBackupStorageVO.getAccessKey() + " " + cloudBackupStorageVO.getSecretKey();
        }
        try {
            client.execCmd(cloudApp.getNamespace(), vastbasePrimary, "mount", "sh", "-c", mountCmd);
        } catch (Exception e) {
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "mount远程服务失败！错误信息为：" + e);
            return;
        }

        // 7.提交备份
        String backupType = "FULL";
        if ("incre".equalsIgnoreCase(backupHis.getBackupType())) {
            backupType = "PTRACK";
        }
        String backupCmd = "sh /scripts/vastbase-backup.sh " + backupType + " backup_" + backupHis.getBackupHisId();
        try {
            client.execCmdOneway(cloudApp.getNamespace(), vastbasePrimary, "sidecar", "sh", "-c", backupCmd);
        } catch (Exception e) {
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "提交备份失败！错误信息为：" + e);
            return;
        }

        // 8.创建定时轮询
        Map<String, String> jobDataMap = new HashMap<>();

        try {
            // 创建定时任务轮询执行结果
            jobDataMap.put("backupHisId", backupHis.getBackupHisId().toString());
            jobDataMap.put("kubeId", cloudApp.getKubeId().toString());
            jobDataMap.put("changeId", String.valueOf(changeId));
            jobDataMap.put("primaryPodName", vastbasePrimary);
            jobDataMap.put("appId", String.valueOf(appId));
            jobDataMap.put("backupTimeout", String.valueOf(backupHis.getMaxBackupDuration()));
            jobDataMap.put("userInfo", JsonUtil.toJson(userInfo));
            log.info("备份定时用户信息：" + JsonUtil.toJson(userInfo));
            appCommonService.callScheduler(cloudApp, cloudApp.getCr(), jobDataMap, ActionEnum.BACKUP, VastbaseBackupAndRestoreWatch.class, resourceChangeHis);
        } catch (Exception e) {
            log.error(e.getMessage());
            backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "Vastbase备份创建定时调度失败！错误信息：" + e.getMessage());
            return;
        }
    }

    public void vastbaseRestore(BackupHis backupHis, Integer appId) {
        // 0.对恢复的底层库类型做校验
        Integer backupAppId = backupHis.getAppId();
        CloudApp goalApp = cloudAppMapper.selectByPrimaryKey(appId, DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_APP);
        CloudApp backupApp = cloudAppMapper.selectByPrimaryKey(backupAppId, DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_APP);
        KubeClient kubeClient = kubeClientService.get(goalApp.getKubeId());
        Vastbase cr = kubeClient.listCustomResource(Vastbase.class, goalApp.getCrName(), goalApp.getNamespace());
        String backupHisMessage = backupHis.getMessage();
        Map backupHisMessageObj = JsonUtil.toObject(Map.class, backupHisMessage);
        String compatibility = (String) backupHisMessageObj.get("compatibility");
        if (!compatibility.equalsIgnoreCase(cr.getSpec().getCompatibility())) {
            throw new CustomException(600, "恢复失败！备份文件底层库兼容类型与恢复目标实例不符！备份文件底层库兼容类型：" + compatibility + "   目标实例底层库兼容类型：" + cr.getSpec().getCompatibility());
        }
        // 1.恢复历史插入
        RestoreHis restoreHis = new RestoreHis();
        restoreHis.setStatus(StatusConstant.RUNNING);
        Timestamp startTime = new Timestamp(System.currentTimeMillis());
        restoreHis.setStartTime(startTime);

        restoreHis.setAppId(appId);
        restoreHis.setAppName(goalApp.getName());
        restoreHis.setAppType(goalApp.getKind());

        //应用所属集群
        KubeConfig byId = kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, goalApp.getKubeId());
        restoreHis.setKubeName(byId.getName());
        restoreHis.setMessage("恢复中...");
        restoreHis.setFileDeleted(false);
        String backDirInPod = "/backup/";
        restoreHis.setRestoreDir(backDirInPod);
        restoreHis.setFileName(backupHis.getFileName());

        // 2.插入操作记录
        ResourceChangeHis resourceChangeHis = new ResourceChangeHis() {{
            setInsertTime(startTime);
            setKind(goalApp.getKind());
            setKubeId(goalApp.getKubeId());
            setNamespace(goalApp.getNamespace());
            setCommand("恢复");
            setStatus("2");
            setAction(ActionEnum.RESTORE.getActionType());
            setMsg("恢复中...");
            setAppId(goalApp.getId());
            setAppName(goalApp.getName());
            setUserName(UserUtil.getAsyncUserinfo().getUsername());
            setUserIp(CloudRequestContext.getContext().getUserIp());
            setKubeName(byId.getName());
            setYaml(goalApp.getCr());
            setLastEndTimestamp(System.currentTimeMillis());
            setAppLogicId(goalApp.getLogicAppId());
        }};
        Integer changeId = insertResourceChangeHis(resourceChangeHis);
        try {
            // 3.找到主节点
            KubeClient client = kubeClientService.get(goalApp.getKubeId());
            Vastbase vastbase = client.listCustomResource(Vastbase.class, goalApp.getCrName(), goalApp.getNamespace());
            String vastbasePrimary = vastbase.getStatus().getVastbasePrimary() + "-0";
            restoreHis.setPodName(vastbasePrimary);
            //插入基本信息
            restoreMapper.commitRestore(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_RESTORE_HIS, restoreHis);
            goalApp.setStatus(CloudAppConstant.AppStatus.PENDING);
            cloudAppService.update(goalApp);

            // 4.获取备份文件名称,修改对应的cr属性：RestoreFile
            //备份文件名称
            String backupFileName = backupHis.getFileName();
            //构造cr
            VastbaseSpec spec = cr.getSpec();
            String fullSourceName = backupApp.getNamespace() + "/" + backupApp.getCrName() + "/" + backupFileName;
            com.shindata.cloud.v1.vastbasespec.Restore restore = new com.shindata.cloud.v1.vastbasespec.Restore();
            restore.setFullSource(fullSourceName);
            //获取备份存储信息
            CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
            Remote remote = new Remote();
            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                remote.setType(CloudAppConstant.OperatorStorageType.NFS);
                remote.setAddress(cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath());
            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                remote.setType(CloudAppConstant.StorageType.S3);
                remote.setAddress(cloudBackupStorageVO.getServer());
                remote.setBucket(cloudBackupStorageVO.getBucket());
                remote.setRegion(cloudBackupStorageVO.getRegion());
                //获取operator的namespace，因为所有operator都相同，所以统一获取mysql的operatornamespace
                String operatorConfig = sysConfigService.findOne("operator.name", "MySQL");
                String operatorNamespace = operatorConfig.split("/")[0];
                remote.setSecret(operatorNamespace + ":backupstorage-secret");
            } else {
                throw new CustomException(600, "恢复失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
            }
            restore.setRemote(remote);
            spec.setRestore(restore);
            cr.setSpec(spec);
            kubeClient.updateCustomResource(cr, Vastbase.class);
            //创建定时轮询备份结果
            Map map = new HashMap();
            map.put("restoreHisId", restoreHis.getRestoreHisId());
            map.put("resourceChangeId", changeId);
            appCommonService.callScheduler(goalApp, YamlEngine.marshal(cr), map, ActionEnum.RESTORE, VastbaseBackupAndRestoreWatch.class, resourceChangeHis);
        } catch (Exception e) {
            restoreReturn(restoreHis, changeId, e.getMessage(), StatusConstant.FAIL);
        }
    }

    private void checkBackupState(Integer appId) {
        BackupHis backupHis = backupMapper.getLastSuccessFullBackupHis(SCHEMA, CLOUD_BACKUP_HIS, appId);
        if (ObjectUtils.isEmpty(backupHis) || !backupHis.getStatus().equalsIgnoreCase("0")) {
            throw new CustomException(600, "备份失败！请先进行全备！");
        }
    }
}
