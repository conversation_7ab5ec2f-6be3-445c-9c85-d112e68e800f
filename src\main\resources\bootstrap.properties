spring.main.allow-circular-references=true
spring.multipart.enabled=true
spring.multipart.max-file-size=300M
spring.multipart.max-request-size=300M
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
spring.application.name=cloud-service
spring.config.import[0]=nacos:application.yaml
spring.config.import[1]=nacos:cloud-service.yaml
spring.cloud.nacos.discovery.server-addr=**************:8848
spring.cloud.nacos.discovery.username=nacos
spring.cloud.nacos.discovery.password=1qaz@WSX9ijn)OKM
spring.cloud.nacos.discovery.ip=*************
spring.cloud.nacos.discovery.port=31232
spring.cloud.nacos.config.server-addr=**************:8848
spring.cloud.nacos.config.group=DEFAULT_GROUP
spring.cloud.nacos.config.file-extension=yaml
spring.cloud.nacos.config.username=nacos
spring.cloud.nacos.config.password=1qaz@WSX9ijn)OKM
spring.cloud.nacos.config.namespace=public
