package cn.newdt.cloud.domain;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter @Setter
public class CloudSysConfig {
    private Integer id;

    private String name;

    private String data;

    private String category;

    private String description;

    private String dataType;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private boolean modified;

    /**
     * 系统参数是否需要重启服务,默认值为0.0为不需要,1为需要
     */
    private int isNeedReboot;

    /**
     * 是否可修改，默认值为0,0为不可修改，1为可修改
     */
    private int isUpdate = 0;
}