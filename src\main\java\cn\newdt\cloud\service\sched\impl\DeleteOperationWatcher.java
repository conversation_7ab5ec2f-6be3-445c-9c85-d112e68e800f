package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.StatusConstant;
import cn.newdt.cloud.domain.BackupTimer;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.ResourceChangeHis;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.AppKindService;
import cn.newdt.cloud.service.AppServiceLoader;
import cn.newdt.cloud.service.BackupTimerService;
import cn.newdt.cloud.service.alert.AlertConfigService;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.utils.JsonUtil;
import cn.newdt.cloud.vo.AppInstanceVO;
import cn.newdt.commons.bean.UserInfo;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.newdt.cloud.constant.ScheduleConstant.JOB_DATA_KEY_CHANGE_ID;

@Slf4j
public class DeleteOperationWatcher extends OpsProcessorContext implements OpsPostProcessor {

    @Autowired
    private AlertConfigService alertConfigService;
    @Autowired
    private BackupTimerService backupTimerService;


//    private List<Step> initSteps() {
//        return ImmutableList.of(disableAlertFor(), deletePod(), );
//    }

    @Override
    public OpsResultDTO postProcess(TriggerHis triggerHis) throws Exception {
        OpsResultDTO.Builder result = OpsResultDTO.builder().stopJob(false);
        Map<String, String> jobDataMap = triggerHis.returnMergedJobDataMap();
        ResourceChangeHis resourceHis = resourceChangeHisService.get(Integer.parseInt(jobDataMap.get(JOB_DATA_KEY_CHANGE_ID)));
        Map<String, Object> rsDataMap = resourceHis.mutableDataMap();

        String appId = jobDataMap.get("appId");
        CloudApp app = appService.get(Integer.valueOf(appId));

        if (disableAlertFor().run(app, rsDataMap))
            if (disableBackupStrategy().run(app, rsDataMap))
                if (deletePod().run(app, rsDataMap))
                    result.status(StatusConstant.SUCCESS).stopJob(true);

        logInfo(app, ActionEnum.DELETE, rsDataMap.toString());

        // update app delete status to 2(recycled)
        if (result.isSuccessful()) {
            app.setIsDeleted(CloudAppConstant.AppDeleteStatus.RECYCLED);
            if (appService.update(app) > 0) {
                log.info("update physic app‘s [delete] as recycled");
            }
            appMultiAZService.updateDelete(app.getLogicAppId(), CloudAppConstant.AppDeleteStatus.RECYCLED);
        }
        if (result.isStopped())
            // 如果在上次操作提交但未结前删除应用，此时cr的spec和cloudapp表中的数据可能不一致
            // 如安装过程中删除，还原时应按cr_run恢复
            appService.handleWatchResult(app.getId(), true);

        // 记录在回收历史以在自动清理时使用
        if (!rsDataMap.containsKey("userInfo")) {
            // fit datamap column length
            UserInfo userInfo = JSONObject.parseObject(jobDataMap.get("userInfo"), UserInfo.class);
            rsDataMap.put("userInfo", JsonUtil.toJson(userInfo));
        }
        resourceHis.setDataMap(JsonUtil.toJson(rsDataMap));
        resourceChangeHisService.update(resourceHis);
        return result.build();
    }

    private Step deletePod() {
        return new Step() {
            @Override
            public String markName() {
                return "pod_deleted";
            }

            @Override
            public boolean execute(CloudApp app) {
                AppKindService instance = AppServiceLoader.getInstance(app.getKind(), app.getArch());
                // delete pod in maintaining or by scale
                instance.delete(app);
                // ignore terminating status
                List<AppInstanceVO> instances = appService.findInstances(app.getId());
                return instances.stream().allMatch(pod -> pod.getStatus().equals(CloudAppConstant.PodStatus.Terminating));
            }
        };
    }

    private Step disableAlertFor() {
        // disable alert
        return new Step() {
            @Override
            public String markName() {
                return "alert_disabled";
            }

            @Override
            public boolean execute(CloudApp app) {
                alertConfigService.disableAlertFor(app);
                return true;
            }
        };
    }

    private Step disableBackupStrategy() {
        // disable alert
        return new Step() {
            @Override
            public String markName() {
                return "backup_strategy_disabled";
            }

            @Override
            public boolean execute(CloudApp app) {

                PageInfo<BackupTimer> timerList = backupTimerService.listPage(new PageDTO() {{
                    setCondition(new HashMap<String, Object>() {{
                        put("appId", app.getId());
                    }});
                }});
                for (BackupTimer backupTimer : timerList.getList()) {
                    backupTimerService.startStopBackupTimer(backupTimer.getBackupTimerId(), false);
                }
                return true;
            }
        };

    }

    interface Step {
        String markName();

        boolean execute(CloudApp app);

        default boolean isDone(Map<String, Object> rsDataMap) {
            return Boolean.parseBoolean(String.valueOf(rsDataMap.getOrDefault(markName(), false)));
        }

        default void done(Map<String, Object> rsDataMap) {
            rsDataMap.put(markName(), true);
        }

        default boolean run(CloudApp app, Map<String, Object> rsDataMap) {
            if (!isDone(rsDataMap)) {
                if (execute(app)) {
                    done(rsDataMap);
                    return true;
                }
                return false;
            }
            return true;
        }
    }

    boolean isDone(Map<String, Object> rsDataMap, String name) {
        return Boolean.parseBoolean(String.valueOf(rsDataMap.getOrDefault(name, false)));
    }
}
