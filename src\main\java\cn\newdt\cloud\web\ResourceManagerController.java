package cn.newdt.cloud.web;

import cn.newdt.cloud.vo.CloudBackupStorageVO;
import cn.newdt.cloud.domain.CloudDataStorage;
import cn.newdt.cloud.dto.ConfigAndStorageResourceDTO;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.dto.ResourceDTO;
import cn.newdt.cloud.service.ResourceManagerService;
import cn.newdt.cloud.service.impl.KafkaService;
import cn.newdt.cloud.service.impl.MysqlV2Service;
import cn.newdt.cloud.service.impl.ZookeeperService;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.response.ResponseResult;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 资源配额管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/20 17:45
 */
@RestController
@RequestMapping("/cloudmanage/resourcemanager")
public class ResourceManagerController {

    @Autowired
    @Qualifier("mysqlResourceManageSvr")
    private ResourceManagerService resourceManagerService;

    @Autowired
    private MysqlV2Service mysqlV2Service;
    @Autowired
    private ZookeeperService zookeeperService;
    @Autowired
    private KafkaService kafkaService;

    @RequestMapping(value = "/mysql", method = RequestMethod.PUT)
    public ResponseResult updateMySQL(@RequestBody ResourceDTO resourceDTO) throws Exception {
        Matcher matcher = Pattern.compile("([MGT]$)").matcher(resourceDTO.getDisk());
        if (matcher.find()) {
            resourceDTO.setDisk(resourceDTO.getDisk().replaceAll(matcher.group(), matcher.group() + "i"));
        }
        Matcher memMatcher = Pattern.compile("([MGT]$)").matcher(resourceDTO.getMemory());
        if (memMatcher.find()) {
            resourceDTO.setMemory(resourceDTO.getMemory().replaceAll(memMatcher.group(), memMatcher.group() + "i"));
        }
        // fixme operator不能同时更新pvc 和 cpu
        mysqlV2Service.update(resourceDTO);
        return ResponseResult.ok("ok", null);
    }

    @RequestMapping(value = "/zookeeper", method = RequestMethod.PUT)
    public ResponseResult updateZooKeePer(@RequestBody ResourceDTO resourceDTO) throws Exception {
        Matcher memMatcher = Pattern.compile("([MGT]$)").matcher(resourceDTO.getMemory());
        if (memMatcher.find()) {
            resourceDTO.setMemory(resourceDTO.getMemory().replaceAll(memMatcher.group(), memMatcher.group() + "i"));
        }
        zookeeperService.update(resourceDTO);
        return ResponseResult.ok("ok", null);
    }

    @RequestMapping(value = "/kafka", method = RequestMethod.PUT)
    public ResponseResult updateKafka(@RequestBody ResourceDTO resourceDTO) throws Exception {
        Matcher memMatcher = Pattern.compile("([MGT]$)").matcher(resourceDTO.getMemory());
        if (memMatcher.find()) {
            resourceDTO.setMemory(resourceDTO.getMemory().replaceAll(memMatcher.group(), memMatcher.group() + "i"));
        }
        kafkaService.update(resourceDTO);
        return ResponseResult.ok("ok", null);
    }

//    @RequestMapping(value = "/mysql/disk", method = RequestMethod.PUT)
//    public ResponseResult updateMySQLDisk(@RequestBody ResourceDTO resourceDTO) throws Exception {
//        resourceManagerService.updateMySQLDisk(resourceDTO);
//        return ResponseResult.ok("ok", null);
//    }

    @ApiOperation(value = "删除PVC")
    @DeleteMapping("/pvc")
    public boolean deletePvc(@RequestParam(value = "kubeId") Integer kubeId,@RequestParam(value = "appId") Integer appId,
                             @RequestParam(value = "namespace") String namespace, @RequestParam(value = "pvcName") String pvcName) {
        return resourceManagerService.deletePvc(kubeId, namespace, pvcName, appId);
    }


    @ApiOperation(value = "获取集群中的应用列表", notes = "获取集群中的应用列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "condition", value = "查询条件", paramType = "query", dataType = "Map")
    })
    @PostMapping("/appList")
    public ResponseResult<Object> queryList(@RequestBody PageDTO condition) {
        condition.setCondition(condition.getCondition().entrySet().stream().filter(e->!"".equals(e.getValue()))
                .collect(Collectors.toMap(e->e.getKey(), e->e.getValue())));
        if (condition.getCondition().get("kubeId") == null) {
            // 查询应用视图
            return ResponseResult.ok(resourceManagerService.searchPage4AppView(condition), null);
        }
        return ResponseResult.ok(resourceManagerService.searchPageInfo(condition), null);
    }


    @ApiOperation(value = "根据appId获取对应pvc列表", notes = "根据appId获取对应pvc列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用Id", paramType = "query", dataType = "Integer")
    })
    @GetMapping("/pvcList")
    public List<Map<String, String>> getPvcList(@RequestParam(name = "appId", required = false) Integer appId,
                                @RequestParam(name = "appLogicId", required = false) Integer appLogicId,
                                @RequestParam(name = "other", required = false) Boolean other,
                                @RequestParam(name = "kubeId", required = false) Integer kubeId,
                                @RequestParam(name = "tenantId", required = false) Integer tenantId) {
        if ((null == other || !other) && (appId == null && appLogicId == null)) {
            throw new CustomException(600, "参数为空");
        }

        return this.resourceManagerService.allPvcList(appId, appLogicId,other,kubeId,tenantId);
    }


    @ApiOperation(value = "根据appId获取对应pv列表", notes = "根据appId获取对应pv列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用Id", paramType = "query", dataType = "Integer")
    })
    @GetMapping("/pvList")
    public List<Map<String, String>> pvList(@RequestParam(name = "appId", required = false) Integer appId,
                                            @RequestParam(name = "appLogicId", required = false) Integer appLogicId,
                                            @RequestParam(name = "other", required = false) Boolean other,
                                            @RequestParam(name = "kubeId", required = false) Integer kubeId,
                                            @RequestParam(name = "tenantId", required = false) Integer tenantId) {

        if ((null == other || !other) && (appId == null && appLogicId == null)) {
            throw new CustomException(600, "参数为空");
        }
        return this.resourceManagerService.pvList(appId, appLogicId, other, kubeId, tenantId);
    }


    @ApiOperation(value = "删除应用所属PVC", notes = "通过appId删除PVC")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appIdList", value = "appIdList", paramType = "body", dataType = "List<Integer>"),
            @ApiImplicitParam(name = "view", value = "application|cluster", paramType = "query", dataType = "String")
    })
    @DeleteMapping("/deletePvcByAppInfo")
    public void deletePvcByAppInfo(@RequestBody List<Integer> appIdList,
                                   @RequestParam String view) {
        if (appIdList == null || appIdList.isEmpty()) {
            throw new IllegalArgumentException("missing parameter idList");
        }
        if (!view.equals("application") && !view.equals("cluster")) {
            throw new IllegalArgumentException("missing parameter idList");
        }
        resourceManagerService.deletePvcByAppInfo(appIdList, view);
    }


    @ApiOperation(value = "删除PVC", notes = "通过集群ID、命名空间和pvcName删除PVC")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "namespace", value = "命名空间", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "pvcNames", value = "pvcName、appid、kubeid的集合", paramType = "query", dataType = "List<ConfigAndStorageResourceDTO> ")
    })
    @DeleteMapping("/deletePvcByName")
    public void deletePvcByName(@RequestParam(value = "namespace") String namespace,
                                @RequestBody List<ConfigAndStorageResourceDTO> pvcNames) {
        resourceManagerService.deletePvcByName(namespace, pvcNames);
    }


    @ApiOperation(value = "删除PV", notes = "通过集群ID和pvName删除PV")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pvNames", value = "pvName、appid、kubeid的集合", paramType = "query", dataType = "List<ConfigAndStorageResourceDTO>")
    })
    @DeleteMapping("/deletePv")
    public void deletePv(@RequestBody List<ConfigAndStorageResourceDTO> pvNames) {
        resourceManagerService.deletePv(pvNames);
    }

    @ApiOperation(value = "获取数据存储资源列表", notes = "获取数据存储资源列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageDTO", value = "获取数据存储资源列表", paramType = "body", dataType = "PageDTO")
    })
    @PostMapping("/dataStorageList")
    public PageInfo<CloudDataStorage> dataStorageList(@RequestBody PageDTO pageDTO) {
        return resourceManagerService.listDataStorage(pageDTO);
    }

    @ApiOperation(value = "获取备份存储资源列表", notes = "获取备份存储资源列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageDTO", value = "获取备份存储资源列表", paramType = "body", dataType = "PageDTO")
    })
    @PostMapping("/backupStorageList")
    public CloudBackupStorageVO backupStorageList() {
        return resourceManagerService.listBackupStorageEncrypt();
    }

    @ApiOperation(value = "添加数据存储资源", notes = "添加数据存储资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cloudDataStorage", value = "数据存储资源对象", paramType = "body", dataType = "CloudDataStorage")
    })
    @PostMapping("/insertDataStorage")
    public void insertDataStorage(@RequestBody CloudDataStorage cloudDataStorage) {
        resourceManagerService.insertDataStorage(cloudDataStorage);
    }

    @ApiOperation(value = "添加备份存储资源", notes = "添加备份存储资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cloudBackupStorage", value = "备份存储资源对象", paramType = "body", dataType = "CloudBackupStorage")
    })
    @PostMapping("/insertBackupStorage")
    public void insertBackupStorage(@RequestBody CloudBackupStorageVO cloudBackupStorage) {
        resourceManagerService.insertBackupStorage(cloudBackupStorage);
    }

    @ApiOperation(value = "修改数据存储资源", notes = "修改数据存储资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cloudDataStorage", value = "数据存储资源对象", paramType = "body", dataType = "CloudDataStorage")
    })
    @PutMapping("/updateDataStorage")
    public void updateDataStorage(@RequestBody CloudDataStorage cloudDataStorage) {
        resourceManagerService.updateDataStorage(cloudDataStorage);
    }

    @ApiOperation(value = "修改备份存储资源", notes = "修改备份存储资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cloudBackupStorage", value = "备份存储资源对象", paramType = "body", dataType = "CloudBackupStorage")
    })
    @PutMapping("/updateBackupStorage")
    public void updateBackupStorage(@RequestBody CloudBackupStorageVO cloudBackupStorage) {
        resourceManagerService.updateBackupStorage(cloudBackupStorage);
    }

    @ApiOperation(value = "删除数据存储资源", notes = "删除数据存储资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据存储资源对象id", paramType = "query", dataType = "Integer")
    })
    @DeleteMapping("/deleteDataStorage/{id}")
    public void deleteDataStorage(@PathVariable("id") Integer id) {
        resourceManagerService.deleteDataStorage(id);
    }

    @ApiOperation(value = "删除备份存储资源", notes = "删除备份存储资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "备份存储资源对象id", paramType = "query", dataType = "Integer")
    })
    @DeleteMapping("/deleteBackupStorage/{id}")
    public void deleteBackupStorage(@PathVariable("id") Integer id) {
        resourceManagerService.deleteBackupStorage(id);
    }

    @ApiOperation(value = "获取备份存储支持类型", notes = "获取备份存储支持类型")
    @GetMapping("/getBackupStorageSupport")
    public List<String> getBackupStorageSupport() {
        List<String> backupStorageList = resourceManagerService.getBackupStorageSupport();
        return backupStorageList;
    }

    @ApiOperation(value = "检查挂载", notes = "检查挂载")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cloudBackupStorage", value = "备份存储资源对象", paramType = "body", dataType = "CloudBackupStorage")
    })
    @PostMapping("/checkMount")
    public Map checkMount(@RequestBody CloudBackupStorageVO cloudBackupStorage) {
        Map<String, Object> checkRes = resourceManagerService.checkMount(cloudBackupStorage);
        return checkRes;
    }
}
