package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.StatusConstant;
import cn.newdt.cloud.domain.RestoreHis;
import cn.newdt.cloud.service.BackupService;
import cn.newdt.cloud.service.RestoreServiceImpl;
import com.google.common.collect.ImmutableSet;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface BackupCreateAppWatch {

    RestoreServiceImpl getRestoreService();

    BackupService getBackupService();

    /**
     * 检查是否已经进行过恢复操作
     *
     * @return true 表示已经进行过恢复操作，false 表示未进行过
     */
    boolean isRecoveryDone(Integer appid);

    /**
     * 获取当前恢复操作状态
     *
     * @return 成功、失败、执行中
     */
    String getProcessStatus(Integer appid);

    /**
     * 执行恢复操作,通过调用 backuputil完成
     */
    void performRecovery(Integer appid, Integer backupHisId);

    /**
     * 执行恢复操作流程
     */
    String executeRecoveryProcess(Integer appid, Integer backupHisId);

}
