package cn.newdt.cloud.utils;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.dto.Label;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.vo.CloudAppVO;
import io.fabric8.kubernetes.api.model.PersistentVolume;
import io.fabric8.kubernetes.api.model.PersistentVolumeClaim;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class MGRUtil {
    public static final String MGR_EXPORTER_ENDPOINT_NAME = "mgr-exporter";

    public static String getMgrExporterSvcName(String crName) {
        return "innodbcluster-" + crName;
    }

    public static String toMyCnf(Map<String, String> config) {
        String collect = config.entrySet().stream()
                .map(e -> e.getKey() + "=" + e.getValue())
                .collect(Collectors.joining("\n"));
        return "[mysqld]\n" + collect;
    }

    public static String getSecretName(String crName, String user) {
        return "mgr-" + crName + "-" + user;
    }

    public static void createHostPath(CloudAppVO vo, KubeClient kubeClient) {
        String crName = vo.getCrName();
        Label[] labels = AppKind.MYSQL_MGR.labels(crName);
        String pvSelectorLabel = "mgr-pv";
        String disk = vo.getDisk();
        String hostpathRoot = vo.getHostpathRoot();
        Integer members = vo.getMembers();
        String namespace = vo.getNamespace();

        List<PersistentVolume> pvs = new ArrayList<>(KubeClientUtil.provideHostpathPV("datadir-" , null, crName, namespace, disk, hostpathRoot, members, 0, labels, pvSelectorLabel));
        List<PersistentVolumeClaim> pvcs = new ArrayList<>(KubeClientUtil.provideHostpathPVC("datadir-", null, namespace, disk, labels, pvs, pvSelectorLabel, 0));

        if (!pvs.isEmpty()) {
            kubeClient.createBatch(pvs.get(0).getKind(), pvs.toArray(new PersistentVolume[0]));
        }
        if (!pvcs.isEmpty()) {
            kubeClient.createBatch(pvcs.get(0).getKind(), pvcs.toArray(new PersistentVolumeClaim[0]));
        }
    }

    public static void createBackupHostPath(CloudAppVO vo, KubeClient kubeClient) {
        String crName = vo.getCrName();
        Label[] labels = AppKind.MYSQL_MGR.labels(crName);
        String pvSelectorLabel = "mgr-pv";
        String disk = vo.getDisk();
        String hostpathRoot = vo.getHostpathRoot();
        String namespace = vo.getNamespace();

        List<PersistentVolume> pvs = new ArrayList<>(KubeClientUtil.provideHostpathPV("backupdir-" , null, crName, namespace, disk, hostpathRoot, 1, 0, labels, pvSelectorLabel));
        List<PersistentVolumeClaim> pvcs = new ArrayList<>(KubeClientUtil.provideHostpathPVC("backupdir-", null, namespace, disk, labels, pvs, pvSelectorLabel, 0));

        if (!pvs.isEmpty()) {
            kubeClient.createBatch(pvs.get(0).getKind(), pvs.toArray(new PersistentVolume[0]));
        }
        if (!pvcs.isEmpty()) {
            kubeClient.createBatch(pvcs.get(0).getKind(), pvcs.toArray(new PersistentVolumeClaim[0]));
        }
    }

    public interface Constants {
        String ROLE_LABEL = "mysql.oracle.com/cluster-role";
        String ROLE_PRIMARY = "PRIMARY";
        String ROLE_SECONDARY = "SECONDARY";
    }

    public static final String REVISION_JOB_DATA_KEY = "oldK8sRevision";

    public static String getStsRevision(CloudApp app, KubeClient client) {
        return KubeClientUtil.getCurrentRevision(app.getCrName(), app.getNamespace(), client);
    }

    public static String getStsName(CloudApp app) {
        return app.getCrName();
    }

    public static Map<String, String> getPVCLabels(CloudApp app) {
        return Arrays.stream(AppKind.MYSQL_MGR.labels(app.getCrName())).collect(Collectors.toMap(Label::getName, Label::getValue));
    }

    public static List<String> getPodNames(String appName, Integer members){
        List<String> podNames = new ArrayList<>(members);
        for (int i = 0; i < members; i++) {
            podNames.add(appName + "-" + i);
        }
        return podNames;
    }


    public enum InnoDBClusterStatusEnum {
        PENDING, INITIALIZING, ONLINE, INVALID, UNKNOWN, OFFLINE, ONLINE_PARTIAL, NO_QUORUM, SPLIT_BRAIN, ONLINE_UNCERTAIN, OFFLINE_UNCERTAIN, NO_QUORUM_UNCERTAIN, FINALIZING;
    }
}
