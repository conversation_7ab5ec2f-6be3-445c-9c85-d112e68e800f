package cn.newdt.cloud.service;

import cn.newdt.cloud.common.OpLogContext;
import cn.newdt.cloud.config.CloudRequestContext;
import cn.newdt.cloud.config.CustomerConfigProperties;
import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.StatusConstant;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.domain.cr.Broker;
import cn.newdt.cloud.domain.cr.MongoDBCluster;
import cn.newdt.cloud.domain.cr.MongoDBCommunity;
import cn.newdt.cloud.dto.*;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.repository.SynchronizedLocalCache;
import cn.newdt.cloud.service.alert.AlertConfigService;
import cn.newdt.cloud.service.csi.CSILoader;
import cn.newdt.cloud.service.csi.CSInterface;
import cn.newdt.cloud.service.impl.*;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.service.sched.impl.InstanceDeleteWatch;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.*;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.bean.DBConnect;
import cn.newdt.commons.bean.MetaVO;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.AsymmetricEncryptionUtil;
import cn.newdt.commons.utils.CommonUtil;
import cn.newdt.commons.utils.UserUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.alibaba.ttl.TtlRunnable;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.shindata.kafka.v1.Kafka;
import com.shindata.mysql.v1.MySQLHA;
import com.shindata.opengauss.v1.OpenGaussCluster;
import com.shindata.postgre.v1.PostgreSql;
import com.shindata.redis.v1.Redis;
import com.shindata.redis.v1.RedisCluster;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.client.CustomResource;
import io.fabric8.kubernetes.client.utils.Serialization;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.RowProcessor;
import org.apache.commons.dbutils.handlers.MapListHandler;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.CloudAppConstant.CustomLabels.APP_COMPONENT;
import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgCategory.*;
import static java.util.stream.Collectors.toMap;

/**
 * AppKindService的默认实现. 实现了应用各种操作的通用逻辑. 包含了一些必须在子类实现的抽象方法.
 * 原则上不应直接修改该类，请通过继承扩展所需功能.
 * <li>覆盖validate 在正在安装前修改vo的值</li>
 * <li>覆盖getInstallExtData 在安装时传递扩展参数给watch类</li>
 * <li>覆盖completeInstanceProperty(AppInstanceVO i, PodDTO pod) 扩展查询实例列表</li>
 * <li>覆盖searchPage 扩展查询应用列表</li>
 */
public abstract class DefaultAppKindService <T extends CustomResource> implements AppKindService, IpReservable{
    Logger log = LoggerFactory.getLogger(DefaultAppKindService.class);
    @Autowired
    protected KubeClientService clientService;
    @Autowired
    protected NetworkService networkService;
    @Autowired
    protected CloudAppService appService;
    @Autowired
    protected AlertConfigService alertConfigService;
    @Resource
    protected ResourceManagerService resourceManagerService;
    @Autowired
    private BackupTimerService backupTimerService;
    @Autowired
    protected BackupService backupService;
    @Autowired
    protected ESUtil esUtil;
    @Autowired
    protected FTPUtils ftpUtils;
    @Autowired
    protected AppOperationHandler operationHandler;
    @Autowired
    private TenantService tenantService;
    @Autowired
    private AppMultiAZService appMultiAZService;
    @Autowired
    protected KubeConfigService kubeConfigService;
    @Autowired
    private CustomerConfigProperties customerConfigProperties;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    protected CloudAppLogicService appLogicService;
    @Autowired
    protected SysConfigService sysConfigService;
    @Autowired
    protected CloudDbParamTemplateService cloudDbParamTemplateService;
    @Autowired
    protected KubeGroupService kubeGroupService;
    @Autowired
    protected CloudAppConfigService appConfigService;
    @Autowired
    protected KubeSchedulerService kubeSchedulerService;
    @Autowired
    protected AccessManagementService accessManagementService;
    @Autowired
    protected NodeService nodeService;
    @Autowired
    protected ResourceChangeHisService resourceChangeHisService;
    @Autowired @Lazy
    protected BackupUtil backupUtil;

    // <deptId, <kubeId, quota>>
    private SynchronizedLocalCache<String, CloudQuota> quotaCache;

    private ConcurrentHashMap<String, Boolean> ns_created_cache = new ConcurrentHashMap<>();
    /**
     * resultSet to List<Map>处理类
     */
    private final MapListHandler mapListHandler = new MapListHandler();

    protected ThreadLocal<List<String>> newlyCreatedPvc = new TransmittableThreadLocal<List<String>>() {
        @Override public List<String> initialValue() {
            return new ArrayList<String>();
        }
    };

    @PostConstruct
    public void init(){
//        Map<String, CloudQuota> collect = quotaService.findAll().stream()
//                .collect(Collectors.toMap(q -> q.getTenantId() + "_" + q.getKubeId(), q -> q));
//        quotaCache.putAll(collect);
    }

    @Override
    public List<AppInstanceVO> findInstanceList(Integer appId, String sortProp, String sort) {
        CloudApp app = appService.get(appId);
        KubeClient client = clientService.get(app.getKubeId());
        // 
        List<PodDTO> pods = client.listPod(app.getNamespace(), AppKind.valueOf(app.getKind(), app.getArch()).labelOfPod(app));
        Map<String, PodDTO> collect = pods.stream().collect(Collectors.toMap(PodDTO::getPodName, p -> p));
        List<AppInstanceVO> instances = appService.findInstancesMetrics(appId, pods);

        // deprecated
        completeInstanceProperty(instances, appId);

        List<CompletableFuture> futures = new ArrayList<>();
        instances.stream().forEach(i -> {
            PodDTO pod = collect.get(i.getPodName());
            if (CloudAppConstant.PodPhase.Pending.equals(pod.getStatus())) {
                return;
            }
            // fetch role of each app instance
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    completeInstanceProperty(i, pod, app, client);
                } catch (Exception e) {
                    log.error("complete instance error " +  e.getMessage());
                }
            }));
        });
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        // sort by sort prop
        if (!StringUtils.isEmpty(sortProp))
            instances.sort((a, b) -> {
                int result = 0;
                try {
                    Object aVal = PropertyUtils.getProperty(a, sortProp);
                    Object bVal = PropertyUtils.getProperty(b, sortProp);
                    if (aVal == null) result = -1;
                    else if (bVal == null) result = 1;
                    else {
                        if (aVal.getClass().isPrimitive()) {
                            result = (int) ((double) aVal - (double) bVal);
                        } else {
                            result = ((Comparable) aVal).compareTo(bVal); // primary < secondary
                        }
                    }

                    if ("desc".equalsIgnoreCase(sort)) {
                        result = -result;
                    }
                } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
                    log.error("", e);
                }
                return result;
            });
        return instances;
    }

    protected void completeInstanceProperty(AppInstanceVO appInstanceVO, PodDTO pod, CloudApp app, KubeClient client) {

    }

    /**
     * 补充实例属性, e.g. 角色
     */
    protected void completeInstanceProperty(List<AppInstanceVO> instances, int appId) {

    }

    @Override
    public abstract AppKind getKind();

    /**
     * @return true 自动选择应用下pods调度到的节点
     */
    public abstract boolean nodePolicy();

    /**
     * construct app specified custom resource object
     *
     * 主要用来进行 cr的构造，步骤如下：
     * 1、cr相关 image 的构造
     * 2、cr相关 resource 属性的填充
     * 3、cr相关 volume 的构造
     * 4、cr相关 cm 的应用
     * 5、cr相关 secret 的应用
     *
     * @param vo app data
     * @param ips 分配的ip
     */
    public abstract T doInstall(CloudAppVO vo, List<String> ips) throws Exception;

    /**
     * 对密码解密
     * @param encryptedPassword
     * @return
     * @throws Exception
     */
    public String decryptPassword(String encryptedPassword) {
        // 解密后密码
        String decryptPassword = null;
        // 1.判断是否传递了密码
        if (!StringUtils.isBlank(encryptedPassword)) {
            // 2.对密码进行解密
            decryptPassword = AsymmetricEncryptionUtil.getEncryptInstance().decrypt(encryptedPassword);
        }
        return decryptPassword;
    }

    /**
     * 在安装时创建service
     *
     * @param cloudApp
     * @param installCr
     * @param <T>
     */
    @Transactional(rollbackFor = Exception.class)
    public <T extends CustomResource> void createServiceOnInstall(CloudAppVO cloudApp, T installCr) {
        try {
            accessManagementService.createServiceOnInstall(cloudApp, installCr);
        } catch (Exception e) {
            log.error("", e);
            throw new CustomException(600, "创建访问管理出错！错误信息为：" + e.getMessage());
        }
    }

    public List<ServiceManager> openSourceKindServiceBuilder(
            String serviceType, CloudAppVO vo, List<?> serviceResources, String endpointName) {

        //1.校验
        AppKind kind = getKind();
        if (serviceResources.isEmpty()) return Collections.emptyList();
        int serviceManagerNum = kind.getServiceManagerNum(serviceType, vo.getMembers());
        if (CollectionUtils.isEmpty(serviceResources) || serviceResources.size() != serviceManagerNum) {
            throw new CustomException(600, "节点端口类型必须指定 " + serviceManagerNum
                    + " 个端口, 实际数量为 " + serviceResources.size() + ", 类型为 " + serviceType);
        }

        List<ServiceManager> svms = new ArrayList<>();
        String namespace = vo.getNamespace();

        for (int i = 0; i < serviceResources.size(); i++) {
            Object serviceResource = serviceResources.get(i);
            //2. 开源实例类型为额外的普通 service，通过 fabric8 构建 Service,再 set 到 ServiceManager 中
            // 2.1 获取serviceName，开源实例使用 sts，没有固定 ip
            String serviceName = getKind().getWriteServiceName(vo.getCrName(), null);
            Integer nodePort = null;
            int dbPort = getKind().getDbPort();

            //2.2 声明构建所需的变量
            Map<String, String> labelMap = Arrays.stream(getKind().labels(vo.getCrName()))
                    .collect(toMap(Label::getName, Label::getValue));
            Map<String, String> selectorLabelMap = Arrays.stream(getKind().labelOfService(vo))
                    .collect(toMap(Label::getName, Label::getValue));
            Map<String, String> annotationMap = null;
            Map<String, Object> additionalMap = null;

            //2.3 初始化 ServiceManager
            ServiceManager serviceManager = new ServiceManager();
            // 没有读写分离，且只需要一个 service
            serviceManager.setPurpose(CloudAppConstant.ServicePurpose.WRITE);
            serviceManager.setServiceName(serviceName);
            serviceManager.setServiceType(serviceType);

            //2.4 根据 ServiceType 填充 NodePort 和 lb注解map
            if (CloudAppConstant.ServiceType.NODE_PORT.equals(serviceType)) {
                //nodeport 方式，serviceResources 结构为 List<Integer>，进行分配 NodePort，只需要一个 NodePort
                nodePort = (Integer) serviceResource;
                serviceManager.setPort(nodePort);
            } else if (CloudAppConstant.ServiceType.LOAD_BALANCER.equals(serviceType)) {
                //lb 方式，serviceResources 结构为 List<String>，进行分配 lbip，只需要一个 lbip，端口为 dbport
                serviceManager.setPort(dbPort);
                String lbip = (String) serviceResource;
                serviceManager.setExternalIp(lbip);
                annotationMap = new HashMap<String, String>(){{
                    putAll(accessManagementService.buildLBAnnotationMap(lbip)); }};
                // LB注意要配置外部连接策略为 Local，否则默认为 Cluster，导致容器内无法获取外部真实 IP
                additionalMap = new HashMap<String, Object>(){{ put("externalTrafficPolicy", "Local");}};
            }
            svms.add(serviceManager);

            //3. 构建 Service
            String yaml = KubeClientUtil.buildServiceYaml(
                    namespace, serviceType, serviceName, nodePort, dbPort, endpointName,
                    labelMap, annotationMap, selectorLabelMap, additionalMap);
            OpLogContext.instance().YAML("Service", yaml, "");
            // 开源的 svc 不属于 cr.spec 可配置属性，所以需要单独提交
            clientService.get(vo.getKubeId()).applyYaml(yaml, namespace);
        }

        return svms;
    }

    public void openSourceKindUpdateServiceBuilder(
            List<ServiceManager> svcMgrs, CloudApp app, Object oldServiceResource) throws Exception {
        ServiceManager serviceManager = svcMgrs.get(0);
        String namespace = serviceManager.getNamespace();
        String serviceType = serviceManager.getServiceType();
        String serviceName = serviceManager.getServiceName();
        Integer nodePort = null;
        Integer undoNodePort = null;
        int dbPort = getKind().getDbPort();
        Map<String, String> labelMap = Arrays.stream(getKind().labels(app.getCrName()))
                .collect(Collectors.toMap(Label::getName, Label::getValue));
        Map<String, String> selectorLabelMap = Arrays.stream(getKind().labelOfService(app))
                .collect(Collectors.toMap(Label::getName, Label::getValue));
        Map<String, String> annotationMap = null;
        Map<String, String> undoAnnotationMap = null;
        Map<String, Object> additionalMap = null;

        //2.4 根据 ServiceType 填充 NodePort 和 lb注解map
        if (CloudAppConstant.ServiceType.NODE_PORT.equals(serviceType)) {
            nodePort = serviceManager.getPort();
            undoNodePort = (Integer) oldServiceResource;
        } else if (CloudAppConstant.ServiceType.LOAD_BALANCER.equals(serviceType)) {
            serviceManager.setPort(dbPort);
            String lbip = serviceManager.getExternalIp();
            serviceManager.setExternalIp(lbip);
            annotationMap = new HashMap<String, String>(){{
                putAll(accessManagementService.buildLBAnnotationMap(lbip)); }};
            // LB注意要配置外部连接策略为 Local，否则默认为 Cluster，导致容器内无法获取外部真实 IP
            additionalMap = new HashMap<String, Object>(){{ put("externalTrafficPolicy", "Local");}};
            undoAnnotationMap = new HashMap<String, String>(){{
                putAll(accessManagementService.buildLBAnnotationMap(String.valueOf(oldServiceResource))); }};
        }

        //3. 构建 Service
        String replaceYaml = KubeClientUtil.buildServiceYaml(
                namespace, serviceType, serviceName, nodePort, dbPort, null,
                labelMap, annotationMap, selectorLabelMap, additionalMap);
        String undoYaml = KubeClientUtil.buildServiceYaml(
                namespace, serviceType, serviceName, undoNodePort, dbPort, null,
                labelMap, undoAnnotationMap, selectorLabelMap, additionalMap);
        OpLogContext.instance().YAML("Service", replaceYaml, undoYaml);
        clientService.get(app.getKubeId()).applyYaml(replaceYaml, namespace);

        HashMap<String, String> data = new HashMap<>();
        data.put("oldServiceResource", oldServiceResource + "");
        data.put("svms", JsonUtil.toJson(svcMgrs));

        appService.callScheduler(
                app, app.getCr(), data, ActionEnum.UPDATE_SERVICE, getProcessorClass(ActionEnum.UPDATE_SERVICE));
    }

    /**
     * 定义操作类型ActionEnum和OpsProcessor映射关系
     * 默认提供删除pod的processor类, 可在子类重新覆盖.
     * <b>注意, 子类default 子句需调用super.getProcessorClass</b>
     */
    public Class<? extends OpsPostProcessor> getProcessorClass(ActionEnum action) {
        switch (action) {
            case DELETE_POD:
                return InstanceDeleteWatch.class;
            default:
                throw new UnsupportedOperationException();
        }
    }

    @Override
    public void callScheduler(CloudApp app, String crYaml, Object extData, ActionEnum actionEnum, Class<?> appDeleteWatchClass) {
        try {
            appService.callScheduler(app, crYaml, extData, actionEnum, appDeleteWatchClass);
        } catch (SchedulerException | JsonProcessingException e) {
            throw new CustomException(600, "提交调度器失败");
        }
    }

    /**
     * 是否支持固定IP, <i>mongodb类型配合calico插件无法实现固定IP</i>
     * @param app
     */
    protected abstract boolean supportIPAM(CloudAppVO app);

    /**
     * 设置vo属性并校验<br/>
     * - 权限
     * - 名称
     * - csi
     * - cni
     * - ip,port,node
     */
    public void validateAndSetup(CloudAppVO app) {
        // set up kind & arch
        AppKind kind = getKind();
        app.setKind(kind.getKind());
        app.setArch(kind.getArch());
//         todo 抽出validator 接口
        basicSetupAndValidation(app);
        extendSetup(app);
    }

    /**
     * 临时修改参数
     * @param vo 提交参数
     * @param config 参数模板内容
     * extracted from mysqlV2Service
     */
    protected void overWriteCnfParam(CloudAppVO vo, Map<String, String> config) {
        overWriteCnfParam(config, vo.getTemplateTmpParam(), getKind().getProduct(), ImmutableMap.of("cpu", vo.getCpu(), "memory", vo.getMemory(), "storage", vo.getDisk()));
    }
    protected void overWriteCnfParam(Map<String, String> config, Map<String, String> tempParamMap, String product, Map<String, String> resourceList) {
        if (tempParamMap != null) {
            // 用户临时指定的参数
            this.prohibitParam(String.join(",", tempParamMap.keySet()), product);
            for (Map.Entry<String, String> entry : tempParamMap.entrySet()) {
                if ("-deleted-".equals(entry.getValue())) {
                    config.remove(entry.getKey());
                } else {
                    config.put(entry.getKey(), entry.getValue());
                }
            }
        }

        // 自动计算的最佳配置
        String paramFormulaStr = sysConfigService.findOne(PARAM_FORMULA, product);
        if (StringUtils.isBlank(paramFormulaStr)){
            return;
        }
        Map<String, String> paramFormulaMap = JSONObject.parseObject(paramFormulaStr).toJavaObject(Map.class);

        for (Map.Entry<String, String> entry : paramFormulaMap.entrySet()) {
            if (!config.containsKey(entry.getKey())) {
                String val = calculate(entry.getValue(),
                        resourceList);
                config.putIfAbsent(entry.getKey(), val);
            }
        }
    }


    private Pattern pattern = Pattern.compile(".* #U=(B|K|M|G)");

    /**
     * 1. 变量：Memory,如果内存为1G，Memory=1024；Cpu,如果cpu为1核，则cpu=1；Disk,如果磁盘为1G，则Disk=1024；
     *    ConfigServersDisk,Mongodb cluster专用 如果configServersDisk为1G，ConfigServersDisk=1024
     * 2. 函数：支持MAX()、MIN()
     * 3. 运算符：支持整型数据的加减乘除,如果计算结果是小数，会截断取整数部分
     * 4. 非计算表达式值，直接返回
     * 5. mysql 缓存变量单位是Byte；Pg|Og 缓存变量单位是KB
     * @param expRaw 原始表达式
     */
    protected String calculate(String expRaw, Map<String, String> resourceList)  {
        // 如果expRaw不含变量，而是参数值，直接返回
        String calculateExp = expRaw.toUpperCase();
        if (!calculateExp.contains("MEMORY") && !calculateExp.contains("CPU") && !calculateExp.contains("DISK")
                && !calculateExp.contains("CONFIGSERVERSDISK")) {
            return expRaw;
        }
        calculateExp = calculateExp.replaceAll("MAX", "max");
        calculateExp = calculateExp.replaceAll("MIN", "min");
        calculateExp = calculateExp.replaceAll("MEMORY", "#MEMORY");// ＃variableName
        calculateExp = calculateExp.replaceAll("CPU", "#CPU");
        // DISK 需要匹配完整词，避免匹配CONFIGSERVERSDISK中的DISK
        calculateExp = calculateExp.replaceAll("\\bDISK\\b", "#DISK");
        calculateExp = calculateExp.replaceAll("CONFIGSERVERSDISK", "#CONFIGSERVERSDISK");
        ExpressionParser parser = new SpelExpressionParser();
        StandardEvaluationContext context = new StandardEvaluationContext(Math.class);
        // required
        Double cpuCore = MetricUtil.getCpuCores(resourceList.get("cpu"));
        context.setVariable("CPU", new Double(cpuCore).intValue());
        Long memory = MetricUtil.getLongValueOfUnit(resourceList.get("memory"), 'M');
        context.setVariable("MEMORY", new Double(memory).longValue());
        Long disk = MetricUtil.getLongValueOfUnit(resourceList.get("storage"), 'M');
        context.setVariable("DISK", new Double(disk).longValue());
        return parser.parseExpression(calculateExp).getValue(context, Double.class).longValue() + "";
    }

    public void extendSetup(CloudAppVO app) {
        log.warn("default implementation does nothing");
    }

    private String validateQuota(CloudAppVO app) {
        String cpuLimit = app.getCpu(), memoryLimit = app.getMemory(), diskLimit = app.getDisk();
        int cpu = MetricUtil.getCpuMilliCores(cpuLimit).intValue();
        long memory = MetricUtil.getResourceLongValue(memoryLimit);
        long disk = StringUtils.isEmpty(diskLimit) ? 0L : MetricUtil.getResourceLongValue(diskLimit);

        quotaCache.compute(app.getOwnerTenant() + "_" + app.getKubeId(), q -> {
            if (cpu * app.getMembers() > q.getLimitsCpu() - q.getLimitsCpuUsage()
                    || memory * app.getMembers() > q.getLimitsMemory() - q.getLimitsMemoryUsage()
                    || disk * app.getMembers() > q.getLimitsDisk() - q.getLimitsDiskUsage()) {
                throw new CustomException(600, "申请资源超出部门资源配额");
            } else {
                q.setLimitsCpuUsage(q.getLimitsCpuUsage() + cpu * app.getMembers());
                q.setLimitsMemoryUsage(q.getLimitsMemoryUsage() + memory * app.getMembers());
                q.setLimitsDiskUsage(q.getLimitsDiskUsage() + disk * app.getMembers());
            }
           return q;
        });
        return null;

    }

    private void basicSetupAndValidation(CloudAppVO app) {
        Objects.requireNonNull(app.getKubeId(), "请选择安装集群");
        // set up owner
        if (app.getOwnerTenant() == null) {
            // 从CloudRequestContext获取租户id
            app.setOwnerTenant(CloudRequestContext.getContext().getTenantId());
            if (app.getNamespace() == null) {
                CustPreconditions.checkNotZero(app.getOwnerTenant(), "");
                CloudTenant tenant = tenantService.findById(app.getOwnerTenant());
                app.setNamespace(tenant.getNamespace());
            }
        }
        CustPreconditions.checkNotZero(app.getNamespace(), "无法确定命名空间");
        app.setOwnerUser(UserUtil.getCurrentUser().getUserid());
        app.setOwnerName(UserUtil.getCurrentUser().getName());

        // check app kind
        CustPreconditions.checkNotZero(app.getKind(), "应用类型为空");

        // 校验name 和 crname
        createCrName(app);
        validateAppName(app);

        // 转换单位为二进制. ui不区分两种格式.
        if (app.getDisk() != null) {
            Matcher matcher = Pattern.compile("([MGT]$)").matcher(app.getDisk());
            if (matcher.find()) {
                app.setDisk(app.getDisk().replaceAll(matcher.group(), matcher.group() + "i"));
            }
        }
        if (app.getMemory() != null) {
            Matcher memMatcher = Pattern.compile("([MGT]$)").matcher(app.getMemory());
            if (memMatcher.find()) {
                app.setMemory(app.getMemory().replaceAll(memMatcher.group(), memMatcher.group() + "i"));
            }
        }
        app.setCniType(appService.getCniType(app.getKubeId()));

        validateStorageClass(app);
        // 移到 convertToCr app.setCpu(app.getCpu().replaceAll("c", ""));
    }

    protected void validateAppName(CloudAppVO app) {
        Objects.requireNonNull(app.getName(), "请填写名称");

        // 验证name符合规范, 根据用户提交名称生成k8s中cr的名称
        if (StringUtils.isEmpty(app.getCrName()))
            appService.processAppName(app);

        // unique check: kubeId+kind+namespace+name
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("fullName", app.getName()); // 精确匹配
        map.put("kubeId", app.getKubeId());
        map.put("namespace", app.getNamespace());
        //2024-06-18 修改：当前命名空间下的 cr 名称不可同名，若该同名 为 该 应用的 前置组件 则 可以
        List<CloudApp> list = appService.findNotCompleteDeletedListWithoutAnnotation(map); // find non-deleted app without @resourceview annotation

        if (list.isEmpty())
            return;

        confirmSameCrnameIsFireApp(app, list);

    }

    //判断同名的crname是否为即将安装应用的前置组件
    private void confirmSameCrnameIsFireApp(CloudAppVO app, List<CloudApp> list) {
        //即将安装的应用类型
        AppKind installAppKind = AppKind.valueOf(app.getKind(), app.getArch());
        //查询到的同名的cr的应用类型
        AppKind sameAppKind = AppKind.valueOf(list.get(0).getKind(), list.get(0).getArch());
        // 映射installAppKind到对应的前置组件类型
        Map<AppKind, AppKind> requiredFrontAppMap = ImmutableMap.of(
                AppKind.Redis, AppKind.Sentinel,
                AppKind.Broker, AppKind.NameServer,
                AppKind.Kibana, AppKind.Elasticsearch,
                AppKind.Clickhouse, AppKind.ClickHouse_Zookeeper
        );
        //安装的应用 不属于 有前置 组件的类型 或者 已安装的同名的 crname 不属于 前置组件类型 不允许安装
        if (!requiredFrontAppMap.values().contains(sameAppKind) || !requiredFrontAppMap.keySet().contains(installAppKind)) {
            throw new DuplicateAppNameException("目前存在重名实例[" + app.getName() + "]，请修改实例名称或者检查回收站是否已经清理");
        }

        AppKind requiredFrontApp = requiredFrontAppMap.get(installAppKind);

        //查询出已有的crname 和 即将 安装的应用的 crname 不是前置组件的关系，则 不允许创建
        if (requiredFrontApp != null && !requiredFrontApp.equals(sameAppKind)) {
            throw new DuplicateAppNameException("目前存在重名实例[" + app.getName() + "]，请修改实例名称或者检查回收站是否已经清理");
        }
    }
    public static class DuplicateAppNameException extends RuntimeException{
        public DuplicateAppNameException(String msg) {
            super(msg);
        }
    }

    /**
     * 设置默认存储类型
     * 校验hostpath root
     */
    protected void validateStorageClass(CloudAppVO app) {
        // validate and set data storage
        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());

        CSInterface csi = getCsInterface(app.getCsiType(), app.getKubeId(), appKind);
        if (csi == null) return;
        app.setCsiType(csi.getType());
        app.setStorageClassName(csi.getStorageClassName());
        if (csi.getType().equals(CSInterface.HOSTPATH)) {
            if (StringUtils.isEmpty(app.getHostpathRoot())) {
                throw new CustomException(600, "未指定hostpath根目录");
            }
        } else {
            app.setHostpathRoot(""); // go lang string init val
        }
        // validate and set backup storage
        if (appKind == AppKind.Broker || appKind == AppKind.NameServer || appKind == AppKind.Kafka || appKind == AppKind.Zookeeper
                || appKind == AppKind.Sentinel){
            // 此5种应用类型没有备份存储
            return;
        }
        // app.getBackupCsiType() 不会为空
        // 前端逻辑：备份存储类型默认值取纳管集群设置的默认存储，如果纳管集群未设置默认存储，则取数据存储类型
//        CSInterface backCSI = getCsInterface(app.getBackupCsiType(), app.getKubeId(), appKind);
        app.setBackupCsiType(app.getBackupCsiType());
        app.setBackupStorageclassname(app.getBackupCsiType());
        app.setBackupHostpathRoot(""); // go lang string init val
    }

    /**
     * 不指定存储插件时获取集群纳管时配置的默认类型, 例如集群组安装时不能指定存储类型
     */
    protected CSInterface getCsInterface(String csiType, Integer kubeId, AppKind appKind) {
        CSInterface csi;
        if(StringUtils.isNotEmpty(csiType)) {
            csi = CSILoader.getCsi(csiType);
        } else {
            csi = appService.getCSI(kubeId, appKind.getProduct());
        }
        return csi;
    }

    @Override
    public PageInfo<? extends CloudAppVO> searchPage(PageDTO page) {
        page.getCondition().put("kind", getKind().getKind());
        // TODO 添加arch 条件, refact mongo list
        return appService.searchPage(page);
    }

    @Transactional(rollbackFor = Exception.class)
    public void install(CloudAppVO vo) {
        T cr = null;
        KubeClient client = null;
        try {
            validateAndSetup(vo);
            AppKind kind = AppKind.valueOf(vo.getKind(), vo.getArch());
            appService.add(vo); // auto_increment id
            client = clientService.get(vo.getKubeId());
            ensureNamespace(vo.getNamespace(), client, vo.getKubeId());
            // config network
            List<String> ips = configNetwork(vo);
            // config image config
            vo.setImageConfig(appConfigService.getImageManifest(getKind(), vo.getVersion()));
            // decrypt password
            String decryptPassword = decryptPassword(vo.getPassword());
            vo.setEncryptedPassword(vo.getPassword()); // todo use encrypted field in submitted form
            if (!StringUtils.isBlank(decryptPassword)) {
                //将解密后的密码放回vo对象
                vo.setPassword(decryptPassword);
            }
            // create cr object
            cr = doInstall(vo, ips); // todo remove ips parameter
            // call k8s
            Class<T> crClass = (Class<T>) kind.getCrClass();
            // setup jobdatamap
            setInstallExtData(vo);
            //创建访问管理
            createServiceOnInstall(vo, cr);
            // callScheduler
            appendCustomLabels(vo, cr);
            appService.callScheduler(vo, YamlEngine.marshal(cr), vo.getExtInstallData(), ActionEnum.CREATE, getProcessorClass(ActionEnum.CREATE), vo);

            cr = client.createCustomResource(cr, crClass);
            createCrControlResource(cr, vo);

        } catch (Exception e) {
            logError("install failed, roll back if need", vo, e);
            if (vo.getId() != null)
                networkService.rollbackParallel(vo, getIpReservationTarget()); // ip port 在其他线程提交事务, 需要回收, 不能在当前事务，否则会被回滚
            if (!(e instanceof DuplicateAppNameException)) {
                // start rollback in k8s
                T finalCr = cr;
                KubeClient finalClient = client;
                CompletableFuture.runAsync(TtlRunnable.get(()->{
                    try {
                        // 新线程, 不关注结果
                        // 回滚k8s资源
                        if (finalCr != null) {
                            clientService.get(vo.getKubeId()).deleteCustomResource(finalCr, (Class<T>) getKind().getCrClass());
                        }
                        deleteCrControlledResources(vo);
                        // 删除新建的PVC
                        if (finalClient != null && !CollectionUtils.isEmpty(newlyCreatedPvc.get())) {
                            for (String pvcName : newlyCreatedPvc.get()) {
                                finalClient.deletePvc(vo.getNamespace(), pvcName);
                            }
                        }
                    } catch (Exception ex) {
                        logError("roll back installation failed", vo, ex);
                    }
                }));
            }

            throw new CustomException(600, String.format("安装应用[%s]%s/%s, ",
                    kubeConfigService.getSimple(vo.getKubeId()).getName(), vo.getNamespace(), vo.getName()) + e.getMessage(), e);
        } finally {
            newlyCreatedPvc.remove();
        }
    }

    /**
     * cr创建后创建依赖cr的其他资源对象
     * @param cr
     * @param vo
     */
    protected void createCrControlResource(T cr, CloudAppVO vo) {

    }

    protected void deletePvc(CloudAppVO vo) {

    }

    /**
     * create namespaced rbac resource. used for role label maintaining for dameng,vastbase,redis,mongo etc.
     * @param kubeClient
     * @param namespace
     */
    protected void createAgentRbac(KubeClient kubeClient, String namespace) {
        String yaml = new StringSubstitutor(ImmutableMap.of("namespace", namespace, "name", getAgentServiceAccountName()))
                .replace(getAgentRbacTemplate());
        List<KubernetesResource> ress = Serialization.unmarshal(
                new ByteArrayInputStream(yaml.getBytes()), Collections.emptyMap());
        ress.stream().filter(res -> res instanceof ServiceAccount)
                .map(sa -> (ServiceAccount)sa)
                .findFirst()
                .ifPresent(sa -> {
                    if (!kubeClient.getGenericResource("v1", "ServiceAccount", namespace,
                            sa.getMetadata().getName()).isPresent())
                        kubeClient.applyYaml(yaml, namespace);
                });
    }

    protected String getAgentRbacTemplate() {
        return sysConfigService.findOne(CloudAppConstant.SysCfgCategory.OPERATOR_CONFIG,
                CloudAppConstant.SysCfgName.AGENT_RBAC);
    }

    /**
     * @return service account name
     */
    protected String getAgentServiceAccountName() {
        return sysConfigService.findOne(K8S_SYS_CONFIG_CM_DATA, CloudAppConstant.SysCfgName.LABEL_UPDATE_SERVICEACCOUNT);
    }

    protected void appendCustomLabels(CloudAppVO vo, T cr) {
        Map<String, String> labels = new HashMap<>();
        if (cr.getMetadata().getLabels() != null) {
            labels.putAll(cr.getMetadata().getLabels());
        }
        labels.put(CloudAppConstant.CustomLabels.RESOURCE_VERSION, vo.getResourceVersion());
        cr.getMetadata().setLabels(labels);
    }

    private void ensureNamespace(String namespace, KubeClient client, Integer kubeId) {
        if (customerConfigProperties.isNamespaceAutoCreate() && !customerConfigProperties.isTenantAsNamespace()) {
            String key = kubeId + "_" + namespace;
            if (!ns_created_cache.containsKey(key) || !ns_created_cache.get(key)) {
                departmentService.ensureNamespacedObjs(client, namespace);
                ns_created_cache.put(key, true);
            }
        }
    }

    void logInfo(String message, CloudApp app) {
        log.info("Install app [{}/{} in cluster {}] " + message, app.getNamespace(), app.getName(), app.getKubeId());
    }

    void logError(String message, CloudApp app, Exception e) {
        String msg = String.format("Install app [%s/%s in cluster %s] " + message, app.getNamespace(), app.getName(), app.getKubeId());
        log.error(msg, e);
    }

    void logError(String message, CloudApp app) {
        String msg = String.format("Install app [%s/%s in cluster %s] " + message, app.getNamespace(), app.getName(), app.getKubeId());
        log.error(msg);
    }


    //处理crname，例如给nameeservice和broker添加cr名称后缀
    public void createCrName(CloudApp vo) {
    }

    public String getAppSystemName(InstallAppVo<? extends OverrideSpec> vo) {
        return StringUtils.isEmpty(vo.getAppSystemName()) ? "默认应用" : vo.getAppSystemName();
    }

    // todo make abstract
    public void deleteCrControlledResources(CloudApp app) {
        log.warn("default rollback implementation does nothing");
    }

    protected List<String> configNetwork(CloudAppVO vo) {
        // fixme spring transaction do not propagate to new thread, so rollback is needed when the error happens
        /*CompletableFuture<List<String>> nodeFuture = CompletableFuture.supplyAsync(() -> {
            List<String> nodes = null;
            if (nodePolicy()) {
                // 多中心选择集群组部署会将选择节点提前, 避免重复调度
                if (vo.getIpNode() != null) {
                    nodes = Arrays.stream(vo.getIpNode()).map(CloudApp.IpNode::getNode).filter(Objects::nonNull).collect(Collectors.toList());
                }
                if (nodes == null || nodes.isEmpty()) {
                    if (CollectionUtils.isEmpty(vo.getAffinity())){
                        vo.setAffinity(kubeConfigService.getK8sNodes(vo.getKubeId(), true).stream().map(NodeDTO::getNodeName).collect(Collectors.toList()));
                    }
                    nodes = networkService.chooseNode(vo, vo.getMembers(), true, vo.getAffinity())
                            .stream().map(NodeDTO::getNodeName).collect(Collectors.toList());
                }
            }
            else {
                // 默认节点调度范围根据 node.taints 配置限定
                if (CollectionUtils.isEmpty(vo.getAffinity())) {
                    vo.setAffinity(kubeConfigService.getK8sNodes(vo.getKubeId(), true).stream().map(NodeDTO::getNodeName).collect(Collectors.toList()));
                }
            }
            return nodes;
        });*/
        CompletableFuture<List<String>> ipFuture = CompletableFuture.supplyAsync(() -> {
            List<String> ips = null;
            if (supportIPAM(vo)) {
                // 指定ip, 仅维护占用关系
                if(vo.getIpNode() != null) {
                    ips = Arrays.stream(vo.getIpNode()).map(CloudApp.IpNode::getIp).filter(StringUtils::isNotEmpty)
                            .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(ips)) {
                        networkService.updateIpStatus(vo, ips);
                    }
                }
                if (CollectionUtils.isEmpty(ips)) {
                    ips = networkService.allocateIp(vo, vo.getMembers(), getIpOwnerKind(), getIpReservationTarget());
                }
            }
            return ips;
        });
//        CompletableFuture<Void> portFuture = CompletableFuture.runAsync(() -> {
//            networkService.allocateNodePort(vo, getNodeportNum());
//        });
        List<String> ips;
        try {
//       List<String> nodes = nodeFuture.get();
            ips = ipFuture.get();
//            portFuture.get(); 访问管理
            vo.setIpList(JsonUtil.toJson(AppUtil.composeNodeNameAndIp(null, ips)));
        } catch (InterruptedException | ExecutionException e) {
            log.error("configure network failed, need rollback", e);
            String error = "为应用配置网络错误";
            if (e.getCause() != null && e.getCause().getClass() == CustomException.class)
                error = e.getCause().getMessage();
            throw new CustomException(600, error);
        }
        return ips;
    }

    public List<String> getNodesMigrateTo(Integer appId, String curNode) {
        CloudApp app = appService.get(appId);
        List<NodeDTO> nodeDTOS = clientService.get(app.getKubeId()).listSimpleNodes(kubeSchedulerService.queryById(app.getKubeSchedulerId()));
        return nodeDTOS.stream().filter(nodeDTO -> !nodeDTO.getNodeName().equals(curNode))
                .filter(nodeDTO -> nodeDTO.isReady())
                .filter(nodeDTO -> nodeDTO.isSchedulable())
                .map(nodeDTO -> nodeDTO.getNodeName())
                .collect(Collectors.toList());
    }

    @Override
    public void upgrade(int appid, String version) throws Exception {
        throw new UnsupportedOperationException();
    }

    /**
     * 扩缩容-应用视图使用
     * @param overrideSpec 设置内部的member值为期望值
     */
    @Override
    public void scale(int appId, OverrideSpec overrideSpec, ActionEnum actionEnum) throws Exception{}
    /**
     * 扩缩容-
     * @param members 扩缩容后节点数
     */
    @Override
    public void scale(int appId, int members) throws Exception {}

    /**
     * 缩容节点-用户指定的实例
     * @param appId App primary key
     * @param scaled 要删除的pod ip&node
     */
    @Override
    public void scaleDown(int appId, List<CloudApp.IpNode> scaled) throws Exception{}
    /**
     * 缩容节点-分片结构，用户无法指定被删除实例
     * @param masterSize 分片数
     * @param spareSize 副本数
     */
    @Override
    public void scaleDown(int appId, Integer masterSize, Integer spareSize) throws Exception{}

    /**
     * merge vo.config with parameter template, then overwrite with temp param, finally calculate memory related parameter from formula-sysconfig
     */
    protected Map<String, String> setupDbParamConfig(CloudAppVO appVO) {
        Map<String, String> config = setupDbParamConfig(appVO.getDbParamTemplateId(), appVO.getTemplateTmpParam(), getKind().getProduct(),
                NullableImmutableMap.of("cpu", appVO.getCpu(), "memory", appVO.getMemory(), "storage", appVO.getDisk()), appVO.getConfig());
        return config;
    }

    /**
     * setup component config belong to this app
     * @param componentProduct component type see {@link AppKind#getProduct()}
     * @param resourceList
     * @param dbParamTemplateId param template of component
     * @param tempParamMap temporary param of component
     */
    protected Map<String, String> setupDbParamConfig(Integer dbParamTemplateId, Map<String, String> tempParamMap, String componentProduct, Map<String, String> resourceList, Map<String, String> config) {
        config = Optional.ofNullable(config).orElse(new HashMap<>());
        if (dbParamTemplateId != null ){
            MySQLParamTemplateDTO mysqlParamTemplate = cloudDbParamTemplateService.getMysqlParamTemplate(dbParamTemplateId);
            Map<String, String> paramTemplateMap = cloudDbParamTemplateService.composeMysqlCnfUseDbParamTemplateToMap(mysqlParamTemplate);
            paramTemplateMap.putAll(config);
            config = paramTemplateMap;
        }
        overWriteCnfParam(config, tempParamMap, componentProduct, resourceList);
        return config;
    }

    @Override
    public void scaleDown(int appId, Integer masterSize, Integer shards, Integer configServersMembers, Integer routerServersMembers) throws Exception{}

    @Override
    @Transactional
    public void uninstall(int appId) throws Exception {
        operationHandler.handleUninstall(appId, getKind().getCrClass(), this);
    }

    @Override
    @Transactional
    public void delete(CloudApp app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        CustomResource cr = kubeClient.listCustomResource(getKind().getCrClass(), app.getCrName(), app.getNamespace());
        if (cr == null) return;
        if (cr.getGroup().contains("shindata.com")) {
            // cr 类型
            setMaintenanceFlag(cr, true);
            kubeClient.updateCustomResource((T)cr, (Class<T>)getKind().getCrClass());

            for (AppInstanceVO instance : appService.findInstances(app.getId())) {
                kubeClient.deletePod(instance.getNamespace(), instance.getPodName());
            }
        } else {
            // sts 类型, mgr, mongo rs, es, ~~kibana~~
            String[] names = getStsOrDeployNames(app);
            if (names == null) throw new RuntimeException("statefulset name not specified");
            for (String name : names) {
                // 借助operator重建
                kubeClient.scaleSts(name, app.getNamespace(), 0);
            }
        }

    }

    private void setMaintenanceFlag(CustomResource cr, boolean maintenance) {
        try {
            Method setMaintenance = cr.getSpec().getClass().getDeclaredMethod("setMaintenance", Boolean.class);
            setMaintenance.invoke(cr.getSpec(), maintenance);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            throw new RuntimeException("Failed to set maintenance flag on CR", e);
        }
    }

    protected String[] getStsOrDeployNames(CloudApp app) {
        return null;
    }

    @Override
    @Transactional
    public void deletePerm(CloudApp app) {
        // 确保 pod 被删除
        // 删除保留的自定义资源（CR）
        clientService.get(app.getKubeId()).deleteCustomResource(app.getNamespace(), app.getCrName(), getKind().getCrClass());
        // 删除未设置所有权引用的 CR 控制的 Kubernetes 资源
        AppServiceLoader.getInstance(AppKind.valueOf(app.getKind(), app.getArch())).deleteCrControlledResources(app);
        // 删除 AccessManager
        accessManagementService.deleteServiceForAppDelete(app);
        // 释放 IP、nodeport、lbip
        networkService.postDeleteApp(app, getIpReservationTarget());
        // 清除数据库用户
        appService.deleteUser(app);
        alertConfigService.deleteAlertFor(app);
        // 清理未结备份
        backupService.stopBackupJobHisForApp(app.getId());
        // 清理定时备份
        PageInfo<BackupTimer> timerList = backupTimerService.listPage(new PageDTO() {{
            setCondition(new HashMap<String, Object>() {{
                put("appId", app.getId());
                put("appDeleteStatus", CloudAppConstant.AppDeleteStatus.RECYCLED);
            }});
        }});
        for (BackupTimer backupTimer : timerList.getList()) {
            try {
                backupTimerService.deleteBackupTimerById(backupTimer.getBackupTimerId(), app.getKind());
            } catch (Exception ignore) {
                log.error(ignore.getMessage());
            }
        }
        // 更新应用状态
        softDelete(app);
        // 清理pvc
        resourceManagerService.deletePvcByApp(app);
    }

    protected void softDelete(CloudApp app) {
        CloudAppLogic cloudAppLogic = appLogicService.get(app.getLogicAppId());
        if (AppMultiAZService.DeployType.multi_az == AppMultiAZService.DeployType.valueOf(cloudAppLogic.getDeployType()))
            appService.selectForUpdate(app.getLogicAppId());
        app.setDeleted(true);
        appService.update(app);
        // 删除成功通知multi-az logic app
        appMultiAZService.updateDelete(app.getLogicAppId(), CloudAppConstant.AppDeleteStatus.DELETED);
    }

    @Override
    @Transactional
    public void recreate(CloudApp app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        CustomResource cr = kubeClient.listCustomResource(getKind().getCrClass(), app.getCrName(), app.getNamespace());
        if (cr.getGroup().contains("shindata.com")) {
            // cr 类型
            setMaintenanceFlag(cr, false);
            kubeClient.updateCustomResource((T)cr, (Class<T>)getKind().getCrClass());
        } else {
            // sts 类型, mgr, mongo rs, es, kibana
            String[] names = getStsOrDeployNames(app);
            if (names == null) throw new RuntimeException("statefulset name not specified");
            for (String name : names) {
                kubeClient.deleteStatefulset(name, app.getNamespace());
            }
        }
    }

    public boolean deleteAppInstance(int appId, String podName) {
        CloudApp app = appService.get(appId);
        // 如果当前存在任务则返回
        ResourceChangeHis latestRcs = resourceChangeHisService.getLatestByAppId(appId);

        CustPreconditions.checkState(!StatusConstant.RUNNING.equals(latestRcs.getStatus()), "当前应用正在执行 " + latestRcs.getCommand() + " 操作，请稍候再试");
        String uid = clientService.get(app.getKubeId()).getPod(app.getNamespace(), podName).getMetadata().getUid();
        try {
            Map<String, Object> map = new HashMap<String, Object>() {{
                put("podName", podName);
                put("uid", uid);
            }};
            appService.callScheduler(app, null, map, ActionEnum.DELETE_POD, getProcessorClass(ActionEnum.DELETE_POD), 5);
            return clientService.get(app.getKubeId()).deletePod(app.getNamespace(), podName);
        } catch (Exception e) {
            log.error("触发删除失败", e);
            throw new CustomException(600, "删除失败，" + e.getMessage());
        }
    }

    /**
     * 应用安装传递给processor的额外参数，比如实例交付时用户填写的需要创建的账密
     */
    protected void setInstallExtData(CloudAppVO vo) {

    }

    /**
     * 指定 IP pool type, 为应用分配IP时从该IP池中选择IP
     */
    public String getIpOwnerKind() {
        // use appkind as default
        return getKind().getKind();
    }


    /**
     * 按各个中心的规约生成app对象，各个实例在调用本方法后可自定义独特属性
     */
    public CloudAppVO overrideSpec(CloudAppLogic logicApp, Integer kubeId, InstallAppVo<? extends OverrideSpec> vo) {
        CloudAppVO app = new CloudAppVO();
        app.setConfig(vo.getConfig());
        app.setName(vo.getName());
        app.setCrName(vo.getCrName());
        app.setAppSystemName(vo.getAppSystemName());
        OverrideSpec overrideSpec = vo.getOverrideSpecs().get(kubeId);
        app.setLogicAppId(logicApp.getId());
        app.setCpu(overrideSpec.getCpu());
        app.setMemory(overrideSpec.getMemory());
        app.setDisk(overrideSpec.getDisk());
        app.setMembers(overrideSpec.getMembers());
        app.setKubeId(kubeId);
        app.setOwnerTenant(logicApp.getOwnerTenant());
        app.setVersion(logicApp.getVersion());
        app.setNamespace(logicApp.getNamespace());
        app.setHostpathRoot(overrideSpec.getHostpathRoot());
        app.setUsername(vo.getUsername());
        app.setPassword(vo.getPassword());
        app.setDbParamTemplateId(vo.getDbParamTemplateId());
        // 安装时如果指定了csi, 则覆盖集群的默认配置
        app.setCsiType(overrideSpec.getCsiType() != null ? overrideSpec.getCsiType() : null);
        app.setBackupDisk(overrideSpec.getBackupDisk());
        app.setBackupStorageclassname(overrideSpec.getBackupStorageClass());
        app.setBackupHostpathRoot(overrideSpec.getBackupHostpathRoot());
        app.setBackupCsiType(overrideSpec.getBackupCsiType());
        app.setAffinity(overrideSpec.getAffinity());
        app.setIpList(JsonUtil.toJson(overrideSpec.getIpNodes()));
        // 设置同城属性
        app.setDisasterRecoveryRole(overrideSpec.getRole());
        // 设置参数模板临时参数
        app.setTemplateTmpParam(vo.getTemplateTmpParam());
        //设置备份历史id
        if(null != vo.getBackupHisId()){
            app.setBackupHisId(String.valueOf(vo.getBackupHisId()));
        }

        // 查找同名的已删除应用，计算当前应用的resource_version.
        // fix 【【容器云】点击存储管理，批量选择已经删除的应用，点击删除-确定。显示删除成功。存储列表任存在未删除的存储】https://www.tapd.cn/44948243/bugtrace/bugs/view?bug_id=1144948243001001541
        //
        int count = appService.countByNameKind(ImmutableMap.of("crName", vo.getCrName(), "kind", vo.getKind(), "arch", vo.getArch(), "namespace", vo.getNamespace(), "kubeId", kubeId));
        app.setResourceVersion(++count + "");
        // 调度策略
        app.setKubeSchedulerId(vo.getKubeSchedulerId());
        KubeScheduler kubeScheduler = kubeSchedulerService.queryFastUseById(vo.getKubeSchedulerId(), kubeId);
        app.setSelector(kubeScheduler.getSelector());
        app.setToleration(kubeScheduler.getToleration());
        app.setAntiAffinityRequired(kubeScheduler.getAntiAffinityRequired());
        app.setEnableAlert(vo.isEnableAlert());
        app.setExecutorGroupId(vo.getExecutorGroupId());
        app.setOrgId(vo.getOrgId());

        //创建svc：根据 kubeid 设置 ServiceType 给各个实例类型
        app.setServiceType(kubeConfigService.getServiceType(kubeId));
        if (Objects.equals(app.getKubeId(), vo.getPrimaryKubeId()))
            app.setRole(CloudAppConstant.ROLE_PRIMARY);
        else app.setRole(CloudAppConstant.ROLE_STANDBY);
        app.setDeployType(vo.getDeployType());

        return app;
    }

    @Override
    public OverrideSpec reviewSpec(int id) {
        CloudApp app = appService.get(id);
        return reviewSpec(app);
    }

    @Override
    public OverrideSpec reviewSpec(CloudApp app) {
        OverrideSpec overrideSpec = new OverrideSpec();
        overrideSpec.setCpu(app.getCpu());
        overrideSpec.setMembers(app.getMembers());
        overrideSpec.setMemory(app.getMemory());
        overrideSpec.setDisk(app.getDisk());
        overrideSpec.setBackupDisk(app.getBackupDisk());
        overrideSpec.setKubeId(app.getKubeId());
        overrideSpec.setKubeSchedulerId(app.getKubeSchedulerId());
        overrideSpec.setTenantId(app.getOwnerTenant());
        return overrideSpec;
    }

    @Override
    public InstallAppVo<? extends OverrideSpec> parseInstallVo(String data) {
        return JsonUtil.toObject(data, new TypeReference<InstallAppVo<OverrideSpec>>() {});
    }

    public String getIpReservationTarget() {
        return "pod";
    }

    public Map<String, Map<String, String>> getConfigParam(Integer appId, Integer logicAppId, String componentKind) {
        Map<String, Map<String, String>> map = new HashMap<>();
        List<CloudApp> apps;
        if (logicAppId != null){
            apps = appLogicService.getPhysicApps(logicAppId);
        } else {
            apps = new ArrayList<>();
            apps.add(appService.findListByCondition( ImmutableMap.of("id",appId)).get(0));
        }
        for (CloudApp app : apps) {
            Map<String, String> config = null;
            AppKind kind = AppKind.valueOf(app.getKind(), app.getArch());
            /*方案一：从k8s实时cr中获取config 参数*/
//            KubeClient kubeClient = clientService.get(app.getKubeId());
//            CustomResource cr = kubeClient.listCustomResource(kind.getCrClass(), app.getCrName(), app.getNamespace());
//            if (cr instanceof MySQLHA) {
//                MySQLHA mySQLHA = (MySQLHA) cr;
//                config = mySQLHA.getSpec().getConfig();
//            } else if (cr instanceof RedisCluster) {
//                RedisCluster redisCluster = (RedisCluster) cr;
//                config = redisCluster.getSpec().getConfig();
//            } else if (cr instanceof OpenGaussCluster) {
//                OpenGaussCluster openGaussCluster = (OpenGaussCluster) cr;
//                config = openGaussCluster.getSpec().getConfig();
//            }
            /* 方案二：从app表的cr字段获取config 参数*/
            if (kind == AppKind.MYSQL_HA){
                config = YamlEngine.unmarshal(app.getCr(), MySQLHA.class).getSpec().getConfig();
            } else if (kind == AppKind.Redis_Cluster){
                config = YamlEngine.unmarshal(app.getCr(), RedisCluster.class).getSpec().getConfig();
            } else if (kind == AppKind.Redis){
                config = YamlEngine.unmarshal(app.getCr(), Redis.class).getSpec().getConfig();
            } else if (kind == AppKind.OpenGauss){
                config = YamlEngine.unmarshal(app.getCr(), OpenGaussCluster.class).getSpec().getConfig();
            }else if (kind == AppKind.MongoDB){
                config = YamlEngine.unmarshal(app.getCr(), MongoDBCommunity.class).getSpec().getAdditionalMongodConfig();
            } else if (kind == AppKind.Kafka) {
                config = YamlEngine.unmarshal(app.getCr(), Kafka.class).getSpec().getConfig();
            } else if (kind == AppKind.Broker) {
                config = YamlEngine.unmarshal(app.getCr(), Broker.class).getSpec().getConfig();
            } else if (kind == AppKind.MongoDB_Cluster) {
                if("DB".equalsIgnoreCase(componentKind)){
                    config = YamlEngine.unmarshal(app.getCr(), MongoDBCluster.class).getSpec().getShardServers().getConfig();
                }else if("CONFIG".equalsIgnoreCase(componentKind)){
                    config = YamlEngine.unmarshal(app.getCr(), MongoDBCluster.class).getSpec().getConfigServers().getConfig();
                }else if(CloudAppConstant.MongoDB.MONGOS.equalsIgnoreCase(componentKind)){
                    config = YamlEngine.unmarshal(app.getCr(), MongoDBCluster.class).getSpec().getRouterServers().getConfig();
                }
            } else if (kind == AppKind.PostgreSQL) {
                config = YamlEngine.unmarshal(app.getCr(), PostgreSql.class).getSpec().getConfig();
            }
            map.put(app.getKubeName() + ":" + app.getKubeId(), config);
        }
        return map;
    }


    @Override
    public void prohibitParam(String param) {
        prohibitParam(param, getKind().getProduct());
    }

    public void prohibitParam(String param, String product) {
        String prohibitContent = sysConfigService.findOne(PARAM_PROHIBIT, product);
        if (StringUtils.isAnyEmpty(param, prohibitContent)) {
            return;
        }
        Set<String> result = new HashSet<>();
        String[] params = param.split(",");
        List<String> prohibited = Arrays.asList(prohibitContent.split(","));
        for (String prop : params) {
            if (prohibited.contains(prop)) {
                result.add(prop);
            }
        }
        if (!result.isEmpty()) {
            String join = StringUtils.join(result, "、");
            log.error("[prohibitParam] 参数 {} 禁止修改，请移除", join);
            throw new CustomException(600, "参数 " + join + " 禁止修改，请移除");
        }
    }

    public void updatePassword(Password password ,int appId) throws Exception{};

    public void modifyConfigParam(Map<String, String> params, Integer appId, String componentKind) throws Exception {
        throw new UnsupportedOperationException();
    }

    /**
     * @param component 可选值 MASTER,DATA(ES); CONFIG,ROUTER(MongoCluster); DB(其他)，默认值: DB
     * @return 根据pod模板返回所有容器的资源请求总量(单个实例)
     */
    public Map<String, ? extends Number> getTotalContainerRequests(String component) {
        Map<String, Long> podRequests = new HashMap<>();

        List<PodTemplateSpec> podTemplateSpec = getPodTemplateSpec(component);
        for (PodTemplateSpec podTemplate : podTemplateSpec) {
            podTemplate.getSpec().getContainers().stream().forEach(c -> {
                if (c.getResources() == null || c.getResources().getRequests() == null)
                    return;
                Map<String, Quantity> requests = c.getResources().getRequests();
                podRequests.compute("cpu", (k, v) -> (v == null ? 0 : v)
                        + (requests.get("cpu") == null ? 0 :
                        MetricUtil.getCpuMilliCores(requests.get("cpu").toString())));
                podRequests.compute("memory", (k, v) -> (v == null ? 0 : v)
                        + (requests.get("memory") == null ? 0 :
                        MetricUtil.getLongValue(requests.get("memory").toString())));
            });
        }
        return podRequests;
    }

    /**
     * @param component default DB, if pass all, return all the podTemplate of the kind
     */
    protected List<PodTemplateSpec> getPodTemplateSpec(String component) {
        AppKind kind = getKind();
        return appService.getPodTemplateSpec(kind, component);
    }

    /**
     * @return 根据pod模板返回除主容器外所有其他容器的资源limit总量
     */
    public Map<String, ? extends Number> getTotalContainerLimits(String component) {
        Map<String, Long> podLimits = new HashMap<>();

        List<PodTemplateSpec> podTemplateSpecs = getPodTemplateSpec(component);
        for (PodTemplateSpec podTemplate : podTemplateSpecs) {
            podTemplate.getSpec().getContainers().stream().forEach(c -> {
                if (c.getResources() == null || c.getResources().getLimits() == null)
                    return;
                Map<String, Quantity> limits = c.getResources().getLimits();
                podLimits.compute("cpu", (k, v) -> (v == null ? 0 : v)
                        + (limits.get("cpu") == null ? 0 :
                        MetricUtil.getCpuMilliCores(limits.get("cpu").toString())));
                podLimits.compute("memory", (k, v) -> (v == null ? 0 : v)
                        + (limits.get("memory") == null ? 0 :
                        MetricUtil.getLongValue(limits.get("memory").toString())));
            });
//            podTemplate.getSpec().get
//                            podLimits.compute("disk", (k, v) -> (v == null ? 0 : v)
//                        + MetricUtil.getLongValue())
        }
        return podLimits;
    }


    /**
     * 转换容忍度为cr所需的数据结构和
     * O -->toleration 中的第二个字符
     */
    protected <O> List<O> convertCRTolerations(TolerationDTO[] tolerationArray, Class<O> clazz) {
        if (tolerationArray == null) return null;
        List<Toleration> tolerationList = Arrays.stream(tolerationArray)
                .filter(item -> StringUtils.isNotEmpty(item.getOperator()))
                .map(tole -> new TolerationBuilder().withKey(tole.getKey())
                .withOperator(tole.getOperator())
                .withValue(StringUtils.isEmpty(tole.getValue()) ? null : tole.getValue())
                .withEffect(tole.getEffect()).withTolerationSeconds(tole.getTolerationSeconds()).build())
                .collect(Collectors.toList());
        return tolerationList.parallelStream().map(item -> {
            O toleration = null;
            try {
                toleration = clazz.newInstance();
                BeanUtil.copyNonNullProperties(item, toleration);
                return toleration;
            } catch (InstantiationException | IllegalAccessException e) {
                e.printStackTrace();
            }
            return toleration;
        }).collect(Collectors.toList());
    }
    /**
     * 转换Node亲和为cr所需的数据结构
     * N -->NodeAffinity 首字符
     */
    public  <N> N convertCRNodeAffinity(SelectorDTO[] selectorDTOS, Class<N> clazz) {
        if (selectorDTOS == null) return null;
        List<NodeSelectorRequirement> requirementList = Arrays.stream(selectorDTOS)
                .filter(item -> StringUtils.isNotEmpty(item.getOperator()))
                .map(selector -> new NodeSelectorRequirementBuilder()
                        .withKey(selector.getKey())
                        .withValues(selector.getValues().stream().filter(StringUtils::isNoneEmpty).collect(Collectors.toList()))
                        .withOperator(selector.getOperator()).build())
                .collect(Collectors.toList());
        if (requirementList.isEmpty()) return null;

        NodeAffinity nodeAffinity = new NodeAffinityBuilder()
                .withNewRequiredDuringSchedulingIgnoredDuringExecution()
                .withNodeSelectorTerms(new NodeSelectorTerm(requirementList, null))
                .endRequiredDuringSchedulingIgnoredDuringExecution()
                .build();
        if (clazz.getTypeName().equals(NodeAffinity.class.getTypeName())){
            return (N) nodeAffinity;
        }
        return JsonUtil.toObject(clazz, JsonUtil.toJson(nodeAffinity));
    }

    public List<Map<String, Object>> query(MetaVO metaVO, String sql, String params, boolean ignore) throws Exception {
        Integer queryApiLimitSize = 1000;
        List<Map<String, Object>> result = Lists.newArrayList();
        try (Connection conn = getConnectionExceptionInfo(metaVO, ignore); PreparedStatement stmt = conn.prepareStatement(sql, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY)) {
            stmt.setQueryTimeout(120);
            QueryRunner qRunner = new QueryRunner();
            if ("MYSQL".equalsIgnoreCase(metaVO.getDbproduct()) || DbTypeUtils.isSequoiadbInstance(metaVO)) {
                stmt.setFetchSize(Integer.MIN_VALUE);
            } else {
                stmt.setFetchSize(queryApiLimitSize);
            }

            Object[] paraArr = null;
            if (params != null && params.trim().length() > 0) {
                paraArr = params.split(",");
            }
            if (Objects.nonNull(paraArr)) {
                qRunner.fillStatement(stmt, paraArr);
            }
            try (ResultSet rs = stmt.executeQuery()) {
                RowProcessor rowProcessor = new ListMapRowProcessor();
                Map<String, Object> data;
                rs.setFetchSize(queryApiLimitSize);
                while (true) {
                    if (rs.next()) {
                        data = rowProcessor.toMap(rs);
                        result.add(data);
                        if (result.size() == queryApiLimitSize) {
                            break;
                        }
                    } else {
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("sql : {} ", sql, e);
            throw new CustomException(600, "查询schema 失败" + e);
        }
        return result;
    }

    public Map<String, List<Map<String, Object>>> querySqlWithPara(MetaVO metaVO, String sql, Boolean useDB, String dbName, List<List<Object>> parmList) {
        Map<String, List<Map<String, Object>>> resultMap = new HashMap<>();
        List<Map<String, Object>> resultList = new ArrayList<>();
        // 定义初始化的key
        String initMapKey = "" + dbName;
        String mapKey = "";
        // 由于下方需要替换,这里使用一个变量保存原始的SQL
        String initSql = sql;
        //获取连接，查询结果
        try (Connection conn = DBConnectManager.getInstance().getConnection(metaVO)) {
            if (conn != null) {
                QueryRunner qRunner = new QueryRunner();
                // 标识是否需要useDB
                String useDataBase = "USE # ;";

                if (useDB) {
                    if (org.apache.commons.lang.StringUtils.isNotBlank(dbName) && dbName.indexOf(",") < 0) {
                        useDataBase = useDataBase.replace("#", dbName);
                        qRunner.execute(conn, useDataBase);
                    }
                }
                // 判断参数列表是不是空
                if (!CollectionUtils.isEmpty(parmList)) {
                    for (List<Object> parmlist : parmList) {
                        Object[] parmArray = parmlist.toArray();
                        mapKey = initMapKey;
                        // 构造一个map的key
                        for (Object objValue : parmArray) {
                            mapKey = mapKey + "*" + String.valueOf(objValue);
                        }

                        // #符号表示需要替换的参数,?表示可以直接用不需要替换的参数
                        if (sql.contains("#")) {
                            // 要求参数列表的参数顺序和SQL中的占位符顺序相同
                            for (int i = 0; i < parmArray.length; i++) {
                                sql = sql.replaceFirst("#", java.util.regex.Matcher.quoteReplacement(String.valueOf(parmArray[i])));
                            }
                            resultList = qRunner.query(conn, sql, mapListHandler);
                            // ?表示不需要替换的参数,直接使用参数数组即可

                        } else if (sql.contains("?")) {
                            resultList = qRunner.query(conn, sql, mapListHandler, parmArray);

                        } else {
                            // 防止发生参数误填现象,直接按照没有参数进行处理
                            resultList = qRunner.query(conn, sql, mapListHandler);

                        }
                        // 将SQL初始化为最开始的SQL
                        sql = initSql;
                        // 语句相同,只有参数为区分项,若参数相同,则结果相同,使用参数作为key
                        resultMap.put(mapKey, resultList);
                    }
                } else {
                    // 没有参数直接执行该SQL获取结果后返回,无参数则次map中只有一条记录,key只需要是用一个初始就好
                    resultList = qRunner.query(conn, sql, mapListHandler);
                    resultMap.put(initMapKey, resultList);
                }
            }

        } catch (Exception e) {
            log.error("运行 jdbc 执行 sql 失败" + e);
            throw new CustomException(600, "查询 mysql 用户信息失败");
        }
        return resultMap;
    }

    /**
     *  执行相关sql
     * @param actionEnum
     * @param resourceChangeHis
     * @param execSqlList
     * @param execSqlStr
     * @param flushSql
     * @param metaVO
     */
    public void runnerExecSql(ActionEnum actionEnum, ResourceChangeHis resourceChangeHis, List<String> execSqlList, String execSqlStr,String flushSql, MetaVO metaVO) {
        try (Connection conn = DBConnectManager.getInstance().getConnection(metaVO)) {
            QueryRunner queryRunner = new QueryRunner();
            if (!CollectionUtils.isEmpty(execSqlList)) {
                execSqlList.parallelStream().forEach(
                        execsql -> {
                            try {
                                queryRunner.execute(conn, execsql);
                            } catch (SQLException throwables) {
                                throwables.printStackTrace();
                            }
                        }
                );
            }
            if (StringUtils.isNotEmpty(execSqlStr))
                queryRunner.execute(conn, execSqlStr);

            //权限信息刷新
            if (StringUtils.isNotEmpty(flushSql))
                queryRunner.execute(conn, flushSql);

            updateResourceChangeHis(resourceChangeHis, StatusConstant.SUCCESS, actionEnum.getAppOperation() + "成功");
            log.debug("执行SQL" + execSqlList + "成功");
        } catch (Exception e) {
            log.error("sql : " + execSqlList + " " + e.getMessage());
            updateResourceChangeHis(resourceChangeHis, StatusConstant.FAIL, actionEnum.getAppOperation() + "失败:" + e.getMessage());
            throw new CustomException(600, actionEnum.getAppOperation() + "失败");
        }
    }

    private Connection getConnectionExceptionInfo(MetaVO metaVO, boolean ignoreDbName) throws SQLException {
        try {
            return getConnection(metaVO, ignoreDbName);
        } catch (Exception e) {
            log.warn(CommonUtil.getTrace(e));
            throw new SQLException("DB Failed to get connected");
        }
    }

    private Connection getConnection(MetaVO metaVO, boolean ignoreDbName) throws SQLException {
        if (ignoreDbName) {
            return DBConnectManager.getInstance().getConnection(metaVO);
        }
        // todo only for getting mysql conn use
        DBConnect dbConnect;
        if (!org.apache.commons.lang3.StringUtils.isBlank(metaVO.getConnectType()) && "ORACLE".equals(metaVO.getDbproduct())) {
            dbConnect = DBConnect.getDBConnect(metaVO.getDbproduct().toUpperCase() + "_" + metaVO.getConnectType().toUpperCase());
        } else {
            dbConnect = DBConnect.getDBConnect(metaVO.getDbproduct().toUpperCase());
        }

        if (DbTypeUtils.isSequoiadbInstance(metaVO)) {
            String nodeTYpe = DbTypeUtils.getSupportDbTypeForNodeType(metaVO.getNode_type());
            dbConnect = DBConnect.getDBConnect(nodeTYpe);
        }

        return DBConnectManager.getInstance().getConnection(metaVO, dbConnect, false, true, false);
    }

    /**
     * 根据修改资源操作记录信息
     *
     * @param status
     * @param msg
     */
    public void updateResourceChangeHis(ResourceChangeHis resourceChangeHis, String status, String msg) {
        resourceChangeHis.setStatus(status);
        resourceChangeHis.setMsg(msg);
        resourceChangeHis.setUpdateTime(new Timestamp(System.currentTimeMillis() + 2000));
        resourceChangeHisService.update(resourceChangeHis);
    }

    public MetaVO getMetaVOByApp(CloudApp app, String dbName) {
        AppDBVO appDBVO = accessManagementService
                .getFirstWriteServiceAddressByAppIdAndKubeId(app.getId(), app.getKubeId());
        MetaVO metaVO = new MetaVO();
        metaVO.setPort(String.valueOf(appDBVO.getPort()));
        metaVO.setIp(appDBVO.getIp());
        metaVO.setVip(appDBVO.getIp());
        metaVO.setDbname(dbName);
        return metaVO;
    }

    /**
     * 获取应用实例所部署在的node集合
     */
    public Set<String> getAppNodes(Integer appId, String component) {
        CloudApp app = appService.get(appId);
        KubeClient client = clientService.get(app.getKubeId());
        List<PodDTO> podDTOS = client.listPod(app.getNamespace(), AppUtil.getPodLabel(app));
        return podDTOS.stream().filter(item -> {
            if (StringUtils.isEmpty(component)) return true;// 不分组件的应用pod
            else if (component.startsWith("es-")) return item.getLabel(CloudAppConstant.ES_STS_NAME).endsWith(component);// es特殊处理
            else return component.equals(item.getLabel(APP_COMPONENT));
        }).map(PodDTO::getNodeName).collect(Collectors.toSet());
    }

    /**
     * 进行集群组调度的时候使用：
     * 需要获取到 每个应用类型 各个组件的 资源信息
     */
    public List<ResourceDTO> getAppComponentResource(InstallAppVo<? extends OverrideSpec> vo) {
        OverrideSpec spec = vo.getSpec();
        ResourceDTO resourceDTO = new ResourceDTO();
        resourceDTO.setCpu(spec.getCpu());
        resourceDTO.setMemory(spec.getMemory());
        resourceDTO.setReplicas(spec.getMembers());

        return Collections.singletonList(resourceDTO);
    }

    @Override
    public void update(Integer id, OverrideSpec overrideSpec) throws Exception {
        CloudApp app = appService.get(id);
        ResourceDTO patch = new ResourceDTO();
        patch.setDisk(overrideSpec.getDisk());
        patch.setMemory(overrideSpec.getMemory());
        patch.setCpu(overrideSpec.getCpu());
        patch.setAppId(app.getId());
        patch.setBackupDisk(overrideSpec.getBackupDisk());
        patch.setId(app.getId());
        update(patch);
    }

    @Override
    public void restore(BackupHis backupHis, Integer appId, String restoreTime, String ftpFilename, String backupType) {

    }
}
