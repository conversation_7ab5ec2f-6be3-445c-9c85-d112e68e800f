<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newdt.cloud.mapper.AlertRuleConfigMapper">
  <resultMap id="BaseResultMap" type="cn.newdt.cloud.domain.alert.AlertRuleConfig">
    <id column="id" jdbcType="INTEGER" property="id" />
    <id column="status" jdbcType="INTEGER" property="status" />
    <result column="resource_type" jdbcType="VARCHAR" property="resourceType" />
    <result column="resource_namespace" jdbcType="VARCHAR" property="resourceNamespace" />
    <result column="config_name" jdbcType="VARCHAR" property="configName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="checksum" jdbcType="VARCHAR" property="checksum" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="cn.newdt.cloud.domain.alert.AlertRuleConfig">
    <result column="json" property="json" />
  </resultMap>
  <resultMap id="JoinResultMap" type="cn.newdt.cloud.domain.alert.AlertRuleConfig">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="resource_type" jdbcType="VARCHAR" property="resourceType" />
    <result column="resource_namespace" jdbcType="VARCHAR" property="resourceNamespace" />
    <result column="checksum" jdbcType="VARCHAR" property="checksum" />
    <result column="config_name" jdbcType="VARCHAR" property="configName" />
    <result column="json" jdbcType="VARCHAR" property="json" />
    <collection property="instances" ofType="cn.newdt.cloud.domain.alert.AlertRuleResource" javaType="ArrayList">
      <id column="ai_id" property="id"/>
      <result column="ai_checksum" property="checksum"/>
      <result column="ai_update_time" property="updateTime"/>
      <result column="sync_time" property="syncTime"/>
      <result column="resource_name" property="resourceName"/>
      <result column="resource_id" property="resourceId"/>
      <result column="ai_resource_namespace" property="resourceNamespace"/>
      <result column="ai_resource_type" property="resourceType"/>
      <result column="kube_id" property="kubeId"/>
    </collection>
  </resultMap>
  <sql id="Base_Column_List">
    id, resource_type, resource_namespace, config_name, create_time, update_time, checksum
  </sql>
  <sql id="Blob_Column_List">
    json
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ${schema}.cloud_alert_rule_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByJoin" resultMap="JoinResultMap">
    select
    ar.id, ar.create_time, ar.update_time, ar.checksum, ar.json, ar.status, ar.resource_type, ar.resource_namespace, ar.config_name
    , ai.id as ai_id, ai.checksum as ai_checksum, ai.resource_name, ai.resource_id, ai.resource_type as ai_resource_type
    , ai.resource_namespace as ai_resource_namespace
    , ai.update_time as ai_update_time
    , ai.sync_time, ai.kube_id
    from
    (
      select t.* from ${schema}.cloud_alert_rule_config t
      left join ${schema}.cloud_alert_rule_resource ai on t.id = ai.rule_set_id
      <where>
        <choose>
          <when test="param.status != null">
            t.status &amp; #{param.status} = #{param.status}
          </when>
          <!--  默认查非模板        -->
          <otherwise>
            t.status = 0
          </otherwise>
        </choose>
        <if test="param.name != null">
          and t.config_name like concat('%', #{param.name}, '%')
        </if>
        <if test="param.resourceType != null">
          <!--    use ar.resource_type    -->
          and t.resource_type = #{param.resourceType}
        </if>
        <if test="param.resourceName != null">
          and ai.resource_name = #{param.resourceName}
        </if>
        <if test="param.ownerTenant != null and param.ownerTenant instanceof java.lang.Integer">
          and t.owner_tenant = #{param.ownerTenant}
        </if>
        <if test="param.ownerTenant != null and param.ownerTenant instanceof java.util.Collection">
          and t.owner_tenant in (
          <foreach collection="param.ownerTenant" item="ownerTenant" separator=",">
            #{ownerTenant}
          </foreach>
          )
        </if>
        <if test="param.ownerUser != null">
          and t.owner_user = #{param.ownerUser}
        </if>
      </where>
      group by t.id
      <choose>
        <when test="param.order != null and param.orderCol != null">
          order by t.${param.orderCol} ${param.order}
        </when>
        <otherwise>
          order by t.create_time desc
        </otherwise>
      </choose>
      <if test="param.offset != null and param.pageSize != 0">
        limit ${param.offset}, ${param.pageSize}
      </if>
    )ar
<!--    <choose>-->
<!--      <when test="param.resourceName != null">-->
<!--        join ${schema}.cloud_alert_rule_resource ai on ar.id = ai.rule_set_id-->
<!--        and ai.resource_name = #{param.resourceName}-->
<!--      </when>-->
<!--      <otherwise>-->
<!--      </otherwise>-->
<!--    </choose>-->
    left join ${schema}.cloud_alert_rule_resource ai on ar.id = ai.rule_set_id
  </select>

  <select id="countSelectByJoin" resultType="long">
    select
    count(*)
    from (
    select 1
    from ${schema}.cloud_alert_rule_config ar
    left join ${schema}.cloud_alert_rule_resource ai on ar.id = ai.rule_set_id
    <where>
      <choose>
        <when test="param.status != null">
          ar.status &amp; #{param.status} = #{param.status}
        </when>
        <!--  默认查非模板        -->
        <otherwise>
          ar.status &amp; 1 = 0
        </otherwise>
      </choose>
      <if test="param.name != null">
        and ar.config_name like concat('%', #{param.name}, '%')
      </if>
      <if test="param.resourceType != null">
        and ar.resource_type = #{param.resourceType}
      </if>
      <if test="param.resourceName != null">
        and ai.resource_name = #{param.resourceName}
      </if>
      <if test="param.ownerTenant != null and param.ownerTenant instanceof java.lang.Integer">
        and ar.owner_tenant = #{param.ownerTenant}
      </if>
      <if test="param.ownerTenant != null and param.ownerTenant instanceof java.util.Collection">
        and ar.owner_tenant in (
        <foreach collection="param.ownerTenant" item="ownerTenant" separator=",">
          #{ownerTenant}
        </foreach>
        )
      </if>
      <if test="param.ownerUser != null">
        and ar.owner_user = #{param.ownerUser}
      </if>
    </where>
    group by ar.id
    ) t
  </select>
  <select id="selectByName" resultType="cn.newdt.cloud.domain.alert.AlertRuleConfig">
      select
      <include refid="Base_Column_List" />
      ,
      <include refid="Blob_Column_List" />
      from ${schema}.cloud_alert_rule_config
      where config_name = #{name,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from ${schema}.cloud_alert_rule_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="cn.newdt.cloud.domain.alert.AlertRuleConfig" useGeneratedKeys="true" keyColumn="id" keyProperty="record.id">
    insert into ${schema}.cloud_alert_rule_config (resource_type, resource_namespace,
      create_time, update_time, checksum, config_name, owner_tenant, owner_user,
      json)
    values (#{record.resourceType,jdbcType=VARCHAR}, #{record.resourceNamespace,jdbcType=VARCHAR},
      #{record.createTime,jdbcType=TIMESTAMP}, #{record.updateTime,jdbcType=TIMESTAMP}, #{record.checksum,jdbcType=VARCHAR},
      #{record.configName,jdbcType=VARCHAR}, #{record.ownerTenant,jdbcType=INTEGER}, #{record.ownerUser,jdbcType=INTEGER},
      #{record.json,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="cn.newdt.cloud.domain.alert.AlertRuleConfig">
    insert into ${schema}.cloud_alert_rule_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="record.id != null">
        id,
      </if>
      <if test="record.resourceType != null">
        resource_type,
      </if>
      <if test="record.resourceNamespace != null">
        resource_namespace,
      </if>
      <if test="record.createTime != null">
        create_time,
      </if>
      <if test="record.updateTime != null">
        update_time,
      </if>
      <if test="record.checksum != null">
        checksum,
      </if>
      <if test="record.json != null">
        json,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="record.id != null">
        #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.resourceType != null">
        #{record.resourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceNamespace != null">
        #{record.resourceNamespace,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.checksum != null">
        #{record.checksum,jdbcType=VARCHAR},
      </if>
      <if test="record.json != null">
        #{record.json,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.newdt.cloud.domain.alert.AlertRuleConfig">
    update  ${schema}.cloud_alert_rule_config
    <set>
      <if test="record.resourceType != null">
        resource_type = #{record.resourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceNamespace != null">
        resource_namespace = #{record.resourceNamespace,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.checksum != null">
        checksum = #{record.checksum,jdbcType=VARCHAR},
      </if>
      <if test="record.configName != null">
        config_name = #{record.configName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.json != null">
        json = #{record.json,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{record.id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="cn.newdt.cloud.domain.alert.AlertRuleConfig">
    update cloud_alert_rule_config
    set resource_type = #{resourceType,jdbcType=VARCHAR},
      resource_namespace = #{resourceNamespace,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      checksum = #{checksum,jdbcType=VARCHAR},
      json = #{json,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.newdt.cloud.domain.alert.AlertRuleConfig">
    update cloud_alert_rule_config
    set resource_type = #{resourceType,jdbcType=VARCHAR},
      resource_namespace = #{resourceNamespace,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      checksum = #{checksum,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

</mapper>