package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.ImageKindEnum;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.CloudAppLogic;
import cn.newdt.cloud.domain.ServiceManager;
import cn.newdt.cloud.domain.cr.Kibana;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.dto.ResourceDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.service.sched.impl.KibanaInstallWatch;
import cn.newdt.cloud.service.sched.impl.KibanaSvcWatch;
import cn.newdt.cloud.service.sched.impl.KibanaWatch;
import cn.newdt.cloud.utils.JsonUtil;
import cn.newdt.cloud.utils.PageUtil;
import cn.newdt.cloud.vo.AppInstanceVO;
import cn.newdt.cloud.vo.CloudAppVO;
import cn.newdt.cloud.vo.InstallAppVo;
import cn.newdt.cloud.vo.OverrideSpec;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.exception.CustomException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.api.model.apps.Deployment;
import io.fabric8.kubernetes.client.CustomResource;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.CloudAppConstant.ResourceName.CPU;
import static cn.newdt.cloud.constant.CloudAppConstant.ResourceName.MEMORY;

@Slf4j
@Service
public class KibanaService extends DefaultAppKindService<Kibana> implements ServiceManageOperation {

    @Autowired
    private AppOperationHandler appOperationHandler;
    @Autowired
    private KubeClientService kubeClientService;
    @Autowired
    private CloudAppConfigService appConfigService;
    @Override
    protected void completeInstanceProperty(List<AppInstanceVO> instances, int appId) {
    }

    @Override
    public AppKind getKind() {
        return AppKind.Kibana;
    }

    @Override
    public String getIpReservationTarget() {
        return "";
    }

    /**
     * 主体上就是构造一个cr
     *
     * @param vo  app data
     * @param ips 分配的ip
     * @return
     */
    @Override
    public Kibana doInstall(CloudAppVO vo, List<String> ips) {
        KibanaVO kibanaVO = (KibanaVO) vo;
        Kibana kibana = new Kibana(kibanaVO.getCrName(), kibanaVO.getNamespace(), null);
        Kibana.KibanaSpec kibanaSpec = new Kibana.KibanaSpec();
        String logDeleteDays = sysConfigService.findOne(CloudAppConstant.SysCfgCategory.LOG_CONFIG, CloudAppConstant.SysCfgName.LOG_DELETE_DAYS);
        Integer logDeleteDaysInt = Integer.valueOf(logDeleteDays);
        Map<String, Object> configMap = new HashMap() {{
            put("logging", new HashMap<String, Object>() {{
                put("appenders", new HashMap<String, Object>() {{
                    put("rolling-file", new HashMap<String, Object>() {{
                        put("fileName", "/var/log/kibana.log");
                        put("layout", new HashMap<String, Object>() {{
                            put("type", "pattern");
                        }});
                        put("policy", new HashMap<String, Object>() {{
                            put("interval", "24h");
                            put("modulate", true);
                            put("type", "time-interval");
                        }});
                        put("strategy", new HashMap<String, Object>() {{
                            put("max", logDeleteDaysInt);
                            put("pattern", "-%i");
                            put("type", "numeric");
                        }});
                        put("type", "rolling-file");
                    }});
                }});
            }});
        }};

        kibana.setSpec(kibanaSpec);
        kibanaSpec.setCount(vo.getMembers());
        kibanaSpec.setVersion(kibanaVO.getVersion());
        kibanaSpec.setConfig(configMap);
        Kibana.ElasticsearchRef elasticsearchRef = new Kibana.ElasticsearchRef(kibanaVO.getEsName(),
                kibanaVO.getNamespace());
        kibanaSpec.setElasticsearchRef(elasticsearchRef);
        // add label:es-ref. 方便查询kibana和es的关联关系
        kibana.getMetadata().setLabels(ImmutableMap.of(CloudAppConstant.ES_REF, kibanaVO.getEsName()));

        PodTemplateSpec podTemplate = new PodTemplateSpec();
        ArrayList<Container> containers = new ArrayList<>();
        Container container = new Container();
        // 修改imagePullPolicy为Always
        container.setImagePullPolicy("IfNotPresent");
        container.setName(getKind().getContainerName());
        ArrayList<EnvVar> vars = new ArrayList<>();
        vars.add(new EnvVar("NODE_OPTIONS", "--max-old-space-size=2048", null));
        container.setEnv(vars);

        ResourceRequirements resources = ResourceHelper.getInstance().resourceRequirements(
                ImmutableMap.of(CPU, kibanaVO.getCpu(), MEMORY, kibanaVO.getMemory()));
        container.setResources(resources);

        //获取镜像配置表中的镜像名称
        Map<ImageKindEnum, String> imageManifest = appConfigService.getImageManifest(getKind(), vo.getVersion());
        String image = imageManifest.get(ImageKindEnum.MainImage);
        if (StringUtils.isBlank(image)) {
            throw new CustomException(600, "Kibana应用未找到对应的镜像信息！kubeId：" + kibanaVO.getKubeId() + "镜像版本：" + kibanaVO.getVersion());
        }
        kibanaSpec.setImage(image);
        containers.add(container);
        PodSpec podSpec = new PodSpec();
        podSpec.setContainers(containers);
        podSpec.setTolerations(convertCRTolerations(vo.getToleration(),Toleration.class));
        Affinity affinity = new AffinityBuilder().withNodeAffinity(convertCRNodeAffinity(vo.getSelector(), NodeAffinity.class)).build();
        podSpec.setAffinity(affinity);
        if (vo.getAntiAffinityRequired()){
            // 强制pod反亲和
            PodAffinityTerm podAffinityTerm = new PodAffinityTermBuilder().withLabelSelector(new LabelSelectorBuilder()
                    .withMatchLabels(ImmutableMap.of("kibana.k8s.elastic.co/name", vo.getCrName(),
                            "common.k8s.elastic.co/type", "kibana"))
                    .build()).withTopologyKey("kubernetes.io/hostname").build();
            PodAntiAffinity podAntiAffinity = new PodAntiAffinityBuilder().withRequiredDuringSchedulingIgnoredDuringExecution(Lists.newArrayList(podAffinityTerm)).build();
            affinity.setPodAntiAffinity(podAntiAffinity);
        }

        podTemplate.setSpec(podSpec);
        kibanaSpec.setPodTemplate(podTemplate);

        return kibana;
    }

    @Override
    protected void validateStorageClass(CloudAppVO app) {
        // sentinel 没有存储 不校验
    }

    @Override
    protected boolean supportIPAM(CloudAppVO app) {
        // kibana cr使用calico插件无法固定ip 无须分配IP
        return !CloudAppConstant.K8sCNIType.CALICO.equals(app.getCniType());
    }

    @Override
    @Transactional
    public void update(ResourceDTO patch) throws Exception {
        CloudApp app = appService.get(patch.getId());
        Consumer<Kibana> modifier = cr -> {
            Kibana.KibanaSpec spec = cr.getSpec();
            spec.getPodTemplate().getSpec().getContainers().get(0).getResources().getLimits().put("cpu", new Quantity(patch.getCpu()));
            spec.getPodTemplate().getSpec().getContainers().get(0).getResources().getLimits().put("memory", new Quantity(patch.getMemory()));
        };
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        Kibana cr = kubeClient.listCustomResource(Kibana.class, app.getCrName(), app.getNamespace());
        Map map = storeOldDeploymentRevision(cr, kubeClient, null);
        appOperationHandler.handleUpdate(patch, modifier, this, Kibana.class, null, map);
    }

    @Override
    public boolean nodePolicy() {
        return false;
    }

    // TODO
    @Override
    public Class<? extends OpsPostProcessor> getProcessorClass(ActionEnum action) {
        switch (action) {
            case CREATE:
                return KibanaInstallWatch.class;
            case UPDATE:
                return KibanaWatch.class;
            case SCALE_OUT:
            case SCALE_IN:
            case UPDATE_SERVICE:
            case DELETE_SERVICE:
                return KibanaSvcWatch.class;
            default:
                return super.getProcessorClass(action);
        }
    }

    @Override
    public List<ServiceManager> createService(
            String serviceType, CloudAppVO vo, List<?> serviceResources, CustomResource installCr) {
        return openSourceKindServiceBuilder(serviceType, vo, serviceResources, null);
    }

    @Override
    public void updateService(
            List<ServiceManager> svcMgrs, CloudApp app, Object oldServiceResource) throws Exception {
        openSourceKindUpdateServiceBuilder(svcMgrs, app, oldServiceResource);
    }

    @Data
    public static class KibanaVO extends CloudAppVO {
        private String kibanaRM = "500Mi";
        private String kibanaRC = "250m";
        private String esName;
        private Integer esId;

//        private String kibanaLM;
//        private String kibanaLC;
//        private Integer kibanaPort;
    }

    @Override
    public PageInfo<KibanaVO> searchPage(PageDTO page) {
        PageInfo<? extends CloudAppVO> pageInfo = super.searchPage(page);
        Map<String, Object> map = new HashMap<>();
        map.put("kind", AppKind.Elasticsearch.getKind());
        Map<String, CloudApp> esMap = appService.findNotDeletedList(map).stream()
                .collect(Collectors.toMap(a -> String.format("%s,%s,%s", a.getKubeId(), a.getNamespace(), a.getCrName()), a -> a));


        return PageUtil.page2PageInfo(pageInfo.getList(), KibanaVO.class, cloudAppVO -> {
            KibanaVO kibanaVO = new KibanaVO();
            BeanUtils.copyProperties(cloudAppVO, kibanaVO);
            Kibana unmarshal = YamlEngine.unmarshal(
                    StringUtils.isEmpty(kibanaVO.getCr()) ? kibanaVO.getCrRun() : kibanaVO.getCr(), Kibana.class);
            kibanaVO.setEsName(unmarshal.getSpec().getElasticsearchRef().getName());
            // todo kibana绑定限制相同namespace
            CloudApp esApp = esMap.get(String.format("%s,%s,%s", cloudAppVO.getKubeId(), cloudAppVO.getNamespace(), kibanaVO.getEsName()));
            if (esApp != null) {
                kibanaVO.setEsId(esApp.getId());
            }
            return kibanaVO;
        });
    }

    public int handleWatchResult(Integer appId, boolean success) {
        return appService.handleWatchResult(appId, success);
    }

    @Override
    public CloudAppVO overrideSpec(CloudAppLogic logicApp, Integer kubeId, InstallAppVo<? extends OverrideSpec> vo) {
        CloudAppVO appVO = super.overrideSpec(logicApp, kubeId, vo);
        KibanaVO kibanaVo = new KibanaVO();
        BeanUtils.copyProperties(appVO, kibanaVo);
        if (vo.getOverrideSpecs().get(kubeId) instanceof KibanaOverrideSpec) {
            KibanaOverrideSpec overrideSpec = (KibanaOverrideSpec) vo.getOverrideSpecs().get(kubeId);
            kibanaVo.setEsName(overrideSpec.getEsName());
            kibanaVo.setKibanaRM(overrideSpec.getKibanaRM());
            kibanaVo.setKibanaRC(overrideSpec.getKibanaRC());
        }
        return kibanaVo;
    }

    @Override
    public InstallAppVo<? extends OverrideSpec> parseInstallVo(String data) {
        return JsonUtil.toObject(data, new TypeReference<InstallAppVo<KibanaOverrideSpec>>() {
        });
    }

    @Getter
    @Setter
    @ToString
    public static class KibanaOverrideSpec extends OverrideSpec {
        private String esName;
        private String kibanaRM = "500Mi";
        private String kibanaRC = "250m";
    }

    public String getDeployName(String crName) {
        return crName + "-kb";
    }
    private Map storeOldDeploymentRevision(Kibana cr, KubeClient kubeClient, Map jobData) {
        Deployment deployments = kubeClient.getDeployments(cr.getMetadata().getNamespace(), getDeployName(cr.getMetadata().getName()));
        String revision = deployments != null ? deployments.getMetadata().getResourceVersion() : null;
        if (jobData == null) jobData = new HashMap();
        jobData.put("oldDeploymentRevision", revision);
        return jobData;
    }

    protected String[] getStsOrDeployNames(CloudApp app) {
        return new String[]{getDeployName(app.getCrName())};
    }

    @Override
    public void delete(CloudApp app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        String[] names = getStsOrDeployNames(app);
        if (names == null) throw new RuntimeException("get name kibana deploy name failed");
        for (String name : names) {
            kubeClient.scaleDeploy(app.getNamespace(), name, 0);
        }
    }

    @Override
    public void recreate(CloudApp app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        String[] names = getStsOrDeployNames(app);
        if (names == null) throw new RuntimeException("get name kibana deploy name failed");
        for (String name : names) {
            kubeClient.deleteDeployment(name, app.getNamespace());
        }
    }

    @Override
    public String getAppSystemName(InstallAppVo<? extends OverrideSpec> vo) {
        return appService.getAppByCrName(vo.getCrName(), vo.getNamespace(), AppKind.Elasticsearch).getAppSystemName();
    }
}
