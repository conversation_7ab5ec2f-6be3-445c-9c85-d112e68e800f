package cn.newdt.cloud.web;

import cn.newdt.cloud.domain.CloudSysConfig;
import cn.newdt.cloud.service.impl.SysConfigService;
import cn.newdt.cloud.service.impl.SysConfigSyncService;
import cn.newdt.commons.exception.CustomException;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@RestController
public class SysController {

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private SysConfigSyncService sysConfigSyncService;

    @GetMapping("cloudmanage/sys/config")
    @ApiOperation("获取可修改系统配置")
    public Object getSysConfig() {
        HashMap<String, Object> condition = new HashMap<>();
        condition.put("isUpdate", 1);
        return sysConfigService.findList(condition);
    }

    @GetMapping("cloudnormal/sys/config")
    @ApiOperation("获取普通用户系统配置")
    public Object getSysConfigNormalUser() {
        HashMap<String, Object> condition = new HashMap<>();
        ArrayList<String> categoryList = new ArrayList<>(Arrays.asList("resources", "app.requests"));
        condition.put("categoryList", categoryList);
        return sysConfigService.findList(condition);
    }

    @PutMapping("cloudmanage/sys/config")
    @ApiOperation(value = "修改系统配置", notes = "通过category和name修改系统配置值，有些参数需要重启服务")
    @ApiImplicitParam(name = "sysconfig", value = "系统配置对象", paramType = "body", required = true)
    public void updateSysConfigByCategoryAndName(@RequestBody List<CloudSysConfig> sysconfigList) {
        //对比新旧配置是否修改
        sysconfigList.stream()
                .map(sysConfig -> {
                    String oldSysConfig = sysConfigService.findOne(sysConfig.getCategory(), sysConfig.getName());
                    if (StringUtils.isBlank(oldSysConfig)) {
                        throw new CustomException(600, "修改参数不存在！");
                    }
                    if (!oldSysConfig.equalsIgnoreCase(sysConfig.getData())) {
                        sysConfig.setModified(true);
                    }
                    return sysConfig;
                })
                .collect(Collectors.toList());
        sysConfigSyncService.updateSysConfigByCategoryAndNames(sysconfigList);
    }
}
