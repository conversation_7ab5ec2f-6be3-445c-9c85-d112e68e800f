package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.common.CommonSearch;
import cn.newdt.cloud.config.CloudRequestContext;
import cn.newdt.cloud.config.FutureService;
import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.domain.cr.Broker;
import cn.newdt.cloud.domain.cr.Elasticsearch;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.filter.ResourceView;
import cn.newdt.cloud.mapper.CloudAppMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.CronTriggerMeta;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.csi.CSIUtil;
import cn.newdt.cloud.service.csi.CSInterface;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.AppInstanceVO;
import cn.newdt.cloud.vo.AppYamlVO;
import cn.newdt.cloud.vo.CloudAppVO;
import cn.newdt.cloud.vo.MetricVO;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.bean.UserInfo;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.UserUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.CaseFormat;
import com.google.common.collect.ImmutableMap;
import com.shindata.kafka.v1.Kafka;
import com.shindata.opengauss.v1.OpenGaussClusterStatus;
import com.shindata.redis.v1.Redis;
import io.fabric8.kubernetes.api.model.ConfigMap;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.api.model.coordination.v1.Lease;
import io.fabric8.kubernetes.api.model.metrics.v1beta1.ContainerMetrics;
import io.fabric8.kubernetes.api.model.metrics.v1beta1.PodMetrics;
import io.fabric8.kubernetes.client.CustomResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.quartz.SchedulerException;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.newdt.cloud.constant.DatasourceConstant.*;
import static cn.newdt.cloud.service.impl.KubeConfigServiceImpl.licenseAppKindMap;

@Service
@Slf4j
public class CloudAppServiceImpl implements CloudAppService {
    @Resource
    private CloudAppMapper cloudAppMapper;

    @Autowired
    private KubeClientService clientService;

    @Autowired
    private ResourceChangeHisService resourceChangeHisService;
    @Autowired
    private OpLogService opLogService;

    @Autowired
    private NodeService nodeService;

    @Autowired
    private MetricService metricService;

    @Autowired
    private CloudDatabaseUserService dbUserService;

    @Autowired
    private KubeConfigService kubeConfigService;

    @Autowired
    private CloudAppLogicService cloudAppLogicService;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private TenantService tenantService;
    @Autowired
    private AccessManagementService accessManagementService;

    // 每个请求3+n个并发, 最大支持并发 = (maxinumPoolSize + queueCapacity) / (3+n)
    private ThreadPoolExecutor executor = new ThreadPoolExecutor(2 * Runtime.getRuntime().availableProcessors(),
            25 * Runtime.getRuntime().availableProcessors(),
            60L, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(500));

    @Override
    @ResourceView
    public PageInfo<CloudAppVO> searchPage(PageDTO page) {
        // todo setVo(CloudApp, CustomResource). 接口拆成两个(一个从表里拿，一个从k8s中拿
        // 1. 根据条件筛选cloud_app
        // 2. 每个app 查询cr状态, prometheus监控数据
        if (page.getPageNum() != null && page.getPageSize() != null) {
//            throw new CustomException(600, "分页参数为空"); // searchPage 方法仅用于分页, 否则回有性能问题.
            PageHelper.offsetPage(page.getPageNum(), page.getPageSize());
        }
        if (StringUtils.isNotBlank(page.getPageSort()) && StringUtils.isNotBlank(page.getPageSortProp())) {
            // fix 创建时间排序,设置为表字段名
            if (page.getPageSortProp().equals("createTimestamp")) {
                page.setPageSortProp("createTime");
            }
        } else {
            // 缺省排序
            page.setPageSortProp("createTime");
            page.setPageSort("desc");
        }
        // fix k8sName排序. 为关联表字段设置别名放到select column clause中
        PageHelper.orderBy(CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, page.getPageSortProp()) + " " + page.getPageSort());
        List<CloudApp> pageList = page.getFlags().get("queryDeleted") != null && page.getFlags().get("queryDeleted")
                ? findAllList(page.getCondition()) : findNotDeletedList(page.getCondition());
        // fix 21558
        PageInfo<CloudAppVO> pageInfo = PageUtil.page2PageInfo(pageList, CloudAppVO.class, cloudApp -> {
            CloudAppVO vo = convertToVO(cloudApp);
            try {
                // get owned k8s cluster info
                Integer kubeId = vo.getKubeId();
                KubeConfig kubeConfig = kubeConfigService.get(kubeId);
                String serviceType = kubeConfig.getServiceType();
                vo.setServiceType(serviceType);
                vo.setK8sName(kubeConfig.getName());
                vo.setK8sServer(kubeConfig.getServer());
                // fix 24076 install operation failed but cr create 成功
                vo.setOpsExecutable(checkOpsExecutable(vo.getId()));
                vo.setAllowRollback(checkRollbackFlag(vo.getId()));
                ResourceChangeHis lastAction = getLastestResourceChangeHis(vo.getId());
                vo.setLastAction(ImmutableMap.of("name", lastAction.getCommand() ));
                // 解决dmp broker、es 资源列表从库数量列值不正确
                AppKind kind = AppKind.valueOf(vo.getKind(), vo.getArch());
                if (kind == AppKind.Broker) {
                    String crYaml = StringUtils.isEmpty(vo.getCr()) ? vo.getCrRun() : vo.getCr();
                    Broker broker = YamlEngine.unmarshal(crYaml, Broker.class);
                    // broker.getSpec().getSize() 就是masterSize
                    vo.setSlaveCount(vo.getMembers() - broker.getSpec().getMasterSize());
                } else if (kind == AppKind.Elasticsearch) {
                    String crYaml = StringUtils.isEmpty(vo.getCr()) ? vo.getCrRun() : vo.getCr();
                    Elasticsearch es = YamlEngine.unmarshal(crYaml, Elasticsearch.class);
                    vo.setSlaveCount(es.getSpec().getNodeSets()[1].getCount());
                } else {
                    vo.setSlaveCount(vo.getMembers() - 1);
                }
                vo.setCsiType(CSIUtil.translateSC(vo.getStorageClassName())); // fixme translate NAS
                vo.setBackupCsiType(vo.getBackupStorageclassname());
                //如果是 NodePort 类型则 serviceIps 为 masterIP，如果是 LoadBalancer 的为 externalIp
                List<ServiceManager> serviceManagers = accessManagementService.serviceList(cloudApp.getId());
                vo.setServiceList(serviceManagers);

            } catch (Exception e) {
                log.error(e.getMessage() + vo, e);
            }
            return vo;
        });
        Object queryTime = page.getCondition().get("queryTime");
        long now = Instant.now().toEpochMilli();
        if (queryTime != null) now = Long.parseLong(queryTime + "");
        try {
            CompletableFuture.runAsync(complementK8sInfo(pageInfo.getList()))
                    .thenRunAsync(complementMetricInfo(pageInfo.getList(), now))
                    .join();
        } catch (Exception e) {
            log.error("", e);
        }

        return pageInfo;
    }

    private Runnable complementMetricInfo(List<CloudAppVO> list, long now) {
        return () -> {
            list.stream().collect(Collectors.groupingBy(app -> app.getKubeId()))
                    .keySet().parallelStream().forEach(kubeId -> complementMetricInfo(kubeId, list, now));
        };

    }

    private Runnable complementK8sInfo(List<CloudAppVO> list) {
        return () -> {
            list.stream().collect(Collectors.groupingBy(app -> app.getKubeId()))
                    .keySet().parallelStream().forEach(kubeId -> complementK8sInfo(kubeId, list));
        };
    }

    private ResourceChangeHis getLastestResourceChangeHis(Integer id) {
        return resourceChangeHisService.getLatestByAppId(id);
    }

    private boolean checkRollbackFlag(Integer id) {
        return resourceChangeHisService.checkAllowRollback(id);
    }


    private boolean checkOpsExecutable(Integer id) {
        // checkout install success
        Optional<ResourceChangeHis> his =
                resourceChangeHisService.getByIdAndAction(id, ActionEnum.CREATE, StatusConstant.SUCCESS);
        if (!his.isPresent())
            // 安装失败后删除重建
            return resourceChangeHisService.getByIdAndAction(id, ActionEnum.RECREATE, StatusConstant.SUCCESS).isPresent();
        return true;
    }

    /**
     * 补充appVo中resource, alertStatus等字段
     *
     */
    private void complementPageV2(List<CloudAppVO> list, long now) throws Exception {
//        if (kind != null) {
        // <kubeId, <namespace/name, app>>
        // fixme 首页接口, kind可能为null, 通过namespace/name可能为null
        Map<Integer, List<CloudAppVO>> appMap2 = list.stream()
                .collect(Collectors.groupingBy(app -> app.getKubeId()));
        appMap2.keySet().parallelStream()
                .forEach(kubeId -> {
                    FutureService futureService = new FutureService();
                    futureService.submit(()->complementK8sInfo(kubeId, appMap2.get(kubeId)));
                    futureService.submit(()->complementMetricInfo(kubeId, appMap2.get(kubeId), now));
                    try {
                        futureService.await(10 * 1000);
                    } catch (Exception e) {
                        log.error("complement page", e);
                    }
                });
//        }
    }

    private void complementMetricInfo(Integer kubeId, List<CloudAppVO> appList, long now) {
        // todo 根据label查询pod指标
        // 按namespaces, podNames筛选prometheus指标, 根据指标label汇总
        // 构建pod->app的关系
        Map<Integer, CloudAppVO> appIdMap = appList.stream().collect(Collectors.toMap(app -> app.getId(), app -> app));
        Map<String, CloudAppVO> pod_app_view = appList.parallelStream().map(app -> {
            List<AppInstanceVO> instances = findInstances(app.getId());
//            multiaztodo app.setIpList(JsonUtil.toJson(instances.stream().filter(pod -> !StringUtils.isAnyEmpty(pod.getNode(),pod.getIp()))
//                    .map(pod -> ImmutableMap.of("node", pod.getNode(), "ip", pod.getIp())).collect(Collectors.toList())));
            return instances;
        })
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(i -> i.getNamespace() + "/" + i.getPodName(), i -> appIdMap.get(i.getAppId())));
        Set<String> podNames = pod_app_view.keySet();
        List<CompletableFuture> parallelFutures = new ArrayList<>();
        parallelFutures.addAll(metricQueryFutures(now, pod_app_view, kubeId, podNames));
        CompletableFuture[] completableFutures = new CompletableFuture[parallelFutures.size()];
        CompletableFuture<Void> combineFuture = CompletableFuture.allOf(parallelFutures.toArray(completableFutures));
        combineFuture.join();
//                .forEach(instances -> {
//                    for (AppInstanceVO instance : instances) {
//                        CloudAppVO app = appMap.get(instance.getNamespace() + "/" + instance.getAppName());
//                        app.setCpuUsageRaw(app.getCpuUsageRaw() + instance.getRawCpu());
//                        app.setMemoryUsageRaw(app.getMemoryUsageRaw() + instance.getRawMemory());
//                        app.setDiskUsageRaw(app.getDiskUsageRaw() + instance.getRawDisk());
//                    }
//                });
    }

    private Integer complementK8sInfo(Integer kubeId, List<CloudAppVO> stringCloudAppVOMap) {
        String namespace = null;
        Set<String> archCollect = stringCloudAppVOMap.stream().map(a -> a.getArch()).collect(Collectors.toSet());
        Set<String> kindCollect = stringCloudAppVOMap.stream().map(a -> a.getKind()).collect(Collectors.toSet());
        Arrays.stream(AppKind.values()).filter(k-> kindCollect.contains(k.getKind()) && archCollect.contains(k.getArch()))
                .parallel() // 同一类型不同架构
                .forEach(appKind -> {
                    Map<String, CustomResource> crMap = clientService.get(kubeId).listCustomResource(appKind.getCrClass(), namespace).stream()
                            .collect(Collectors.toMap(cr -> cr.getMetadata().getNamespace() + "/" + cr.getMetadata().getName(), cr -> cr));
                    handleBesidesMySQL(stringCloudAppVOMap, crMap, appKind, kubeId);
                });
        return null;
    }

    private void handleBesidesMySQL(List<CloudAppVO> nameMap, Map<String, CustomResource> crMap, AppKind appKind, Integer kubeId) {
        nameMap.stream()
                .filter(e -> Objects.equals(e.getKubeId(), kubeId) && appKind == AppKind.valueOf(e.getKind(), e.getArch())).forEach(value -> {
                    CustomResource<?, ?> cr = crMap.get(value.getNamespace() + "/" + value.getCrName());
                    if (cr != null) {
                        value.setAlertStatus(appKind.getAlertStatus(cr));
                        // 关闭集群的场景，对status和alertStatus进行转换
//                        if (CloudAppConstant.AppStatus.STOPPED.equals(value.getStatus())){
//                            value.setStatus(CloudAppConstant.AppStatus.SUCCESS);
//                            value.setAlertStatus(CloudAppConstant.AppStatus.STOPPED.toLowerCase());
//                        }
                    }
                    else {
                        value.setAlertStatus("实例资源不存在");
                        value.setCrDeleted(true);
                    }
                });
    }


    private List<CompletableFuture> metricQueryFutures(long now, Map<String, CloudAppVO> podToAppMap, Integer kubeId, Set<String> podNameCollection) {
        List<CompletableFuture> parallelFutures = new ArrayList<>();

        parallelFutures.add(CompletableFuture.supplyAsync(() -> metricService.queryCpuLimitByPods(now, podNameCollection, kubeId), executor)
                .thenAccept(metrics -> metrics.forEach(v -> {
                    CloudAppVO vo = findAppPodBelongs(podToAppMap, v);
                    vo.setCpuRaw(vo.getCpuRaw() + v.getValue());
                })));
        parallelFutures.add(CompletableFuture.supplyAsync(() -> metricService.queryMemoryLimitByPods(now, podNameCollection, kubeId), executor)
                .thenAccept(metrics -> metrics.forEach(v -> {
                    CloudAppVO vo = findAppPodBelongs(podToAppMap, v);
                    vo.setMemoryRaw(vo.getMemoryRaw() + v.getValue());
                })));
        parallelFutures.add(CompletableFuture.supplyAsync(() -> metricService.queryDiskLimitByPods(now, podNameCollection, kubeId), executor)
                .thenAccept(metrics -> metrics.forEach(v -> {
                    CloudAppVO vo = findAppPodBelongs(podToAppMap, v);
                    if (AppUtil.isBackupPVC(vo, v.getPvc())) {
                        vo.setBackupDiskRaw(vo.getBackupDiskRaw() + v.getValue());
                    } else {
                        vo.setDiskRaw(vo.getDiskRaw() + v.getValue());
                    }
                })));
        parallelFutures.add(CompletableFuture.supplyAsync(() -> metricService.queryCpuByPods(now, podNameCollection, kubeId), executor)
                .thenAccept(metrics -> metrics.forEach(v -> {
                    CloudAppVO vo = findAppPodBelongs(podToAppMap, v);
                    vo.setCpuUsageRaw(vo.getCpuUsageRaw() + v.getValue());
                })));
        parallelFutures.add(CompletableFuture.supplyAsync(() -> metricService.queryMemoryByPods(now, podNameCollection, kubeId), executor)
                .thenAccept(metrics -> metrics.forEach(v -> {
                    CloudAppVO vo = findAppPodBelongs(podToAppMap, v);
                    vo.setMemoryUsageRaw(vo.getMemoryUsageRaw() + v.getValue());
                })));
        parallelFutures.add(CompletableFuture.supplyAsync(() -> metricService.queryDiskByPods(now, podNameCollection, kubeId), executor)
                .thenAccept(metrics -> metrics.forEach(v -> {
                    CloudAppVO vo = findAppPodBelongs(podToAppMap, v);
                    if (AppUtil.isBackupPVC(vo, v.getPvc())) {
                        vo.setBackupDiskUsageRaw(vo.getBackupDiskUsageRaw() + v.getValue());
                    } else {
                        vo.setDiskUsageRaw(vo.getDiskUsageRaw() + v.getValue());
                    }
                })));
        return parallelFutures;
    }

    private CloudAppVO findAppPodBelongs(Map<String, CloudAppVO> podToAppMap, MetricVO.ValueVO v) {
        return podToAppMap.get(v.getNamespace() + "/" + v.getPod());
//        // todo 获取podName前缀, 同appKind getPodPattern定义匹配
//        // mysql 192x129x1x1 redis 192-129-1-1
//        String podPrefix = v.getNamespace() + "/" + v.getPod().replaceAll("(\\d+x){3}\\d+$|(\\d+-){3}\\d+$|(?<=-)\\d+$", "");
//        // 针对kibana
//        if (podPrefix.contains("-kb-"))
//            podPrefix = podPrefix.substring(0,podPrefix.indexOf("-kb-") + 4);
//        if(podPrefix.contains("-master-")){
//            String str1 = podPrefix.replaceAll("-master-", "");
//            podPrefix = str1.substring(0, str1.lastIndexOf("-") + 1);
//        }else if(podPrefix.contains("-replica-")){
//            String str1 = podPrefix.replaceAll("-replica-", "");
//            String str2 = str1.substring(0, str1.lastIndexOf("-"));
//            podPrefix = str1.substring(0, str2.lastIndexOf("-") + 1);
//        }
//        return podToAppMap.get(podPrefix);
    }

    /**
     * 获取相关部门下的 crname 应用
     * @param crName
     * @param namespace
     * @param appKind
     * @return
     */
    @Override
    public CloudApp getAppByCrName(String crName, String namespace, AppKind appKind) {
        Map<String, Object> map = new HashMap<>();
        map.put("fullCrName", crName);
        map.put("namespace", namespace);
        map.put("kind", appKind.getKind());
        map.put("arch", appKind.getArch());
        List<CloudApp> resultApps = findNotDeletedList(map);
        return CollectionUtils.isNotEmpty(resultApps) ? resultApps.get(0) : null;
    }

    @Override
    public List<CloudApp> selectByIdIn(List<Integer> idCollect) {
        return cloudAppMapper.getByJoinLogicApp(DatasourceConstant.SCHEMA, CLOUD_APP, DatasourceConstant.CLOUD_APP_LOGIC,
                ImmutableMap.of("appIdList", idCollect)
                );
    }

    @Override
    @ResourceView
    public List<CloudApp> findNotDeletedList(Map<String, Object> map) {
        if (map == null) {
            map = new HashMap<>();
        } else {
            map = new HashMap<>(map);
        }
        map.put("deleted", 0);
        return cloudAppMapper.findAllList(map, DatasourceConstant.SCHEMA, CLOUD_APP, DatasourceConstant.CLOUD_APP_LOGIC,
                DatasourceConstant.CLOUD_KUBE_CONFIG_TABLE, CLOUD_KUBE_SCHEDULER);
    }

    @Override
    @ResourceView
    public List<CloudApp> findNotCompleteDeletedList(Map<String, Object> map) {
        if (map == null) {
            map = Collections.emptyMap();
        }
        List<Integer> notCompleteDeleteds = Arrays.asList(CloudAppConstant.AppDeleteStatus.NORMAL, CloudAppConstant.AppDeleteStatus.RECYCLED);
        map.put("notCompleteDeleteds", notCompleteDeleteds);
        return cloudAppMapper.findAllList(map, DatasourceConstant.SCHEMA, CLOUD_APP, DatasourceConstant.CLOUD_APP_LOGIC,
                DatasourceConstant.CLOUD_KUBE_CONFIG_TABLE, CLOUD_KUBE_SCHEDULER);
    }

    @Override
    public List<CloudApp> findNotDeletedListNoNamespace(Map<String, Object> map) {
        if (map == null) {
            map = Collections.emptyMap();
        }
        map.put("deleted", 0);
        return cloudAppMapper.findAllListNoNamespace(map, DatasourceConstant.SCHEMA, CLOUD_APP, DatasourceConstant.CLOUD_APP_LOGIC,
                DatasourceConstant.CLOUD_KUBE_CONFIG_TABLE, CLOUD_KUBE_SCHEDULER);
    }

    @Override
    public List<CloudApp> findNotDeletedListWithoutAnnotation(Map<String, Object> condition) {
        return findNotDeletedList(condition);
    }

    @Override
    public List<CloudApp> findNotCompleteDeletedListWithoutAnnotation(Map<String, Object> condition) {
        return findNotCompleteDeletedList(condition);
    }

    @Override
    public void callScheduler(CloudApp app, ActionEnum actionEnum, Object extData, Class<?> callbackClass) throws Exception {

        String yaml = null;
        if (actionEnum != ActionEnum.START_POD && actionEnum != ActionEnum.STOP_POD
             && actionEnum != ActionEnum.DELETE) { // 删除操作不更新spec不需要记
            if (StringUtils.isNotEmpty(app.getCrRun())) {
                // app update reach here
                yaml = app.getCrRun();
            } else {
                CustomResource cr = AppUtil.createCrObject(app);
                yaml = YamlEngine.marshal(cr);
            }
        }

        callScheduler(app, yaml, extData, actionEnum, callbackClass);
    }

    @Override
    public Integer callScheduler(CloudApp app, String crRun, Object extData, ActionEnum actionEnum, Class<?> callbackClass) throws SchedulerException, JsonProcessingException {
        return callScheduler(app, crRun, extData, actionEnum, callbackClass, 30);
    }
    @Override
    public Integer callScheduler(CloudApp app, String crRun, Object extData, ActionEnum actionEnum, Class<?> callbackClass, int seconds) throws SchedulerException, JsonProcessingException {
        CustPreconditions.checkState(seconds > 0, "周期不能小于0");
        if (crRun != null)
            crRun = crRun.replaceAll("!!\\S+", "");
        // pre-check
        if (actionEnum != ActionEnum.DELETE)
            appCanToAction(app, actionEnum);
        // set app's cr_run
        app.setStatus(CloudAppConstant.AppStatus.PENDING);
        app.setCrRun(crRun);
        LocalDateTime now = LocalDateTime.now();
        app.setUpdateTime(now);
        update(app);
        // 添加操作记录
        long lastEndTimestamp = getLastUpdateTimestamp(app);

        ResourceChangeHis his = getResourceChangeHis(app, actionEnum, StatusConstant.RUNNING, app.getCrRun(), null);
        his.setLastEndTimestamp(lastEndTimestamp);
        resourceChangeHisService.add(his);
        //放入用户信息用于纳管应用
        UserInfo userInfo = UserUtil.getCurrentUser();
        CronTriggerMeta triggerMeta = new CronTriggerMeta();
        triggerMeta.setCron(String.format("0/%d * * * * ? ", seconds));
        Map<String, String> jobData = new HashMap<>();
        jobData.put("appName", app.getCrName());
        jobData.put("appId", app.getId() + "");
        jobData.put("kubeId", String.valueOf(app.getKubeId()));
        jobData.put("class", callbackClass.getName());
        jobData.put("extDataStr", JsonUtil.toJson(extData));
        jobData.put("userInfo", JsonUtil.toJson(userInfo));
        jobData.put("ThreadId", MDC.get("ThreadId"));
        triggerMeta.setTriggerGroup(app.getName());
        triggerMeta.setTriggerName(actionEnum.getActionType());

        triggerMeta.setJobDataMap(jobData);

        resourceChangeHisService.addSchedule(his, triggerMeta);
        opLogService.appendAndSave(his.getId(), app.getCrName(), his.getAction() + "_" + his.getId());

        return his.getId();
    }


    private long getLastUpdateTimestamp(CloudApp app) {
        AppKind kind = AppKind.valueOf(app.getKind(), app.getArch());
        CustomResource customResource = clientService.get(app.getKubeId()).listCustomResource(kind.getCrClass(), app.getCrName(), app.getNamespace());
        if (customResource != null) {
            Object status = customResource.getStatus();
            if (status != null) {
                Map map = JsonUtil.toMap(status);
                Object str = map.get("lastUpdateTime"); // fixme 仅shindata cr有该属性
                if (str == null) str = map.get("lastUpdatetime");
                if (str == null) str = map.get("lastupdatetime");
                if (str != null) {
                    String lastUpdateTime = (String) str;
                    return LocalDateTime.parse(lastUpdateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).atZone(ZoneId.systemDefault())
                            .toInstant().toEpochMilli();
                }
            }
            return System.currentTimeMillis();
        } else return 0;
    }

    public ResourceChangeHis getResourceChangeHis(CloudApp app, ActionEnum actionEnum, String status, String crRun, String msg) {
        ResourceChangeHis his = new ResourceChangeHis();
        his.setAppId(app.getId());
        his.setAppLogicId(app.getLogicAppId());
        his.setAppName(app.getName());
        his.setKubeId(app.getKubeId());
        his.setNamespace(app.getNamespace());
        his.setKind(app.getKind());
        his.setCommand(actionEnum.getAppOperation());
        his.setYaml(crRun);
        his.setAction(actionEnum.getActionType());
        his.setUserId(UserUtil.getCurrentUser().getUserid());
        his.setUserName(UserUtil.getCurrentUser().getUsername());
        his.setUserIp(CloudRequestContext.getContext().getUserIp());
        his.setMsg(msg);

        CustPreconditions.checkNotNull(his.getCommand(), "command not be null");
        CustPreconditions.checkNotNull(his.getKind(), "kind not be null");
        CustPreconditions.checkNotNull(his.getKubeId(), "kubeId not be null");
        CustPreconditions.checkNotNull(his.getNamespace(), "Namespace not be null");

        Timestamp startTime = new Timestamp(System.currentTimeMillis() + 2000);
        his.setInsertTime(startTime);
        his.setStatus(status);
        his.setLastEndTimestamp(System.currentTimeMillis());
        return his;
    }

    /**
     * 将vo对象放入定时调度
     * @param app
     * @param crRun
     * @param extData
     * @param actionEnum
     * @param callbackClass
     * @throws SchedulerException
     * @throws JsonProcessingException
     */
    public void callScheduler(CloudApp app, String crRun, Object extData, ActionEnum actionEnum, Class<?> callbackClass, CloudAppVO vo) throws SchedulerException, JsonProcessingException {
        String voStr = "";
        if (crRun != null)
            crRun = crRun.replaceAll("!!\\S+", "");
        // pre-check
        if (actionEnum != ActionEnum.DELETE)
            appCanToAction(app.getId());
        // set app's cr_run
        if (actionEnum != ActionEnum.START_POD && actionEnum != ActionEnum.STOP_POD) {
            app.setStatus(CloudAppConstant.AppStatus.PENDING);
            app.setCrRun(crRun);
            LocalDateTime now = LocalDateTime.now();
            app.setUpdateTime(now);
            update(app);
        }
        //放入crrun
        vo.setCrRun(crRun);
        //将vo转换为json串
        voStr = JSONObject.toJSONString(vo);
        //放入用户信息用于纳管应用
        UserInfo userInfo = UserUtil.getCurrentUser();
        // 添加操作记录
        long lastEndTimestamp = System.currentTimeMillis();
        ResourceChangeHis his = getResourceChangeHis(app, actionEnum, StatusConstant.RUNNING, app.getCrRun(), null);
        his.setLastEndTimestamp(lastEndTimestamp);
        if (!ObjectUtils.isEmpty(vo.getOrgId())) {
            his.setDataMap(String.valueOf(vo.getOrgId()));
        }
        resourceChangeHisService.add(his);
        CronTriggerMeta triggerMeta = new CronTriggerMeta();
        triggerMeta.setCron("0/30 * * * * ? ");
        Map<String, String> jobData = new HashMap<>();
        jobData.put("appName", app.getCrName());
        jobData.put("appId", app.getId() + "");
        jobData.put("kubeId", String.valueOf(app.getKubeId()));
        jobData.put("class", callbackClass.getName());
        jobData.put("extDataStr", JsonUtil.toJson(extData));
        jobData.put("vo", voStr);
        jobData.put("userInfo", JsonUtil.toJson(userInfo));
        jobData.put("backupHisId", vo.getBackupHisId());
        triggerMeta.setTriggerGroup(app.getCrName());
        triggerMeta.setTriggerName(actionEnum.getActionType());
        triggerMeta.setJobDataMap(jobData);

        resourceChangeHisService.addSchedule(his, triggerMeta);
    }

    /**
     * 重载创建定时调度方法，新增参数操作记录，可使用已经存在的操作记录创建定时任务
     * @param app
     * @param crRun
     * @param extData
     * @param actionEnum
     * @param callbackClass
     * @param resourceChangeHis
     * @throws SchedulerException
     * @throws JsonProcessingException
     */
    public void callScheduler(CloudApp app, String crRun, Object extData, ActionEnum actionEnum, Class<?> callbackClass, ResourceChangeHis resourceChangeHis) throws SchedulerException, JsonProcessingException {
        crRun = crRun.replaceAll("!!\\S+", "");
        // pre-check
//        appCanToAction(app, actionEnum);
        // set app's cr_run
        if (actionEnum != ActionEnum.START_POD && actionEnum != ActionEnum.STOP_POD) {
            app.setStatus(CloudAppConstant.AppStatus.PENDING);
            app.setCrRun(crRun);
            LocalDateTime now = LocalDateTime.now();
            app.setUpdateTime(now);
            update(app);
        }
        // 调度
        //判断是否传入了操作记录，没有则创建
        ResourceChangeHis his = null;
        if(null == resourceChangeHis){
            his = newRch(app, actionEnum);
        }else{
            his = resourceChangeHis;
        }

        CronTriggerMeta triggerMeta = new CronTriggerMeta();
        triggerMeta.setCron("0/30 * * * * ? ");
        Map<String, String> jobData = new HashMap<>();
        jobData.put("appName", app.getCrName());
        jobData.put("appId", app.getId() + "");
        jobData.put("kubeId", String.valueOf(app.getKubeId()));
        jobData.put("class", callbackClass.getName());
        jobData.put("extDataStr", JsonUtil.toJson(extData));
        UserInfo userInfo = UserUtil.getCurrentUser() == null?UserUtil.getAsyncUserinfo():UserUtil.getCurrentUser();
        jobData.put("userInfo", JsonUtil.toJson(userInfo));

        triggerMeta.setTriggerGroup(app.getName());
        triggerMeta.setTriggerName(actionEnum.getActionType());
        triggerMeta.setJobDataMap(jobData);

        resourceChangeHisService.addSchedule(his, triggerMeta);
    }

    @Override
    public <T extends CustomResource> T getCustomResource(String crName, String namespace, Integer kubeId, Class<T> customResourceType) {
        return clientService.get(kubeId).listCustomResource(customResourceType, crName, namespace);
    }
    @Override
    public CloudApp findOne(Map<String, Object> map) {
        List<CloudApp> list = findNotDeletedList(map);
        if (list.isEmpty()) {
            return null;
        }
        if (list.size() > 1) {
            throw new IllegalStateException("expect one but found " + list.size());
        }
        return list.get(0);

    }

    /**
     * 根据kubeId获取到所有备份信息
     * @param kubeId
     * @param namespace
     * @param dbType
     * @return
     */
    @Override
    public List<BackupHis> getBackupHisByKubeId(int kubeId,String namespace,String dbType) {
        List<BackupHis> backupFileList = cloudAppMapper.getBackupHisByKubeId(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_HIS, CLOUD_APP, kubeId,namespace,dbType);
        return backupFileList;
    }

    @Override
    public void validate(CloudAppVO vo) {

    }

    @Override
    public CSInterface getCSI(Integer kubeId, String kind) {
        return kubeConfigService.getCSInterface(kubeId, kind);
    }

    @Override
    public OpenGaussClusterStatus getOGCrStatus(int appId) {
        CloudApp app = get(appId);
        return clientService.get(app.getKubeId()).getOGCrStatus(app.getCrName(), app.getNamespace());
    }

    @Override
    public int setCrRunNull(CloudApp update) {
        update.setUpdateTime(LocalDateTime.now());
        return cloudAppMapper.updateCrRunById(update, DatasourceConstant.SCHEMA, CLOUD_APP);
    }

    @ResourceView
    public Map<String, Object> countApp(Map<String, Object> map) {
        List<CloudAppVO> list = findNotDeletedList(map).stream().map(this::convertToVO)
                .collect(Collectors.toList());
        try {
            // complete with alertstatus
            CompletableFuture.runAsync(complementK8sInfo(list))
                    .join();
        } catch (Exception e) {
            log.error("", e);
        }

        Stream<CloudAppVO> stream = list.stream();
        String licenseType = UserUtil.getCurrentUser().getDbtypes();
        // filter with license
        if (licenseType != null) {
            // find all AppKind in license
            Set<AppKind> appKindsInLicense = Arrays.stream(licenseType.split(","))
                    .filter(type -> licenseAppKindMap.containsKey(type.toUpperCase()))
                    .flatMap(type -> licenseAppKindMap.get(type.toUpperCase()).stream())
                    .collect(Collectors.toSet());
            stream = stream
                    .filter(app -> {
                        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
                        // 不统计关联应用，只统计kind=product的应用 过滤 namesvr zk kibana sentinel
                        return appKindsInLicense.contains(appKind);
                    });
            // 对 stream 按类型和状态分组
            Map<String, Map<String, Long>> kindGroup =
                    stream.collect(Collectors.groupingBy(app -> app.getKind(), // 只统计数据库类型
                            Collectors.groupingBy(app -> app.getHealth().getHealth(), Collectors.counting())));

            // response sample: { total: 99, kind_of_apps: [ kind_count ]}
            List<Map<String, Object>> kindCountList = kindGroup.keySet().stream().map(kind -> {
                // kind_count= { kind: mysql, green: 1, yellow: 9, total: 10 }
                Map<String, Object> kindCount = new HashMap<>(kindGroup.get(kind));
                kindCount.put("total", kindGroup.get(kind).values().stream().mapToLong(l -> l).sum());
                kindCount.put("kind", AppKind.valueOfKind(kind).stream().findFirst().map(ak -> ak.getProduct()).orElse(kind));
                return kindCount;
            }).collect(Collectors.toList());

            // license 中尚未创建应用的类型填充
//            List<Map<String, Object>> notInstalledProductPadding = appKindsInLicense.stream()
//                    .filter(appKind -> !kindGroup.containsKey(appKind.getKind()))
//                    .map(AppKind::getProduct).distinct()
//                    .map(product -> ImmutableMap.<String, Object>of("kind", product, "total", 0))
//                    .collect(Collectors.toList());
//            kindCountList.addAll(notInstalledProductPadding);

            return ImmutableMap.<String, Object>builder()
                    .put("total", kindCountList.stream().mapToLong(kc -> (long) kc.get("total")).sum())
                    .put("count_by_kind", kindCountList).build();
        }
        throw new CustomException(600, "license of products is empty");
    }

    @Override
    public Object queryMetricLine(int id, long start, long end, String metric, int step, String view, String podName) {

        Object data;
        if ("dcp".equalsIgnoreCase(view)) {
            return getAppByLogicId(id).stream().findAny().map(app -> {
                return metricService.queryAppReport(app, start, end, step, metric, podName);
            }).orElse(null);
        }
        if (StringUtils.isNotEmpty(view) && view.equalsIgnoreCase("logic")) {
            data = getAppByLogicId(id).stream().flatMap(app -> {
                List list = (List) metricService.queryRangeOfAppPerPod(app, start, end, step, metric);
                return list.stream();
            }).collect(Collectors.toList());
        } else {
            CloudApp app = get(id);
            data = metricService.queryRangeOfAppPerPod(app, start, end, step, metric);
        }
        switch (metric) {
            case "cpu":
                return MetricUtil.formatChartData(data, "cpu");
            case "memory":
                return MetricUtil.formatChartData(data, "内存");
            case "disk":
                return MetricUtil.formatChartData(data, "存储");
            case "backupDisk":
                return MetricUtil.formatChartData(data, "存储");
            default:
                return null;
        }
//        Object data;
//        if (StringUtils.isNotEmpty(view) && view.equalsIgnoreCase("logic")) {
//            data = getAppByLogicId(id).stream().map(app ->
//                            metricService.queryRangeById(app.getId(), start, end, metric, app.getKubeId(), app.getKind(), step))
//                    .reduce(new TreeMap<>(), (acc, cur) -> {
//                        Map curMap = (Map) cur;
//                        if (cur == null) return acc;
//                        for (Object o : curMap.keySet()) {
//                            ((Map) acc).compute(o, (k, v) -> {
//                                if (v == null) {
//                                    return curMap.get(o);
//                                } else {
//                                    return Double.parseDouble(v + "") + Double.parseDouble(curMap.get(o) + "");
//                                }
//                            });
//                        }
//                        return acc;
//                    });
//        } else {
//            CloudApp app = get(id);
//            data = metricService.queryRangeById(id, start, end, metric, app.getKubeId(), app.getKind(), step);
//        }
//        switch (metric) {
//            case "cpu":
//                return MetricUtil.formatChartData(data, "cpu");
//            case "memory":
//                return MetricUtil.formatChartData(data, "内存");
//            case "disk":
//                return MetricUtil.formatChartData(data, "存储");
//            case "backupDisk":
//                return MetricUtil.formatChartData(data, "存储");
//            default:
//                return null;
//        }
    }

    @Override
    public Object queryInstanceMetricLine(int id, String podName, long start, long end, String metric, int step) {
        CloudApp app = get(id);
        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
        Object data = metricService.queryRangeOfPodPerContainer(app, app.getKubeId(), app.getNamespace() + "/" + podName, start, end, step, metric, appKind);
        switch (metric) {
            case "cpu":
                return MetricUtil.formatChartData(data, "cpu");
            case "memory":
                return MetricUtil.formatChartData(data, "内存");
            case "disk":
                return MetricUtil.formatChartData(data, "数据存储");
            case "backupDisk":
                return MetricUtil.formatChartData(data, "备份存储");
            default:
                return null;
        }
    }

    @Override
    public void deleteUser(CloudApp app) {
        dbUserService.deleteAppUser(app.getId());
    }

    @Override
    public Map<String, List<String>> getAppKindAndArch(Integer kubeId) {
        List<CloudApp> list = cloudAppMapper.groupKindAndArch(kubeId, DatasourceConstant.SCHEMA, CLOUD_APP);
        Map<String, List<String>> collect = list.stream().collect(Collectors.groupingBy(app -> app.getKind(), Collectors.mapping(app -> app.getArch(), Collectors.toList())));
        return collect;
    }

    @Override
    public <T extends CustomResource> T getCustomResource(CloudApp app, Class<T> crClass) {
        return clientService.get(app.getKubeId()).listCustomResource(crClass, app.getCrName(), app.getNamespace());
    }

    @Override
    public CloudAppVO getVO(int id) {
        CloudApp app = get(id);
        CloudAppVO vo = convertToVO(app);
        List<CloudDatabaseUser> dbUser = dbUserService.findDbUser(id, CloudAppConstant.UserRole.ADMIN);
        if (!dbUser.isEmpty()) {
            CloudDatabaseUser user = dbUser.get(0);
            vo.setUsername(user.getUsername());
            vo.setPassword(user.getPassword());
        }
        return vo;
    }

    public CloudAppVO convertToVO(CloudApp app) {
        CloudAppVO vo = new CloudAppVO();
        BeanUtils.copyProperties(app, vo);

//        vo.setKubeServer(IPUtil.getIpFromUrl(kubeService.getServer(vo.getKubeId())));
        return vo;
    }

    @Override
    public String getCniType(Integer kubeId) {
        return kubeConfigService.getCNIType(kubeId);
    }

    @Override
    public void processAppName(CloudApp app) {
        String candidateName = HanUtil.toPinyin(app.getName()).toLowerCase();
        // crname 为name的拼音化, 符合DNS_SUBDOMAIN规范 todo 校验podname 满足k8s规则
        // see https://kubernetes.io/docs/concepts/overview/working-with-objects/names/
        // operator使用crname拼接字符串作为service name. 拼接后符合RFC 1035 Label Names规范
        String svcName = AppKind.valueOf(app.getKind(), app.getArch()).getWriteServiceName(candidateName, "");
        if (null != svcName && !Pattern.compile("^(?![-0-9])[a-z-0-9]{1,63}(?<!-)$").matcher(svcName).find()) {
            throw new IllegalArgumentException("service name: " + svcName
                    + "格式不正确, 应以字母开头, 字母或数字结尾, 允许字母数字和横线-, 且长度≤63");
        }
        app.setCrName(generateCrName(app, candidateName));
    }

    @Override
    public String generateCrName(AppMetadata app, String candidateName) {
        if (candidateName == null) candidateName = HanUtil.toPinyin(app.getName()).toLowerCase();
        List<CloudApp> list;
        // 多音字检查
        Map<String, Object> criteria = new HashMap<>();
        criteria.put("fullCrName", candidateName);
        criteria.put("kubeId", app.getKubeId());
        criteria.put("namespace", app.getNamespace());
        criteria.put("kind", app.getKind());
        list = findNotDeletedList(criteria);
        String generatedCrName = candidateName;
        if (!list.isEmpty()) { // cr 存在多音字造成crname同名
            criteria = new HashMap<>();
            criteria.put("kubeId", app.getKubeId());
            criteria.put("crName", candidateName); // 模糊查询
            criteria.put("namespace", app.getNamespace());
            criteria.put("kind", app.getKind());

            Set<String> collect = findNotDeletedList(criteria).stream().map(CloudApp::getCrName).collect(Collectors.toSet());
            do {
                // 拼接随机字符串
                generatedCrName = candidateName + "-" + RandomStringUtils.random(4, true, true).toLowerCase(); //

            } while (collect.contains(generatedCrName));
        }
        return generatedCrName;
    }

    @Override
    public AppInstanceVO findInstance(Integer appId, String podName) {
        // 1.查询app
        CloudApp app = get(appId);

        // 2.根据podName判断实例类型
        String instanceType = CloudAppConstant.DMP.CLUSTER_TYPE_DB;
        if(CloudAppConstant.Kind.MONGODB.equalsIgnoreCase(app.getKind()) && CloudAppConstant.Arch.CLUSTER.equalsIgnoreCase(app.getArch())){
            if(-1 != podName.indexOf("-" + CloudAppConstant.MongoDB.SHARD + "-")){
                instanceType = CloudAppConstant.DMP.CLUSTER_TYPE_DB;
            }else if(-1 != podName.indexOf("-" + CloudAppConstant.MongoDB.CONFIG + "-")){
                instanceType = CloudAppConstant.MongoDB.CONFIG.toUpperCase();
            }else if(-1 != podName.indexOf("-" + CloudAppConstant.MongoDB.ROUTER + "-")){
                instanceType = CloudAppConstant.MongoDB.ROUTER.toUpperCase();
            }else{
                throw new CustomException(600, "未获取到MongoDB-Cluster的实例类型！");
            }
            instanceType = instanceType.toUpperCase();
        }else if(CloudAppConstant.Kind.ELASTICSEARCH.equalsIgnoreCase(app.getKind())){
            if(-1 != podName.indexOf("-" + CloudAppConstant.ElasticSearch.MASTER + "s-")){
                instanceType = CloudAppConstant.ElasticSearch.MASTER.toUpperCase();
            }else if(-1 != podName.indexOf("-" + CloudAppConstant.ElasticSearch.DATA + "-")){
                instanceType = CloudAppConstant.ElasticSearch.DATA.toUpperCase();
            }else{
                throw new CustomException(600, "未获取到Elasticsearch的实例类型！");
            }
        } else if (CloudAppConstant.Kind.TIDB.equalsIgnoreCase(app.getKind())) {
            if (-1 != podName.indexOf("-" + ComponentKindEnum.PD.name().toLowerCase() + "-")) {
                instanceType = ComponentKindEnum.PD.name();
            } else if (-1 != podName.indexOf("-" + ComponentKindEnum.TIDB.name().toLowerCase() + "-")) {
                instanceType = ComponentKindEnum.TIDB.name();
            } else if (-1 != podName.indexOf("-" + ComponentKindEnum.TIKV.name().toLowerCase() + "-")) {
                instanceType = ComponentKindEnum.TIKV.name();
            } else {
                throw new CustomException(600, "未获取到TiDB的实例类型！");
            }
        }

        // 3.返回信息
        AppInstanceVO appInstanceVO = new AppInstanceVO(){{
            setKubeId(app.getKubeId());
            setAppId(app.getId());
            setAppName(app.getName());
            setArch(app.getArch());
            setAppKind(app.getKind());
            setPodName(podName);
            setNamespace(app.getNamespace());
        }};
        appInstanceVO.setComponentKind(instanceType);
        return appInstanceVO;
    }

    private List<AppInstanceVO> findInstances(CloudApp app, List<PodDTO> pods) {
        List<AppInstanceVO> res = new ArrayList<>();
        for (PodDTO pod : pods) {
            AppInstanceVO vo = podToCloudAppInstance(app, pod);
            vo.setAppId(app.getId());
            vo.setAppName(app.getCrName());
            vo.setAppKind(app.getKind());
            vo.setArch(app.getArch());
            vo.setNamespace(app.getNamespace());
            vo.setKubeId(app.getKubeId());
            vo.setZoneRole(app.getRole());
            res.add(vo);
        }

        return res;
    }

    @Override
    public List<AppInstanceVO> findInstances(Integer appId) {

        CloudApp app = get(appId);//.orElseThrow(() -> new IllegalArgumentException("can not find instance of app: appId -" + appId));
        try {
            List<PodDTO> pods = clientService.get(app.getKubeId()).listPod(app.getNamespace(), AppKind.valueOf(app.getKind(), app.getArch()).labelOfPod(app));
            return findInstances(app, pods);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<AppInstanceVO> findInstancesMetrics(Integer appId) {
        CloudApp app = find(appId).orElseThrow(() -> new IllegalArgumentException("can not find instance of app: appId -" + appId));
        List<PodDTO> pods = clientService.get(app.getKubeId()).listPod(app.getNamespace(), AppKind.valueOf(app.getKind(), app.getArch()).labelOfPod(app));

        return findInstancesMetrics(appId, pods);
    }

    @Override
    public List<AppInstanceVO> findInstancesMetrics(Integer appId, List<PodDTO> pods) {
        CloudApp app = get(appId);
        List<AppInstanceVO> vos = findInstances(app, pods);
        try {
            // query memory usage
            AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
            // todo refactor
            List<PodDTO> podsMetric = clientService.get(app.getKubeId()).listPodMetrics(app.getNamespace(), appKind.labelOfPod(app));

            Map<String, PodDTO> collect1 = podsMetric.stream().collect(Collectors.toMap(p -> p.getPod().getMetadata().getName(), p -> p));
            Set<String> podNames = pods.stream().map(pod -> pod.getPodNameWithNamespace()).collect(Collectors.toSet());
            // query disk usage
            List<MetricVO.ValueVO> volumeMetrics = new ArrayList<>();
            try {
                volumeMetrics = metricService.queryDiskByPods(Instant.now().toEpochMilli(), podNames, app.getKubeId());
            } catch (Exception e) {
                log.error("查询pod存储容量错误", e);
            }
            Map<String, Double> collect = volumeMetrics.stream().collect(Collectors.groupingBy(v -> v.getPod(), Collectors.summingDouble(v -> v.getValue())));
            // 组合
            vos = vos.stream().map(vo -> {
                AppInstanceVO temp = podToCloudAppInstance(app, collect1.get(vo.getPodName()));
                if (collect.get(vo.getPodName()) != null) {
                    long bytes = collect.get(vo.getPodName()).longValue();
                    vo.setRawDisk(bytes);
                    vo.setDisk(MetricUtil.getLongValueOfUnit(bytes, 'G'));
                }
                vo.setCpu(temp.getCpu());
                vo.setRawCpu(temp.getRawCpu());
                vo.setMemory(temp.getMemory());
                vo.setRawMemory(temp.getRawMemory());
                return vo;
            }).collect(Collectors.toList());
            return vos;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return vos;
    }


    @Override
    public AppInstanceVO podToCloudAppInstance(CloudApp app, PodDTO pod) {
        AppInstanceVO vo = new AppInstanceVO();

        String podName = pod.getPodName();
        vo.setPodName(podName);
//        vo.setRole(pod.getPod().getMetadata().getLabels().get("role"));
        vo.setIp(pod.getPodIp());
        vo.setPort(AppUtil.getPort(app) + "");
//        vo.setPodDTO(pod);
        if (pod.getMetrics() != null) {
            // 聚合pod内所用容器的metrics
            String kind = app.getKind();
            String arch = app.getArch();
            String containerName = AppUtil.getContainerName(podName, kind, arch);
            if (containerName != null && pod.getMetrics().get(containerName) != null) {
                Quantity cpu = pod.getMetrics().get(containerName).getUsage().get("cpu");
                Double c = MetricUtil.getCpuTimeValueOfUnit(cpu.toString(), 'c');
                vo.setCpu(new DecimalFormat("#.##").format(c) + "c");
                vo.setRawCpu(c * 1000);
                long memory = Long.parseLong(pod.getMetrics().get(containerName).getUsage().get("memory").getAmount());
                vo.setRawMemory(1024 * memory);
                vo.setMemory(MetricUtil.getLongValueOfUnit(vo.getRawMemory(), 'G'));
            }
            // TODO: 同一个app的实例列表包含不同组件，不同组件容器名不同
        }
        vo.setStartTime(pod.getPod().getStatus().getStartTime()); // fixme container's starttime
        if (vo.getStartTime() != null)
            vo.setPeriod(DateUtil.formatDuration(Duration.between(vo.getStartTime(), LocalDateTime.now())));
        vo.setStatus(pod.getStatus());
        //新增节点名称
        vo.setNode(pod.getPod().getSpec().getNodeName());
        vo.setLabels(pod.getLabels());
        vo.setContainerNameList(pod.getContainers());
        vo.setComponentKind(pod.getLabel(CloudAppConstant.CustomLabels.APP_COMPONENT));
        return vo;
    }

    @Override
    public CloudApp get(Integer id) {
        return cloudAppMapper.selectByPrimaryKey(id, DatasourceConstant.SCHEMA, CLOUD_APP);
    }

    @Override
    public Optional<CloudApp> find(Integer id) {
        CloudApp app = cloudAppMapper.selectByPrimaryKey(id, DatasourceConstant.SCHEMA, CLOUD_APP);
        return Optional.ofNullable(app).filter(a -> Boolean.FALSE == a.getDeleted());
    }

    @Override
    @Transactional
    public Integer add(CloudApp app) {
        LocalDateTime now = LocalDateTime.now();
        app.setCreateTime(now);
        app.setUpdateTime(now);
        app.setDeleted(false);
        if (StringUtils.isEmpty(app.getStatus())) {
            app.setStatus(CloudAppConstant.AppStatus.PENDING);
        }
        app.setDeleted(false);
        cloudAppMapper.insert(app, DatasourceConstant.SCHEMA, CLOUD_APP);
        return app.getId();
    }

    @Override
    public Integer delete(Integer id) {
        return cloudAppMapper.deleteByPrimaryKey(id, DatasourceConstant.SCHEMA, CLOUD_APP);
    }

    @Override
    public Integer update(CloudApp data) {
        LocalDateTime now = LocalDateTime.now();
        data.setUpdateTime(now);
        return cloudAppMapper.updateByPrimaryKeySelective(data, DatasourceConstant.SCHEMA, CLOUD_APP);
    }

    private CloudApp preHandleOps(CloudApp app, ActionEnum actionEnum) {
        appCanToAction(app.getId());
//        CustPreconditions.checkState(appCanToAction(app.getId()), "应用最近还有执行中的操作，请稍候再试");

        // pod 启停不需要更新pending以及cr
        if (actionEnum != ActionEnum.START_POD && actionEnum != ActionEnum.STOP_POD) {
            String yaml;
            if (app.getCrRun() != null) {
                // app update reach here
                yaml = app.getCrRun();
            } else {
                if (app.getCr() != null) {
                    // app delete
                    yaml = app.getCr();
                } else {
                    // app install
                    CustomResource cr = AppUtil.createCrObject(app);
                    yaml = YamlEngine.marshal(cr);
                    yaml = yaml.replaceAll("!!\\S+", "");
                }
            }
            app.setStatus(CloudAppConstant.AppStatus.PENDING);
            app.setCrRun(yaml);
            LocalDateTime now = LocalDateTime.now();
            app.setUpdateTime(now);
            update(app);
        }

        return app;
    }

    @Override
    public int handleWatchResult(int appId, boolean success) {
        log.info("handle watch result appId:{}, success: {}", appId, success);
        CloudApp update = get(appId);
        int isDeleted = update.getIsDeleted();

        String status;
        if (success) {
            status = CloudAppConstant.AppStatus.SUCCESS;
            update = convertCrYamlToCloudApp(appId);
            update.setIsDeleted(isDeleted); // 避免被零值覆盖
        } else {
            status = CloudAppConstant.AppStatus.FAILED;
            // 失败回滚 unnecessary
        }
        update.setStatus(status);
        update.setUpdateTime(LocalDateTime.now());
        CloudApp.IpNode[] ipNode = update.getIpNode();
        if (ipNode != null && ipNode.length > 0) update.setMembers(ipNode.length);
        // todo set ip list for mongo
        return update(update);
    }

    @Override
    public CloudApp convertCrYamlToCloudApp(int appId) {
        CloudApp update = get(appId);

        String crRunUpdate = update.getCrRun();
        // upgrade app to expected according to cr_run
        if (!"".equals(crRunUpdate)) {
            // fixme convertToApp返回新的实例，如果字段类型有零值会覆盖实际数据
            update = AppUtil.convertToApp(crRunUpdate, update.getKind(), update.getArch());
            update.setCr(crRunUpdate);
            update.setCrRun("");
            update.setId(appId);
        }
        CloudApp.IpNode[] ipNode = update.getIpNode();
        if (ipNode != null && ipNode.length > 0) update.setMembers(ipNode.length);
        return update;
    }

    @Override
    public boolean appCanToAction(CloudApp cloudApp, ActionEnum actionEnum) {
        // 停止情况下,仅可启动操作
        ResourceChangeHis latestRcs = resourceChangeHisService.getLatestByAppId(cloudApp.getId());
        if (latestRcs == null) return true;
       // if ("Backup".equalsIgnoreCase(latestRcs.getAction()) || "Restore".equalsIgnoreCase(latestRcs.getAction())) return true;
        // 启停操作可以重新触发 todo 只能串行提交
       ActionEnum latestAction = ActionEnum.actionTypeOf(latestRcs.getAction());
//        EnumSet<ActionEnum> set = EnumSet.of(ActionEnum.START, ActionEnum.STOP, ActionEnum.START_POD, ActionEnum.STOP_POD);
//        if (set.contains(latestAction) && latestAction == actionEnum) {
//            return true;
//        }
        //根据备份文件创建应用，因此会出现正在创建，然后还会进行备份操作
        if (ActionEnum.RESTORE.equals(actionEnum) && ActionEnum.CREATE.equals(latestAction)) return true;
        CustPreconditions.checkState(!StatusConstant.RUNNING.equals(latestRcs.getStatus()), "当前应用正在执行 " + latestRcs.getCommand() + " 操作，请稍候再试");
        CustPreconditions.checkState(!StatusConstant.ROLLBACK_IN_PROGRESS.equals(latestRcs.getStatus()),
                "当前应用正在执行 " + latestRcs.getCommand() + " 回滚操作，请稍候再试");
        CustPreconditions.checkState(!cloudApp.getStatus().equals(CloudAppConstant.AppStatus.STOPPED)
                || EnumSet.of(ActionEnum.START, ActionEnum.START_POD).contains(actionEnum), "集群已停止，不能执行" + actionEnum.getAppOperation() + "操作");
        return true;
    }

    @Override
    public void appCanToAction(Integer appId) {

        ResourceChangeHis latestRcs = resourceChangeHisService.getLatestByAppId(appId);
        //取消了
        if (latestRcs != null && !"Backup".equalsIgnoreCase(latestRcs.getAction()) && !"Restore".equalsIgnoreCase(latestRcs.getAction())) {
            // 非执行中可操作应用
            CustPreconditions.checkState(!StatusConstant.RUNNING.equals(latestRcs.getStatus()), "当前应用正在执行 " + latestRcs.getCommand() + " 操作，请稍候再试");
        }
    }

    @Override
    public PageInfo<CloudApp> listPage(PageDTO pageDTO) {
        return null;
    }

//    @Override
//    @Transactional
//    public String clusterScaleOut(Integer appId, List<CloudApp.IpNode> outIpNodeList) throws Exception {
//        CloudApp app = cloudAppMapper.selectByPrimaryKey(appId, DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_APP);
//        JSONArray jsonArray = JSON.parseArray(app.getIpList());
//        String clusterName = app.getCrName();
//        List<String> podNameList = new ArrayList<>();
//        for (CloudApp.IpNode ipNode : outIpNodeList) {
//            JSONObject jsonObject = new JSONObject();
//            jsonObject.put("node", ipNode.getNode());
//            jsonObject.put("ip", ipNode.getIp());
//            jsonArray.add(jsonObject);
//            podNameList.add(getPodName(clusterName, ipNode.getIp()));
//        }
//        app.setIpList(jsonArray.toJSONString());
//        log.info("扩容后ipList:" + jsonArray.toJSONString());
//
//        // 预留ip成功标识
//        boolean flag = true;
//        for (int i = 0; i < outIpNodeList.size(); i++) {
//            flag = networkService.reserveIp("default", "default", podNameList.get(i),
//                    outIpNodeList.get(i).getIp());
//            if (!flag) {
//                return "扩容失败";
//            }
//        }
//        if (update(app) > 0) {
//            //MysqlHaSync mysqlCR = convertToMySqlCr(app);
//            //clientService.get().updateCustomResource(mysqlCR, MysqlHaSync.class);
//            Map map = new HashMap();
//            map.put("app", app);
//            map.put("oldIpNodeList", app.getIpList());
//            map.put("type", ActionEnum.SCALE_OUT);
//            map.put("outIpNodeList", JSON.toJSONString(outIpNodeList));
//            callScheduler(app, ActionEnum.SCALE_OUT, map, AppScaleOutWatch.class);
//            clientService.get().updateCustomResource(convertToMySqlCr(app), MysqlHaSync.class, app.getNamespace());
//            return "提交成功";
//        } else {
//            return "扩容失败";
//        }
//    }

    /**
     * 创建一个新的ResourceChangeHis对象
     * @param app
     * @param actionEnum
     * @return
     */
    private ResourceChangeHis newRch(CloudApp app, ActionEnum actionEnum){
        ResourceChangeHis his = new ResourceChangeHis();
        his.setAppId(app.getId());
        his.setAppName(app.getName());
        his.setKubeId(app.getKubeId());
        his.setNamespace(app.getNamespace());
        his.setKind(app.getKind());
        his.setCommand(actionEnum.getAppOperation());
        his.setYaml(app.getCrRun());
        his.setAction(actionEnum.getActionType());
        his.setUserName(UserUtil.getCurrentUser().getUsername());
        his.setUserIp(CloudRequestContext.getContext().getUserIp());

        CustPreconditions.checkNotNull(his.getCommand(), "command not be null");
        CustPreconditions.checkNotNull(his.getKind(), "kind not be null");
        CustPreconditions.checkNotNull(his.getKubeId(), "kubeId not be null");
        CustPreconditions.checkNotNull(his.getNamespace(), "Namespace not be null");
        his.setLastEndTimestamp(System.currentTimeMillis());
        Timestamp startTime = new Timestamp(System.currentTimeMillis() + 2000);
        his.setInsertTime(startTime);
        his.setStatus(StatusConstant.RUNNING);
        resourceChangeHisService.add(his);
        return his;
    }

    @Override
    public List<CloudApp> findAllList(Map<String, Object> map) {
//        map.remove("deleted");
        Object kind = map.get("kind");
        if (kind instanceof List) {
            map.remove("kind");
            map.put("kinds", kind);
        }
        return cloudAppMapper.findAllList(map, DatasourceConstant.SCHEMA, CLOUD_APP, DatasourceConstant.CLOUD_APP_LOGIC, DatasourceConstant.CLOUD_KUBE_CONFIG_TABLE, CLOUD_KUBE_SCHEDULER);
    }

    @Override
    public List<CloudApp> getAppListByKubeId(Integer kubeId) {
        return cloudAppMapper.getAppListByKubeId(kubeId, DatasourceConstant.SCHEMA, CLOUD_APP);
    }

    @Override
    public List<CloudApp> getAppByKubeIdAndNamespaceAndKind(int kubeId, String namespace, String kind) {
        List<CloudApp> appByKubeIdAndNamespaceAndKind = cloudAppMapper.getAppByKubeIdAndNamespaceAndKind(kubeId, namespace, kind, DatasourceConstant.SCHEMA, CLOUD_APP);
        return appByKubeIdAndNamespaceAndKind;
    }

    @Override
    public List<CloudApp> findListByCondition(Map<String, Object> condition) {
        if (Objects.isNull(condition)) {
            condition = Collections.emptyMap();
        }
        return cloudAppMapper.findAllList(condition, DatasourceConstant.SCHEMA, CLOUD_APP, DatasourceConstant.CLOUD_APP_LOGIC, DatasourceConstant.CLOUD_KUBE_CONFIG_TABLE, CLOUD_KUBE_SCHEDULER);
    }


    @Override
    public CloudApp findOneByIplist(String schema, String cloudApp, String findIpList, String kind, Integer kubeId) {
        return cloudAppMapper.findOneByIplist(DatasourceConstant.SCHEMA, CLOUD_APP, findIpList, kind, kubeId);
    }

    @Override
    public PageInfo<CloudAppVO> searchPageSkipAutho(PageDTO pageDTO) {
        return searchPage(pageDTO);
    }

    @Override
    @Deprecated
    //该方法已经弃用，请使用getCloudAppByLogicId
    public List<CloudApp> getAppByLogicId(int logicAppId) {
        return findAllList(Collections.singletonMap("logicAppId", logicAppId));
    }

    @Override
    public CloudApp getCloudAppByLogicId(int logicAppId) {
        return findAllList(Collections.singletonMap("logicAppId", logicAppId)).stream().findFirst().orElseThrow(() -> new CustomException(600, "未查询到应用信息"));
    }

    @Override
    public List<CloudApp> getByJoinLogicApp(Map<String, Object> condition) {
        Object kind = condition.get("kind");
        if (kind instanceof List) {
            condition.remove("kind");
            condition.put("kinds", kind);
        }
        List<CloudApp> apps = cloudAppMapper.getByJoinLogicApp(DatasourceConstant.SCHEMA, CLOUD_APP, DatasourceConstant.CLOUD_APP_LOGIC, condition);
        return apps;
    }

    @Override
    public List<CloudApp> getPhysicApps(Integer appLogicId) {
        return cloudAppMapper.findAllList(Collections.singletonMap("logicAppId", appLogicId), UserUtil.getSchema(), CLOUD_APP, CLOUD_APP_LOGIC, CLOUD_KUBE_CONFIG_TABLE, CLOUD_KUBE_SCHEDULER);
    }

    @Override
    public int countByNameKind(Map<String, Object> param) {
        List<CommonSearch> conditions = MybatisUtil.getJavaPropMapDBColumn(param,
                "CloudApp" + MybatisUtil.ORM_RESULT_MAP, "kind","cr_name","arch","namespace");
        return cloudAppMapper.count(UserUtil.getSchema(), CLOUD_APP, conditions);
    }

    @Override
    public void selectForUpdate(Integer logicAppId) {
        cloudAppMapper.selectForUpdate(UserUtil.getSchema(), CLOUD_APP, logicAppId);
    }

    @Override
    public CloudApp selectForUpdateByID(Integer appId) {
        return cloudAppMapper.selectForUpdateByID(UserUtil.getSchema(), CLOUD_APP, appId);
    }

    @Override
    public void stopChangeHisAsFailed(Integer hisId, String message) {
        boolean f = resourceChangeHisService.stopResourceChangeHis(hisId, message, false);
        CloudApp app = get(resourceChangeHisService.get(hisId).getAppId());
        app.setStatus(CloudAppConstant.AppStatus.FAILED);
        update(app);
    }

    @Override
    public void appendChangeHisMessage(Integer hisId, String result) {
        ResourceChangeHis his = resourceChangeHisService.get(hisId);
        his.setMsg(his.getMsg() == null ? result : his.getMsg().concat("\n").concat(result));
        his.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
        resourceChangeHisService.update(his);
    }

//    @Override
//    public Set<String> filterInvalidUserDefinedParams(String kind, String arch, String version, Map<String, String> paraMap) {
//        String whiteListOfCfg = sysConfigService.findOne(CloudAppConstant.SysCfgCategory.PARAM_WHITELIST, AppKind.valueOf(kind,arch).getProduct());
//        String blackListOfCfg = sysConfigService.findOne(CloudAppConstant.SysCfgCategory.PARAM_BLACKLIST, AppKind.valueOf(kind,arch).getProduct());
//        if (StringUtils.isEmpty(whiteListOfCfg) && StringUtils.isEmpty(blackListOfCfg)) {
//            return Collections.emptySet();
//        }
//        Set<String> result = new HashSet<>();
//        Set<String> paramNames = paraMap.keySet();
//        Set<String> whiteList = StringUtils.isEmpty(whiteListOfCfg) ? Collections.emptySet()
//                : Arrays.stream(whiteListOfCfg.split(",")).map(String::trim).collect(Collectors.toSet());
//        Set<String> blackList = StringUtils.isEmpty(blackListOfCfg) ? Collections.emptySet()
//                :Arrays.stream(blackListOfCfg.split(",")).map(String::trim).collect(Collectors.toSet());
//        for (String prop : paramNames) {
//            if (blackList.contains(prop) || !whiteList.contains(prop)) {
//                result.add(prop);
//            }
//        }
//
//        return result;
//    }

    @Override
    public int recordResourceChangeHis(CloudApp app, ActionEnum actionEnum, String status, String crRun, String msg) {
        ResourceChangeHis his = getResourceChangeHis(app, actionEnum, status, crRun, msg);
        his.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
        return resourceChangeHisService.add(his);
    }

    @Autowired
    private DataAuthorizeService dataAuthorizeService;
    @ResourceView
    @Override
    public List<CloudApp> findHealthyManagedApps(Map<String, Object> params) {
        List<CloudApp> notDeletedList = findNotDeletedList(params);
        List<CloudAppVO> collect = notDeletedList.stream()
//                .filter(app -> checkOpsExecutable(app.getId()))
                .filter(app -> dataAuthorizeService.hasPermission(null, app.getId())) // 权限控制
                .map(this::convertToVO)
                .collect(Collectors.toList());
        if (!collect.isEmpty()) {
            try {
                // complete with alertstatus
                CompletableFuture.runAsync(complementK8sInfo(collect))
                        .join();
            } catch (Exception e) {
                log.error("", e);
            }
            collect = collect.stream().filter(vo -> vo.getHealth().checkHealthy())// 健康
                    .collect(Collectors.toList());
        }
        return collect.stream().map(vo -> vo).collect(Collectors.toList());
    }

    public List<AppYamlVO> getAppyamls(String appYamlType, Integer appLogicId, String name, Integer kubeId, String appid) {
        List<AppYamlVO> results = new ArrayList<>();
        List<CloudApp> cloudApps = new ArrayList<>();
        // 通过部门ID获取namespace

        if (ResourceKinds.KIND_CUSTOM_RESOURCE_DEFINITION.equals(appYamlType)) {
            //应用视图   ：  集群视图
            cloudApps = null != appLogicId ? cloudAppLogicService.getPhysicApps(appLogicId) : Collections.singletonList(get(Integer.valueOf(appid)));
            for (CloudApp cloudApp : cloudApps) {
                AppYamlVO appYamlVO = getAppYamlVO(appYamlType, name, cloudApp.getKubeId(), cloudApp, cloudApp.getNamespace());
                results.add(appYamlVO);
            }
            return results;
        }

        String namespace = null;
        if (appLogicId != null) {
            namespace = getAppByLogicId(appLogicId).stream().findAny().map(app -> app.getNamespace())
                    .orElseThrow(CustomException::new);
        }
        if (appid != null) {
            namespace = Optional.ofNullable(get(Integer.valueOf(appid)))
                    .map(app -> app.getNamespace())
                    .orElseThrow(CustomException::new);
        }
        return Collections.singletonList(getAppYamlVO(appYamlType, name, kubeId, null, namespace));

    }

    @Override
    public List<AppInstanceVO> getOperators(Integer kubeId, String kind, String arch) {
        // 0.定义公共变量

        //命名空间
        String operatorNamespace = null;
        //返回结果
        List<AppInstanceVO> res = new ArrayList<>();


        // 1.获取配置的operator信息

        //根据appkind类获取operator的名称以及namespace
        // 相同类型不同架构的operator名称不一致
        AppKind appKind = AppKind.valueOf(kind, arch);
        String operatorName = appKind.getOperatorName();

        //获取cloud_sys_config表中配置的所有operator信息，与appkind中配置的operator信息进行比对，若存在一致数据，则确定Operator信息
        //校验结果
        boolean isOK = false;
        //查询所有operator配置
        List<CloudSysConfig> cloudSysConfigs = sysConfigService.find("operator.name");
        //遍历校验
        for (CloudSysConfig cloudSysConfig : cloudSysConfigs) {
            String data = cloudSysConfig.getData();
            String[] namespaceAndName = data.split("/");
            if(operatorName.equals(namespaceAndName[1])){
                //校验到一致的配置，记录operator信息
                operatorNamespace = namespaceAndName[0];
                isOK = true;
                break;
            }
        }
        //判断校验结果
        if(!isOK){
            //校验未通过
            throw new CustomException(600, "未找到匹配的operator配置！");
        }


        // 2.获取operator实例
        KubeClient kubeClient = clientService.get(kubeId);
        String finalOperatorNamespace = operatorNamespace;
        List<Pod> pods = new ArrayList<>();
        String finalOperatorName = operatorName;
        if (appKind == AppKind.MYSQL_MGR) {
            pods = kubeClient.listPod(finalOperatorNamespace).stream().filter(pod -> pod.getMetadata().getName().contains(finalOperatorName) && !pod.getMetadata().getName().contains("-controller-manager-")).collect(Collectors.toList());
        } else {
            pods = kubeClient.listPod(finalOperatorNamespace).stream().filter(pod -> pod.getMetadata().getName().contains(finalOperatorName)).collect(Collectors.toList());
        }
        //判断是否存在pod
        if(null == pods || 0 == pods.size()) {
            return res;
        }

        // 3.获取集群配置
        KubeConfig kubeConfig = kubeConfigService.get(kubeId);


        // 4.判断operator架构，如果是deployment则获取cm，用来获取主从信息，如果是statefulset则不获取
        //主实例名称
        Optional<String> holderIdentity = Optional.empty();
        String operatorArch = pods.get(0).getMetadata().getOwnerReferences().get(0).getKind();
        if (!"StatefulSet".equalsIgnoreCase(operatorArch)) {
            // lease name: <watch-namespace>.<lease-key>.<group>
            // holderIdentity: <pod-name>_<container-id>
            // pod-name: (sts): <sts-name>-<index> (deployment): <deploy-name>-<hash>-<random-charseq>
            try {
                List<Lease> leaseList = kubeClient.getLease(finalOperatorNamespace);
                holderIdentity = leaseList.stream().map(lease -> lease.getSpec().getHolderIdentity())
                        .filter(holder -> holder.contains(finalOperatorName))
                        .map(holder -> holder.split("_")[0])
                        .findAny();
            } catch (Exception ignore) {
            }

        }
//                && !AppKind.MongoDB.getKind().equalsIgnoreCase(kind.toLowerCase())
//                && !AppKind.NameServer.getKind().equalsIgnoreCase(kind)
//                && !AppKind.Flink.getKind().equalsIgnoreCase(kind)
//                && !AppKind.Broker.getKind().equalsIgnoreCase(kind)
//                && !AppKind.Clickhouse.getKind().equalsIgnoreCase(kind)
//                && !AppKind.MYSQL_MGR.getKind().equalsIgnoreCase(kind)
//                && !AppKind.TIDB.getKind().equals(kind)
//        ) {
//            //因为clickhouse-zookeeper使用的 zookeeper 得operator，因此需要特殊处理一下
//            String tempKind = AppKind.ClickHouse_Zookeeper.getKind().equalsIgnoreCase(kind) ? AppKind.Zookeeper.getKind() : kind;
//            ConfigMap cm = kubeClient.getConfigMap(tempKind.toLowerCase() + "-" + "operator-manager-config", finalOperatorNamespace);
//            if(null != cm){
//                String dataYaml = cm.getData().get("controller_manager_config.yaml");
//                JSONObject dataYamlObj = YamlEngine.unmarshal(dataYaml, JSONObject.class);
//                JSONObject leaderElection = dataYamlObj.getJSONObject("leaderElection");
//                if(null != leaderElection){
//                    String cmName = leaderElection.getString("resourceName");
//                    ConfigMap findLeaderCm = kubeClient.getConfigMap(cmName, finalOperatorNamespace);
//                    String leadStr = findLeaderCm.getMetadata().getAnnotations().get("control-plane.alpha.kubernetes.io/leader");
//                    JSONObject leadObj = JSONObject.parseObject(leadStr);
//                    holderIdentity = leadObj.getString("holderIdentity");
//                }else{
//                    throw new CustomException(600, "获取operator角色失败！未获取到leaderElection属性！");
//                }
//            }else{
//                throw new CustomException(600, "获取operator角色失败！未获取到operator configmap！");
//            }
//        }


        // 5.获取cpu、内存、存储信息，并放入其他属性
        for(Pod pod : pods){
            AppInstanceVO vo = new AppInstanceVO();
            //放入实例其他信息
            vo.setPodName(pod.getMetadata().getName());
            vo.setNode(pod.getSpec().getNodeName());
            vo.setKubeName(kubeConfig.getName());
            vo.setIp(pod.getStatus().getPodIP());
            vo.setStatus(pod.getStatus().getPhase());
            vo.setNamespace(finalOperatorNamespace);
            vo.setArch(arch);
            vo.setKubeId(kubeId);
            vo.setKind(kind);
            //角色
            if(holderIdentity.isPresent() && holderIdentity.get().equals(pod.getMetadata().getName())){
                vo.setRole(CloudAppConstant.ROLE_PRIMARY);
            }else{
                vo.setRole(CloudAppConstant.ROLE_SECONDARY);
            }
            res.add(vo);
            if (true) {
                continue;
            }
            PodMetrics metrics = kubeClient.getClient().top().pods().metrics(pod.getMetadata().getNamespace(), pod.getMetadata().getName());
            if(null != metrics.getContainers()){
                for(ContainerMetrics containerMetrics : metrics.getContainers()){
                    if (!"manager".equalsIgnoreCase(containerMetrics.getName())
                            && !"mongodb-kubernetes-operator".equalsIgnoreCase(containerMetrics.getName())
                            && !"clickhouse-operator".equalsIgnoreCase(containerMetrics.getName())
                            && !"flink-kubernetes-operator".equalsIgnoreCase(containerMetrics.getName())
                            && !"mysql-operator".equalsIgnoreCase(containerMetrics.getName())
                            && !"tidb-controller-manager".equalsIgnoreCase(containerMetrics.getName())
                    )
                    {
                        continue;
                    }else{
                        Quantity cpu = containerMetrics.getUsage().get("cpu");
                        Double m = MetricUtil.getCpuTimeValueOfUnit(cpu.toString(), 'm');
                        if (m < 1000){
                            vo.setCpu(new BigDecimal(m).setScale(2, RoundingMode.HALF_UP) + "m");
                        } else {
                            vo.setCpu(new BigDecimal(m / 1000).setScale(2, RoundingMode.HALF_UP) + "c");
                        }
                        vo.setRawCpu(m);
                        long memory = Long.parseLong(containerMetrics.getUsage().get("memory").getAmount());
                        vo.setMemory(MetricUtil.humanReadableByteCountBin(1024 * memory)); // binary byte
                        vo.setRawMemory(1024 * memory);

                        break;
                    }
                }
            }
        }
        res.sort(Comparator.comparing(AppInstanceVO::getRole));
        return res;
    }

    @Override
    public List<PodTemplateSpec> getPodTemplateSpec(AppKind kind, String component) {
        String configMapYaml = sysConfigService.findOne(CloudAppConstant.SysCfgCategory.POD_TEMPLATE, "app.pod.template");
        JSONObject podTemplateObj = JSONObject.parseObject(configMapYaml);
        StringBuilder keyPrefix = new StringBuilder();
        keyPrefix.append(kind.getKind()).append("-").append(kind.getArch()).append("-");
        if (StringUtils.isEmpty(component)) {
            component = "DB";
        }
        if (!"All".equalsIgnoreCase(component))
            keyPrefix.append(component.toUpperCase());

        return podTemplateObj.keySet().stream().filter(key -> key.startsWith(keyPrefix.toString()))
                .map(key -> {
                    String podTemplateString = podTemplateObj.getString(key);
                    return YamlEngine.unmarshal(podTemplateString, PodTemplateSpec.class);
                }).collect(Collectors.toList());
    }

    @Override
    public List<String> getPodContainerNamesByTemplate(AppKind kind) {
        return getPodTemplateSpec(kind, "all").stream()
                .flatMap(tpl -> tpl.getSpec().getContainers().stream())
                .map(c -> c.getName()).collect(Collectors.toList());
    }

    @Override
    public List<CloudApp> listAppByNoBinlog(List<Integer> appIdList) {
                String appIdStr = appIdList.toString().replace("[", "").replace("]", "");
        return cloudAppMapper.listAppByNoBinlog(SCHEMA, CLOUD_BINLOG_BACKUP_HIS, CLOUD_APP, appIdStr);
    }

    private AppYamlVO getAppYamlVO(String appYamlType, String name, Integer kubeId, CloudApp app,String nameSpace) {
        //集群视图
        KubeConfig kubeConfig = kubeConfigService.get(kubeId);
        KubeClient kubeClient = clientService.get(kubeId);

        AppYamlVO appYamlVO;
        switch (appYamlType) {
            case ResourceKinds.KIND_POD:
                appYamlVO = getPodYaml(kubeClient, nameSpace, name);
                break;
            case ResourceKinds.KIND_PERSISTENT_VOLUME:
                appYamlVO = getPVYaml(kubeClient, name);
                break;
            case ResourceKinds.KIND_PERSISTENT_VOLUME_CLAIM:
                appYamlVO = getPVCYaml(kubeClient, name, nameSpace);
                break;
            case ResourceKinds.KIND_CONFIG_MAP:
                appYamlVO = getCmYaml(kubeClient, name, nameSpace);
                break;
            case ResourceKinds.KIND_CUSTOM_RESOURCE_DEFINITION:
                appYamlVO = getCrdYaml(kubeClient, app.getCrName(), nameSpace, app.getKind(), app.getArch());
                break;
            default:
                return new AppYamlVO();
        }

        appYamlVO.setKubeId(kubeConfig.getId());
        appYamlVO.setKubeName(kubeConfig.getName());
        return appYamlVO;
    }

    /*
        获取 crd  信息
     */
    private AppYamlVO getCrdYaml(KubeClient kubeClient, String crdName, String nameSpace, String kind, String arch) {
        AppKind appKind = AppKind.valueOf(kind, arch);
        Class<? extends CustomResource> crClass = appKind.getCrClass();
        CustomResource customResource = kubeClient.listCustomResource(crClass, crdName, nameSpace);
        return new AppYamlVO(YamlEngine.marshal(customResource));
    }

    /*
        获取 PV yaml信息
     */
    private AppYamlVO getPVYaml(KubeClient kubeClient, String pvName) {
        if (StringUtils.isEmpty(pvName)) {
            throw new CustomException(600, "缺少相关查询参数！");
        }
        PersistentVolume pvYamlObj = kubeClient.getPv(pvName);
        return new AppYamlVO(null == pvYamlObj ? "" : YamlEngine.marshal(pvYamlObj));
    }

    /*
        获取 PVC  yaml 信息
     */
    private AppYamlVO getPVCYaml(KubeClient kubeClient, String pvcName, String nameSpace) {
        if (StringUtils.isEmpty(nameSpace) || StringUtils.isEmpty(pvcName)) {
            throw new CustomException(600, "缺少相关查询参数！");
        }
        PersistentVolumeClaim pvcYamlObj = kubeClient.getPvc(nameSpace, pvcName);
        return new AppYamlVO(null == pvcYamlObj ? "" : YamlEngine.marshal(pvcYamlObj));
    }

    /*
        获取 POD yaml 信息
     */
    private AppYamlVO getPodYaml(KubeClient kubeClient,String nameSpace,String podName) {
        if (StringUtils.isEmpty(nameSpace) || StringUtils.isEmpty(podName)) {
            throw new CustomException(600, "缺少相关查询参数！");
        }
        Pod podYamlObj = kubeClient.getPod(nameSpace, podName);
        return new AppYamlVO(null == podYamlObj ? "" : YamlEngine.marshal(podYamlObj));
    }

    /*
        获取  CM  yaml 信息
     */
    private AppYamlVO getCmYaml(KubeClient kubeClient, String cmName, String nameSpace) {
        if (StringUtils.isEmpty(nameSpace) || StringUtils.isEmpty(cmName)) {
            throw new CustomException(600, "缺少相关查询参数！");
        }
        ConfigMap cmYamlObj = kubeClient.getConfigMap(cmName, nameSpace);
        return new AppYamlVO(null == cmYamlObj ? "" : YamlEngine.marshal(cmYamlObj));
    }

    @Override
    public void deleteCurrentTriggersOfApp(int appId) {
        CloudApp app = get(appId);
        List<ResourceChangeHis> resourceChangeHis = resourceChangeHisService.listByAppId(app.getId());
        log.info("unschedule quartz jobs related to app " + appId);
        for (ResourceChangeHis his : resourceChangeHis) {
//            String triggerName = his.getAction() + "_" + his.getId();
//            String triggerGroup = app.getCrName();
            if (StatusConstant.RUNNING.equals(his.getStatus()) || StatusConstant.ROLLBACK_IN_PROGRESS.equals(his.getStatus()))
                resourceChangeHisService.stopResourceChangeHis(his.getId(), "delete triggers of the app-" + appId, false);
        }
    }

    @Override
    public void appendOpLogOfHis(int hisId) {
        ResourceChangeHis his = resourceChangeHisService.get(hisId);
        opLogService.appendAndSave(his.getId(), his.getAppName(), his.getAction() + "_" + his.getId());
    }

    @Override
    public List<CloudApp> listAppByBackupStorage(String backupStorageclassname) {
        List<CloudApp> appList = cloudAppMapper.listAppByBackupStorage(SCHEMA, CLOUD_APP, backupStorageclassname);
        return appList;
    }

    @Override
    public Set<String> getAppSystemNameList(Integer tenantId) {
        CloudTenant tenant = tenantService.findById(tenantId);
        return cloudAppLogicService.getAppSystemNameList(tenant.getNamespace());
    }

    @Override
    public void updateAppSystemName(Integer appLogicId, String appSystemName) {
        CloudApp cloudApp = this.getCloudAppByLogicId(appLogicId);
        if (null == cloudApp)
            throw new CustomException(600, "未查询到相关资源信息");

        ResourceChangeHis resourceChangeHis = getResourceChangeHis(cloudApp, ActionEnum.UPDATE_APP_SYSTEM_NAME, StatusConstant.RUNNING, null, "修改资源" + cloudApp.getName() + "的所属应用:由<" + cloudApp.getAppSystemName() + ">改为:<" + appSystemName + ">");
        try {
            //若是有前置组件，则一起修改，
            AppKind appKind = AppKind.valueOf(cloudApp.getKind(), cloudApp.getArch());
            if (appKind.equals(AppKind.Redis) || appKind.equals(AppKind.Broker) || appKind.equals(AppKind.Elasticsearch) || appKind.equals(AppKind.Clickhouse))
                updatePreCompentAppSystemName(appSystemName, cloudApp, appKind);

            //修改资源的所属应用信息
            cloudAppMapper.updateAppSystemNameByLogicId(appLogicId, appSystemName, SCHEMA, CLOUD_APP);
            cloudAppLogicService.updateAppSystemNameByPriId(appLogicId, appSystemName);
            resourceChangeHis.setStatus(StatusConstant.SUCCESS);
        } catch (Exception e) {
            //添加记录
            resourceChangeHis.setStatus(StatusConstant.FAIL);
            throw new CustomException(600, "修改所属应用为：" + appSystemName + "，失败！");
        }finally {
            resourceChangeHis.setUpdateTime(new Timestamp(System.currentTimeMillis() + 2000));
            resourceChangeHisService.add(resourceChangeHis);
        }

    }

    private void updatePreCompentAppSystemName(String appSystemName, CloudApp cloudApp, AppKind appKind) {
        Map<String, Object> map = new HashMap<>();
        String crName = "";
        if (AppKind.Elasticsearch.equals(appKind) || AppKind.Broker.equals(appKind)) {
            appKind = AppKind.Elasticsearch.equals(appKind) ? AppKind.Kibana : AppKind.NameServer;
            crName = cloudApp.getCrName();
        } else if (AppKind.Redis.equals(appKind)) {
            appKind = AppKind.Sentinel;
            Redis cr = YamlEngine.unmarshal(StringUtils.isEmpty(cloudApp.getCr()) ? cloudApp.getCrRun() : cloudApp.getCr(), Redis.class);
            crName = cr.getSpec().getSentinel();
        } else {
            appKind = AppKind.ClickHouse_Zookeeper;
            crName = cloudApp.getCrName();
        }
        map.put("fullCrName", crName);
        map.put("namespace", cloudApp.getNamespace());
        map.put("kubeId", cloudApp.getKubeId());
        map.put("kind", appKind.getKind());
        map.put("arch", appKind.getArch());
        List<CloudApp> apps = this.findNotDeletedList(map);
        if (CollectionUtils.isNotEmpty(apps)) {
            cloudAppMapper.updateAppSystemNameByLogicId(apps.get(0).getLogicAppId(), appSystemName, SCHEMA, CLOUD_APP);
            cloudAppLogicService.updateAppSystemNameByPriId(apps.get(0).getLogicAppId(), appSystemName);
        }
    }

}