package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.impl.SysConfigService;
import com.shindata.mysql.v1.MySQLHA;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

@Getter
@Slf4j
public class MySQLNewInstallWatch extends MySQLNewWatch {

    @Autowired
    private SysConfigService sysConfigService;

    @Override
    public void doStopWatchResource(CloudApp app, TriggerHis triggerHis, OpsResultDTO.Builder result, MySQLHA cr) {
        logInfo(app, ActionEnum.CREATE, "execute stopping processor");
        if (result.isSuccessful()) {
            appMultiAZService.enableAlert(app);
            serviceManageOperationWatcherHelper.doStopWatchSvc(app, triggerHis, result.isSuccessful(), cr);

            Map<String, String> mergedJobDataMap = triggerHis.returnMergedJobDataMap();
            if (StringUtils.isEmpty(mergedJobDataMap.get("backupHisId"))) {
                String username = mergedJobDataMap.get("username");
                String password = mergedJobDataMap.get("password"); // todo 加解密
                if (StringUtils.isNoneBlank(username, password)) {
                    if (!dbUserService.findDbUserByName(app.getId(), username).isPresent()) {
                        result.msg("create user " + username);
                        dbUserService.createUser(app.getId(), username, "", CloudAppConstant.UserRole.ADMIN);
                        createRootUser(username, password, app);
                    }
                }
            }

            //创建dmp监控用户
            Map<String, String> dmpMonitorUser = operationUtil.createDMPMonitorUser();
            createRootUser(dmpMonitorUser.get("username"), dmpMonitorUser.get("password"), app);

            // 纳管到dmp
            if(autoManagement) {
                logInfo(app, ActionEnum.CREATE, "app will be managed into CMDB");
                result.msg("sync cloud app to DMP");
                result.msg(operationUtil.syncToDMP(app, triggerHis));
            }
        }

    }

    private void createRootUser(String username, String password, CloudApp app) {
        try {
            String createUserCmd = "mysql -uk8sadmin -pk8sadmin -e ?"; // ?作为占位符 之后被替换
            String createUserSql = String.format("CREATE USER IF NOT EXISTS '%s'@'%%' IDENTIFIED BY '%s'", username, password);
            String grantSql = String.format("GRANT ALL ON *.* TO '%s'@'%%' with grant option", username);
            String flushSql = "FLUSH PRIVILEGES";
            KubeClient client = kubeClientService.get(app.getKubeId());
            PodDTO pod = client.listPod(app.getNamespace(), AppKind.MYSQL_HA.labelOfPod(app)).stream().filter(p -> CloudAppConstant.ROLE_SOURCE.equals(p.getLabels().get("app.kubernetes.io/role")))
                    .findFirst().orElseThrow(() -> new IllegalStateException("没有主库label, 因此创建用户未完成"));
            String[] cmd = createUserCmd.split(" ");
            cmd[cmd.length-1] = createUserSql;
            String createUserRes = client.execCmd(app.getNamespace(), pod.getPodName(), AppKind.MYSQL_HA.getContainerName(), cmd);
            cmd[cmd.length-1] = grantSql;
            String grantRes = client.execCmd(app.getNamespace(), pod.getPodName(), AppKind.MYSQL_HA.getContainerName(), cmd);
            cmd[cmd.length-1] = flushSql;
            String flushRes = client.execCmd(app.getNamespace(), pod.getPodName(), AppKind.MYSQL_HA.getContainerName(), cmd);
        } catch (Exception e) {
            throw new RuntimeException("create user failed, will try later. url", e);
        }
    }

}
