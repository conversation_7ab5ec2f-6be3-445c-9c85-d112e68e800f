server:
  port: 8300
  max-http-header-size: 1000000
  connection-timeout: 60000
  session:
    timeout: 86400 #用户回话session过期时间
  tomcat:
    basedir: ../tmp/cloud-service-tomcat/

swagger:
  title: CloudService Api
  description: ShinData云平台API
  base-package: cn.newdt.cloud.web

mybatis:
  mapper-locations: classpath*:/mybatis/**/*.xml
  type-aliases-package: cn.newdt.cloud.domain,cn.newdt.commons.bean
  configuration:
    call-setters-on-nulls: false
    jdbc-type-for-null: null

logging:
  #  level:
  #    cn:
  #      newdt:
  #        cloud:
  #          mapper: debug # 打印mybatis日志
  config: file:config/logback-spring.xml

####################cloud-service 业务配置#############################
#elasticsearch配置相关信息
elasticsearch:
  ip: *************
  port: 32766
  username: elastic
  password: T6CAz93K4gOzb1IO310s27ce
quartz:
  enable: true # 启用quartz
  job-store-type: jdbc
  properties: # quartz 配置
    org.quartz.scheduler:
      instanceId: AUTO #Default hostname and timestamp generate instance ID, which can be any string, but must be the only corresponding qrtz_scheduler_state INSTANCE_NAME field for all dispatchers
    #      batchTriggerAcquisitionMaxCount: 1 # scheduler 节点可以一次acquire的trigger数量
    #      batchTriggerAcquisitionFireAheadTimeWindow: 0 # fire time在时间窗口范围的trigger会被batch acquired and fired
    org.quartz.jobStore:
      class: org.springframework.scheduling.quartz.LocalDataSourceJobStore # Persistence Configuration
      #      driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate #We only make database-specific proxies for databases
      useProperties: true
      misfireThreshold: 120000
      isClustered: true
      #clusterCheckinInterval: 5000
      tablePrefix: ndtmdb.CLOUD_QRTZ_
      acquireTriggersWithinLock: false # 是否在 acquire 前执行select quartz_locks for update 获取锁。早期版本需要为 true，避免死锁.
    org.quartz.threadPool:
      class: org.quartz.simpl.SimpleThreadPool
      threadCount: 50
      threadPriority: 5
      threadsInheritContextClassLoaderOfInitializingThread: true

customer:
  #name: ceb # 决定IPAM的方式，光大分支使用
  tenant-as-namespace: true # 租户是否与命名空间绑定，默认 true。
  namespace-auto-create: true # ns不存在时是否自动创建，默认 true.

webterminal:
  port: 7681 # go-tty 服务端口，用于运维窗口功能

kubeConfig:
  boc_cni_port: 9013 # boc网络插件api server端口，IP为集群IP, 默认9013
  agent_port: 55233 # deprecated, agent端口, 默认55233


autoManagement: true #自动纳管开关，开启为true

# 备份超时时间，单位为秒
backupTimeout:
  redis: 86400
  es: 86400
  mongodb: 86400
  mysql: 86400
  opengauss: 86400

ftp:
  protocol: ftp # 可选ftp，sftp。指定ftp服务器的协议，e.g. 指定ftp时则使用ftp://<ip>:<ftpport> 连接;
  # 复用dmp ftp 配置
  #ip:
  #userName:
  #password:
  #ftpport: 21
  #sftpport: 22
  #basePath: /tmp

app:
  operation:
    check_timeout: false # 是否判断操作超时
    timeout: 15 # 运维操作超时时间，单位分支
  ipam:
    calico:
      checkBy: api # 检查IP是否free的方法，可选ping, api, agent
  schedule:
    anti-affinity-strict: false # 应用的实例是否严格限制部署在同一node，默认为true；当前仅MySQL启用
    affinity-strict: true # 应用的实例是否严格按照用户指定节点部署，默认为true；当前仅MySQL启用

storedb:
  db-type: mysql

encryption:
  asymmetricalgorithm: SM2 # 非对称加密，用于前端密码加密，后端解密
  symmetricalgorithm: # 对称加密
    resource: SM4 # 例如目标库密码加密
    sql: AES # 用于取数SQL加密