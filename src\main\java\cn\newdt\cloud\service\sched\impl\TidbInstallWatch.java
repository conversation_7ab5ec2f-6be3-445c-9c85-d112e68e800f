package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.impl.TidbService;
import cn.newdt.cloud.utils.TidbUtil;
import cn.newdt.commons.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class TidbInstallWatch extends TidbWatch {
    @Autowired
    private TidbService tidbService;

    @Override
    protected void doStopWatch(CloudApp app, boolean success, TriggerHis triggerHis) {
        // 安装
        if (success) {
            appMultiAZService.enableAlert(app);
            serviceManageOperationWatcherHelper.doStopWatchSvc(app, triggerHis, success, null);
            Map<String, String> mergedJobDataMap = triggerHis.returnMergedJobDataMap();
            String username = mergedJobDataMap.get("username");
            String password = mergedJobDataMap.get("password"); // todo 加解密
            if (StringUtils.isNoneBlank(username, password)) {
                if (!dbUserService.findDbUserByName(app.getId(), username).isPresent()) {
                    dbUserService.createUser(app.getId(), username, "", CloudAppConstant.UserRole.ADMIN);
                    //创建普通用户
                    createUser(username, password, app);
                    // 创建监控用户
                    Map<String, String> dmpMonitorUser = operationUtil.createDMPMonitorUser();
                    createUser(dmpMonitorUser.get("username"), dmpMonitorUser.get("password"), app);
                    //创建admin用户
                    createRootUser(app);
                }
            }
            if (autoManagement) {
                //添加纳管到dmp
                operationUtil.syncToDMP(app, triggerHis);
            }
        }
    }

    private void createUser(String username, String password, CloudApp app) {
        try {
            //创建基本用户和admin用户
            String createUserSql = "CREATE USER IF NOT EXISTS  \'%s\'@\'%%\' IDENTIFIED BY \'%s\';";
            String grantUserSql = "GRANT ALL PRIVILEGES ON *.* TO \'%s\'@\'%%\' WITH GRANT OPTION;";
            // 刷新权限命令
            String flushSql = "FLUSH PRIVILEGES;";
            List<String> sqlList = new ArrayList<>();
            sqlList.add(String.format(createUserSql, username, password));
            sqlList.add(String.format(grantUserSql, username));
            tidbService.runExecSql(app, ActionEnum.CREATE_APP_USER, sqlList, flushSql, Boolean.TRUE);
        } catch (Exception e) {
            log.error("clickhouse创建用户失败！");
            throw new CustomException(600, "clickhouse创建用户失败！");
        }
    }

    private void createRootUser(CloudApp app) {
        String createAdminSql = "CREATE USER IF NOT EXISTS  \'%s\'@\'%%\' IDENTIFIED BY \'%s\';";
        String grantAdminSql = "GRANT ALL PRIVILEGES ON *.* TO \'%s\'@\'%%\' WITH GRANT OPTION;";
        //修改原本root用户密码
        String updateRootSql = "ALTER USER \'root\'@\'%%\' IDENTIFIED BY \'%s\';";
        // 刷新权限命令
        String flushSql = "FLUSH PRIVILEGES;";
        List<String> sqlList = new ArrayList<>();

        sqlList.add(String.format(createAdminSql, TidbUtil.TIDB_ADMIN_USERNAME, TidbUtil.TIDB_ADMIN_PASSWORD));
        sqlList.add(String.format(grantAdminSql, TidbUtil.TIDB_ADMIN_USERNAME));
        sqlList.add(String.format(updateRootSql, TidbUtil.TIDB_ADMIN_PASSWORD));
        tidbService.runExecSql(app, ActionEnum.CREATE_APP_USER, sqlList, flushSql, Boolean.TRUE);
    }
}
