package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.domain.CloudSysConfig;
import cn.newdt.cloud.domain.KubeConfig;
import cn.newdt.cloud.filter.ResourceChangeLog;
import cn.newdt.cloud.mapper.CloudSysConfigMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.GlobalConfigMap;
import cn.newdt.cloud.service.KubeClientService;
import cn.newdt.cloud.service.KubeConfigService;
import cn.newdt.cloud.service.ResourceHelper;
import cn.newdt.cloud.service.alert.RetentionConfigureService;
import cn.newdt.cloud.service.csi.CSILoader;
import cn.newdt.cloud.utils.ESUtil;
import cn.newdt.cloud.utils.JsonUtil;
import cn.newdt.cloud.utils.PropertyUtil;
import cn.newdt.cloud.vo.CloudTenantVO;
import cn.newdt.commons.exception.ArgumentException;
import cn.newdt.commons.exception.CustomException;
import com.alibaba.fastjson.JSONObject;
import io.fabric8.kubernetes.api.model.ConfigMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.DependsOn;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.ActionEnum.MODIFY_SYSCONFIG;
import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgCategory.*;
import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgName.*;
import static cn.newdt.cloud.constant.DatasourceConstant.CLOUD_SYS_CONFIG;
import static cn.newdt.cloud.constant.DatasourceConstant.SCHEMA;

@Service
@Slf4j
public class SysConfigSyncService implements InitializingBean {
    private ObserverRegistry observerRegistry;
    @Autowired
    private KubeClientService kubeClientService;
    @Autowired
    private KubeConfigService kubeConfigService;
    @Autowired
    private SysConfigService sysConfigService;

    public boolean updateSysConfigByCategoryAndName(CloudSysConfig sysconfig) {

        if (sysConfigService.updateSysConfigByCategoryAndName(sysconfig) == 1) {
            observerRegistry.handleUpdate(sysconfig);
            return true;
        }
        return false;
    }

    public void updateSysConfigByCategoryAndNames(List<CloudSysConfig> sysConfigList) {
       sysConfigService.updateSysConfigByCategoryAndNames(sysConfigList);

       sysConfigList.stream().forEach(sysConfig -> {
           if (OVER_ALLOCATION_RATIO.equalsIgnoreCase(sysConfig.getCategory())) {
               observerRegistry.handleUpdate(sysConfig);
           }
       });
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("Start loading sysconfig");

        // 读取在k8s存放系统配置的 ConfigMap 元数据
        CMMetadata bind = PropertyUtil.bind(
                PropertyUtil.<CloudSysConfig>read(sysConfigService.find(K8S_SYS_CONFIG_CM),
                        cfg -> StringUtils.join(cfg.getCategory(), ".", cfg.getName()),
                        cfg -> cfg.getData()),
                CMMetadata.class, K8S_SYS_CONFIG_CM);
        GlobalConfigMap.createInstance(bind.name, bind.namespace, sysConfigService, kubeConfigService);

        try {
            ResourceHelper.createInstance(sysConfigService, kubeConfigService);
            observerRegistry = new ObserverRegistry();
            // 默认触发以下配置更新维护
            observerRegistry.handleUpdateByCategory(sysConfigService.find(
                    K8S_SYS_CONFIG_CM_DATA,
                    OVER_ALLOCATION_RATIO // ResourceHelper needed
            ));
        } catch (Exception e) {
            log.error("Cloud_sys_config init handler failed with exception", e);
        }
    }

    public static class ObserverRegistry {
        // key is [category], or [category].[name],
        Map<String, List<Consumer<List<CloudSysConfig>>>> updateObservers = new ConcurrentHashMap<>();

        private ObserverRegistry() {
            // 注册
            updateObservers.put(OVER_ALLOCATION_RATIO,
                    Collections.singletonList(ResourceHelper.getInstance()::updateRatioOnAll));
            updateObservers.put(K8S_SYS_CONFIG_CM_DATA,
                    Collections.singletonList(GlobalConfigMap.getInstance()::loadData));
        }

        public void handleUpdateByCategory(@NotEmpty List<CloudSysConfig> sysConfig) {
            Optional.ofNullable(updateObservers.get(sysConfig.get(0).getCategory()))
                    .ifPresent(handlers -> handlers.forEach(observer -> observer.accept(sysConfig)));
        }

        public void handleUpdate(CloudSysConfig sysConfig) {
            handleUpdateByCategory(Collections.singletonList(sysConfig));
        }
    }

    public static class CMMetadata {
        String name;
        String namespace;
    }

}
