package cn.newdt.cloud.vo;

import cn.newdt.cloud.domain.KubeConfig;

import java.time.LocalDateTime;
import java.util.List;

public class CloudZoneVO {
    public transient static final int defaultID = 1;
    private Integer id;

    private String name;

    private boolean defaultZone = false;

    /**
     * 0 启用, 1 未启用
     */
    private String status = "0";

    private String description;

    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private List<KubeConfig> kubes;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isDefaultZone() {
        return defaultZone;
    }

    public void setDefaultZone(boolean defaultZone) {
        this.defaultZone = defaultZone;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public List<KubeConfig> getKubes() {
        return kubes;
    }

    public void setKubes(List<KubeConfig> kubes) {
        this.kubes = kubes;
    }
}