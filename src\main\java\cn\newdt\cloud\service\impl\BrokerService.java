package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.common.OpLogContext;
import cn.newdt.cloud.common.TriFunction;
import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.CloudAppLogic;
import cn.newdt.cloud.domain.NodePortInfo;
import cn.newdt.cloud.domain.ServiceManager;
import cn.newdt.cloud.domain.cr.Broker;
import cn.newdt.cloud.dto.*;
import cn.newdt.cloud.mapper.NodePortInfoMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.service.sched.impl.BrokerClusterServiceWatch;
import cn.newdt.cloud.service.sched.impl.BrokerInstallWatch;
import cn.newdt.cloud.service.sched.impl.BrokerScaleWatch;
import cn.newdt.cloud.service.sched.impl.BrokerWatch;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.AppInstanceVO;
import cn.newdt.cloud.vo.CloudAppVO;
import cn.newdt.cloud.vo.InstallAppVo;
import cn.newdt.cloud.vo.OverrideSpec;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.exception.CustomException;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import io.fabric8.kubernetes.api.model.ConfigMap;
import io.fabric8.kubernetes.api.model.NodeAffinity;
import io.fabric8.kubernetes.api.model.Toleration;
import io.fabric8.kubernetes.client.CustomResource;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgCategory.OPERATOR_CONFIG;

@Slf4j
@Service
public class BrokerService extends DefaultAppKindService<Broker> implements ServiceManageOperation, ComposedAppService{
    @Autowired
    private AppOperationHandler appOperationHandler;
    @Autowired
    private KubeClientService kubeClientService;

    @Autowired
    private KubeConfigService kubeConfigService;

    @Autowired
    private NameServerService nameServerService;

    @Autowired
    private NodePortInfoMapper nodePortInfoMapper;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private NodePortService nodePortService;

    @Autowired
    private AccessManagementService accessManagementService;

    @Autowired
    private CloudAppConfigService appConfigService;

    @Autowired
    private CloudDatabaseUserService dbUserService;

    @Override
    protected void completeInstanceProperty(List<AppInstanceVO> instances, int appId) {

    }

    @Override
    public AppKind getKind() {
        return AppKind.Broker;
    }

    @Override
    public String getIpReservationTarget() {
        return "statefulset";
    }

    @Override
    public String getIpOwnerKind() {
        // 使用RocketMQ的ip池
        return "RocketMQ";
    }

    @Override
    public boolean nodePolicy() {
        return false;
    }

    @Override
    public Broker doInstall(CloudAppVO vo, List<String> ips) throws Exception {
        //获取kubeClient
        KubeClient kubeClient = kubeClientService.get(vo.getKubeId());
        //获取镜像
        Map<ImageKindEnum, String> imageManifest = appConfigService.getImageManifest(getKind(), vo.getVersion());
        String brokerImage = imageManifest.get(ImageKindEnum.MainImage);
        if(StringUtils.isBlank(brokerImage)){
            throw new CustomException(600, "Broker应用未找到对应的镜像信息！镜像版本：" + vo.getVersion());
        }
        String filebeatImage = imageManifest.getOrDefault(ImageKindEnum.Filebeat, "");

        String exporterImage = imageManifest.getOrDefault(ImageKindEnum.Exporter, "");

        Broker.FilebeatConfig filebeatConfig = new Broker.FilebeatConfig();
        filebeatConfig.setImage(filebeatImage);
        filebeatConfig.setConfigFile("broker-cluster-filebeat.yaml");
        filebeatConfig.setConfigMap("operator-filebeat-configmap");
        CloudApp nameserver = appService.get(vo.getNameserviceId());
        if(Objects.isNull(nameserver)){
            throw new CustomException(600, "没有查询到依赖的nameserver信息");
        }

        String ipList = nameserver.getIpList();
        JSONArray nodeAndIpList = JSONArray.parseArray(ipList);
        String[] nameserverIpList = new String[nodeAndIpList.size()];
        for(int i=0;i<nodeAndIpList.size();i++) {
            JSONObject everyNodeAndIp = nodeAndIpList.getJSONObject(i);
            String ip = everyNodeAndIp.getString("ip");
            nameserverIpList[i] = ip;
        }

        List<Broker.IpListConfig> ipListConfigs = new ArrayList<>();
        // 初始化ipListConfig
        for (String ip : ips) {
            ipListConfigs.add(Broker.IpListConfig.builder().ip(ip).build());
        }

        //构建broker
        Map<String, String> configMap = new HashMap<>();

        //创建secret
        String usernameKey = "username";
        String passwordKey = "password";
        //判断是否设置密码
        Broker.Secret secret = new Broker.Secret();
        if (!StringUtils.isEmpty(vo.getUsername()) && !StringUtils.isEmpty(vo.getPassword())) {
            try {
                //data
                Map<String, String> brokerSecretData = new HashMap<>();
                brokerSecretData.put(usernameKey, vo.getUsername());
                brokerSecretData.put(passwordKey, vo.getPassword());
                //创建cr部分
                secret.setUsernameKey(usernameKey);
                secret.setPasswordKey(passwordKey);
                secret.setSecretName(getSecretName(vo.getCrName()));
                //创建secret
                kubeClient.createSecret(vo.getNamespace(), getSecretName(vo.getCrName()), null, brokerSecretData);
                //用户名 落表
                dbUserService.createUser(vo.getId(), vo.getUsername(), vo.getEncryptedPassword(), CloudAppConstant.UserRole.ADMIN);
            } catch (Exception e) {
                throw new CustomException(600, "创建secret失败！错误信息：" + e.getMessage());
            }
        } else {
                //没有创建密码，修改配置为false
                configMap.put("Acl Enable", "false");
        }

        Broker.BrokerSpec spec = Broker.BrokerSpec.builder()
                .masterSize(vo.getMasterSize())
                .ipList(ipListConfigs.toArray(new Broker.IpListConfig[0]))
                .image(brokerImage)
                .cpu(vo.getCpu())
                .memory(vo.getMemory())
                .nameServers(nameserverIpList)
                .servicePort(0)
                .filebeat(filebeatConfig)
                .storage(Broker.StorageConfig.builder()
                        .Size(vo.getDisk())
                        .StorageClass(vo.getStorageClassName())
                        .HostpathRoot(vo.getHostpathRoot())
                        .build())
                .schedule(Broker.ScheduleConfig.builder()
                        .antiAffinityRequired(vo.getAntiAffinityRequired())
                        .nodeAffinity(convertCRNodeAffinity(vo.getSelector(), NodeAffinity.class))
                        .tolerations(convertCRTolerations(vo.getToleration(), Toleration.class))
                        .build())
                .config(configMap)
                .exporterImage(exporterImage)
//                .secret(Broker.Secret.builder()
//                        .secretName(getSecretName(vo.getCrName()))
//                        .usernameKey(usernameKey)
//                        .passwordKey(passwordKey)
//                        .build())
                .build();
        if(!StringUtils.isEmpty(secret.getUsernameKey()) && !StringUtils.isEmpty(secret.getPasswordKey()) && !StringUtils.isEmpty(secret.getSecretName())){
            //放入spec
            spec.setSecret(secret);
        }

        // 获取master节点数
//        Integer masterSize = vo.getMasterSize();
        // 获取nodePort
//        List<Integer> freePorts = networkService.allocateNodePort(vo, masterSize);

        // 创建filebeat-cm
//        ensureFilebeatCM(kubeClient, "rocketmq-filebeat-cm", vo.getNamespace());

        // todo 提取全局方法
        Map<String, String> config = new HashMap<>();
        log.info("[broker 安装]参数模板id为：" +  vo.getDbParamTemplateId());

        if (vo.getDbParamTemplateId() != null ){
            MySQLParamTemplateDTO mysqlParamTemplate = cloudDbParamTemplateService.getMysqlParamTemplate(vo.getDbParamTemplateId());
            config = cloudDbParamTemplateService.composeMysqlCnfUseDbParamTemplateToMap(mysqlParamTemplate);
        }
        overWriteCnfParam(vo, config);
        spec.setConfig(config);

        Broker cr = new Broker(vo.getCrName(), vo.getNamespace(), spec);

        return cr;
    }

    /**
     * 此为模板, 一个ns下的pod共用一个. mount时替换fields属性
     */
    private void ensureFilebeatCM(KubeClient kubeClient, String cmName, String namespace) {
        ConfigMap cm = kubeClient.getConfigMap(cmName, namespace);
        if (cm == null) {
            String cmYaml = sysConfigService.findOne(OPERATOR_CONFIG, "RocketMQ.config");
            Map<String, String> param = new HashMap<>();
            param.put("NAMESPACE", namespace);
            param.put("es_host", esUtil.getEsIp() + ":" + esUtil.getEsPort());
            param.put("es_username", esUtil.getEsUsername());
            param.put("es_pwd", esUtil.getEsPassword());
            param.put("es_protocol", esUtil.getProtocol());
            cmYaml = YamlUtil.evaluateTemplate(cmYaml, param);
            kubeClient.applyYaml(cmYaml, namespace);
        }
    }

    @Transactional
    public int occupyPort(CloudApp app, Integer port) {
        NodePortInfo nodePortInfo = new NodePortInfo();
        nodePortInfo.setKubeId(app.getKubeId());
        nodePortInfo.setPort(port);
        nodePortInfo.setPortType(CloudAppConstant.NodePortType.SERVICE_PORT);
        nodePortInfo.setStatus(CloudAppConstant.NodePortStatus.USED);
        nodePortInfo.setAppId(app.getId());
        //记录写端口
        int n = 0;
        if (app.getWritePort() != null) {
            n += nodePortInfoMapper.updatePortStatus(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_NODEPORT_USED_INFO, nodePortInfo);
            log.debug("[port mgr] update " + nodePortInfo.getPort() + " to be used");
        }
        return n;
    }

    @Override
    public void createCrName(CloudApp vo){
//        vo.setCrName(vo.getName() + NAME_SUFFIX);
//        vo.setName(vo.getName() + "-broker");
    }

    @Override
    public Class<? extends OpsPostProcessor> getProcessorClass(ActionEnum action) {
        switch (action) {
            case CREATE:
                return BrokerInstallWatch.class;
            case UPDATE:
            case MODIFY_PARAM:
                return BrokerWatch.class;
            case SCALE_OUT:
            case SCALE_IN:
                return BrokerScaleWatch.class;
            case UPDATE_SERVICE:
            case DELETE_SERVICE:
            case CREATE_SERVICE:
                return BrokerClusterServiceWatch.class;
            default:
                return super.getProcessorClass(action);
        }
    }

    @Override
    protected boolean supportIPAM(CloudAppVO app) {
        return true;
    }

    @Override
    public void update(ResourceDTO patch) throws Exception {
        Consumer<Broker> modifier = (cr) -> {
            Broker.BrokerSpec spec = cr.getSpec();
            spec.setCpu(patch.getCpu());
            spec.setMemory(patch.getMemory());
            spec.getStorage().setSize(patch.getDisk());
        };
        Consumer<Broker> storageModifier = (cr) -> {
            Broker.BrokerSpec spec = cr.getSpec();
            if (MetricUtil.lessAndNotZero(spec.getStorage().getSize(), patch.getDisk()))
                spec.getStorage().setSize(patch.getDisk());
        };
        appOperationHandler.handleUpdate(patch, modifier, this, Broker.class, storageModifier);
    }

    @Override
    public void scale(int id, OverrideSpec vo, ActionEnum action) throws Exception {
        if (action == ActionEnum.SCALE_OUT) {
//            scaleOut(id, vo.getMasterSize(), vo.getSpareSize(),vo.getManualPort(),vo.getPorts());
            scaleOut(id, vo.getMasterSize(), vo.getSpareSize());
        } else if (action == ActionEnum.SCALE_IN) {
            scaleDown(id, vo.getMasterSize(), vo.getSpareSize());
        }
    }

    private void scaleOut(int appId, Integer masterSize, Integer spareSize) throws Exception {
        CloudApp cloudApp = appService.get(appId);
        CustPreconditions.checkNotNull(appId, "appId not be null!");
        String namespace = cloudApp.getNamespace();
        String crName = cloudApp.getCrName();
        Integer kubeId = cloudApp.getKubeId();
        String serviceType = accessManagementService.usedServiceList(appId).get(0).getServiceType();
        Broker cr = kubeClientService.get(kubeId).listCustomResource(Broker.class, crName, namespace);
        Broker.IpListConfig[] ipArray = cr.getStatus().getSpec().getIpList();
        //获取当前实际的masterSize
        int curMasterSize = cr.getSpec().getMasterSize();
        // 获取当前实际的副本数
        int curSpareSize = (ipArray.length / curMasterSize) - 1;
        int newMember = masterSize * (spareSize + 1);
        int oldMember = curMasterSize * (curSpareSize + 1);
        int deltaMembers = newMember - oldMember;
        TriFunction<CloudApp, List<String>, Broker, Broker> modifier = (app, scaledIPs, current) -> {
            List<ServiceManager> serviceManagerList = new ArrayList<>();
            Broker.IpListConfig[] currentIpList = current.getSpec().getIpList();

            // 生成新增成员所需的 ServiceResource
            List serviceResourceList =
                    accessManagementService.genServiceResourceByAppKindMember(deltaMembers, getKind(), kubeId, serviceType);
            //扩容后的 iplist 数组
            int currentIpListLength = currentIpList.length;
            Broker.IpListConfig[] newIpLists = new Broker.IpListConfig[currentIpListLength + deltaMembers];
            System.arraycopy(ipArray, 0, newIpLists, 0, currentIpListLength);

            // 设置扩容节点的cr信息、lb/NodePort 信息、 ServiceManager 信息
            for (int i = 0; i < deltaMembers; i++) {
                String newIp = scaledIPs.get(i);
                Broker.IpListConfig newIpList = new Broker.IpListConfig();
                newIpList.setIp(newIp);

                // 初始化 ServiceManager 对象，broker 只有一个写 service
                ServiceManager serviceManager = new ServiceManager();
                serviceManager.setPurpose(CloudAppConstant.ServicePurpose.WRITE);
                serviceManager.setServiceName(getKind().getWriteServiceName(app.getCrName(), newIp));

                if (serviceType.equals(CloudAppConstant.ServiceType.NODE_PORT)) {
                    // 如果服务类型为 NodePort，则分配节点端口
                    int nodePort = (Integer) serviceResourceList.get(i);
                    newIpList.setPort(nodePort);
                    serviceManager.setPort(nodePort);
                } else if (serviceType.equals(CloudAppConstant.ServiceType.LOAD_BALANCER)) {
                    // 如果服务类型为 LoadBalancer，则分配负载均衡 IP
                    String lbip = String.valueOf(serviceResourceList.get(i));
                    newIpList.setExternalIP(lbip);
                    newIpList.setPort(getKind().getDbPort());
                    serviceManager.setExternalIp(lbip);
                }

                // 将新生成的服务管理器和 IP 列表添加到扩容后的 iplist 数组
                serviceManagerList.add(serviceManager);
                newIpLists[currentIpListLength + i] = newIpList;
            }
            //更新 cr 属性，设置新 iplist 和 masterSize
            current.getSpec().setIpList(newIpLists);
            current.getSpec().setMasterSize(masterSize);

            // 根据服务类型记录操作日志
            if (serviceType.equals(CloudAppConstant.ServiceType.NODE_PORT)) {
                OpLogContext.instance().PORT(serviceResourceList);
            } else if (serviceType.equals(CloudAppConstant.ServiceType.LOAD_BALANCER)) {
                OpLogContext.instance().LBIP(serviceResourceList);
            }

            // 插入新生成的服务资源到访问管理服务中
            accessManagementService.initServices(app, serviceManagerList, serviceType);

            return current;
        };

        operationHandler.handleScaleUp(
                appId, deltaMembers, Broker.class, this,
                getIpOwnerKind(), getIpReservationTarget(), modifier);
    }

    /**
     * 缩容
     * @param appId
     * @param masterSize 缩容后主实例数量
     * @param spareSize
     * @throws Exception
     */
    @Override
    public void scaleDown(int appId, Integer masterSize, Integer spareSize) throws Exception {
        CloudApp app = appService.get(appId);
        CustPreconditions.checkNotNull(appId, "appId not be null!");
        String crName = app.getCrName();
        String namespace = app.getNamespace();
        Broker cr = kubeClientService.get(app.getKubeId()).listCustomResource(Broker.class, crName, namespace);
        Broker.BrokerInstance[] brokerTopology = cr.getStatus().getBrokerTopology();
        Broker.IpListConfig[] ipArray = cr.getStatus().getSpec().getIpList();
        // 获取当前实际的副本数
        int finalSpareSizePerShard = spareSize / masterSize; // per shard

        List<Broker.BrokerInstance> finalMasterSizeBrokerList = Arrays.stream(brokerTopology)
                // 根据broker 的索引来保留不超过分片数的 broker
                .filter(brokerInstance -> brokerInstance.getBrokerIndex() <= masterSize - 1)
                .collect(Collectors.toList());
        // 最终保留的 broker 的 map，key：brokerIndex，value：List<brokerInstance>
        Map<Integer, List<Broker.BrokerInstance>> finalBrokerMap = new HashMap<>(
                masterSize * finalSpareSizePerShard * 2);
        for (Broker.BrokerInstance brokerInstance : finalMasterSizeBrokerList) {
            Integer brokerIndex = brokerInstance.getBrokerIndex();
            List<Broker.BrokerInstance> brokerInstances = finalBrokerMap.get(brokerIndex);
            // 根据每分片备库数来保留 broker
            if (brokerInstances == null) {
                finalBrokerMap.put(brokerIndex, new ArrayList<Broker.BrokerInstance>(){{add(brokerInstance);}});
            } else if(brokerInstances.size() <= finalSpareSizePerShard) {
                brokerInstances.add(brokerInstance);
            }
        }

        // 最终保留的 broker 的 podip 集合，用于 serviceManagerList 更新缩容掉的资源
        Set<String> finalIpStrings = finalBrokerMap.values().stream()
                .flatMap(List::stream).map(Broker.BrokerInstance::getIp).collect(Collectors.toSet());

        Map<String, Object> map = new HashMap<>();
        //4.挑选出要释放的 svm
        List<ServiceManager> serviceManagerList = accessManagementService.usedServiceList(appId);
        Set<String> serviceNameSet = finalIpStrings.stream()
                .map(ip -> getKind().getWriteServiceName(app.getCrName(), ip)).collect(Collectors.toSet());
        // 和最终 ip 的 servicename 进行对比，如果相同则移除
        serviceManagerList.removeIf(serviceManager -> serviceNameSet.contains(serviceManager.getServiceName()));
        map.put("scaleDownServiceManagerList", JsonUtil.toJson(serviceManagerList));

        Broker crBYApp = YamlEngine.unmarshal(app.getCr(), Broker.class);
        Broker.IpListConfig[] finalIpLists = Arrays.stream(ipArray)
                .filter(ip -> finalIpStrings.contains(ip.getIp()))
                .toArray(Broker.IpListConfig[]::new);
        crBYApp.getSpec().setIpList(finalIpLists);
        crBYApp.getSpec().setMasterSize(masterSize);
        String applyYaml = YamlEngine.marshal(crBYApp);

        //5. 记录操作，用于回滚
        OpLogContext.instance().YAML("CR", applyYaml, app.getCr());

        //6. 提交cr触发调度
        clientService.get(app.getKubeId()).updateCustomResource(crBYApp, Broker.class);
        appService.callScheduler(app, applyYaml, map,
                ActionEnum.SCALE_IN, getProcessorClass(ActionEnum.SCALE_IN));
    }

    @Override
    public void uninstall(int appId) throws Exception {
        operationHandler.handleUninstall(appId, getKind().getCrClass(), this);
    }

    @Override
    protected void completeInstanceProperty(AppInstanceVO appInstanceVO, PodDTO pod, CloudApp app, KubeClient client) {
        String brokerId = pod.getLabels().get("rocketmq.brokerId");
        if(!StringUtils.isBlank(brokerId)) {
            appInstanceVO.setRole("0".equals(brokerId) ? "primary" : "secondary");
        }
        appInstanceVO.setBrokerName(pod.getLabel("rocketmq.brokerName"));
    }

    @Override
    public CloudAppVO overrideSpec(CloudAppLogic logicApp, Integer kubeId, InstallAppVo<? extends OverrideSpec> vo) {
        CloudAppVO appVO = super.overrideSpec(logicApp, kubeId, vo);
        if (vo.getOverrideSpecs().get(kubeId) instanceof BrokerOverrideSpec) {
            BrokerOverrideSpec overrideSpec = (BrokerOverrideSpec) vo.getOverrideSpecs().get(kubeId);
            // todo 多中心考虑具体架构实现
            appVO.setNameserviceId(overrideSpec.getNameserviceId());
            appVO.setSpareSize(overrideSpec.getSpareSize());
            appVO.setMasterSize(overrideSpec.getMasterSize());
            appVO.setMembers(overrideSpec.getMasterSize() * (overrideSpec.getSpareSize() + 1));
        }
        return appVO;
    }

    @Override
    public InstallAppVo<? extends OverrideSpec> parseInstallVo(String data) {
        InstallAppVo<BrokerOverrideSpec> vo = JsonUtil.toObject(data, new TypeReference<InstallAppVo<BrokerOverrideSpec>>() {
        });
        if (vo != null) {
            if (vo.getSpec() != null && vo.getSpec().getMembers() == 0) {
                BrokerOverrideSpec spec = vo.getSpec();
                spec.setMembers(spec.getMasterSize() * (spec.getSpareSize() + 1));
            }
            if (!org.springframework.util.CollectionUtils.isEmpty(vo.getOverrideSpecs())) {
                for (BrokerOverrideSpec spec : vo.getOverrideSpecs().values()) {
                    if (spec.getMembers() == 0 && spec.getMasterSize() != null && spec.getSpareSize() != null)
                        spec.setMembers(spec.getMasterSize() * (spec.getSpareSize() + 1));
                }
            }
        }

        return vo;
    }

    @Override
    public List<ServiceManager> createService(
            String serviceType, CloudAppVO vo, List<?> serviceResources, CustomResource installCr) {
        //1.校验
        AppKind kind = getKind();
        if (serviceResources.isEmpty()) return Collections.emptyList();
        int serviceManagerNum = kind.getServiceManagerNum(serviceType, vo.getMembers());
        if (CollectionUtils.isEmpty(serviceResources) || serviceResources.size() != serviceManagerNum) {
            throw new CustomException(600, "节点端口类型必须指定 " + serviceManagerNum
                    + " 个端口, 实际数量为 " + serviceResources.size() + ", 类型为 " + serviceType);
        }
        //2 声明构建所需的变量
        List<ServiceManager> svms = new ArrayList<>();
        Broker cr = (Broker) installCr;
        Broker.IpListConfig[] ipLists = cr.getSpec().getIpList();
        int dbPort = getKind().getDbPort();
        for (int i = 0; i < serviceResources.size(); i++) {
            Object serviceResource = serviceResources.get(i);
            Broker.IpListConfig ipList = ipLists[i];
            Integer nodePort;

            //2.3 初始化 ServiceManager
            ServiceManager serviceManager = new ServiceManager();
            // 没有读写分离，且只需要一个 service
            serviceManager.setPurpose(CloudAppConstant.ServicePurpose.WRITE);
            serviceManager.setServiceName(getKind().getWriteServiceName(vo.getCrName(), ipList.getIp()));
            serviceManager.setServiceType(serviceType);

            //2.4 根据 ServiceType 填充 NodePort 和 lb
            if (CloudAppConstant.ServiceType.NODE_PORT.equals(serviceType)) {
                //nodeport 方式，serviceResources 结构为 List<Integer>，进行分配 NodePort，只需要一个 NodePort
                nodePort = (Integer) serviceResource;
                serviceManager.setPort(nodePort);
                ipList.setPort(nodePort);
            } else if (CloudAppConstant.ServiceType.LOAD_BALANCER.equals(serviceType)) {
                //lb 方式，serviceResources 结构为 List<String>，进行分配 lbip，只需要一个 lbip，端口为 dbport
                serviceManager.setPort(dbPort);
                String lbip = (String) serviceResource;
                serviceManager.setExternalIp(lbip);
                ipList.setExternalIP(lbip);
            }

            svms.add(serviceManager);
        }

        return svms;
    }

    @Override
    public void updateService(List<ServiceManager> svcMgrs, CloudApp app, Object oldServiceResource) throws Exception {
        ServiceManager serviceManager = svcMgrs.get(0);
        String serviceType = serviceManager.getServiceType();
        Map<String, String> data = new HashMap<>();
        data.put("oldServiceResource", oldServiceResource + "");
        Consumer<Broker> modifier = broker -> {
            cn.newdt.cloud.domain.cr.Broker.IpListConfig[] ipLists = broker.getSpec().getIpList();
            for (Broker.IpListConfig ipList : ipLists) {
                if (serviceType.equalsIgnoreCase(CloudAppConstant.ServiceType.NODE_PORT)) {
                    if (ipList.getPort().equals(oldServiceResource)) {
                        ipList.setPort(serviceManager.getPort());
                        break;
                    }
                } else if (serviceType.equalsIgnoreCase(CloudAppConstant.ServiceType.LOAD_BALANCER)) {
                    if (ipList.getExternalIP().equals(oldServiceResource)) {
                        ipList.setExternalIP(serviceManager.getExternalIp());
                        break;
                    }
                }
            }
        };
        operationHandler.handleService(
                app, svcMgrs, modifier, Broker.class, this, data, ActionEnum.UPDATE_SERVICE);
    }

    @Override
    public CloudApp referringApp(CloudApp app) {
        return appService.getAppByCrName(app.getCrName(), app.getNamespace(), AppKind.NameServer);
    }

    @Getter
    @Setter
    @ToString
    public static class BrokerOverrideSpec extends OverrideSpec {
        // broker安装依赖的namesever
        private Integer nameserviceId;
        private Integer masterSize;
        private Integer spareSize;
    }

    @Data
    public static class BrokerVO extends CloudAppVO {
        // broker安装依赖的namesever
        private Integer nameserviceId;
        private String nameserverName;
    }

    @Override
    public PageInfo<? extends CloudAppVO> searchPage(PageDTO page) {
        PageInfo<? extends CloudAppVO> pageInfo = super.searchPage(page);
        return PageUtil.page2PageInfo(pageInfo.getList(), BrokerVO.class, appVo -> {
            BrokerVO bVo = new BrokerVO();
            BeanUtils.copyProperties(appVo, bVo);

            String crYaml = StringUtils.isEmpty(appVo.getCr()) ? appVo.getCrRun() : appVo.getCr();;
            if (StringUtils.isNotEmpty(crYaml)) {
                Broker broker = YamlEngine.unmarshal(crYaml, Broker.class);
                if (broker!= null) {
                    bVo.setMasterSize(broker.getSpec().getMasterSize());
                }
                bVo.setSlaveCount(bVo.getMembers() - bVo.getMasterSize());
            }
            Map<String, Object> map = new HashMap<>();
            map.put("fullCrName", this.referringAppName(appVo));
            map.put("namespace", appVo.getNamespace());
            map.put("kubeId", appVo.getKubeId());
            map.put("kind", AppKind.NameServer.getKind());
            CloudApp nameserver = appService.findOne(map);
            if (nameserver != null) {
                bVo.setNameserviceId(nameserver.getId());
                bVo.setNameserverName(nameserver.getName());
            }
            return bVo;
        });
    }

    @Override
    public void modifyConfigParam(Map<String, String> params, Integer appId, String componentKind) throws Exception {
        CustPreconditions.checkState(!org.springframework.util.CollectionUtils.isEmpty(params), "要修改的参数列表为空");

        CloudApp app = appService.get(appId);
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        Broker crInK8s = kubeClient.listCustomResource(Broker.class, app.getCrName(), app.getNamespace());
        Broker crInDbStore = YamlEngine.unmarshal(app.getCr(), Broker.class);
        Map<String, String> config = crInK8s.getSpec().getConfig();

        if (config == null) {
            config = new HashMap<>();
        }
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String key = entry.getKey();
            String val = entry.getValue();
            this.prohibitParam(key);
            config.put(key, val);
        }

        crInK8s.getSpec().setConfig(config);
        crInDbStore.setSpec(crInK8s.getSpec());

        HashMap<Object, Object> map = new HashMap<>();
        map.put("params", params);
        map.put("appId", appId);
        appService.callScheduler(app, YamlEngine.marshal(crInDbStore), map, ActionEnum.MODIFY_PARAM, getProcessorClass(ActionEnum.MODIFY_PARAM));

        kubeClient.updateCustomResource(crInK8s, Broker.class);

    }

    @Override
    public String getAppSystemName(InstallAppVo<? extends OverrideSpec> vo) {
        return appService.getAppByCrName(vo.getCrName(), vo.getNamespace(), AppKind.NameServer).getAppSystemName();
    }

    private String getSecretName(String crName) {
        return crName + "-user";
    }
}
