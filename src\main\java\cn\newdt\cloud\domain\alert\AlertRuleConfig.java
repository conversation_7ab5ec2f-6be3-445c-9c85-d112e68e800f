package cn.newdt.cloud.domain.alert;

import cn.newdt.cloud.domain.alert.json.RuleSet;
import cn.newdt.cloud.utils.JsonUtil;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * entity of table cloud_alert_rule_config
 */
@Data
public class AlertRuleConfig {

    private Integer id;

    private Integer status;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String checksum;

    private String json;

    private String resourceType;

    private String resourceNamespace;

    private List<AlertRuleResource> instances;

    private String configName;
    private Integer ownerTenant;
    private Integer ownerUser;

    public RuleSet parseJson() {
        return JsonUtil.toObject(RuleSet.class, json);
    }

    public enum Status {
        Default(1),
        Disable(2),
        Visible(4),
        ;
        private final int code;

        Status(int code) {
            this.code = code;
        }

        public int getCode() {
            return code;
        }
    }

    private Map<String, Boolean> status_enum;

    private Map<String, Boolean> getStatus_enum() {
        if (status_enum == null && status != null) {
            Status[] values = Status.values();
            status_enum = new HashMap<>();
            for (Status value : values) {
                status_enum.put(value.name(), (value.code | status) == value.code);
            }
        }
        return status_enum;
    }
    public boolean defaultFlag() {
        return getStatus_enum().get(Status.Default.name());
    }

}