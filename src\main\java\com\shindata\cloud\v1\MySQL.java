package com.shindata.cloud.v1;

@io.fabric8.kubernetes.model.annotation.Version(value = "v1" , storage = true , served = true)
@io.fabric8.kubernetes.model.annotation.Group("cloud.shindata.com")
@io.fabric8.kubernetes.model.annotation.Singular("mysql")
@io.fabric8.kubernetes.model.annotation.Plural("mysqls")
public class MySQL extends io.fabric8.kubernetes.client.CustomResource<com.shindata.cloud.v1.MySQLSpec, com.shindata.cloud.v1.MySQLStatus> implements io.fabric8.kubernetes.api.model.Namespaced {
}

