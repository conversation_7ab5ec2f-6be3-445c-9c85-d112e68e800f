package cn.newdt.cloud.service.kubernetes_client;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.utils.ThreadUtil;
import com.shindata.mysql.v1.MySQLHA;
import io.fabric8.kubernetes.api.model.Pod;
import io.fabric8.kubernetes.client.DefaultKubernetesClient;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.fabric8.kubernetes.client.informers.ResourceEventHandler;
import io.fabric8.kubernetes.client.informers.SharedIndexInformer;
import io.fabric8.kubernetes.client.informers.SharedInformerFactory;
import io.fabric8.kubernetes.client.informers.cache.Cache;
import io.fabric8.kubernetes.client.informers.cache.Indexer;
import io.fabric8.kubernetes.client.informers.cache.Lister;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
public class MySQLHAInformerExample {
    public static void main(String[] args) throws InterruptedException {
        try (KubernetesClient client = new DefaultKubernetesClient()) {
            System.out.println("connected to " + client.getMasterUrl());
            SharedInformerFactory informerFactory = client.informers(ThreadUtil.getExecutor());
            // set resync to 0 to prevent informer receive event even resource version not updated
            SharedIndexInformer<MySQLHA> mySQLHASharedIndexInformer = informerFactory.sharedIndexInformerFor(MySQLHA.class, 0);

            mySQLHASharedIndexInformer.addEventHandler(
                new ResourceEventHandler<MySQLHA>() {
                    @Override
                    public void onAdd(MySQLHA obj) {
                        log.info("add action-{}. should read resource detail and sync it", obj.getMetadata().getName());
                    }

                    @Override
                    public void onUpdate(MySQLHA oldObj, MySQLHA newObj) {
                        log.info("update action-{}. should read resource detail and sync it", oldObj.getMetadata().getResourceVersion());
                    }

                    @Override
                    public void onDelete(MySQLHA obj, boolean deletedFinalStateUnknown) {
                        log.info("delete action. should sync with delete operation");
                    }
                }
            );

            // use lister with indexer.
            SharedIndexInformer<MySQLHA> anotherInformer = informerFactory.sharedIndexInformerFor(MySQLHA.class, 0);
            Indexer<MySQLHA> indexer = anotherInformer.getIndexer();
            Lister<MySQLHA> mySQLHALister = new Lister<>(indexer);

            Timer timer = new Timer();
            timer.scheduleAtFixedRate(new TimerTask() {
                @Override
                public void run() {
                    mySQLHALister.namespace("yan-test").list().stream().forEach(a -> {
                        System.out.println(a.getMetadata().getName() + ": " + a.getStatus().getState());
                    });
                }
            }, 3, 10000);

            // pod informer with filtering option
            String[] appKindNames = Arrays.stream(AppKind.values()).map(ak -> ak.getProduct().toLowerCase()).toArray(String[]::new);
            SharedIndexInformer<Pod> podInformer = client.pods().inAnyNamespace().withLabelIn(CloudAppConstant.CustomLabels.APP, appKindNames).inform();
            Lister<Pod> podLister = new Lister<>(podInformer.getIndexer());

//            new Timer().scheduleAtFixedRate(new TimerTask() {
//                @Override
//                public void run() {
//                    podLister.list().stream().forEach(p -> {
//                        System.out.println(p.getMetadata().getName() + ":" + p.getStatus().getPhase());
//                    });
//                }
//            }, 0, 30000);

            informerFactory.startAllRegisteredInformers();

        }

        Thread thread = new Thread();
        thread.setDaemon(true);
        thread.start();

    }
}
