package cn.newdt.cloud.utils;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.ResourceEnum;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.CloudSysConfig;
import cn.newdt.cloud.vo.MetricVO;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import io.fabric8.kubernetes.api.model.Container;
import io.fabric8.kubernetes.api.model.Pod;
import io.fabric8.kubernetes.api.model.Quantity;
import io.fabric8.kubernetes.api.model.ResourceRequirements;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.CharacterIterator;
import java.text.DecimalFormat;
import java.text.StringCharacterIterator;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class MetricUtil {


    public static final Pattern CPU_PATTERN = Pattern.compile("(\\d+(\\.\\d+)?)([cmμn]?)");

    //SI (1 k = 1,000)
    public static String humanReadableByteCountSI(long bytes) {
        if (-1000 < bytes && bytes < 1000) {
            return bytes + " B";
        }
        CharacterIterator ci = new StringCharacterIterator("kMGTPE");
        while (bytes <= -999_950 || bytes >= 999_950) {
            bytes /= 1000;
            ci.next();
        }
        return String.format("%.1f %cB", bytes / 1000.0, ci.current());
    }

    /**
     * Opposite operation with humanReadableByteCountSI
     */
    public static long getLongValueCountSI(String str) {
        if (StringUtils.isEmpty(str)) return 0;
        str = str.toUpperCase();
        String pattern = "(\\d+)(\\w*)";
        Matcher matcher = Pattern.compile(pattern).matcher(str);
        if (matcher.matches()) {
            String group = matcher.group(1);
            String c = matcher.group(2);
            long l = Long.parseLong(group);
            if (StringUtils.isEmpty(c)) return l;
            CharacterIterator ci = new StringCharacterIterator("KMGTPE");
            int endIndex = ci.getEndIndex();
            for (int i = 0; i <= endIndex; i++) {
                l *= 1000;
                if (ci.current() == c.charAt(0)) {
                    break;
                }
                ci.next();
            }
            return l;
        }
        return 0;
    }

    public static void main(String[] args) {
        System.out.println(MetricUtil.getLongValueCountSI("1M"));
        Quantity q = new Quantity("1234", "m");
        System.out.println(MetricUtil.getCpuTimeValueOfUnit(q.toString(), 'c'));
        String valueWithUnit = "1Gi";
        Matcher matcher = Pattern.compile("([KMGT])i?").matcher(valueWithUnit);
        System.out.println(matcher.find());
        System.out.println(matcher.group());
        String unit = MetricUtil.getUnit(valueWithUnit);
        System.out.println(unit);
        System.out.println(MetricUtil.getLongValueOfUnit(1287201920, 'G'));

        System.out.println(MetricUtil.formatCpu("500.6666m")); // 501m
        System.out.println(MetricUtil.formatCpu("1.5")); // 1500m
        System.out.println(MetricUtil.formatCpu("150m")); // 150m
        System.out.println(MetricUtil.formatCpu("2")); // 2
        System.out.println(MetricUtil.formatCpu("2c")); // 2

        System.out.println(new Quantity(MetricUtil.formatCpu("2")).toString());

    }

    //Binary (1 Ki = 1,024)
    public static String humanReadableByteCountBin(long bytes) {
        long absB = bytes == Long.MIN_VALUE ? Long.MAX_VALUE : Math.abs(bytes);
        if (absB < 1024) {
            return bytes + " B";
        }
        long value = absB;
        CharacterIterator ci = new StringCharacterIterator("KMGTPE");
        for (int i = 40; i >= 0 && absB > 0xfffccccccccccccL >> i; i -= 10) {
            value >>= 10;
            ci.next();
        }
        value *= Long.signum(bytes);
        return String.format("%.2f %ciB", value / 1024.0, ci.current());
    }

    public static String humanReadableCpuCount(int cpuMilliCores) {
        return new BigDecimal(cpuMilliCores)
                .divide(new BigDecimal(1000)).setScale(2, RoundingMode.HALF_UP)
                + "c";
    }
    /** 获取内存度量字符串(携带单位)的数值(以字节表示)*/
    public static long getLongValue(String valueWithUnit){
        if (StringUtils.isBlank(valueWithUnit)) return 0L;
        Matcher matcher = Pattern.compile("(\\d+(\\.\\d+)?)([KMGT])i?").matcher(valueWithUnit);

        double value = 0;
        char unit = 0;
        if (matcher.find()){
            value = Double.parseDouble(matcher.group(1));
            unit = matcher.group(3).charAt(0);
        }else {
            throw new IllegalArgumentException("metric value not match pattern ((\\d+(\\.\\d+)?))([KMGT]i?) ," + valueWithUnit);
        }
        CharacterIterator ci = new StringCharacterIterator("KMGTPE");
        for (int i = 0; i < ci.getEndIndex(); i++) {
            value *= 1024;

            if (unit == ci.current()) {
                break;
            }
            ci.next();
        }
        return new Double(value).longValue();
    }

    /**
     * 单位转换
     * @param originValueWithUnit 数值+单位, e.g. 123Mi
     * @param targetUnit 存储度量单位, [BKMGTPE]
     * @deprecated 向上转换可能丢失精度，如1500Mi -> 1Gi
     */
    public static Long getLongValueOfUnit(String originValueWithUnit, char targetUnit) {
        if (StringUtils.isEmpty(originValueWithUnit)) return null;
        double byteValue = getLongValue(originValueWithUnit);
        if (targetUnit == 'B') return (long) byteValue;
        CharacterIterator ci = new StringCharacterIterator("KMGTPE");
        for (int i = 0; i < ci.getEndIndex(); i++) {
            byteValue /= 1024;
            if (targetUnit == ci.current()) {
                break;
            }
            ci.next();
        }
        return (long) Math.round(byteValue);
    }


    public static String getLongValueOfUnit(long byteValue, char targetUnit) {
        if (targetUnit == 'B') return byteValue + "";
        double result = byteValue;
        CharacterIterator ci = new StringCharacterIterator("KMGTPE");
        for (int i = 0; i < ci.getEndIndex(); i++) {
            result /= 1024;
            if (targetUnit == ci.current()) {
                break;
            }
            ci.next();
        }
        return new DecimalFormat("#.##").format(result) + targetUnit + "iB";
    }

    public static String formatResource(String origin) {
        if (origin.contains(".") ) {
            Quantity quantity = new Quantity(origin);
            double i = Double.parseDouble(quantity.getAmount());
            if (i < 1 << 10)
            return downConversion(origin);
            else {
                return (long)Math.ceil(i) + quantity.getFormat();
            }
        } return origin;
    }

    /**
     * 存储单位向下转换
     */
    public static String downConversion(String originValueWithUnit) {
        if (StringUtils.isEmpty(originValueWithUnit)) return null;
        String unitAll = "BKMGTPE";
        Matcher matcher = Pattern.compile("(\\d+(\\.\\d+)?)([KMGT])i?").matcher(originValueWithUnit);
        if (matcher.matches()) {
            char originUnit = matcher.group(3).charAt(0);
            char targetUnit = unitAll.indexOf(originUnit) > 0 ? unitAll.charAt(unitAll.indexOf(originUnit) - 1)
                    : 'B'; // avoid outofbound
            return getLongValueOfUnit(originValueWithUnit, targetUnit) + (targetUnit + "i"); // fixme 每次除只保留了整数部分
        } else
            throw new IllegalArgumentException("not valid storage format");
    }

    public static Double getCpuTimeValueOfUnit(String valueWithUnit, char targetUnit) {
        if (StringUtils.isEmpty(valueWithUnit)) return null;
        Matcher matcher = CPU_PATTERN.matcher(valueWithUnit);
        double value = 0;
        char unit = 0;
        if (matcher.find()){
            value = Double.parseDouble(matcher.group(1));
            unit = StringUtils.isEmpty(matcher.group(3)) ? 'c' : matcher.group(3).charAt(0) ;
        }else {
            throw new IllegalArgumentException("cpu格式错误" + valueWithUnit);
        }

        CharacterIterator ci = new StringCharacterIterator("cmμn");
        int idx=0,idx_=0;
        for (int i = 0; i < ci.getEndIndex(); i++) {
            if (unit == ci.current()) {
                idx = i;
            }
            if (targetUnit == ci.current()) {
                idx_ = i;
            }
            ci.next();
        }

        int e = (idx_ - idx);
        value = Math.pow(1000, e) * value;
        return value;
    }



    public static Integer getCpuMilliCores(String valueWithUnit){
        if (StringUtils.isEmpty(valueWithUnit)) return null;
//        Matcher matcher = Pattern.compile("(\\d+\\.?\\d+)(\\S+)").matcher(valueWithUnit);
        Matcher matcher = CPU_PATTERN.matcher(valueWithUnit);
        double value = 0;
        String unit = "";
        if (matcher.find()){
            value = Double.parseDouble(matcher.group(1));
            unit = matcher.group(3);
        }else {
            throw new IllegalArgumentException("cpu格式错误" + valueWithUnit);
        }

        if (StringUtils.isEmpty(unit) || StringUtils.equalsIgnoreCase("c", unit)) {  //cpu  如果单位为c或者不带单位  （不带单位默认为C）
            value *= 1000;
        }

        return (int)value;
    }

    public static Double getCpuCores(String valueWithUnit){
        if (StringUtils.isEmpty(valueWithUnit)) return 0.0;
//        Matcher matcher = Pattern.compile("(\\d+\\.?\\d+)(\\S+)").matcher(valueWithUnit);
        Matcher matcher = Pattern.compile("(\\d+(\\.\\d+)?)([cm]?)").matcher(valueWithUnit);
        double value = 0;
        String unit = "";
        if (matcher.find()){
            value = Double.parseDouble(matcher.group(1));
            unit = matcher.group(3);
        }else {
            throw new IllegalArgumentException("cpu格式错误" + valueWithUnit);
        }
        if (StringUtils.equalsIgnoreCase("m", unit)) {
            value /= 1000;
        }

        return value;
    }

    public static String getCpuUnit(String valueWithUnit){
        Matcher matcher = Pattern.compile("(\\d+(\\.\\d+)?)([cm]?)").matcher(valueWithUnit);
        String unit = "";
        if (matcher.find()){
            unit = matcher.group(3);
        }else {
            throw new IllegalArgumentException("(\\d+(\\.\\d+)?)([cm]?) ," + valueWithUnit);
        }
        return unit;
    }

    public static double getCpuMilliCores(String val, String unit){
        double value = Double.parseDouble(val);
        if (StringUtils.equalsIgnoreCase("c", unit) ) {
            value *= 1000;
        }
        return value;
    }

    /** 转为百分数 */
    public static double formatToPercent(double v){
        return (double) Math.round(100 * v * 100) / 100;
    }

    /**
     * 内存和dist单位转换
     * @param resource
     * @return
     */
    public static long getResourceLongValue(String resource) {
        if (StringUtils.isEmpty(resource) || resource.equals("null")) {
            return 0l;
        }
        double ret;

        if (resource.endsWith("n")) {
            ret = Double.parseDouble(resource.replace("n", ""))* Math.pow(10, -9);
        } else if (resource.endsWith("u")) {
            ret = Double.parseDouble(resource.replace("u", "")) * Math.pow(10, -6);
        } else if (resource.endsWith("m")) {
            ret = Double.parseDouble(resource.replace("m", "")) * Math.pow(10, -3);
        } else if (resource.endsWith("k")) {
            ret = Double.parseDouble(resource.replace("k", "")) * Math.pow(10, 3);
        } else if (resource.endsWith("K")) {
            ret = Double.parseDouble(resource.replace("K", "")) * Math.pow(10, 3);
        } else if (resource.endsWith("M")) {
            ret = Double.parseDouble(resource.replace("M", "")) * Math.pow(10, 6);
        } else if (resource.endsWith("G")) {
            ret = Double.parseDouble(resource.replace("G", "")) * Math.pow(10, 9);
        } else if (resource.endsWith("T")) {
            ret = Double.parseDouble(resource.replace("T", "")) * Math.pow(10, 12);
        } else if (resource.endsWith("P")) {
            ret = Double.parseDouble(resource.replace("P", "")) * Math.pow(10, 15);
        } else if (resource.endsWith("E")) {
            ret = Double.parseDouble(resource.replace("E", "")) * Math.pow(10, 18);
        } else if (resource.endsWith("Ki")) {
            ret = Double.parseDouble(resource.replace("Ki", "")) * Math.pow(2, 10);
        } else if (resource.endsWith("Mi")) {
            ret = Double.parseDouble(resource.replace("Mi", "")) * Math.pow(2, 20);
        } else if (resource.endsWith("Gi")) {
            ret = Double.parseDouble(resource.replace("Gi", "")) * Math.pow(2, 30);
        } else if (resource.endsWith("Ti")) {
            ret = Double.parseDouble(resource.replace("Ti", "")) * Math.pow(2, 40);
        } else if (resource.endsWith("Pi")) {
            ret = Double.parseDouble(resource.replace("Pi", "")) * Math.pow(2, 50);
        } else if (resource.endsWith("Ei")) {
            ret = Double.parseDouble(resource.replace("Ei", "")) * Math.pow(2, 60);
        } else {
            ret = Double.parseDouble(resource);
        }
        return new Double(ret).longValue();
    }

    /**
     *
     * @param totalStr  资源总量
     * @param rateStr   使用比率  请求率/使用率/上限
     * @param baseStr   基数 百分比/千分比
     * @return
     */
    public static long getLongByRate(String totalStr, String rateStr, String baseStr){

        BigDecimal total = new BigDecimal(totalStr);
        BigDecimal rate = new BigDecimal(rateStr);
        BigDecimal base = new BigDecimal(baseStr);
        return total.multiply(rate).divide(base).longValue();
    }

    static Pattern pattern = Pattern.compile("\\d+(\\.\\d+)?");
    public static double getNumericPart(String value) {
        Matcher matcher = pattern.matcher(value);
        if (matcher.find()) {
            return Double.parseDouble(matcher.group());
        }
        return 0;
    }

    public static boolean endWithUnit(String valueWithUnit) {
        Matcher matcher = Pattern.compile("(\\d+(\\.\\d+)?)([KMGT])i?").matcher(valueWithUnit);
        return matcher.find();
    }

    public static String getUnit(String valueWithUnit) {
        Matcher m = Pattern.compile("([KMGT])i?$").matcher(valueWithUnit);
        if (m.find()){
            return m.group();
        } else {
            return "";
        }
    }

    public static Map<String, BigDecimal> computePodResourceRequest(Pod pod) {
        Map<String, BigDecimal> requests = new HashMap<>();
        for (Container container : pod.getSpec().getContainers()) {
            if (container.getResources().getRequests()!=null)
                for (Map.Entry<String, Quantity> stringQuantityEntry : container.getResources().getRequests().entrySet()) {
                    BigDecimal amount = Quantity.getAmountInBytes(stringQuantityEntry.getValue());
                    requests.compute(stringQuantityEntry.getKey(), (k,v) -> v==null?amount:v.add(amount));
                }
        }
        // see https://github.com/kubernetes/kubernetes/issues/44697#issuecomment-296116592
        // pod 资源申请量取 max(max(init 容器request), sum of other containers)
        for (Container initContainer : pod.getSpec().getInitContainers()) {
            if (initContainer.getResources().getRequests()!=null)
                for (Map.Entry<String, Quantity> stringQuantityEntry : initContainer.getResources().getRequests().entrySet()) {
                    BigDecimal amount = Quantity.getAmountInBytes(stringQuantityEntry.getValue());
                    requests.computeIfPresent(stringQuantityEntry.getKey(), (k,v)->amount.compareTo(v)>0?amount:v);
                }
        }
        return requests;
    }

    /**
     * 计算app实例资源申请值. 汇总所有容器和init容器
     */
    public static Map<String, BigDecimal> computePodResourceRequest(CloudApp app, String configYaml, List<CloudSysConfig> defaultRequests) {
        Pod pod = YamlEngine.unmarshal(configYaml, Pod.class);
        String dbContainer = AppKind.valueOf(app.getKind(), app.getArch()).getContainerName();
        String cpu = null, memory = null;
        for (CloudSysConfig limitRangeConfig : defaultRequests) {
            if (limitRangeConfig.getName().equals("container.request.cpu")) {
                cpu = limitRangeConfig.getData();
            } else if (limitRangeConfig.getName().equals("container.request.memory")) {
                memory = limitRangeConfig.getData();
            }
        }
        // 没有设置request的容器设为limitRange规定的默认值
        for (Container container : pod.getSpec().getContainers()) {
            ResourceRequirements resources = container.getResources();
            if (resources == null) {
                resources = new ResourceRequirements(null, new HashMap<>());
                container.setResources(resources);
            }
            Map<String, Quantity> requests = resources.getRequests();
            if (cpu != null) {
                requests.put(ResourceEnum.cpu.toString(), new Quantity(cpu));
            }
            if (memory != null) {
                requests.put(ResourceEnum.memory.toString(), new Quantity(memory));
            }
        }
        return computePodResourceRequest(pod);
    }

    /**
     * 格式化为echarts data
     * @param o Map K: timestamp, V: value;
     *          List[Map], Map K: series name, V: List[Entry], Entry{time,value}
     * @param type y轴名称
     */
    public static Object formatChartData(Object o, String type) {
        if (o instanceof Map) { // line
            String json = "{\"series\":[{\"type\":\"line\"}],\"dataset\":{\"source\":[],\"dimensions\":[\"timestamp\",\"" + type + "\"]}}";
            JSONObject data = JSONObject.parseObject(json);
            JSONArray source = data.getJSONObject("dataset").getJSONArray("source");
            Map<Object, Double> map = (Map<Object, Double>) o;
            List<Map> list = map.entrySet().stream().map(e -> {
                Map m = new HashMap();
                m.put("timestamp", DateUtil.format(Double.valueOf(e.getKey() + "").longValue() * 1000, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").withZone(ZoneId.systemDefault())));
                // 格式化为合适的单位,  一系列数据分别适合的单位不同.
                m.put(type, e.getValue());
                return m;
            }).collect(Collectors.toList());
            source.addAll(list);
            return data;
        } else if (o instanceof List) { // stacked line
            String timestampLabel = "timestamp";
            Map<String, String> seriesEntry = ImmutableMap.of("type", "line");
            List<String> dimensions = new ArrayList<>();
            dimensions.add(timestampLabel);
//          {"series":[{"type":"line"}],"dataset":{"source":[{series1: v1, series2: v2, ..., "timestamp":""}, ...],"dimensions":["timestamp","" + type + ""]}}";
            // [ { "pod-name", [{unix-time: , value: }, ...]}, ... ]
            List<List<MetricVO.RangeValueEntry>> genericClientResponse = (List<List<MetricVO.RangeValueEntry>>) o;
            Set<Long> timestampSet = new HashSet<>();
            List<Map<Long, MetricVO.RangeValueEntry>> groupByTimeEachSeries = genericClientResponse.stream().map(values -> {
                Map<Long, MetricVO.RangeValueEntry> timeMapData = new HashMap<>();
                for (MetricVO.RangeValueEntry value : values) {
                    timestampSet.add(value.getTime());
                    timeMapData.put(value.getTime(), value);
                }
                dimensions.add(values.get(0).getLabel());
                timestampSet.addAll(values.stream().map(v -> v.getTime()).collect(Collectors.toList()));
                return timeMapData;
            }).collect(Collectors.toList());
            // iterate time set, construct data.source series list
            // K: series name; V: value
            List<Map<String, Object>> chartSource = new TreeSet<>(timestampSet).stream().map(timestamp -> {
                Map<String, Object> seriesData = new HashMap();
                seriesData.put(timestampLabel, DateUtil.format(timestamp * 1000, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").withZone(ZoneId.systemDefault())));
                for (Map<Long, MetricVO.RangeValueEntry> groupByTime : groupByTimeEachSeries) {
                    MetricVO.RangeValueEntry rangeValueEntry = groupByTime.get(timestamp);
                    if (rangeValueEntry == null) continue; // 这个系列没有该时刻数据
                    seriesData.put(rangeValueEntry.getLabel(), rangeValueEntry.getValue());
                }
                return seriesData;
            }).collect(Collectors.toList());

            Map<String, Object> data = new HashMap<>();
            data.put("series", genericClientResponse.stream().map(e -> seriesEntry).collect(Collectors.toList()));
            data.put("dataset", ImmutableMap.of("source", chartSource, "dimensions", dimensions));
            return data;
        }
        return null;
    }

    public static boolean lessAndNotZero(String left, String right) {
        if (right == null) return false;
        if (left == null) return true;
        BigDecimal amountInBytes = Quantity.getAmountInBytes(new Quantity(right));
        return amountInBytes.compareTo(BigDecimal.ZERO) != 0 && Quantity.getAmountInBytes(new Quantity(left)).compareTo(amountInBytes) < 0;
    }

    public static String sumStorageWithUnit(String... items) {
        Long megabytes = Arrays.stream(items).map(item -> MetricUtil.getLongValueOfUnit(item, 'M'))
                .reduce(0l, Long::sum);
        String val = MetricUtil.humanReadableByteCountBin(megabytes * 1024 * 1024).replaceAll("[\\sB]", "");
        if (val.contains("."))
            val = downConversion(val);
        return val;

    }

    public static List<MetricSample> parseExporterMetric(@NotNull String metricsText, String name_filter) {
        Pattern metricPattern = Pattern.compile("^(\\w+)\\{(.*)\\}\\s+([\\d\\.]+)$");
        String[] lines = metricsText.split("\n");

        List<MetricSample> metricSampleList = new ArrayList<>();

        for (String line : lines) {
            if (line.startsWith("#") || line.trim().isEmpty()) {
                continue; // Skip comments and empty lines
            }
            Matcher matcher = metricPattern.matcher(line);
            if (matcher.matches()) {
                String name = matcher.group(1);
                if (!name.contains(name_filter))
                    continue;
                String labels = matcher.group(2);
                double value = Double.parseDouble(matcher.group(3));
                metricSampleList.add(new MetricSample(name, labels, value));
            }
        }
        return metricSampleList;
    }

    // format cpu to appropriate format: for double value
    public static String formatCpu(String cpuValue) {
        if (cpuValue == null || cpuValue.trim().isEmpty()) {
            return cpuValue;
        }

        Matcher matcher = CPU_PATTERN.matcher(cpuValue);
        if (!matcher.matches()) {
            return cpuValue; // Return original if doesn't match pattern
        }

        double numberPart = Double.parseDouble(matcher.group(1));
        String unitPart = matcher.group(3);
        try {
            double value = numberPart;

            if ("m".equals(unitPart)) {
                // Format values with "m" unit (millicores)
                return String.format("%dm", Math.round(value));
            } else {
                // For values without "m" unit
                if (value == (long) value) {
                    // If it's a whole number, return as-is without conversion
                    return String.format("%d", (long) value);
                } else {
                    // If it has decimal, convert to millicores
                    return String.format("%dm", Math.round(value * 1000));
                }
            }
        } catch (NumberFormatException e) {
            return cpuValue; // Return original if parsing fails
        }
    }

    @Data
    public static class MetricSample {
        private String name;
        private Map<String, String> labels;
        private double value;

        public MetricSample(String name, String labels, double value) {
            this.name = name;
            this.labels = parseLabels(labels);
            this.value = value;
        }

        private Map<String, String> parseLabels(String labels) {
            Map<String, String> labelMap = new HashMap<>();
            String[] pairs = labels.split(",");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=");
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim();
                    String value = keyValue[1].trim().replace("\"", ""); // Remove quotes from values
                    labelMap.put(key, value);
                }
            }
            return labelMap;
        }
    }
}