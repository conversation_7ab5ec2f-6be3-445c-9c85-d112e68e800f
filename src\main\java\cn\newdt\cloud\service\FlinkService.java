package cn.newdt.cloud.service;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.ImageKindEnum;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.dto.*;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.csi.CSIUtil;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.service.sched.impl.flink.FlinkDeploymentResourceWatch;
import cn.newdt.cloud.service.sched.impl.flink.FlinkInstallWatch;
import cn.newdt.cloud.service.sched.impl.flink.FlinkServiceWatch;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.*;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import io.fabric8.kubernetes.api.model.ConfigMap;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.client.CustomResource;
import io.fabric8.kubernetes.client.utils.Serialization;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.apache.flink.v1beta1.FlinkDeployment;
import org.apache.flink.v1beta1.FlinkDeploymentSpec;
import org.apache.flink.v1beta1.FlinkSessionJob;
import org.apache.flink.v1beta1.flinkdeploymentspec.JobManager;
import org.apache.flink.v1beta1.flinkdeploymentspec.TaskManager;
import org.apache.flink.v1beta1.flinkdeploymentspec.jobmanager.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.newdt.cloud.constant.ActionEnum.MIGRATE;
import static cn.newdt.cloud.constant.CloudAppConstant.LABEL_NODE_NAME;
import static cn.newdt.cloud.constant.CloudAppConstant.Operator.IN;
import static cn.newdt.cloud.constant.CloudAppConstant.StorageType.NAS;
import static cn.newdt.cloud.constant.CloudAppConstant.StorageType.S3;
import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgCategory.OPERATOR_CONFIG;
import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgCategory.OPERATOR_SERVICE;


@Service
public class FlinkService extends DefaultAppKindService<FlinkDeployment> implements ServiceManageOperation, MigrateOperation {

    @Autowired
    @Lazy
    private FlinkJobService flinkJobService;

    private final String DEFAULT_SA_NAME = "flink";
    private final String EMPTY_STORAGE = "1Gi";
    private final String JOB_MANAGER = "jobManager";
    private final String TASK_MANAGER = "taskManager";
    private final String FLINK_MAIN_CONTAINER = "flink-main-container";
    private final String CLUSTERIP_SVC = "apiVersion: v1\n" +
            "kind: Service\n" +
            "metadata:\n" +
            "  name: flink-${crname}\n" +
            "  labels:\n" +
            "    app.kubernetes.io/app: flink\n" +
            "  ownerReferences:\n" +
            "  - apiVersion: ${flink_gv}\n" +
            "    kind: FlinkDeployment\n" +
            "    blockOwnerDeletion: false\n" +
            "    controller: false\n" +
            "    name: ${crname}\n" +
            "    uid: ${cr_uid}\n" +
            "spec:\n" +
            "  ports:\n" +
            "  - name: exporter\n" +
            "    port: 9249\n" +
            "    targetPort: 9249\n" +
            "  type: ClusterIP\n" +
            "  selector:\n" +
            "    app.kubernetes.io/app: flink\n" +
            "    app.kubernetes.io/name: ${crname}";

    private final String ENTRYPOINT_SH = "#!/usr/bin/env bash\n" +
            "args=$@\n" +
            "libdir=/opt/flink/usr/lib/flink\n" +
            "cp -r $libdir/* /opt/flink/\n" +
            "chown -R flink:flink /opt/flink/lib /opt/flink/plugins/s3-*\n" +
            "#chmod -R 644 /opt/flink/lib /opt/flink/plugins/s3-*\n" +
            "/docker-entrypoint.sh $@";

    private final String filebeatContainerYaml = "" +
            "args:\n" +
            "- -c\n" +
            "- filebeat -c /etc/filebeat/filebeat.yml\n" +
            "command:\n" +
            "- /bin/sh\n" +
            "env:\n" +
            "- name: APP_NAMESPACE\n" +
            "  valueFrom:\n" +
            "    fieldRef:\n" +
            "      apiVersion: v1\n" +
            "      fieldPath: metadata.namespace\n" +
            "- name: APP_NAME\n" +
            "  valueFrom:\n" +
            "    fieldRef:\n" +
            "      apiVersion: v1\n" +
            "      fieldPath: metadata.labels['app']\n" +
            "- name: COMPONENT_NAME\n" +
            "  valueFrom:\n" +
            "    fieldRef:\n" +
            "      apiVersion: v1\n" +
            "      fieldPath: metadata.labels['component']\n" + // used int the filebeat config file
            "- name: APP_TYPE\n" +
            "  value: flink\n" +
            "- name: POD_IP\n" +
            "  valueFrom:\n" +
            "    fieldRef:\n" +
            "      apiVersion: v1\n" +
            "      fieldPath: status.podIP\n" +
            "- name: POD_NAME\n" +
            "  valueFrom:\n" +
            "    fieldRef:\n" +
            "      apiVersion: v1\n" +
            "      fieldPath: metadata.name\n" +
            "imagePullPolicy: IfNotPresent\n" +
            "name: filebeat\n" +
//            "resources:\n" +
//            "  limits:\n" +
//            "    cpu: 100m\n" +
//            "    memory: 200Mi\n" +
//            "  requests:\n" +
//            "    cpu: 50m\n" +
//            "    memory: 50Mi\n" +
//            "securityContext:\n" +
//            "  runAsUser: 0\n" +
            "volumeMounts:\n" +
            "- mountPath: /opt/flink/log\n" +
            "  name: log-volume\n" +
            "- mountPath: /etc/filebeat/\n" +
            "  name: flink-filebeat\n" +
            "- mountPath: /etc/localtime\n" +
            "  name: timezone"
            ;

    @Override
    public AppKind getKind() {
        return AppKind.Flink;
    }

    @Override
    public boolean nodePolicy() {
        return true;
    }

    @Override
    public Class<? extends OpsPostProcessor> getProcessorClass(ActionEnum action) {
        switch (action) {
            case CREATE:
                return FlinkInstallWatch.class;
            case UPDATE:
            case SCALE_OUT:
            case SCALE_IN:
            case DELETE:
            case UPGRADE:
            case MODIFY_PARAM:
                return FlinkDeploymentResourceWatch.class;
            case CREATE_SERVICE:
            case UPDATE_SERVICE:
            case DELETE_SERVICE:
                return FlinkServiceWatch.class;
            default:
                return super.getProcessorClass(action);
        }

    }

    @Override
    protected boolean supportIPAM(CloudAppVO app) {
        return false;
    }

    public void extendSetup(CloudAppVO app) {
        FlinkVO flinkVO = (FlinkVO) app;
        // flink operator 存储大小 不支持小数
        flinkVO.setMasterMemory(hackMemory(flinkVO.getMasterMemory()));
        flinkVO.setDataMemory(hackMemory(flinkVO.getDataMemory()));
    }

    private String hackMemory(String masterMemory) {
        if (masterMemory.contains(".")) {
            return MetricUtil.downConversion(masterMemory);
        } return masterMemory;
    }

    @Override
    public FlinkDeployment doInstall(CloudAppVO app, List<String> ips) throws Exception {
        Map<ImageKindEnum, String> imageKindEnumStringMap = app.getImageConfig();

        String namespace = app.getNamespace();
        String crName = app.getCrName();
        Map<String, String> config = setupDbParamConfig(app); // 包含默认参数，见paramformula
        FlinkDeployment flink;

        FlinkDeploymentSpec spec = new FlinkDeploymentSpec();
        spec.setFlinkVersion(toFlinkVersion(app.getVersion()));
        spec.setImage(imageKindEnumStringMap.get(ImageKindEnum.MainImage));
        spec.setMode(FlinkDeploymentSpec.Mode.STANDALONE);
        spec.setServiceAccount(DEFAULT_SA_NAME);

        GenericSpec.ComponentList componentList = getComponentList(app);
        GenericSpec.ComponentSpec jobManagerSpec = componentList.namedComponent(JOB_MANAGER);
        GenericSpec.ComponentSpec taskManagerSpec = componentList.namedComponent(TASK_MANAGER);
        JobManager jobManager = getJobManager(jobManagerSpec);
        spec.setJobManager(jobManager);
        TaskManager taskManager = getTaskManager(taskManagerSpec);
        spec.setTaskManager(taskManager);
        spec.setFlinkConfiguration(config);
        spec.setPodTemplate(getPodTemplate(crName, app, imageKindEnumStringMap));

        // 存储配置，对于Flink各个节点需要访问同一份数据，因此需要使用共享存储
        // NAS: mount nfs volume, 手动创建nfs子路径，格式: <kind>/<arch>/<namespace>/<name>
        // S3:  copy s3 plugin to plugin directory
        // 根据存储类型配置以下参数: state.savepoints.dir, state.checkpoints.dir, high-availability.storageDir
        new StorageConfigurator(app, imageKindEnumStringMap)
                .configure(spec.getPodTemplate(), config);

        flink = new FlinkDeployment();
        flink.setMetadata(new ObjectMetaBuilder().withName(crName).withNamespace(namespace).build());
        flink.setSpec(spec);

        return flink;
    }

    @Override
    public void update(Integer id, OverrideSpec overrideSpec) throws Exception{

        if (overrideSpec instanceof FlinkService.FlinkOverrideSpec) {
            FlinkResourceDTO patch = new FlinkResourceDTO();
            FlinkService.FlinkOverrideSpec updateSpec = (FlinkService.FlinkOverrideSpec) overrideSpec;
            patch.setMasterCpu(updateSpec.getMasterCpu());
            patch.setDataCpu(updateSpec.getDataCpu());
            patch.setMasterMemory(updateSpec.getMasterMemory());
            patch.setDataMemory(updateSpec.getDataMemory());
            patch.setAppId(id);
            update(patch);
        }
    }

    @Override
    public void update(ResourceDTO patch) throws Exception {
        FlinkResourceDTO flinkResourceDTO = (FlinkResourceDTO) patch;
        Consumer<FlinkDeployment> modifier = (cr) -> {
            Resource jobManager = new Resource();
            jobManager.setCpu(MetricUtil.getCpuCores(flinkResourceDTO.getMasterCpu()));
            jobManager.setMemory(flinkResourceDTO.getMasterMemory());
            cr.getSpec().getJobManager().setResource(jobManager);

            org.apache.flink.v1beta1.flinkdeploymentspec.taskmanager.Resource taskManager =
                    new org.apache.flink.v1beta1.flinkdeploymentspec.taskmanager.Resource();
            taskManager.setCpu(MetricUtil.getCpuCores(flinkResourceDTO.getDataCpu()));
            taskManager.setMemory(flinkResourceDTO.getDataMemory());
            cr.getSpec().getTaskManager().setResource(taskManager);
        };
        operationHandler.handleUpdate(patch, modifier, this, FlinkDeployment.class, null);
    }

    @Override
    public void scale(int appId, OverrideSpec overrideSpec, ActionEnum actionEnum) throws Exception {
        BiConsumer<CloudApp, FlinkDeployment> modifier = (app, flink) -> {
            FlinkOverrideSpec flinkOverrideSpec = (FlinkOverrideSpec) overrideSpec;
            flink.getSpec().getJobManager().setReplicas(Long.valueOf(flinkOverrideSpec.getMasterSize()));
            flink.getSpec().getTaskManager().setReplicas(Long.valueOf(flinkOverrideSpec.getDataSize()));
        };
        operationHandler.handleScale(appId, FlinkDeployment.class, this, modifier, actionEnum);
    }

    @Override
    public void upgrade(int appid, String version) throws Exception {
        BiFunction<Map<ImageKindEnum, String>, FlinkDeployment, FlinkDeployment> modifier =
                (images, deployedCR) -> {
                    deployedCR.getSpec().setImage(images.get(ImageKindEnum.MainImage));
                    deployedCR.getSpec().setFlinkVersion(toFlinkVersion(version));
                    return deployedCR;
                };
        operationHandler.handleUpgrade(appid, version, modifier, FlinkDeployment.class, this);
    }

    @Override
    public void modifyConfigParam(Map<String, String> params, Integer appId, String componentKind) throws Exception {
        BiConsumer<FlinkDeployment, Function<Map<String, String>, Map<String, String>>> consumer = (cr, func) -> {
            cr.getSpec().setFlinkConfiguration(func.apply(cr.getSpec().getFlinkConfiguration()));
        };
        operationHandler.handleModifyParam(appId, params, FlinkDeployment.class, this, consumer);
    }

    @Override
    public String migrate(MigrateDTO migrateDTO) throws Exception {
        Integer appId = migrateDTO.getAppId();
        CloudApp app = appService.get(appId);
        KubeClient kubeClient = clientService.get(app.getKubeId());

        FlinkDeployment deployedCR = kubeClient.listCustomResource(FlinkDeployment.class, app.getCrName(), app.getNamespace());
        BiFunction<MigrateDTO.MigrateNode, CloudApp, FlinkDeployment> modifer = (migrateNode, cloudApp) -> {
            FlinkDeployment cr = YamlEngine.unmarshal(app.getCr(), FlinkDeployment.class);
            cr.setSpec(deployedCR.getSpec());
            cr.getSpec().getPodTemplate().getSpec().setAffinity(
                    convertCRNodeAffinity(new SelectorDTO[]{SelectorDTO.builder().key(LABEL_NODE_NAME)
                                    .operator(IN).values(Collections.singletonList(migrateNode.getNewNode().getNode())).build()},
                            Affinity.class
                    ));

            return cr;
        };

        operationHandler.handleMigrate(
                migrateDTO,
                modifer,
                getProcessorClass(MIGRATE), null,
                getIpOwnerKind(),
                getIpReservationTarget(),
                FlinkDeployment.class,
                kubeClient,
                ImmutableMap.of(CloudAppConstant.OPTS_ADDITIONAL_KEY_LASTUPDATETIME,
                        deployedCR.getStatus().getReconciliationStatus().getReconciliationTimestamp()));
        return ActionEnum.MIGRATE.getAppOperation();
    }

    @Override
    public void deletePerm(CloudApp app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        KubernetesResourceList<FlinkSessionJob> flinkSessionJobKubernetesResourceList = kubeClient
                .listCustomResource(FlinkSessionJob.class, app.getNamespace(), getKind().labels(app.getCrName()));
        flinkSessionJobKubernetesResourceList.getItems().parallelStream()
                .forEach(job -> {
                    kubeClient.deleteCustomResource(job, FlinkSessionJob.class);
                });
        super.deletePerm(app);
    }

    @Override
    protected void validateStorageClass(CloudAppVO app) {
        CloudBackupStorageVO currentStorageVO =
                resourceManagerService.listBackupStorage(); // todo not related to backup storage

        String csiType = app.getCsiType();
        if (StringUtils.isEmpty(csiType)) {
            csiType = currentStorageVO.getStorageType();
            app.setCsiType(csiType);
        }
        CustPreconditions.checkState(csiType.equalsIgnoreCase(NAS) ||
                        csiType.equalsIgnoreCase(S3),
                "存储类型配置错误");
    }

    private String getSubDirNAS(CloudApp app) {
        return String.format("/flink-data/%s/%s/%s/%s",
                app.getKind().toLowerCase(),
                app.getArch().toLowerCase(),
                app.getNamespace(),
                app.getCrName());
    }

    @Override
    protected void createCrControlResource(FlinkDeployment cr, CloudAppVO vo) {

        String crName = vo.getCrName();
        String namespace = vo.getNamespace();
        KubeClient client = clientService.get(vo.getKubeId());

        List<CompletableFuture> all = new ArrayList<>();

        // todo add ownerreference
        // create flink exporter svc
        all.add(CompletableFuture.runAsync(() -> {
            client.applyYaml(new StringSubstitutor(ImmutableMap.of(
                    "crname", crName,
                    "flink_gv", crName,
                    "cr_uid", cr.getMetadata().getUid()
                    )).replace(CLUSTERIP_SVC), namespace);
        }));

        // add sql file store(config map)
        String configMapName = FlinkUtil.getSqlConfigMapName(crName);
        all.add(CompletableFuture.runAsync(() -> {
            ConfigMap cm = new ConfigMap();
            ObjectMeta objectMeta = new ObjectMeta();
            Map<String, String> labelMap = Arrays.stream(AppKind.Flink.labels(crName))
                    .collect(Collectors.toMap(Label::getName, Label::getValue));
            objectMeta.setLabels(labelMap);
            objectMeta.setName(configMapName);
            objectMeta.setOwnerReferences(Collections.singletonList(new OwnerReferenceBuilder()
                    .withApiVersion(cr.getApiVersion())
                    .withUid(cr.getMetadata().getUid())
                    .withKind(cr.getKind())
                    .withName(crName)
                    .withController(false)
                    .withBlockOwnerDeletion(false)
                    .build()));
            cm.setMetadata(objectMeta);
            cm.setData(ImmutableMap.of(
                    "docker-entrypoint.sh",
                    ENTRYPOINT_SH
            ));
            client.applyConfigMap(cm, namespace);
        }));

        // create filebeat configMap
        all.add(CompletableFuture.runAsync(() -> createFilebeatCM(crName, namespace, client)));

        // create rbac (serviceaccount: flink)
        all.add(CompletableFuture.runAsync(() ->
                client.applyYaml(new StringSubstitutor(ImmutableMap.of("namespace", namespace))
                        .replace(getRbacYaml()), namespace)));
        CompletableFuture.allOf(all.toArray(new CompletableFuture[0])).join();

    }

    @Override
    public void deleteCrControlledResources(CloudApp app) {
        String crName = app.getCrName();
        String namespace = app.getNamespace();
        KubeClient client = clientService.get(app.getKubeId());


    }

    private String getRbacYaml() {
        return sysConfigService.findOne(OPERATOR_CONFIG, "Flink.rbac");
    }

    private void createPVC(CloudAppVO vo, String crName, String namespace, KubeClient client) {
        String pvcName = getPvcName(crName);
        if (client.getPvc(namespace, pvcName) == null) {
            client.createPvc(new PersistentVolumeClaimBuilder().withNewMetadata()
                    .withName(pvcName).withNamespace(namespace)
                    .withLabels(Label.toMap(getKind().labelOfPod(vo)))
                    .endMetadata().withNewSpec()
                    .withStorageClassName(vo.getStorageClassName())
                    .withAccessModes("ReadWriteOnce")
                    .withNewResources()
                    .withRequests(ImmutableMap.of("storage", new Quantity(EMPTY_STORAGE)))
                    .endResources()
                    .endSpec().build(), namespace);
        }
    }

    private void createFilebeatCM(String crName, String namespace, KubeClient client) {
        client.applyYaml(
                YamlUtil.evaluateTemplate(getFilebeatContainerYaml(), ImmutableMap.<String, String>builder()
                        .put("namespace", namespace)
                        .put("name", crName)
                        .put("es_host", esUtil.getEsIp() + ":" + esUtil.getEsPort())
                        .put("es_username", esUtil.getEsUsername())
                        .put("es_pwd", esUtil.getEsPassword())
                        .put("es_protocol", esUtil.getProtocol()).build()
                ));
    }

    private GenericSpec.ComponentList getComponentList(CloudAppVO app) {
        FlinkVO vo = (FlinkVO) app;
        GenericSpec.ComponentSpec job = new GenericSpec.ComponentSpec();
        job.setReplicas(vo.getMasterSize());
        job.setLimits(ImmutableMap.of("cpu", vo.getMasterCpu(), "memory", vo.getMasterMemory()));
        job.setName(JOB_MANAGER);
        GenericSpec.ComponentSpec task = new GenericSpec.ComponentSpec();
        task.setReplicas(vo.getDataSize());
        task.setLimits(ImmutableMap.of("cpu", vo.getDataCpu(), "memory", vo.getDataMemory()));
        task.setName(TASK_MANAGER);
        GenericSpec.ComponentList componentList = new GenericSpec.ComponentList();
        componentList.add(job);
        componentList.add(task);
        return componentList;
    }

    protected String[] getStsOrDeployNames(CloudApp app) {
        return new String[]{
                app.getCrName(),
                app.getCrName() + "-taskmanager"
        };
    }

    @Override
    public void delete(CloudApp app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        String[] names = getStsOrDeployNames(app);
        if (names == null) throw new RuntimeException("get name flink deploy name failed");
        for (String name : names) {
            kubeClient.scaleDeploy(app.getNamespace(), name, 0);
        }
    }

    @Override
    public void recreate(CloudApp app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        String[] names = getStsOrDeployNames(app);
        if (names == null) throw new RuntimeException("get name flink deploy name failed");
        for (String name : names) {
            kubeClient.deleteDeployment(name, app.getNamespace());
        }
    }

    @Override
    public List<ServiceManager> createService(
            String serviceType, CloudAppVO vo, List<?> serviceResources, CustomResource installCr) {
        return openSourceKindServiceBuilder(serviceType, vo, serviceResources, null);
    }

    private String replaceSvcYaml(CloudApp app, Integer port, String svcName, String serviceYaml) {
        Function<Label, String> labelStringFunction = lb -> lb.getName() + ": " + lb.getValue();
        List<Label> list = Arrays.asList(getKind().labelOfPod(app));
        return new StringSubstitutor(ImmutableMap.<String, Object>of(
                "name", svcName,
                "namespace", app.getNamespace(),
                "nodePort", port + "",
                "selectors",
                        Stream.concat(list.stream(), Stream.of(
                            new Label("component", "jobmanager"))// svc expose jobmanager rest endpoint
                        ).map(labelStringFunction).collect(Collectors.joining("\n    ")),
                "labels", String.join("\n    ",
                        list.stream().map(labelStringFunction).collect(Collectors.toList()))
        )).replace(serviceYaml);
    }

    @Override
    public PageInfo<? extends CloudAppVO> searchPage(PageDTO page) {
        PageInfo<? extends CloudAppVO> pageInfo = super.searchPage(page);
        return PageUtil.page2PageInfo(pageInfo.getList(), FlinkVO.class, (appVO -> {
            FlinkVO flinkVO = new FlinkVO();
            BeanUtils.copyProperties(appVO, flinkVO);
            FlinkOverrideSpec overrideSpec = (FlinkOverrideSpec) reviewSpec(appVO);
            flinkVO.setDataSize(overrideSpec.getDataSize());
            flinkVO.setMasterSize(overrideSpec.getMasterSize());
            flinkVO.setDataCpu(overrideSpec.getDataCpu());
            flinkVO.setDataMemory(overrideSpec.getDataMemory());
            flinkVO.setMasterCpu(overrideSpec.getMasterCpu());
            flinkVO.setMasterMemory(overrideSpec.getMasterMemory());
            return flinkVO;
        }));
    }

    @Override
    protected void completeInstanceProperty(AppInstanceVO appInstanceVO, PodDTO pod, CloudApp app, KubeClient client) {
        appInstanceVO.setRole(pod.getLabel("component"));
    }

    @Override
    public void updateService(List<ServiceManager> svcMgrs, CloudApp app, Object oldServiceResource) throws Exception {
        openSourceKindUpdateServiceBuilder(svcMgrs, app, oldServiceResource);
    }

    private String getSvcYaml() {
        return sysConfigService.findOne(OPERATOR_SERVICE, "Flink.service");
    }


    @Getter
    @Setter
    @ToString
    public static class FlinkOverrideSpec extends OverrideSpec {
        private Integer masterSize;
        private Integer dataSize;
        private String dataCpu;
        private String dataMemory;
        private String masterCpu;
        private String masterMemory;

        public String getCpu() {
            return masterCpu;
        }

        public String getMemory() {
            return masterMemory;
        }

    }

    @Getter
    @Setter
    public static class FlinkVO extends CloudAppVO {
        /**
         * 分片个数
         */
        private Integer masterSize;

        /**
         * 备库个数
         */
        private Integer dataSize;

        /**
         * 数据节点cpu
         */
        private String dataCpu;

        /**
         * 数据节点memory
         */
        private String dataMemory;

        /**
         * master节点cpu
         */
        private String masterCpu;

        /**
         * master节点memory
         */
        private String masterMemory;

    }


    // flink support storage type: NAS or S3
    // this tempmlate will merge to both jobmanager and taskmanager
    private Pod getPodTemplate(String crName, CloudAppVO appVO, Map<ImageKindEnum, String> imageKindEnumStringMap) {
        PodBuilder podBuilder = new PodBuilder();
        podBuilder.withMetadata(new ObjectMetaBuilder().withLabels(Label.toMap(getKind().labelOfPod(appVO))).build());

        PodFluent.SpecNested<PodBuilder> podBuilderSpecNested = podBuilder.withNewSpec();

        // add containers
        String sqlVolumeName = "sql-volume"; // 通过configMap动态映射用户提交的sql
        String logVolumeName = "log-volume";
        String libVolumeName = "extlib";

        // copy prom reporter jar, connector jars & plugin jars
        String mainCommand = "cp -r /opt/flink/usr/lib/flink/* /opt/flink/ && /docker-entrypoint.sh ";
        Container main = new ContainerBuilder().withName(FLINK_MAIN_CONTAINER)
                // the command will be ignored by config kubernetes.entry.path, see
                // https://nightlies.apache.org/flink/flink-docs-master/docs/deployment/config/#kubernetes-entry-path
//                .withCommand("bash")
//                .withArgs("-c", mainCommand)
                .withVolumeMounts(
                       new ArrayList<>(
                               ImmutableList.of(
                                       new VolumeMountBuilder().withName(logVolumeName)
                                               .withMountPath("/opt/flink/log").build(),
                                       new VolumeMountBuilder().withName(sqlVolumeName)
                                               .withMountPath(FlinkUtil.getScriptPath()).build(),
                                       new VolumeMountBuilder().withName(libVolumeName)
                                               .withMountPath("/opt/flink/usr/lib").build(),
                                       new VolumeMountBuilder().withName("timezone")
                                               .withMountPath("/usr/share/zoneinfo/Asia/Shanghai").build()
                               )
                        )
                ).build();

        Container filebeat = YamlEngine.unmarshal(filebeatContainerYaml, Container.class);
        filebeat.setImage(imageKindEnumStringMap.get(ImageKindEnum.Filebeat));
        podBuilderSpecNested.addToContainers(main, filebeat);

        podBuilderSpecNested.addToInitContainers(new ContainerBuilder()
                .withName("flink-prepare")
                .withImage(imageKindEnumStringMap.get(ImageKindEnum.Other))
                .withVolumeMounts(new ArrayList<>(ImmutableList.of(
                        new VolumeMountBuilder().withName(libVolumeName)
                                .withMountPath("/opt/flink/usr/lib").build()
                )))
                .withCommand("sh", "-c")
                .withArgs("cp -r /flink /opt/flink/usr/lib")
                .build());

        // add volumes
        podBuilderSpecNested
                .addToVolumes(new VolumeBuilder().withName(logVolumeName) // fixed name
                        .withNewEmptyDir()
                        .withAdditionalProperties(Collections.emptyMap())
                        .endEmptyDir()
                        .build())
                .addToVolumes(new VolumeBuilder()
                        .withName(sqlVolumeName)
                        .withNewConfigMap().withName(FlinkUtil.getSqlConfigMapName(crName))
                        .withDefaultMode(511)
                        .endConfigMap()
                        .build())
                .addToVolumes(new VolumeBuilder()
                        .withName(libVolumeName)
                        .withNewEmptyDir()
                        .withAdditionalProperties(Collections.emptyMap())
                        .endEmptyDir()
                        .build())
                .addToVolumes(new VolumeBuilder().withName("flink-filebeat")
                        .withNewConfigMap().withName("flink-filebeat").endConfigMap()
                        .build())
                .addToVolumes(new VolumeBuilder().withName("timezone")
                        .withNewHostPath("/usr/share/zoneinfo/Asia/Shanghai", "")
                        .build());

        // add tolerance and node-affinity
        KubeScheduler kubeScheduler = kubeSchedulerService.queryById(appVO.getKubeSchedulerId());
        if (kubeScheduler != null) {
            Affinity k8sAffinity = kubeScheduler.toK8sAffinity(appVO);
            List<Toleration> k8sTolerance = kubeScheduler.toK8sTolerance();
            podBuilderSpecNested
                    .withAffinity(k8sAffinity)
                    .withTolerations(k8sTolerance);
        }

        return podBuilderSpecNested.endSpec().build();
    }


    private String getFilebeatContainerYaml() {
        return sysConfigService.findOne(OPERATOR_CONFIG, "Flink.config");
    }

    private String getPvcName(String crName) {
        return "flink-" + crName + "-data-volume";
    }

    private boolean isNAS(String csiType) {
        return CSIUtil.isNAS(csiType);
    }

    private JobManager getJobManager(GenericSpec.ComponentSpec jobManagerSpec) {
        JobManager jobManager = new JobManager();
        jobManager.setReplicas((long) jobManagerSpec.getReplicas());
        Resource resource = new Resource();
        resource.setCpu(MetricUtil.getCpuCores(jobManagerSpec.getLimits().get("cpu")));
        resource.setMemory(jobManagerSpec.getLimits().get("memory"));
        jobManager.setResource(resource);
        PodBuilder podBuilder = new PodBuilder();
        podBuilder.withNewSpec()
                .withContainers(new ContainerBuilder()
                        .withName(FLINK_MAIN_CONTAINER)
                        .withResources(ResourceHelper.getInstance().resourceRequirements(jobManagerSpec.getLimits()))
                        .build())
                .endSpec();
        jobManager.setPodTemplate(podBuilder.build());
        return jobManager;
    }

    private TaskManager getTaskManager(GenericSpec.ComponentSpec spec) {
        TaskManager taskManager = new TaskManager();
        taskManager.setReplicas((long) spec.getReplicas());
        org.apache.flink.v1beta1.flinkdeploymentspec.taskmanager.Resource resource =
                new org.apache.flink.v1beta1.flinkdeploymentspec.taskmanager.Resource();
        resource.setCpu(MetricUtil.getCpuCores(spec.getLimits().get("cpu")));
        resource.setMemory(spec.getLimits().get("memory"));
        taskManager.setResource(resource);
        // operator 合并template 没有合并resource，not work
        PodBuilder podBuilder = new PodBuilder();
        podBuilder.withNewSpec()
                .withContainers(new ContainerBuilder()
                        .withName(FLINK_MAIN_CONTAINER)
                        .withResources(ResourceHelper.getInstance().resourceRequirements(spec.getLimits()))
                        .build())
                .endSpec();
        taskManager.setPodTemplate(podBuilder.build());
        return taskManager;
    }

    private FlinkDeploymentSpec.FlinkVersion toFlinkVersion(String version) {
        String sep = "\\.";
        int major = Integer.parseInt(version.split(sep)[0]);
        int minor = Integer.parseInt(version.split(sep)[1]);

        return FlinkDeploymentSpec.FlinkVersion.valueOf("V" + major + "_" + minor);
    }

    @Override
    public InstallAppVo<? extends OverrideSpec> parseInstallVo(String data) {
        InstallAppVo<FlinkOverrideSpec> vo = JsonUtil.toObject(data, new TypeReference<InstallAppVo<FlinkOverrideSpec>>() {
        });
        if (vo != null) {
            if (vo.getSpec() != null && vo.getSpec().getMembers() == 0) {
                FlinkOverrideSpec spec = vo.getSpec();
                spec.setMembers(spec.getDataSize() + spec.getMasterSize());
            }
            if (!CollectionUtils.isEmpty(vo.getOverrideSpecs())) {
                for (FlinkOverrideSpec spec : vo.getOverrideSpecs().values()) {
                    if (spec.getMembers() == 0 && spec.getMasterSize() != null && spec.getDataSize() != null)
                        spec.setMembers(spec.getMasterSize() + spec.getDataSize());
                }
            }
        }
        return vo;
    }

    public CloudAppVO overrideSpec(CloudAppLogic logicApp, Integer kubeId, InstallAppVo<? extends OverrideSpec> vo) {
        CloudAppVO appVO = super.overrideSpec(logicApp, kubeId, vo);
        FlinkVO flinkVO = new FlinkVO();
        BeanUtils.copyProperties(appVO, flinkVO);
        if (vo.getOverrideSpecs().get(kubeId) instanceof FlinkOverrideSpec) {
            FlinkOverrideSpec overrideSpec = (FlinkOverrideSpec) vo.getOverrideSpecs().get(kubeId);
            flinkVO.setDataCpu(overrideSpec.getDataCpu());
            flinkVO.setDataMemory(overrideSpec.getDataMemory());
            flinkVO.setDataSize(overrideSpec.getDataSize());
            flinkVO.setMasterCpu(overrideSpec.getMasterCpu());
            flinkVO.setMasterMemory(overrideSpec.getMasterMemory());
            flinkVO.setMasterSize(overrideSpec.getMasterSize());
            flinkVO.setMembers(flinkVO.getMasterSize() + flinkVO.getDataSize());
        }
        return flinkVO;
    }

    @Override
    public OverrideSpec reviewSpec(CloudApp app) {
        OverrideSpec overrideSpec = super.reviewSpec(app);
        FlinkOverrideSpec flinkOverrideSpec = new FlinkOverrideSpec();
        BeanUtils.copyProperties(overrideSpec, flinkOverrideSpec);
        FlinkDeployment cr = null;
        try {
            String crYaml = StringUtils.isEmpty(app.getCr()) ? app.getCrRun() : app.getCr();
            cr = Serialization.unmarshal(crYaml, FlinkDeployment.class);
            FlinkDeploymentSpec spec = cr.getSpec();
            flinkOverrideSpec.setDataCpu(spec.getTaskManager().getResource().getCpu() + "");
            flinkOverrideSpec.setDataMemory(spec.getTaskManager().getResource().getMemory() + "");
            flinkOverrideSpec.setDataSize(spec.getTaskManager().getReplicas().intValue());

            flinkOverrideSpec.setMasterCpu(spec.getJobManager().getResource().getCpu() + "");
            flinkOverrideSpec.setMasterMemory(spec.getJobManager().getResource().getMemory() + "");
            flinkOverrideSpec.setMasterSize(spec.getJobManager().getReplicas().intValue());
        } catch (Exception e) {
            log.error("", e);
        }
        return flinkOverrideSpec;
    }

    @Override
    public void restore(BackupHis backupHis, Integer appId, String restoreTime, String ftpFilename, String backupType) {
        flinkJobService.restore(backupHis, backupHis.getPodName());
    }


    private class StorageConfigurator {
        private CloudBackupStorageVO storageConfig = null;
        private Map<ImageKindEnum, String> imageMap;
        private CloudAppVO app = null;
        // refactme
        Map<String, BiConsumer<Map<String, String>, Pod>> storageConfigure = ImmutableMap.of(
                S3, (config, pod) -> {
                    String bucket = storageConfig.getBucket();
                    String subDir = String.format("%s/%s/%s/%s", app.getKind().toLowerCase(), app.getArch().toLowerCase(), app.getNamespace(), app.getCrName());
                    config.put("state.savepoints.dir", "s3://" + bucket + "/" + subDir + "/savepoints");
                    config.put("state.checkpoints.dir", "s3://" + bucket + "/" + subDir + "/checkpoints");
                    config.put("high-availability.storageDir", "s3://" + bucket + "/" + subDir + "/ha");
                    config.put("s3.endpoint", storageConfig.getServer());
                    config.put("s3.access-key", storageConfig.getAccessKey());
                    config.put("s3.secret-key", storageConfig.getSecretKey());
                    config.put("s3.path.style.access", "true");
                    // disable ssl verify
                    config.put("env.java.opts.jobmanager", "-Dcom.amazonaws.sdk.disableCertChecking=true");
                    if (StringUtils.isNotEmpty(storageConfig.getRegion()))
                        config.put("s3a.endpoint.region", storageConfig.getRegion()); // for hadoop flugin
                    // volume append-volume
                    // setup pod template copy s3 plugin jar

                },
                NAS, (config, pod) -> {
                    String subDir = getSubDirNAS(app);
                    config.put("state.savepoints.dir", "file://" + subDir + "/savepoints");
                    config.put("state.checkpoints.dir", "file://" + subDir + "/checkpoints");
                    config.put("high-availability.storageDir", "file://" + subDir + "/ha");
                    // setup nfs volume, todo 这种方式需要宿主机安装nfs-client
                    pod.getSpec().getVolumes()
                            .addAll(Stream.of(
                                    new VolumeBuilder()
                                            .withName("flink-data")
                                            .withNewNfs(storageConfig.getMountPath(), false, storageConfig.getServer())
                                            .build()
                                    ).collect(Collectors.toList()));
                    VolumeMount vm = new VolumeMountBuilder()
                            .withName("flink-data")
                            .withMountPath("/flink-data")
                            .build();

                    pod.getSpec()
                            .getContainers().stream()
                            .filter(c -> c.getName().equalsIgnoreCase(FLINK_MAIN_CONTAINER))
                            .forEach(c -> c.getVolumeMounts().add(vm));

                    // modify init container -- add nfs mount volume, add init command
                    String initArgs = "mkdir -p " + subDir
                            + ";chown -R 9999 " + subDir;

                    pod.getSpec()
                            .getInitContainers().stream().filter(c -> c.getName().equalsIgnoreCase("flink-prepare"))
                            .findAny()
                            .ifPresent(c -> {
                                c.getVolumeMounts().add(vm);
                                String args = c.getArgs().get(c.getArgs().size() - 1) + ";" + initArgs;
                                c.getArgs().set(c.getArgs().size() - 1, args);
                            });
                }
        );

        private Volume getExtVolume() {
            return new VolumeBuilder()
                    .withName("extlib")
                    .withEmptyDir(new EmptyDirVolumeSource())
                    .build();
        }

        // return volumeMount to mount separate jar
        private List<VolumeMount> getExtLibVMs() {
            VolumeMount vm2 = new VolumeMountBuilder()
                    .withName("extlib")
                    .withMountPath("/opt/flink/lib/flink-dist-1.17.2.jar")// modified flink web jar
                    .withSubPath("flink-dist-1.17.2.jar")
                    .build();
            VolumeMount vm3 = new VolumeMountBuilder()
                    .withName("extlib")
                    .withMountPath("/opt/flink/lib/flink-metrics-prometheus-1.18.1.jar") // for prom metric reporter
                    .withSubPath("flink-metrics-prometheus-1.18.1.jar")
                    .build();
            return ImmutableList.of(vm2, vm3);
        }


        public StorageConfigurator(CloudAppVO app, Map<ImageKindEnum, String> imageKindEnumStringMap) {
            this.app = app;
            this.imageMap = imageKindEnumStringMap;
            this.storageConfig =
                    resourceManagerService.listBackupStorage(); // todo not related to backup storage

        }

        public void configure(Pod podTemplate, Map<String, String> config) {
            storageConfigure.get(app.getCsiType().toLowerCase())
                    .accept(config, podTemplate);
        }
    }
}
