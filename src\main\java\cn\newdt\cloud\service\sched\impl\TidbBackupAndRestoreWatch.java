package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.StatusConstant;
import cn.newdt.cloud.domain.BackupHis;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.RestoreHis;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.mapper.BackupMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.RestoreServiceImpl;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.utils.BackupUtil;
import cn.newdt.cloud.utils.JsonUtil;
import cn.newdt.cloud.utils.TidbUtil;
import cn.newdt.cloud.vo.CloudBackupStorageVO;
import cn.newdt.commons.bean.UserInfo;
import cn.newdt.commons.utils.UserUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pingcap.v1alpha1.TidbCluster;
import com.pingcap.v1alpha1.tidbclusterstatus.pd.Leader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import static cn.newdt.cloud.constant.DatasourceConstant.CLOUD_BACKUP_HIS;
import static cn.newdt.cloud.constant.DatasourceConstant.SCHEMA;

@Slf4j
public class TidbBackupAndRestoreWatch extends OpsProcessorContext implements OpsPostProcessor<TidbCluster> {
    @Autowired
    private BackupUtil backupUtil;
    @Autowired
    private BackupMapper backupMapper;
    @Autowired
    private RestoreServiceImpl restoreServiceImpl;

    @Override
    public OpsResultDTO postProcess(TriggerHis triggerHis) throws Exception {
        // set async userinfo
        String userInfoStr = triggerHis.getJobDataMap().get("userInfo");
        UserInfo userInfo = JSONObject.parseObject(userInfoStr, UserInfo.class);
        UserUtil.setAsyncUserInfo(userInfo);

        //创建返回结果
        OpsResultDTO.Builder result = OpsResultDTO.builder().stopJob(false);  //.stopJob是是否停止任务的标识，TRUE为停止让

        //根据appId获取app对象
        Map<String, String> jobDataMap = triggerHis.getJobDataMap();
        String appId = jobDataMap.get("appId");
        CloudApp app = appService.get(Integer.valueOf(appId));

        //设置runcr
        updateBasicApp(app);

        //获取操作类型
        String handType = getHandType(triggerHis);

        KubeClient kubeClient = kubeClientService.get(app.getKubeId());

        //当前时间
        Date nowDate = new Date();
        //获取超时时间
        Integer backupTimeOut = backupUtil.getBackupTimeOut(app.getId());

        if (ActionEnum.BACKUP.toString().equalsIgnoreCase(handType)) {
            AppKind kind = AppKind.TIDB;
            //备份操作
            //时间转换
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
            //获取所有需要参数
            String extDataStr = jobDataMap.get("extDataStr");
            JSONObject extData = JSON.parseObject(extDataStr);
            Integer backupHisId = extData.getInteger("backupHisId");
            Integer changeId = extData.getInteger("changeId");
            String backupStartDateSDF = extData.getString("backupStartDateSDF");
            String podNames = extData.getString("podNames");
            String backupPodName = extData.getString("backupPodName");
            String backupFilename = extData.getString("backupFilename");
            Date backupStartDate = sdf.parse(backupStartDateSDF);
            //查询备份存储类型
            CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();

            //查询备份历史
            BackupHis backupHis = backupMapper.getBackupHisById(SCHEMA, CLOUD_BACKUP_HIS, backupHisId);
            //记录备份信息：数据库名称、备份路径、备份文件名称
            JSONObject messageObj = JSONObject.parseObject(backupHis.getMessage());
            String[] backupPodNameArr = podNames.split(",");

            //判断app还是否存在
            if (app.getDeleted())
                return getResultBackupFail(result, app, changeId, backupHis, messageObj, "应用不存在！");

            //判断是否超时
            boolean isTimeout = backupUtil.checkBackupAndRestoreTimeout(backupStartDate, nowDate, backupTimeOut);

            if (isTimeout)
                //补全备份历史
                return getResultBackupFail(result, app, changeId, backupHis, messageObj, "TiDB备份超时!");

            log.info("[TiDB Backup]：定时回调，备份中...");
            result.stopJob(false).msg("备份中...").status(StatusConstant.RUNNING);

            //先判断是否备份结束
            String namespace = app.getNamespace();
            String tidbBackupLog = kubeClient.execCmd(namespace, backupPodName, TidbUtil.TIDB_BACKUP_CONTAINER_NAME,
                    "sh", "-c", "tail -n 1 /var/log/tidb-backup.log");

            if (tidbBackupLog.contains("error") || tidbBackupLog.contains("Failed"))
                //失败
                return getResultBackupFail(result, app, changeId, backupHis, messageObj, "TiDB备份失败！" + tidbBackupLog);

            //是否备份成功
            Boolean isBackupSuc = null;
            if (tidbBackupLog.contains("Backup complete")) {
                //成功
                isBackupSuc = true;
                log.info("[TiDB Backup]：定时回调，备份成功，等待文件准备...");
                result.stopJob(false).msg("备份成功，等待文件准备...").status(StatusConstant.RUNNING);
            }

            //开始进行文件移动
            Boolean isFileMvSuc = null;
            if (null != isBackupSuc && isBackupSuc) {
                //需要进行备份文件的转移<每一个pod中进行文件的转移>
                try {
                    for (String backupFilePodName : backupPodNameArr) {
                        String mvFileLogExist = kubeClient.execCmd(
                                namespace, backupFilePodName, TidbUtil.TIDB_BACKUP_CONTAINER_NAME,
                                "sh", "-c", "test -e /var/log/tidb-backupfile-move.log && echo yes || echo no");
                        if (mvFileLogExist.contains("yes")) {
                            //文件存在，查看文件移动日志
                            String tidbMvFileLog = kubeClient.execCmd(
                                    namespace, backupFilePodName, TidbUtil.TIDB_BACKUP_CONTAINER_NAME,
                                    "sh", "-c", "tail -n 1 /var/log/tidb-backupfile-move.log");
                            if (tidbMvFileLog.contains("success")) {
                                //此时备份结束
                                isFileMvSuc = true;
                                log.info("[TiDB Backup]：Move " + backupFilePodName + " Success");
                            } else if (tidbMvFileLog.contains("Failed")) {
                                //此时备份成功，移动文件失败
                                isFileMvSuc = false;
                                log.info("[TiDB Backup]：Move " + backupFilePodName + " Failed");
                                break;
                            }
                        } else if (mvFileLogExist.contains("no")) {
                            //移动日志文件不存在，则开始进行文件移动
                            String fileMvCommand = "bash /scripts/tidb-backupfile-move.sh " + backupFilename;
                            kubeClient.execCmd(namespace, backupFilePodName,
                                    TidbUtil.TIDB_BACKUP_CONTAINER_NAME, "sh", "-c", fileMvCommand);
                            log.info("[TiDB Backup]：Move " + backupFilePodName);
                        }
                    }
                } catch (Exception e) {
                    log.info("[TiDB Backup]：Move Failed！", e);
                    isFileMvSuc = false;
                }
            }

            //判断是否成功备份文件移动，成功后进行压缩
            Boolean isCompressSuccessed = null;
            String containerBackupStorageRootDir = "/mnt/shared";
            //到第一个分片的pod中获取压缩日志
            String compressPodName = backupPodNameArr[0];
            //是否移动成功
            if(null != isFileMvSuc && isFileMvSuc) {
                //由于mount是在TIDB_MNT_CONTAINER_NAME，所以压缩也要在TIDB_MNT_CONTAINER_NAME容器
                String compressLogFilePath = "/tmp/" + backupFilename + "-compress.log";
                String isCompressLogExistResult = kubeClient.execCmd(namespace, compressPodName, TidbUtil.TIDB_MNT_CONTAINER_NAME,
                        "sh", "-c", "test -f " + compressLogFilePath + " && echo \"File is exist\" || echo \"File is not exist\"");
                if (isCompressLogExistResult.contains("File is exist")) {
                    log.debug("[TiDB Backup]：Check Compress Result");
                    //文件存在，读取最后一行日志，判断是否包含 "Compress Success" 或 "Compress Failed" 来确定压缩状态。
                    String redisCompressLog = kubeClient.execCmd(namespace, compressPodName, TidbUtil.TIDB_MNT_CONTAINER_NAME,
                            "sh", "-c", "tail -1 " + compressLogFilePath + " 2>/dev/null");
                    if(redisCompressLog.contains(backupFilename + " Compress Success")){
                        //成功
                        isCompressSuccessed = true;
                        log.debug("[TiDB Backup]：Check Compress Result: " + isCompressSuccessed);
                    } else if (redisCompressLog.contains(backupFilename + " Compress Failed")) {
                        //失败
                        isCompressSuccessed = false;
                        log.debug("[TiDB Backup]：Check Compress Result: " + isCompressSuccessed);
                    }
                } else {
                    //文件不存在，说明压缩尚未开始。进行压缩
                    String mountScript = backupUtil.buildMountScriptCMD(
                            cloudBackupStorageVO, containerBackupStorageRootDir, "sh", true);

                    //到第一个分片的pod中执行 mount 命令，然后把整体进行压缩
                    try {
                        //到第一个分片的pod中执行 mount 命令
                        //由于mount是在TIDB_MNT_CONTAINER_NAME，所以压缩也要在TIDB_MNT_CONTAINER_NAME容器
                        log.debug("[TiDB Backup] 备份压缩-进行 mount");
                        kubeClient.execCmd(namespace, compressPodName, TidbUtil.TIDB_MNT_CONTAINER_NAME,
                                "sh", "-c", mountScript);

                        //执行压缩
                        log.debug("[TiDB Backup] 备份压缩-进行压缩");
                        String backupFileContextPath = kind.getBackupFilePath(
                                namespace, app.getName(), backupFilename);
                        String backupFileContextDir =
                                backupFileContextPath.substring(0, backupFileContextPath.lastIndexOf("/"));
                        String backupDir = containerBackupStorageRootDir + File.separator + backupFileContextDir;
                        //异步执行压缩，正常结束exit 0的话输出压缩成功，异常exit 1的话输出压缩失败
                        kubeClient.execCmdOneway(
                                namespace, compressPodName, TidbUtil.TIDB_MNT_CONTAINER_NAME,
                                "sh", "-c", "touch " + compressLogFilePath + " && cd "
                                        + backupDir + "; tar -czf " + backupFilename + ".tar.gz" + " * > /dev/null 2>&1"
                                        + " && echo \""+ backupFilename + " Compress Success\" > " + compressLogFilePath
                                        + " || echo \"" + backupFilename + " Compress Failed\" > " + compressLogFilePath);

                    } catch (Exception e) {
                        isCompressSuccessed = false;
                        log.warn("[TiDB Backup] 备份成功但压缩失败！", e);
                    }
                }

            }

            //判断是否成功备份
            if (null != isCompressSuccessed && isCompressSuccessed) {
                //成功
                backupHis.setMessage(JsonUtil.toJson(messageObj));
                backupUtil.backupReturn(backupHis, changeId, StatusConstant.SUCCESS, backupFilename, "TiDB备份成功！");
                result.stopJob(true).msg("备份成功！").status(StatusConstant.SUCCESS);
            } else if(null != isCompressSuccessed && !isCompressSuccessed){
                //文件转移成功但压缩失败
                String compressFailedMsg = "压缩失败！";
                backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "TiDB备份文件转移成功！" + compressFailedMsg);
                result.stopJob(true).msg("备份成功！" + compressFailedMsg).status(StatusConstant.FAIL);
            } else if (null != isFileMvSuc && !isFileMvSuc) {
                //备份成功但文件转移失败
                String mvFailedMsg = "压缩失败！";
                backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "TiDB备份成功！" + mvFailedMsg);
                result.stopJob(true).msg("备份成功！" + mvFailedMsg).status(StatusConstant.FAIL);
            } else if (null != isBackupSuc && !isBackupSuc) {
                //备份失败
                backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "TiDB备份失败！");
                result.stopJob(true).msg("备份失败！").status(StatusConstant.FAIL);
            }

            //存在压缩操作，最终进行卸载
            if (null != isCompressSuccessed) {
                String mountScript = backupUtil.buildMountScriptCMD(cloudBackupStorageVO,
                        containerBackupStorageRootDir, "sh", false);
                //到第一个分片的pod中执行 umount 命令
                log.debug("[TiDB Backup] 备份压缩-进行 umount");
                //由于mount是在TIDB_MNT_CONTAINER_NAME，所以umount也要在TIDB_MNT_CONTAINER_NAME容器
                kubeClient.execCmd(namespace, compressPodName, TidbUtil.TIDB_MNT_CONTAINER_NAME,
                        "sh", "-c", mountScript);
            }
        } else {
            //恢复操作
            //时间转换
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
            //获取所有需要参数
            String extDataStr = jobDataMap.get("extDataStr");
            JSONObject extData = JSON.parseObject(extDataStr);
            Integer restoreHisId = extData.getInteger("restoreHisId");
            RestoreHis restoreHis = restoreServiceImpl.get(restoreHisId);
            String resourceChangeIdStr = extData.getString("resourceChangeId");
            String backupFileName = extData.getString("backupFileName");
            Integer resourceChangeId = Integer.valueOf(resourceChangeIdStr);
            String restorePodNames = extData.getString("restorePodNames");
            String[] restorePodNameArr = restorePodNames.split(",");
            String restorePodName = extData.getString("restorePodName");
            String restoreStartDateSDF = extData.getString("restoreStartDateSDF");
            Date restoreStartDate = sdf.parse(restoreStartDateSDF);
            //记录备份信息：数据库名称、备份路径、备份文件名称
            //判断app还是否存在
            if (app.getDeleted())
                return getResultRestoreFail(result, app, resourceChangeId, restoreHis, "应用不存在！");

            //恢复尚未成功，判断是否超过五分钟
            boolean isTimeout = backupUtil.checkBackupAndRestoreTimeout(restoreStartDate, nowDate, backupTimeOut);
            if (isTimeout)
                return getResultRestoreFail(result, app, resourceChangeId, restoreHis, "TiDB恢复超时!");

            log.info("[TiDB-cluster恢复]：定时回调，恢复中...");
            result.stopJob(false).msg("恢复中...").status(StatusConstant.RUNNING);

            //判断是否开启了恢复操作，没有则判断是否文件移动成功
            String namespace = app.getNamespace();
            String tidbRestoreLogExist = kubeClient.execCmd(namespace, restorePodName, TidbUtil.TIDB_BACKUP_CONTAINER_NAME, "sh", "-c", "test -e /var/log/tidb-restore.log && echo yes || echo no");
            if (tidbRestoreLogExist.contains("no")) {
                //先获取日志判断文件是否移动完成
                String tidbCpFileLog = "";
                Boolean isFileCpSuc = null;
                for (String cpPodName : restorePodNameArr) {
                    tidbCpFileLog = kubeClient.execCmd(namespace, cpPodName, TidbUtil.TIDB_BACKUP_CONTAINER_NAME, "sh", "-c", "tail -n 1 /var/log/tidb-restorefile-move.log");
                    if (tidbCpFileLog.contains("success")) {
                        //此时备份文件复制完成
                        isFileCpSuc = true;
                    } else if (tidbCpFileLog.contains("Failed")) {
                        //此时备份成功，移动文件失败
                        isFileCpSuc = false;
                        break;
                    }
                }
                //执行恢复操作
                if (null != isFileCpSuc && isFileCpSuc) {
                    //查询pd信息
                    TidbCluster tidbCr = kubeClient.listCustomResource(TidbCluster.class, app.getCrName(), namespace);
                    //进入某一个pod执行备份命令，所有pod都需要进行备份文件的转移
                    Leader leader = tidbCr.getStatus().getPd().getLeader();
                    String pdClientURL = leader.getClientURL();
                    kubeClient.execCmd(namespace, restorePodName, TidbUtil.TIDB_BACKUP_CONTAINER_NAME, "sh", "-c", "sh /scripts/tidb-restore.sh " + backupFileName + " " + pdClientURL);
                } else if (null != isFileCpSuc && !isFileCpSuc) {
                    //复制文件失败
                    return getResultRestoreFail(result, app, resourceChangeId, restoreHis, "TiDB恢复失败！" + tidbCpFileLog);
                }
            }

            if (tidbRestoreLogExist.contains("yes")) {
                log.info("[TiDB-cluster恢复]：定时回调，文件移动完成，恢复中...");
                result.stopJob(false).msg("文件移动完成，恢复中...").status(StatusConstant.RUNNING);
                String tidbRestoreLog = kubeClient.execCmd(namespace, restorePodName, TidbUtil.TIDB_BACKUP_CONTAINER_NAME, "sh", "-c", "tail -n 1 /var/log/tidb-restore.log");
                if (tidbRestoreLog.contains("success") || tidbRestoreLog.contains("Restore complete")) {
                    backupUtil.restoreReturn(restoreHis, resourceChangeId, "恢复成功！", StatusConstant.SUCCESS);
                    result.stopJob(true).msg("恢复成功！").status(StatusConstant.SUCCESS);
                } else if (tidbRestoreLog.contains("Failed") || tidbRestoreLog.contains("ERROR")) {
                    //失败
                    backupUtil.restoreReturn(restoreHis, resourceChangeId, "tidb恢复失败！" + tidbRestoreLog, StatusConstant.FAIL);
                    result.stopJob(true).msg("tidb恢复失败！" + tidbRestoreLog).status(StatusConstant.FAIL);
                }
            }
        }

        OpsResultDTO dto = result.build();
        if (dto.getStopJob()) {
            appService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
        }
        return dto;
    }

    private OpsResultDTO getResultBackupFail(OpsResultDTO.Builder result, CloudApp app, Integer changeId, BackupHis backupHis, JSONObject messageObj, String msg) {
        //补全备份历史
        messageObj.put("msg", msg + "[" + app.getName() + "]");
        backupHis.setMessage(JsonUtil.toJson(messageObj));
        backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", msg + "！[" + app.getName() + "]");
        result.stopJob(true).msg(msg + "[" + app.getName() + "]").status(StatusConstant.FAIL);
        OpsResultDTO dto = result.build();
        appService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
        return dto;
    }

    private OpsResultDTO getResultRestoreFail(OpsResultDTO.Builder result, CloudApp app, Integer resourceChangeId, RestoreHis restoreHis, String msg) {
        //补全恢复历史
        backupUtil.restoreReturn(restoreHis, resourceChangeId, msg + "！[" + app.getName() + "]", StatusConstant.FAIL);
        result.stopJob(true).msg(msg + "！[" + app.getName() + "]").status(StatusConstant.FAIL);
        OpsResultDTO dto = result.build();
        appService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
        return dto;
    }

    private String getHandType(TriggerHis triggerHis) {
        //获取操作类型
        String triggerName = triggerHis.getTriggerName();
        return triggerName.substring(0, triggerName.lastIndexOf("_"));
    }

    private void updateBasicApp(CloudApp app) {
        CloudApp cloudApp = new CloudApp();
        cloudApp.setCrRun(app.getCr());
        cloudApp.setId(app.getId());
        appService.update(cloudApp);
    }

}
