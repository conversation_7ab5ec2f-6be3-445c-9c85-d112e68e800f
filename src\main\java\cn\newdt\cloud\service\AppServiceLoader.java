package cn.newdt.cloud.service;

import cn.newdt.cloud.common.OpLogContext;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.ComponentKindEnum;
import cn.newdt.cloud.domain.KubeConfig;
import cn.newdt.cloud.dto.*;
import cn.newdt.cloud.repository.MetricClient;
import cn.newdt.cloud.service.impl.*;
import cn.newdt.cloud.utils.MetricUtil;
import cn.newdt.cloud.utils.NullableImmutableMap;
import cn.newdt.cloud.vo.CloudAppVO;
import cn.newdt.cloud.vo.MetricVO;
import cn.newdt.cloud.vo.OverrideSpec;
import cn.newdt.cloud.vo.RedisVO;
import cn.newdt.commons.bean.UserInfo;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.UserUtil;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import io.fabric8.kubernetes.api.model.Quantity;
import io.fabric8.kubernetes.api.model.ResourceRequirements;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ClassUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.reflection.ExceptionUtil;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.CloudAppConstant.ElasticSearch;
import static cn.newdt.cloud.constant.CloudAppConstant.MongoDB;
import static cn.newdt.cloud.domain.cr.MongoDBCommunity.LOG_PV_SIZE;

//import cn.newdt.cloud.service.auth.LicenseService;

@Component
@Slf4j
public class AppServiceLoader implements ApplicationContextAware {
    public static final String STORAGE = CloudAppConstant.ResourceName.STORAGE;
    public static final String MEMORY = CloudAppConstant.ResourceName.MEMORY;
    public static final String CPU = CloudAppConstant.ResourceName.CPU;
    private static Map<AppKind, AppKindService> SERVICE_MAP = new HashMap<>();

    @Autowired
    private AppScheduler appScheduler;
    @Autowired
    private NodeService nodeService;
    @Autowired
    private MetricService metricService;
    @Autowired
    private MetricServiceImpl metricServiceImpl;
//    @Autowired
//    private LicenseService licenseService;
    @Autowired
    private KubeConfigService kubeConfigService;
    @Autowired
    private MetricClient client;

    /**
     * 根据AppKind获取对应AppKindService接口
     */
    public static AppKindService getInstance(AppKind appKind) {
        return Optional.ofNullable(SERVICE_MAP.get(appKind))
                .orElseThrow(() -> new IllegalArgumentException("appkindservice not registered for kind:" + appKind));
    }

    public static AppKindService getInstance(String kind, String arch) {
        AppKind appKind = AppKind.valueOf(kind, arch);
        return getInstance(appKind);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, AppKindService> beans = applicationContext.getBeansOfType(AppKindService.class);
        for (AppKindService value : beans.values()) {
            SERVICE_MAP.put(value.getKind(), proxy(value));
        }
    }

    private AppKindService proxy(AppKindService value) {
        Class<?> ultimateClass = AopProxyUtils.ultimateTargetClass(value);
        Class<?>[] interfaces = ClassUtils.getAllInterfaces(ultimateClass).toArray(new Class[0]);
        return (AppKindService) Proxy.newProxyInstance(
                value.getClass().getClassLoader(), interfaces,
                new AppKindServiceHandler(value, appScheduler, metricService, metricServiceImpl, kubeConfigService, client));
    }

    static class AppKindServiceHandler implements InvocationHandler {
        private AppKindService appKindService;
        private AppScheduler appScheduler;
        private MetricService metricService;
        private MetricServiceImpl metricServiceImpl;
//        private LicenseService licenseService;
        private KubeConfigService kubeConfigService;
        private MetricClient client;
        private final Set<String> proxyMethodNameSet;

        private ThreadLocal<Integer> tenantId = new ThreadLocal<>();

        public AppKindServiceHandler(AppKindService appKindService, AppScheduler appScheduler, MetricService metricService, MetricServiceImpl metricServiceImpl, KubeConfigService kubeConfigService, MetricClient client) {
            proxyMethodNameSet = ImmutableSet.of("install", "scale", "migrate", "update");
            this.appKindService = appKindService;
            this.appScheduler = appScheduler;
            this.metricService = metricService;
//            this.licenseService = licenseService;
            this.metricServiceImpl = metricServiceImpl;
            this.kubeConfigService = kubeConfigService;
            this.client = client;
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            if (proxyMethodNameSet.contains(method.getName())) {
                handleProxyMethod(method.getName(), args);
            }
            try {
                return method.invoke(appKindService, args);
            } catch (Throwable e) {
                e = ExceptionUtil.unwrapThrowable(e);
                throw e;
            } finally {
                OpLogContext.instance().reset();
            }
        }

        public void handleProxyMethod(String methodName, Object[] args) {
            // call validate method
            List<ResourceQuantity> quantities = new ArrayList<>();

            // 对于install,scale 需校验节点和配额
            // 对于migrate 配额没有变化，仅校验节点(指定节点)
            // 对于update  request没有变化，只需校验配额
            switch (methodName) {
                case "install":
                    quantities = parseInstall((CloudAppVO) args[0]);
                    break;
                case "scale": // 处理重载方法 scale(id, member) & scale(id, overridespec)
                    quantities = parseScale(args);
                    break;
                case "migrate":
                    quantities = parseMigrate(args);
                    break;
                case "update":
                    if (args != null && args.length == 1 && args[0] instanceof ResourceDTO)
                        // todo 直接解析spec
                        quantities = parseUpdate(args);
                    break;
                default:
                    throw new UnsupportedOperationException();
            }
            try {
                checkClusterCpu(quantities);
                validate(quantities);
            } catch (Exception e) {
                if (e instanceof CustomException)
                    throw e;
                log.error("配额校验发生非CustomException", e);
            } finally {
                tenantId.remove();
            }
        }

        /**
         * 校验cpu是否超出license限制
         *
         * @return
         */
        public void checkClusterCpu(List<ResourceQuantity> quantities) {
            try {
                //获取license中的最大cpu限制
                Integer maxvcpu = Integer.valueOf(UserUtil.getCurrentUser().getMaxvcpu()); //Integer.valueOf(licenseService.getLicenseUtil().getMaxdbnum());
                //获取所有的集群id
                List<KubeConfig> kubeConfigs = kubeConfigService.findEnabled(null);
                //统计所有cpu使用量
                Double allCpuUsage = 0.0;
                for (KubeConfig kubeConfig : kubeConfigs) {
                    try {
                        Set<String> podNames = metricServiceImpl.collectPodNamesForCpu(null, kubeConfig.getId());
                        if (CollectionUtils.isEmpty(podNames)) {
                            continue;
                        }
                        MetricVO metricVO = client.queryCpu(0l, "", kubeConfig.getId(), podNames);
                        Double usageCpu = metricVO.getUsage().getValue();
                        allCpuUsage += usageCpu;
                    } catch (Exception e) {
                        //TODO HD：可能由于普罗米修斯问题导致异常
                    }
                }
                //取出新增cpu数量
                for (ResourceQuantity quantity : quantities) {
                    Map<String, ? extends Number> requests = quantity.getRequests();
                    if (!CollectionUtils.isEmpty(requests)) {
                        double cpu = requests.get("cpu").doubleValue() / 1000 * quantity.getMember();
                        allCpuUsage += cpu;
                    }
                }
                //校验cpu是否超额
                if (allCpuUsage > maxvcpu) {
                    throw new CustomException(600, "已超出cpu最大限制！");
                }
            } catch (Exception e) {
                throw new CustomException(500, "校验cpu最大值限制未通过！");
            }
        }

        /**
         * ResourceQuantity.requests 部分校验节点资源
         * ResourceQuantity.limits 部分校验租户配额
         * @param quantities 待校验的资源数量
         */
        private void validate(List<ResourceQuantity> quantities) {
            if (quantities == null) {
                return;
            }
            // 过滤缩容或资源降级的部分
            quantities.forEach(q -> {
                if (q.getLimits() != null) {
                    q.setLimits(new HashMap<>(q.getLimits()));
                    q.getLimits().entrySet().removeIf(e -> e != null && e.getValue() != null && e.getValue().longValue() <= 0);
                }
                if (q.getRequests() != null) {
                    q.setRequests(new HashMap<>(q.getRequests()));
                    q.getRequests().entrySet().removeIf(e -> e != null && e.getValue() != null && e.getValue().longValue() <= 0);
                }
            });
            quantities = quantities.stream()
                    .filter(quantity -> quantity.getMember() > 0)
                    .collect(Collectors.toList());
            if (quantities.isEmpty()) {
                return;
            }
            List<NodeDTO> nodeDTOS = appScheduler.listCandidates(quantities.get(0).getKubeId(), quantities.get(0).getKubeSchedulerId());
            UserInfo userInfo = UserUtil.getCurrentUser();
            quantities.parallelStream()
                    .filter(q -> !CollectionUtils.isEmpty(q.getRequests()))
                    .forEach(quantity -> {
                        UserUtil.setAsyncUserInfo(userInfo);
                        Map<String, ? extends Number> requests = quantity.getRequests();
                        ResourceDTO resourceDTO = new ResourceDTO();
                        resourceDTO.setAppId(quantity.getAppId());
                        resourceDTO.setAntiAffinity(quantity.getAntiAffinity());
                        resourceDTO.setKubeSchedulerId(quantity.getKubeSchedulerId());
                        resourceDTO.setCpu(getValue(requests, CPU) + "m");
                        resourceDTO.setMemory(getValue(requests, MEMORY) + "");
                        resourceDTO.setDisk(getValue(requests, STORAGE) + "");
                        List<String> affinity = null; // 必须遵循亲和校验
                        if (quantity.getNode() != null) {
                            // 后续操作会对affinity列表修改
                            affinity = new ArrayList<>(Arrays.asList(quantity.getNode().split(",")));
                        }
                        //chooseNode会修改node缓存的可分配数值，相当于占用，而校验时不应该实际占用。
                        appScheduler.chooseNode(quantity.getKubeId(), resourceDTO, quantity.getMember(),
                                true, affinity, nodeDTOS);
                    });

            Map<String, MetricVO> quotaMetric = new ConcurrentHashMap<>(metricService.queryTenantQuota(
                    tenantId.get(), quantities.get(0).getKubeId(), 0l));
            Optional.ofNullable(quotaMetric.get(CloudAppConstant.QuotaItems.LIMIT_CPU))
                    .ifPresent(cpuMetric -> {
                        cpuMetric.getRequest().setValue(cpuMetric.getRequest().getValue().intValue() * 1000.0);
                        cpuMetric.getCapacity().setValue(cpuMetric.getCapacity().getValue().intValue() * 1000.0);
                    });
           quantities.stream()
                    .filter(q -> !CollectionUtils.isEmpty(q.getLimits()))
                    .forEach(quantity -> {
                        UserUtil.setAsyncUserInfo(userInfo);
                        Map<String, ? extends Number> limits = quantity.getLimits();
                        checkTenantQuota(
                                quantity.getKubeId(),
                                quantity.getMember(),
                                getValue(limits, CPU).intValue(), getValue(limits, MEMORY).longValue(),
                                getValue(limits, STORAGE).longValue(), quotaMetric);
                    });
            logInfo("validation passed");
        }

        Number getValue(Map<String, ? extends Number> map, String key) {
            if (map.get(key) != null) {
                return map.get(key);
            } else {
                return 0;
            }
        }
        /**
         * 从该appKind的podTemplate中读取所有容器的resources.request汇总.
         * @return K, [cpu,memory], V 资源申请量
         */
        private Map<String, ? extends Number> getAppRequest(String component) {
            DefaultAppKindService defaultService = (DefaultAppKindService) appKindService;
            return defaultService.getTotalContainerRequests(component);
        }

        ResourceRequirements getMainContainerResourceRequest(Map<String, String> limits) {
            // get from ResourceHelper the request and limit requirements
            ResourceRequirements resourceRequirements = ResourceHelper.getInstance().resourceRequirements(new ResourceDTO() {{
                setCpu(limits.get(CPU));
                setMemory(limits.get(MEMORY));
            }});
            // put storage on limits
            if (StringUtils.isNotEmpty(limits.get(STORAGE)))
                resourceRequirements.setLimits(ImmutableMap.<String, Quantity>builder().putAll(resourceRequirements.getLimits())
                    .put(STORAGE, new Quantity(limits.get(STORAGE))).build());
            return resourceRequirements;
        }

        /**
         * 获取应用类型指定组件容器组的资源申请，包括request 和 limit
         */
        Resources summarizeContainerResource(
                AppKind kind, String component, Supplier<ResourceRequirements> mainContainerResource) {

            Map<String, Long> podLimits = new HashMap<>();
            Map<String, Long> podRequests = new HashMap<>();
            if (mainContainerResource != null) {
                add(podRequests, mainContainerResource.get().getRequests());
                add(podLimits, mainContainerResource.get().getLimits());
            }
            List<ResourceRequirements> resourceRequirementOfSidecars =
                    ResourceHelper.getInstance().getResourceRequirementOfSidecars(kind, component);

            for (ResourceRequirements res : resourceRequirementOfSidecars) {
                add(podRequests, res.getRequests());
                add(podLimits, res.getLimits());
            }

            Resources resources = new Resources(podRequests, podLimits);
            log.debug("get pod resources of [{}:{}], {} ", kind, component, resources);
            return resources;

        }

        private void add(Map<String, Long> acc, Map<String, Quantity> resource) {
            acc.compute(CPU, (k, v) -> (v == null ? 0 : v)
                    + (resource.get(CPU) == null ? 0 :
                    MetricUtil.getCpuMilliCores(resource.get(CPU).toString())));
            acc.compute(MEMORY, (k, v) -> (v == null ? 0 : v)
                    + (resource.get(MEMORY) == null ? 0 :
                    MetricUtil.getLongValue(resource.get(MEMORY).toString())));
            acc.compute(STORAGE, (k, v) -> (v == null ? 0 : v)
                    + (resource.get(STORAGE) == null ? 0 :
                    MetricUtil.getLongValue(resource.get(STORAGE).toString())));

        }

        /**
         * todo  mongo log-pv volumeTemplate
         */
        private Map<String, ? extends Number> getAppLimits(String component) {
            DefaultAppKindService defaultService = (DefaultAppKindService) appKindService;
            Map totalContainerLimits = defaultService.getTotalContainerLimits(component);
            logInfo("get app containers limits summary: " + totalContainerLimits);
            return totalContainerLimits;
        }

        /**
         * 获取当前应用pod实例所部署在的node集合
         */
        private Set<String> getAppNodes(Integer appId,String component) {
            DefaultAppKindService defaultService = (DefaultAppKindService) appKindService;
            return defaultService.getAppNodes(appId, component);
        }

        private Map<String, Number> merge(Map<String, Number> mainContainer, Map<String, ? extends Number> others) {
            Map<String, Number> merged = new HashMap<>(mainContainer);
            for (String key : merged.keySet()) {
                merged.compute(key, (k,v) -> {
                    if (others.containsKey(key)) {
                        return plus(v, others.get(key));
                    }
                    return v;
                });
            }
            return merged;
        }

        private  Number plus(Number v, Number number) {
            if (v instanceof Integer) {
                return v.intValue() + number.intValue();
            }
            if (v instanceof Long) {
                return v.longValue() + number.longValue();
            }
            return v.doubleValue() + number.doubleValue();
        }

        /**
         * 页面资源参数与配额limits.cpu,limits.memory,requests.storage比较.
         * cpuIncr单位milli core, memory,storage单位byte
         * @param cpuIncr 单位豪核
         * @param memoryIncr 单位字节
         * @param diskIncr 单位字节
         * @param quotaMetric Key: [limits.cpu,limits.memory,requests.storage], 单位同
         */
        public void checkTenantQuota(int kubeId, int member, int cpuIncr, long memoryIncr,
                                      long diskIncr, Map<String, MetricVO> quotaMetric) {
            logInfo("检查部门配额，资源申请量" + ImmutableMap.of("cpu", cpuIncr, "memory", memoryIncr, "disk", diskIncr));
            logInfo("当前配额" + quotaMetric.toString());
            if (quotaMetric.isEmpty()) return;
            MetricVO cpuMetric = quotaMetric.get(CloudAppConstant.QuotaItems.LIMIT_CPU);
            MetricVO memoryMetric = quotaMetric.get(CloudAppConstant.QuotaItems.LIMIT_MEMORY);
            MetricVO storageMetric = quotaMetric.get(CloudAppConstant.QuotaItems.REQUEST_STORAGE);

            StringBuilder errorMsg = new StringBuilder();
            if (cpuMetric != null ) {
                if (cpuMetric.getCapacity().getValue() > 0
                        && cpuMetric.getRequest().getValue() + cpuIncr * member > cpuMetric.getCapacity().getValue()) {
                    errorMsg.append(log("cpu",
                            MetricUtil.humanReadableCpuCount((int) ((cpuMetric.getCapacity().getValue() - cpuMetric.getRequest().getValue()))),
                            MetricUtil.humanReadableCpuCount(cpuIncr)));
                }
                cpuMetric.getRequest().setValue(cpuMetric.getRequest().getValue() + cpuIncr * member);
            }
            if (memoryMetric != null) {
                if (memoryMetric.getCapacity().getValue() > 0
                        && memoryMetric.getRequest().getValue() + memoryIncr * member > memoryMetric.getCapacity().getValue()) {
                    errorMsg.append(log("内存",
                            MetricUtil.humanReadableByteCountBin(memoryMetric.getCapacity().getValue().longValue() - memoryMetric.getRequest().getValue().longValue()),
                            MetricUtil.humanReadableByteCountBin(memoryIncr)));
                }
                memoryMetric.getRequest().setValue(memoryMetric.getRequest().getValue() + memoryIncr * member);
            }
            if (storageMetric != null) {
                if (storageMetric.getCapacity().getValue() > 0 // 需要一个有效的cap用来比较
                        && storageMetric.getRequest().getValue() + diskIncr * member > storageMetric.getCapacity().getValue()) {
                    errorMsg.append(log("存储",
                            MetricUtil.humanReadableByteCountBin(storageMetric.getCapacity().getValue().longValue() - storageMetric.getRequest().getValue().longValue()),
                            MetricUtil.humanReadableByteCountBin(diskIncr)));
                }
                storageMetric.getRequest().setValue(storageMetric.getRequest().getValue() + diskIncr * member);
            }

            if (errorMsg.length() > 0) {
                log.error(errorMsg.toString());
                String k8Name = kubeConfigService.get(kubeId).getName();
                throw new CustomException(600, String.format("租户资源配额在集群[%s]不足，请联系管理员", k8Name));
            }
            // *Note*: if metric is null passed

        }

        private String log(String... args) {
            String format = String.format("%s：剩余 %s，单个节点申请：%s<br>", args[0], args[1], args[2]);
            return format;
        }

        private void logInfo(String msg) {
            log.info("[appkindservice handler]" + msg);
        }

        /**
         * 处理两个重载方法scale(id, members)和scale(id, overridespec, actionenum)，以及不同类型的不同参数
         */
        private List<ResourceQuantity> parseScale(Object[] args) {
            int id = (int) args[0];
            // current spec
            OverrideSpec overrideSpec = appKindService.reviewSpec(id);
            tenantId.set(overrideSpec.getTenantId());
            // increased number
            switch (appKindService.getKind()) {
                case Redis:
                    RedisOverrideSpec redis_scale_spec = (RedisOverrideSpec) args[1];
                    RedisOverrideSpec redis_spec = (RedisOverrideSpec) overrideSpec;

                    ResourceQuantity comp_redis = new ResourceQuantityBuilder()
                            .appId(id)
                            .kubeId(redis_spec.getKubeId())
                            .kubeSchedulerId(redis_spec.getKubeSchedulerId())
                            .member(redis_scale_spec.getMembers() - redis_spec.getMembers())
                            .resources(summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.DB.toString(),
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, redis_spec.getCpu(), MEMORY, redis_spec.getMemory(), STORAGE, redis_spec.getDisk()))))
                            .build();
                    ResourceQuantity comp_sentinel = new ResourceQuantityBuilder()
                            .kubeId(redis_spec.getKubeId())
                            .kubeSchedulerId(redis_spec.getKubeSchedulerId())
                            .member(redis_scale_spec.getSentinelMembers() == 0 ? 1 : redis_spec.getSentinelMembers())
                            .resources(summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.MONITOR.toString(),
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, redis_spec.getSentinelCpu(),
                                            MEMORY, redis_spec.getSentinelMemory(),
                                            STORAGE, redis_spec.getSentinelDisk()))))
                            .build();
                    return ImmutableList.of(comp_redis, comp_sentinel);
                case Kafka:
                    KafkaService.KafkaOverrideSpec kafka_scale_spec = (KafkaService.KafkaOverrideSpec) args[1];
                    KafkaService.KafkaOverrideSpec kafka_spec = (KafkaService.KafkaOverrideSpec) overrideSpec;

                    ResourceQuantity comp_kafka = new ResourceQuantityBuilder()
                            .appId(id)
                            .kubeId(kafka_spec.getKubeId())
                            .kubeSchedulerId(kafka_spec.getKubeSchedulerId())
                            .member(kafka_scale_spec.getMembers() - kafka_spec.getMembers())
                            .resources(summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.DB.toString(),
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, kafka_spec.getCpu(), MEMORY, kafka_spec.getMemory(), STORAGE, kafka_spec.getDisk()))))
                            .build();
                    ResourceQuantity comp_zk = new ResourceQuantityBuilder()
                            .kubeId(kafka_spec.getKubeId())
                            .kubeSchedulerId(kafka_spec.getKubeSchedulerId())
                            .member(kafka_scale_spec.getZkMembers() == 0 ? 1 : kafka_spec.getZkMembers())
                            .resources(summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.MONITOR.toString(),
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, kafka_spec.getZkCpu(),
                                            MEMORY, kafka_spec.getZkMemory(),
                                            STORAGE, kafka_spec.getZkDisk()))))
                            .build();
                    return ImmutableList.of(comp_kafka, comp_zk);

                case Dameng:
                    DamengService.DMOverrideSpec dmScaleSpec = (DamengService.DMOverrideSpec) args[1];
                    DamengService.DMOverrideSpec dmSpec = (DamengService.DMOverrideSpec) overrideSpec;

                    ResourceQuantity dm_db = new ResourceQuantityBuilder()
                            .appId(id)
                            .kubeId(dmSpec.getKubeId())
                            .kubeSchedulerId(dmSpec.getKubeSchedulerId())
                            .member(dmScaleSpec.getDataSize() - dmSpec.getDataSize())
                            .resources(summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.DB.toString(),
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, dmSpec.getDataCpu(), MEMORY, dmSpec.getDataMemory(), STORAGE, dmSpec.getDisk()))))
                            .build();
                    ResourceQuantity dm_monitor = new ResourceQuantityBuilder()
                            .kubeId(dmSpec.getKubeId())
                            .kubeSchedulerId(dmSpec.getKubeSchedulerId())
                            .member(dmScaleSpec.getMonitorSize() == 0 ? 1 : dmSpec.getMonitorSize())
                            .resources(summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.MONITOR.toString(),
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, dmSpec.getMonitorCpu(), MEMORY, dmSpec.getMonitorMemory(), STORAGE, null))))
                            .build();
                    return ImmutableList.of(dm_db, dm_monitor);
                case Elasticsearch:
                    ElasticsearchClusterService.EsOverrideSpec esScaleSpec = (ElasticsearchClusterService.EsOverrideSpec) args[1];
                    ElasticsearchClusterService.EsOverrideSpec spec =
                            (ElasticsearchClusterService.EsOverrideSpec) overrideSpec;
                    List<ResourceQuantity> quantities = new ArrayList<>();
                    // master node
                    quantities.add(new ResourceQuantityBuilder()
                            .appId(id)
                            .kubeId(spec.getKubeId())
                            .kubeSchedulerId(spec.getKubeSchedulerId())
                            .member(esScaleSpec.getMasterSize() - spec.getMasterSize())
                            .resources(summarizeContainerResource(appKindService.getKind(), ElasticSearch.MASTER,
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, spec.getMasterCpu(), MEMORY, spec.getMasterMemory(), STORAGE, spec.getMasterDisk()))))
                            .antiAffinity(getAppNodes(id, "es-" + ElasticSearch.MASTER + "s"))
                            .build());

                    // data node
                    if (esScaleSpec.getDataSize() > spec.getDataSize()) {
                        quantities.add(new ResourceQuantityBuilder()
                                .appId(id)
                                .kubeId(spec.getKubeId())
                                .kubeSchedulerId(spec.getKubeSchedulerId())
                                .member(esScaleSpec.getDataSize() - spec.getDataSize())
                                .resources(summarizeContainerResource(appKindService.getKind(), ElasticSearch.DATA,
                                        () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                                CPU, spec.getDataCpu(), MEMORY, spec.getDataMemory(), STORAGE, spec.getDataDisk()))))
                                .antiAffinity(getAppNodes(id, "es-" + ElasticSearch.DATA))
                                .build());
                    }
                    return quantities;
                case Broker:
                case Redis_Cluster:
                    OverrideSpec rcScaleSpec = (OverrideSpec) args[1];
                    if (rcScaleSpec.getMasterSize() == null) rcScaleSpec.setMasterSize(overrideSpec.getMasterSize());
                    if (rcScaleSpec.getSpareSize() == null) rcScaleSpec.setSpareSize(overrideSpec.getSpareSize() * rcScaleSpec.getMasterSize());
                    ResourceQuantity redisQuantity = new ResourceQuantityBuilder()
                            .appId(id)
                            .kubeId(overrideSpec.getKubeId())
                            .kubeSchedulerId(overrideSpec.getKubeSchedulerId())
                            .member(rcScaleSpec.getMasterSize() + rcScaleSpec.getSpareSize() - overrideSpec.getMembers())
                            .resources(summarizeContainerResource(appKindService.getKind(), null,
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, overrideSpec.getCpu(), MEMORY, overrideSpec.getMemory(), STORAGE, overrideSpec.getDisk()))))
                            .antiAffinity(getAppNodes(id, null))
                            .build();
                    return ImmutableList.of(redisQuantity);
                case MongoDB_Cluster:
                    OverrideSpec mgScaleSpec = (OverrideSpec) args[1];
                    MongoDbClusterService.MongoDBClusterOverrideSpec mgCurSpec =
                            (MongoDbClusterService.MongoDBClusterOverrideSpec) overrideSpec;
                    // 接口参数特殊处理
                    if (mgScaleSpec.getMasterSize() == null) mgScaleSpec.setMasterSize(overrideSpec.getMasterSize());
                    if (mgScaleSpec.getSpareSize() == null) mgScaleSpec.setSpareSize((overrideSpec.getSpareSize() / overrideSpec.getMasterSize()) * mgScaleSpec.getMasterSize());
                    if (mgScaleSpec.getConfigServersMembers() == 0) mgScaleSpec.setConfigServersMembers(overrideSpec.getConfigServersMembers());
                    if (mgScaleSpec.getRouterServersMembers() == 0) mgScaleSpec.setRouterServersMembers(overrideSpec.getRouterServersMembers());
                    // shard
                    ResourceQuantity shardQuantity = new ResourceQuantityBuilder()
                            .appId(id)
                            .kubeId(overrideSpec.getKubeId())
                            .kubeSchedulerId(overrideSpec.getKubeSchedulerId())
                            .member(mgScaleSpec.getMasterSize() + mgScaleSpec.getSpareSize() - mgCurSpec.getMasterSize() - mgCurSpec.getSpareSize())
                            .resources(summarizeContainerResource(appKindService.getKind(), null,
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, overrideSpec.getCpu(), MEMORY, overrideSpec.getMemory(), STORAGE, overrideSpec.getDisk()))))
                            .antiAffinity(getAppNodes(id, MongoDB.SHARD))
                            .build();
                    // config
                    ResourceQuantity configQuantity = new ResourceQuantityBuilder()
                            .appId(id)
                            .kubeId(overrideSpec.getKubeId())
                            .kubeSchedulerId(overrideSpec.getKubeSchedulerId())
                            .member(mgScaleSpec.getConfigServersMembers() - mgCurSpec.getConfigServersMembers())
                            .resources(summarizeContainerResource(appKindService.getKind(), MongoDB.CONFIG,
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, mgCurSpec.getConfigServersCpu(), MEMORY, mgCurSpec.getConfigServersMemory(),
                                            STORAGE, mgCurSpec.getConfigServersDisk()))))
                            .antiAffinity(getAppNodes(id, MongoDB.CONFIG))
                            .build();
                    // router
                    ResourceQuantity routerQuantity = new ResourceQuantityBuilder()
                            .appId(id)
                            .kubeId(overrideSpec.getKubeId())
                            .kubeSchedulerId(overrideSpec.getKubeSchedulerId())
                            .member(mgScaleSpec.getRouterServersMembers() - mgCurSpec.getRouterServersMembers())
                            .resources(summarizeContainerResource(appKindService.getKind(), MongoDB.ROUTER,
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, mgCurSpec.getRouterServersCpu(), MEMORY, mgCurSpec.getRouterServersMemory(),
                                            STORAGE, mgCurSpec.getRouterServersDisk()))))
                            .antiAffinity(getAppNodes(id, MongoDB.ROUTER))
                            .build();
                    return ImmutableList.of(shardQuantity, configQuantity, routerQuantity);
                case MongoDB:
                    OverrideSpec mgscaleSpec = (OverrideSpec) args[1];
                    ResourceQuantity mgQuantity = new ResourceQuantityBuilder()
                            .appId(id)
                            .kubeId(overrideSpec.getKubeId())
                            .kubeSchedulerId(overrideSpec.getKubeSchedulerId())
                            .member(mgscaleSpec.getMembers() - overrideSpec.getMembers())
                            .resources(summarizeContainerResource(appKindService.getKind(), null,
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, overrideSpec.getCpu(), MEMORY, overrideSpec.getMemory(),
                                            STORAGE, MetricUtil.sumStorageWithUnit(overrideSpec.getDisk(), LOG_PV_SIZE)))))
                            .antiAffinity(getAppNodes(id, null))
                            .build();
                    return ImmutableList.of(mgQuantity);
                case TIDB:
                    TidbService.TidbOverrideSpec tidbScaleSpec = (TidbService.TidbOverrideSpec) args[1];
                    TidbService.TidbOverrideSpec tidbCurSpec = (TidbService.TidbOverrideSpec) overrideSpec;

                    // pd
                    ResourceQuantity pdQuantity = new ResourceQuantityBuilder()
                            .appId(id)
                            .kubeId(overrideSpec.getKubeId())
                            .kubeSchedulerId(overrideSpec.getKubeSchedulerId())
                            .member(tidbScaleSpec.getPdReplicas() - tidbCurSpec.getPdReplicas())
                            .resources(summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.PD.name(),
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, tidbCurSpec.getPdCpu(), MEMORY, tidbCurSpec.getPdMemory(), STORAGE, tidbCurSpec.getPdDisk()))))
                            .antiAffinity(getAppNodes(id, ComponentKindEnum.PD.name()))
                            .build();

                    // tidb
                    ResourceQuantity tidbQuantity = new ResourceQuantityBuilder()
                            .appId(id)
                            .kubeId(overrideSpec.getKubeId())
                            .kubeSchedulerId(overrideSpec.getKubeSchedulerId())
                            .member(tidbScaleSpec.getTidbReplicas() - tidbCurSpec.getTidbReplicas())
                            .resources(summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.PD.name(),
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, tidbCurSpec.getTidbCpu(), MEMORY, tidbCurSpec.getTidbMemory(), STORAGE, ""))))
                            .antiAffinity(getAppNodes(id, ComponentKindEnum.TIDB.name()))
                            .build();

                    // tikv
                    ResourceQuantity tikvQuantity = new ResourceQuantityBuilder()
                            .appId(id)
                            .kubeId(overrideSpec.getKubeId())
                            .kubeSchedulerId(overrideSpec.getKubeSchedulerId())
                            .member(tidbScaleSpec.getTikvReplicas() - tidbCurSpec.getTikvReplicas())
                            .resources(summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.PD.name(),
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, tidbCurSpec.getTikvCpu(), MEMORY, tidbCurSpec.getTikvMemory(), STORAGE, tidbCurSpec.getTikvDisk()))))
                            .antiAffinity(getAppNodes(id, ComponentKindEnum.TIDB.name()))
                            .build();
                    return ImmutableList.of(pdQuantity, tidbQuantity, tikvQuantity);
                default: // mysql, pg, og, kafka, zk, mongo(replicaset), redis(ha), sentinel, nameserver
                    int members;
                    // 处理重载方法scale(id, members)和scale(id, overridespec, actionenum)
                    if (args[1] instanceof OverrideSpec) {
                        OverrideSpec scaleSpec = (OverrideSpec) args[1];
                        members = scaleSpec.getMembers();
                    } else {
                        members = (int) args[1]; // 参数需统一为总数而不是增量
                    }
                    ResourceQuantity defaultQuantity = new ResourceQuantityBuilder()
                            .appId(id)
                            .kubeId(overrideSpec.getKubeId())
                            .kubeSchedulerId(overrideSpec.getKubeSchedulerId())
                            .member(members - overrideSpec.getMembers())
                            .resources(summarizeContainerResource(appKindService.getKind(), null,
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, overrideSpec.getCpu(), MEMORY, overrideSpec.getMemory(),
                                            STORAGE, overrideSpec.getDisk()))))
                            .antiAffinity(getAppNodes(id, null))
                            .build();
                    return ImmutableList.of(defaultQuantity);
            }
        }

        /**
         * <p>资源升级仅升级limit，不会升级request，调度过程仅与request有关。</p>
         * <p></p>
         * 资源升级为滚动升级，即重新为同名的pod，需要先删除旧的
         * 仅处理升级，
         * pod重新调度考虑共享存储和非共享存储，后者需要在当前节点重建。
         *
         * @return 资源提升的部分，这部分为在节点上新申请的资源
         */
        private List<ResourceQuantity> parseUpdate(Object[] args) {
            ResourceDTO patch = (ResourceDTO) args[0];
            Integer appId = patch.getAppId() == null ? patch.getId() : patch.getAppId();
            OverrideSpec overrideSpec = appKindService.reviewSpec(appId);
            tenantId.set(overrideSpec.getTenantId());
            List<ResourceQuantity> quantities = parseUpdate(patch, overrideSpec);
            boolean decreaseStorageCheck = quantities.stream().noneMatch(resourceQuantity ->
                    resourceQuantity.getLimits().get(STORAGE).longValue() < 0
            );
            if (! decreaseStorageCheck) {
                throw new CustomException(600, "不允许减少存储大小");
            }
            return quantities;
        }

        private List<ResourceQuantity> parseUpdate(ResourceDTO patch, OverrideSpec overrideSpec) {
            switch (appKindService.getKind()) {
                case Kafka:
                    KafkaService.KafkaResourceDTO kafka_patch = (KafkaService.KafkaResourceDTO) patch;
                    KafkaService.KafkaOverrideSpec kafka_spec = (KafkaService.KafkaOverrideSpec) overrideSpec;
                    ResourceQuantity comp_kafka = new ResourceQuantityBuilder()
                            .kubeId(kafka_spec.getKubeId())
                            .kubeSchedulerId(kafka_spec.getKubeSchedulerId())
                            .member(kafka_spec.getMembers())
                            .limits(ImmutableMap.of(CPU, cpu(kafka_patch.getCpu()) - cpu(kafka_spec.getCpu()),
                                    MEMORY, memoryOrDisk(kafka_patch.getMemory()) - memoryOrDisk(kafka_spec.getMemory()),
                                    STORAGE, memoryOrDisk(kafka_patch.getDisk()) - memoryOrDisk(kafka_spec.getDisk())
                            ))
                            .build();
                    ResourceQuantity comp_zk = new ResourceQuantityBuilder()
                            .kubeId(kafka_spec.getKubeId())
                            .kubeSchedulerId(kafka_spec.getKubeSchedulerId())
                            .member(kafka_spec.getZkMembers())
                            .limits(ImmutableMap.of(CPU, cpu(kafka_patch.getZkCpu()) - cpu(kafka_spec.getZkCpu()),
                                    MEMORY, memoryOrDisk(kafka_patch.getZkMemory()) - memoryOrDisk(kafka_spec.getZkMemory()),
                                    STORAGE, memoryOrDisk(kafka_patch.getZkDisk()) - memoryOrDisk(kafka_spec.getZkDisk())
                            ))
                            .build();
                    return ImmutableList.of(comp_kafka, comp_zk);

                case Redis:
                    RedisService.RedisResourceDTO redis_patch = (RedisService.RedisResourceDTO) patch;
                    RedisOverrideSpec redis_spec = (RedisOverrideSpec) overrideSpec;
                    ResourceQuantity comp_redis = new ResourceQuantityBuilder()
                            .kubeId(redis_spec.getKubeId())
                            .kubeSchedulerId(redis_spec.getKubeSchedulerId())
                            .member(redis_spec.getMembers())
                            .limits(ImmutableMap.of(CPU, cpu(redis_patch.getCpu()) - cpu(redis_spec.getCpu()),
                                    MEMORY, memoryOrDisk(redis_patch.getMemory()) - memoryOrDisk(redis_spec.getMemory()),
                                    STORAGE, memoryOrDisk(redis_patch.getDisk()) - memoryOrDisk(redis_spec.getDisk())
                            ))
                            .build();
                    ResourceQuantity comp_sentinel = new ResourceQuantityBuilder()
                            .kubeId(redis_spec.getKubeId())
                            .kubeSchedulerId(redis_spec.getKubeSchedulerId())
                            .member(redis_spec.getSentinelMembers())
                            .limits(ImmutableMap.of(CPU, cpu(redis_patch.getSentinelCpu()) - cpu(redis_spec.getSentinelCpu()),
                                    MEMORY, memoryOrDisk(redis_patch.getSentinelMemory()) - memoryOrDisk(redis_spec.getSentinelMemory()),
                                    STORAGE, memoryOrDisk(redis_patch.getDisk()) - memoryOrDisk(redis_spec.getDisk())
                            ))
                            .build();
                    return ImmutableList.of(comp_redis, comp_sentinel);

                case Dameng:
                    DamengResourceDTO dmPatch = (DamengResourceDTO) patch;
                    DamengService.DMOverrideSpec dmSpec = (DamengService.DMOverrideSpec) overrideSpec;
                    ResourceQuantity dm_db = new ResourceQuantityBuilder()
                            .kubeId(dmSpec.getKubeId())
                            .kubeSchedulerId(dmSpec.getKubeSchedulerId())
                            .member(dmSpec.getDataSize())
                            .limits(ImmutableMap.of(CPU, cpu(dmPatch.getCpu()) - cpu(dmSpec.getDataCpu()),
                                    MEMORY, memoryOrDisk(dmPatch.getMemory()) - memoryOrDisk(dmSpec.getDataMemory()),
                                    STORAGE, memoryOrDisk(dmPatch.getDisk()) - memoryOrDisk(dmSpec.getDisk())
                                    ))
                            .build();
                    ResourceQuantity dm_monitor = new ResourceQuantityBuilder()
                            .kubeId(dmSpec.getKubeId())
                            .kubeSchedulerId(dmSpec.getKubeSchedulerId())
                            .member(dmSpec.getMonitorSize() == 0 ? 1 : dmSpec.getMonitorSize())
                            .limits(ImmutableMap.of(CPU, cpu(dmPatch.getMonitorCpu()) - cpu(dmSpec.getMonitorCpu()),
                                    MEMORY, memoryOrDisk(dmPatch.getMonitorMemory()) - memoryOrDisk(dmSpec.getMonitorMemory())
                            ))
                            .build();
                    return ImmutableList.of(dm_db, dm_monitor);
                case Elasticsearch:
                    EsResourceDTO esPatch = (EsResourceDTO) patch;
                    ElasticsearchClusterService.EsOverrideSpec spec =
                            (ElasticsearchClusterService.EsOverrideSpec) overrideSpec;
                    List<ResourceQuantity> quantities = new ArrayList<>();
                    // master node
                    quantities.add(new ResourceQuantityBuilder()
                            .kubeId(spec.getKubeId())
                            .kubeSchedulerId(spec.getKubeSchedulerId())
                            .member(overrideSpec.getMasterSize())
                            .limits(ImmutableMap.of(CPU, cpu(esPatch.getCpu()) - cpu(spec.getMasterCpu()),
                                    MEMORY, memoryOrDisk(esPatch.getMemory()) - memoryOrDisk(spec.getMasterMemory()),
                                    STORAGE, memoryOrDisk(esPatch.getDisk()) - memoryOrDisk(spec.getMasterDisk())))
                            .build());

                    // data node
                    quantities.add(new ResourceQuantityBuilder()
                            .kubeId(spec.getKubeId())
                            .kubeSchedulerId(spec.getKubeSchedulerId())
                            .member(spec.getDataSize())
                            .limits(ImmutableMap.of(CPU, cpu(esPatch.getDataCpu()) - cpu(spec.getDataCpu()),
                                    MEMORY, memoryOrDisk(esPatch.getDataMemory()) - memoryOrDisk(spec.getDataMemory()),
                                    STORAGE, memoryOrDisk(esPatch.getDataDisk()) + memoryOrDisk(esPatch.getBackupDisk()) -
                                            memoryOrDisk(spec.getDataDisk()) - memoryOrDisk(spec.getBackupDisk())))
                            .build());
                    return quantities;
                case MongoDB_Cluster:
                    MongoDbClusterResourceDTO mcPatch = (MongoDbClusterResourceDTO) patch;
                    MongoDbClusterService.MongoDBClusterOverrideSpec mgCurSpec =
                            (MongoDbClusterService.MongoDBClusterOverrideSpec) overrideSpec;
                    // shard
                    ResourceQuantity shardQuantity = new ResourceQuantityBuilder()
                            .kubeId(overrideSpec.getKubeId())
                            .kubeSchedulerId(overrideSpec.getKubeSchedulerId())
                            .member(overrideSpec.getMembers())
                            .limits(ImmutableMap.of(CPU, cpu(patch.getCpu()) - cpu(overrideSpec.getCpu()),
                                    MEMORY, memoryOrDisk(patch.getMemory()) - memoryOrDisk(overrideSpec.getMemory()),
                                    STORAGE, memoryOrDisk(patch.getDisk()) + memoryOrDisk(patch.getBackupDisk()) -
                                            memoryOrDisk(overrideSpec.getDisk()) - memoryOrDisk(overrideSpec.getBackupDisk())))
                            .build();
                    // config
                    ResourceQuantity configQuantity = new ResourceQuantityBuilder()
                            .kubeId(overrideSpec.getKubeId())
                            .kubeSchedulerId(overrideSpec.getKubeSchedulerId())
                            .member(overrideSpec.getMembers())
                            .limits(ImmutableMap.of(CPU, cpu(mcPatch.getRouterServersCpu()) - cpu(mgCurSpec.getConfigServersCpu()),
                                    MEMORY, memoryOrDisk(mcPatch.getConfigServersMemory()) - memoryOrDisk(mgCurSpec.getConfigServersMemory()),
                                    STORAGE, memoryOrDisk(mcPatch.getConfigServersDisk()) - memoryOrDisk(mgCurSpec.getConfigServersDisk())))
                            .build();
                    // router
                    ResourceQuantity routerQuantity = new ResourceQuantityBuilder()
                            .kubeId(overrideSpec.getKubeId())
                            .kubeSchedulerId(overrideSpec.getKubeSchedulerId())
                            .member(overrideSpec.getMembers())
                            .limits(ImmutableMap.of(CPU, cpu(mcPatch.getConfigServersCpu()) - cpu(mgCurSpec.getRouterServersCpu()),
                                    MEMORY, memoryOrDisk(mcPatch.getRouterServersMemory()) - memoryOrDisk(mgCurSpec.getRouterServersMemory()),
                                    STORAGE, memoryOrDisk(mcPatch.getRouterServersDisk()) - memoryOrDisk(mgCurSpec.getRouterServersDisk())))
                            .build();
                    return ImmutableList.of(shardQuantity, configQuantity, routerQuantity);
                case TIDB:
                    TidbResourceDTO tidbPatch = (TidbResourceDTO) patch;
                    TidbService.TidbOverrideSpec tidbCurSpec = (TidbService.TidbOverrideSpec) overrideSpec;
                    // pd
                    ResourceQuantity pdQuantity = new ResourceQuantityBuilder()
                            .kubeId(overrideSpec.getKubeId())
                            .kubeSchedulerId(overrideSpec.getKubeSchedulerId())
                            .member(tidbCurSpec.getPdReplicas())
                            .limits(ImmutableMap.of(CPU, cpu(tidbPatch.getPdCpu()) - cpu(tidbCurSpec.getPdCpu()),
                                    MEMORY, memoryOrDisk(tidbPatch.getPdMemory()) - memoryOrDisk(tidbCurSpec.getPdMemory()),
                                    STORAGE, memoryOrDisk(tidbPatch.getPdDisk()) - memoryOrDisk(tidbCurSpec.getPdDisk())))
                            .build();

                    // tidb
                    ResourceQuantity tidbQuantity = new ResourceQuantityBuilder()
                            .kubeId(overrideSpec.getKubeId())
                            .kubeSchedulerId(overrideSpec.getKubeSchedulerId())
                            .member(tidbCurSpec.getTidbReplicas())
                            .limits(ImmutableMap.of(CPU, cpu(tidbPatch.getTidbCpu()) - cpu(tidbCurSpec.getTidbCpu()),
                                    MEMORY, memoryOrDisk(tidbPatch.getTidbMemory()) - memoryOrDisk(tidbCurSpec.getTidbMemory()),
                                    STORAGE, memoryOrDisk(tidbPatch.getTidbDisk()) - memoryOrDisk(tidbCurSpec.getTidbDisk())))
                            .build();

                    // tikv
                    ResourceQuantity tikvQuantity = new ResourceQuantityBuilder()
                            .kubeId(overrideSpec.getKubeId())
                            .kubeSchedulerId(overrideSpec.getKubeSchedulerId())
                            .member(tidbCurSpec.getTikvReplicas())
                            .limits(ImmutableMap.of(CPU, cpu(tidbPatch.getTikvCpu()) - cpu(tidbCurSpec.getTikvCpu()),
                                    MEMORY, memoryOrDisk(tidbPatch.getTikvMemory()) - memoryOrDisk(tidbCurSpec.getTikvMemory()),
                                    STORAGE, memoryOrDisk(tidbPatch.getTikvDisk()) - memoryOrDisk(tidbCurSpec.getTikvDisk())))
                            .build();

                    return ImmutableList.of(pdQuantity, tidbQuantity, tikvQuantity);
                case Vastbase:
                    VastbaseResourceDTO vbPatch = (VastbaseResourceDTO) patch;
                    VastbaseService.VBOverrideSpec vbSpec = (VastbaseService.VBOverrideSpec) overrideSpec;
                    List<ResourceQuantity> vbQuantities = new ArrayList<>();
                    // master node
                    vbQuantities.add(new ResourceQuantityBuilder()
                            .kubeId(vbSpec.getKubeId())
                            .kubeSchedulerId(vbSpec.getKubeSchedulerId())
                            .member(((VastbaseService.VBOverrideSpec) overrideSpec).getVastbaseSize())
                            .limits(ImmutableMap.of(CPU, cpu(vbPatch.getVastbaseCpu()) - cpu(vbSpec.getVastbaseCpu()),
                                    MEMORY, memoryOrDisk(vbPatch.getVastbaseMemory()) - memoryOrDisk(vbSpec.getVastbaseMemory()),
                                    STORAGE, memoryOrDisk(vbPatch.getVastbaseDisk()) - memoryOrDisk(vbSpec.getVastbaseDisk())))
                            .build());
                    return vbQuantities;
                default: // mysql, pg, og, kafka, zk, mongo(replicaset), redis, sentinel, broker, nameserver
                    ResourceQuantity defaultQuantity = new ResourceQuantityBuilder()
                            .kubeId(overrideSpec.getKubeId())
                            .kubeSchedulerId(overrideSpec.getKubeSchedulerId())
                            .member(overrideSpec.getMembers())
                            .limits(ImmutableMap.of(CPU, cpu(patch.getCpu()) - cpu(overrideSpec.getCpu()),
                                    MEMORY, memoryOrDisk(patch.getMemory()) - memoryOrDisk(overrideSpec.getMemory()),
                                    STORAGE, memoryOrDisk(patch.getDisk()) + memoryOrDisk(patch.getBackupDisk())
                                            - memoryOrDisk(overrideSpec.getDisk()) - memoryOrDisk(overrideSpec.getBackupDisk())))
                            .build();
                    return ImmutableList.of(defaultQuantity);
            }
        }

        /**
         * @return resource quantity series. 应用可能存在不同规格的实例
         */
        private List<ResourceQuantity> parseInstall(CloudAppVO vo) {
            tenantId.set(vo.getOwnerTenant());
            switch (appKindService.getKind()) {
                case Dameng:
                    DamengService.DamengVO dmVo = (DamengService.DamengVO) vo;
                    ResourceQuantity dm_db = new ResourceQuantityBuilder()
                            .kubeId(dmVo.getKubeId())
                            .kubeSchedulerId(dmVo.getKubeSchedulerId())
                            .member(dmVo.getDataSize())
                            .resources(summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.DB.toString(),
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, dmVo.getDataCpu(), MEMORY, dmVo.getDataMemory(), STORAGE, dmVo.getDisk()))))
                            .build();
                    ResourceQuantity dm_monitor = new ResourceQuantityBuilder()
                            .kubeId(dmVo.getKubeId())
                            .kubeSchedulerId(dmVo.getKubeSchedulerId())
                            .member(dmVo.getMonitorSize() == null ? 1 : dmVo.getMonitorSize())
                            .resources(summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.MONITOR.toString(),
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, dmVo.getMonitorCpu(), MEMORY, dmVo.getMonitorMemory(), STORAGE, null))))
                            .build();
                    return ImmutableList.of(dm_db, dm_monitor);
                case Elasticsearch:
                    ElasticsearchClusterService.EsVo esVo = (ElasticsearchClusterService.EsVo) vo;
                    // master node
                    ResourceQuantity master = new ResourceQuantityBuilder()
                            .kubeId(esVo.getKubeId())
                            .kubeSchedulerId(esVo.getKubeSchedulerId())
                            .member(esVo.getMasterSize())
                            .resources(summarizeContainerResource(appKindService.getKind(), ElasticSearch.MASTER,
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, esVo.getMasterCpu(), MEMORY, esVo.getMasterMemory(), STORAGE, esVo.getMasterDisk()))))
                            .build();

                    // data node
                    ResourceQuantity data = new ResourceQuantityBuilder()
                            .kubeId(esVo.getKubeId())
                            .kubeSchedulerId(esVo.getKubeSchedulerId())
                            .member(esVo.getDataSize())
                            .resources(summarizeContainerResource(appKindService.getKind(), ElasticSearch.DATA,
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, esVo.getCpu(), MEMORY, esVo.getMemory(), STORAGE, esVo.getDisk()))))
                            .build();
                    // exporter node
                    ResourceQuantity exporter = new ResourceQuantityBuilder()
                            .kubeId(esVo.getKubeId())
                            .kubeSchedulerId(esVo.getKubeSchedulerId())
                            .member(1)
                            .resources(summarizeContainerResource(appKindService.getKind(), ElasticSearch.EXPORTER, null))
                            .build();
                    return ImmutableList.of(master, data, exporter);
                case MongoDB_Cluster:
                    MongoDbClusterService.MongoDBClusterVO mongoVo = (MongoDbClusterService.MongoDBClusterVO) vo;
                    Resources shardR = summarizeContainerResource(appKindService.getKind(), null,
                            () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                    CPU, mongoVo.getCpu(), MEMORY, mongoVo.getMemory(), STORAGE, mongoVo.getDisk())));
                    // shard node
                    ResourceQuantity shard = new ResourceQuantityBuilder()
                            .kubeId(mongoVo.getKubeId())
                            .kubeSchedulerId(mongoVo.getKubeSchedulerId())
                            .member(mongoVo.getMasterSize())
                            .requests(shardR.getRequests())
                            .limits(shardR.limits)
                            .build();

                    // config node
                    Resources configR = summarizeContainerResource(appKindService.getKind(), MongoDB.CONFIG,
                            () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                    CPU, mongoVo.getConfigServersCpu(), MEMORY, mongoVo.getConfigServersMemory(),
                                    STORAGE, mongoVo.getConfigServersDisk())));
                    ResourceQuantity config = new ResourceQuantityBuilder()
                            .kubeId(mongoVo.getKubeId())
                            .kubeSchedulerId(mongoVo.getKubeSchedulerId())
                            .member(mongoVo.getConfigServersMembers())
                            .requests(configR.getRequests())
                            .limits(configR.getLimits())
                            .build();

                    // route node
                    Resources resourceR = summarizeContainerResource(appKindService.getKind(), MongoDB.ROUTER,
                            () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                    CPU, mongoVo.getRouterServersCpu(), MEMORY, mongoVo.getRouterServersMemory(),
                                    STORAGE, mongoVo.getRouterServersDisk())));

                    ResourceQuantity route = new ResourceQuantityBuilder()
                            .kubeId(mongoVo.getKubeId())
                            .kubeSchedulerId(mongoVo.getKubeSchedulerId())
                            .member(mongoVo.getRouterServersMembers())
                            .requests(resourceR.getRequests())
                            .limits(resourceR.getLimits())
                            .build();

                    return ImmutableList.of(shard, config, route);
                case MongoDB:
                    Resources mongo = summarizeContainerResource(appKindService.getKind(), null,
                            () -> getMainContainerResourceRequest(ImmutableMap.of(
                                    CPU, vo.getCpu(), MEMORY, vo.getMemory(),
                                    STORAGE, MetricUtil.sumStorageWithUnit(vo.getDisk(), LOG_PV_SIZE))));
                    ResourceQuantity mgQuantity = new ResourceQuantityBuilder()
                            .kubeId(vo.getKubeId())
                            .kubeSchedulerId(vo.getKubeSchedulerId())
                            .member(vo.getMembers())
                            .requests(mongo.getRequests())
                            .limits(mongo.getLimits())
                            .build();
                    return ImmutableList.of(mgQuantity);
                case TIDB:
                    TidbService.TidbClusterVO tidbVo = (TidbService.TidbClusterVO) vo;
                    //pd组件
                    Resources pdR = summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.PD.name(),
                            () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                    CPU, tidbVo.getPdCpu(), MEMORY, tidbVo.getPdMemory(), STORAGE, tidbVo.getPdDisk())));

                    // pd node
                    ResourceQuantity pd = new ResourceQuantityBuilder()
                            .kubeId(tidbVo.getKubeId())
                            .kubeSchedulerId(tidbVo.getKubeSchedulerId())
                            .member(tidbVo.getPdReplicas())
                            .requests(pdR.getRequests())
                            .limits(pdR.limits)
                            .build();

                    // config node
                    Resources tidbR = summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.TIDB.name(),
                            () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                    CPU, tidbVo.getTidbCpu(), MEMORY, tidbVo.getTidbMemory(),
                                    STORAGE, tidbVo.getTidbDisk())));

                    ResourceQuantity tidb = new ResourceQuantityBuilder()
                            .kubeId(tidbVo.getKubeId())
                            .kubeSchedulerId(tidbVo.getKubeSchedulerId())
                            .member(tidbVo.getTidbReplicas())
                            .requests(tidbR.getRequests())
                            .limits(tidbR.getLimits())
                            .build();

                    // route node
                    Resources tikvR = summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.TIKV.name(),
                            () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                    CPU, tidbVo.getTikvCpu(), MEMORY, tidbVo.getTikvMemory(),
                                    STORAGE, tidbVo.getTikvDisk())));

                    ResourceQuantity tikv = new ResourceQuantityBuilder()
                            .kubeId(tidbVo.getKubeId())
                            .kubeSchedulerId(tidbVo.getKubeSchedulerId())
                            .member(tidbVo.getTikvReplicas())
                            .requests(tikvR.getRequests())
                            .limits(tikvR.getLimits())
                            .build();

                    return ImmutableList.of(pd, tidb, tikv);
                case Vastbase:
                    VastbaseService.VastbaseVO vbVo = (VastbaseService.VastbaseVO) vo;
                    // vastbase node
                    ResourceQuantity vastbase = new ResourceQuantityBuilder()
                            .kubeId(vbVo.getKubeId())
                            .kubeSchedulerId(vbVo.getKubeSchedulerId())
                            .member(vbVo.getVastbaseSize())
                            .resources(summarizeContainerResource(appKindService.getKind(), CloudAppConstant.Vastbase.VASTBASE,
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, vbVo.getVastbaseCpu(), MEMORY, vbVo.getVastbaseMemory(), STORAGE, vbVo.getVastbaseDisk()))))
                            .build();

                    // dcs node
                    ResourceQuantity dcs = new ResourceQuantityBuilder()
                            .kubeId(vbVo.getKubeId())
                            .kubeSchedulerId(vbVo.getKubeSchedulerId())
                            .member(vbVo.getDcsSize())
                            .resources(summarizeContainerResource(appKindService.getKind(), CloudAppConstant.Vastbase.DCS,
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, vbVo.getDcsCpu(), MEMORY, vbVo.getDcsMemory(), STORAGE, vbVo.getDisk()))))
                            .build();
                    return ImmutableList.of(vastbase, dcs);

                case Redis:
                    RedisVO redisVo = (RedisVO) vo;
                    ResourceQuantity comp_redis = new ResourceQuantityBuilder()
                            .kubeId(redisVo.getKubeId())
                            .kubeSchedulerId(redisVo.getKubeSchedulerId())
                            .member(redisVo.getMembers())
                            .resources(summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.DB.toString(),
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, redisVo.getCpu(), MEMORY, redisVo.getMemory(), STORAGE, redisVo.getDisk()))))
                            .build();
                    ResourceQuantity sentinelComp = new ResourceQuantityBuilder()
                            .kubeId(redisVo.getKubeId())
                            .kubeSchedulerId(redisVo.getKubeSchedulerId())
                            .member(redisVo.getSentinelMembers() == 0 ? 1 : redisVo.getSentinelMembers())
                            .resources(summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.MONITOR.toString(),
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, redisVo.getSentinelCpu(),
                                            MEMORY, redisVo.getSentinelMemory(),
                                            STORAGE, redisVo.getSentinelDisk()))))
                            .build();
                    return ImmutableList.of(comp_redis, sentinelComp);

                case Kafka:
                    KafkaService.KafkaVO kafkaVo = (KafkaService.KafkaVO) vo;
                    ResourceQuantity comp_kafka = new ResourceQuantityBuilder()
                            .kubeId(kafkaVo.getKubeId())
                            .kubeSchedulerId(kafkaVo.getKubeSchedulerId())
                            .member(kafkaVo.getMembers())
                            .resources(summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.DB.toString(),
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, kafkaVo.getCpu(), MEMORY, kafkaVo.getMemory(), STORAGE, kafkaVo.getDisk()))))
                            .build();
                    ResourceQuantity comp_zk = new ResourceQuantityBuilder()
                            .kubeId(kafkaVo.getKubeId())
                            .kubeSchedulerId(kafkaVo.getKubeSchedulerId())
                            .member(kafkaVo.getZkMembers() == 0 ? 1 : kafkaVo.getZkMembers())
                            .resources(summarizeContainerResource(appKindService.getKind(), ComponentKindEnum.MONITOR.toString(),
                                    () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                            CPU, kafkaVo.getZkCpu(),
                                            MEMORY, kafkaVo.getZkMemory(),
                                            STORAGE, kafkaVo.getZkDisk()))))
                            .build();
                    return ImmutableList.of(comp_kafka, comp_zk);
                default:
                    Resources default_ = summarizeContainerResource(appKindService.getKind(), null,
                            () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                    CPU, vo.getCpu(), MEMORY, vo.getMemory(), STORAGE, vo.getDisk())));

                    ResourceQuantity quantity = new ResourceQuantityBuilder()
                            .kubeId(vo.getKubeId())
                            .kubeSchedulerId(vo.getKubeSchedulerId())
                            .member(vo.getMembers())
                            .requests(default_.getRequests())
                            .limits(default_.getLimits())
                            .build();
                    return ImmutableList.of(quantity);
            }
        }

        /**
         * migratedNode will schedule to newNode, also acquire from quota.
         */
        private List<ResourceQuantity> parseMigrate(Object[] args) {
            if (appKindService instanceof MigrateOperation) {
                MigrateDTO migrateDTO = (MigrateDTO) args[0];
                OverrideSpec overrideSpec = appKindService.reviewSpec(migrateDTO.getAppId());
                tenantId.set(overrideSpec.getTenantId());
                Resources resources = summarizeContainerResource(appKindService.getKind(), null,
                        () -> getMainContainerResourceRequest(NullableImmutableMap.of(
                                CPU, overrideSpec.getCpu(), MEMORY, overrideSpec.getMemory(), STORAGE, overrideSpec.getDisk())));
                Set<String> appNodes = null;
                switch (appKindService.getKind()) {
                    case Dameng:
                        appNodes = getAppNodes(migrateDTO.getAppId(), DamengService.Name.ENTRY_DATABASE);
                        break;
                    default:
                        appNodes = getAppNodes(migrateDTO.getAppId(), null);
                }
                ResourceQuantity defaultQuantity = new ResourceQuantityBuilder()
                        .kubeId(overrideSpec.getKubeId())
                        .kubeSchedulerId(overrideSpec.getKubeSchedulerId())
                        .member(migrateDTO.getMigrateNodes().size())
                        .node(migrateDTO.getMigrateNodes().get(0).getNewNode().getNode())
                        .antiAffinity(appNodes) // 设置反亲和时不能迁移到当前所在nodes
                        .appId(migrateDTO.getAppId())
                        .requests(resources.getRequests())
                        .limits(resources.getLimits())
                        .build();
                return ImmutableList.of(defaultQuantity);
            }
            return null;
        }

        private long memoryOrDisk(String resource) {
            return MetricUtil.getResourceLongValue(resource);
        }

        private Integer cpu(String cpu) {
            if (StringUtils.isEmpty(cpu)) return 0;
            return MetricUtil.getCpuMilliCores(cpu);
        }

        // parse resource requested quantity

        @Data
        @AllArgsConstructor
        public class ResourceQuantity {
            private int kubeId;
            private Integer appId;
            private Integer kubeSchedulerId;
            private String node;
            private int member;

            /**
             * cpu 单位豪核, 内存、存储 单位字节. 存储记入limits
             */
            Map<String, ? extends Number> requests;
            Map<String, ? extends Number> limits;

            private Set<String> antiAffinity;
        }

        private class ResourceQuantityBuilder {
            private int kubeId;
            private Integer appId;
            private Integer kubeSchedulerId;
            private String node;
            private int member;

            Map<String, ? extends Number> requests;
            Map<String, ? extends Number> limits;

            private Set<String> antiAffinity;

            public ResourceQuantityBuilder resources(Resources resources) {
                this.requests = resources.getRequests();
                this.limits = resources.getLimits();
                return this;
            }

            public ResourceQuantityBuilder requests(Map<String, ? extends Number> requests) {
                this.requests = requests;
                return this;
            }

            public ResourceQuantityBuilder limits(Map<String, ? extends Number> limits) {
                this.limits = limits;
                return this;
            }

            public ResourceQuantityBuilder kubeId(int kubeId) {
                this.kubeId = kubeId;
                return this;
            }

            public ResourceQuantityBuilder appId(Integer appId) {
                this.appId = appId;
                return this;
            }

            public ResourceQuantityBuilder kubeSchedulerId(Integer kubeSchedulerId) {
                this.kubeSchedulerId = kubeSchedulerId;
                return this;
            }

            public ResourceQuantityBuilder node(String node) {
                this.node = node;
                return this;
            }

            public ResourceQuantityBuilder member(int member) {
                this.member = member;
                return this;
            }

            public ResourceQuantityBuilder antiAffinity(Set<String> antiAffinity) {
                this.antiAffinity = antiAffinity;
                return this;
            }

            public ResourceQuantity build() {
                return new ResourceQuantity(kubeId, appId, kubeSchedulerId, node, member, requests, limits, antiAffinity);
            }
        }
    }

    static class Resources {
        public Resources(Map<String, Long> requests, Map<String, Long> limits) {
            this.requests = requests;
            this.limits = limits;
        }

        Map<String, Long> limits;

        public Map<String, Long> getLimits() {
            return limits;
        }

        public void setLimits(Map<String, Long> limits) {
            this.limits = limits;
        }

        public Map<String, Long> getRequests() {
            return requests;
        }

        public void setRequests(Map<String, Long> requests) {
            this.requests = requests;
        }

        Map<String, Long> requests;


        @Override
        public String toString() {
            return "Resources{" +
                    "limits=" + limits +
                    ", requests=" + requests +
                    '}';
        }
    }

}
