package cn.newdt.cloud.web;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.domain.CloudAppLogic;
import cn.newdt.cloud.domain.alert.AlertChannel;
import cn.newdt.cloud.domain.alert.AlertContactGroup;
import cn.newdt.cloud.domain.alert.AlertMetric;
import cn.newdt.cloud.domain.alert.AlertRuleConfig;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.service.CloudAppLogicService;
import cn.newdt.cloud.service.alert.*;
import cn.newdt.cloud.vo.AlertRuleConfigVo;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.response.ResponseResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@Slf4j
public class AlertConfigController {

    @Autowired
    private AlertConfigService configService;
    @Autowired
    private AlertConfigSyncService configSyncService;
    @Autowired
    private AlertNotifyConfigService notifyConfigService;
    @Autowired
    private MetricService metricService;
    @Autowired
    private CloudAppLogicService appLogicService;
    @Autowired
    private RetentionConfigureService retentionConfigureService;


    @ApiOperation("新建告警通知通道")
    @PostMapping("cloudmanage/alert/channel")
    public Object createChannel(@RequestBody AlertChannel channel) {
        int id = notifyConfigService.saveChannel(channel);
        return ImmutableMap.of("id", id);
    }

    @ApiOperation("查询支持的告警通知通道")
    @GetMapping("cloudmanage/alert/channel/list")
    public Object getChannel() {
        List<AlertChannel> channels = notifyConfigService.listChannel();
        return channels;
    }

    @ApiOperation("查询支持的告警通知通道")
    @GetMapping("cloudmanage/alert/channel")
    public Object getChannel(String channel) {
        return notifyConfigService.listChannel().stream()
                .filter(ch -> channel.equals(ch.getChannel())).findAny()
                .orElseThrow(() -> new CustomException(600, "未找到指定配置"));
    }

    @GetMapping("cloudmanage/alert/contact/group")
    public List<AlertContactGroup> getContactGroup() {
        PageHelper.orderBy("group_id desc");
        return notifyConfigService.listContactGroupVo();
    }

    @PostMapping("cloudmanage/alert/contact/group")
    public Object createGroup(@RequestBody AlertContactGroup group) {
        int id;
        if (group.getGroupId() == null)
            id = notifyConfigService.createContactGroup(group);
        else
            id = notifyConfigService.updateContactGroup(group);
        return ImmutableMap.of("id", id);
    }

    @GetMapping("cloudmanage/alert/contact/group/{id}")
    public Object describeGroup(@PathVariable("id") int id) {
        return notifyConfigService.describeGroup(id);
    }

    @DeleteMapping("cloudmanage/alert/contact/group/{id}")
    public Object deleteGroup(@PathVariable("id") int id) {
        return notifyConfigService.deleteGroup(id);
    }

    @ApiOperation("添加规则时查询指标元数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "product", value = "Appkind.product, 能够获取同一product的不同应用类型", example = "Kafka")
    })
    @GetMapping("cloudnormal/alert/metric_meta")
    public List<AlertMetric> getMetricGroup(@RequestParam(required = false) String metricName,
                                            @RequestParam(required = false) String resourceType,
                                            @RequestParam(required = false) String product) {

        ImmutableSet.Builder<String> builder = ImmutableSet.<String>builder().add("All"); // first add common metrics in category All
        List<AlertMetric> metricList = metricService.listMetricMeta(null);
        if (StringUtils.isNotEmpty(product))
            // transform product to kind(=resourceType)
            builder.addAll(AppKind.valueOfProduct(product).stream().map(AppKind::getKind).collect(Collectors.toSet()));
        if (StringUtils.isNotEmpty(resourceType))
            builder.add(resourceType);

        ImmutableSet<String> candidateSet = builder.build();
        metricList = metricList.stream()
                    .filter(metric -> candidateSet.contains(metric.getResourceType()))
                    .collect(Collectors.toList());
        return metricList;
    }

    @ApiOperation("创建告警规则")
    @PostMapping("cloudnormal/alert/rules")
    public Object postRules(@RequestBody AlertRuleConfig alertRules) {
        Integer id = configService.saveAlertRules(alertRules);
        return ImmutableMap.of("id", id);
    }

    @ApiOperation("查询告警规则列表")
    @PostMapping("cloudnormal/alert/rules/list")
    public PageInfo<AlertRuleConfigVo> getRules(@RequestBody PageDTO pageDTO) {
        List<AlertRuleConfigVo> alertRuleSetVoList = configService.getAlertRuleSetVoList(pageDTO);
        return new PageInfo<>(alertRuleSetVoList);
    }

    @ApiOperation("描述单个告警规则")
    @GetMapping("cloudnormal/alert/rules/{id}")
    public AlertRuleConfig describeRule(@PathVariable int id) {
        return configService.describeCurrentRuleSet(id);
    }

    @ApiOperation("查询应用关联的告警规则")
    @GetMapping("cloudnormal/alert/rules")
    public AlertRuleConfig describeRule(@RequestParam(required = false) String namespace,
                                        @RequestParam(required = false) String type,
                                        @RequestParam(required = false) String name,
                                        @RequestParam(required = false) Integer appId
    ) {
        AlertRuleConfig alertRuleConfig;
        if (appId != null) {
            CloudAppLogic cloudAppLogic = appLogicService.get(appId);
            alertRuleConfig = configService.describeCurrentRuleSet(cloudAppLogic.getNamespace(), cloudAppLogic.getKind() + "_" + cloudAppLogic.getArch(), cloudAppLogic.getCrName());
        } else alertRuleConfig = configService.describeCurrentRuleSet(namespace, type, name);
        return alertRuleConfig;
    }

    @ApiOperation("监控告警数据清理配置")
    @ApiResponses({
            @ApiResponse(code = 200, message = "", responseContainer = "List", response = RetentionConfigureService.Configuration.class)
    })
    @GetMapping("cloudmanage/alert/retentionConfig")
    public ResponseResult getRetentionConfiguration() {
        return ResponseResult.ok(retentionConfigureService.list(), null);
    }

    @ApiOperation("修改监控告警清理配置")
    @ApiImplicitParam(dataTypeClass = RetentionConfigureService.Configuration.class, paramType = "body", collectionFormat = "list")
    @PutMapping("cloudmanage/alert/retentionConfig")
    public ResponseResult updateRetentionConfiguration(@RequestBody List<RetentionConfigureService.Configuration> configurations) {
        retentionConfigureService.modifyRetentionConfiguration(configurations);
        return ResponseResult.ok("修改成功", null);
    }
}
