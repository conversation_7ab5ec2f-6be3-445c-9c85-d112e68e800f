package cn.newdt.cloud.service.sched.impl.pg;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.utils.JsonUtil;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import com.fasterxml.jackson.core.type.TypeReference;
import com.shindata.postgre.v1.PostgreSql;
import io.fabric8.kubernetes.client.CustomResource;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

public class PostgreSqlScaleWatch extends PostgreSqlWatch{

    @Override
    public void doStopWatchResource(CloudApp app, TriggerHis triggerHis, OpsResultDTO.Builder result, PostgreSql cr) {
        PostgreSql oldCR = YamlEngine.unmarshal(app.getCr(), PostgreSql.class);
        PostgreSql newCR = YamlEngine.unmarshal(app.getCrRun(), PostgreSql.class);
        List<String> oldIPs = oldCR.getSpec().getIpList(), newIPs = newCR.getSpec().getIpList();
        List<String> scaleInIPS = oldIPs.stream().filter(ip -> !newIPs.contains(ip)).collect(Collectors.toList());
        List<String> scaleOutIPs = newIPs.stream().filter(ip -> !oldIPs.contains(ip)).collect(Collectors.toList());
        if (!scaleInIPS.isEmpty()) {
            String podNameStr = triggerHis.returnMergedJobDataMap().get("oldPodNames");
            if (result.isSuccessful()) {
                // release trimmed ip
                networkService.releaseIp(app, scaleInIPS, postgreSQLService.getIpReservationTarget());
                // 缩容后删除遗留的pv、pvc
                resourceManagerService.cleanStorageScaleInAfter(app, scaleInIPS);
                if (autoManagement) {
                    //纳管端口
                    operationUtil.alterToDMP(app, triggerHis);
                }
            }
        } else if (!scaleOutIPs.isEmpty()) {
            if (result.isSuccessful()) {
                BiFunction<CloudApp, List<String>, PostgreSql> addRemoteFuc = (i, addIps) -> {
                    String yaml = StringUtils.isEmpty(i.getCrRun()) ? i.getCr() : i.getCrRun();
                    PostgreSql cr1 = YamlEngine.unmarshal(yaml, PostgreSql.class);
                    cr1.getSpec().getRemoteIpList().addAll(addIps);
                    return cr1;
                };
                doStopWatchScaleOut(app, PostgreSql.class, addRemoteFuc, scaleOutIPs, oldIPs);
            }
        }
    }

    private <T extends CustomResource> void doStopWatchScaleOut(CloudApp app, Class<T> type, BiFunction<CloudApp, List<String>, T> modifer, List<String> scaleOutIPs, List<String> oldIPs) {
        Integer logicAppId = app.getLogicAppId();
        if (logicAppId != null) {
            appLogicService.getPhysicApps(logicAppId).stream().filter(i->!i.getId().equals(app.getId()))
                    .parallel().forEach(i-> {
                        // 更新表和k8s实际属性
                        // get then update fixme 可能覆盖更新
                        logInfo(app, ActionEnum.SCALE_OUT, "更新remoteIP，新增" + scaleOutIPs);
                        T cr = modifer.apply(i, scaleOutIPs);
                        kubeClientService.get(i.getKubeId()).updateCustomResource(cr, type);
                        String yaml = YamlEngine.marshal(cr); // TODO 触发远程更新watch
                        if (StringUtils.isEmpty(i.getCrRun())) {
                            i.setCr(yaml);
                        } else i.setCrRun(yaml);
                        appService.update(i);
                    });
        }
    }
}
