package cn.newdt.cloud.utils;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.ComponentKindEnum;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.dto.Label;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.vo.CloudAppVO;
import cn.newdt.commons.exception.CustomException;
import com.moandjiezana.toml.TomlWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.sql.Driver;
import java.util.*;

@Slf4j
public class TidbUtil {
    public static final String FILEBEAT_VOLUME_NAME = "tidb-filebeat-volume";
    public static final String TIDB_LOG_VOLUME_NAME = "tidb-log-volume";
    public static final String TIDB_FILEBEAT_CONFIGMAP_NAME = "tidb-filebeat-config";
    public static final String TIDB_LOG_DIR = "/var/log/shindb/tidb/";
    public static String TIDB_ADMIN_USERNAME = "tAdmin";
    public static String TIDB_ROOT_USERNAME = "root";
    public static String TIDB_ADMIN_PASSWORD = "tAdmin";
    public static String TIDB_DRIVER_NAME = "com.mysql.cj.jdbc.Driver";
    public static String TIKV_EXPORTER_SVC_SUFFIX = "-tikv-exporter";

    //备份相关
    //mount path
    public static final String TIDB_MNT_CONTAINER_NAME = "s3fs-container";
    public static final String TIDB_MNT_PATH = "/mnt";
    public static final String TIDB_MNT_VOLUME_NAME = "backup-mnt-shared";

    //backup path
    public static final String TIDB_BACKUP_CONTAINER_NAME = "br-backup";
    public static final String TIDB_BACKUP_VOLUME_NAME = "tikv";
    public static final String TIDB_BACKUP_PATH = "/var/lib/tikv";
    public static final String UMOUNT_SCRIPT = "sh /scripts/mount-remote-storage.sh false /mnt/shared";

    //相关脚本
    public static final String TIDB_SCRIPTS_CONFIGMAP_NAME = "tidb-scripts-config";
    public static final String TIDB_SCRIPT_VOLUME_NAME = "tidb--script-volume-name";


    public static Driver getDriver() throws Exception {
        return (Driver) Class.forName(TIDB_DRIVER_NAME).newInstance();
    }

    public static String getConnectionUrl(String ip, Integer port) {
        return new StringBuilder("jdbc:mysql://").append(ip).append(":").append(port).append("/mysql?").append("useSSL=false&serverTimezone=UTC").toString();
    }

    //连接参数、用户名、密码登<Tidb 默认用户没有 密码> 只有第一次登录可以使用，使用过后会修改密码
    public static Properties getConnectProperties(Boolean isRoot) {
        Properties props = new Properties();
        props.put("user", isRoot ? TIDB_ROOT_USERNAME : TIDB_ADMIN_USERNAME);
        props.put("password", isRoot ? "" : TIDB_ADMIN_PASSWORD);

        return props;
    }

    /**
     * 将 map 转换成 toml String 格式
     *
     * @param map
     * @return
     */
    public static String covertMapToTomlString(Map<String, Object> map) {

        return new TomlWriter().write(convertToTomlMap(map));
    }


    /**
     * 将一个map转换成嵌套格式，
     * 然后再转换成 toml格式
     *
     * @param flatMap
     * @return
     */
    public static Map<String, Object> convertToTomlMap(Map<String, Object> flatMap) {
        Map<String, Object> result = new HashMap<>();
        for (String key : flatMap.keySet()) {
            String[] parts = key.split("\\.");
            Map<String, Object> current = result;
            for (int i = 0; i < parts.length - 1; i++) {
                current = (Map<String, Object>) current.computeIfAbsent(parts[i], k -> new HashMap<>());
            }
            current.put(parts[parts.length - 1], flatMap.get(key));
        }
        return result;
    }

    /**
     * 判断是否有不可修改的参数
     *
     * @param param
     * @param prohibitContent
     */
    public static void prohibitParam(String param, String prohibitContent) {

        if (StringUtils.isAnyEmpty(param, prohibitContent)) {
            return;
        }
        Set<String> result = new HashSet<>();
        String[] params = param.split(",");
        List<String> prohibited = Arrays.asList(prohibitContent.split(","));
        for (String prop : params) {
            if (prohibited.contains(prop)) {
                result.add(prop);
            }
        }
        if (!result.isEmpty()) {
            String join = StringUtils.join(result, "、");
            log.error("[prohibitParam] 参数 {} 禁止修改，请移除", join);
            throw new CustomException(600, "参数 " + join + " 禁止修改，请移除");
        }
    }

    public static Object calculate(CloudAppVO vo, Object expRaw) {
        // 如果expRaw不含变量，而是参数值，直接返回
        String calculateExp = String.valueOf(expRaw).toUpperCase();
        if (!calculateExp.contains("MEMORY") && !calculateExp.contains("CPU") && !calculateExp.contains("DISK") && !calculateExp.contains("CONFIGSERVERSDISK")) {
            return expRaw;
        }
        calculateExp = calculateExp.replaceAll("MAX", "max");
        calculateExp = calculateExp.replaceAll("MIN", "min");
        calculateExp = calculateExp.replaceAll("MEMORY", "#MEMORY");// ＃variableName
        calculateExp = calculateExp.replaceAll("CPU", "#CPU");
        // DISK 需要匹配完整词，避免匹配CONFIGSERVERSDISK中的DISK
        calculateExp = calculateExp.replaceAll("\\bDISK\\b", "#DISK");
        calculateExp = calculateExp.replaceAll("CONFIGSERVERSDISK", "#CONFIGSERVERSDISK");
        ExpressionParser parser = new SpelExpressionParser();
        StandardEvaluationContext context = new StandardEvaluationContext(Math.class);
        // required
        Double cpuCore = MetricUtil.getCpuCores(vo.getCpu());
        context.setVariable("CPU", new Double(cpuCore).intValue());
        Long memory = MetricUtil.getLongValueOfUnit(vo.getMemory(), 'M');
        context.setVariable("MEMORY", new Double(memory).longValue());
        Long disk = MetricUtil.getLongValueOfUnit(vo.getDisk(), 'M');
        context.setVariable("DISK", new Double(disk).longValue());
        return parser.parseExpression(calculateExp).getValue(context, Double.class).longValue() + "";
    }

    public static String buildTiKVExporterServiceName(String crName) {
        // return crName + "-tikv-exporter";
        return crName + TIKV_EXPORTER_SVC_SUFFIX;
    }

    public static String getTidbBackupPath(CloudApp backupApp) {
        return "/mnt/shared/" + AppKind.TIDB.getKind() + "/" + AppKind.TIDB.getArch() + "/" + backupApp.getNamespace() + "/" + backupApp.getCrName() + "/";
    }

    public static boolean isFloat(String value) {
        return value.matches("-?\\d+(\\.\\d+)?");
    }

    public static String getComponentKind(PodDTO pod) {
        return ComponentKindEnum.valueOf(pod.getLabel(CloudAppConstant.CustomLabels.APP_COMPONENT).toUpperCase()).name();
    }

    /**
     * 判断当前pvc是否为tidb 的pvc
     *
     * @return
     */
    public static Boolean isTidbPvc(String crName, Map<String, String> actLabels) {
        Label[] labels = getTidbPvcLabel(crName);
        return Arrays.stream(labels).allMatch(l -> actLabels.containsKey(l.getName()) && actLabels.get(l.getName()).equals(l.getValue()));
    }

    public static Label[] getTidbPvcLabel(String crName) {
        return AppKind.TIDB.labels(crName);
    }

}
