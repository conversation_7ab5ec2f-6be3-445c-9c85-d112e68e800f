package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.config.CloudRequestContext;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.DatasourceConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.CloudTenant;
import cn.newdt.cloud.domain.LogTemplate;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.mapper.LogTemplateMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.CloudAppService;
import cn.newdt.cloud.service.KubeClientService;
import cn.newdt.cloud.utils.DateUtil;
import cn.newdt.cloud.utils.MybatisUtil;
import cn.newdt.cloud.vo.AppInstanceVO;
import cn.newdt.commons.exception.CustomException;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.fabric8.kubernetes.api.model.Container;
import io.fabric8.kubernetes.api.model.Event;
import io.fabric8.kubernetes.api.model.PersistentVolumeClaim;
import io.fabric8.kubernetes.api.model.Pod;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.AppKind.*;
import static cn.newdt.cloud.utils.MybatisUtil.ORM_RESULT_MAP;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/04/01 14:10
 */
@Service
@Slf4j
public class LogTemplateService {

    @Autowired
    private LogTemplateMapper logTemplateMapper;

    @Autowired
    private KubeClientService kubeClientService;

    @Autowired
    private TenantService tenantService;

    @Autowired
    protected CloudAppService appService;

    @Autowired
    private CloudAppServiceImpl cloudAppService;

    private static String LOGTEMPLATE_MAP = "logTemplateMap";

    /**
     * @param code 编码
     * @return
     */
    public LogTemplate getLogTemplateByCode(String code) {
        return logTemplateMapper.getLogTemplateByCode(DatasourceConstant.SCHEMA,DatasourceConstant.CLOUD_LOG_TEMPLATE,code);
    }
    /*public PageInfo<LogTemplate> getLogTemplateLike(Integer offset, Integer limit, String type, String searchVal, String sort) {
        // 获取搜索类型type的数据类型fieldType
        String fieldType = null;
        String searchColumn = null;
        if (!StringUtils.isEmpty(type)) {
            fieldType = MybatisUtil.getJavaPropMapJavaType(LOGTEMPLATE_MAP,type);
            searchColumn = MybatisUtil.getJavaPropMapDBColumn(LOGTEMPLATE_MAP,type);
        }

        // 设置分页的开始行数&每页大小
        if (!StringUtils.isEmpty(offset) && !StringUtils.isEmpty(limit)){
            PageHelper.offsetPage(offset, limit);
        }
        // 根据-/+设置排序方式-（DESC）/+（ASC）
        String sortStr = MybatisUtil.getColumnSortStatement(sort, LOGTEMPLATE_MAP);
        List<LogTemplate> esTemplateList = logTemplateMapper.getLogTemplatelike(DatasourceConstant.SCHEMA,DatasourceConstant.CLOUD_LOG_TEMPLATE,
                searchColumn,searchVal,fieldType,sortStr,"code");
        return new PageInfo<>(esTemplateList);
    }*/

    public PageInfo<LogTemplate> searchPage(Integer id, Integer offset, Integer limit, String searchCol,
                                            String searchVal, boolean like, String sort, String sortCol, String kind) {
        if (offset != null && limit != null) {
            PageHelper.offsetPage(offset, limit);
        }

        if (searchCol != null) {
            String resultMapId = "LogTemplate" + ORM_RESULT_MAP;
            if (MybatisUtil.existResultMap(resultMapId)) {
                searchCol = MybatisUtil.getJavaPropMapDBColumn(resultMapId, searchCol);
            }
        }

        List<LogTemplate> esTemplateList = logTemplateMapper.getLogTemplatelike(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_LOG_TEMPLATE,
                searchCol, searchVal, like, sort, sortCol, kind);
        return new PageInfo<LogTemplate>(esTemplateList);
    }

    public String containerLog(String containerName, String podName, String namespace, Integer kubeId, Integer rowNum) {
        // 1.获取kubeclient
        KubeClient kubeClient = kubeClientService.get(kubeId);


        // 2.查询log
        String log = "";
        try {
            log = kubeClient.getClient().pods().inNamespace(namespace).withName(podName)
                    .inContainer(containerName)
//                .limitBytes(204800)
                    .tailingLines(10000)
                    .getLog();
        } catch (Exception e) {
            throw new CustomException(500, "暂时无法获取日志！");
        }

        String[] logArr = log.split("\r\n|\r|\n");
        List<String> logList = Arrays.asList(logArr);
        if(null != rowNum && rowNum <= logList.size()){
            logList = logList.subList(logList.size() - rowNum, logList.size());
            log = String.join("\r\n", logList);
        }
        return log;
    }

    public PageInfo<List<EventVO>> event(String podName, Integer appId, Integer offset, Integer limit,
                                        String searchProp, String searchVal, String sort, String sortProp) {
        // 1.获取kubeclient和app
        CloudApp app = cloudAppService.get(appId);
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());


        // 2.根据类型查询当前类型应用的crd类型
        AppKind appKind = valueOf(app.getKind(), app.getArch());
        String crClassName = appKind.getCrClass().getName();
        String crdName = crClassName.substring(crClassName.lastIndexOf(".") + 1);


        // 3.遍历插入属性
        List<EventVO> events = kubeClient.getClient().v1().events().inNamespace(app.getNamespace()).list().getItems().stream()
                .filter(Event -> {
                    return ("Pod".equals(Event.getInvolvedObject().getKind()) && podName.equals(Event.getInvolvedObject().getName())) ||
                            ("PersistentVolumeClaim".equals(Event.getInvolvedObject().getKind()) && -1 != Event.getInvolvedObject().getName().indexOf(podName) && -1 != Event.getInvolvedObject().getName().indexOf("-pvc")) ||
                            (crdName.equalsIgnoreCase(Event.getInvolvedObject().getKind()) && app.getName().equals(Event.getInvolvedObject().getName()));
                })
                .map(event -> {
                    EventVO resEvent = new EventVO();
                    resEvent.setTypeVO(event.getType());
                    resEvent.setMessageVO(event.getMessage());
                    resEvent.setNamespaceVO(app.getNamespace());
                    resEvent.setInvolvedObjectVO(event.getInvolvedObject().getKind() + ":" + event.getInvolvedObject().getName());
                    resEvent.setSourceVO(event.getSource().getComponent());
                    resEvent.setCountVO(event.getCount());
                    resEvent.setPodNameVO(podName);
                    resEvent.setKubeIdVO(app.getKubeId());
                    resEvent.setKindVO(app.getKind());
                    resEvent.setAppIdVO(appId);
                    resEvent.setArchVO(app.getArch());
                    //时间转换，UTC转换为东八区时间
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
                    ZoneId zoneIdUTC8 = ZoneId.of("Asia/Shanghai");
                    try {
                        if (null != event.getFirstTimestamp()) {
                            LocalDateTime firstLocalDateTime = LocalDateTime.ofInstant(sdf.parse(event.getFirstTimestamp()).toInstant(), zoneIdUTC8);
                            resEvent.setAgeVO(DateUtil.formatDuration(Duration.between(firstLocalDateTime, LocalDateTime.now(ZoneId.of("Z")))));
                            resEvent.setAgeSort(Integer.valueOf(String.valueOf(Duration.between(firstLocalDateTime, LocalDateTime.now(ZoneId.of("Z"))).getSeconds())));
                        }
                    } catch (ParseException e) {
                        throw new CustomException(600, "查询age失败！");
                    }
                    try {
                        if (null != event.getLastTimestamp()) {
                            LocalDateTime lastLocalDateTime = LocalDateTime.ofInstant(sdf.parse(event.getLastTimestamp()).toInstant(), zoneIdUTC8);
                            resEvent.setLastSeenVO(DateUtil.formatDuration(Duration.between(lastLocalDateTime, LocalDateTime.now(ZoneId.of("Z")))));
                            resEvent.setLastSeenSort(Integer.valueOf(String.valueOf(Duration.between(lastLocalDateTime, LocalDateTime.now(ZoneId.of("Z"))).getSeconds())));
                        }
                    } catch (ParseException e) {
                        throw new CustomException(600, "查询lastSeen失败！");
                    }
                    return resEvent;
                }).collect(Collectors.toList());


        // 4.排序
        if (!StringUtils.isBlank(sortProp)) {
            if("lastSeenVO".equalsIgnoreCase(sortProp)){
                if("DESC".equalsIgnoreCase(sort)){
                    Collections.sort(events, new Comparator<EventVO>() {
                        public int compare(EventVO obj1, EventVO obj2) {
                            if(null != obj1.getLastSeenSort() && null != obj2.getLastSeenSort()){
                                return obj2.getLastSeenSort().compareTo(obj1.getLastSeenSort());
                            }else if(null == obj1.getLastSeenSort() && null != obj2.getLastSeenSort()){
                                return -1;
                            }else if(null != obj1.getLastSeenSort() && null == obj2.getLastSeenSort()){
                                return 1;
                            }else{
                                return 0;
                            }
                        }
                    });
                }else{
                    Collections.sort(events, new Comparator<EventVO>() {
                        public int compare(EventVO obj1, EventVO obj2) {
                            if(null != obj1.getLastSeenSort() && null != obj2.getLastSeenSort()){
                                return obj1.getLastSeenSort().compareTo(obj2.getLastSeenSort());
                            }else if(null == obj1.getLastSeenSort() && null != obj2.getLastSeenSort()){
                                return -1;
                            }else if(null != obj1.getLastSeenSort() && null == obj2.getLastSeenSort()){
                                return 1;
                            }else{
                                return 0;
                            }
                        }
                    });
                }
            }else if("ageVO".equalsIgnoreCase(sortProp)){
                if("DESC".equalsIgnoreCase(sort)){
                    Collections.sort(events, new Comparator<EventVO>() {
                        public int compare(EventVO event1, EventVO event2) {
                            if(null != event1.getAgeSort() && null != event2.getAgeSort()){
                                return event2.getAgeSort().compareTo(event1.getAgeSort());
                            }else if(null == event1.getAgeSort() && null != event2.getAgeSort()){
                                return -1;
                            }else if(null != event1.getAgeSort() && null == event2.getAgeSort()){
                                return 1;
                            }else{
                                return 0;
                            }
                        }
                    });
                }else{
                    Collections.sort(events, new Comparator<EventVO>() {
                        public int compare(EventVO event1, EventVO event2) {
                            if(null != event1.getAgeSort() && null != event2.getAgeSort()){
                                return event1.getAgeSort().compareTo(event2.getAgeSort());
                            }else if(null == event1.getAgeSort() && null != event2.getAgeSort()){
                                return -1;
                            }else if(null != event1.getAgeSort() && null == event2.getAgeSort()){
                                return 1;
                            }else{
                                return 0;
                            }
                        }
                    });
                }
            }else{
                events.sort((a, b) -> {
                    return sortByProp(a, b, sort, sortProp);
                });
            }
        }else{
            Collections.sort(events, new Comparator<EventVO>() {
                public int compare(EventVO obj1, EventVO obj2) {
                    if(null != obj1.getLastSeenSort() && null != obj2.getLastSeenSort()){
                        return obj1.getLastSeenSort().compareTo(obj2.getLastSeenSort());
                    }else if(null == obj1.getLastSeenSort() && null != obj2.getLastSeenSort()){
                        return -1;
                    }else if(null != obj1.getLastSeenSort() && null == obj2.getLastSeenSort()){
                        return 1;
                    }else{
                        return 0;
                    }
                }
            });
        }


        // 5.过滤
        events = filterEvent(events, searchProp, searchVal);


        // 6.进行分页
        //判断是否使用默认分页规则
        PageHelper.offsetPage(null == offset ? 0 : offset, null == limit ? events.size() : limit);
        //创建page对象
        Page page = new Page(0, null == limit ? events.size() : limit);
        //总条数
        int total = events.size();
        page.setTotal(total);
        //开始条数
        int startIndex = null == offset ? 0 : offset;
        //结束条数
        int endIndex = Math.min(startIndex + (null == limit ? events.size() : limit), total);
        //添加数据到page对象
        page.addAll(events.subList(startIndex, endIndex));
        //创建pageInfo对象
        PageInfo pageInfo = new PageInfo<>(page);
        return pageInfo;
    }

    public PageInfo<List<EventVO>> event(Integer kubeId, String kind, Integer offset, Integer limit,
                                         String searchProp, String searchVal, String sort, String sortProp) {
        // 1.获取kubeclient和namespace
        KubeClient kubeClient = kubeClientService.get(kubeId);
        Integer tenantId = CloudRequestContext.getContext().getTenantId();
        CloudTenant tenant = tenantService.findById(tenantId);
        String namespace = tenant.getNamespace();

        // 2.获取crdName，用来对event的类型进行比对
        List<EventVO> events = new ArrayList<>();
        final List<String> CRD_NAMES = new ArrayList<>();

        if(!StringUtils.isBlank(kind)){
            List<AppKind> appKinds = valueOfKind(kind);
            List<String> crdNames = appKinds.stream().map(appKind -> {
                String crClassName = appKind.getCrClass().getName();
                //对sentinel进行额外判断
                if("Redis".equals(kind) && "com.shindata.redis.v1.Sentinel".equals(crClassName)){
                    return null;
                }
                String crdName = crClassName.substring(crClassName.lastIndexOf(".") + 1);
                return crdName;
            }).collect(Collectors.toList());
            crdNames.removeAll(Collections.singleton(null));
            CRD_NAMES.addAll(crdNames);
            events = kubeClient.getClient().v1().events().inNamespace(namespace).list().getItems().stream()
                    .filter(Event -> {
                        if (CRD_NAMES.contains(Event.getInvolvedObject().getKind())) {
                            //判断crd符合
                            return true;
                        } else if ("Pod".equals(Event.getInvolvedObject().getKind())) {
                            //判断pod类型是否符合
                            String podName = Event.getInvolvedObject().getName();
                            Pod pod = kubeClient.getPod(namespace, podName);
                            if (null != pod) {
                                String crName = pod.getMetadata().getOwnerReferences().get(0).getKind();
                                if (CRD_NAMES.contains(crName)) {
                                    return true;
                                } else {
                                    //判断容器是否为elasticsearch或mongod或kibana
                                    List<Container> containers = pod.getSpec().getContainers();
                                    if ("Elasticsearch".equalsIgnoreCase(kind)) {
                                        return containers.stream().filter(container -> "elasticsearch".equals(container.getName())).findAny().isPresent();
                                    } else if ("MongoDB".equals(kind)) {
                                        return containers.stream().filter(container -> "mongod".equals(container.getName())).findAny().isPresent();
                                    } else if ("Kibana".equals(kind)) {
                                        return containers.stream().filter(container -> "kibana".equals(container.getName())).findAny().isPresent();
                                    } else {
                                        return false;
                                    }
                                }

                            } else {
                                return false;
                            }
                        } else if ("PersistentVolumeClaim".equals(Event.getInvolvedObject().getKind())) {
                            String pvcName = Event.getInvolvedObject().getName();
                            PersistentVolumeClaim pvc = kubeClient.getPvc(namespace, pvcName);
                            String pvcLabel = pvc.getMetadata().getLabels().get(CloudAppConstant.CustomLabels.APP);
                            if (!StringUtils.isBlank(pvcLabel) && kind.equalsIgnoreCase(pvcLabel)) {
                                return true;
                            } else if (!StringUtils.isBlank(pvcLabel)) {
                                String esLabel = pvc.getMetadata().getLabels().get("common.k8s.elastic.co/type");
                                return !StringUtils.isBlank(esLabel) && kind.equalsIgnoreCase(esLabel);
                            } else {
                                return false;
                            }
                        } else {
                            return false;
                        }
                    })
                    .map(event -> {
                        EventVO eventVO = setEvent(event, kubeId, namespace, CRD_NAMES, appKinds);
                        return eventVO;
                    }).collect(Collectors.toList());
        }else{
            AppKind[] appKindArr = values();
            List<AppKind> appKindList = Arrays.asList(appKindArr);
            List<String> crdNames = appKindList.stream().map(appKind -> {
                String crClassName = appKind.getCrClass().getName();
                String crdName = crClassName.substring(crClassName.lastIndexOf(".") + 1);
                return crdName;
            }).collect(Collectors.toList());
            CRD_NAMES.addAll(crdNames);
            //不需要筛选
            events = kubeClient.getClient().v1().events().inNamespace(namespace).list().getItems().stream()
                    .map(event -> {
                        EventVO eventVO = setEvent(event, kubeId, namespace, CRD_NAMES, appKindList);
                        return eventVO;
                    }).filter(event -> (null != event && null != event.getInvolvedObjectVO() && event.getInvolvedObjectVO().contains("Pod"))
                            || (null != event && null != event.getInvolvedObjectVO() && event.getInvolvedObjectVO().contains("PersistentVolumeClaim"))).limit(10).collect(Collectors.toList());
        }
        // 3.排序并去除null元素
        events.removeAll(Collections.singleton(null));
        if(!StringUtils.isBlank(sortProp)){
            events.sort((a, b) -> {
                return sortByProp(a, b, sort, sortProp);
            });
        }


        // 4.过滤
        events = filterEvent(events, searchProp, searchVal);


        // 5.进行分页
        //判断是否使用默认分页规则
        PageHelper.offsetPage(null == offset ? 0 : offset, null == limit ? events.size() : limit);
        //创建page对象
        Page page = new Page(0, null == limit ? events.size() : limit);
        //总条数
        int total = events.size();
        page.setTotal(total);
        //开始条数
        int startIndex = null == offset ? 0 : offset;
        //结束条数
        int endIndex = Math.min(startIndex + (null == limit ? events.size() : limit), total);
        //添加数据到page对象
        page.addAll(events.subList(startIndex, endIndex));
        //创建pageInfo对象
        PageInfo pageInfo = new PageInfo<>(page);
        return pageInfo;
    }

    private int sortByProp(Object a, Object b, String sort, String sortProp){
        int result = 0;
        try {
            Object aVal = PropertyUtils.getProperty(a, sortProp);
            Object bVal = PropertyUtils.getProperty(b, sortProp);
            if (aVal == null) result = -1;
            else if (bVal == null) result = 1;
            else {
                if (aVal.getClass().isPrimitive()) {
                    result = (int) ((double) aVal - (double) bVal);
                } else {
                    result = ((Comparable) aVal).compareTo(bVal); // primary < secondary
                }
            }

            if ("desc".equalsIgnoreCase(sort)) {
                result = -result;
            }
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
            log.error("", e);
        }
        return result;
    }

    public String operatorLog(AppInstanceVO appInstanceVO, Integer rowNum) {
        // 1.获取kubeclient
        KubeClient kubeClient = kubeClientService.get(appInstanceVO.getKubeId());


        // 2.查询log
        //判断当前应用类型，如果是mongodb复制集，则在mongodb-kubernetes-operator容器查询日志
        String log = "";
        String containerName = "manager";
        String kind = appInstanceVO.getKind();
        String arch = appInstanceVO.getArch();
        if (Flink.getKind().equalsIgnoreCase(kind)) {
            containerName = "flink-kubernetes-operator";
        } else if (Clickhouse.getKind().equalsIgnoreCase(kind)) {
            containerName = "clickhouse-operator";
        } else if (MYSQL_MGR.getKind().equalsIgnoreCase(kind) && MYSQL_MGR.getArch().equalsIgnoreCase(arch)) {
            containerName = "mysql-operator";
        } else if (MongoDB.getKind().equalsIgnoreCase(kind) && MongoDB.getArch().equalsIgnoreCase(arch)){
            containerName = "mongodb-kubernetes-operator";
        } else if (TIDB.getKind().equalsIgnoreCase(kind) && TIDB.getArch().equalsIgnoreCase(arch)) {
            containerName = "tidb-operator";
        }
        try {
            log = kubeClient.getClient().pods().inNamespace(appInstanceVO.getNamespace()).withName(appInstanceVO.getPodName())
                    .inContainer(containerName)
//                    .limitBytes(204800)
                    .tailingLines(10000)
                    .getLog();
        } catch (Exception e) {
            log = "查询错误！请查看Operator状态是否正常！";
        }

        // 3.判断log字符串大小，进行截断
        String[] logArr = log.split("\n");
        List<String> logList = Arrays.asList(logArr);
        if(rowNum <= logList.size()){
            logList = logList.subList(logList.size() - rowNum, logList.size());
            log = String.join("\r\n", logList);
        }
        return log;
    }

    /**
     * 为event插入属性
     *
     * @param event
     * @param kubeId
     * @param namespace
     * @return
     */
    private EventVO setEvent(Event event, Integer kubeId, String namespace, List<String> CRD_NAMES, List<AppKind> appKinds) {
        // 1.获取kubeclient
        KubeClient kubeClient = kubeClientService.get(kubeId);
        EventVO resEvent = new EventVO();
        resEvent.setTypeVO(event.getType());
        resEvent.setMessageVO(event.getMessage());
        resEvent.setNamespaceVO(namespace);
        resEvent.setInvolvedObjectVO(event.getInvolvedObject().getKind() + ":" + event.getInvolvedObject().getName());
        resEvent.setSourceVO(event.getSource().getComponent());
        resEvent.setCountVO(event.getCount());
        resEvent.setKubeIdVO(kubeId);

        //根据event类型获取podName与appId
        Integer appId = null;
        String podName = null;
        if (CRD_NAMES.contains(event.getInvolvedObject().getKind())) {
            //放入类型
            resEvent.setEventKindVO("cr");
            //获取任意一个podName即可
            //使用crClass对crd进行判断，获取到appkind
            List<AppKind> findAppkinds = appKinds.stream().filter(appKind -> appKind.getCrClass().getName().substring(appKind.getCrClass().getName().lastIndexOf(".") + 1)
                    .equals(event.getInvolvedObject().getKind())).collect(Collectors.toList());
            AppKind appKind = findAppkinds.get(0);
            //创建app用来构建label
            CloudApp app = new CloudApp();
            app.setName(event.getInvolvedObject().getName());
            app.setCrName(event.getInvolvedObject().getName());
            app.setNamespace(namespace);
            app.setKind(appKind.getKind());
            app.setArch(appKind.getArch());
            app.setKubeId(kubeId);
            List<PodDTO> podDTOS = kubeClient.listPod(namespace, appKind.labelOfPod(app));
            if(CollectionUtils.isEmpty(podDTOS)){
                return null;
            }
            podName = podDTOS.get(0).getPodName();
            List<CloudApp> apps = appService.getAppByKubeIdAndNamespaceAndKind(kubeId, namespace, appKind.getKind());
            if(CollectionUtils.isEmpty(apps)){
                return null;
            }
            List<CloudApp> resAppList = apps.stream().filter(everyApp -> everyApp.getCrName().equals(event.getInvolvedObject().getName())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(resAppList)){
                return null;
            }
            appId = resAppList.get(resAppList.size() - 1).getId();
        } else if ("Pod".equals(event.getInvolvedObject().getKind())) {
            //放入类型
            resEvent.setEventKindVO("pod");
            //直接获取podName
            podName = event.getInvolvedObject().getName();
            //根据label获取应用名称，进而获取应用id
            Pod pod = kubeClient.getPod(namespace, podName);
            if(null == pod){
                return null;
            }
            Map<String, String> labels = pod.getMetadata().getLabels();
            String crName = labels.get(CloudAppConstant.CustomLabels.APP_NAME);
            if(StringUtils.isBlank(crName)){
                if("elasticsearch".equals(labels.get("common.k8s.elastic.co/type"))){
                    crName = labels.get("elasticsearch.k8s.elastic.co/cluster-name");
                } else if("kibana".equals(labels.get("common.k8s.elastic.co/type"))){
                    crName = labels.get("kibana.k8s.elastic.co/name");
                }
                if(StringUtils.isBlank(crName)){
                    return null;
                }
            }
            AppKind appKind = getAppKindByLabels(labels, crName);
            if(null == appKind) {
                return null;
            }
            List<CloudApp> apps = appService.getAppByKubeIdAndNamespaceAndKind(kubeId, namespace, appKind.getKind());
            if(!CollectionUtils.isEmpty(apps)){
                final String CR_NAME = crName;
                List<CloudApp> resAppList = apps.stream().filter(everyApp -> everyApp.getCrName().equals(CR_NAME)).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(resAppList)){
                    appId = resAppList.get(resAppList.size() - 1).getId();
                }else{
                    return null;
                }
            }else{
                return null;
            }
        } else if ("PersistentVolumeClaim".equals(event.getInvolvedObject().getKind())) {
            //放入类型
            resEvent.setEventKindVO("pvc");
            String pvcName = event.getInvolvedObject().getName();
            PersistentVolumeClaim pvc = kubeClient.getPvc(namespace, pvcName);
            if(null == pvc){
                return null;
            }
            Map<String, String> labels = pvc.getMetadata().getLabels();
            String kind = labels.get(CloudAppConstant.CustomLabels.APP);
            String crName = labels.get(CloudAppConstant.CustomLabels.APP_NAME);
            //如果此时crName为null，则为es或kibana
            if(StringUtils.isBlank(crName)){
                if("elasticsearch".equals(labels.get("common.k8s.elastic.co/type"))){
                    crName = labels.get("elasticsearch.k8s.elastic.co/cluster-name");
                } else if("kibana".equals(labels.get("common.k8s.elastic.co/type"))){
                    crName = labels.get("kibana.k8s.elastic.co/name");
                }
                if(StringUtils.isBlank(crName)){
                    return null;
                }
            }
            String component = labels.get(CloudAppConstant.CustomLabels.APP_COMPONENT);
            String resourceVersion = labels.get(CloudAppConstant.CustomLabels.RESOURCE_VERSION);
            String labelVO = kind + "-" + crName + "-" + component + "/" + resourceVersion;
            resEvent.setLabelVO(labelVO);
            podName = labels.get("app.kubernetes.io/instance");
            if(StringUtils.isBlank(podName)){
                return null;
            }
            AppKind appKind = getAppKindByLabels(labels, crName);
            //如果为null，为export
            if(null == appKind){
                return null;
            }
            List<CloudApp> apps = appService.getAppByKubeIdAndNamespaceAndKind(kubeId, namespace, appKind.getKind());
            if(!CollectionUtils.isEmpty(apps)){
                final String CR_NAME = crName;
                List<CloudApp> resAppList = apps.stream().filter(everyApp -> everyApp.getCrName().equals(CR_NAME)).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(resAppList)){
                    appId = resAppList.get(resAppList.size() - 1).getId();
                }else{
                    return null;
                }
            }else{
                return null;
            }
        }
        resEvent.setPodNameVO(podName);
        resEvent.setAppIdVO(appId);
        //时间转换，UTC转换为东八区时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        ZoneId zoneIdUTC8 = ZoneId.of("Asia/Shanghai");
        try {
             if(null != event.getFirstTimestamp()){
                LocalDateTime firstLocalDateTime = LocalDateTime.ofInstant(sdf.parse(event.getFirstTimestamp()).toInstant(), zoneIdUTC8);
                resEvent.setAgeVO(DateUtil.formatDuration(Duration.between(firstLocalDateTime, LocalDateTime.now(ZoneId.of("Z")))));
                resEvent.setAgeSort(Integer.valueOf(String.valueOf(Duration.between(firstLocalDateTime, LocalDateTime.now(ZoneId.of("Z"))).getSeconds())));
            }
        } catch (ParseException e) {
            throw new CustomException(600, "查询age失败！");
        }
        try {
            if(null != event.getLastTimestamp()){
                LocalDateTime lastLocalDateTime = LocalDateTime.ofInstant(sdf.parse(event.getLastTimestamp()).toInstant(), zoneIdUTC8);
                resEvent.setLastSeenVO(DateUtil.formatDuration(Duration.between(lastLocalDateTime, LocalDateTime.now(ZoneId.of("Z")))));
                resEvent.setLastSeenSort(Integer.valueOf(String.valueOf(Duration.between(lastLocalDateTime, LocalDateTime.now(ZoneId.of("Z"))).getSeconds())));
            }
        } catch (ParseException e) {
            throw new CustomException(600, "查询lastSeen失败！");
        }
        return resEvent;
    }

    /**
     * 过滤event
     */
    private List<EventVO> filterEvent(List<EventVO> events, String searchProp, String searchVal){
        //创建记录pvc版本的map,key为 kind-crname-component，value为 resourceVersion
        HashMap<String, String> resourceVersionMap = new HashMap<>();
        //创建记录需要删除的event信息set
        Set<String> removeLabelSet = new HashSet<>();

        //根据标签，过滤掉低版本的pvc event
        events = events.stream().map(event -> {
            //过滤pvc类型的event低版本
            String labelVO = event.getLabelVO();
            if(!StringUtils.isBlank(labelVO) && "pvc".equals(event.getEventKindVO())){
                //对label进行切割，获取pvc类型和版本
                String[] pvcInfoAndVersion = labelVO.split("/");
                //查询是否在版本map中已经存在当前pvc
                if(!resourceVersionMap.containsKey(pvcInfoAndVersion[0])){
                    //不存在，则记录当前pvc的信息和版本
                    resourceVersionMap.put(pvcInfoAndVersion[0], pvcInfoAndVersion[1]);
                }else{
                    //存在，则获取版本进行对比，将版本较高的进行记录，并将版本较低的event从event结果集中移除
                    Integer oldVersion = Integer.valueOf(resourceVersionMap.get(pvcInfoAndVersion[0]));
                    Integer newVersion = Integer.valueOf(pvcInfoAndVersion[1]);
                    if(oldVersion >= newVersion){
                        //当前event不返回
                        return null;
                    }else{
                        //记录在set中
                        removeLabelSet.add(pvcInfoAndVersion[0] + "/" + resourceVersionMap.get(pvcInfoAndVersion[0]));
                        //map中替换为当前的较高版本信息
                        resourceVersionMap.put(pvcInfoAndVersion[0], pvcInfoAndVersion[1]);
                    }
                }
            }
            if(!StringUtils.isBlank(searchVal) && !StringUtils.isBlank(searchProp)){
                //反射获取筛选列的值
                Class clazz = event.getClass();
                try {
                    Field searchColField = clazz.getDeclaredField(searchProp);
                    searchColField.setAccessible(true);
                    String value = String.valueOf(searchColField.get(event));
                    if (value.toLowerCase().contains(searchVal.toLowerCase())) {
                        return event;
                    }else{
                        return null;
                    }
                } catch (NoSuchFieldException e) {
                    throw new CustomException(600, "筛选失败！");
                } catch (IllegalAccessException e) {
                    throw new CustomException(600, "获取筛选值失败！");
                }
            }
            return event;
        }).collect(Collectors.toList());
        //过滤list中的null
        events.removeAll(Collections.singleton(null));

        //过滤set中较低版本的pvc event
        events = events.stream().filter(eventVO -> !removeLabelSet.contains(eventVO.getLabelVO())).collect(Collectors.toList());
        return events;
    }

    @Data
    public static class EventVO extends Event {
        private String typeVO;
        private String messageVO;
        private String namespaceVO;
        private String involvedObjectVO;
        private String sourceVO;
        private Integer countVO;
        private String ageVO;
        private String lastSeenVO;
        private String podNameVO;
        private Integer kubeIdVO;
        private String kindVO;
        private Integer appIdVO;
        private String archVO;
        private String labelVO;         //记录event的label，用来进行过滤
        private String eventKindVO;     //记录event的类型，目前有三种：cr、pod、pvc

        private Integer lastSeenSort;   //用来排序
        private Integer ageSort;        //用来排序
    }
}
