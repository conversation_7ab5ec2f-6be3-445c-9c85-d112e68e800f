package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.commons.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class OpenGaussInstallWatch extends OpenGaussWatch {

    @Override
    protected void doStopWatch(CloudApp app, boolean success, TriggerHis triggerHis, OpsResultDTO build) {
        if (success) {
            appMultiAZService.enableAlert(app);
            //更新svc记录
            serviceManageOperationWatcherHelper.doStopWatchSvc(app, triggerHis, success, null);

            Map<String, String> mergedJobDataMap = triggerHis.returnMergedJobDataMap();
            if (StringUtils.isEmpty(mergedJobDataMap.get("backupHisId"))) {
                String username = mergedJobDataMap.get("username");
                String password = mergedJobDataMap.get("password"); // todo 加解密
                if (StringUtils.isNoneBlank(username, password)) {
                    if (!dbUserService.findDbUserByName(app.getId(), username).isPresent()) {
                        build.setMsg("create user " + username);
                        dbUserService.createUser(app.getId(), username, "", CloudAppConstant.UserRole.ADMIN);
                        createRootUser(username, password, app);
                    }
                }

                //创建dmp监控用户
                Map<String, String> dmpMonitorUser = operationUtil.createDMPMonitorUser();
                createRootUser(dmpMonitorUser.get("username"), dmpMonitorUser.get("password"), app);
            }

            // 纳管到dmp
            if (autoManagement) {
                logInfo(app, ActionEnum.CREATE, "app will be managed into CMDB");
                build.setMsg("sync cloud app to DMP");
                build.setMsg(operationUtil.syncToDMP(app, triggerHis));
            }
        }
    }

    private void createRootUser(String username, String password, CloudApp app) {
        KubeClient client = kubeClientService.get(app.getKubeId());
        //获取主节点
        List<PodDTO> pods = client.listPod(app.getNamespace(), AppKind.OpenGauss.labelOfPod(app));
        List<PodDTO> primaryPods = pods.stream().filter(pod -> "primary".equalsIgnoreCase(pod.getPod().getMetadata().getLabels().get("opengauss.role"))).collect(Collectors.toList());
        PodDTO primaryPod = primaryPods.get(0);
        try {
            String createUserSql = "gsql -d postgres -r -c \"create user " + username + " with sysadmin MONADMIN password '" + password + "';GRANT SELECT ON STATEMENT_HISTORY TO " + username + "; GRANT CREATE ON TABLESPACE PG_GLOBAL TO " + username + ";\"";
            //需要修改一下密码否则连接会有提示
            String alertRole = client.execCmd(app.getNamespace(), primaryPod.getPodName(), "og", 30, "sh", "-c",
                    createUserSql);
        } catch (Exception e) {
            log.error("opengauss创建用户失败！");
            throw new CustomException(600, "opengauss创建用户失败！");
        }
    }
}
