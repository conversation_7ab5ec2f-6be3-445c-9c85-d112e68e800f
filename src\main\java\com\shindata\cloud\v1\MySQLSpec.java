package com.shindata.cloud.v1;

@com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)
@com.fasterxml.jackson.annotation.JsonPropertyOrder({"archType","disasterRecoveryApps","disasterRecoveryRole","entries","maintenance","options","primaryAddress","restore","secrets"})
@com.fasterxml.jackson.databind.annotation.JsonDeserialize(using = com.fasterxml.jackson.databind.JsonDeserializer.None.class)
public class MySQLSpec implements io.fabric8.kubernetes.api.model.KubernetesResource {

    public enum ArchType {

        @com.fasterxml.jackson.annotation.JsonProperty("ha")
        HA("ha"), @com.fasterxml.jackson.annotation.JsonProperty("mgr")
        MGR("mgr");

        java.lang.String value;

        ArchType(java.lang.String value) {
            this.value = value;
        }

        @com.fasterxml.jackson.annotation.JsonValue()
        public java.lang.String getValue() {
            return value;
        }
    }

    @com.fasterxml.jackson.annotation.JsonProperty("archType")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private ArchType archType = io.fabric8.kubernetes.client.utils.Serialization.unmarshal("\"ha\"", ArchType.class);

    public ArchType getArchType() {
        return archType;
    }

    public void setArchType(ArchType archType) {
        this.archType = archType;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("disasterRecoveryApps")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.List<String> disasterRecoveryApps;

    public java.util.List<String> getDisasterRecoveryApps() {
        return disasterRecoveryApps;
    }

    public void setDisasterRecoveryApps(java.util.List<String> disasterRecoveryApps) {
        this.disasterRecoveryApps = disasterRecoveryApps;
    }

    public enum DisasterRecoveryRole {

        @com.fasterxml.jackson.annotation.JsonProperty("primary")
        PRIMARY("primary"), @com.fasterxml.jackson.annotation.JsonProperty("standby")
        STANDBY("standby"), @com.fasterxml.jackson.annotation.JsonProperty("standalone")
        STANDALONE("standalone");

        java.lang.String value;

        DisasterRecoveryRole(java.lang.String value) {
            this.value = value;
        }

        @com.fasterxml.jackson.annotation.JsonValue()
        public java.lang.String getValue() {
            return value;
        }
    }

    @com.fasterxml.jackson.annotation.JsonProperty("disasterRecoveryRole")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private DisasterRecoveryRole disasterRecoveryRole = io.fabric8.kubernetes.client.utils.Serialization.unmarshal("\"standalone\"", DisasterRecoveryRole.class);

    public DisasterRecoveryRole getDisasterRecoveryRole() {
        return disasterRecoveryRole;
    }

    public void setDisasterRecoveryRole(DisasterRecoveryRole disasterRecoveryRole) {
        this.disasterRecoveryRole = disasterRecoveryRole;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("entries")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.List<com.shindata.common.spec.Entries> entries;

    public java.util.List<com.shindata.common.spec.Entries> getEntries() {
        return entries;
    }

    public void setEntries(java.util.List<com.shindata.common.spec.Entries> entries) {
        this.entries = entries;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("maintenance")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Boolean maintenance;

    public Boolean getMaintenance() {
        return maintenance;
    }

    public void setMaintenance(Boolean maintenance) {
        this.maintenance = maintenance;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("options")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.shindata.common.spec.Options options;

    public com.shindata.common.spec.Options getOptions() {
        return options;
    }

    public void setOptions(com.shindata.common.spec.Options options) {
        this.options = options;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("primaryAddress")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String primaryAddress;

    public String getPrimaryAddress() {
        return primaryAddress;
    }

    public void setPrimaryAddress(String primaryAddress) {
        this.primaryAddress = primaryAddress;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("restore")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.shindata.cloud.v1.mysqlspec.Restore restore;

    public com.shindata.cloud.v1.mysqlspec.Restore getRestore() {
        return restore;
    }

    public void setRestore(com.shindata.cloud.v1.mysqlspec.Restore restore) {
        this.restore = restore;
    }

    /**
     * 应使用k8s标准的SecretTypeBasicAuth Secret，多个用户使用多个Secret
     * mongodb keyfile只能使用默认类型secret
     */
    @com.fasterxml.jackson.annotation.JsonProperty("secrets")
    @com.fasterxml.jackson.annotation.JsonPropertyDescription("应使用k8s标准的SecretTypeBasicAuth Secret，多个用户使用多个Secret\nmongodb keyfile只能使用默认类型secret")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.shindata.common.spec.Secrets secrets;

    public com.shindata.common.spec.Secrets getSecrets() {
        return secrets;
    }

    public void setSecrets(com.shindata.common.spec.Secrets secrets) {
        this.secrets = secrets;
    }
}

