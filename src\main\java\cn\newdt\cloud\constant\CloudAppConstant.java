
package cn.newdt.cloud.constant;

public interface CloudAppConstant {
    String WRITE_PORT = "writePort";
    String READ_PORT = "readPort";
    String MYSQL_BACKUP_VOLUME_NAME = "backup-pvc";
    String MYSQL_LOCAL_PATH = "/Mysql/";
    String MongoDBBackupPath = "/k8sBack/mongo/";
    String SECRET_PASSWORD_KEY = "password";
    String EXTERNAL_IP = "externalIp";

    String JOB_EXT_DATA_KEY = "extDataStr";

    String BACKUP_PATH_KEY = "ftpBackupPath";
    String BINLOG_DIR = "/data/binlog/";
    String BINLOG_INDEX = "mysql-bin.index";
    interface AppDeleteStatus {
        int NORMAL = 0;
        int DELETED = 1;
        int RECYCLED = 2;
    }

    interface DmpInstClass {
        String DB = "DB";
        String MW = "MW";
    }

    interface SysCfgCategory {
        String MULTI_PART = "multi-part";
        String APP_REQUEST = "app.requests";
        String LIMIT_RANGE = "limitrange";
        String OPERATOR_NAME = "operator.name";
        String IMAGE_MANAGE = "imageManage";
        String CLUSTER_MANAGEMENT = "clusterManagement";
        String BACKUP_MANAGEMENT = "backup.config";
        String DMP_MANAGEMENT = "dmp.config";
        String POD_TEMPLATE = "app.pod.template";
        String PARAM_FORMULA = "paramformula.config";
        String PARAM_PROHIBIT = "paramProhibit.config";
        String CONFIGMAP_TEMPLATE = "app.configmap.template";
        String OPERATOR_SERVICE = "operator.service";
        String PARAM_WHITELIST = "dbParam.whiteList";
        String PARAM_BLACKLIST = "dbParam.blackList";
        String OPERATOR_CONFIG = "operator.config";
        String ALERT_CFG = "alertConfig";
        String RBAC_CFG = "rbac.rolePermission";
        String LOG_CONFIG = "logConfig";
        String RECYCLECONFIG = "recycleConfig";
        /** 监控告警数据清理配置 */
        String ALERT_RETENTION_CFG= "alertConfig";
        String OVER_ALLOCATION_RATIO = "k8s.resourceAllocationRatio";
        String K8S_SYS_CONFIG_CM = "k8s.sysConfig.metadata";
        String K8S_SYS_CONFIG_CM_DATA = "k8s.sysConfig.data";
        String INSTALL_CONFIG = "install.config";
    }

    interface SysCfgName {
        String LOG_DELETE_DAYS = "log_delete_days";
        String PROMETHEUS_RETENTION = "prometheus_retention";
        String PROMETHEUS_RETENTIONSIZE = "prometheus_retentionSize";
        String APP_RETENTION_DAYS = "app_retention_days";
        String AGENT_RBAC = "label.rbac";
        String VASTBASE_LICENSE = "VASTBASE_LICENSE";
        String LABEL_UPDATE_SERVICEACCOUNT = "LABEL_UPDATE_SERVICEACCOUNT";
        String INSTALL_PATH = "install.path";
    }

    interface SysCfgType {
        String STRING = "string";
    }

    /**
     * 运维命令
     */
    interface Cmd {
        String CHECK_MAINTENANCE = "ls /data/files/scripts/maintenance";
        String ENABLE_MAINTENANCE = "touch /data/maintenance | echo 2 > /data/maintenance";
        String DISABLE_MAINTENANCE = "rm -rf /data/maintenance";
        String OPENGAUSS_EXIST_MAINTENANCE_FILE = "[ -f /gauss/files/maintenance ] && cat /gauss/files/maintenance || echo maintenance文件不存在";
        String MYSQL_EXIST_MAINTENANCE_FILE = "[ -f /data/maintenance ] && cat /data/maintenance || echo maintenance文件不存在";
        String OPENGAUSS_ENABLE_MAINTENANCE = "touch /gauss/files/maintenance | echo 2 > /gauss/files/maintenance";
        String OPENGAUSS_DISABLE_MAINTENANCE = "rm -rf /gauss/files/maintenance";
        String QUERY_STORAGE_METRIC = "/bin/sh /data/files/scripts/K8SMySQLStartStop.sh -mode "; // remain suffix space
        String START_STOP = "/bin/sh /data/files/scripts/K8SMySQLStartStop.sh -mode "; // remain suffix space
        String START_DB = "/bin/sh /data/files/scripts/K8SMySQLStartStop.sh -mode start";
        String STOP_DB = "/bin/sh /data/files/scripts/K8SMySQLStartStop.sh -mode stop";
        /**
         * 存活探针
         */
        String CHECK_MYSQL_LIVENESS = "[ -f /data/files/scripts/mysqlLivenessProbe ] && echo 1 || echo 0";

        String CHECK_MYSQL_MGR_LIVENESS = "sh [ /livenessprobe.sh ] && echo 1 || echo 0";

        /**
         * 获取webTerminal的返回的url地址(含随机串，博云网络插件使用)
         */
        String GET_TERMINAL_URL_WITH_RANDOMSTR = "ttyd -p ##port## -b /##randomStr## --once bash ";

        /**
         * 使用随机字符串获取url，不设置单次使用后就关闭进程的参数
         */
        String GET_TERMINAL_URL_WITH_RANDOMSTR_AGAIN = "ttyd -p ##port## -b /##randomStr##  bash ";
        /**
         * 获取webTerminal的返回的url地址
         */
        String GET_TERMINAL_URL = "nohup ttyd -p ##port##  --once bash &";
        /**
         * 转发一个本地端口到 Pod 端口
         */
        String LOCAL_PORT_FORWARD_POD = "nohup  kubectl port-forward --address=0.0.0.0 -n ##namespace## pods/##podName## ##localport##:##port## >/dev/null 2>webterminal.log &" ;
        /**
         * 删除kubectl port-forward 进程
         */
        String KILL_PORT_FORWARD_PROCESS = "kill -9 `ps -ef|grep 'kubectl port-forward --address=0.0.0.0 -n ##namespace## pods/##podName## ##localport##:##port##' |grep -v grep  |awk '{print $2}'`";
        String KILL_TTYD_PROCESS = "kill -9 `ps -ef|grep 'ttyd -p ##port## -b ##randomStr##'|grep -v grep|awk '{print $2}'`";
        /**
         * 查看sentinel info
         */
        String SENTINEL_INFO = "redis-cli -p " + AppKind.Sentinel.getDbPort() + " info sentinel";
        String CALICOCTL_VERSION = "calicoctl version";

        /**
         * redis清除实例数据
         */
        String GET_REDIS_PASSWORD = "cat /data/redis.conf | grep requirepass | awk '{print $2}'";

        String CONNECT_REDIS = "redis-cli -h ";

        String FLUSH_ALL = " flushall";

        String SAVE_SLAVE = " SAVE";

        /**
         * 集群存在时手动配置主从命令: shindb-toolkit -config /data/files/scripts/shindb-toolkit.ini -sourceSwitch -newSourceIP ************** -allServerIP **************,**************,**************
         */
        String MYSQL_SWITCH_MASTER = "shindb-toolkit -config /data/files/scripts/shindb-toolkit.ini -sourceSwitch -newSourceIP %s -allServerIP %s ";

    }

    interface CalicoCheckMethod {
        String PING = "ping";
        String AGENT = "agent";
        String API = "api";
    }
    /**
     * pod label-主从角色标签名
     */
    String MS_LABEL = "role";
    /**
     * pod-label-集群名称标签名
     */
    String LABEL_APP_NAME = "cluster";
//    String LABEL_OPENGAUSS_NAME = "opengauss.cluster";
    String LABEL_OPENGAUSS_NAME = CustomLabels.APP_NAME;
    String LABEL_MONGO_NAME = "app";

    String ROLE_PRIMARY = "primary";
    String ROLE_STANDBY = "standby";
    String ROLE_SECONDARY = "secondary";
    String ROLE_MASTER = "master";
    String ROLE_SLAVE = "slave";
    String ROLE_SOURCE= "source";
    String ROLE_REPLICA= "replica";
    String MYSQL_CONTAINER_NAME = "mysql";
    String SIDECAR_CONTAINER_NAME = "sidecar";
    String XTRABACKUP_CONTAINER_NAME = "xtrabackup";
    String REDIS_CLUSTER_CONTAINER_NAME = "redis";
    String SENTINEL_CLUSTER_CONTAINER_NAME = "sentinel";
    String MONGODB_CONTAINER_NAME = "mongod";
    String LABEL_REDIS_NAME = "app.kubernetes.io/name";
    String LABEL_KIBANA_NAME = "kibana.k8s.elastic.co/name";
    String ES_APP_NAME = "elasticsearch.k8s.elastic.co/cluster-name";
    String ES_STS_NAME = "elasticsearch.k8s.elastic.co/statefulset-name";
    String ES_REF = "app.kubernetes.io/es-ref";
    String CLICKHOUSE_APP = "clickhouse.altinity.com/app";
    String CLICKHOUSE_APP_NAME = "clickhouse.altinity.com/chi";
    String TIDB_REF = "app.kubernetes.io/managed-by";
    String MONGODB_REPLICASET_UNIQUE_LABEL = "app";
    //必须为og
    String OPENGAUSS_CONTAINER_NAME = "og";
    String OPENGAUSS_DATA_MOUNT_PATH = "/gaussdata/openGauss";
    /**
     * mongodb-replicaset 数据在容器中的 mount目录
     */
    String MONGODB_RS_DATA_MOUNT_PATH = "/data";
    String ELASTICSEARCH_DATA_MOUNT_PATH = "/data";
    String CLICKHOUSE_DATA_BACKUP_PATH = "/var/lib/clickhouse/backup";
    //zookeeper
    String LABEL_ZOOKEEPER_NAME = "app.kubernetes.io/name";
    //kafka
    String LABEL_KAFKA_NAME = "app.kubernetes.io/name";
    //nameserver
    String LABEL_NAMESERVER_NAME = "name_service_cr=";

    //rediscluster临时目录
    String TMP_CLOUD_REDISCLUSTER_BACKUP = "/tmp/cloud/rediscluster/backup";
    //redis临时目录
    String TMP_CLOUD_REDIS_BACKUP = "/tmp/cloud/redis/backup";
    //mysql临时目录
    String TMP_CLOUD_MYSQLHA_BACKUP = "/tmp/cloud/mysqlha/backup";
    //es临时目录
    String TMP_CLOUD_ES_BACKUP = "/tmp/cloud/es/backup";

    //存活
    Integer LIVENESS_STOP= 0;
    Integer LIVENESS_SRART = 1 ;
    Integer LIVENESS_UNKNOW = 2;

    String LABEL_NODE_NAME = "kubernetes.io/hostname";

    String OPTS_ADDITIONAL_KEY_LASTUPDATETIME = "lastUpdateTimestamp";

    interface CustomLabels {
        String ZK_REF = "app.kubernetes.io/zk-ref";

        String APP = "app.kubernetes.io/app";
        String APP_COMPONENT = "app.kubernetes.io/component";
        String ARCHITECTURE = "app.kubernetes.io/arch";
        String APP_NAME = "app.kubernetes.io/name";
        String INSTANCE = "app.kubernetes.io/instance";
        /**
         * 类似metadata.resourceversion. 用于在cr相关资源(e.g. pvc)与cr的引用关系之间添加版本控制.
         */
        String RESOURCE_VERSION = "app.kubernetes.io/version";
        String POD_ROLE = "app.kubernetes.io/role";
        String ROLE = "app.kubernetes.io/role";
    }
    /**
     * 集群实例状态
     *
     * @see <a href="https://kubernetes.io/zh/docs/concepts/workloads/pods/pod-lifecycle/#pod-phase">doc: pod lifecycle</a>
     */
    interface PodStatus {
        /**
         * 所有container就绪可提供服务
         */
        String RUNNING = "Running";
        /**
         * 部分或全部container未就绪
         */
        String NOT_READY = "Not ready";
        /**
         * 手动停机时容器状态
         */
        String STOPPED = "Stopped";
        String Terminating = "Terminating";
    }

    interface PodPhase{
        /**
         * 所有container就绪可提供服务
         */
        String RUNNING = "Running";
        /**
         * 部分或全部container未就绪
         */
        String NOT_READY = "Not ready";

        String STOPPED = "Stopped";
        String SUCCEEDED = "Succeeded";
        String FAILED = "Failed";
        String Pending = "Pending";
    }

    /**
     * pv相关状态
     */
    interface PVPhase {
        String released = "Released";
        String available = "Available";
        String bound = "Bound";
        String failed = "Failed";
    }

    /**
     * pvc相关状态
     */
    interface PVCPhase {
        String pending = "pending";
    }


    /**
     * 集群状态
     */
    interface AppStatus {
        String SUCCESS = "Succeeded";
        String STOPPED = "Stopped";
        /**
         * 运维操作未结
         */
        String PENDING = "Running";
        String FAILED = "Failed";
    }

    interface UserRole {
        String ADMIN = "admin";
        String QUERY = "query";
    }

    interface NodePortRange{
        Integer MIN_PORT = 30000;
        Integer MAX_PORT = 32767;
    }
    interface NodePortStatus{
        String FREE = "0"; //空闲
        String USED = "1"; //已占用
    }
    interface NodePortType{
        String WRITE_PORT = "0"; //写端口
        String READ_PORT = "1"; //读端口
        String WEB_TERMINAL = "2"; //远程端口
        String SERVICE_PORT = "3"; //服务端口，代表没有读写分离的 Service
        //todo 未占用的状态和NodePortStatus冲突了，去掉或者使用NodePortStatus
        String UNUSE = "-"; //未占用
    }

    interface LoadBalancerIPStatus{
        int FREE = 0; //空闲
        int USED = 2; //已占用
    }

    interface ServicePurpose{
        /**
         * 写/服务，读写分离情况下代表写，没有读写分离的话 Service
         */
        Integer WRITE = 0;
        /**
         * 读，读写分离情况下代表读
         */
        Integer READ = 1; //读
        /**
         * 远程窗口端口
         */
        Integer WEB_TERMINAL = 2;
        /**
         * Redis 的 NodePort 模式下的通讯端口
         */
        Integer REDIS_BUS_PORT = 3; //
    }

    interface ServiceManagerStatus{
        //空闲
        int FREE = 0;
        //已占用
        int USED = 1;
        //预分配
        int RESERVED = 2;

        // 总数，虚拟的状态，用于统计目前连接池总数调用 cloud_nodeport_used_info 和 cloud_loadbalancer_ip_info 获取总数
        int TOTAL = 3;
    }

    /**
     * ConfigMap data key
     * (data value 为 db-param 变量)
     */
    String MYSQL_CNF = "my3306.cnf";

    /**
     * 以ConfigMap存储MySQL.cnf配置,定义ConfigMap资源名后缀
     */
    String CONFIGMAP_DB_PARAMS = "-db-params";

    /**
     * k8s集群网络插件类型
     */
    interface K8sCNIType{
        String CALICO = "calico"; //calico网络插件
        String FABRIC = "fabric"; //博云fabric网络插件
    }

    /**
     * k8s集群存储插件类型
     */
    interface K8sCSIType{
        String CARINA = "carina"; //博云carina存储插件
        String TOOPLVM = "topolvm"; //tooplvm存储插件
        String HOST_PATH = "hostpath"; //hostPath
        String LOCAL_PATH_PROVISIONER = "local-path-provisioner";
        String S3 = "s3";
        String NAS = "nas";
    }

    interface KubeAddWay{
        String TOKEN="token";
        String KUBECONFIG="kubeConfig";
    }

    interface NodeStatus{
        String READY = "Ready";
        String NOT_READY = "Not ready";
    }

    interface NodeRole{
        String MASTER = "master";
        String SLAVE = "slave";
    }

    interface KubeState{
        byte ENABLED = 1;
        byte DISABLED = 0;
    }

    interface ImageConfig{
        // 镜像上传方式
        Integer OFFLINE = 0;
        Integer ONLINE = 1;
        // 镜像上传状态
        Integer SUCCESS = 0;
        Integer FAIL = 1;
        Integer UPLOADING = 2;
    }

    interface UploadBackupFileState{
        String success = "success";
        String failed = "failed";
        String running = "running";
    }

    interface ServiceType{
        String NODE_PORT = "NodePort";
        String LOAD_BALANCER = "LoadBalancer";
        String CLUSTER_IP = "ClusterIP";
    }

    interface BackupType {
        String incre = "incre";
        String full = "full";
        String cum = "cum";
    }

    interface UsernameAndPassword {
        String mongoDBUsername = "admin";
        String mongoDBPassword = "Passw0rd";
    }

    interface DMP {
        String CLUSTER_OPERATOR_ADD = "ADD";
        String CLUSTER_OPERATOR_DEL = "DEL";
        String CLUSTER_OPERATOR_UPDATE = "UPDATE";
        String INSTANCE_OPERATOR_ADD = "ADD";
        String INSTANCE_OPERATOR_DEL = "DEL";
        String INSTANCE_OPERATOR_UPDATE = "UPDATE";
        String REDIS_ARCH_HA = "HA";
        String REDIS_ARCH_CLUSTER = "SHARD";
        String INSTANCE_ROLE_MASTER = "主";
        String INSTANCE_ROLE_SLAVE = "备";
        String CLUSTER_TYPE_DB = "DB";
        String CLUSTER_TYPE_MID = "MID";
    }

    interface CustomResourceState{
        String READY = "ready";
        String MAINTAINING = "maintaining";
        String RECOVERING = "recovering";
        String RESTORING = "restoring";
        String CREATING = "creating";
        String UPDATING = "updating";
        String FAILED = "failed";
        String INVALID = "invalid";
    }

    interface Yaml{
        String NAME = "name";
        String NAMESPACE = "namespace";
        String METADATA = "metadata";
        String LABELS = "labels";
    }

    interface QuotaItems {
        String LIMIT_CPU = "limits.cpu";
        String LIMIT_MEMORY = "limits.memory";
        String REQUEST_STORAGE = "requests.storage";
    }

    interface ElasticSearch {
        String DATA = "data";
        String MASTER = "master";
        String EXPORTER = "EXPORTER";
    }

    interface Vastbase {
        String VASTBASE = "vastbase";
        String DCS = "dcs";
    }

    interface Arch {
        String HA = "HA";
        String CLUSTER = "Cluster";
        String REPLICASET = "ReplicaSet";
        String HA_SENTINEL = "HA-Sentinel";
        String MGR = "MGR";
    }

    interface Kind {
        String MYSQL = "MySQL";
        String MONGODB = "MongoDB";
        String MONGODB_CLUSTER = "MongoDB_Cluster";
        String OPENGAUSS = "openGauss";
        String REDIS_SENTINEL = "Redis-Sentinel";
        String ZOOKEEPER = "Zookeeper";
        String KAFKA = "Kafka";
        String NAMESERVER = "NameServer";
        String BROKER = "Broker";
        String REDIS = "Redis";
        String REDIS_CLUSTER = "Redis_Cluster";
        String ELASTICSEARCH = "Elasticsearch";
        String KIBANA = "Kibana";
        String POSTGRESQL = "PostgreSQL";
        String CLICKHOUSE = "ClickHouse";
        String FLINK = "Flink";
        String TIDB = "TiDB";
        String VASTBASE = "Vastbase";
        String DAMENG = "Dameng";
    }

    interface WeekDay {
        String MONDAY = "Monday";
        String TUESDAY = "Tuesday";
        String WEDNESDAY = "Wednesday";
        String THURSDAY = "Thursday";
        String FRIDAY = "Friday";
        String SATURDAY = "Saturday";
        String SUNDAY = "Sunday";
    }

    interface MongoDB {
        String SHARD = "shard";
        String CONFIG = "config";
        String ROUTER = "router";
        String MONGOS = "mongos";

        String SHARD_NAME = "Shard";
        String CONFIG_NAME = "ConfigServer";
        String MONGOS_NAME = "Mongos";

        int DEFAULT_ROUTER_PORT = 27017;
        int DEFAULT_SHARD_PORT = 27018;
        int DEFAULT_CONFIG_PORT = 27019;
    }

    interface Operator{
        String IN = "In";
        String NOT_IN = "NotIn";
        String EXISTS = "Exists";
        String DOESNOTEXIST = "DoesNotExist";
        String GT = "Gt";
        String LT = "Lt";
        String EQUAL = "Equal";
    }

    interface PvcCategory {
        String DATA = "data";
        String BACKUP = "backup";
        String LOG = "log";
    }

    interface KubeSchedulerMode{
        String TAINT = "Taint";
        String LABEL_TOLERATION = "Label_Toleration";
    }

    interface StorageType {
        String NAS = "nas";
        String S3 = "s3";
    }

    interface OperatorStorageType {
        String NFS = "nfs";
    }

    interface CLOUD_MOUNT_PATH {
        String NAS_PATH = "/opt/nfsmountpath";
        String S3_PATH = "/opt/s3mountpath";
    }

    interface NoSqlEnums {
        String MONGODB = "MONGODB";
        String REDIS = "REDIS";
        String SEQUOIADB = "SEQUOIADB";
    }

    interface SEQUOIADBNodeType{
        String Cluster = "Cluster";
        String MySQL = "MySQL";
        String MariaDB = "MariaDB";
    }

    interface ProductType{
        String INFORMIX = "Informix";
        String GBASE8S = "GBase 8s";
    }

    interface DbProductType {
        String MYSQL = "MYSQL";
        String POSTGRESQL = "POSTGRESQL";
        String OPENGAUSS = "OPENGAUSS";
    }

    interface ResourceName {
        String CPU = "cpu";
        String MEMORY = "memory";
        String STORAGE = "storage";
    }

    interface GraganaKeys {
        String VAR_NAMESPACE = "var-namespace";
        String VAR_JOB = "var-job";
        String GRAFANA_URL = "grafana_url";
        String SENTINEL_JOB = "sentinel_job";
        String ZK_JOB = "zk_job";
        String VAR_CLUSTER = "var-cluster";
        String NAMESVR_JOB = "namesvr_job";
        String VAR_NAME = "var-name";
        String VAR_EXPORTED_NAMESPACE = "var-exported_namespace";
        String VAR_CHI = "var-chi";
        String VAR_TIDB_CLUSTER = "var-tidb_cluster";
    }

    /**
     * 添加新应用类型 必须添加的 mount
     */
    interface Volumes {
        String TIMEZONE_NAME = "timezone";
        String TIMEZONE_HOSTPATH = "/usr/share/zoneinfo/Asia/Shanghai";
    }

    interface VolumeMounts {
        String TIMEZONE_MOUNTPATH = "/etc/localtime";
    }

    interface ImagePullPolicy {
        String Always = "Always";
        String IFNOTPRESENT = "IfNotPresent";
        String Never = "Never";
    }

    interface ConfigUpdateStrategy{
        String ROLLINGUPDATE = "RollingUpdate";
        String INPLACEUPDATE = "InPlaceUpdate";
    }

    interface PodManagementPolicy {
        String PARALLEL = "Parallel";
        String ORDEREDREADY  = "OrderedReady ";
    }

}