package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.BackupHis;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.vo.CloudBackupStorageVO;
import cn.newdt.cloud.domain.RestoreHis;
import cn.newdt.cloud.domain.dmp.BinlogBackupHis;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.mapper.BackupMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.BackupService;
import cn.newdt.cloud.service.CloudAppService;
import cn.newdt.cloud.service.KubeClientService;
import cn.newdt.cloud.service.RestoreServiceImpl;
import cn.newdt.cloud.service.impl.MysqlV2Service;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.service.sched.TriggerInfoService;
import cn.newdt.cloud.utils.BackupUtil;
import cn.newdt.cloud.utils.CloudFTPUtil;
import cn.newdt.cloud.utils.JsonUtil;
import cn.newdt.commons.bean.UserInfo;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.UserUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.shindata.mysql.v1.MySQLHA;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.DatasourceConstant.CLOUD_BACKUP_HIS;

@Slf4j
public class MysqlBackupAndRestoreWatch extends OpsProcessorContext implements OpsPostProcessor<MySQLHA> {

    @Autowired
    private CloudAppService cloudAppService;

    @Autowired
    private KubeClientService kubeClientService;

    @Autowired
    private BackupMapper backupMapper;

    @Autowired
    private BackupUtil backupUtil;

    @Autowired
    private RestoreServiceImpl restoreServiceImpl;

    @Autowired
    private CloudFTPUtil ftpUtil;

    @Autowired
    private KubeClientService clientService;

    @Autowired
    private BackupService backupService;

    @Autowired
    private TriggerInfoService triggerInfoService;

    @Autowired
    private MysqlV2Service mysqlV2Service;

    @Autowired
    private CloudAppService appService;

    @Value("${ftp.basePath:/tmp/}")
    private String basePath;

    @Override
    public OpsResultDTO postProcess(TriggerHis triggerHis) throws Exception {
        log.info("[mysql备份恢复]进入mysql备份恢复watch");
        //创建返回结果
        OpsResultDTO.Builder result = OpsResultDTO.builder().stopJob(false);  //.stopJob是是否停止任务的标识，TRUE为停止
        //根据appId获取app对象
        Map<String, String> jobDataMap = triggerHis.getJobDataMap();
        String appId = jobDataMap.get("appId");
        CloudApp app = cloudAppService.get(Integer.valueOf(appId));
        KubeClient kubeClient = clientService.get(app.getKubeId());
        //修改crRun
        CloudApp cloudApp = new CloudApp();
        cloudApp.setCrRun(app.getCr());
        cloudApp.setId(app.getId());
        cloudAppService.update(cloudApp);
        //获取操作类型
        String triggerName = triggerHis.getTriggerName();
        String handType = triggerName.substring(0, triggerName.lastIndexOf("_"));

        //获取cr对象
        MySQLHA cr = kubeClientService.get(app.getKubeId())
                .listCustomResource(MySQLHA.class, app.getCrName(), app.getNamespace());
        //当前时间
        Date nowDate = new Date();
        String extDataStr = jobDataMap.get("extDataStr");
        JSONObject extData = JSON.parseObject(extDataStr);
        //获取超时时间
        Integer backupTimeOut = backupUtil.getBackupTimeOut(app.getId());
        if (ActionEnum.BACKUP.toString().equalsIgnoreCase(handType)){
            try {
                String backupHisIdStr = extData.getString("backupHisId");
                String kubeIdStr = extData.getString("kubeId");
                String backupIp = extData.getString("backupIp");
                String changeIdStr = extData.getString("changeId");
//                String lastBinlogName = extData.getString("lastBinlogName");
                String backupPath = extData.getString("backupPath");
                String userInfoStr = extData.getString("userInfo");
                String backupDbLog = extData.getString("backupDbLog");
                String primaryPodName = extData.getString("primaryPodName");
                Integer binlogBackupHisId = extData.getInteger("binlogBackupHisId");
                String latestBinlogCodes = extData.getString("latestBinlogCodes");

                log.info("[mysql备份]trigger中参数信息如下：");
                log.info("[mysql备份]trigger中参数：backupHisId={}, kubeId={}, backupIp={}, changeId={}, backupPath={}, userInfo={}, backupDbLog={}, primaryPodName={}, binlogBackupHisId={}, latestBinlogCodes={}",
                        backupHisIdStr, kubeIdStr, backupIp, changeIdStr, backupPath, userInfoStr, backupDbLog, primaryPodName, binlogBackupHisId, latestBinlogCodes);

                //异步方法放入用户信息
                UserInfo userInfo = JsonUtil.toObject(UserInfo.class, userInfoStr);
                UserUtil.setAsyncUserInfo(userInfo);
                Integer changeId = Integer.valueOf(changeIdStr);
                log.info("[mysql备份]开始判断");
                if (!StringUtils.isEmpty(backupHisIdStr) && !StringUtils.isEmpty(kubeIdStr)) {
                    log.info("[mysql备份]判断1");
                    //备份历史ID不为空执行判断脚本
                    BackupHis backupHis = backupService.get(Integer.parseInt(backupHisIdStr));
                    if (backupHis != null) {
                        log.info("[mysql备份]判断2");
                        Timestamp now = new Timestamp(System.currentTimeMillis());
                        Timestamp startTime = backupHis.getStartTime();
                        if (backupUtil.checkBackupAndRestoreTimeout(startTime, now, backupTimeOut)) {
                            log.info("[mysql备份]异常判断1：备份超时");
                            backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "Mysql备份超时！");
                            result.stopJob(true).msg("Mysql备份超时！").status(StatusConstant.FAIL);
                            OpsResultDTO dto = result.build();
                            cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                            return dto;
                        }
                        log.info("[mysql备份]执行检查结果的命令，命令为：cat /data/tmp/backup_" + backupHisIdStr);
                        String catFullBackupResultCmd = String.format("[ -r /data/tmp/backup_%s ] && cat /data/tmp/backup_%s  || echo '{}'", backupHisIdStr, backupHisIdStr);
                        String catFullBackupResult = kubeClient.execCmd(app.getNamespace(), backupHis.getPodName(),"xtrabackup", "sh", "-c",
                                catFullBackupResultCmd);
                        // 执行结果处理，解析执行结果，更新备份历史
                        //校验脚本执行成功
                        if (!StringUtils.isEmpty(catFullBackupResult)) {
                            log.info("[mysql备份]判断3");
                            Map backupRes = JsonUtil.toObject(Map.class, catFullBackupResult);
                            //判断备份以及binlog备份成功，binlog根据日志判断
                            if ("complete".equals(backupRes.get("status"))) {
                                log.info("[mysql备份]判断4");
                                //设置一个标识判断binlog是否备份成功
                                boolean isBinlogSuccess = false;
                                //判断是否执行binlog备份
                                if (true) {
                                    Timestamp binlogStartTime = Timestamp.valueOf((String) backupRes.get("startTime"));
                                    try {
                                        watchBinlogBackup(result, backupHis, kubeClient, app, latestBinlogCodes, binlogStartTime);
                                        isBinlogSuccess = true;
                                    } catch (Exception e) {
                                        //备份执行失败
                                        String backupLog = backupUtil.readLogFile(kubeClient, app.getNamespace(), backupHis.getPodName(), "xtrabackup", "/data/tmp/backup_*.log");
                                        backupUtil.backupSuccessUpdateHis(backupHis, backupRes, backupIp, changeId, StatusConstant.FAIL, 0L);
                                        backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", backupLog);
                                        result.stopJob(true).msg("mysql备份失败，binlog备份失败！" + e.getLocalizedMessage()).status(StatusConstant.FAIL);
                                        OpsResultDTO dto = result.build();
                                        cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                                        return dto;
                                    }
                                }else{
                                    isBinlogSuccess = true;
                                }

                                //备份执行成功
                                if(isBinlogSuccess){
                                    backupUtil.backupSuccessUpdateHis(backupHis, backupRes, backupIp, changeId, StatusConstant.SUCCESS, Long.valueOf(String.valueOf(backupRes.get("fileSize"))));
                                    backupUtil.backupReturn(backupHis, changeId, StatusConstant.SUCCESS, String.valueOf(backupRes.get("fileName")), "mysql备份成功！");
                                    result.stopJob(true).msg("mysql备份成功！").status(StatusConstant.SUCCESS);
                                    OpsResultDTO dto = result.build();
                                    cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                                    return dto;
                                }
                            } else if ("failed".equals(backupRes.get("status"))) {
                                //备份执行失败
                                String backupLog = backupUtil.readLogFile(kubeClient, app.getNamespace(), backupHis.getPodName(), "xtrabackup", "/data/tmp/backup_*.log");
                                backupUtil.backupSuccessUpdateHis(backupHis, backupRes, backupIp, changeId, StatusConstant.FAIL, 0L);
                                backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", backupLog);
                                result.stopJob(true).msg("mysql备份失败，请查看备份日志！").status(StatusConstant.FAIL);
                                OpsResultDTO dto = result.build();
                                cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                                return dto;
                            }else{
                                backupUtil.backupSuccessUpdateHis(backupHis, backupRes, backupIp, changeId, StatusConstant.RUNNING, 0L);
                                backupUtil.backupReturn(backupHis, changeId, null, "", "尚未生成备份结果文件");
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("[Trigger Paused]triggerGroup:{},Name:{},error:{}", triggerHis.getTriggerGroup(), triggerHis.getTriggerName(), e);
            }
        }else if(ActionEnum.RESTORE.toString().equalsIgnoreCase(handType)){
            /**
             * 恢复步骤数字符号与操作说明
             * 1：mysql启停
             * 2：检测liveness
             * 3：清除备份中的replica metadata
             * 4：配置主从
             * 5：解除维护模式
             * 6：binlog恢复
             */
            try {
                String restoreHisIdStr = extData.getString("restoreHisId");
                String kubeIdStr = extData.getString("kubeId");
                String changeHisIdStr = extData.getString("changeHisId");

                log.info("[mysql恢复]恢复定时watch参数   restoreHisId={}， changeHisId={}， kubeId={}，", restoreHisIdStr, changeHisIdStr, kubeIdStr);

                Integer changeId = Integer.valueOf(changeHisIdStr);
                String restoreTime = extData.getString("restoreTime");
                String startBinlogName = extData.getString("startBinlogName");
                String binlogPos = extData.getString("binlogPos");
                String podNames = extData.getString("podNames");
                String primaryPodName = extData.getString("primaryPodName");
                String primaryPodIp = extData.getString("primaryPodIp");
                String slavePodIps = extData.getString("slavePodIps");
                String backupAppId = extData.getString("backupAppId");
                String backupFtpPath = extData.getString("backupFtpPath");
                String backupHisIdStr = extData.getString("backupHisId");
                Integer backupHisId = null;
                if (!StringUtils.isEmpty(backupHisIdStr)) {
                    backupHisId = Integer.valueOf(backupHisIdStr);
                }
                String restoreNums = extData.getString("restoreNums");
                String fileName = extData.getString("fileName");

                log.info("[mysql恢复]恢复定时watch参数信息如下：");
                log.info("[mysql恢复]恢复定时watch参数：restoreHisId={}, kubeId={}, changeHisId={}, restoreTime={}, startBinlogName={}, binlogPos={}, podNames={}, primaryPodName={}, primaryPodIp={}, slavePodIps={}, backupAppId={}, backupFtpPath={}, restoreNums={}, fileName={}",
                        restoreHisIdStr, kubeIdStr, changeHisIdStr, restoreTime, startBinlogName, binlogPos, podNames, primaryPodName, primaryPodIp, slavePodIps, backupAppId, backupFtpPath, restoreNums, fileName);
                log.info("[mysql恢复]判断1");
                //备份历史ID不为空执行判断脚本
                RestoreHis restoreHis = restoreServiceImpl.get(Integer.parseInt(restoreHisIdStr));
                log.info("[mysql恢复]判断2");
                if (restoreHis == null) {
                    backupUtil.restoreReturn(restoreHis, changeId, "恢复历史为空！", StatusConstant.FAIL);
                    result.stopJob(true).msg("恢复历史为空！").status(StatusConstant.FAIL);
                    OpsResultDTO dto = result.build();
                    if (dto.getStopJob()){
                        cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                    }
                    return dto;
                }

                //校验是否是操作后的ready
                if (isStatusOutDated(triggerHis, cr.getStatus().getLastUpdateTime())) {
                    return result.build();
                }

                Timestamp now = new Timestamp(System.currentTimeMillis());
                Timestamp startTime = restoreHis.getStartTime();
                if (now.getTime() - startTime.getTime() > 1000 * 60 * 60 * 24) {
                    //一天为最大备份时间，超过一天则直接判断为失败
                    backupUtil.restoreReturn(restoreHis, changeId, "恢复超时！", StatusConstant.FAIL);
                    result.stopJob(true).msg("恢复超时！").status(StatusConstant.FAIL);
                    OpsResultDTO dto = result.build();
                    if (dto.getStopJob()){
                        cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                    }
                    return dto;
                }
                //获取cr状态
                String state = cr.getStatus().getState();
                if ("ready".equalsIgnoreCase(state)) {
                    //恢复成功
                    //判断是否进行binlog恢复
                    if (null != restoreTime) {
                        try {
                            restoreByTime(restoreTime, startBinlogName, binlogPos, primaryPodName, backupAppId, now, app, restoreHis.getRestoreHisId(), backupHisId);
                        } catch (Exception e) {
                            log.error("[mysql恢复]xbk备份恢复成功，binlog恢复错误，信息为：", e.getLocalizedMessage());
                            backupUtil.restoreReturn(restoreHis, changeId, "xbk备份恢复成功，binlog恢复错误，信息为：" + e.getLocalizedMessage(), StatusConstant.FAIL);
                            result.stopJob(true).msg("xbk备份恢复成功，binlog恢复错误，信息为：" + e.getLocalizedMessage()).status(StatusConstant.FAIL);
                            OpsResultDTO dto = result.build();
                            if (dto.getStopJob()) {
                                cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                            }
                            return dto;
                        }
                    }

                    //补全恢复历史
                    //恢复执行成功
                    backupUtil.restoreReturn(restoreHis, changeId, "恢复成功！", StatusConstant.SUCCESS);
//                updateRestoreHis(restoreHisId,restoreStartDate,nowDate,StatusConstant.SUCCESS);
                    result.stopJob(true).msg("恢复成功！").status(StatusConstant.SUCCESS);
                    OpsResultDTO dto = result.build();
                    if (dto.getStopJob()) {
                        cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                    }
                    return dto;
                }


            } catch (Exception e) {
                log.error("[Trigger Paused]triggerGroup:{},Name:{},error:{}", triggerHis.getTriggerGroup(), triggerHis.getTriggerName(), e);
            }
        }

        return null;
    }

    private String restoreByTime(String restoreTime, String startBinlogName, String binlogPos, String primaryPodName, String backupAppId, Timestamp now, CloudApp app, int restoreHisId, Integer backupHisId) throws Exception {
        log.info("[mysql恢复]执行按binlog恢复");
        //执行binlog恢复
        //查询biunlog备份的路径
        //需要查询当前恢复时间之后最近的XBK备份历史
        String restoreTimeStr = restoreTime.replaceAll("_", " ");
        log.info("[mysql恢复]恢复时间：" + restoreTimeStr);
        BackupHis closeBackupHis = backupMapper.getCloseBackupHisAfterRestoreTime("cust1", CLOUD_BACKUP_HIS, Integer.valueOf(backupAppId), restoreTimeStr);

        //获取binlog备份路径
        BinlogBackupHis binlogBackupHis = backupMapper.getBinlogBackupHisByAppIdAndStartTime("cust1", DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, Integer.valueOf(backupAppId), String.valueOf(closeBackupHis.getStartTime()));
        String backupDir = binlogBackupHis.getBackupDir();
        //获取binlog备份路径最后的日期文件夹
        if (backupDir.length() - 1 == backupDir.lastIndexOf("/")) {
            backupDir = backupDir.substring(0, backupDir.length() - 1);
        }
        String binlogTimeDir = backupDir.substring(backupDir.lastIndexOf("/") + 1);

        log.info("[mysql恢复]恢复文件所在路径：" + backupDir);
        //当前时间戳字符串
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String nowStr = sdf.format(now);
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        //设置mysql主节点mysql为非只读：set global read_only=0;
//        kubeClient.execCmd(app.getNamespace(), primaryPodName, "mysql", "sh", "-c", "mysql -uk8sadmin -pk8sadmin -e 'set global read_only=0;'");

        //临时挂载
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        String mountCmd = "";
        if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
            mountCmd = "bash /scripts/mount-remote-storage.sh true /mnt/share "
                    + CloudAppConstant.OperatorStorageType.NFS
                    + " " + cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
        } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
            mountCmd = "bash /scripts/mount-remote-storage.sh true /mnt/share "
                    + CloudAppConstant.StorageType.S3
                    + " " + cloudBackupStorageVO.getServer()
                    + " " + (StringUtils.isEmpty(cloudBackupStorageVO.getRegion()) ? "''" : cloudBackupStorageVO.getRegion())
                    + " " + cloudBackupStorageVO.getBucket()
                    + " " + cloudBackupStorageVO.getAccessKey()
                    + " " + cloudBackupStorageVO.getSecretKey();
        } else {
            throw new CustomException(600, "binlog恢复失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
        }

        try {
            kubeClient.execCmd(app.getNamespace(), primaryPodName, "xtrabackup", "sh", "-c", mountCmd);
        } catch (Exception e) {
            log.error("[mysqlbinlog恢复]挂载远程路径失败，信息为：" + e.getMessage() + "     详细信息为:" + e);
            throw new CustomException(600, "binlog恢复失败！挂载远程路径失败！");
        }

        //复制binlog到/data/tmp
        String cpBinlogCmd = "cp -r /mnt/share/mysql/mysqlha/" + app.getNamespace() + "/" + closeBackupHis.getAppName() + "/binlog/" + binlogTimeDir + " /data/tmp/";
        try {
            kubeClient.execCmd(app.getNamespace(), primaryPodName, "xtrabackup", "sh", "-c", cpBinlogCmd);
        } catch (Exception e) {
            log.error("[mysql备份binlog]复制binlog失败，信息为：" + e.getMessage() + "     详细信息为:" + e);
            throw new CustomException(600, "备份binlog失败！复制binlog失败！");
        }

        //将返回的gtid的tid+1，根据gtid获取所在binlog文件名称
        BackupHis backupHis = backupService.get(backupHisId);
        String xbkGtid = backupHis.getBinlogPos();
        String[] xbkGtidArr = xbkGtid.split(":");
        String gtid = xbkGtidArr[0] + ":" + (Integer.valueOf(xbkGtidArr[1]) + 1);
        //获取包含起始点信息的binlog文件
        String binlogFileName = "";
        try {
            String searchPost = "sh /scripts/mysql-search-pos.sh " + gtid + " " + binlogTimeDir;
            String binlogFileNameStr = kubeClient.execCmd(app.getNamespace(), primaryPodName, "xtrabackup", "sh", "-c", searchPost);
            binlogFileName = binlogFileNameStr.replace("\n", "");
        } catch (Exception e) {
            log.error("[mysql binlog恢复]binlog恢复失败，信息为：" + e.getMessage() + "     详细信息为:" + e);
            throw new CustomException(600, "binlog恢复失败！查询起始pos失败！");
        }
        //找到关键字：SESSION.GTID_NEXT= '${gtid}'后面第一个end_log_pos，将其作为pos起始点
        try {
            String getBinlogContent = "mysqlbinlog " + "/data/tmp/" + binlogTimeDir + "/" + binlogFileName;
            String binlogContent = kubeClient.execCmd(app.getNamespace(), primaryPodName, "xtrabackup", "sh", "-c", getBinlogContent);
            int gtidIndex = binlogContent.indexOf("SESSION.GTID_NEXT= '" + gtid + "'");
            if (-1 == gtidIndex) {
                log.error("[mysql binlog恢复]binlog恢复失败，未在binlog中找到gtid：" + gtid);
                throw new CustomException(600, "binlog恢复失败！未在binlog中找到gtid！" + gtid);
            }
            String posStr = binlogContent.substring(gtidIndex);
            String posStartStr = posStr.substring(posStr.indexOf("end_log_pos ") + "end_log_pos ".length());
            binlogPos = posStartStr.substring(0, posStartStr.indexOf(" "));
        } catch (Exception e) {
            log.error("[mysql binlog恢复]binlog恢复失败，信息为：" + e.getMessage() + "     详细信息为:" + e);
            throw new CustomException(600, "binlog恢复失败！查询起始pos失败！");
        }
        //在mysql容器执行binlog恢复
        String restoreByBinlogScript = "bash /scripts/mysql-binlog-restore.sh /data/tmp/" + binlogTimeDir + " " + binlogFileName + " " + binlogPos + " " + restoreTime + " binlog_restore_" + restoreHisId;
        log.info("[mysql恢复]开始执行按binlog恢复:" + restoreByBinlogScript);
        String restoreRes = kubeClient.execCmd(app.getNamespace(), primaryPodName, "mysql", "sh", "-c", restoreByBinlogScript);
        log.info("[mysql恢复]结束执行按binlog恢复,结果：" + restoreRes);
        //检查日志
        String binlogBackupLog = "";
        try {
            binlogBackupLog = kubeClient.execCmd(app.getNamespace(), primaryPodName, "mysql", "sh", "-c",
                    "cat /data/tmp/binlog_restore_" + restoreHisId);
        } catch (Exception e) {
            //失败
            throw new CustomException(600, "binlog检查恢复失败！信息为：" + e);
        }
        JSONObject binlogLog = JSON.parseObject(binlogBackupLog);
        String status = binlogLog.getString("status");
        log.info("[binlog恢复]恢复结果：" + status);

        //取消挂载
        String umountCmd = "bash /scripts/mount-remote-storage.sh false /mnt/share";
        try {
            kubeClient.execCmdOneway(app.getNamespace(), primaryPodName, "xtrabackup", "sh", "-c", umountCmd);
        } catch (Exception e) {
            log.error("[mysql备份binlog]取消挂载远程路径失败，信息为：" + e.getMessage() + "     详细信息为:" + e);
            throw new CustomException(600, "备份binlog失败！取消挂载远程路径失败！");
        }

        //清理复制的binlog
        String rmBinlogCmd = "rm -rf /data/tmp/" + binlogTimeDir;
        try {
            kubeClient.execCmdOneway(app.getNamespace(), primaryPodName, "xtrabackup", "sh", "-c", rmBinlogCmd);
        } catch (Exception e) {
            log.error("[mysql备份binlog]清理binlog失败，信息为：" + e.getMessage() + "     详细信息为:" + e);
            throw new CustomException(600, "备份binlog失败！清理binlog失败！");
        }

        if("failed".equals(status)){
            //失败
            throw new CustomException(600, "binlog恢复结果为：失败！");
        }
        if(status.indexOf("Not Found Any required binlog file") != -1){
            //失败
            throw new CustomException(600, "未找到必要的binlog文件！");
        }
        return binlogTimeDir;
    }

    /**
     * 创建目录 /backup/mgr/{namespace}/{crname}/backup/binlog/{timestamp}
     * 遍历扫描出binlog目录新增的binlog文件
     * 拷贝binlog文件到备份目录
     */
    private void watchBinlogBackup(OpsResultDTO.Builder result, BackupHis backupHis, KubeClient kubeClient, CloudApp app, String latestBinlogCodes, Timestamp startTime) {
        // 1.获取节点用来备份binlog
        String podName = null;
        List<PodDTO> allPodMySQL = backupUtil.getAllPodMySQL(app, kubeClient);
        if (CollectionUtils.isEmpty(allPodMySQL)) {
            throw new CustomException(600, "没有找到用来备份binlog的节点！");
        }

        // 2.判断主节点的binlog是否可用，要求是001的bonlog创建时间要早于上次xbk备份
        SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd HH:mm:ss");
        //获取上次备份历史，这次历史已经插入，所以应该是获取倒数第二新的数据
        PageDTO pageDTO = new PageDTO();
        Map<String, Object> condition = new HashMap<>();
        condition.put("appType", app.getKind());
        condition.put("status", "0");
        condition.put("appId", app.getId());
        pageDTO.setCondition(condition);
        pageDTO.setPageSort("desc");
        pageDTO.setPageSortProp("endTime");
        pageDTO.setPageSize(1);
        PageInfo<BackupHis> lastTwoBackupHisList = backupService.listPage(pageDTO);
        List<BackupHis> list = lastTwoBackupHisList.getList();
        if (CollectionUtils.isEmpty(list) || backupHis.getBackupType().equalsIgnoreCase("full")) {
            //第一次备份，直接取第一个节点
            podName = allPodMySQL.get(0).getPodName();
        } else {
            //获取上次备份的开始时间
            BackupHis oldBackupHis = list.get(0);
            Timestamp oldBackupStartTime = oldBackupHis.getStartTime();
            //遍历所有节点，获取节点中的首个binlog创建时间
            List<String> podNameList = allPodMySQL.stream().map(podDTO -> {
                //查询binlog创建时间
                try {
                    String binlogCreateTime = kubeClient.execCmd(app.getNamespace(), podDTO.getPodName(), AppKind.MYSQL_HA.getContainerName(), "sh", "-c",
                            "mysqlbinlog /data/binlog/mysql-bin.000001 | grep -E \"Start: binlog.*created\"");
                    String binlogCreateTimeReplace = binlogCreateTime.replace("\n", "");
                    String binlogCreateTimeFin = binlogCreateTimeReplace.substring(binlogCreateTimeReplace.indexOf("created ") + "created ".length(), binlogCreateTimeReplace.indexOf(" at startup"));
                    //处理时间
                    String[] split = binlogCreateTimeFin.split(" ");
                    String[] split1 = split[split.length - 1].split(":");
                    for (String every : split1) {
                        if (every.length() == 1) {
                            StringBuilder stringBuilder = new StringBuilder();
                            StringBuilder append = stringBuilder.append("0").append(every);
                            every = append.toString();
                            split1[0] = every;
                        }
                    }
                    binlogCreateTimeFin = split[0] + " " + split1[0] + ":" + split1[1] + ":" + split1[2];
                    long binlogLong = LocalDateTime.parse(binlogCreateTimeFin, DateTimeFormatter.ofPattern("yyMMdd HH:mm:ss")).atZone(ZoneId.systemDefault())
                            .toInstant().toEpochMilli();
                    long oldBackTime = LocalDateTime.parse(sdf.format(oldBackupStartTime), DateTimeFormatter.ofPattern("yyMMdd HH:mm:ss")).atZone(ZoneId.systemDefault())
                            .toInstant().toEpochMilli();
                    //比较时间
                    if (binlogLong < oldBackTime) {
                        //符合要求
                        return podDTO.getPodName();
                    } else {
                        return null;
                    }
                } catch (Exception e) {
                    log.error("[mysqlhabinlog备份]查询binlog文件创建时间失败！");
                    throw new CustomException(600, "查询binlog文件创建时间失败！");
                }
            }).collect(Collectors.toList());
            //去除null
            podNameList.removeAll(Collections.singleton(null));
            if (CollectionUtils.isEmpty(podNameList)) {
                throw new CustomException(600, "不存在可以备份binlog的节点！建议尽快执行全量备份！");
            }
            podName = podNameList.get(0);
        }

        if (StringUtils.isEmpty(podName)) {
            throw new CustomException(600, "未找到备份节点！");
        }
        try {
            kubeClient.execCmd(app.getNamespace(), podName, AppKind.MYSQL_HA.getContainerName(), "sh", "-c",
                    "mysql -u${MYSQL_ROOT_USER} -p${MYSQL_ROOT_PASSWORD} -P${DB_PORT} -e \"flush logs\" 1> /dev/null 2>&1");
        } catch (Exception e) {
            log.error("[mysqlhabinlog文件上传]刷新binlog文件失败！");
            throw new CustomException(600, "刷新binlog文件失败！");
        }

        // 2.刷新binlog
        try {
            kubeClient.execCmd(app.getNamespace(), podName, AppKind.MYSQL_HA.getContainerName(), "sh", "-c",
                    "mysql -u${MYSQL_ROOT_USER} -p${MYSQL_ROOT_PASSWORD} -P${DB_PORT} -e \"flush logs\" 1> /dev/null 2>&1");
        } catch (Exception e) {
            log.error("[mysqlhabinlog文件上传]刷新binlog文件失败！");
            throw new CustomException(600, "刷新binlog文件失败！");
        }

        // 3.筛选出需要备份的binlog
        String binlogResult;
        try {
            // 返回样式 mysql-bin.000001 20240202101010mysql-bin.000001
            binlogResult = kubeClient.execCmd(app.getNamespace(), podName, "mysql", "sh", "-c",
                    "ls -l --time-style='+%Y%m%d%H%M%S' /data/binlog/mysql-bin.* | awk -F'[ ]+' '{print $7,$6$7}' ");
            log.info("[mysql备份binlog文件上传] 本次备份 binlogResult:{}", binlogResult);
        } catch (Exception e) {
            log.error("[mysql备份binlog文件上传] 查找binlog文件信息失败，信息为:{} 详细信息为:{}", e.getMessage(), e);
            throw new CustomException(600, "备份binlog失败！");
        }
        if (org.apache.commons.lang3.StringUtils.isEmpty(binlogResult)) {
            throw new CustomException(600, "binlog文件为空！");
        }
        // key binlog文件名称，value binlog文件的识别码
        Map<String, String> binlogMap = Arrays.stream(binlogResult.trim().split("\n")).map(item -> item.split("\\s+"))
                .collect(Collectors.toMap(arr -> arr[0], arr -> arr[1]));
        // 去除当前使用的binlog和索引文件
        binlogMap.remove("/data/binlog/mysql-bin.index");
        binlogMap.remove(Collections.max(binlogMap.keySet()));
        Collection<String> uploadBinlogNames = binlogMap.keySet();
        //下面代码为过滤binlog使用，勿删
//        // 对比上次binlog文件，找出差异文件上传
//        if (!org.apache.commons.lang3.StringUtils.isEmpty(latestBinlogCodes)) {
//            List<String> latestBinlogCodeList = Arrays.stream(latestBinlogCodes.split(",")).collect(Collectors.toList());
//            uploadBinlogNames = binlogMap.entrySet().stream().filter(item -> !latestBinlogCodeList.contains(item.getValue()))
//                    .map(Map.Entry::getKey).collect(Collectors.toSet());
//        }
//        log.info("[mysql备份binlog文件备份] binlog {}", JsonUtil.toJson(uploadBinlogNames));

        // 4.临时挂载
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
            String mountCmd = "bash /scripts/mount-remote-storage.sh true /mnt/share "
                    + CloudAppConstant.OperatorStorageType.NFS
                    + " " + cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
            try {
                kubeClient.execCmd(app.getNamespace(), podName, "xtrabackup", "sh", "-c", mountCmd);
            } catch (Exception e) {
                log.error("[mysql备份binlog]挂载远程路径失败，信息为：" + e.getMessage() + "     详细信息为:" + e);
                throw new CustomException(600, "备份binlog失败！挂载远程路径失败！");
            }
        } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
            String mountCmd = "bash /scripts/mount-remote-storage.sh true /mnt/share "
                    + CloudAppConstant.StorageType.S3
                    + " " + cloudBackupStorageVO.getServer()
                    + " " + (StringUtils.isEmpty(cloudBackupStorageVO.getRegion()) ? "''" : cloudBackupStorageVO.getRegion())
                    + " " + cloudBackupStorageVO.getBucket()
                    + " " + cloudBackupStorageVO.getAccessKey()
                    + " " + cloudBackupStorageVO.getSecretKey();
            try {
                kubeClient.execCmd(app.getNamespace(), podName, "xtrabackup", "sh", "-c", mountCmd);
            } catch (Exception e) {
                log.error("[mysql备份binlog]挂载远程路径失败，信息为：" + e.getMessage() + "     详细信息为:" + e);
                throw new CustomException(600, "备份binlog失败！挂载远程路径失败！");
            }
        } else {
            throw new CustomException(600, "binlog备份失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
        }

        // 5.遍历备份
        String timestamp = startTime.toLocalDateTime().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String binlogBackupPodPath = String.format("/mnt/share/mysql/mysqlha/" + app.getNamespace() + "/" + app.getCrName() + "/binlog/%s/", timestamp);
        try {
            kubeClient.execCmd(app.getNamespace(), podName, "xtrabackup", "sh", "-c", "mkdir -p " + binlogBackupPodPath);
            for (String binlog : uploadBinlogNames) {
                String cmd = String.format("cp %s %s ", binlog, binlogBackupPodPath);
                log.info("[mysql备份binlog文件备份]命令为：" + cmd);
                kubeClient.execCmd(app.getNamespace(), podName, "xtrabackup", "sh", "-c", cmd);
            }
        } catch (Exception e) {
            log.error("[mysql备份binlog文件上传]复制binlog失败，信息为：" + e.getMessage() + "     详细信息为:" + e);
            throw new CustomException(600, "复制binlog失败！");
        }

        // 6.取消挂载
        String umountCmd = "bash /scripts/mount-remote-storage.sh false /mnt/share";
        try {
            kubeClient.execCmdOneway(app.getNamespace(), podName, "xtrabackup", "sh", "-c", umountCmd);
        } catch (Exception e) {
            log.error("[mysql备份binlog]取消挂载远程路径失败，信息为：" + e.getMessage() + "     详细信息为:" + e);
            throw new CustomException(600, "备份binlog失败！取消挂载远程路径失败！");
        }

        BinlogBackupHis binlogBackupHis = backupService.listBinlogBackupHis(app.getId());
        binlogBackupHis.setFileCodes(String.join(",", binlogMap.values()));
        binlogBackupHis.setStartTime(startTime);
        binlogBackupHis.setEndTime(new Timestamp(System.currentTimeMillis()));
        binlogBackupHis.setBackupLength(0);
        binlogBackupHis.setClusterId(app.getId());
        binlogBackupHis.setClusterName(app.getName());
        binlogBackupHis.setMsg("Y");
        binlogBackupHis.setStatus("0");
        binlogBackupHis.setBackupDir(binlogBackupPodPath);
        backupMapper.updateBinlogBackupHis(UserUtil.getSchema(), DatasourceConstant.CLOUD_BINLOG_BACKUP_HIS, binlogBackupHis);
        result.stopJob(true).status(StatusConstant.SUCCESS);
    }

}