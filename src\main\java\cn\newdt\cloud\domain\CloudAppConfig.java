package cn.newdt.cloud.domain;

import cn.newdt.cloud.constant.ImageKindEnum;
import cn.newdt.commons.exception.CustomException;
import com.alibaba.fastjson.JSONArray;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description cloud_app_config
 * @date 2022-09-05
 */
@Data
public class CloudAppConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;
    /**
     * 名称
     */
    private String name;

    /**
     * 应用类型
     */
    private String kind;

    /**
     * 应用架构
     */
    private String arch;

    /**
     * 版本号
     */
    private String version;

    /**
     * crd yaml
     */
    private String crd;

    /**
     * 配置项列表
     */
    private String template;

    /**
     * create_time
     */
    private Date createTime;

    /**
     * create_user
     */
    private String createUser;
    /**
     * 类型：app、operator
     */
    private String type;

    @Data
    public static class AppConfigTemplate{
        private String type;
        private String key;
        private String val;
    }

    public Map<ImageKindEnum, String> parseImageTemplate() {
        String template = getTemplate();
        List<AppConfigTemplate> templates = JSONArray.parseArray(template).toJavaList(CloudAppConfig.AppConfigTemplate.class);
        Map<ImageKindEnum, String> result = new EnumMap<>(ImageKindEnum.class);
        for (CloudAppConfig.AppConfigTemplate temp : templates) {
            if ("image".equals(temp.getType())) {
                String val = temp.getVal();
                if (!val.contains(":")) {
                    throw new CustomException(600, String.format("镜像信息格式错误，%s", val));
                }
                int idx = val.lastIndexOf(":");
                String imgName = val.substring(0, idx);
                String imgVersion = val.substring(idx + 1);
                result.put(ImageKindEnum.valueOf(temp.getKey()), val);
            }
        }
        return result;
    }
}