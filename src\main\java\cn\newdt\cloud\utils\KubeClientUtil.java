package cn.newdt.cloud.utils;

import cn.newdt.cloud.dto.Label;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.csi.CSIUtil;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.api.model.apps.StatefulSet;
import io.fabric8.kubernetes.api.model.apps.StatefulSetStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * KubeClientApi 工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/30 18:18
 */
@Slf4j
public class KubeClientUtil {

    public static List<PersistentVolume> provideHostpathPV(String pvNamePrefix, String podNamePrefix, String crName, String namespace, String storage, String hostpathRoot, Integer size, Integer curMembers, Label[] labels, String pvSelectorLabel) {
        Map<String, String> labelMap = labels == null ? new HashMap<>() :
                Arrays.stream(labels).collect(Collectors.toMap(Label::getName, Label::getValue));
        return IntStream.range(curMembers, size + curMembers).mapToObj(i -> {
            String podName = StringUtils.isEmpty(podNamePrefix) ? "" + i : podNamePrefix + "-" + i;
            String pvName = pvNamePrefix + "-" + podName;
            String[] pathNames = {hostpathRoot, namespace, crName, podName};
            String hostPath = String.join("/", pathNames);
            labelMap.put(pvSelectorLabel, pvName);
            return new PersistentVolumeBuilder().withNewMetadata().withName(pvName).withLabels(labelMap).endMetadata()
                    .withNewSpec().withStorageClassName(CSIUtil.getStorageClassNameOfHostpath()).withCapacity(Collections.singletonMap("storage", getQuantity(storage)))
                    .withAccessModes("ReadWriteOnce")
                    .withNewHostPath().withType("DirectoryOrCreate").withNewPath(hostPath).endHostPath()
                    .endSpec().build();

        }).collect(Collectors.toList());
    }

    public static List<PersistentVolumeClaim> provideCsiPVC(String pvcNamePrefix, String podNamePrefix, String namespace, String storage, Label[] labels, Integer size, Integer curMembers, String storageClassName) {
        Map<String, String> labelMap = labels == null ? null :
                Arrays.stream(labels).collect(Collectors.toMap(Label::getName, Label::getValue));
        return IntStream.range(curMembers, size + curMembers).mapToObj(i -> {
            String pvcName = getPvcName(pvcNamePrefix, podNamePrefix, i);
            return new PersistentVolumeClaimBuilder().withNewMetadata().withName(pvcName).withNamespace(namespace).withLabels(labelMap).endMetadata()
                    .withNewSpec().withStorageClassName(storageClassName)
                    .withAccessModes("ReadWriteOnce")
                    .withNewResources().withRequests(Collections.singletonMap("storage", getQuantity(storage))).endResources()
                    .endSpec().build();
        }).collect(Collectors.toList());
    }

    public static PersistentVolumeClaim provideSharedCsiPVC(String pvcName, String namespace, String storage, Map<String, String> labelMap, String storageClassName) {
        return new PersistentVolumeClaimBuilder().withNewMetadata().withName(pvcName).withNamespace(namespace).withLabels(labelMap).endMetadata()
                .withNewSpec().withStorageClassName(storageClassName)
                .withAccessModes("ReadWriteMany")
                .withNewResources().withRequests(Collections.singletonMap("storage", getQuantity(storage))).endResources()
                .endSpec().build();
    }

    private static Quantity getQuantity(String storage) {
        return storage == null ? null : new Quantity(storage);
    }

    private static String getPvcName(String pvcNamePrefix, String podNamePrefix, int i) {
//        String podName = podNamePrefix + "-" + i;
        String podName = StringUtils.isEmpty(podNamePrefix) ? "" + i : podNamePrefix + "-" + i;
        return pvcNamePrefix + "-" + podName;
    }

    public static List<PersistentVolumeClaim> provideHostpathPVC(String pvcNamePrefix, String podNamePrefix, String namespace, String storage, Label[] labels, List<PersistentVolume> pvs, String pvSelectorLabel, Integer curMembers) {
        Map<String, String> labelMap = labels == null ? null :
                Arrays.stream(labels).collect(Collectors.toMap(Label::getName, Label::getValue));
        return IntStream.range(curMembers, curMembers + pvs.size()).mapToObj(i -> {
            PersistentVolume pv = pvs.get(i - curMembers);
            String podName = StringUtils.isEmpty(podNamePrefix) ? "" + i : podNamePrefix + "-" + i;
            String pvcName = pvcNamePrefix + "-" + podName;
            return new PersistentVolumeClaimBuilder().withNewMetadata().withName(pvcName).withNamespace(namespace).withLabels(labelMap).endMetadata()
                    .withNewSpec().withStorageClassName(CSIUtil.getStorageClassNameOfHostpath())
                    .withAccessModes("ReadWriteOnce")
                    .withNewResources().withRequests(Collections.singletonMap("storage", getQuantity(storage))).endResources()
                    .withNewSelector().withMatchLabels(Collections.singletonMap(pvSelectorLabel, pv.getMetadata().getName())).endSelector()
                    .endSpec().build();
        }).collect(Collectors.toList());
    }

    public static long dfUsage(KubeClient kubeClient, String podName, String containerName, String namespace, String path) {
        String cmd = String.format("du -h -d 0 %s | awk '{print $1}'", path);
        try {
            String data = kubeClient.execCmd(namespace, podName, containerName, "sh", "-c", cmd);
            return MetricUtil.getLongValue(data);
        } catch (Exception e) {
            log.error("", e);
        }
        return 0;
    }

    // Filesystem                Size      Used Available Capacity Mounted on
    ///dev/topolvm/eef7a759-1f80-4cc6-864c-dc43096cf47b                                   3.9G    409.0M      3.5G  10% /data
    public static long dfAvail(KubeClient kubeClient, String podName, String containerName, String namespace, String backupRootPath)  {
        String cmd = String.format("df -PH %s | awk '{print $4}' | tail -n +2", backupRootPath); // hostpath volume return disk stat of host
        try {
            String data = kubeClient.execCmd(namespace, podName.trim(), containerName, "sh", "-c", cmd);
            return MetricUtil.getLongValue(data);
        } catch (Exception e) {
            log.error("", e);
        }
        return 0;
    }

    // url 同FtpUtils.getURL
    // absoluteFileNames 需为/backup文件夹下的相对文件名, note: 目前仅pg ftp脚本改版
    public static void uploadFileToFtp(KubeClient kubeClient, String url, String namespace, String podName, String ftpBackupPath, String ... absoluteFileNames) {
        // ftp-upload.sh [url] [source] [remotePath]
        Arrays.stream(absoluteFileNames).parallel()
                .forEach(relativeFilename -> {
                    if (StringUtils.isNotEmpty(relativeFilename)) {
                        String cmd = String.format("bash /scripts/ftp-upload.sh %s %s %s", url, relativeFilename, ftpBackupPath);
                        try {
                            kubeClient.execCmd(namespace, podName, "ftp", true, 0, "sh", "-c", cmd);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                });
    }

    public static Boolean describeContainerReady(KubeClient kubeClient, String containerName, String namespace, String podName) {
        Pod pod = kubeClient.getPod(namespace, podName);
        List<ContainerStatus> containerStatuses = pod.getStatus().getContainerStatuses();
        ContainerStatus status = containerStatuses.stream().filter(c -> c.getName().equals(containerName)).findAny().orElseThrow(()->new RuntimeException("container not found"));
        return status.getReady();
    }

    public static Secret backupResource(Secret secret) {
        Secret backup = new Secret();
        backup.setMetadata(new ObjectMetaBuilder()
                .withOwnerReferences(secret.getMetadata().getOwnerReferences())
                .withName(secret.getMetadata().getName())
                .withNamespace(secret.getMetadata().getNamespace())
                .build());
        backup.setData(new HashMap<>(secret.getData()));
        backup.setType(secret.getType());
        return backup;
    }

    public static boolean validateStsStatus(StatefulSet sts, String oldRevision) {
        if (sts == null) return false;
        StatefulSetStatus status = sts.getStatus();
        if (status.getUpdatedReplicas() == status.getReplicas() && status.getReadyReplicas() == status.getReplicas()) {
            if (!sts.getMetadata().getResourceVersion().equals(oldRevision)) {
                return true;
            }
        }
        return false;
    }

    public static String getCurrentRevision(String name, String namespace, KubeClient kubeClient) {
        // throw KubernetesClientException if not found
        return kubeClient.getStatefulSet(name, namespace).getMetadata().getResourceVersion();
    }

    public static PersistentVolumeClaim provideSharedCsiPVC(String pvcNamePrefix, String podNamePrefix, String namespace, String storage, Label[] labels, String storageClassName) {
        Map<String, String> labelMap = labels == null ? null :
                Arrays.stream(labels).collect(Collectors.toMap(Label::getName, Label::getValue));
        String pvcName = MongoUtil.getSharedBackupPVCName(podNamePrefix, pvcNamePrefix);
        return new PersistentVolumeClaimBuilder().withNewMetadata().withName(pvcName).withNamespace(namespace).withLabels(labelMap).endMetadata()
                .withNewSpec().withStorageClassName(storageClassName)
                .withAccessModes("ReadWriteMany")
                .withNewResources().withRequests(Collections.singletonMap("storage", getQuantity(storage))).endResources()
                .endSpec().build();
    }

    public static String buildServiceYaml(
            String namespace, String serviceType, String serviceName,
            Integer nodePort, int dbPort, String endpointName, Map<String, String> labelMap,
            Map<String, String> annotationMap, Map<String, String> selectorLabelMap, Map<String, Object> additionalMap) {
        return YamlEngine.marshal(
                new ServiceBuilder().withMetadata(
                                new ObjectMetaBuilder()
                                        .withName(serviceName)
                                        .withNamespace(namespace)
                                        .withLabels(labelMap)
                                        .withAnnotations(annotationMap).build())
                        .withSpec(
                                new ServiceSpecBuilder()
                                        .withType(serviceType)
                                        .withSelector(selectorLabelMap)
                                        .withPorts(new ServicePortBuilder()
                                                .withPort(dbPort).withName(endpointName).withNodePort(nodePort).build())
                                        .addToAdditionalProperties(additionalMap)
                                        .build())
                        .build());
    }
}
