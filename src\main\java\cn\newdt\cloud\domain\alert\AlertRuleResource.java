package cn.newdt.cloud.domain.alert;

import lombok.ToString;

import java.time.LocalDateTime;

@ToString
public class AlertRuleResource {
    private Integer id;

    private Integer ruleSetId;

    private Integer kubeId;

    // (multiaz): resourceId(logicId) + kubeId 对应唯一的cloudApp实例
    private Integer resourceId;

    /**
     * @deprecated 合并组件弃用
     */
    private Integer primaryResourceId;

    private String resourceName;

    private String resourceType;

    private String resourceNamespace;

    private String checksum;

    private LocalDateTime updateTime;

    private LocalDateTime syncTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getRuleSetId() {
        return ruleSetId;
    }

    public void setRuleSetId(Integer ruleSetId) {
        this.ruleSetId = ruleSetId;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getResourceNamespace() {
        return resourceNamespace;
    }

    public void setResourceNamespace(String resourceNamespace) {
        this.resourceNamespace = resourceNamespace;
    }

    public String getChecksum() {
        return checksum;
    }

    public void setChecksum(String checksum) {
        this.checksum = checksum;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public LocalDateTime getSyncTime() {
        return syncTime;
    }

    public void setSyncTime(LocalDateTime syncTime) {
        this.syncTime = syncTime;
    }

    public Integer getKubeId() {
        return kubeId;
    }

    public void setKubeId(Integer kubeId) {
        this.kubeId = kubeId;
    }

    public Integer getResourceId() {
        return resourceId;
    }

    public void setResourceId(Integer resourceId) {
        this.resourceId = resourceId;
    }

    public Integer getPrimaryResourceId() {
        return this.primaryResourceId;
    }
    public void setPrimaryResourceId(Integer primaryResourceId) {
        this.primaryResourceId = primaryResourceId;
    }
}