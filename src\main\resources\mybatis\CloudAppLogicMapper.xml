<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.newdt.cloud.mapper.CloudAppLogicMapper">
  <resultMap id="CloudAppLogicResultMap" type="cn.newdt.cloud.domain.CloudAppLogic">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="deploy_type" jdbcType="VARCHAR" property="deployType" />
    <result column="primary_kube_id" jdbcType="INTEGER" property="primaryKubeId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="cr_name" jdbcType="VARCHAR" property="crName" />
    <result column="kind" jdbcType="VARCHAR" property="kind" />
    <result column="arch" jdbcType="VARCHAR" property="arch" />
    <result column="owner_user" jdbcType="INTEGER" property="ownerUser" />
    <result column="owner_name" jdbcType="VARCHAR" property="ownerName" />
    <result column="owner_tenant" jdbcType="INTEGER" property="ownerTenant" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="members" jdbcType="INTEGER" property="members" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="namespace" jdbcType="VARCHAR" property="namespace" />
    <result column="write_port" jdbcType="INTEGER" property="writePort" />
    <result column="read_port" jdbcType="INTEGER" property="readPort" />
    <result column="external_id" jdbcType="INTEGER" property="externalId" />
    <result column="scheduler_id" jdbcType="INTEGER" property="kubeSchedulerId" />
    <result column="scheduler_name" jdbcType="VARCHAR" property="kubeSchedulerName" />
    <result column="app_system_name" jdbcType="VARCHAR" property="appSystemName" />
  </resultMap>
  <sql id="Base_Column_List">
    ${schema}.${table}.id, deploy_type, primary_kube_id, ${schema}.${table}.name, cr_name, kind, arch, owner_user, owner_name, owner_tenant,
    ${schema}.${table}.create_time, ${schema}.${table}.update_time, is_deleted, members, version, namespace, write_port, read_port, external_id, scheduler_id, app_system_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="CloudAppLogicResultMap">
    select
    <include refid="Base_Column_List" />, cks.name as scheduler_name
    from ${schema}.${table}
    left join ${schema}.${cloud_kube_scheduler} cks on cks.id = ${table}.scheduler_id
    where ${schema}.${table}.id = #{id,jdbcType=INTEGER}
  </select>
  <insert id="insert" parameterType="cn.newdt.cloud.domain.CloudAppLogic" useGeneratedKeys="true" keyProperty="app.id">
    insert into ${schema}.${table} (deploy_type, primary_kube_id,
      name, cr_name, kind, arch, owner_user,
      owner_name, owner_tenant, create_time,
      update_time, is_deleted, members,
      version, namespace, write_port,
      read_port, external_id, scheduler_id, app_system_name)
    values (#{app.deployType,jdbcType=VARCHAR}, #{app.primaryKubeId,jdbcType=INTEGER},
    #{app.name,jdbcType=VARCHAR}, #{app.crName,jdbcType=VARCHAR}, #{app.kind,jdbcType=VARCHAR}, #{app.arch,jdbcType=VARCHAR},
    #{app.ownerUser,jdbcType=INTEGER}, #{app.ownerName,jdbcType=VARCHAR}, #{app.ownerTenant,jdbcType=INTEGER},
    #{app.createTime,jdbcType=TIMESTAMP}, #{app.updateTime,jdbcType=TIMESTAMP}, #{app.isDeleted,jdbcType=TINYINT},
    #{app.members,jdbcType=INTEGER}, #{app.version,jdbcType=VARCHAR}, #{app.namespace,jdbcType=VARCHAR},
    #{app.writePort,jdbcType=INTEGER}, #{app.readPort,jdbcType=INTEGER}, #{app.externalId,jdbcType=INTEGER},
    #{app.kubeSchedulerId,jdbcType=INTEGER}, #{app.appSystemName,jdbcType=VARCHAR})
  </insert>

  <insert id="insertSelective" parameterType="cn.newdt.cloud.domain.CloudAppLogic">
    insert into cloud_app_logic
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deployType != null">
        deploy_type,
      </if>
      <if test="primaryKubeId != null">
        primary_kube_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="crName != null">
        cr_name,
      </if>
      <if test="kind != null">
        kind,
      </if>
      <if test="arch != null">
        arch,
      </if>
      <if test="ownerUser != null">
        owner_user,
      </if>
      <if test="ownerName != null">
        owner_name,
      </if>
      <if test="ownerTenant != null">
        owner_tenant,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        is_deleted,
      </if>
      <if test="members != null">
        members,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="namespace != null">
        namespace,
      </if>
      <if test="writePort != null">
        write_port,
      </if>
      <if test="readPort != null">
        read_port,
      </if>
      <if test="kubeSchedulerId != null">
        scheduler_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deployType != null">
        #{deployType,jdbcType=VARCHAR},
      </if>
      <if test="primaryKubeId != null">
        #{primaryKubeId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="crName != null">
        #{crName,jdbcType=VARCHAR},
      </if>
      <if test="kind != null">
        #{kind,jdbcType=VARCHAR},
      </if>
      <if test="arch != null">
        #{arch,jdbcType=VARCHAR},
      </if>
      <if test="ownerUser != null">
        #{ownerUser,jdbcType=INTEGER},
      </if>
      <if test="ownerName != null">
        #{ownerName,jdbcType=VARCHAR},
      </if>
      <if test="ownerTenant != null">
        #{ownerTenant,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="members != null">
        #{members,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="namespace != null">
        #{namespace,jdbcType=VARCHAR},
      </if>
      <if test="writePort != null">
        #{writePort,jdbcType=INTEGER},
      </if>
      <if test="readPort != null">
        #{readPort,jdbcType=INTEGER},
      </if>
      <if test="kubeSchedulerId != null">
        #{kubeSchedulerId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.newdt.cloud.domain.CloudAppLogic">
    update ${schema}.${table}
    <set>
      <if test="app.deployType != null">
        deploy_type = #{app.deployType,jdbcType=VARCHAR},
      </if>
      <if test="app.primaryKubeId != null">
        primary_kube_id = #{app.primaryKubeId,jdbcType=INTEGER},
      </if>
      <if test="app.name != null">
        name = #{app.name,jdbcType=VARCHAR},
      </if>
      <if test="app.crName != null">
        cr_name = #{app.crName,jdbcType=VARCHAR},
      </if>
      <if test="app.kind != null">
        kind = #{app.kind,jdbcType=VARCHAR},
      </if>
      <if test="app.arch != null">
        arch = #{app.arch,jdbcType=VARCHAR},
      </if>
      <if test="app.ownerUser != null">
        owner_user = #{app.ownerUser,jdbcType=INTEGER},
      </if>
      <if test="app.ownerName != null">
        owner_name = #{app.ownerName,jdbcType=VARCHAR},
      </if>
      <if test="app.ownerTenant != null">
        owner_tenant = #{app.ownerTenant,jdbcType=INTEGER},
      </if>
      <if test="app.createTime != null">
        create_time = #{app.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="app.updateTime != null">
        update_time = #{app.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="app.deleted != null">
        is_deleted = #{app.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="app.members != null">
        members = #{app.members,jdbcType=INTEGER},
      </if>
      <if test="app.version != null">
        version = #{app.version,jdbcType=VARCHAR},
      </if>
      <if test="app.namespace != null">
        namespace = #{app.namespace,jdbcType=VARCHAR},
      </if>
      <if test="app.writePort != null">
        write_port = #{app.writePort,jdbcType=INTEGER},
      </if>
      <if test="app.readPort != null">
        read_port = #{app.readPort,jdbcType=INTEGER},
      </if>
      <if test="app.externalId != null">
        external_id = #{app.externalId,jdbcType=INTEGER}
      </if>
    </set>
    where id = #{app.id,jdbcType=INTEGER}
  </update>

  <select id="selectByMultiColumn" resultMap="CloudAppLogicResultMap">
    select <include refid="Base_Column_List" />, cks.name as scheduler_name
    from ${schema}.${table}
    left join ${schema}.${cloud_kube_scheduler} cks on cks.id = ${table}.scheduler_id
    <where>
      <if test="commonSearchList != null and commonSearchList.size > 0">
        and
        <include refid="cn.newdt.cloud.mapper.CommonMapper.searchMulti" />
      </if>
    </where>
    <include refid="cn.newdt.commons.mapper.Common.sort" />
  </select>

  <resultMap id="RecycledAppMap" type="cn.newdt.cloud.vo.RecycleLogicAppVO">
    <id column="id" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="cr_name" jdbcType="VARCHAR" property="crName" />
    <result column="namespace" jdbcType="VARCHAR" property="namespace" />
    <result column="kind" jdbcType="VARCHAR" property="kind" />
    <result column="arch" jdbcType="VARCHAR" property="arch" />
    <result column="recycle_time" property="recycleTime"/>
    <result column="recycle_his_id" property="recycleChangeHisId"/>

    <collection property="apps" ofType="cn.newdt.cloud.vo.RecycleLogicAppVO$AppVO" javaType="ArrayList">
      <id column="app_id" property="id"/>
      <result column="cpu" property="cpu"/>
      <result column="memory" property="memory"/>
      <result column="disk" property="disk"/>
      <result column="members" property="members"/>
<!--      <result column="crYaml" property="crYaml"/>-->
<!--      <result column="crRunYaml" property="crRunYaml"/>-->
    </collection>

  </resultMap>

  <select id="selectByMapAndJoin" resultMap="RecycledAppMap">
    SELECT apl.id , apl.name, apl.cr_name, apl.namespace, apl.kind,apl.arch, ap.id AS app_id, ap.cr AS crYaml, ap.cr_run as
    crRunYaml, ap.cr,ap.cpu,ap.memory,ap.disk,ap.members,
    recycle_time, recycle_his_id
    FROM (
    select in_lap.*, g_his.update_time AS recycle_time, his_id as recycle_his_id, g_his.action from ${schema}.${table} in_lap
    LEFT JOIN (
    SELECT app_logic_id, MAX(update_time) as update_time, action, max(id) as his_id
    FROM ${schema}.${table_cloud_resource_change_his} his WHERE ACTION = 'Delete' GROUP BY app_logic_id,action) g_his ON in_lap.id = g_his.app_logic_id
    WHERE in_lap.is_deleted = 2
    <if test="param.ownerUser!=null">and in_lap.owner_user = #{param.ownerUser}</if>
    <if test="param.ownerTenant!=null and param.ownerTenant instanceof java.lang.Integer">and in_lap.owner_tenant= #{param.ownerTenant}</if>
    <if test="param.ownerTenant!=null and param.ownerTenant instanceof java.util.Collection">and in_lap.owner_tenant in (
      <foreach collection="param.ownerTenant" item="ownerTenant" separator=",">
        #{ownerTenant}
      </foreach>
      )
    </if>

    <if test="param.kind !=null">and in_lap.kind = #{param.kind}</if>
    <if test="param.arch !=null">and in_lap.arch = #{param.arch}</if>
    <if test="param.name !=null">and in_lap.name like CONCAT(#{param.name},'%') </if>
    <choose>
      <when test="param.order != null and param.orderCol != null">
        order by ${param.orderCol} ${param.order}
      </when>
      <otherwise>
        order by recycle_time desc
      </otherwise>
    </choose>
    <if test="param.offset != null and param.pageSize != 0">
      limit ${param.pageSize} offset ${param.offset}
    </if>
    ) apl
    LEFT JOIN ${schema}.${table_cloud_app} ap ON apl.id = ap.logic_id
    <choose>
      <when test="param.order != null and param.orderCol != null">
        order by ${param.orderCol} ${param.order}
      </when>
      <otherwise>
        order by recycle_time desc
      </otherwise>
    </choose>
  </select>

  <select id="countSelectByMapAndJoin" resultType="java.lang.Long">
    select count(*) from ${schema}.${table} in_lap
    LEFT JOIN (
    SELECT app_logic_id, MAX(update_time) as update_time, action
    FROM ${schema}.${table_cloud_resource_change_his} his WHERE ACTION = 'Delete' GROUP BY app_logic_id,action) g_his ON in_lap.id = g_his.app_logic_id
    WHERE in_lap.is_deleted = 2
    <if test="param.ownerUser!=null">and in_lap.owner_user = #{param.ownerUser}</if>
    <if test="param.ownerTenant!=null and param.ownerTenant instanceof java.lang.Integer">and in_lap.owner_tenant= #{param.ownerTenant}</if>
    <if test="param.ownerTenant!=null and param.ownerTenant instanceof java.util.Collection">and in_lap.owner_tenant in (
      <foreach collection="param.ownerTenant" item="ownerTenant" separator=",">
        #{ownerTenant}
      </foreach>
      )
    </if>
    <if test="param.kind !=null">and in_lap.kind = #{param.kind}</if>
    <if test="param.arch !=null">and in_lap.arch = #{param.arch}</if>
  </select>

  <select id="countSelectByMultiColumn" resultType="long">
    select count(*)
    from ${schema}.${table}
    <where>
      <if test="commonSearchList != null and commonSearchList.size > 0">
        and
        <include refid="cn.newdt.cloud.mapper.CommonMapper.searchMulti" />
      </if>
      <if test="extra.excludeKinds != null and extra.excludeKinds.size > 0">
        and kind not in
        <foreach collection="extra.excludeKinds" item="item" separator="," open="(" close=")">
        #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="getAppSystemNameList" resultType="java.lang.String">
    /*NDTM*/select distinct app_system_name
            FROM ${schema}.${table}
            where namespace = #{namespace,jdbcType=VARCHAR}
  </select>

  <update id="updateAppSystemNameByPriId">
    update ${schema}.${table}
    set app_system_name = #{appSystemName,jdbcType=VARCHAR}
    where id = #{logicId,jdbcType=INTEGER}
  </update>
</mapper>