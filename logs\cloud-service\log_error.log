
2025-07-28 11:23:13.363       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 11:23:13.392       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 11:23:17.138       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 11:23:17.150       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 11:23:39.817       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 11:23:39.837       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 11:23:43.816       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 11:23:43.819       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 11:27:25.111       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 11:27:25.117       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 11:27:26.088       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 11:27:26.090       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 11:27:34.798       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 11:27:34.806       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 11:27:35.762       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 11:27:35.764       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 11:28:10.047       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 11:28:10.064       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 11:28:11.098       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 11:28:11.102       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 11:28:13.218       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 11:28:13.231       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 11:28:14.201       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 11:28:14.202       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 11:29:20.636       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 11:29:20.650       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 11:29:21.711       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 11:29:21.712       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 11:29:49.584       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 11:29:49.586       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 11:29:49.674       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 11:29:49.675       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 11:30:43.418       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 11:30:43.432       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 11:30:44.413       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 11:30:44.420       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 12:13:01.617       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:180
				MESSAGE:异常ID:e568d093-d90d-476c-bb2e-e71b5f5e5799, 请求地址:http://192.168.2.217:8300/cloudmanage/inspect
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: insert into cust1.cloud_inspect_task_result (result_id, task_id, rule_id,     resource, resource_type, result_status,     severity, create_time, update_time,     result_details)     values
### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:236)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at cn.newdt.commons.config.CustomMyBatisExceptionTranslator.translateExceptionIfPossible(CustomMyBatisExceptionTranslator.java:50)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446)
	at com.sun.proxy.$Proxy148.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:278)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:58)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59)
	at com.sun.proxy.$Proxy262.insertBatch(Unknown Source)
	at cn.newdt.cloud.service.inspect.InspectJobTaskService.savePlan(InspectJobTaskService.java:55)
	at cn.newdt.cloud.service.inspect.InspectJobTaskService$$FastClassBySpringCGLIB$$6a83bc3a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.inspect.InspectJobTaskService$$EnhancerBySpringCGLIB$$6b4ca428.savePlan(<generated>)
	at cn.newdt.cloud.service.inspect.InspectService.inspect(InspectService.java:66)
	at cn.newdt.cloud.service.inspect.InspectService.inspect(InspectService.java:222)
	at cn.newdt.cloud.web.InspectController.saveInspectPlan(InspectController.java:39)
	at cn.newdt.cloud.web.InspectController$$FastClassBySpringCGLIB$$e79221c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.InspectController$$EnhancerBySpringCGLIB$$7bb4f5d4.saveInspectPlan(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.filter.CloudManageFilter.doFilter(CloudManageFilter.java:35)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:95)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:960)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:388)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:46)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy310.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:198)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433)
	... 136 common frames omitted

2025-07-28 12:26:10.624       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:180
				MESSAGE:异常ID:f3206f3b-4eb3-454e-aaae-808d8c533d24, 请求地址:http://192.168.2.217:8300/cloudmanage/inspect
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: insert into cust1.cloud_inspect_task_result (result_id, task_id, rule_id,     resource, resource_type, result_status,     severity, create_time, update_time,     result_details)     values
### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:236)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at cn.newdt.commons.config.CustomMyBatisExceptionTranslator.translateExceptionIfPossible(CustomMyBatisExceptionTranslator.java:50)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446)
	at com.sun.proxy.$Proxy148.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:278)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:58)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59)
	at com.sun.proxy.$Proxy262.insertBatch(Unknown Source)
	at cn.newdt.cloud.service.inspect.InspectJobTaskService.savePlan(InspectJobTaskService.java:55)
	at cn.newdt.cloud.service.inspect.InspectJobTaskService$$FastClassBySpringCGLIB$$6a83bc3a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.inspect.InspectJobTaskService$$EnhancerBySpringCGLIB$$6b4ca428.savePlan(<generated>)
	at cn.newdt.cloud.service.inspect.InspectService.inspect(InspectService.java:66)
	at cn.newdt.cloud.service.inspect.InspectService.inspect(InspectService.java:222)
	at cn.newdt.cloud.web.InspectController.saveInspectPlan(InspectController.java:39)
	at cn.newdt.cloud.web.InspectController$$FastClassBySpringCGLIB$$e79221c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.InspectController$$EnhancerBySpringCGLIB$$7bb4f5d4.saveInspectPlan(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.filter.CloudManageFilter.doFilter(CloudManageFilter.java:35)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:95)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:960)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:388)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:46)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy310.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:198)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433)
	... 136 common frames omitted

2025-07-28 13:54:57.185       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 13:54:57.213       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 13:54:59.127       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 13:54:59.134       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 13:57:12.738       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:180
				MESSAGE:异常ID:deba32f2-7273-4a33-8e21-09e9463af5ac, 请求地址:http://192.168.2.217:8300/cloudmanage/inspect
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: insert into cust1.cloud_inspect_task_result (result_id, task_id, rule_id,     resource, resource_type, result_status,     severity, create_time, update_time,     result_details)     values
### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:236)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at cn.newdt.commons.config.CustomMyBatisExceptionTranslator.translateExceptionIfPossible(CustomMyBatisExceptionTranslator.java:50)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446)
	at com.sun.proxy.$Proxy148.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:278)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:58)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59)
	at com.sun.proxy.$Proxy262.insertBatch(Unknown Source)
	at cn.newdt.cloud.service.inspect.InspectJobTaskService.savePlan(InspectJobTaskService.java:55)
	at cn.newdt.cloud.service.inspect.InspectJobTaskService$$FastClassBySpringCGLIB$$6a83bc3a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.inspect.InspectJobTaskService$$EnhancerBySpringCGLIB$$a6b6a6c6.savePlan(<generated>)
	at cn.newdt.cloud.service.inspect.InspectService.inspect(InspectService.java:66)
	at cn.newdt.cloud.service.inspect.InspectService.inspect(InspectService.java:222)
	at cn.newdt.cloud.web.InspectController.saveInspectPlan(InspectController.java:39)
	at cn.newdt.cloud.web.InspectController$$FastClassBySpringCGLIB$$e79221c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.InspectController$$EnhancerBySpringCGLIB$$680c86b0.saveInspectPlan(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.filter.CloudManageFilter.doFilter(CloudManageFilter.java:35)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:95)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:960)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:388)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:46)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy310.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:198)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433)
	... 136 common frames omitted

2025-07-28 14:30:27.037       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:30:27.047       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:30:27.058       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:30:27.081       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:30:27.191       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:30:27.192       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:30:27.189       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:30:27.194       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:30:32.593       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:30:32.925       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:30:32.932       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:30:32.932       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:30:34.300       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:30:34.300       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:30:34.467       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:30:34.467       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:33:55.519       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:33:55.543       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:33:56.533       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:33:56.533       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:33:58.414       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:33:58.414       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:33:58.430       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:33:58.462       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:33:59.110       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:33:59.110       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:33:59.111       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:33:59.112       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:33:59.112       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:33:59.117       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:33:59.111       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:33:59.121       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:34:10.377       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:34:10.377       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:34:10.381       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:34:10.380       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:34:11.829       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:34:11.830       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:34:11.835       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:34:11.836       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:34:16.731       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:34:16.734       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:34:16.752       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:34:16.761       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:34:16.794       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:34:16.795       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:34:16.803       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:34:16.804       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:35:21.131       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:35:21.150       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:35:21.164       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:35:21.174       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:35:22.306       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:35:22.307       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:35:22.416       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:35:22.416       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:44:41.668       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:44:41.672       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:44:43.371       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:44:43.374       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:45:11.078       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:45:11.083       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:45:11.957       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:45:11.959       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:52:14.981       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertConfigSyncService  LINE:123
				MESSAGE:[alert config sync] failed
java.lang.NullPointerException: null
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.ensureChannelSecret(AlertConfigSyncService.java:276)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.syncConfig(AlertConfigSyncService.java:168)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.lambda$enqueueSyncTask$3(AlertConfigSyncService.java:119)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-28 14:52:29.116       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertConfigSyncService  LINE:123
				MESSAGE:[alert config sync] failed
java.lang.NullPointerException: null
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.ensureChannelSecret(AlertConfigSyncService.java:276)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.syncConfig(AlertConfigSyncService.java:168)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.lambda$enqueueSyncTask$3(AlertConfigSyncService.java:119)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-28 14:54:28.568       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:54:28.576       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:54:29.895       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:54:29.896       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:54:45.681       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:54:45.685       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:54:46.688       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:54:46.689       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:56:08.006       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:56:08.015       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:56:08.953       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:56:08.954       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:56:23.364       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:56:23.367       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:56:24.465       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:56:24.467       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:56:45.602       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertConfigSyncService  LINE:123
				MESSAGE:[alert config sync] failed
java.lang.NullPointerException: null
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.ensureChannelSecret(AlertConfigSyncService.java:276)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.syncConfig(AlertConfigSyncService.java:168)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.lambda$enqueueSyncTask$3(AlertConfigSyncService.java:119)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-28 14:56:58.188       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:56:58.395       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:56:59.697       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:56:59.699       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:59:05.921       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 14:59:05.931       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 14:59:06.003       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 14:59:06.004       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:02:21.604       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:02:21.613       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:02:21.661       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:02:21.662       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:02:26.499       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:02:26.506       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:02:26.601       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:02:26.603       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:02:43.403       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:02:43.403       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:02:43.404       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:02:43.413       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:02:43.467       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:02:43.469       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:02:43.477       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:02:43.479       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:03:10.638       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:03:10.644       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:03:10.644       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:03:10.649       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:03:10.692       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:03:10.694       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:03:10.708       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:03:10.708       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:04:05.700       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:04:05.705       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:04:05.705       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:04:05.716       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:04:07.328       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:04:07.330       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:04:07.351       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:04:07.352       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:04:34.299       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:04:34.305       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:04:34.314       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:04:34.327       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:04:34.372       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:04:34.373       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:04:34.396       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:04:34.397       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:18:48.791       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:18:48.791       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:18:48.795       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:18:48.798       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:18:48.844       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:18:48.845       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:18:48.854       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:18:48.854       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:19:00.866       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:19:00.865       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:19:00.870       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:19:00.870       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:19:01.871       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:19:01.872       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:19:01.957       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:19:01.958       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:20:02.481       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:20:02.720       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:20:02.727       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:20:02.766       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:20:02.787       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:20:02.805       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:20:02.834       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:20:02.836       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:21:24.496       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:21:24.506       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:21:24.508       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:21:24.510       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:21:25.943       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:21:25.944       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:21:25.976       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:21:25.977       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:22:24.171       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:22:24.171       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:22:24.178       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:22:24.178       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:22:24.237       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:22:24.239       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:22:24.242       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:22:24.245       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:22:26.596       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:22:26.605       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:22:26.614       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:22:26.618       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:22:28.001       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:22:28.002       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:22:28.037       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:22:28.037       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:30:45.186       LEVEL:ERROR ThreadId:
				POSITION:org.springframework.boot.diagnostics.LoggingFailureAnalysisReporter  LINE:40 
				MESSAGE:

***************************
APPLICATION FAILED TO START
***************************

Description:

Field alertApi in cn.newdt.cloud.service.inspect.InspectFunctionRuleDef required a bean of type 'cn.newdt.cloud.service.alertmanager.AlertApi' that could not be found.

The injection point has the following annotations:
	- @org.springframework.beans.factory.annotation.Autowired(required=true)


Action:

Consider defining a bean of type 'cn.newdt.cloud.service.alertmanager.AlertApi' in your configuration.


2025-07-28 15:34:48.272       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:34:48.300       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:34:49.920       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:34:49.921       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:34:51.176       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:34:51.184       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:34:52.097       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:34:52.099       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:35:53.039       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:35:53.041       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:35:53.822       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:35:53.822       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:35:56.047       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:35:56.053       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:35:56.912       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:35:56.913       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:36:34.768       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:36:34.777       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:36:34.786       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:36:34.797       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:36:34.844       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:36:34.844       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:36:34.845       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:36:34.845       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:49:19.740       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:49:19.742       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:49:19.752       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:49:19.752       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:49:19.811       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:49:19.811       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:49:19.812       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:49:19.812       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:49:26.609       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:49:26.630       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:49:26.761       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:49:26.791       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:49:27.705       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:49:27.706       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:49:27.965       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:49:27.967       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:49:38.701       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:49:38.716       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:49:39.642       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:49:39.643       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:49:45.687       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:49:45.691       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:49:46.572       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:49:46.574       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:51:00.977       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:51:00.992       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:51:01.849       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:51:01.850       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:51:14.268       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:51:14.272       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:51:15.205       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:51:15.207       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:52:13.456       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:52:13.463       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:52:14.442       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:52:14.443       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:53:30.493       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:53:30.496       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:53:31.406       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:53:31.408       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:53:57.731       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:53:57.737       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:53:57.737       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:53:57.744       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:53:57.807       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:53:57.808       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:53:57.813       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:53:57.814       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:54:02.035       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:54:02.050       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:54:02.050       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:54:02.050       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:54:03.069       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:54:03.070       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:54:03.116       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:54:03.116       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:55:44.057       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:55:44.079       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:55:45.041       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:55:45.042       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:55:49.784       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 15:55:49.824       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 15:55:50.920       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 15:55:50.921       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:07:23.724       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:07:23.726       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:07:24.781       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:07:24.783       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:07:29.961       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:07:29.962       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:07:30.898       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:07:30.899       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:09:32.467       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:09:32.467       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:09:32.473       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:09:32.474       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:09:32.567       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:09:32.568       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:09:32.573       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:09:32.575       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:09:34.381       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:09:34.397       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:09:34.398       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:09:34.404       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:09:36.021       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:09:36.022       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:09:36.122       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:09:36.123       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:09:40.171       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:09:40.177       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:09:41.105       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:09:41.105       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:09:42.638       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:09:42.653       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:09:43.533       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:09:43.534       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:10:16.543       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:10:16.553       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:10:16.773       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:10:16.773       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:10:17.473       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:10:17.473       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:10:17.762       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:10:17.765       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:11:43.213       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:11:43.215       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:11:44.084       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:11:44.085       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:11:45.770       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:11:45.777       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:11:46.610       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:11:46.610       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:11:47.420       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:11:47.429       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:11:48.028       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:11:48.029       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:12:08.911       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:12:08.915       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:12:09.890       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:12:09.892       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:12:15.042       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:12:15.046       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:12:15.923       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:12:15.923       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:12:25.292       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:12:25.296       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:12:26.158       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:12:26.160       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:12:27.004       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:12:27.012       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:12:27.445       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:12:27.446       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:12:28.632       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:12:28.638       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:12:29.487       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:12:29.489       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:14:26.356       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:14:26.360       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:14:27.193       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:14:27.194       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:16:28.802       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:16:28.818       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:16:28.818       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:16:28.837       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:16:28.896       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:16:28.899       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:16:28.898       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:16:28.900       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:19:43.382       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:19:43.404       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:19:44.961       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:19:44.962       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:20:45.554       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:20:45.558       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:20:47.284       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:20:47.285       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:21:48.570       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:21:48.570       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:21:49.960       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:21:49.961       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:22:33.821       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:22:33.827       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:22:34.859       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:22:34.861       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:23:07.712       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:23:07.713       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:23:07.715       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:23:07.716       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:23:07.764       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:23:07.765       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:23:07.765       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:23:07.765       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:26:27.013       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:26:27.020       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:26:27.022       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:26:27.026       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:26:27.090       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:26:27.092       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:26:27.092       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:26:27.092       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:26:31.288       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:26:31.300       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:26:31.315       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:26:31.322       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:26:32.369       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:26:32.370       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:26:32.385       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:26:32.385       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:27:09.503       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:27:09.507       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:27:09.510       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:27:09.513       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:27:09.553       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:27:09.553       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:27:09.555       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:27:09.555       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:27:10.737       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:27:10.741       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:27:10.744       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:27:10.749       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:27:11.826       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:27:11.827       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:27:11.877       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:27:11.878       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:27:33.647       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:27:33.655       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:27:34.471       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:27:34.472       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:29:22.510       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:29:22.540       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:29:23.415       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:29:23.416       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:29:28.742       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:29:28.744       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:29:29.569       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:29:29.571       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:36:25.232       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:36:25.239       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:36:25.240       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:36:25.244       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:36:27.118       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:36:27.119       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:36:27.159       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:36:27.161       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:42:19.617       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:42:19.629       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:42:19.642       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:42:19.647       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:42:19.680       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:42:19.681       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:42:19.698       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:42:19.698       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:43:09.159       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:43:09.165       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:43:09.166       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:43:09.184       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:43:09.192       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:43:09.196       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:43:09.263       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:43:09.264       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:43:09.280       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:43:09.280       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:43:10.165       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:43:10.165       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:43:10.172       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:43:10.172       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:43:10.205       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:43:10.205       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:43:10.207       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:43:10.207       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:43:10.281       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:43:10.282       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:44:05.208       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:44:05.208       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:44:05.215       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:44:05.215       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:44:05.236       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:44:05.239       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:44:05.272       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:44:05.272       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:44:05.274       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:44:05.274       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:44:06.221       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:44:06.221       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:44:06.228       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:44:06.228       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:44:06.246       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:44:06.247       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:44:06.292       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:44:06.293       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:44:06.293       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:44:06.293       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:45:08.640       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:45:08.649       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:45:08.675       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:45:08.685       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:45:08.698       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:45:08.702       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:45:08.703       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:45:08.720       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:45:08.724       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:45:08.724       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:45:09.593       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:45:09.595       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:45:09.697       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:45:09.697       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:45:09.699       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:45:09.702       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:45:09.727       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:45:09.727       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:45:09.728       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:45:09.728       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:48:00.718       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:48:00.718       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:48:00.718       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:48:00.722       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:48:00.747       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:48:00.753       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:48:00.815       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:48:00.813       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:48:00.815       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:48:00.815       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:48:01.738       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:48:01.739       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:48:01.762       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:48:01.763       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:48:01.766       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:48:01.766       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:48:01.795       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:48:01.796       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:48:01.797       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:48:01.798       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:48:05.114       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:48:05.121       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:48:05.130       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:48:05.138       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:48:06.322       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:48:06.324       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:48:06.324       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:48:06.325       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:48:43.526       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:48:43.529       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:48:44.477       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:48:44.478       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:49:09.433       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:49:09.438       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:49:10.359       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:49:10.360       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:49:43.064       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:49:43.068       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:49:43.071       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:49:43.073       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:49:43.079       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:49:43.093       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:49:43.135       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:49:43.135       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:49:43.135       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:49:43.137       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:49:44.093       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:49:44.093       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:49:44.105       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:49:44.105       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:49:44.143       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:49:44.144       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:49:44.149       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:49:44.150       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:49:44.189       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:49:44.189       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:50:32.431       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:50:32.445       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:50:32.468       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:50:32.473       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:50:32.482       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:50:32.494       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:50:32.494       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:50:32.495       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:50:32.496       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:50:32.510       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:50:33.449       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:50:33.454       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:50:33.457       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:50:33.457       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:50:33.494       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:50:33.494       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:50:33.495       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:50:33.494       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:50:33.507       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:50:33.508       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:50:37.237       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:50:37.239       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:50:37.251       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:50:37.254       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:50:38.211       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:50:38.212       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:50:38.332       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:50:38.332       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:51:40.345       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:51:40.371       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:51:40.379       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:51:40.393       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:51:40.422       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:51:40.429       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:51:40.437       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:51:40.437       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:51:40.438       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:51:40.438       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:51:41.387       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:51:41.401       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:51:41.401       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:51:41.411       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:51:41.420       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:51:41.421       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:51:41.463       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:51:41.463       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:51:41.471       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:51:41.471       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:54:36.427       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:54:36.427       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:54:36.431       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:54:36.433       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:54:37.437       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:54:37.438       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:54:37.532       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:54:37.532       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:57:25.540       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:57:25.554       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:57:25.554       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:57:25.614       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:57:25.623       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:57:25.657       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:57:25.786       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:57:25.788       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:57:25.823       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:57:25.829       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:57:26.546       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:57:26.549       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:57:26.548       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:57:26.552       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:57:26.584       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:57:26.584       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:57:26.584       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:57:26.585       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:57:26.853       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:57:26.854       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:59:05.650       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:59:05.662       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:59:05.663       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:59:05.685       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:59:05.689       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:59:05.726       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:59:05.763       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:59:05.764       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:59:05.815       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:59:05.817       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:59:06.671       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:59:06.673       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 16:59:06.675       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:59:06.678       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 16:59:06.717       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:59:06.718       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:59:06.718       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:59:06.718       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:59:06.731       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 16:59:06.732       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:00:07.448       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:00:07.463       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:00:08.270       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:00:08.272       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:08:02.075       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:08:02.075       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:08:02.083       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:08:02.114       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:08:02.115       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:08:02.117       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:08:02.191       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:08:02.199       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:08:02.192       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:08:02.217       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:08:03.094       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:08:03.106       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:08:03.106       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:08:03.106       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:08:03.171       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:08:03.171       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:08:03.172       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:08:03.172       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:08:03.316       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:08:03.318       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:09:30.757       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:09:30.757       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:09:30.774       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:09:30.775       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:09:30.779       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:09:30.786       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:09:30.807       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:09:30.808       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:09:30.822       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:09:30.824       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:09:31.789       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:09:31.793       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:09:31.792       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:09:31.816       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:09:31.842       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:09:31.843       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:09:31.852       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:09:31.852       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:09:32.218       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:09:32.218       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:10:01.526       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:10:01.532       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:10:01.532       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:10:01.534       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:10:01.564       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:10:01.565       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:10:01.653       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:10:01.663       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:10:01.686       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:10:01.688       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:10:02.138       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:10:02.138       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:10:02.518       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:10:02.527       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:10:02.537       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:10:02.552       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:10:02.593       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:10:02.593       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:10:02.593       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:10:02.593       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:12:31.433       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:12:31.436       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:12:31.987       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:12:31.992       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:16:04.647       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:16:04.647       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:16:04.660       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:16:04.660       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:16:04.667       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:16:04.676       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:16:04.736       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:16:04.736       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:16:04.757       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:16:04.758       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:16:04.824       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:16:04.824       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:16:06.362       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:16:06.364       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:16:06.382       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:16:06.382       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:16:06.410       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:16:06.410       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:16:06.412       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:16:06.412       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:18:11.976       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:18:11.976       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:18:11.978       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:18:11.984       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:18:12.041       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:18:12.042       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:18:12.054       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:18:12.056       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:43:48.521       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:43:48.521       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:43:48.521       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:43:48.522       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:43:48.604       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:43:48.604       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:43:48.605       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:43:48.605       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:46:22.198       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:46:22.198       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:46:22.219       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:46:22.225       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:46:23.008       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:46:23.020       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:46:23.163       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:46:23.164       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:46:23.216       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:46:23.217       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:46:23.281       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:46:23.284       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:46:24.085       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:46:24.086       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:46:24.101       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:46:24.102       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:46:24.148       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:46:24.148       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:46:24.149       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:46:24.149       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:53:29.031       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:53:29.031       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:53:29.038       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:53:29.065       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:53:29.080       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:53:29.097       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:53:29.137       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:53:29.139       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:53:29.140       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:53:29.148       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:53:29.201       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:53:29.203       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:53:30.042       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:53:30.044       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:53:30.051       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:53:30.052       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:53:30.086       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:53:30.087       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:53:30.105       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:53:30.105       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:53:30.940       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:53:30.949       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:53:30.948       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:53:30.951       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:53:31.012       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:53:31.012       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:53:31.013       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:53:31.013       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:53:40.195       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-28 17:53:40.200       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-28 17:53:40.295       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 17:53:40.296       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-28 18:06:16.981       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:180
				MESSAGE:异常ID:d540fc9e-37f6-4571-acf4-241bd9df7934, 请求地址:http://192.168.2.217:8300/cloudmanage/inspect
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: insert into cust1.cloud_inspect_task_result (result_id, task_id, rule_id,     resource, resource_type, result_status,     severity, create_time, update_time,     result_details)     values
### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:236)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at cn.newdt.commons.config.CustomMyBatisExceptionTranslator.translateExceptionIfPossible(CustomMyBatisExceptionTranslator.java:50)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446)
	at com.sun.proxy.$Proxy148.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:278)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:58)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59)
	at com.sun.proxy.$Proxy262.insertBatch(Unknown Source)
	at cn.newdt.cloud.service.inspect.InspectJobTaskService.savePlan(InspectJobTaskService.java:55)
	at cn.newdt.cloud.service.inspect.InspectJobTaskService$$FastClassBySpringCGLIB$$6a83bc3a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.inspect.InspectJobTaskService$$EnhancerBySpringCGLIB$$6b4ca428.savePlan(<generated>)
	at cn.newdt.cloud.service.inspect.InspectService.inspect(InspectService.java:68)
	at cn.newdt.cloud.service.inspect.InspectService.inspect(InspectService.java:224)
	at cn.newdt.cloud.web.InspectController.saveInspectPlan(InspectController.java:39)
	at cn.newdt.cloud.web.InspectController$$FastClassBySpringCGLIB$$e79221c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.InspectController$$EnhancerBySpringCGLIB$$87e867c6.saveInspectPlan(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.filter.CloudManageFilter.doFilter(CloudManageFilter.java:35)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '' at line 5
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:95)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:960)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:388)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:46)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy310.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:198)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:185)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433)
	... 136 common frames omitted
