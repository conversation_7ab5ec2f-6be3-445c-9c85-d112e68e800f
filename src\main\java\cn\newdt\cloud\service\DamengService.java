package cn.newdt.cloud.service;

import cn.newdt.cloud.config.CloudRequestContext;
import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.domain.dmp.BinlogBackupHis;
import cn.newdt.cloud.dto.*;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.sched.DamengSwitchMasterWatcher;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.service.sched.impl.*;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.*;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.SymmetricEncryptionUtil;
import cn.newdt.commons.utils.UserUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.shindata.cloud.dameng.v1.Dameng;
import com.shindata.cloud.dameng.v1.DamengSpec;
import com.shindata.common.spec.Entries;
import com.shindata.common.spec.Secrets;
import com.shindata.common.spec.entries.Services;
import com.shindata.common.spec.secrets.Users;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.api.model.apps.StatefulSet;
import io.fabric8.kubernetes.client.CustomResource;
import io.fabric8.kubernetes.client.utils.Serialization;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.ActionEnum.MIGRATE;
import static cn.newdt.cloud.constant.CloudAppConstant.LABEL_NODE_NAME;
import static cn.newdt.cloud.constant.CloudAppConstant.Operator.IN;
import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgCategory.OPERATOR_CONFIG;

@Service
public class DamengService extends DefaultAppKindService<Dameng> implements StartStopOperation, SwitchMasterOperation, MigrateOperation, ServiceManageOperation {

    @Autowired
    private CloudDatabaseUserService databaseUserService;
    @Autowired
    private OperationUtil operationUtil;

    @Override
    public void turnOnMaintenance(Integer appId) {

    }

    public static class Name {

        public static final String MOUNT_POINT = "/opt/dmdbms/data/mnt";
        public static final String BACKUP_STORE_PATH_FORMAT = "DAMENG/${namespace}/${crName}";
        public static final String CONTAINER_DM = AppKind.Dameng.getContainerName();
        public static final String DM_DATA_VOL_NAME = "dm-data";
        public static final String ENTRY_DATABASE = "database";
        public static final String ENTRY_MONITOR = "monitor";
        public static final String SERVICE_WRITE = "write";
        public static final String SERVICE_READ = "read";
        public static final String DBA_USER = "SYSDBA";
        public static final String DAMENG_EXPORTER_CONFIG = "dameng_exporter.config";
        public static String exporterPortName = "exporter";
        public static int exporterPort = 9200;
        public static final String DAMENG_FILEBEAT = "operator-filebeat-configmap";

        public static final String DMP_VERSION_8 = "DM Database Server x64 V8";

        public static String getDbaSecretName(String crName) {
            return String.format("dm-%s-dba-secret", crName);
        }

        public static String getMonSecretName(String crName, String username) {// todo username format not fit in secret name convention
            return String.format("dm-%s-%s-secret", crName, "mon");
        }

        public static String getDmpSecretName(String crName) {
            return String.format("dm-%s-dmp-secret", crName);
        }

        public static String getBackupStorePathFormatted(String namespace, String crname) {
            return StringSubstitutor.replace(BACKUP_STORE_PATH_FORMAT,
                    ImmutableMap.of("namespace", namespace, "crName", crname));
        }
    }

    public static class Util {
        static Entries getDefaultEntry(Dameng dameng) {
            return dameng.getSpec().getEntries().get(0);
        }

        static Entries getMonitorEntry(Dameng dameng) {
            return dameng.getSpec().getEntries().get(1);
        }

        static ResourceRequirements getResources(Entries entry) {
            return entry.getPodTemplate().getSpec().getContainers().get(0).getResources();
        }

        public static PodDTO getMonitor(List<PodDTO> pods) {
            return pods.stream().filter(podDTO -> Name.ENTRY_MONITOR.equals(podDTO.getLabel(CloudAppConstant.CustomLabels.APP_COMPONENT)))
                    .findFirst()
                    .orElseThrow(() -> new IllegalStateException("monitor pod not found"));
        }
    }

    @Override
    public AppKind getKind() {
        return AppKind.Dameng;
    }

    @Override
    public boolean nodePolicy() {
        return false;
    }

    @Override
    public PageInfo<? extends CloudAppVO> searchPage(PageDTO page) {
        PageInfo<? extends CloudAppVO> cloudAppVOPageInfo = super.searchPage(page);
        
        return PageUtil.page2PageInfo(cloudAppVOPageInfo.getList(), DamengVO.class, cloudAppVO -> {
            DamengVO dmVO = new DamengVO();
            BeanUtils.copyProperties(cloudAppVO, dmVO);
            String yaml = StringUtils.isNotEmpty(cloudAppVO.getCr()) ? cloudAppVO.getCr() : cloudAppVO.getCrRun();
            Dameng cr = YamlEngine.unmarshal(yaml, Dameng.class);
            Entries defaultEntry = Util.getDefaultEntry(cr);
            ResourceRequirements resources = Util.getResources(defaultEntry);
            KubeClient client = clientService.get(cloudAppVO.getKubeId());
            //查询ipnode为实际情况
            List<PodDTO> pods = client.listPod(cloudAppVO.getNamespace(), getKind().labelOfPod(cloudAppVO));
            List<CloudApp.IpNode> ipnodes = pods.stream()
                    .filter(pod -> Name.ENTRY_DATABASE.equalsIgnoreCase(pod.getLabel(CloudAppConstant.CustomLabels.APP_COMPONENT)))
                    .map(pod -> {
                CloudApp.IpNode ipNode = new CloudApp.IpNode();
                ipNode.setNode(pod.getNodeName());
                ipNode.setIp(pod.getPodIp());
                return ipNode;
            }).collect(Collectors.toList());
            dmVO.setIpList(JsonUtil.toJson(ipnodes));
            dmVO.setDataSize(defaultEntry.getReplicas());
            dmVO.setDataCpu(String.valueOf(resources.getLimits().get("cpu")));
            dmVO.setDataMemory(String.valueOf(resources.getLimits().get("memory")));
            dmVO.setDisk(String.valueOf(
                    defaultEntry.getVolumeTemplates().stream()
                            .filter(pvc->pvc.getMetadata().getName().equalsIgnoreCase(Name.DM_DATA_VOL_NAME))
                            .map(pvc->pvc.getSpec().getResources().getRequests().get("storage"))
                            .findFirst().get()
            ));

            Entries monitorEntry = Util.getMonitorEntry(cr);
            dmVO.setMonitorSize(monitorEntry.getReplicas());
            ResourceRequirements monitorResources = Util.getResources(monitorEntry);

            dmVO.setMonitorCpu(String.valueOf(monitorResources.getLimits().get("cpu")));
            dmVO.setMonitorMemory(String.valueOf(monitorResources.getLimits().get("memory")));
            List<ServiceManager> svcMgrs = accessManagementService.usedServiceList(cloudAppVO.getId());
            svcMgrs.forEach(svc -> {
                if (getKind().getReadServiceName(cloudAppVO.getCrName()).equals(svc.getServiceName())) {
                    dmVO.setReadPort(svc.getPort());
                } else if (getKind().getWriteServiceName(cloudAppVO.getCrName(), null).equals(svc.getServiceName())) {
                    dmVO.setWritePort(svc.getPort());
                }
            });
            return dmVO;
        });
    }

    @Override
    public List<AppInstanceVO> findInstanceList(Integer appId, String sortProp, String sort) {
        List<AppInstanceVO> instanceList = super.findInstanceList(appId, sortProp, sort);
        instanceList.sort(Comparator
                .comparing(AppInstanceVO::getComponentKind, Comparator.nullsLast(Comparator.naturalOrder())) // Default componentKind order
                .thenComparing(AppInstanceVO::getRole, Comparator.nullsLast(Comparator.naturalOrder()))); // Default role order

        return instanceList;
    }

    @Override
    protected void completeInstanceProperty(AppInstanceVO appInstanceVO, PodDTO pod, CloudApp app, KubeClient client) {
        String role = pod.getLabel(CloudAppConstant.CustomLabels.ROLE);
        if (CloudAppConstant.ROLE_STANDBY.equalsIgnoreCase(role)) {
             role = CloudAppConstant.ROLE_SECONDARY;
        } else if ("NORMAL".equalsIgnoreCase(role)) { // 单实例为主
            String yaml = StringUtils.isNotEmpty(app.getCr()) ? app.getCr() : app.getCrRun();
            Dameng cr = YamlEngine.unmarshal(yaml, Dameng.class);
            if (cr.getSpec().getEntries().get(0).getReplicas() == 1)
                role = CloudAppConstant.ROLE_PRIMARY;
        }
        appInstanceVO.setRole(role);

        appInstanceVO.setComponentKind(pod.getLabel(CloudAppConstant.CustomLabels.APP_COMPONENT));
        appInstanceVO.setComponentKind(pod.getLabel(CloudAppConstant.CustomLabels.APP_COMPONENT));

        //补充liveness
        String livenessProbe = "";
        if ("monitor".equalsIgnoreCase(pod.getLabel("app.kubernetes.io/component"))) {
            livenessProbe = "pgrep -f \"dmserver\" >/dev/null 2>&1 || echo \"1\"";
        } else {
            livenessProbe = "bash /scripts/rediness-probe.sh &&echo 0 ||echo 1";
        }
        String data = null;
        try {
            data = client.execCmd(appInstanceVO.getNamespace(), appInstanceVO.getPodName(), "dm", "sh", "-c", livenessProbe);
        } catch (Exception e) {
            data = "1";
        }
        data = data != null ? data.replaceAll("\\s", "") : null;
        appInstanceVO.setLiveness(!"1".equals(data) ? 1 : 0);
        appInstanceVO.setStatus(!"1".equals(data) ? CloudAppConstant.PodStatus.RUNNING : CloudAppConstant.PodStatus.STOPPED);
    }

    @Override
    public Dameng doInstall(CloudAppVO vo, List<String> ips) throws Exception {
        Map<ImageKindEnum, String> imageKindEnumStringMap = vo.getImageConfig();
        Map<String, String> config = setupDbParamConfig(vo); // 包含默认参数，见paramformula
        KubeScheduler kubeScheduler = kubeSchedulerService.queryFastUseById(vo.getKubeSchedulerId(), vo.getKubeId());
        final String namespace = vo.getNamespace();
        final String crName = vo.getCrName();
//        vo.setUsername("SYSDBA"); // overwrite

        Dameng dameng = new Dameng();
        dameng.setMetadata(new ObjectMetaBuilder().withName(crName).withNamespace(namespace).build());
        DamengSpec damengSpec = new DamengSpec();
        dameng.setSpec(damengSpec);
        configureSpec(getComponentList(vo), imageKindEnumStringMap, config, kubeScheduler, dameng, vo.getResourceVersion(), vo);

        Secrets secrets = new Secrets();
        Users dbaUser = new Users();
        dbaUser.setSecretName(Name.getDbaSecretName(crName));
        Users dmpUser = new Users();
        dmpUser.setSecretName(Name.getDmpSecretName(crName));
        secrets.setUsers(ImmutableList.of(dbaUser, dmpUser));
        damengSpec.setSecrets(secrets);

        Entries dbEntry = Util.getDefaultEntry(dameng);
        List<Services> services = ObjectUtils.defaultIfNull(dbEntry.getServices(), new ArrayList<>());
        dbEntry.setServices(services);
        Services exporterSvc = new Services();
        exporterSvc.setName("exporter");
        exporterSvc.setServiceSpec(new ServiceSpecBuilder()
                .withSelector(Label.toMap(getKind().labels(crName)))
                .withType("ClusterIP")
                .withPorts(new ServicePortBuilder().withName(Name.exporterPortName)
                        .withPort(9200)
                        .build())
                .build());
        services.add(exporterSvc);
        return dameng;
    }

    @Override
    public void extendSetup(CloudAppVO app) {
        CustPreconditions.checkState(app.getCrName().length() <= 18, "达梦名称长度不能超出18");
    }

    private void createFilebeatCM(String crName, String namespace, KubeClient client) {
        client.applyYaml(
                YamlUtil.evaluateTemplate(getFilebeatConfigYaml(), ImmutableMap.<String, String>builder()
                        .put("namespace", namespace)
                        .put("name", crName)
                        .put("es_host", esUtil.getEsIp() + ":" + esUtil.getEsPort())
                        .put("es_username", esUtil.getEsUsername())
                        .put("es_pwd", esUtil.getEsPassword())
                        .put("es_protocol", esUtil.getProtocol()).build()
                ));
    }

    private String getFilebeatConfigYaml() {
        return sysConfigService.findOne(OPERATOR_CONFIG, "Dameng.config");
    }

    @Override
    protected void createCrControlResource(Dameng cr, CloudAppVO vo) {

        final String crName = vo.getCrName();
        final String namespace = vo.getNamespace();
        KubeClient kubeClient = clientService.get(vo.getKubeId());
        List<CompletableFuture> all = new ArrayList<>();
        // owned by cr
        if (StringUtils.isNotEmpty(vo.getPassword())) {
            all.add(CompletableFuture.runAsync(() -> {
                Map<String, String> secretData = new HashMap<>();
                secretData.put("password", vo.getPassword());
                secretData.put("username", Name.DBA_USER);
                kubeClient.createSecret(vo.getNamespace(),
                        Name.getDbaSecretName(crName), null, secretData, cr);
                databaseUserService.createUser(vo.getId(), DamengService.Name.DBA_USER, vo.getEncryptedPassword(),
                        CloudAppConstant.UserRole.ADMIN);
            }));
        }
        all.add(CompletableFuture.runAsync(() -> {
            // 创建secret for dmp监控用户
            String dmpMonitorUserConfig = sysConfigService.findOne(CloudAppConstant.SysCfgCategory.DMP_MANAGEMENT, "monitoruser");
            if (StringUtils.isBlank(dmpMonitorUserConfig)) {
                throw new CustomException(500, "");
            }
            String[] dmpMonitorUserConfigArr = dmpMonitorUserConfig.split("/");
            //对密码解密
            String encryptPass = dmpMonitorUserConfigArr[1];
            String dmpPassword = SymmetricEncryptionUtil.getEncryptInstance().decrypt(encryptPass);
            Map<String, String> secretData = new HashMap<>();
            String username = dmpMonitorUserConfigArr[0];
            secretData.put("username", username);
            secretData.put("password", dmpPassword);

            kubeClient.createSecret(vo.getNamespace(),
                        Name.getDmpSecretName(crName), null, secretData, cr);
            databaseUserService.createUser(vo.getId(), username, encryptPass,
                        CloudAppConstant.UserRole.ADMIN);
        }));
        all.add(CompletableFuture.runAsync(() -> {
            log.info("create dameng_exporter.config secret");
            CloudDatabaseUser result = databaseUserService.findDmpAdminUser().orElseThrow(() -> new IllegalStateException("miss admin user setting"));
            String username = result.getUsername();
            try {
                String password = URLEncoder.encode(result.getPassword(), "utf-8");
                kubeClient.createSecret(namespace, Name.getMonSecretName(crName, ""), null, ImmutableMap.of(
                                Name.DAMENG_EXPORTER_CONFIG,
                                String.format("dbHost=127.0.0.1:5236?autoCommit=true\n" +
                                        "dbUser=%s\n" +
                                        "dbPwd=%s", username, password)),
                        cr);
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
        }));

        // not owned by cr
//        all.add(CompletableFuture.runAsync(() -> {
//            log.info("create filebeat cm");
////            final String cmName = "dameng-filebeat";
//            createFilebeatCM(crName, namespace, kubeClient);
//        }));
        all.add(CompletableFuture.runAsync(() -> {
            log.info("create dameng-agent rbac resource");
            createAgentRbac(kubeClient, namespace);
        }));

        CompletableFuture.allOf(all.toArray(new CompletableFuture[0])).join();
    }

    @Override
    protected void setInstallExtData(CloudAppVO vo) {
        Map adminUserParamMap = new HashMap();
        adminUserParamMap.put("username", vo.getUsername());
        adminUserParamMap.put("password", vo.getEncryptedPassword());
        vo.setExtInstallData(adminUserParamMap);
    }

    private GenericSpec.ComponentList getComponentList(CloudAppVO app) {
        DamengVO vo = (DamengVO) app;
        GenericSpec.ComponentSpec database = new GenericSpec.ComponentSpec();
        database.setReplicas(vo.getDataSize());
        database.setLimits(ImmutableMap.of("cpu", vo.getCpu(), "memory", vo.getMemory()));
        database.setName(Name.ENTRY_DATABASE);
        database.setStorage(new GenericSpec.StorageList(
                ImmutableList.of(GenericSpec.StorageSpec.builder()
                        .name(Name.DM_DATA_VOL_NAME)
                        .storageClassName(app.getStorageClassName())
                        .size(app.getDisk())
                        .build())
        ));
        GenericSpec.ComponentSpec monitor = new GenericSpec.ComponentSpec();
        monitor.setReplicas(vo.getMonitorSize());
        monitor.setLimits(ImmutableMap.of("cpu", vo.getMonitorCpu(), "memory", vo.getMonitorMemory()));
        monitor.setName(Name.ENTRY_MONITOR);
        GenericSpec.ComponentList componentList = new GenericSpec.ComponentList();
        componentList.add(database);
        componentList.add(monitor);
        return componentList;
    }

    private void configureSpec(GenericSpec.ComponentList componentList, Map<ImageKindEnum, String> imageKindEnumStringMap, Map<String, String> config, KubeScheduler kubeScheduler, Dameng dameng, String resourceVersion, CloudAppVO appVO) {
        List<Toleration> k8sTolerance = kubeScheduler.toK8sTolerance();
        Affinity k8sAffinity = kubeScheduler.toK8sAffinity(appVO);
        List<Entries> entries = new ArrayList<>();
        DamengSpec spec = dameng.getSpec();
        spec.setEntries(entries);

        String initParams = config.remove("init-params");
        List<EnvVar> envs = new ArrayList<>();
        addInitToEnv(initParams, envs);
        GenericSpec.ComponentSpec dbComponent = componentList.get(0);
        Entries database = new Entries();
        Entries monitor = new Entries();
        entries.add(database);
        entries.add(monitor);
        database.setName(dbComponent.getName());
        database.setImage(imageKindEnumStringMap.get(ImageKindEnum.MainImage));
        database.setReplicas(dbComponent.getReplicas());
        database.setConfig(config);

        String serviceAccount = getAgentServiceAccountName();
        database.setPodTemplate(new PodBuilder()
                .withNewSpec()
                .withServiceAccountName(serviceAccount)
                .withAffinity(k8sAffinity)
                .withTolerations(k8sTolerance)
                .withContainers(
                        // main container
                        new ContainerBuilder()
                                .withName(getKind().getContainerName())
                                .withResources(ResourceHelper.getInstance().resourceRequirements(dbComponent.getLimits()))
                                .withEnv(envs)
                                .withVolumeMounts(
                                        new VolumeMountBuilder().withName("log-volume").withMountPath("/opt/dmdbms/log")
                                                .build(),
                                        new VolumeMountBuilder().withName("timezone").withMountPath("/etc/localtime")
                                                .build()
                                )
                                .build(),
                        // mount container. torefact
                        new ContainerBuilder()
                                .withName("mount")
                                .withImage(imageKindEnumStringMap.get(ImageKindEnum.Mount))
                                .build(),
                        // filebeat
                        getFilebeatContainer(imageKindEnumStringMap.get(ImageKindEnum.Filebeat)),
                        // exporter
                        new ContainerBuilder()
                                .withName("exporter")
                                //
                                .withCommand("sh", "-c", String.format(
                                        "while true; do\n" +
                                                "if nc -z localhost %d; then\n" +
                                                "  echo \"dm is ready. Performing readiness checks for exporter...\"\n" +
                                                "  if ! nc -z localhost 9200; then\n" +
                                                "    /app/dameng_exporter --configFile=%s --no-encodeConfigPwd &\n" +
                                                "  fi\n" +
                                                "else\n" +
                                                "  echo \"dm is not ready. Staying ready by default.\"\n" +
                                                "fi\n" +
                                                "sleep 5\n" +
                                                "done", getKind().getDbPort(), "/opt/" + Name.DAMENG_EXPORTER_CONFIG))
                                .withImage(imageKindEnumStringMap.get(ImageKindEnum.Exporter))
                                .withVolumeMounts(
                                        new VolumeMountBuilder().withName("exporter-config").withMountPath("/opt")
                                                .build())
                                .withPorts(new ContainerPortBuilder().withName(Name.exporterPortName).withContainerPort(9200).build())
                                .build()
                )
                .withVolumes(ImmutableList.of(
                        new VolumeBuilder().withName("dameng-filebeat").withConfigMap(
                                new ConfigMapVolumeSourceBuilder().withName(Name.DAMENG_FILEBEAT).build()
                        ).build(),
                        new VolumeBuilder().withName("log-volume").withEmptyDir(new EmptyDirVolumeSource()).build(),
                        new VolumeBuilder().withName("exporter-config")
                                .withSecret(new SecretVolumeSourceBuilder()
                                        .withSecretName(Name.getMonSecretName(dameng.getMetadata().getName(), ""))
                                        .build())
                                .build(),
                        new VolumeBuilder().withName(CloudAppConstant.Volumes.TIMEZONE_NAME).withNewHostPath(CloudAppConstant.Volumes.TIMEZONE_HOSTPATH, "").build()
                ))
                .endSpec()
                .build());
        database.setVolumeTemplates(dbComponent.getStorage().toPVC(resourceVersion));

        componentList.stream().filter(c -> c.getName().equals(Name.ENTRY_MONITOR))
                .findFirst()
                .ifPresent(e -> {
                    monitor.setImage(database.getImage());
                    monitor.setReplicas(e.getReplicas());
                    monitor.setName(e.getName());
                    monitor.setPodTemplate(new PodBuilder()
                            .withNewSpec()
                            .withAffinity(k8sAffinity)
                            .withTolerations(k8sTolerance)
                            .withContainers(
                                    // main container
                                    new ContainerBuilder()
                                            .withName(getKind().getContainerName())
                                            .withResources(ResourceHelper.getInstance().resourceRequirements(e.getLimits()))
                                            .build())
                            .endSpec()
                            .build()
                    );
                });
    }

    private Container getFilebeatContainer(String image) {
        return YamlEngine.unmarshal(YamlUtil.evaluateTemplate(filebeatYaml, ImmutableMap.of("image", image)),
                Container.class);
    }

    private final String filebeatYaml = "args:\n" +
            "- /etc/filebeat/filebeat-entrypoint.sh\n" +
            "command:\n" +
            "- bash\n" +
            "env:\n" +
            "- name: APP_NAMESPACE\n" +
            "  valueFrom:\n" +
            "    fieldRef:\n" +
            "      apiVersion: v1\n" +
            "      fieldPath: metadata.namespace\n" +
            "- name: APP_NAME\n" +
            "  valueFrom:\n" +
            "    fieldRef:\n" +
            "      apiVersion: v1\n" +
            "      fieldPath: metadata.labels['app.kubernetes.io/name']\n" +
            "- name: COMPONENT_NAME\n" +
            "  valueFrom:\n" +
            "    fieldRef:\n" +
            "      apiVersion: v1\n" +
            "      fieldPath: metadata.labels['app.kubernetes.io/component']\n" + // used int the filebeat config file
            "- name: APP_TYPE\n" +
            "  value: flink\n" +
            "- name: POD_IP\n" +
            "  valueFrom:\n" +
            "    fieldRef:\n" +
            "      apiVersion: v1\n" +
            "      fieldPath: status.podIP\n" +
            "- name: POD_NAME\n" +
            "  valueFrom:\n" +
            "    fieldRef:\n" +
            "      apiVersion: v1\n" +
            "      fieldPath: metadata.name\n" +
            "- name: FILEBEAT_CFG_FILE\n" +
            "  value: dameng-datawatch-filebeat.yaml\n" +
            "imagePullPolicy: IfNotPresent\n" +
            "name: filebeat\n" +
            "securityContext:\n" +
            "  privileged: true\n" +
            "  runAsUser: 0\n" +
            "image: ${image}\n" +
            "volumeMounts:\n" +
            "- mountPath: /opt/dmdbms/log\n" +
            "  name: log-volume\n" +
            "- name: script\n" +
            "  mountPath: /scripts/\n" +
            "- mountPath: /etc/filebeat/\n" +
            "  name: dameng-filebeat\n";
//            "- mountPath: /etc/localtime\n" +
//            "  name: timezone";

    private void addInitToEnv(String initParams, List<EnvVar> envs) {
        if (initParams == null) return;
        String[] pairs = initParams.split("\\s+");
        for (String pair : pairs) {
            String[] keyValue = pair.split("=");
            if (keyValue.length == 2) {
                envs.add(new EnvVar(keyValue[0], keyValue[1], null));
            } else {
                // invalid pair format
            }
        }
    }

    @Override
    public void delete(CloudApp app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        Dameng cr = kubeClient.listCustomResource(Dameng.class, app.getCrName(), app.getNamespace());
        if (cr == null) return;
        cr.getSpec().setMaintenance(true);
        cr.getSpec().getEntries().forEach(entry -> entry.setReplicas(0));
        kubeClient.updateCustomResource(cr, Dameng.class);
        // fix: operator 暂不支持处于已经维护状态时缩减实例数
        List<StatefulSet> sset = kubeClient.listStatefulSetWithLables(app.getNamespace(), getKind().labels(app.getCrName()));
        for (StatefulSet sts : sset) {
            kubeClient.scaleSts(sts.getMetadata().getName(), sts.getMetadata().getNamespace(), 0);
        }
    }

    @Override
    public void recreate(CloudApp app) {
        String yaml = StringUtils.isEmpty(app.getCr()) ? app.getCrRun() : app.getCr() ;
        Dameng cr = YamlEngine.unmarshal(yaml, Dameng.class);
        cr.getSpec().setMaintenance(false);
        KubeClient kubeClient = clientService.get(app.getKubeId());
        kubeClient.updateCustomResource(cr, Dameng.class);
    }

    @Override
    protected boolean supportIPAM(CloudAppVO app) {
        return false;
    }

    @Override
    public void update(Integer id, OverrideSpec overrideSpec)throws Exception {
        if (overrideSpec instanceof DamengService.DMOverrideSpec) {
            DamengResourceDTO patch = new DamengResourceDTO();
            DamengService.DMOverrideSpec updateSpec = (DamengService.DMOverrideSpec) overrideSpec;
            patch.setMonitorCpu(updateSpec.getMonitorCpu());
            patch.setCpu(updateSpec.getDataCpu());
            patch.setMonitorMemory(updateSpec.getMonitorMemory());
            patch.setMemory(updateSpec.getDataMemory());
            patch.setDisk(updateSpec.getDisk());
            patch.setId(id);
            update(patch);
        }
    }

    @Override
    public void update(ResourceDTO patch) throws Exception {
        DamengResourceDTO dmPatch = (DamengResourceDTO) patch;
        operationHandler.handleUpdate(
                patch,
                cr -> {
                    DamengSpec spec = cr.getSpec();
                    dataEntryPeek(spec,
                            e -> {
                            },
                            c -> c.setResources(ResourceHelper.getInstance().resourceRequirements(
                                    ImmutableMap.of("cpu", dmPatch.getCpu(), "memory", dmPatch.getMemory()))
                            ));
                    monitorEntryPeek(spec,
                            c -> {
                            },
                            c -> c.setResources(ResourceHelper.getInstance().resourceRequirements(
                                    ImmutableMap.of("cpu", dmPatch.getMonitorCpu(), "memory", dmPatch.getMonitorMemory()))
                            ));
                },
                this,
                Dameng.class,
                cr -> {
                    DamengSpec spec = cr.getSpec();
                    dataPvcPeek(spec,
                            c -> c.getSpec().setResources(new ResourceRequirementsBuilder().withRequests(
                                    ImmutableMap.of("storage", new Quantity(patch.getDisk()))).build()
                            ));
                });
    }

    private static void dataPvcPeek(DamengSpec spec, Consumer<PersistentVolumeClaim> storage) {
        spec.getEntries().stream().filter(e -> e.getName().equals(Name.ENTRY_DATABASE))
                .findAny()
                .flatMap(e -> e.getVolumeTemplates().stream().filter(pvc -> pvc.getMetadata().getName().equals(Name.DM_DATA_VOL_NAME)).findFirst())
                .ifPresent(storage);
    }

    private static void monitorEntryPeek(DamengSpec spec, Consumer<Entries> entryConsumer, Consumer<Container> containerConsumer) {
        spec.getEntries().stream().filter(e -> e.getName().equals(Name.ENTRY_MONITOR))
                .peek(entryConsumer)
                .findAny()
                .flatMap(e -> e.getPodTemplate().getSpec().getContainers().stream().filter(c -> c.getName().equals(Name.CONTAINER_DM)).findFirst())
                .ifPresent(containerConsumer);
    }

    private static void dataEntryPeek(DamengSpec spec, Consumer<Entries> entryConsumer) {
        dataEntryPeek(spec, entryConsumer, c -> {
        });
    }

    private static void dataEntryPeek(DamengSpec spec, Consumer<Entries> entryConsumer, Consumer<Container> containerConsumer) {
        spec.getEntries().stream().filter(e -> e.getName().equals(Name.ENTRY_DATABASE))
                .peek(entryConsumer)
                .findAny()
                .flatMap(e -> e.getPodTemplate().getSpec().getContainers().stream().filter(c -> c.getName().equals(Name.CONTAINER_DM)).findFirst())
                .ifPresent(containerConsumer);
    }

    public void scale(int appId, OverrideSpec overrideSpec, ActionEnum actionEnum) throws Exception {
        operationHandler.handleScale(
                appId,
                Dameng.class,
                this,
                (app, cr) -> {
                    DMOverrideSpec dmOverrideSpec = (DMOverrideSpec) overrideSpec;
                    dataEntryPeek(cr.getSpec(), e -> e.setReplicas(dmOverrideSpec.getDataSize()));
                },
                actionEnum);
    }

    @Override
    public List<ServiceManager> createService(
            String serviceType, CloudAppVO vo, List<?> serviceResources, CustomResource installCr) {
        //1.校验
        AppKind kind = getKind();
        if (serviceResources.isEmpty()) return Collections.emptyList();
        int serviceManagerNum = kind.getServiceManagerNum(serviceType, vo.getMembers());
        if (CollectionUtils.isEmpty(serviceResources) || serviceResources.size() != serviceManagerNum) {
            throw new CustomException(600, "节点端口类型必须指定 " + serviceManagerNum
                    + " 个端口, 实际数量为 " + serviceResources.size() + ", 类型为 " + serviceType);
        }

        //2. 声明构建所需的变量
        List<ServiceManager> svms = new ArrayList<>();
        Dameng cr = (Dameng) installCr;
        Entries dbEntry = Util.getDefaultEntry(cr);
        List<Services> services = dbEntry.getServices();
        if (services == null) {
            services = new ArrayList<>();
        }
        int dbPort = kind.getDbPort();
        String crName = vo.getCrName();
        Map<String, String> labelMap = Arrays.stream(getKind().labels(crName))
                .collect(Collectors.toMap(Label::getName, Label::getValue));
        Map<String, String> selectorLabelMap = Arrays.stream(getKind().labelOfService(vo))
                .collect(Collectors.toMap(Label::getName, Label::getValue));
        selectorLabelMap.put(CloudAppConstant.CustomLabels.APP_COMPONENT, "database");
        Map<String, String> annotationMap = null;
        Map<String, Object> additionalMap = null;

        for (int i = 0; i < serviceResources.size(); i++) {
            Object serviceResource = serviceResources.get(i);
            //2. 通过 fabric8 构建 Service,再 set 到 ServiceManager 中

            //2.3 初始化 ServiceManager
            ServiceManager serviceManager = new ServiceManager();
            // 有读写分离，需要两个 service，且设置属性
            String serviceName;
            if (i == 0) {
                serviceManager.setPurpose(CloudAppConstant.ServicePurpose.WRITE);
                serviceName = kind.getWriteServiceName(crName, null);
                selectorLabelMap.put(CloudAppConstant.CustomLabels.ROLE, CloudAppConstant.ROLE_PRIMARY);
            } else {
                serviceManager.setPurpose(CloudAppConstant.ServicePurpose.READ);
                serviceName = kind.getReadServiceName(crName);
                selectorLabelMap.put(CloudAppConstant.CustomLabels.ROLE, CloudAppConstant.ROLE_STANDBY);
            }
            serviceManager.setServiceName(serviceName);
            serviceManager.setServiceType(serviceType);
            Integer nodePort = null;

            //2.4 根据 ServiceType 填充 NodePort 和 lb注解map
            if (CloudAppConstant.ServiceType.NODE_PORT.equals(serviceType)) {
                //nodeport 方式，serviceResources 结构为 List<Integer>，进行分配 NodePort，只需要一个 NodePort
                nodePort = (Integer) serviceResource;
                serviceManager.setPort(nodePort);
            } else if (CloudAppConstant.ServiceType.LOAD_BALANCER.equals(serviceType)) {
                //lb 方式，serviceResources 结构为 List<String>，进行分配 lbip，只需要一个 lbip，端口为 dbport
                serviceManager.setPort(dbPort);
                String lbip = (String) serviceResource;
                serviceManager.setExternalIp(lbip);
                annotationMap = new HashMap<String, String>(){{
                    putAll(accessManagementService.buildLBAnnotationMap(lbip)); }};
                // LB注意要配置外部连接策略为 Local，否则默认为 Cluster，导致容器内无法获取外部真实 IP
                additionalMap = new HashMap<String, Object>(){{ put("externalTrafficPolicy", "Local");}};
            }

            //3. 构建 Service
            Services service = new Services();
            service.setMetadata(
                    new ObjectMetaBuilder()
                            .withName(serviceName)
                            .withLabels(labelMap)
                            .withAnnotations(annotationMap).build());
            service.setName(serviceManager.getPurpose().equals(0) ? Name.SERVICE_WRITE : Name.SERVICE_READ);
            service.setServiceSpec(
                    new ServiceSpecBuilder()
                            .withType(serviceType)
                            .withSelector(selectorLabelMap)
                            .withPorts(new ServicePortBuilder()
                                    .withPort(dbPort).withNodePort(nodePort).build())
                            .addToAdditionalProperties(additionalMap)
                            .build());

            //4. 把 Service 添加到 ServiceList 中，把 ServiceManager 添加到 ServiceManagerList 中
            services.add(service);
            svms.add(serviceManager);
        }

        //5. 设置 cr.spec
        dbEntry.setServices(services);

        return svms;
    }

    private Services getBuild(ServiceManager serviceManager, CloudApp app) {
        String name = serviceManager.getServiceName().endsWith(Name.SERVICE_WRITE) ? Name.SERVICE_WRITE : Name.SERVICE_READ;
        Map<String, String> selector = Label.toMap(AppKind.Dameng.labels(app.getCrName()));
        selector.put(CloudAppConstant.CustomLabels.APP_COMPONENT, "database");
        // 读写分离
        String role = serviceManager.getServiceName().endsWith(Name.SERVICE_WRITE) ? CloudAppConstant.ROLE_PRIMARY : CloudAppConstant.ROLE_STANDBY;
        selector.put(CloudAppConstant.CustomLabels.ROLE, role);
        Services services = new Services();
        services.setName(name);
        services.setServiceSpec(new ServiceSpecBuilder()
                .withType(serviceManager.getServiceType())
                .withPorts(new ServicePortBuilder().withPort(getKind().getDbPort()).withNodePort(serviceManager.getPort()).build())
                .withSelector(selector)
                .build());
        return services;
    }

    @Override
    public void updateService(List<ServiceManager> svcMgrs, CloudApp app, Object oldServiceResource) throws Exception {
        ServiceManager serviceManager = svcMgrs.get(0);
        String serviceType = serviceManager.getServiceType();
        Map<String, String> data = new HashMap<>();
        data.put("oldServiceResource", oldServiceResource + "");
        Consumer<Dameng> modifier = (cr) -> Util.getDefaultEntry(cr).getServices().stream().filter(
                service -> service.getMetadata() != null
                        && service.getMetadata().getName().equals(serviceManager.getServiceName()))
                .findAny().ifPresent(service -> {
            if (serviceType.equalsIgnoreCase(CloudAppConstant.ServiceType.NODE_PORT)) {
                //nodeport 方式，serviceResources 结构为 List<Integer>，进行分配 NodePort，只需要一个 NodePort
                service.getServiceSpec().getPorts().get(0).setNodePort(serviceManager.getPort());
            } else if (serviceType.equalsIgnoreCase(CloudAppConstant.ServiceType.LOAD_BALANCER)) {
                //lb 方式，serviceResources 结构为 List<String>，进行分配 lbip，只需要一个lbip，端口为 dbport
                service.getMetadata().getAnnotations().putAll(
                        accessManagementService.buildLBAnnotationMap(serviceManager.getExternalIp()));
            }
        });

        operationHandler.handleService(
                app, svcMgrs, modifier, Dameng.class, this, data, ActionEnum.UPDATE_SERVICE);
    }

    @Getter
    @Setter
    @ToString
    public static class DMOverrideSpec extends OverrideSpec {
        private int dataSize;
        private String dataCpu;
        private String dataMemory;
        private int monitorSize = 1;
        private String monitorCpu;
        private String monitorMemory;

    }

    @Getter
    @Setter
    public static class DamengVO extends CloudAppVO {
        private Integer monitorSize = 1;
        private String monitorCpu;
        private String monitorMemory;
        private Integer dataSize;
        private String dataCpu;
        private String dataMemory;
    }

    @Override
    public Class<? extends OpsPostProcessor> getProcessorClass(ActionEnum action) {
        switch (action) {
            case CREATE:
                return DamengResourceInstallWatch.class;
            case SCALE_IN:
                return DamengScaleWatch.class;
            case UPDATE:
            case SCALE_OUT:
            case DELETE:
            case UPGRADE:
            case MODIFY_PARAM:
                return DamengResourceWatch.class;
            case CREATE_SERVICE:
            case UPDATE_SERVICE:
            case DELETE_SERVICE:
                return DamengSvcWatch.class;
            case MIGRATE:
                return DamengMigrateWatch.class;
            case START_POD:
            case STOP_POD:
                return DamengStartStopWatch.class;
            case SWITCH_MASTER:
                return DamengSwitchMasterWatcher.class;
            default:
                return super.getProcessorClass(action);
        }
    }

    @Override
    public InstallAppVo<? extends OverrideSpec> parseInstallVo(String data) {
        InstallAppVo<DMOverrideSpec> vo = JsonUtil.toObject(data, new TypeReference<InstallAppVo<DMOverrideSpec>>() {
        });
        if (vo != null) {
            if (vo.getSpec() != null && vo.getSpec().getMembers() == 0) {
                DMOverrideSpec spec = vo.getSpec();
                spec.setMembers(spec.getDataSize() + spec.getMonitorSize());
            }
            if (!CollectionUtils.isEmpty(vo.getOverrideSpecs())) {
                for (DMOverrideSpec spec : vo.getOverrideSpecs().values()) {
                    if (spec.getMembers() == 0)
                        spec.setMembers(spec.getDataSize() + spec.getMonitorSize());
                }
            }
        }
        return vo;
    }

    public CloudAppVO overrideSpec(CloudAppLogic logicApp, Integer kubeId, InstallAppVo<? extends OverrideSpec> vo) {
        CloudAppVO appVO = super.overrideSpec(logicApp, kubeId, vo);
        DamengVO damengVO = new DamengVO();
        BeanUtils.copyProperties(appVO, damengVO);
        if (vo.getOverrideSpecs().get(kubeId) instanceof DMOverrideSpec) {
            DMOverrideSpec overrideSpec = (DMOverrideSpec) vo.getOverrideSpecs().get(kubeId);
            damengVO.setDataCpu(overrideSpec.getDataCpu());
            damengVO.setDataMemory(overrideSpec.getDataMemory());
            damengVO.setDataSize(overrideSpec.getDataSize());
            damengVO.setMonitorCpu(overrideSpec.getMonitorCpu());
            damengVO.setMonitorMemory(overrideSpec.getMonitorMemory());
            damengVO.setMonitorSize(overrideSpec.getMonitorSize());
            damengVO.setCpu(overrideSpec.getDataCpu());
            damengVO.setMemory(overrideSpec.getDataMemory());
        }
        return damengVO;
    }

    @Override
    public OverrideSpec reviewSpec(CloudApp app) {
        OverrideSpec overrideSpec = super.reviewSpec(app);
        DMOverrideSpec dmOverrideSpec = new DMOverrideSpec();
        BeanUtils.copyProperties(overrideSpec, dmOverrideSpec);
        try {
            String crYaml = StringUtils.isEmpty(app.getCr()) ? app.getCrRun() : app.getCr();
            Dameng cr = Serialization.unmarshal(crYaml, Dameng.class);
            DamengSpec spec = cr.getSpec();
            dataEntryPeek(spec, e -> {
            }, c -> {
                dmOverrideSpec.setDataCpu(c.getResources().getLimits().get("cpu").toString());
                dmOverrideSpec.setDataMemory(c.getResources().getLimits().get("memory").toString());
                dmOverrideSpec.setDataSize(Util.getDefaultEntry(cr).getReplicas());
            });
            dataPvcPeek(spec, c -> {
                dmOverrideSpec.setDisk(c.getSpec().getResources().getRequests().get("storage").toString());
            });
            monitorEntryPeek(
                    spec,
                    e -> dmOverrideSpec.setMonitorSize(e.getReplicas()),
                    c -> {
                        dmOverrideSpec.setMonitorCpu(c.getResources().getLimits().get("cpu").toString());
                        dmOverrideSpec.setMonitorMemory(c.getResources().getLimits().get("memory").toString());
                    }
            );

        } catch (Exception e) {
            log.error("", e);
        }
        return dmOverrideSpec;
    }

    /**
     * 增备恢复依赖基备份. 累积增备基备份一定是全备, 差额增备基备份可以是增备。
     * 按时间点恢复依赖归档日志的完整性。 DB_MAGIC (1 from backup set)
     */
    @Override
    public void restore(BackupHis backupHis, Integer appId, String pointInTime, String ftpFilename, String backupType) {
        CloudApp restoreAPP = appService.get(appId);
        KubeClient kubeClient = clientService.get(restoreAPP.getKubeId());

        // verify pre-requisite
        // 1. mount backup storage
        // 2. maintenance flag on

        List<PodDTO> podDTOS = kubeClient.listPod(restoreAPP.getNamespace(), AppKind.Dameng.labels(restoreAPP.getCrName()));
        podDTOS = podDTOS.stream().filter(pod -> DamengService.Name.ENTRY_DATABASE.equals(pod.getLabels().get(CloudAppConstant.CustomLabels.APP_COMPONENT)))
                .collect(Collectors.toList());
        String crStr = StringUtils.isEmpty(restoreAPP.getCr()) ? restoreAPP.getCrRun() : restoreAPP.getCr();
        Dameng cr = YamlEngine.unmarshal(crStr, Dameng.class);
        Integer replicas = cr.getSpec().getEntries().get(0).getReplicas();
        Preconditions.checkArgument(!podDTOS.isEmpty() && podDTOS.size() == replicas,
                "目标实例节点列表为空或不满足期望副本数，请稍后重试");
        // turn on maintenance flag
        try {
            cr.getSpec().setMaintenance(true);
            kubeClient.updateCustomResource(cr, Dameng.class);
        } catch (Exception e) {
            throw new CustomException(600, "更新为维护状态发生错误 " + e.getMessage(), e);
        }

        // verify mounted and todo space
        final String SCRIPT_PATH = "/scripts/";
        final String mountPoint = DamengService.Name.MOUNT_POINT;
        final String mountContainerName = "mount";
        try {
            String cmd = backupUtil.mountCmd(SCRIPT_PATH, mountPoint);
            for (PodDTO podDTO : podDTOS) {
                kubeClient.execCmd(restoreAPP.getNamespace(), podDTO.getPodName(), mountContainerName, 0, "sh", "-c", cmd);
            }
        } catch (Exception e) {
            throw new CustomException(600, "挂载远程备份存储失败", e);
        }
        RestoreHis his = backupUtil.insertRestoreHis(restoreAPP, Collections.singletonList(backupHis),
                podDTOS.stream().map(PodDTO::getPodName).collect(Collectors.joining(",")), "");

        try {
            ImmutableMap.Builder<Object, Object> data = ImmutableMap.builder();
            data.put("restoreHisId", his.getRestoreHisId()).put("backupHisId", backupHis.getBackupHisId());
            if (StringUtils.isNotEmpty(pointInTime)) {
                pointInTime = pointInTime.replace("_", "T");
                Timestamp timestamp = Timestamp.valueOf(LocalDateTime.parse(pointInTime, DateTimeFormatter.ISO_LOCAL_DATE_TIME));
                BinlogBackupHis binlog = backupService.listBinlogHis(appId).stream()
                        .filter(bhis -> StatusConstant.SUCCESS.equals(bhis.getStatus()))
                        .filter(bhis -> bhis.getStartTime().before(timestamp) && bhis.getEndTime().after(timestamp))
                        // todo 筛选与备份lsn连续的归档
                        .findAny().orElseThrow(() -> new CustomException(600, "查询归档日志备份失败, 无法恢复到该时间点"));
                data.put("pointTime", pointInTime); // point-in-time recovery
                data.put("binlogHisId", binlog.getId());
            }
            appService.callScheduler(restoreAPP, crStr, data.build(),
                    ActionEnum.RESTORE, DamengBackupRestoreWatch.class);
        } catch (Exception e) {
            // ensure umount
            for (PodDTO podDTO : podDTOS) {
                backupUtil.umount(kubeClient, restoreAPP.getNamespace(), podDTO.getPodName(), SCRIPT_PATH, mountPoint, mountContainerName);
            }
            // ensure maintenance off
            cr.getSpec().setMaintenance(false);
            kubeClient.updateCustomResource(cr, Dameng.class);
            throw new CustomException(600, "提交恢复操作失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public String migrate(MigrateDTO migrateDTO) throws Exception {
        Integer appId = migrateDTO.getAppId();
        CloudApp app = appService.get(appId);

        CloudApp.IpNode newNode = migrateDTO.getMigrateNodes().get(0).getNewNode();
        appService.callScheduler(app, app.getCr(), ImmutableMap.of("nodeName", newNode.getNode(), "thePod", migrateDTO.getPodName()),
                ActionEnum.MIGRATE, getProcessorClass(ActionEnum.MIGRATE), 10);

        return MIGRATE.getAppOperation();
    }


    public void switchMaster(Integer appId, String newSourceIP) {
        //进入从节点执行switchover命令切换主从
        CloudApp app = appService.get(appId);

        KubeClient client = clientService.get(app.getKubeId());
        List<PodDTO> pods = client.listPod(app.getNamespace(), getKind().labelOfPod(app));
        //判断是否为从节点并获取pod编码
        pods.stream().
                filter(i -> newSourceIP.equals(i.getPodIp()) && CloudAppConstant.ROLE_STANDBY.equals(i.getLabels().get("app.kubernetes.io/role"))).
                findAny().orElseThrow(() -> new CustomException(600, "此实例不是从节点"));

        try {
            appService.callScheduler(app, app.getYaml(), ImmutableMap.of("newSourceIP", newSourceIP),
                    ActionEnum.SWITCH_MASTER, getProcessorClass(ActionEnum.SWITCH_MASTER), 10);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void beforeSwitch(Integer id, String newSourceIP) {

    }

    @Override
    public void executeSwitchMaster(String oldSourceIP, String newSourceIP, KubeClient kubeClient, CloudApp app, String resultFile) {

    }

    @Override
    public void start(int appId, @Nullable String instanceName) throws Exception {
        String cmd = "bash /scripts/start.sh";
        operationHandler.handleStart(appId, instanceName, this, cmd, i -> CloudAppConstant.ROLE_PRIMARY.equals(i.getRole()),
                i -> Name.ENTRY_DATABASE.equalsIgnoreCase(i.getComponentKind()));
    }

    @Override
    @Transactional
    public void stop(int appId, String instanceName) {
        String cmd = "bash /scripts/ensure-watcher.sh stop;" +
                "pid=$(pgrep -f /opt/dmdbms/bin/dmserver);" +
                "kill $pid";
        Consumer<Dameng> turnOnMaintain = cr -> {
            cr.getSpec().setMaintenance(true);
        };
        operationHandler.handleStop(appId, instanceName, this, cmd, turnOnMaintain, Dameng.class,
                i -> Name.ENTRY_DATABASE.equalsIgnoreCase(i.getLabel(CloudAppConstant.CustomLabels.APP_COMPONENT)));
    }

    @Override
    public List<ResourceDTO> getAppComponentResource(InstallAppVo<? extends OverrideSpec> vo) {
        List<ResourceDTO> resourceDTOList = new ArrayList<>();
        DMOverrideSpec spec = (DMOverrideSpec) vo.getSpec();
        ResourceDTO database = new ResourceDTO();
        database.setCpu(spec.dataCpu);
        database.setMemory(spec.dataMemory);
        database.setDisk(spec.getDisk());
        database.setReplicas(spec.getDataSize());
        ResourceDTO monitor = new ResourceDTO();
        monitor.setCpu(spec.getMonitorCpu());
        monitor.setMemory(spec.getMonitorMemory());
        monitor.setReplicas(spec.getMonitorSize());
        resourceDTOList.add(database);
        resourceDTOList.add(monitor);
        return resourceDTOList;
    }
}