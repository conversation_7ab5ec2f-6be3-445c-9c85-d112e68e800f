package cn.newdt.cloud.web;

import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.ResourceChangeHis;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.service.CloudAppLogicService;
import cn.newdt.cloud.service.ResourceChangeHisService;
import cn.newdt.cloud.utils.CustPreconditions;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.response.ResponseResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * RESTful API of  KubeResource.
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/30 19:04
 */
@RestController
@RequestMapping("cloudmanage/resourcechangehis")
public class ResourceChangeHisManageController {

    @Autowired
    private ResourceChangeHisService resourceChangeHisService;
    @Autowired
    private CloudAppLogicService appLogicService;

    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    public ResponseResult get(@PathVariable("id") final Integer id) {
        return ResponseResult.ok("ok", resourceChangeHisService.get(id), null);
    }

    @RequestMapping(value = "/list/page", method = RequestMethod.POST)
    public ResponseResult listPage(@RequestBody PageDTO pageDTO) {
        return ResponseResult.ok(resourceChangeHisService.listPage(pageDTO), null);
    }

    @GetMapping(value = "/list/kind")
    public Map<String, String> listKind() {
        return resourceChangeHisService.listKind();
    }

    @ApiOperation("操作回滚")
    @RequestMapping(value = "/rollback", method = RequestMethod.PUT)
    @PreAuthorize("@dataAuthorizeService.hasPermission(#appLogicId, #appId)")
    public ResponseResult rollback(@RequestParam(required = false) final Integer appId,
                                   @RequestParam(required = false) final Integer appLogicId) {
        CustPreconditions.checkState(appId != null || appLogicId != null, "参数错误");
        if (appLogicId != null) {
            List<CloudApp> apps = appLogicService.getPhysicApps(appLogicId);
            // 多中心应用共同进退 fixme 部分成功的情况下不能回滚
            List<ResourceChangeHis> hisList = apps.stream().map(app -> resourceChangeHisService.getLatestByAppId(app.getId()))
                    .filter(his -> resourceChangeHisService.checkHisAllowRollback(his.getId()))
                    .collect(Collectors.toList());
            if (hisList.size() == apps.size())
                for (ResourceChangeHis his : hisList) {
                    resourceChangeHisService.rollback(his.getId());
                }
            else {
                throw new CustomException(600, "多可用区中存在不支持回滚的操作或状态");
            }
        } else {
            ResourceChangeHis latestByAppId = resourceChangeHisService.getLatestByAppId(appId);
            resourceChangeHisService.rollback(latestByAppId.getId());
        }

        return ResponseResult.ok("ok", null, null);
    }

}
