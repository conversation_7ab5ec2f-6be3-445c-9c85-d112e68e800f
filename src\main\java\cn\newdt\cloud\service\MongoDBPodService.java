package cn.newdt.cloud.service;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.repository.KubeClient;

import java.util.List;
import java.util.Map;

/**
 * @author: lianzb
 * @description: 用于 MongoDB 两个架构通用的逻辑
 */
public interface MongoDBPodService {
    /**
     * 获取 MongoDB 的状态中的 members信息
     * @param kubeClient
     * @param namespace
     * @param crName
     * @param podName
     * @param appKind
     * @return
     */
    List<Map<String, String>> getMongoDbStatusMembers(
            KubeClient kubeClient, String namespace, String crName, String podName,  AppKind appKind);
}
