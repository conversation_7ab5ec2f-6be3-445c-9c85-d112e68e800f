package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.config.CloudRequestContext;
import cn.newdt.cloud.config.FutureService;
import cn.newdt.cloud.config.MultiplePrometheusService;
import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.dto.KubeTokenDTO;
import cn.newdt.cloud.dto.NodeDTO;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.filter.ResourceChangeLog;
import cn.newdt.cloud.filter.ResourceView;
import cn.newdt.cloud.mapper.BackupTimerMapper;
import cn.newdt.cloud.mapper.KubeConfigMapper;
import cn.newdt.cloud.mapper.ResourceChangeHisMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.repository.KubeClientManager;
import cn.newdt.cloud.repository.KubeConfigsRepository;
import cn.newdt.cloud.repository.PrometheusClient;
import cn.newdt.cloud.repository.impl.FabricKubeClientFactory;
import cn.newdt.cloud.sched.CronTriggerMeta;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.cni.BocCniHelperFactory;
import cn.newdt.cloud.service.cni.CalicoCniHelperFactory;
import cn.newdt.cloud.service.cni.KubeConfigListener;
import cn.newdt.cloud.service.csi.CSILoader;
import cn.newdt.cloud.service.csi.CSInterface;
import cn.newdt.cloud.service.sched.impl.NodeWatch;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.ConfigManageVO;
import cn.newdt.cloud.vo.KubeClusterCardVO;
import cn.newdt.cloud.vo.KubeConfigVO;
import cn.newdt.commons.bean.UserInfo;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.exception.CustomIOException;
import cn.newdt.commons.utils.AsymmetricEncryptionUtil;
import cn.newdt.commons.utils.UserUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableList;
import com.google.gson.Gson;
import io.fabric8.kubernetes.client.KubernetesClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.SchedulerException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.yaml.snakeyaml.Yaml;

import java.io.IOException;
import java.math.BigInteger;
import java.net.InetAddress;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.newdt.cloud.constant.ActionEnum.*;
import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgCategory.*;
import static cn.newdt.cloud.constant.DatasourceConstant.*;
import static cn.newdt.cloud.utils.CustPreconditions.checkState;

/**
 * KubeClusterConfig服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/03/26 12:50
 */
@Service
@Slf4j
public class KubeConfigServiceImpl implements KubeConfigService, InitializingBean {

    @Value("${kubeConfig.boc_cni_port:9013}")
    private int BOC_CNI_PORT;
    @Value("${kubeConfig.agent_port:55233}")
    private String AGENT_PORT;

    private List<KubeConfigListener> listeners = new ArrayList<>();
    @Autowired
    private KubeClientService kubeClientService;
    @Autowired
    private KubeConfigMapper kubeConfigMapper;
    @Autowired
    private KubeConfigsRepository kubeConfigsRepository;
    @Autowired
    private MetricService metricService;
    @Autowired
    private IpPoolConfigService ipPoolConfigService;
    @Autowired
    private LoadBalancerIpPoolConfigService lbIpPoolConfigService;
    @Autowired
    private NodePortService nodePortService;
    @Autowired
    PrometheusClient prometheusClient;
    @Autowired
    private CloudQuotaService resourceQuotaService;
    @Autowired
    private IpService ipService;
    @Autowired
    private BackupTimerMapper backupTimerMapper;
    @Autowired
    private CloudAppService cloudAppService;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private CloudZoneService cloudZoneService;
    @Autowired
    private SSHUtil sshUtil;
    @Autowired
    private ResourceChangeHisService resourceChangeHisService;
    @Autowired
    private ResourceChangeHisMapper resourceChangeHisMapper;
    @Autowired
    private AccessManagementService accessManagementService;

    /**
     * Map[K,V] K=AppKind, V=Image@Version
     */
    private final Map<String, Map<String, String>> imageMap = new HashMap<>();

    public KubeConfigServiceImpl() {

    }

    @Override
    public List<KubeConfig> loadAll() {
        List<KubeConfig> kccs = new LinkedList<>();
        KubeConfig kcc;
        Gson gson = new Gson();
        Timestamp time = new Timestamp(System.currentTimeMillis());
        for (io.kubernetes.client.util.KubeConfig each : kubeConfigsRepository.load()) {
            Map clusterMap = (Map) each.getClusters().get(0);
            kcc = new KubeConfig();
            //"clusters": [{
            //		"cluster": {
            //			"certificate-authority-data": "11",
            //			"server": "https://192.168.2.236:6443"
            //		},
            //		"name": "kubernetes"
            //	}],
            kcc.setName((String) clusterMap.get("name"));
            // fixme kubeconfig file可以包含多个context, 我们的kube_config应该与context一一对应
            kcc.setServer((String) ((Map) clusterMap.get("cluster")).get("server"));
            kcc.setCurrentContext(each.getCurrentContext());
            kcc.setConfig(gson.toJson(each));

            try (KubernetesClient client = kubeClientService.getClient(kcc)) {
                final String gitVersion = client.getVersion().getGitVersion();
                if (gitVersion.contains("v")) {
                    kcc.setVersion(gitVersion.substring(gitVersion.indexOf("v") + 1));
                } else {
                    kcc.setVersion(gitVersion);
                }
            } catch (Exception e) {
                log.error("KubeServer:{},error:{}", kcc.getServer(), e.getMessage());
            }
            kcc.setUpdateTime(time);
            kcc.setInsertTime(time);
            kccs.add(kcc);
        }
        // 配置以server条件更新kubeConfig，否则新增kubeConfig
        for (KubeConfig each : kccs) {
            if (update(each) == 0) {
                add(each);
            }
        }
        return kccs;
    }

    @Override
    public List<KubeConfig> listByUserSchema(String userSchema) {
        CustPreconditions.checkState(!StringUtils.isEmpty(userSchema), "Schema not be empty");
        final List<KubeConfig> kubeConfigs = kubeConfigMapper.listByMap(userSchema, CLOUD_KUBE_CONFIG_TABLE, CLOUD_TENANT, new HashMap<>(2));
        for (KubeConfig each : kubeConfigs) {
            each.setIp(RegexUtil.getIp(each.getServer()));
            each.setPort(RegexUtil.getPort(each.getServer()));
        }
        return kubeConfigs;
    }

    @Override
    public KubeConfig get(Integer id) {
        return kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, id);
    }

    @Override
    public KubeConfig getSimple(Integer id) {
        return kubeConfigMapper.getSimpleById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, id);
    }

    @Override
    public Integer add(KubeConfig kubeConfig) {
        Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
        kubeConfig.setInsertTime(timestamp);
        Integer i = kubeConfigMapper.add(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, kubeConfig);
        for (KubeConfigListener listener : listeners) {
            listener.update(kubeConfig);
        }
        return i;
    }

    @Override
    public void addList(List<KubeConfig> kubeConfigs) {
        kubeConfigMapper.addList(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, kubeConfigs);
    }

    @Override
    public String getCNIType(Integer kubeId) {
        KubeConfig config = kubeConfigMapper.getById(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_KUBE_CONFIG_TABLE, kubeId);
        String cniType = config.getCniType();
        return cniType;
    }

    @Override
    public CSInterface getCSInterface(Integer kubeId, String kind) {
        return get(kubeId).getCSI(kind);
    }

    @Override
    public String getServiceType(Integer kubeId) {
        return get(kubeId).getServiceType();
    }

    @Override
    public KubeConfig findOneByServer(String k8sHost) {
        return kubeConfigMapper.getByServer(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, k8sHost);
    }

    @Autowired @Lazy
    private NodeService nodeService;
    @Override
    @ResourceView
    public List<KubeConfigVO> getKubeOwned(PageDTO pageDTO) {
        return findEnabledVO(pageDTO);
    }

    private List<KubeConfigVO> findEnabledVO(PageDTO pageDTO) {
        pageDTO.getCondition().put("state", CloudAppConstant.KubeState.ENABLED);
        List<KubeConfig> kubes = kubeConfigMapper.listByMap(UserUtil.getSchema(), CLOUD_KUBE_CONFIG_TABLE, CLOUD_QUOTA, pageDTO.getCondition());
        List<KubeConfigVO> vos = kubes.parallelStream().map(s -> {
            KubeConfigVO o = new KubeConfigVO();
            BeanUtils.copyProperties(s, o);
            o.setIp(RegexUtil.getIp(s.getServer()));
            o.setPort(RegexUtil.getPort(s.getServer()));
            if (StringUtils.isNotEmpty(s.getCsiType())) {
                o.setStoragePlugin(Arrays.asList(s.getCsiType().split(",")));
            }
            return o;
        }).collect(Collectors.toList());
        return vos;
    }

    @Override
    @ResourceView
    public List<KubeClusterCardVO> getKubeClusterCardInfo(PageDTO pageDTO) {
        if (pageDTO == null) {
            pageDTO = new PageDTO();
        }
        pageDTO.getCondition().put("state", CloudAppConstant.KubeState.ENABLED);
        List<KubeConfig> kubes = kubeConfigMapper.listByMap(UserUtil.getSchema(), CLOUD_KUBE_CONFIG_TABLE, CLOUD_QUOTA, pageDTO.getCondition());
        return kubes.parallelStream()
                .map(convertFunction())
                .collect(Collectors.toList());
    }

    private Function<KubeConfig, KubeClusterCardVO> convertFunction() {
        return s->{
            KubeClusterCardVO vo = new KubeClusterCardVO();
            vo.setName(s.getName());
            vo.setVersion(s.getVersion());
            vo.setId(s.getId());
            // get k8s node info
            int node = 0, nodeNotReady = 0, controlPane = 0, controlPaneNotReady = 0;
            try {
                List<NodeDTO> nodes = kubeClientService.get(vo.getId()).listNodes();
                for (NodeDTO dto : nodes) {
                    if ("master".equalsIgnoreCase(dto.getRole())) {
                        controlPane++;
                        if (!dto.isReady()) {
                            controlPaneNotReady++;
                        }
                    } else {
                        node++;
                        if (!dto.isReady()) {
                            nodeNotReady++;
                        }
                    }
                }
                vo.setNode(node);
                vo.setNodeNotReady(nodeNotReady);
                vo.setControlPane(controlPane);
                vo.setControlPaneNotReady(controlPaneNotReady);
                // get k8s hardware info
                Map<String, Double> metrics = metricService.queryClusterMetric(vo.getId());
                vo.setCpu(metrics.get("cpu"));
                vo.setCpuUsage(metrics.get("cpu_usage"));
                vo.setMemoryUsage(metrics.get("memory_usage"));
                vo.setMemory(metrics.get("memory"));
                vo.setDisk(metrics.get("disk"));
                vo.setDiskUsage(metrics.get("disk_usage"));
            } catch (Exception e) {
                log.error("failed get info of k8s cluster: {}, error: {}", s, e.getMessage());
            }
            return vo;
        };
    }

    @Override
    public List<KubeConfig> list(Map<String, Object> condition) {
        if (condition == null)
            condition = Collections.emptyMap();
        if (condition.get("labelMap") != null)  {
            condition.put("labelMap", JsonUtil.toObject(condition.get("labelMap").toString(), new TypeReference<Map>(){
            }));
        }
        List<KubeConfig> kubeConfigs = kubeConfigMapper.listAllColumnByMap(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, condition);
        // 设置zone属性
        List<CloudZone> zoneList = cloudZoneService.list(null);
        Optional<CloudZone> defaultZone = zoneList.stream().filter(zone -> zone.isDefaultZone()).findFirst();
        for (KubeConfig kubeConfig : kubeConfigs) {
            if (kubeConfig.getZoneId() != null) {
                for (CloudZone cloudZone : zoneList) {
                    if (kubeConfig.getZoneId() == cloudZone.getId())
                        kubeConfig.setZone(cloudZone.getName());
                }
            } else {
//                if (defaultZone.isPresent()) {
//                    kubeConfig.setZoneId(defaultZone.get().getId());
//                    kubeConfig.setZone(defaultZone.get().getName());
//                }
            }
        }
        return kubeConfigs;
    }

    @Override
    public Integer delete(Integer id) {
        return kubeConfigMapper.deleteById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, id);
    }

    @Override
    public Integer update(KubeConfig kubeConfig) {
        kubeConfig.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
        int i = 0;
        if ((i = kubeConfigMapper.updateSelective(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, kubeConfig)) > 0) {
            for (KubeConfigListener listener : listeners) {
                listener.update(get(kubeConfig.getId()));
            }
        }
        return i;
    }

    @Override
    // todo ip历史信息集群列表页调用这个接口，但不需要查询全部字段
    public PageInfo listPage(PageDTO pageDTO) {
        List<KubeConfig> results;
        // 为空查所有
        UserInfo userInfo = UserUtil.getAsyncUserinfo();
        Map<String, Object> condition = pageDTO == null ? new HashMap<>() : pageDTO.getCondition();
        if (condition.get("labelMap") != null)  {
            condition.put("labelMap", JsonUtil.toObject(condition.get("labelMap").toString(), new TypeReference<Map>(){
            }));
        }
        //是否需要健康检查
        boolean checkHealth = true;
        if (condition.get("isCheckHealth") != null && condition.get("isCheckHealth").equals(false)) {
            checkHealth = false;
        }
        boolean queryDefaultZone = Optional.ofNullable(condition.get("zoneId")).filter(id -> Integer.parseInt(id + "") == CloudZone.defaultID).isPresent();
        if (queryDefaultZone)
            condition.remove("zoneId");
        results = kubeConfigMapper.listAllColumnByMap(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, condition);
        if (queryDefaultZone)
            results = results.stream().filter(kubeConfig -> kubeConfig.getZoneId() == CloudZone.defaultID)
                    .collect(Collectors.toList());
        PageInfo<ConfigManageVO> pageInfo = PageUtil.page2PageInfo(
                results, ConfigManageVO.class, convertor(userInfo, checkHealth));
        return pageInfo;
    }

    private Function<KubeConfig, ConfigManageVO> convertor(UserInfo userInfo, boolean checkHealth) {
        return s -> {
            ConfigManageVO vo = null;
            try {
                if (userInfo!=null) {
                    UserUtil.setAsyncUserInfo(userInfo);
                }
                int kubeId = s.getId();
                vo = new ConfigManageVO();
                BeanUtils.copyProperties(s, vo);
                vo.setAddedWay(StringUtils.isEmpty(s.getConfig()) ? StringUtils.isEmpty(s.getToken()) ? "" : CloudAppConstant.KubeAddWay.TOKEN :
                        CloudAppConstant.KubeAddWay.KUBECONFIG);
                vo.setConfig(s.getConfig());
                vo.setToken(s.getToken());

                //判断 Service 类型进行填充
                if (CloudAppConstant.ServiceType.LOAD_BALANCER.equals(s.getServiceType())) {
                    // 填充loadbalancer ip 区间字段
                    List<LoadBalancerIpPoolConfig> lbIpPoolConfigs = lbIpPoolConfigService.listByKubeId(kubeId);
                    List<String> lbIpRangeList = lbIpPoolConfigs.stream().map(LoadBalancerIpPoolConfig::getCidr).collect(Collectors.toList());
                    vo.setLbIPConfigs(lbIpRangeList);
                } else if (CloudAppConstant.ServiceType.NODE_PORT.equals(s.getServiceType())) {
                    // 填充 nodePort 区间字段
                    List<CloudNodeportConfig> portConfig = nodePortService.listByKubeId(kubeId);
                    List<String> portConfigList = portConfig.stream()
                            .map(o -> o.getMin() + "-" + o.getMax()).collect(Collectors.toList());
                    vo.setPortConfig(portConfigList);
                }
                //添加 Service 的使用和全部数量情况
                vo.setUsedServiceCount(accessManagementService.serviceCountByKubeId(kubeId, 1));
                vo.setTotalServiceCount(accessManagementService.serviceCountByKubeId(kubeId, 3));

                // 填充 ip 区间字段
                Map<String, List<String>> ipConfigs = ipPoolConfigService.listByKubeId(kubeId).stream()
                        .collect(Collectors.groupingBy(o -> o.getAppType(), Collectors.mapping(o -> o.getCidr(), Collectors.toList())));
                vo.setIpConfigMap(ipConfigs);

                if (checkHealth) {
                    Map<String, Boolean> health = checkCluster(s).entrySet().stream()
                            .collect(Collectors.toMap(e -> e.getKey(), e -> StringUtils.isEmpty(e.getValue())));
                    vo.setHealth(new TreeMap<>(health));
                }
            } catch (Exception e) {
                log.error("list kubeconfig error", e);
            }
            return vo;
        };
    }

    @Autowired
    private MultiplePrometheusService multiplePrometheusService;
    @Override
    public void afterPropertiesSet() throws Exception {
        // register listener todo
        listeners.add(BocCniHelperFactory.getInstance());
        listeners.add(CalicoCniHelperFactory.getInstance());
        listeners.add(multiplePrometheusService);
        // initialize listener's cache
        List<KubeConfig> list = list(Collections.emptyMap());
        for (KubeConfig kubeConfig : list) {
            for (KubeConfigListener listener : listeners) {
                listener.update(kubeConfig);
            }
        }
    }

    @Override
    public Integer addKubeToken(KubeTokenDTO kubeTokenDTO) {
        KubeConfig kubeConfig = kubeTokenDTO.convertToKubeConfig();
        Timestamp now = new Timestamp(System.currentTimeMillis());
        kubeConfig.setInsertTime(now);
        kubeConfig.setUpdateTime(now);
        return add(kubeConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @ResourceChangeLog(action = ADD_CLUSTER)
    public void createOrReplaceConfigManage(ConfigManageVO vo, MultipartFile kubeConfigFile) {
        if (kubeConfigFile != null) {
            try {
                byte[] bytes = kubeConfigFile.getBytes();
                String configContent = new String(bytes, StandardCharsets.UTF_8);
                vo.setConfig(configContent);
            } catch (IOException e) {
                throw new CustomIOException("读取配置文件失败");
            }
        }

        validate(vo);

        // insert, 设置主键id
        if (vo.getId() == null) {
            vo.setState(CloudAppConstant.KubeState.DISABLED);
            try {
                add(vo);
            } catch (Exception e) {
                log.error("", e);
                throw new CustomException(600, "添加失败");
            }
        } else {
            // 重置连接 更新token
            KubeConfig old = get(vo.getId());
            if (!Objects.equals(old.getConfig(), vo.getConfig())
                    || !Objects.equals(old.getToken(), vo.getToken())) {
//                OkHttpClientHolder.clean(vo);
                KubeClientManager.instance(new FabricKubeClientFactory()).cleanClient(vo);
                log.info("old http client cleaned");
            }
        }

        // 检查集群组件, 集群状态为启用时严格校验, 否则忽略错误
        Map<String, String> statusMap = checkCluster(vo);
        if (vo.getState() != null && vo.getState() == CloudAppConstant.KubeState.ENABLED) {
            Boolean noError = statusMap.values().stream().map(StringUtils::isEmpty).reduce(true, (o, a) -> o & a);
            if (!noError) {
//                throw new CustomException(600, formatErrMsg(statusMap));
            }
        }

        try {
            if (!CollectionUtils.isEmpty(vo.getIpConfigMap()))
                configureIpPool(vo.getIpConfigMap(), vo.getId()); // fixme: insert ip only if kubeConfig server is not empty
        } catch (Exception e) {
            log.error("[config manager]-configure ip pool", e);
            throw new CustomException(600, "IP池添加时系统错误");
        }
        try {
            if (!CollectionUtils.isEmpty(vo.getPortConfig()))
                configureNodePort(vo.getPortConfig(), vo.getId());
        } catch (Exception e) {
            log.error("[config manager]-configure node port", e);
            throw new CustomException(600, "端口范围添加时系统错误");
        }
        try {
            if (!CollectionUtils.isEmpty(vo.getLbIPConfigs()))
                configureLoadBalancerIpPool(vo.getLbIPConfigs(), vo.getId());
        } catch (Exception e) {
            log.error("[config manager]-configure node port", e);
            throw new CustomException(600, "loadbalancer IP区间添加时系统错误");
        }

        update(vo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @ResourceChangeLog(action = MODIFY_CLUSTER)
    public void updateConfigManage(ConfigManageVO vo) {
        // ip port是否有追加
        Integer id = vo.getId();

        KubeConfig kubeConfig = get(id);
        // copy kubeConfig to vo
        if ("{}".equals(kubeConfig.getCsiType())) kubeConfig.setCsiType(null);
        BeanUtil.copyWithoutOverwrite(kubeConfig, vo);
        createOrReplaceConfigManage(vo, null);
    }

    private void validate(ConfigManageVO vo) {
        checkState(StringUtils.isNotEmpty(vo.getName()), "集群名称不能为空");
//        if (vo.getPortConfig() != null)
//            checkState(!Range.isCoincide(vo.getPortConfig()), "Port范围重合");
//        if (vo.getIpConfigMap() != null)
//            for (List<String> value : vo.getIpConfigMap().values()) {
//                checkState(!Range.isCoincide(value), "IP范围重合");
//            }
        // 去掉提交数据中的空格
        if (vo.getIpConfigMap() != null) {
            for (Map.Entry<String, List<String>> entry : vo.getIpConfigMap().entrySet()) {
                List<String> collect = entry.getValue().stream()
                        .map(e -> e.replaceAll("\\s", ""))
                        .collect(Collectors.toList());
                vo.getIpConfigMap().put(entry.getKey(), collect);
            }
        }
        if (vo.getPortConfig() != null) {
            vo.setPortConfig(vo.getPortConfig().stream().map(e -> e.replaceAll("\\s", ""))
                    .collect(Collectors.toList())
            );
        }
        checkURL(vo.getPrometheusServer());
        checkURL(vo.getAgentServer());
        checkURL(vo.getCniApiServer());
        checkURL(vo.getGrafanaServer());

        if (vo.getIpConfigMap() != null && !vo.getIpConfigMap().isEmpty() && StringUtils.isEmpty(vo.getCniType())) {
            throw new CustomException(600, "未配置网络类型");
        }
    }

    private void checkURL(String URL) {
        if (StringUtils.isNotEmpty(URL))
            try {
                new URL(URL);
            } catch (MalformedURLException e) {
                throw new CustomException(600, URL + e.getMessage());
            }
    }

    @Override
    public Map<String, Collection<String>> options(Integer kubeId) {
        Map<String, Collection<String>> map = new HashMap<>();
        List<String> cni = new ArrayList<>();
        map.put("cni", cni);
        for (CNIEnum value : CNIEnum.values()) {
            cni.add(value.toString());
        }


        String csiInstalled = sysConfigService.findOne(CLUSTER_MANAGEMENT, "csiInstalled");
        if (csiInstalled != null) {
            map.put("csi", Arrays.asList(csiInstalled.split(",")));
        } else {
            map.put("csi", Collections.emptyList());
        }

        LinkedHashSet<String> appKind  = new LinkedHashSet<>();
        map.put("appKind", appKind);
        String licenseType = UserUtil.getCurrentUser().getDbtypes();
        if (licenseType != null) {
            Set<String> licenseProduct = Arrays.stream(licenseType.split(",")).map(type -> licenseProductMap.get(type.toUpperCase()))
                    .collect(Collectors.toSet());
            for (AppKind value : AppKind.values()) {
                if (licenseProduct.contains(value.getProduct()))
                    appKind.add(value.getProduct());
            }
        }
        return map;
    }


    /**
     * license 中数据库类型和AppKind enum实例的映射关系，不包含关联应用类型 如zk
     */
    public static final Map<String, List<AppKind>> licenseAppKindMap = new HashMap<>();

    public static final Map<String, String> licenseProductMap = new HashMap<>();

    static {
//        licenseAppKindMap.put("ORACLE", null);
//        licenseAppKindMap.put("DB2", null);
        licenseAppKindMap.put("MYSQL", ImmutableList.of(AppKind.MYSQL_HA, AppKind.MYSQL_MGR));
//        licenseAppKindMap.put("SQLSERVER", );
//        licenseAppKindMap.put("HANA", );
        licenseAppKindMap.put("POSTGRESQL", Collections.singletonList(AppKind.PostgreSQL));
//        licenseAppKindMap.put("GBase 8s", );
        licenseAppKindMap.put("MONGODB", ImmutableList.of(AppKind.MongoDB_Cluster, AppKind.MongoDB));
        licenseAppKindMap.put("REDIS", ImmutableList.of(AppKind.Redis, AppKind.Redis_Cluster));
//        licenseAppKindMap.put("SHINDB", );
//        licenseAppKindMap.put("OCEANBASE", );
        licenseAppKindMap.put("OPENGAUSS", ImmutableList.of(AppKind.OpenGauss));
//        licenseAppKindMap.put("DAMENG", );
//        licenseAppKindMap.put("TIDB", );
        licenseAppKindMap.put("ELASTICSEARCH", ImmutableList.of(AppKind.Elasticsearch));
        licenseAppKindMap.put("KAFKA", ImmutableList.of(AppKind.Kafka));
        licenseAppKindMap.put("ROCKETMQ", ImmutableList.of(AppKind.Broker));
        licenseAppKindMap.put("FLINK", ImmutableList.of(AppKind.Flink));
        licenseAppKindMap.put("CLICKHOUSE", ImmutableList.of(AppKind.Clickhouse));
        licenseAppKindMap.put("TIDB", ImmutableList.of(AppKind.TIDB));
        licenseAppKindMap.put("DAMENG", ImmutableList.of(AppKind.Dameng));
        licenseAppKindMap.put("VASTBASE", ImmutableList.of(AppKind.Vastbase));

        for (String key : licenseAppKindMap.keySet()) {
            licenseProductMap.put(key, licenseAppKindMap.get(key).get(0).getProduct());
        }
    }





    @Override
    public ConfigManageVO getDetail(int id) {
        KubeConfig kubeConfig = get(id);
        ConfigManageVO vo = convertor(null, false).apply(kubeConfig);
        return vo;
    }

    /**
     * 查询 k8s 节点
     * @param id
     * @param isSchedulable :不传时为：null 查询所有
     *                      不为null时，则根据该参数查询
     * @return
     */
    @Override
    public List<NodeDTO> getK8sNodes(int id, Boolean isSchedulable) {
        try {
            List<NodeDTO> nodeDTOS = kubeClientService.get(id).listNodes();
            nodeDTOS = null == isSchedulable ? nodeDTOS : nodeDTOS.stream().filter(dto -> isSchedulable.equals(dto.isSchedulable())).collect(Collectors.toList());
            nodeDTOS = nodeDTOS.stream().sorted(Comparator.comparing(NodeDTO::getRole)).collect(Collectors.toList());
            return nodeDTOS;
        } catch (Exception e) {
            log.error("", e);
            return Collections.emptyList();
        }
    }

    @Override
    @ResourceChangeLog(action = DISABLE_CLUSTER)
    public String disableConfiguredCluster(int id) {
        if (updateState(id, CloudAppConstant.KubeState.DISABLED)>0)
            return "禁用成功";
        return "禁用失败";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @ResourceChangeLog(action = ENABLE_CLUSTER)
    public String enableConfiguredCluster(int id) {
        ConfigManageVO vo = convertor(null, false).apply(get(id));
        Map<String, String> checkResult = checkCluster(vo);
        Boolean noError = checkResult.values().stream().map(StringUtils::isEmpty).reduce(true, (o, a) -> o & a);
        if (!noError) {
            String msg = formatErrMsg(checkResult);
            throw new CustomException(600, msg);
        }
        if (updateState(id, CloudAppConstant.KubeState.ENABLED) > 0) {
            initializeOnK8s(vo);
            return "启用成功";
        }
        return "启用失败";
    }

    @Override
    public void checkHealth(ConfigManageVO vo) {

        Map<String, String> statusMap = checkCluster(vo);

        Boolean noError = statusMap.values().stream().map(StringUtils::isEmpty).reduce(true, (o, a) -> o & a);
        if (!noError) {
            throw new CustomException(600, formatErrMsg(statusMap));
        }
    }

    @Override
    public long queryCsiAllocatable(Integer kubeId, NodeDTO dto, String csiType) {
//        KubeConfig kubeConfig = get(kubeId);
//        switch (csiType) {
//            case TOPOLVM:
//                return Long.parseLong(dto.getAnnotations().get("capacity.topolvm.cybozu.com/00default")); // todo config
//            case CARINA:
//                // see https://github.com/carina-io/carina/blob/main/docs/user-guide.md
//                // todo 当有新的pv创建成功后会变更node.status.allocatable，这变更会有点延迟
//                // disk-type: ssd / hhd
//                Quantity ssdCap = dto.getAllocatables().get("carina.storage.io/carina-vg-ssd");
//                Quantity hhdCap = dto.getAllocatables().get("carina.storage.io/carina-vg-hhd");
////                ssdCap = Pattern.compile("[KMGTkmgt]i?").matcher(ssdCap).find() ? ssdCap : ssdCap + "Gi";
//                return MetricUtil.getResourceLongValue((MetricUtil.getLongValueCountSI(ssdCap == null ? null : ssdCap.toString())
//                        + MetricUtil.getLongValueCountSI(hhdCap == null ? null : hhdCap.toString())) + "Gi");
//            case HOSTPATH:
//                GoAgentClient.Response res = new GoAgentClient(kubeConfig.getAgentServer()).execCmd("df -h | awk '$6==\"/\"{print $4}'");
//                String data = res.getData();
//                return MetricUtil.getLongValue(data);
//            default:
                return 0;
//        }
    }

    private String formatErrMsg(Map<String, String> checkResult) {
        return checkResult.values().stream().filter(StringUtils::isNotEmpty)
                .collect(Collectors.joining("<br>"));
    }

    /**
     * 校验集群配置, 校验项有前后依赖关系
     * @param kubeConfig
     * @return Map[String, String]. key为校验项,包括[k8s, NodePort, Agent Server, CNI, CSI, IP pool, Prometheus, LoadBalancer],
     * value为校验错误提示, 为空表示校验通过
     */
    private Map<String, String> checkCluster(KubeConfig kubeConfig) {
        FutureService futureService = new FutureService();
        // <ensureType, <code|msg, codeValue|msg>>
        Map<String, String> statusMap = new ConcurrentHashMap<>();

        if (kubeConfig == null){
            return statusMap;
        }

        String apiServerHealth = checkApiServer(kubeConfig);
        statusMap.put("k8s", apiServerHealth);

        //根据 service 类型进行不同的属性校验
        if (CloudAppConstant.ServiceType.LOAD_BALANCER.equals(kubeConfig.getServiceType())){
            futureService.submit(()-> statusMap.put("LoadBalancer IP区间", checkLoadBalancerIpConfig(kubeConfig)));
        } else if (CloudAppConstant.ServiceType.NODE_PORT.equals(kubeConfig.getServiceType())) {
            futureService.submit(() -> statusMap.put("端口", checkNodePort(kubeConfig)));
        }

        futureService.submit(()-> statusMap.put("存储", checkCsi(kubeConfig)));

        futureService.compose(
//                ()-> statusMap.put("Agent server", checkAgentServer(kubeConfig)),
                ()-> statusMap.put("网络", checkCni(kubeConfig)), // check cni after check agent
                ()-> statusMap.put("IP", checkIpConfig(kubeConfig))); // check ip pool after check cni.

        futureService.submit(()-> statusMap.put("Prometheus", checkPrometheus(kubeConfig)));
        futureService.submit(() -> statusMap.put("Grafana", checkGrafana(kubeConfig)));
        futureService.submit(()-> statusMap.put("Operator", checkAppDeployEnv(kubeConfig)));

        futureService.submit(() -> statusMap.put("Configmap", checkBackupStorageConfigmap(kubeConfig)));

        try {
            futureService.await();
        } catch (CompletionException e) {
            log.error("", e);
        }

        return statusMap;
    }

    private String checkBackupStorageConfigmap(KubeConfig kubeConfig) {
        //获取operator的namespace，由于所有应用的operator都处于同一个namespace，所以取其一
        GlobalConfigMap globalCM = GlobalConfigMap.getInstance();
        if (kubeClientService.get(kubeConfig).getConfigMap(globalCM.getCM_NAME(), globalCM.getCM_NAMESPACE()) == null) {
            return "未初始化备份存储ConfigMap";
        }
        return "";
    }

    private String checkLoadBalancerIpConfig(KubeConfig vo) {
        List<LoadBalancerIpPoolConfig> exist = lbIpPoolConfigService.listByKubeId(vo.getId());
        // insert or update
        if (vo instanceof ConfigManageVO) {
            ConfigManageVO cmVO = (ConfigManageVO) vo;
            List<String> addedLBIpConfig = cmVO.getLbIPConfigs();
            if (CollectionUtils.isEmpty(addedLBIpConfig) && exist.isEmpty()) {
                // "loadbalancer IP区间未设置"
                return  "loadbalancer ID 未设置";
            }

            // 校验loadbalancer IP参数是否重合, 交叉检验
            List<String> ipRanges = new ArrayList<>(addedLBIpConfig);
            if (Range.isCoincide(ipRanges)) {
                // "loadbalancer IP区间重合"
                return "loadbalancer ID 重合";
            }
        }
        return "";
    }

    private int updateState(int id, byte enabled) {
        KubeConfig updatable = new KubeConfig();
        updatable.setId(id);
        updatable.setState(enabled);
        return update(updatable);
    }

    /**
     * @param ipConfigMap Map< appKind, ipList>, 按应用类型配置网络. 包含多端ip范围,
     *                    ip范围格式: 博云 start-end ,calico cidr
     */
    private void configureIpPool(Map<String, List<String>> ipConfigMap, Integer kubeConfigId) {
        for (Map.Entry<String, List<String>> entry : ipConfigMap.entrySet()) {
            String appKind = entry.getKey();
            for (String ipRange : entry.getValue()) {
                IpPoolConfig ipConfig = new IpPoolConfig();
                ipConfig.setKubeId(kubeConfigId);
                ipConfig.setAppType(appKind);
                ipConfig.setCidr(ipRange);
                ipConfig.setNetwork("default"); // todo cni插件定义的 network name
                if (ipPoolConfigService.add(ipConfig) < 0) {
                    throw new CustomException(600, "ip配置添加失败");
                }
            }
        }
    }
    /**
     *  ipRanges : loadbalancer ip范围
     *
     */
    private void configureLoadBalancerIpPool(List<String> ipRanges, Integer kubeConfigId) {
        Pattern pattern = Pattern.compile("(\\d+)-(\\d+)");
        for (String ipRange : ipRanges) {
            Matcher matcher = pattern.matcher(ipRange);
            if (!matcher.find()) {
                throw new CustomException(600, "格式错误, " + ipRange);
            }
            LoadBalancerIpPoolConfig lbIpConfig = new LoadBalancerIpPoolConfig();
            lbIpConfig.setCidr(ipRange);
            lbIpConfig.setKubeId(kubeConfigId);
            if (lbIpPoolConfigService.add(lbIpConfig) < 0) {
                throw new CustomException(600, "lb ip配置添加失败");
            }
        }
    }

    /**
     * @param portConfig   多个port范围配置
     * @param kubeConfigId 集群id
     */
    private void configureNodePort(List<String> portConfig, Integer kubeConfigId) {
        Pattern pattern = Pattern.compile("(\\d+)-(\\d+)");
        for (String portRange : portConfig) {
            Matcher matcher = pattern.matcher(portRange);
            if (!matcher.find()) {
                throw new CustomException(600, "格式错误, " + portRange);
            }
            int port1 = Integer.parseInt(matcher.group(1));
            int port2 = Integer.parseInt(matcher.group(2));
            nodePortService.addNodePortRange(kubeConfigId, Math.min(port1, port2), Math.max(port1, port2));
        }
    }

    private String connectionTest(KubeConfig vo) {
        try {
            return new FabricKubeClientFactory().createClient(vo).getVersion();
        } catch (Exception e) {
            log.error("", e);
            throw new CustomException(600, "尝试连接集群失败 " + e.getMessage());
        }
    }

    private static class Range {
        String rangeStr;
        String little;
        String big;
        BigInteger start;
        BigInteger end;
        /** 判断range重合时，与当前range重合的另一个range, 记录以给出前端提示 */
        Range coincide;

        public Range(Integer start, Integer end) {
            this.start = BigInteger.valueOf(start);
            this.end = BigInteger.valueOf(end);
        }

        public Range(String rangeStr) {
            this.rangeStr = rangeStr;
            if (!rangeStr.contains(".")) { // 端口
                start = new BigInteger(rangeStr.split("-")[0]);
                end = new BigInteger(rangeStr.split("-")[1]);
            } else {
                try {
                    if (rangeStr.contains("-")) { // -分隔的ip段
                        little = rangeStr.split("-")[0];
                        big = rangeStr.split("-")[1];
                    } else { // cidr表示的ip范围
                        CIDRUtil cidr = new CIDRUtil(rangeStr);
                        little = cidr.getStartAddress();
                        big = cidr.getEndAddress();
                    }
                    // ip 用integer表示便于比较
                    this.start = new BigInteger(1, InetAddress.getByName(little).getAddress());
                    this.end = new BigInteger(1, InetAddress.getByName(big).getAddress());
                } catch (UnknownHostException ignored) {
                }
            }
        }

        public boolean isCovered(List<String> ipConfigStrs) {
            for (String ipConfigStr : ipConfigStrs) {
                if (isCovered(new Range(ipConfigStr))) {
                    return true;
                }
            }
            return false;
        }

        /**
         * @return true if 重合
         */
        private boolean isCovered(Range other) {
            // 不重合的情况 other表示的ip区间在当前ip区间左或右, 其余情况则区间相交
            boolean isCover = !(other.start.compareTo(this.end) > 0 || other.end.compareTo(this.start) < 0);
            if (isCover) coincide = other; // 记录重合的另一范围以给前端提示
            return isCover;
        }

        /**
         * 校验两个字符串表示的rangeList是否重合. string 用-分隔或为cidr格式
         *
         * @param cur  参照的rangeList
         * @param apnd 待校验rangeList
         * @param e error holder
         */
//        static boolean isCoincide(List<String> cur, List<String> apnd, CustomException e) {
//            for (String s : apnd) {
//                Range range ;
//                if ((range = new Range(s)).isCovered(cur)) {
//                    e.setStatus(600);
//                    e.setData(range.rangeStr + "," + range.coincide.rangeStr); // 返回判重失败的两个range
//                    return true;
//                }
//            }
//            return false;
//        }

        /**
         * 校验字符串表示的rangeList是否重合
         */
        static boolean isCoincide(List<String> list) {
            List<String> cur = new ArrayList<>();
            for (String s : list) {
                if (cur.isEmpty()) cur.add(s);
                else if (!new Range(s).isCovered(cur)) {
                    cur.add(s);
                } else
                    return true;
            }
            return false;
        }

        public int count() {
            return end.subtract(start).intValue();
        }
    }

    private String checkApiServer(KubeConfig vo) {
        if (StringUtils.isBlank(vo.getToken()) && StringUtils.isBlank(vo.getConfig())) {
            return "未填写连接集群的token或kubeconfig";
        }

        if (StringUtils.isNotBlank(vo.getConfig())) {
            connectionInfoFromKubeConfig(vo);
        } else if (StringUtils.isNotBlank(vo.getToken())){
            if (StringUtils.isBlank(vo.getServer())) {
                return "k8s master IP为空";
            }
        }

        if (StringUtils.isNotEmpty(vo.getServer())) {
            // 同一集群不能重复纳管
            List<KubeConfig> exist = kubeConfigMapper.listAllColumnByMap(SCHEMA, CLOUD_KUBE_CONFIG_TABLE,
                    Collections.singletonMap("server", vo.getServer()));
            if (exist.size() > 1) {
                return "集群重复纳管，该IP集群已被纳管为:" + exist.stream().filter(e->!e.getName().equals(vo.getName())).map(e->e.getName()).collect(Collectors.joining(","));
            }
            try {
                if (!exist.isEmpty()) { // 新纳管集群校验时为空
                    if (StringUtils.isNoneBlank(vo.getToken()) && !Objects.equals(vo.getToken(), exist.get(0).getToken())) {
                        vo.setConfig("");
                    }
                    if (StringUtils.isNoneBlank(vo.getConfig()) && !Objects.equals(vo.getConfig(), exist.get(0).getConfig())) {
                        vo.setToken("");
                    }
                }
                vo.setVersion(Optional.of(connectionTest(vo)).get()); // 优先使用config
            } catch (Exception e) {
                log.error("测试连接失败", e);
                return "测试连接失败";
            }
        } else {
            return "未解析到集群主机";
        }

        return "";
    }

    private void connectionInfoFromKubeConfig(KubeConfig vo) {
        // read kubeConfigFile content
        try {
            String config = vo.getConfig();
            Yaml yaml = new Yaml();
            Object load = null;
            load = yaml.load(config);
            Map<String, Object> configMap = (Map) load;
            String currentContext = (String) configMap.get("current-context");
            if (currentContext == null) {
                currentContext = (String) configMap.get("currentContextName"); // 兼容config为json格式
            }
            ArrayList<Object> contexts = (ArrayList) configMap.get("contexts");
            ArrayList<Object> clusters = (ArrayList) configMap.get("clusters");
            ArrayList<Object> users = (ArrayList) configMap.get("users");
            Object preferences = configMap.get("preferences");
            io.kubernetes.client.util.KubeConfig kubeConfig = new io.kubernetes.client.util.KubeConfig(contexts, clusters, users);
            kubeConfig.setContext(currentContext);
            kubeConfig.setPreferences(preferences);

            Map<String, String> clusterMap = new HashMap<>();
            for (Object obj : clusters) {
                Map cluster = (Map) obj;
                String name = (String) cluster.get("name");
                String server = (String) ((Map) cluster.get("cluster")).get("server");
                clusterMap.put(name, server);
            }
            // 保存current context中的cluster
            Map currentCtx = null;
            for (Object obj : contexts) {
                Map map = (Map) obj;
                if (currentContext.equals(map.get("name")))
                    currentCtx = map;
            }
            String cluster = (String) ((Map) currentCtx.get("context")).get("cluster");
            vo.setServer(clusterMap.get(cluster));
            vo.setCurrentContext((String) currentCtx.get("name"));
            vo.setConfig(vo.getConfig()); // yaml format
        } catch (Exception e) {
            log.error("", e);
            throw new CustomException(600, "kubeconfig文件解析错误");
        }

    }

    // set cni_api_server, after agent_server and server is not empty
    private String checkCni(KubeConfig config) {
        if (StringUtils.isEmpty(config.getCniType())) {
            return "网络类型未设置";
        }
        if (StringUtils.isEmpty(config.getServer()))
            return "";
        // 校验并返回枚举值
        CNIEnum cniEnum ;
        try {
            cniEnum = CNIEnum.valueOf(config.getCniType());
        } catch (IllegalArgumentException e) {
            return e.getMessage();
        }
        // set field- cni_api_server
        if (cniEnum == CNIEnum.calico && StringUtils.isNotEmpty(config.getAgentServer())) {
            config.setCniApiServer(config.getAgentServer());
        }
        if (StringUtils.isEmpty(config.getCniApiServer())) {
            if (cniEnum == CNIEnum.fabric && StringUtils.isNotEmpty(config.getServer())) {
                config.setCniApiServer("http://" + RegexUtil.getIp(config.getServer()) + ":" + BOC_CNI_PORT);
            }
        }
//        if (StringUtils.isEmpty(config.getCniApiServer())) {
//            return "网络插件API Server未设置";
//        }
        // todo ensure network plugin installed
        // check cni admin tool is installed
        if (cniEnum == CNIEnum.calico) {
//            GoAgentClient client = new GoAgentClient();
//            try {
//                client.execCmd(config.getAgentServer(), CloudAppConstant.Cmd.CALICOCTL_VERSION);
//            } catch (Exception e) {
//                log.error("check cni tool error: " + e.getMessage());
//                return "网络插件安装异常";
//            }
        }
        // check cni api server is running
//        String cniApiServer = config.getCniApiServer();
//        if (!NetworkUtil.telnet(IPUtil.getIpFromUrl(cniApiServer), RegexUtil.getPort(cniApiServer))) {
//            return "cni api server " + cniApiServer + " is not running";
//        }

        return "";
    }

    private String checkCsi(KubeConfig config) {
        if (StringUtils.isEmpty(config.getCsiType()))
            return "存储插件未设置";
        Map<String, String> csiMap = new HashMap<>();
        try {
            // todo 为所有类型初始化csitype
            String csiTypeStr = config.getCsiType(); // jsonmap, key=appkind, value={name:,hostpathRoot}
            Map<String, Map<String, String>> csiTypeMapByAppKind =
                    JsonUtil.toObject(csiTypeStr, new TypeReference<Map<String, Map<String, String>>>() {});
            // ensure in installed list
            if (csiTypeMapByAppKind != null && !csiTypeMapByAppKind.isEmpty()) {
                for (Map.Entry<String, Map<String, String>> entry : csiTypeMapByAppKind.entrySet()) {
                    String csiType = entry.getValue().get("name");
                    try {
                        CSILoader.getCsi(csiType);
                    } catch (Exception e) {
                        return "存储插件未实现-" + csiType;
                    }
                    csiMap.put(entry.getKey(), csiType);
                    if (CSInterface.HOSTPATH.equals(csiType) && StringUtils.isEmpty(entry.getValue().get("hostPathRoot"))) {
                        return "hostpath根目录未设置";
                    }
                }
            } else {
                return "存储插件未设置";
            }
        } catch (IllegalArgumentException e) {
            return e.getMessage();
        }
        // ensure sc created
//        if (StringUtils.isNotEmpty(config.getServer())) {
//            KubeClient kubeClient = kubeClientService.get(config);
//            for (String csiType : csiMap.values()) {
//                CSInterface csi = CSILoader.getCsi(csiType);
//                if (csi.getType().equals(csiType))
//                    continue;
//                List<StorageClassDTO> list = kubeClient.listStorageClass();
//                Optional<StorageClassDTO> any = list.stream().filter(s -> s.getName().equals(csi.getStorageClassName())).findAny();
//                if (!any.isPresent()) {
//                    return "check csi-" + "storageClass not support:" + csi.getStorageClassName();
//                }
                // check csidriver created
//        kubeClient.listCsiDriver(any.get().getProvisioner());
//            }
//        }
        return "";
    }

    private String checkPrometheus(KubeConfig config) {
        String prometheus = config.getPrometheusServer();
        String version = null;
        if (prometheus.split("@").length > 1) {
            version = prometheus.split("@")[1];
        }
        if (StringUtils.isEmpty(prometheus) ||
                StringUtils.isEmpty((prometheus = prometheus.replaceAll("@.*", "")))) {
            return "prometheus未设置";
        }
        if (!prometheusClient.checkHealth(prometheus)) {
            return "prometheus服务无法连接";
        }
        if (!prometheusClient.getByVersion(version).isPresent()) {
            return "当前版本prometheus监控指标配置不存在";
        }
        return "";
    }

    private String checkGrafana(KubeConfig config) {
        String grafana = config.getGrafanaServer();
        if (StringUtils.isEmpty(grafana)) {
            return "grafana未设置";
        }
        //校验grafana，调用health接口
        if (!prometheusClient.checkGrafanaHealth(grafana)) {
            return "grafana服务无法连接";
        }
        return "";
    }

    private String checkIpConfig(KubeConfig vo) {

        List<IpPoolConfig> exist = ipPoolConfigService.listByKubeId(vo.getId());
        // insert or update
        if (vo instanceof ConfigManageVO) {
            ConfigManageVO cmVO = (ConfigManageVO) vo;
            Map<String, List<String>> addedIpConfig = cmVO.getIpConfigMap();
            if (CollectionUtils.isEmpty(addedIpConfig) && exist.isEmpty()) {
                return "IP区间未设置";
            }

            // 校验IP参数是否重合, 交叉检验
            List<String> ipRanges = new ArrayList<>();
            for (List<String> value : addedIpConfig.values()) {
                ipRanges.addAll(value);
            }
            // 相同集群纳管多次
//            List<String> ipRangesOfExistCluster = new ArrayList<>();
//            if (StringUtils.isNotEmpty(vo.getServer())) {
//                List<KubeConfig> sameServer = findListByServer(vo.getServer());
//                for (KubeConfig kubeConfig : sameServer) {
//                    if (Objects.equals(kubeConfig.getId(), vo.getId())) continue;
//                    List<String> ippoolOfThisKube = ipPoolConfigService.listByKubeId(kubeConfig.getId()).stream().map(o -> o.getCidr()).collect(Collectors.toList());
//                    ipRangesOfExistCluster.addAll(ippoolOfThisKube);
//                }
//            }
//            CustomException error = new CustomException(600);
//            if (Range.isCoincide(ipRanges, ipRangesOfExistCluster, error)) {
//                return "IP区间配置错误, 以下IP区间重合:" + error.getData().toString();
//            }
            // 编辑时区分出新增的部分, 并更新ipConfigMap集合
            Map<String, List<String>> curIpConfig = exist.stream()
                    .collect(Collectors.groupingBy(o -> o.getAppType(), Collectors.mapping(o -> o.getCidr(), Collectors.toList())));
            if (cmVO.getIpConfigMap() != null)
                for (Map.Entry<String, List<String>> entry : curIpConfig.entrySet()) {
                    List<String> oldRange = entry.getValue();
                    List<String> newRange = cmVO.getIpConfigMap().get(entry.getKey());
                    if (newRange != null)
                        newRange.removeAll(oldRange);// 更新集合
                    // newRange changed
                    // no need check coincide
                    // CustPreconditions.checkState(!Range.isCoincide(oldRange, newRange), "IP范围");
                }
            // 不重合则插入表. 插入逻辑移出check方法,避免校验集群时持久化脏数据

            //将判断向下移动，不然会出现数据库多次存储问题
            if (Range.isCoincide(ipRanges)) {
                return "IP区间重合";
            }
        } else {
            if (exist.isEmpty()) {
                return "IP区间未设置";
            }
        }

        // check ip池ip个数与实际管理的ip个数相等
//        Map<String, List<IpPoolConfig>> collect = exist.stream().collect(Collectors.groupingBy(c -> c.getAppType()));
//        Map<String, Integer> ipCount = ipPoolConfigService.countIpByAppKind(vo.getId());
//        Map<String, Map> result = new HashMap<>();
//        boolean compare = true;
//        for (Map.Entry<String, List<IpPoolConfig>> entry : collect.entrySet()) {
//            int count = 0;
//            for (IpPoolConfig ipPoolConfig : entry.getValue()) {
//                count += new Range(ipPoolConfig.getCidr()).count();
//            }
//            HashMap value = new HashMap();
//            value.put("expected", count);
//            value.put("managed", ipCount.get(entry.getKey()));
//            compare &= count == ipCount.get(entry.getKey());
//            result.put(entry.getKey(), value);
//        }
//        if (!compare) {
//            return "ip pool managed number not correct";
//        }

        return "";
    }

    private List<KubeConfig> findListByServer(String server) {
        return list(Collections.singletonMap("server", server));
    }

    private String checkNodePort(KubeConfig vo) {
        List<CloudNodeportConfig> list = nodePortService.listByKubeId(vo.getId());
        if (vo instanceof ConfigManageVO) {
            List<String> newPortRange = ((ConfigManageVO) vo).getPortConfig();
            if (CollectionUtils.isEmpty(newPortRange) && list.isEmpty())
                return "端口区间未设置";

            if (Range.isCoincide(newPortRange)) {
                return "端口区间重合";
            }

            // 集群纳管多次判断 fix 23073
//            List<KubeConfig> sameServer = findListByServer(vo.getServer());
//            List<String> existConfigOfExistClusters = new ArrayList<>();
//            sameServer.stream().filter(k-> !Objects.equals(k.getId(), vo.getId())).map(k -> nodePortService.listByKubeId(k.getId()))
//                            .flatMap(Collection::stream)
//                            .map(nodePortObj -> nodePortObj.getMin() + "-" + nodePortObj.getMax())
//                    .forEach(existConfigOfExistClusters::add);
//
//            CustomException error = new CustomException(600);
//            // 当前集群端口配置 与 已纳管的相同集群的端口不能重合
//            if (Range.isCoincide(newPortRange, existConfigOfExistClusters, error)) {
//                return "端口区间配置错误, 以下端口区间重合:" + error.getData().toString();
//            }

            // 编辑时区分出新增的部分, 并更新portConfig集合
            List<String> curPortConfig = list.stream().map(o -> o.getMin() + "-" + o.getMax()).collect(Collectors.toList());
            // 不重合则入库
            newPortRange.removeAll(curPortConfig);//                CustPreconditions.checkState(!Range.isCoincide(curPortConfig, newPortRange), "Port范围重合");
        } else {
            if (list.isEmpty()) {
                return "端口区间未设置";
            }
        }

//        int count = nodePortService.countById(vo.getId());
//        Integer expected = list.stream().map(o -> o.getBig() - o.getLittle()).reduce(0, (o, a) -> a += o);
//        if (expected != count) {
//            return "port number not correct";
//        }
        return "";
    }

    public String checkAgentServer(int id) {
        return checkAgentServer(get(id));
    }

    @Override
    public int removeLabels(int id, List<String> labels) {
        return kubeConfigMapper.removeLabels(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, labels, id);
    }

    @Override
    public String getName(int kubeId) {
        return kubeConfigMapper.getSimpleById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, kubeId).getName();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addNode(Integer kubeId, String masterIp, String masterPassword, String nodeIp, String nodePassword) {
        // 1.校验参数
        checkNodeParam(masterIp, masterPassword, nodeIp, nodePassword, kubeId);

        // 2.密码解密
        String decryptMasterPassword = decryptPassword(masterPassword);
        String decryptNodePassword = decryptPassword(nodePassword);

        // 3.获取介质所在路径
        String installPath = sysConfigService.findOne(CloudAppConstant.SysCfgCategory.INSTALL_CONFIG, CloudAppConstant.SysCfgName.INSTALL_PATH);
        if (StringUtils.isEmpty(installPath)) {
            throw new CustomException(600, "未找到安装介质路径配置！");
        }

        // 5.提交定时调度
        Map map = new HashMap();
        map.put("kubeId", kubeId);
        map.put("installPath", installPath);
        map.put("masterIp", masterIp);
        map.put("masterPassword", decryptMasterPassword);
        map.put("nodeIp", nodeIp);
        map.put("nodePassword", decryptNodePassword);
        try {
            callScheduler(kubeId, map, ADD_NODE, NodeWatch.class);
        } catch (Exception e) {
            throw new CustomException(600, "操作节点添加定时调度错误！" + e);
        }
    }

    @Override
    public void deleteNode(Integer kubeId, String masterIp, String masterPassword, String nodeIp, String nodePassword) {
        // 1.校验参数
        checkNodeParam(masterIp, masterPassword, nodeIp, nodePassword, kubeId);

        // 2.密码解密
        String decryptMasterPassword = decryptPassword(masterPassword);
        String decryptNodePassword = decryptPassword(nodePassword);

        // 3.获取介质所在路径
        String installPath = sysConfigService.findOne(CloudAppConstant.SysCfgCategory.INSTALL_CONFIG, CloudAppConstant.SysCfgName.INSTALL_PATH);
        if (StringUtils.isEmpty(installPath)) {
            throw new CustomException(600, "未找到安装介质路径配置！");
        }

        // 4.获取nodeName
        List<NodeDTO> nodes = kubeClientService.get(kubeId).listNodes().stream().filter(node -> node.getNodeIp().equalsIgnoreCase(nodeIp)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nodes)) {
            throw new CustomException(600, "未找到待删除节点！");
        }

        // 5.提交定时调度
        Map map = new HashMap();
        map.put("kubeId", kubeId);
        map.put("installPath", installPath);
        map.put("masterIp", masterIp);
        map.put("masterPassword", decryptMasterPassword);
        map.put("nodeName", nodes.get(0).getNodeName());
        try {
            callScheduler(kubeId, map, DELETE_NODE, NodeWatch.class);
        } catch (Exception e) {
            throw new CustomException(600, "操作节点添加定时调度错误！" + e);
        }
    }

    @Override
    public void cordonNode(Integer kubeId, String masterIp, String masterPassword, String nodeIp, String nodePassword) {
        // 1.校验参数
        checkNodeParam(masterIp, masterPassword, nodeIp, nodePassword, kubeId);

        // 2.密码解密
        String decryptMasterPassword = decryptPassword(masterPassword);
        String decryptNodePassword = decryptPassword(nodePassword);

        // 3.获取介质所在路径
        String installPath = sysConfigService.findOne(CloudAppConstant.SysCfgCategory.INSTALL_CONFIG, CloudAppConstant.SysCfgName.INSTALL_PATH);
        if (StringUtils.isEmpty(installPath)) {
            throw new CustomException(600, "未找到安装介质路径配置！");
        }

        // 4.获取nodeName
        List<NodeDTO> nodes = kubeClientService.get(kubeId).listNodes().stream().filter(node -> node.getNodeIp().equalsIgnoreCase(nodeIp)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nodes)) {
            throw new CustomException(600, "未找到待操作节点！");
        }

        // 5.提交定时调度
        Map map = new HashMap();
        map.put("kubeId", kubeId);
        map.put("masterIp", masterIp);
        map.put("masterPassword", decryptMasterPassword);
        map.put("nodeName", nodes.get(0).getNodeName());
        try {
            callScheduler(kubeId, map, CORDON_NODE, NodeWatch.class);
        } catch (Exception e) {
            throw new CustomException(600, "操作节点添加定时调度错误！" + e);
        }

    }

    @Override
    public void unCordonNode(Integer kubeId, String masterIp, String masterPassword, String nodeIp, String nodePassword) {
        // 1.校验参数
        checkNodeParam(masterIp, masterPassword, nodeIp, nodePassword, kubeId);

        // 2.密码解密
        String decryptMasterPassword = decryptPassword(masterPassword);
        String decryptNodePassword = decryptPassword(nodePassword);

        // 3.获取介质所在路径
        String installPath = sysConfigService.findOne(CloudAppConstant.SysCfgCategory.INSTALL_CONFIG, CloudAppConstant.SysCfgName.INSTALL_PATH);
        if (StringUtils.isEmpty(installPath)) {
            throw new CustomException(600, "未找到安装介质路径配置！");
        }

        // 4.获取nodeName
        List<NodeDTO> nodes = kubeClientService.get(kubeId).listNodes().stream().filter(node -> node.getNodeIp().equalsIgnoreCase(nodeIp)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nodes)) {
            throw new CustomException(600, "未找到待操作节点！");
        }

        // 5.提交定时调度
        Map map = new HashMap();
        map.put("kubeId", kubeId);
        map.put("masterIp", masterIp);
        map.put("masterPassword", decryptMasterPassword);
        map.put("nodeName", nodes.get(0).getNodeName());
        try {
            callScheduler(kubeId, map, UNCORDON_NODE, NodeWatch.class);
        } catch (Exception e) {
            throw new CustomException(600, "操作节点添加定时调度错误！" + e);
        }
    }

    @Override
    public void drainNode(Integer kubeId, String masterIp, String masterPassword, String nodeIp, String nodePassword) {
        // 1.校验参数
        checkNodeParam(masterIp, masterPassword, nodeIp, nodePassword, kubeId);

        // 2.密码解密
        String decryptMasterPassword = decryptPassword(masterPassword);
        String decryptNodePassword = decryptPassword(nodePassword);

        // 3.获取介质所在路径
        String installPath = sysConfigService.findOne(CloudAppConstant.SysCfgCategory.INSTALL_CONFIG, CloudAppConstant.SysCfgName.INSTALL_PATH);
        if (StringUtils.isEmpty(installPath)) {
            throw new CustomException(600, "未找到安装介质路径配置！");
        }

        // 4.获取nodeName
        List<NodeDTO> nodes = kubeClientService.get(kubeId).listNodes().stream().filter(node -> node.getNodeIp().equalsIgnoreCase(nodeIp)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nodes)) {
            throw new CustomException(600, "未找到待操作节点！");
        }

        // 5.提交定时调度
        Map map = new HashMap();
        map.put("kubeId", kubeId);
        map.put("masterIp", masterIp);
        map.put("masterPassword", decryptMasterPassword);
        map.put("nodeName", nodes.get(0).getNodeName());
        try {
            callScheduler(kubeId, map, DRAIN_NODE, NodeWatch.class);
        } catch (Exception e) {
            throw new CustomException(600, "操作节点添加定时调度错误！" + e);
        }
    }

    // set agent_server
    private String checkAgentServer(KubeConfig config) {
        if (StringUtils.isNotEmpty(config.getServer())) {
            if (StringUtils.isEmpty(config.getAgentServer())) {
                String ip = RegexUtil.getIp(config.getServer());
                config.setAgentServer("http://" + ip + ":" + AGENT_PORT);
            }
            if (!NetworkUtil.telnet(IPUtil.getIpFromUrl(config.getAgentServer()), RegexUtil.getPort(config.getAgentServer()))) {
                return "agent server未运行";
            }
        }
        return "";
    }

    /**
     * check 应用部署所需的 operator(crd,rbac..), todo image
     */
    private String checkAppDeployEnv(KubeConfig config) {
        if (StringUtils.isEmpty(config.getServer())) return "";
        if (StringUtils.isEmpty(config.getCsiType())) return "";
        Map<String, Map<String, String>> csiTypeMapByAppKind =
                JsonUtil.toObject(config.getCsiType(), new TypeReference<Map<String, Map<String, String>>>() {});
        if (csiTypeMapByAppKind == null) return "";
        Set<String> appProducts = csiTypeMapByAppKind.keySet();
        StringBuffer notFoundBuf = new StringBuffer();
        StringBuffer configErrBuf = new StringBuffer();
        KubeClient kubeClient = kubeClientService.get(config);
        String errorMsg = "";
        try {
            // 注意：同一个类型可能有多个operator，比如不同架构有各自的operator
            List<CloudSysConfig> operatorNameConfig = sysConfigService.find(CloudAppConstant.SysCfgCategory.OPERATOR_NAME)
                    .stream().filter(cfg -> appProducts.contains(cfg.getName()))
                    .collect(Collectors.toList());

            operatorNameConfig.stream().parallel()
                    .forEach(cfg -> {
                        // check operator and config
                        String kind = cfg.getName();
                        String operatorNamespace_name = cfg.getData();
                        String[] opNameCfg = operatorNamespace_name.split("/");
                        if (opNameCfg.length == 2) {
                            String namespace = opNameCfg[0];
                            String name = opNameCfg[1];
                            Integer readyReplicas = Stream.of("deploy", "sts").parallel()// operator可能部署为任一类型
                                    .map(type -> {
                                        switch (type) {
                                            case "deploy":
                                                return Optional.ofNullable(kubeClient.getDeployments(namespace, name))
                                                        .map(e -> e.getStatus().getReadyReplicas()).orElse(0);
                                            case "sts":
                                                return Optional.ofNullable(kubeClient.getStatefulSet(name, namespace))
                                                        .map(e -> e.getStatus().getReadyReplicas()).orElse(0);
                                            default:
                                                throw new RuntimeException();
                                        }
                                    }).filter(i -> i >= 1).findAny().orElse(0);// 任一类型存在或都不存在
                            // 验证deploy ready replica个数>1
                            if (readyReplicas < 1) {
                                notFoundBuf.append(kind + ",");
                            }
                        } else {
                            configErrBuf.append(kind + ",");
                        }
                    });
        } catch (Exception e) {
            log.error("检测operator遇到错误", e);
            errorMsg = "检查控制器时遇到错误 " + e.getMessage();
        }
        String string = "";
        if (notFoundBuf.length()>0) {
            string = notFoundBuf.toString();
            if (string.endsWith(",")) {
                string = string.substring(0, string.length() - 1);
            }
            string += " 控制器未部署<br>";
        }
        if (configErrBuf.length()>0) {
            string += configErrBuf.toString();
            if (string.endsWith(",")) {
                string = string.substring(0, string.length() - 1);
            }
            string += " 控制器系统配置格式错误，应为 namespace/name";
        }
        if (StringUtils.isNotEmpty(errorMsg)) {
            if (string.length() > 0) {
                string += "<br>";
            }
            string += errorMsg;
        }
        //
//        kubeClient.listNodes().stream()
//                .filter(node -> {
//                    Set<String> images = node.getImages();
//                });
        return string;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @ResourceChangeLog(action = DELETE_CLUSTER)
    public void deleteCluster(List<Integer> kubeIdList) {
        for (Integer kubeId : kubeIdList) {
            //清理cloud_ip_pool_config表
            ipPoolConfigService.deleteByKubeId(kubeId);
            //清理cloud_ip_info表
            ipService.deleteKubeId(kubeId);
            //清理cloud_nodeport_config表
            nodePortService.deleteByKubeIdAndTable(SCHEMA, CLOUD_NODEPORT_CONFIG, kubeId);
            //清理cloud_nodeport_used_info表
            nodePortService.deleteByKubeIdAndTable(SCHEMA, CLOUD_NODEPORT_USED_INFO, kubeId);
            //清理cloud_backup_timer表
            List<CloudApp> appList = cloudAppService.getAppListByKubeId(kubeId);
            for (CloudApp cloudApp : appList) {
                List<BackupTimer> backupTimerList = backupTimerMapper.getBackupTimerByAppId(SCHEMA, CLOUD_BACKUP_TIMER, cloudApp.getId());
                for (BackupTimer backupTimer : backupTimerList) {
                    backupTimerMapper.deleteBackupTimerById(SCHEMA, CLOUD_BACKUP_TIMER, backupTimer.getBackupTimerId());
                }
            }
            //清理cloud_kube_config表
            kubeConfigMapper.deleteById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, kubeId);
        }
    }

    /**
     * fixme 按照执行操作后的规格进行节点选择
     */
    @Override
    @Deprecated
    public Object getClusterResources(Integer kubeId, String kind) {
        Map<String, Object> param = new HashMap<>();
        param.put("state", CloudAppConstant.KubeState.ENABLED);
        if (kubeId != null) param.put("id", kubeId);
        List<KubeConfig> kubes = list(param);
        return kubes.parallelStream().map(s -> {
            KubeConfigVO o = new KubeConfigVO();
            BeanUtils.copyProperties(s, o);
//            NodeDTO nodeDTO = nodeService.sumNodeAllocatable(s.getId());
            List<NodeDTO> nodeDTOS = nodeService.listCandidates(s.getId(), s.getCSI(kind), null);
            Collections.sort(nodeDTOS);
            NodeDTO nodeDTO = nodeDTOS.get(0);
            o.getAllocatable().put("cpu", Double.valueOf(nodeDTO.getAllocatableCpu() + "").longValue());
            o.getAllocatable().put("memory", nodeDTO.getAllocatableMem());
            // todo disk allocatable 不同应用csiType不一样
            //            for (AppKind kind : AppKind.values()) {
            //                o.getAllocatableDisk().put(kind.getProduct(), queryCsiAllocatable());
            //            }
            return o;
        }).collect(Collectors.toList());

    }

    @Override
    public List<KubeConfig> findEnabled(Map<String, Object> param) {
        if (param == null) param = new HashMap<>(2);
        param.put("state", CloudAppConstant.KubeState.ENABLED);
        return list(param);
    }

    @Override
    public List<KubeClusterCardVO> listMetrics(List<KubeConfigVO> vos) {
        return vos.parallelStream().map(k -> {
            KubeClusterCardVO vo = new KubeClusterCardVO();
            vo.setId(k.getId());
            vo.setName(k.getName());
            vo.setHost(k.getIp());
            vo.setVersion(k.getVersion());
            try {
                Map<String, Double> metrics = metricService.queryClusterMetric(vo.getId());
                vo.setCpu(metrics.get("cpu"));
                vo.setCpuUsage(metrics.get("cpu_usage"));
                vo.setMemoryUsage(metrics.get("memory_usage"));
                vo.setMemory(metrics.get("memory"));
                vo.setDisk(metrics.get("disk"));
                vo.setDiskUsage(metrics.get("disk_usage"));
            } catch (Exception e) {
                log.error("failed get metric of k8s cluster: {}, error: {}", k, e.getMessage());
            }
            return vo;
        }).collect(Collectors.toList());

    }

    @Override
    public List<KubeConfigVO> findEnabledVO(Map<String, Object> param) {
        if (param == null) param = new HashMap<>();
        PageDTO pageDTO = new PageDTO();
        pageDTO.setCondition(param);
        return findEnabledVO(pageDTO);
    }

    @Override
    public List<KubeConfigVO> listVO(Map<String, Object> param) {
        List<KubeConfig> list = list(param);
        return list.parallelStream().map(createVO()).collect(Collectors.toList());
    }

    @Override
    public String getOperatorNameWithNamespace(AppKind kind) {
        String one = sysConfigService.findOne(CloudAppConstant.SysCfgCategory.OPERATOR_NAME, kind.getProduct());
        Objects.requireNonNull(one, "查找operator name失败-" + kind);
        return one;
    }

    private Function<KubeConfig, KubeConfigVO> createVO() {
        return k -> {
            KubeConfigVO vo = new KubeConfigVO();
            vo.setId(k.getId());
            vo.setName(k.getName());
            vo.setIp(RegexUtil.getIp(k.getServer()));
            vo.setVersion(k.getVersion());
            return vo;
        };
    }

    /**
     * 初始化配置新纳管的集群
     */
    public void initializeOnK8s(KubeConfig kc) {
        KubeClient client = KubeClientManager.instance(new FabricKubeClientFactory())
                .createClient(kc);

        // specific global setting data
        ResourceHelper.getInstance()
                .updateRatioOn(client, sysConfigService.find(OVER_ALLOCATION_RATIO));

        // other global setting  data
        GlobalConfigMap.getInstance().updateByMerging(client, sysConfigService.findMap(K8S_SYS_CONFIG_CM_DATA));
    }

    public void callScheduler(Integer kubeId, Map extData, ActionEnum actionEnum, Class<?> callbackClass) throws JsonProcessingException, SchedulerException {
        //操作记录
        String name = getName(kubeId);
        ResourceChangeHis rch = getResourceChangeHis(kubeId, name, actionEnum);
        rch.setStatus(StatusConstant.RUNNING);
        rch.setMsg("操作节点中");
        rch.setUpdateTime(new Timestamp(new Date().getTime()));
        Integer changeId = resourceChangeHisService.add(rch);

        //放入操作记录Id
        extData.put("changeId", rch.getId());

        //新增定时调度
        CronTriggerMeta triggerMeta = new CronTriggerMeta();
        triggerMeta.setCron("0/30 * * * * ? ");
        Map<String, String> jobData = new HashMap<>();
        jobData.put("kubeId", String.valueOf(kubeId));
        jobData.put("class", callbackClass.getName());
        jobData.put("extDataStr", JsonUtil.toJson(extData));
        UserInfo userInfo = UserUtil.getCurrentUser() == null ? UserUtil.getAsyncUserinfo() : UserUtil.getCurrentUser();
        jobData.put("userInfo", JsonUtil.toJson(userInfo));

        triggerMeta.setTriggerGroup(name);
        triggerMeta.setTriggerName(actionEnum.getActionType());
        triggerMeta.setJobDataMap(jobData);
        resourceChangeHisService.addSchedule(rch, triggerMeta);
    }

    private ResourceChangeHis getResourceChangeHis(Integer kubeId, String name, ActionEnum action) {
        ResourceChangeHis rch = new ResourceChangeHis();
        rch.setAction(action.getActionType());
        rch.setCommand(action.getAppOperation());
        rch.setKind(CloudResourceKindEnum.KUBE_CONFIG.getKey());
        rch.setAppLogicId(0); // 无效值，仅用于数据库非空校验
        rch.setAppId(0);
        rch.setInsertTime(new Timestamp(new Date().getTime()));
        rch.setUpdateTime(new Timestamp(new Date().getTime()));
        UserInfo currentUser = UserUtil.getCurrentUser();
        rch.setUserId(currentUser.getUserid());
        rch.setUserName(currentUser.getUsername());
        rch.setUserIp(CloudRequestContext.getContext().getUserIp());
        rch.setKubeId(kubeId);
        rch.setAppName(name);// 借用appName 存放操作实体名称
        if (kubeId != null)
            rch.setDataMap(kubeId.toString());
        rch.setKubeName(name);
        return rch;
    }

    /**
     * 对密码解密
     *
     * @param encryptedPassword
     * @return
     * @throws Exception
     */
    public String decryptPassword(String encryptedPassword) {
        // 解密后密码
        String decryptPassword = null;
        // 1.判断是否传递了密码
        if (!StringUtils.isBlank(encryptedPassword)) {
            // 2.对密码进行解密
            decryptPassword = AsymmetricEncryptionUtil.getEncryptInstance().decrypt(encryptedPassword);
        }
        return decryptPassword;
    }

    private void checkNodeParam(String masterIp, String masterPassword, String nodeIp, String nodePassword, Integer kubeId) {
        // 0.校验当前集群是否正在执行其他操作
        Map<String, Object> searchMap = new LinkedHashMap<>();
        searchMap.put("dataMap", kubeId);
        searchMap.put("status", "2");
        List<ResourceChangeHis> resourceChangeHis = resourceChangeHisMapper.listByMap(SCHEMA, CLOUD_RESOURCE_TABLE_HIS, CLOUD_KUBE_CONFIG_TABLE, searchMap);
        if (!CollectionUtils.isEmpty(resourceChangeHis)) {
            throw new CustomException(600, "该集群正在执行其他操作！");
        }

        // 1.校验参数
        if (StringUtils.isEmpty(masterIp)) {
            throw new CustomException(600, "缺少参数，主节点IP！");
        } else if (StringUtils.isEmpty(masterPassword)) {
            throw new CustomException(600, "缺少参数，主节点root密码！");
        } else if (StringUtils.isEmpty(nodeIp)) {
            throw new CustomException(600, "缺少参数，节点IP！");
        } else if (StringUtils.isEmpty(nodePassword)) {
            throw new CustomException(600, "缺少参数，节点root密码！");
        }

        //密码解密
        String decryptMasterPassword = decryptPassword(masterPassword);
        String decryptNodePassword = decryptPassword(nodePassword);

        // 2.获取校验master节点与新增节点是否可连接
        try {
            sshUtil.CustomSSHCmd(masterIp, "root", decryptMasterPassword, "echo 1");
        } catch (Exception e) {
            throw new CustomException(600, "测试主节点连通错误！" + e);
        }

        try {
            sshUtil.CustomSSHCmd(nodeIp, "root", decryptNodePassword, "echo 1");
        } catch (Exception e) {
            throw new CustomException(600, "测试目标节点连通错误！" + e);
        }

    }
//    public void prepareKubeClient() {
//        if (kubeClientManager != null) {
//            Collection<Integer> cachedClient = kubeClientManager.cachedClientID();
//            findEnabled(null).stream()
//                    .filter(kc -> !cachedClient.contains(kc.getId()))
//                    .collect(Collectors.toList())
//                    .parallelStream()
//                    .forEach(kc -> kubeClientManager.createClient(kc));
//        }
//        else
//            kubeClientService.getAll();
//    }
}