package cn.newdt.cloud.utils;


import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.dto.CloudInstDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.fegin.ManagerService;
import cn.newdt.cloud.mapper.AppPdbinfoMapper;
import cn.newdt.cloud.mapper.KubeConfigMapper;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.impl.AccessManagementService;
import cn.newdt.cloud.service.impl.SysConfigService;
import cn.newdt.cloud.service.impl.TenantService;
import cn.newdt.cloud.vo.AppInstanceVO;
import cn.newdt.cloud.vo.CloudAppVO;
import cn.newdt.commons.bean.UserInfo;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.response.ResponseResult;
import cn.newdt.commons.utils.AsymmetricEncryptionUtil;
import cn.newdt.commons.utils.SymmetricEncryptionUtil;
import cn.newdt.commons.utils.UserUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.CloudAppConstant.DMP.CLUSTER_TYPE_DB;
import static cn.newdt.cloud.constant.CloudAppConstant.DMP.CLUSTER_TYPE_MID;
import static cn.newdt.cloud.constant.DatasourceConstant.*;

/**
 * cloud_DMP 运维接口
 */

@Component
@Slf4j
public class OperationUtil {

//    Logger log = LoggerFactory.getLogger(OperationUtil.class);

//    @Autowired
//    private CmdbUtil cmdbUtil;
    @Autowired
    private CloudAppLogicService logicAppService;

    @Autowired
    private CloudAppService appService;

    @Autowired
    private AppPdbinfoMapper appPdbinfoMapper;

    @Autowired
    private KubeClientService kubeClientService;

    @Autowired
    private TenantService tenantService;

    @Autowired
    private KubeClientService clientService;

    @Autowired
    private CloudDatabaseUserService dbUserService;

    @Autowired
    private AccessManagementService accessManagementService;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private ManagerService managerService;

    @Autowired
    private CloudDatabaseUserService databaseUserService;

    @Autowired
    private KubeConfigMapper kubeConfigMapper;

    @Autowired
    private ResourceChangeHisService resourceChangeHisService;

    /**
     * TODO-- 1.针对访问管理修改自动纳管逻辑，创建service后推送vipPort给cmdb
     * TODO-- 2. 修改推送信息，实例port改为默认端口
      */

    /**
     * 获取vo对象以及userinfo
     *
     * @param app
     * @param triggerHis
     */
    public String syncToDMP(CloudApp app, TriggerHis triggerHis) {
        // todo 灾备切换是否重新纳管
        if (!CloudAppConstant.ROLE_PRIMARY.equals(app.getRole()))
            return null;
        //转化为对象
        String voStr = triggerHis.getJobDataMap().get("vo");
        CloudAppVO vo = JSONObject.parseObject(voStr, CloudAppVO.class);
        if (ObjectUtils.isEmpty(vo)) {
            CloudAppVO appVO = new CloudAppVO();
            BeanUtils.copyProperties(app, appVO);
            vo = appVO;
        }
        //转化为用户对象UserInfo
        String userInfoStr = triggerHis.getJobDataMap().get("userInfo");
        UserInfo userInfo = JSONObject.parseObject(userInfoStr, UserInfo.class);
        UserUtil.setAsyncUserInfo(userInfo);
        return manageApp(vo);
    }

    /**
     * 纳管应用到DMP
     *
     * @param app
     */
    public String manageApp(CloudAppVO app) {
        // 1.构造纳管对象
        CloudInstDTO cloudInstDTO = structureCloudInstDTO(app);
        // 2.纳管
        ResponseResult<Integer> resp = managerService.save(cloudInstDTO);
        // 判断结果
        if (!(StringUtils.isNotEmpty(resp.getMsg()) && resp.getMsg().equalsIgnoreCase("success"))) {
            log.error("交付成功！纳管失败：" + resp.getDetail().getMessage());
            throw new CustomException(500, "交付成功！纳管失败！" +
                    "命名空间：" + app.getNamespace() + "   " +
                    "实例名称：" + app.getCrName() + "   " +
                    "连接地址:" + cloudInstDTO.getAccessIp() + "   " +
                    "连接端口:" + cloudInstDTO.getAccessPort() + "   " +
                    "监控用户:" + cloudInstDTO.getUsername() + "   " +
                    "详情：" + resp.getDetail().getMessage());
        }
        String res = "交付成功！纳管成功！" +
                "命名空间：" + app.getNamespace() + "   " +
                "实例名称：" + app.getCrName() + "   " +
                "连接地址:" + cloudInstDTO.getAccessIp() + "   " +
                "连接端口:" + cloudInstDTO.getAccessPort();
        if (StringUtils.isEmpty(app.getUsername())) {
            return res;
        }
        return res + "   数据库管理用户:" + app.getUsername();
    }

    /**
     * 取消纳管
     */
    public void cancelManageApp(Integer logicAppId) {
        ResponseResult<Boolean> res = managerService.remove(String.valueOf(logicAppId));
        Boolean data = res.getData();
        if (data == null || !data) {
            throw new CustomException(500, "取消纳管失败！");
        }
    }

    public Map<String, String> createDMPMonitorUser() {
        Map<String, String> dmpMonitorUser = getDMPMonitorUser();
        return dmpMonitorUser;
    }

    public Map<String, String> getDMPMonitorUser() {
        Map<String, String> userInfoMap = new HashMap<>();
        //创建dmp监控用户
        //获取配置
        String dmpMonitorUserConfig = sysConfigService.findOne(CloudAppConstant.SysCfgCategory.DMP_MANAGEMENT, "monitoruser");
        if (StringUtils.isBlank(dmpMonitorUserConfig)) {
            throw new CustomException(500, "纳管应用失败！未找到配置的！");
        }
        String[] dmpMonitorUserConfigArr = dmpMonitorUserConfig.split("/");
        //对密码解密
        String dmpPassword = SymmetricEncryptionUtil.getEncryptInstance().decrypt(dmpMonitorUserConfigArr[1]);
        userInfoMap.put("username", dmpMonitorUserConfigArr[0]);
        userInfoMap.put("password", dmpPassword);
        return userInfoMap;
    }

    /**
     * 构造纳管对象
     * todo 纳管logicApp 而不是 physical app
     * @param app
     * @return
     */
    private CloudInstDTO structureCloudInstDTO(CloudAppVO app) {
        //根据namespace查询部门id
        Optional<CloudTenant> cloudTenant = tenantService.findByNamespace(app.getNamespace());
        if (!cloudTenant.isPresent()) {
            throw new CustomException(500, "纳管应用失败！未找到租户！");
        }
        Integer externalId = cloudTenant.get().getExternalId();
        ArrayList<Integer> orgIds = new ArrayList<>();
        //判断orgId是否为空，为空则查询操作记录，查询最早的那条记录(安装记录)，获取datamap中的orgId
        if (ObjectUtils.isEmpty(app.getOrgId())) {
            ResourceChangeHis oldestByAppId = resourceChangeHisService.getOldestByAppId(app.getId());
            orgIds.add(Integer.valueOf(oldestByAppId.getDataMap()));
        } else {
            orgIds.add(app.getOrgId());
        }

        List<ServiceManager> serviceManagerWrite = null;

        //查询访问管理
        List<ServiceManager> serviceManagers = accessManagementService.AZServiceList(app.getLogicAppId());
        if (!CollectionUtils.isEmpty(serviceManagers)) {
            serviceManagerWrite = serviceManagers.stream()
                    // only apply current app which would be primary in a multi-az deployment
                    .filter(svc -> svc.getAppId().equals(app.getId()))
                    .filter(svc -> svc.getPurpose().equals(CloudAppConstant.ServicePurpose.WRITE))
                    .collect(Collectors.toList());
        }

        //查询实例monitor账密
        String dmpMonitorUserConfig = sysConfigService.findOne(CloudAppConstant.SysCfgCategory.DMP_MANAGEMENT, "monitoruser");
        if (StringUtils.isBlank(dmpMonitorUserConfig)) {
            throw new CustomException(500, "纳管应用失败！未找到配置的！");
        }
        String[] dmpMonitorUserConfigArr = dmpMonitorUserConfig.split("/");

        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());

        //查询kubeConfig，获取grafanaserver
        String grafanaUrl = grafanaSettings(app, appKind);

        CloudInstDTO cloudInstDTO = appKind.applyToDMPDTO(app);
        cloudInstDTO.setPrometheusLabels(grafanaUrl);

        //1.通用属性构造
        cloudInstDTO.setExternalId(String.valueOf(app.getLogicAppId()));
        cloudInstDTO.setInstName(app.getCrName());
        cloudInstDTO.setInstClass(appKind.instClass());
        cloudInstDTO.setInstType(appKind.instType());
        //根据serviceType进行accessip和accessport的set
        if (!ObjectUtils.isEmpty(serviceManagerWrite)) {
            if (StringUtils.isEmpty(serviceManagerWrite.get(0).getServiceIps()))
                throw new CustomException(500, "访问IP为空，纳管数据缺失");
            cloudInstDTO.setAccessIp(serviceManagerWrite.get(0).getServiceIps());
            cloudInstDTO.setAccessPort(ObjectUtils.isEmpty(serviceManagerWrite.get(0).getPort()) ? null : String.valueOf(serviceManagerWrite.get(0).getPort()));
        }
        cloudInstDTO.setVersion(app.getVersion());
        cloudInstDTO.setAppSysName(app.getAppSystemName());
        if (StringUtils.isEmpty(cloudInstDTO.getUsername())) {
            cloudInstDTO.setUsername(dmpMonitorUserConfigArr[0]);
            cloudInstDTO.setPassword(dmpMonitorUserConfigArr[1]);
        }
        cloudInstDTO.setOrgIds(orgIds);
        cloudInstDTO.setDbaUser(UserUtil.getCurrentUser().getName());
        cloudInstDTO.setExecutorGroupId(app.getExecutorGroupId());
        if (AppKind.MongoDB == appKind) {
            cloudInstDTO.setDbname("admin");
        } else if (AppKind.MongoDB_Cluster == appKind) {
            cloudInstDTO.setDbname("admin");
        } else if (AppKind.OpenGauss == appKind) {
            cloudInstDTO.setDbname("postgres");
        } else if (AppKind.Vastbase == appKind) {
            cloudInstDTO.setDbname("postgres");
            cloudInstDTO.setArch("STREAM_REP");
        } else if (AppKind.MYSQL_HA == appKind) {
        } else if (AppKind.MYSQL_MGR == appKind) {
        } else if (AppKind.PostgreSQL == appKind) {
            cloudInstDTO.setDbname("postgres");
        } else if (AppKind.Redis == appKind) {
            //如果是redis则不需要传递用户，而是传递管理密码
            cloudInstDTO.setUsername("");
            // 仅安装时trigger state包含用户信息，应用还原时无法获取。
            if (StringUtils.isEmpty(app.getPassword())) {
                String password = kubeClientService.get(app.getKubeId()).getSecret(app.getNamespace(), "redis-" + app.getCrName() + "-password").getData().get("password");
                byte[] decode = Base64.getDecoder().decode(password);
                String decodedString = new String(decode);
                cloudInstDTO.setPassword(decodedString);
            } else {
                cloudInstDTO.setPassword(app.getPassword());
            }
        } else if (AppKind.Redis_Cluster == appKind) {
            cloudInstDTO.setUsername("");
            // 仅安装时trigger state包含用户信息，应用还原时无法获取。
            if (StringUtils.isEmpty(app.getPassword())) {
                String password = kubeClientService.get(app.getKubeId()).getSecret(app.getNamespace(), "rc-" + app.getCrName() + "-password").getData().get("password");
                byte[] decode = Base64.getDecoder().decode(password);
                String decodedString = new String(decode);
                cloudInstDTO.setPassword(decodedString);
            } else {
                cloudInstDTO.setPassword(app.getPassword());
            }
        } else if (AppKind.Kafka == appKind) {
            cloudInstDTO.setIsCollect(0);
            //传递自创建用户名密码
            cloudInstDTO.setUsername(app.getUsername());
            cloudInstDTO.setPassword(app.getPassword());
            // 仅安装时trigger state包含用户信息，应用还原时无法获取。
            if (StringUtils.isEmpty(app.getUsername()) || StringUtils.isEmpty(app.getPassword())) {
                databaseUserService.findDbUser(app.getId(), CloudAppConstant.UserRole.ADMIN)
                        .stream().findAny().ifPresent(dbUser -> {
                    cloudInstDTO.setUsername(dbUser.getUsername());
                    cloudInstDTO.setPassword(AsymmetricEncryptionUtil.getEncryptInstance().decrypt(dbUser.getPassword()));
                });
            }
        } else if (AppKind.Elasticsearch == appKind) {
            cloudInstDTO.setIsCollect(0);
            //传递自创建用户名密码
            cloudInstDTO.setUsername(app.getUsername());
            cloudInstDTO.setPassword(app.getPassword());
            // 仅安装时trigger state包含用户信息，应用还原时无法获取。
            if (StringUtils.isEmpty(app.getUsername()) || StringUtils.isEmpty(app.getPassword())) {
                databaseUserService.findDbUser(app.getId(), CloudAppConstant.UserRole.ADMIN)
                        .stream().findAny().ifPresent(dbUser -> {
                    cloudInstDTO.setUsername(dbUser.getUsername());
                    cloudInstDTO.setPassword(AsymmetricEncryptionUtil.getEncryptInstance().decrypt(dbUser.getPassword()));
                });
            }
        } else if (AppKind.Broker == appKind) {
            cloudInstDTO.setIsCollect(0);
            //传递自创建用户名密码
            cloudInstDTO.setUsername(app.getUsername());
            cloudInstDTO.setPassword(app.getPassword());
            // 仅安装时trigger state包含用户信息，应用还原时无法获取。
            if (StringUtils.isEmpty(app.getUsername()) || StringUtils.isEmpty(app.getPassword())) {
                databaseUserService.findDbUser(app.getId(), CloudAppConstant.UserRole.ADMIN)
                        .stream().findAny().ifPresent(dbUser -> {
                    cloudInstDTO.setUsername(dbUser.getUsername());
                    cloudInstDTO.setPassword(AsymmetricEncryptionUtil.getEncryptInstance().decrypt(dbUser.getPassword()));
                });
            }

            cloudInstDTO.setArch("Broker");
        } else if (AppKind.Flink == appKind) {
            cloudInstDTO.setIsCollect(0);
            cloudInstDTO.setUsername("");
            cloudInstDTO.setPassword("");
        } else if (AppKind.Sentinel == appKind) {
            //修改sentinel的架构为哨兵
            cloudInstDTO.setArch("SENTINEL");
            cloudInstDTO.setUsername("");
            cloudInstDTO.setPassword("");
            cloudInstDTO.setIsCollect(0);
        } else if (AppKind.Clickhouse == appKind) {
            cloudInstDTO.setIsCollect(0);
        } else if (AppKind.Zookeeper == appKind) {
            cloudInstDTO.setArch("Zookeeper");
            cloudInstDTO.setUsername("");
            cloudInstDTO.setPassword("");
            cloudInstDTO.setIsCollect(0);
        } else if (AppKind.ClickHouse_Zookeeper == appKind) {
            cloudInstDTO.setArch("Zookeeper");
            cloudInstDTO.setUsername("");
            cloudInstDTO.setPassword("");
            cloudInstDTO.setIsCollect(0);
        } else if (AppKind.NameServer == appKind) {
            cloudInstDTO.setArch("NameServer");
            cloudInstDTO.setUsername("");
            cloudInstDTO.setPassword("");
            cloudInstDTO.setIsCollect(0);
        } else if (AppKind.Kibana == appKind) {
            cloudInstDTO.setArch("Kibana");
            cloudInstDTO.setUsername("");
            cloudInstDTO.setPassword("");
            cloudInstDTO.setIsCollect(0);
        } else if (AppKind.TIDB == appKind){
            cloudInstDTO.setArch("DISTRIBUTED");
        }else {
            cloudInstDTO.setDbname("");
        }
        log.info("纳管对象：" + cloudInstDTO.toString());
        return cloudInstDTO;
    }

    // 多中心格式变更
    // {
    // "var-job":"og-dolphintest",
    // "grafana_url":"[{name:'',url:''}]",
    // "var-namespace":"yantest"
    // }
    private String grafanaSettings(CloudAppVO app, AppKind appKind) {
        // 兼容性代码，原来的作为模板
        String template = appKind.getGrafanaUrl("%s", app);
        if (StringUtils.isEmpty(template)) return null;
        Map<String, String> templateStruct = JsonUtil.toObject(template, new TypeReference<Map<String, String>>() {});
        String url_template = (String) templateStruct.get(CloudAppConstant.GraganaKeys.GRAFANA_URL);
        List<ImmutableMap<String, String>> multiAZGrafanaUrl = logicAppService.getPhysicApps(app.getLogicAppId()).stream().map(papp -> {
            KubeConfig kubeConfig = kubeConfigMapper.getById(SCHEMA, CLOUD_KUBE_CONFIG_TABLE, papp.getKubeId());
            if (null == kubeConfig) {
                log.error("未获取到集群配置！");
                throw new CustomException(500, "未获取到集群配置！");
            }
            String grafanaServer = kubeConfig.getGrafanaServer();
            return ImmutableMap.of("name", kubeConfig.getName(), "url", String.format(url_template, grafanaServer));
        }).collect(Collectors.toList());
        templateStruct.put(CloudAppConstant.GraganaKeys.GRAFANA_URL, JsonUtil.toJson(multiAZGrafanaUrl));// 多中心替换模板中的url
        return JsonUtil.toJson(templateStruct);
    }


    /**
     * 容器云应用(集群+实例)纳管到CMDB.
     * - cloud_app_pdbinfo没有相关记录提交纳管.
     */
    public void doActionToManage(CloudAppVO app) {
        log.info("[纳管到dmp]");
        if (null != app) {
            log.info("[纳管到dmp] {}-{}", app.getId(), app.getCrName());
        }

        // 查询纳管状态. 因为事务为rr模式, 这里不会查到isManaging > 0 && cmdbManaged > 0的情形.
        List<AppPdbinfo> appPdbinfoList = appPdbinfoMapper.findByMap(SCHEMA, CLOUD_APP_PDBINFO, Collections.singletonMap("appId", app.getId()));
        List<AppPdbinfo> isManaging = appPdbinfoList.stream().filter(e -> e.getRealStatus() == 0).collect(Collectors.toList()); // 纳管中
        List<AppPdbinfo> cmdbManaged = appPdbinfoList.stream().filter(e -> e.getRealStatus() == 1).collect(Collectors.toList());//dmp纳管完成
        List<AppPdbinfo> cloudManaged = appPdbinfoList.stream().filter(e -> e.getRealStatus() == 2).collect(Collectors.toList());//容器云纳管完成

        List<PodDTO> podList = getPodList(app);
        int expectedInstanceNum = podList.size();
        log.info("[容器云应用纳管] 当前纳管状态: expected [{}], isManaging [{}], cmdbManaged [{}], cloudManaged [{}]",
                expectedInstanceNum, isManaging, cmdbManaged, cloudManaged);

        if (cloudManaged.size() == expectedInstanceNum) return;

        if (isManaging.size() > 0) {
            throw new ManageUnfinishedException("等待纳管中实例完成纳管"); // todo
        } else {
//            CloudManageClusterDTO cloudManageClusterDto = new CloudManageClusterDTO();
//            cloudManageClusterDto.setInstanceDTOList(managedInstance(app));
//            cloudManageClusterDto.setClusterDto(managedCluster(app));
            try {
//                CloudManageClusterVO cloudManageClusterVO = cmdbUtil.cloudManageCluster(cloudManageClusterDto);
//                log.info("[容器云应用纳管] CMDB 纳管完成, 应用ID-{}, CMDB纳管ID-{}", app.getId(), cloudManageClusterVO.getCluId());
            } catch (Exception e) {
                throw new ManageFailedException(e); // 如果报错, cmdb回滚, 容器云服务可以重试.
            }
        }

        // cmdb纳管完成， 更新容器云纳管状态
        cmdbManaged = appPdbinfoMapper.findByMap(SCHEMA, CLOUD_APP_PDBINFO, Collections.singletonMap("appId", app.getId()))
                .stream().filter(e -> e.getRealStatus() == 1).collect(Collectors.toList());
        int count = 0;
        for (AppPdbinfo e : cmdbManaged) {
            e.setKubeId(app.getKubeId());
            e.setKind(app.getKind());
            e.setRealStatus(2);
            appPdbinfoMapper.update(SCHEMA, CLOUD_APP_PDBINFO, e);
            count ++;
        }
        if (count < expectedInstanceNum) {
            throw new UnexpectedManageException(String.format("call cmdb succeed, but managed number [%d/%d] is incorrect.", count, expectedInstanceNum));
        }
    }


    /**
     * 维护DMP纳管应用实例和容器云应用实例关系
     * @param cloudManageClusterVO
     * @param vo
     */

    /**
     * 调用纳管实例接口
     * @param vo
     * @return
     */
//    public List<CloudManageClusterDTO.InstanceDTO> managedInstance(CloudAppVO vo) {
//
//        List<CloudManageClusterDTO.InstanceDTO> instanceDtoList = new ArrayList<>();
//        KubeClient client = kubeClientService.get(vo.getKubeId());
//        AppKind appKind = AppKind.valueOf(vo.getKind(), vo.getArch());
//        Integer appId = vo.getId();
//        List<AppInstanceVO> instanceList = findInstanceList(appId, appKind);
//        List<String> slaveIpList = new ArrayList<>();
//        // 主从架构设置从节点IP
//        if(!vo.getKind().equalsIgnoreCase("zookeeper") &&
//                !vo.getKind().equalsIgnoreCase("kafka") &&
//                !vo.getKind().equalsIgnoreCase("nameserver") &&
//                !vo.getKind().equalsIgnoreCase("kibana") &&
//                !vo.getKind().equalsIgnoreCase("mongodb") && !vo.getArch().equalsIgnoreCase("cluster")){
//            slaveIpList = instanceList.stream().filter(instance -> !instance.getRole().equalsIgnoreCase("primary"))
//                    .map(o -> {
//                        if (appKind == AppKind.MongoDB)
//                            return o.getPodName() + "." + vo.getName() + "-svc." + vo.getNamespace() + ".svc.cluster.local";
//                        else
//                            return o.getIp();
//                    }).collect(Collectors.toList());
//        }
//        for (AppInstanceVO instanceVO : instanceList) {
//            CloudManageClusterDTO.InstanceDTO instanceDto = new CloudManageClusterDTO.InstanceDTO();
//            // add value
//            if(!vo.getKind().equalsIgnoreCase("mysql") && StringUtils.isEmpty(vo.getUsername())) {
//                instanceDto.setUsername("");
//            } else if(AppKind.MYSQL_MGR.getArch().equals(vo.getArch()) && StringUtils.isEmpty(vo.getUsername())){
//                instanceDto.setUsername("root");
//            }else if(vo.getKind().equalsIgnoreCase("mysql") && AppKind.MYSQL_HA.getArch().equals(vo.getArch())){
//                instanceDto.setUsername("k8sadmin");
//            }else if(appKind ==  AppKind.MongoDB){
//                instanceDto.setUsername("admin");
//            }else {
//                instanceDto.setUsername(vo.getUsername());
//            }
//            if(!vo.getKind().equalsIgnoreCase("mysql") && StringUtils.isEmpty(vo.getPassword())) {
//                instanceDto.setPassword("");
//            } else if(AppKind.MYSQL_MGR.getArch().equals(vo.getArch())  && StringUtils.isEmpty(vo.getPassword())){
//                instanceDto.setPassword(vo.getRootPassword());
//            }else if(vo.getKind().equalsIgnoreCase("mysql") && AppKind.MYSQL_HA.getArch().equals(vo.getArch())){
//                instanceDto.setPassword("k8sadmin");
//            }else if(appKind == AppKind.MongoDB){
//                instanceDto.setPassword("Passw0rd");
//            }else {
//                instanceDto.setPassword(vo.getPassword());
//            }
//            instanceDto.setOperating(INSTANCE_OPERATOR_ADD);    //ADD、DEL、UPDATE
//            instanceDto.setVersion(vo.getVersion());
//            String arch;
//            if (appKind == AppKind.Redis){
//                arch = REDIS_ARCH_HA;
//            } else if (appKind == AppKind.Redis_Cluster){
//                arch = REDIS_ARCH_CLUSTER;
//            } else {
//                arch = vo.getArch();
//            }
//            instanceDto.setArch(arch);
//            //将pod信息转换为实例信息
//            instanceDto.setInstName(getInstName(appKind, instanceVO.getPodName()));
//            instanceDto.setInstAlias(instanceVO.getPodName());
//            if (appKind == AppKind.MongoDB){
//                // mongodb stateful set pod ip不固定，采用dns
//                instanceDto.setIp(instanceVO.getPodName() + "." + vo.getName() + "-svc." + vo.getNamespace() + ".svc.cluster.local");
//                instanceDto.setReplica_set_name(vo.getCrName());
//            } else {
//                instanceDto.setIp(instanceVO.getIp());
//            }
//            instanceDto.setPort(appKind.getDbPort()+"");
//            /*try{
//                //获取nodeip
//                Node node = client.getNode(instanceVO.getNode());
//                //getAddresses是数组，但是只获取第一个，查看是否获取到nodeIp
//                instanceDto.setVip(node.getStatus().getAddresses().get(0).getAddress());
//                // zookeeper、kibana 设置nodeport端口
//                instanceDto.setVip_port(vo.getNodeportList()); // fixme instance's vport 应仅设置为指向该 pod 的nodeport.
//            } catch (Exception e) {
//                e.printStackTrace();
//                log.error("get nodeName error", e);
//            }*/
//            //网络分区，传kubeId
//            instanceDto.setNetworkPartition(vo.getKubeId());
//            if(ROLE_PRIMARY.equalsIgnoreCase(instanceVO.getRole())) {
//                instanceDto.setInstRole(INSTANCE_ROLE_MASTER);
//            } else if(ROLE_SECONDARY.equalsIgnoreCase(instanceVO.getRole())) {
//                instanceDto.setInstRole(INSTANCE_ROLE_SLAVE);
//            }
////            instanceDto.setInstRole(instanceVO.getRole());
//            instanceDto.setSlaveIp("");
//            // 主节点
//            if(INSTANCE_ROLE_MASTER.equalsIgnoreCase(instanceDto.getInstRole())) {
//                if(!CollectionUtils.isEmpty(slaveIpList)){
//                    log.info("slaveIp = " + slaveIpList.toString());
//                    instanceDto.setSlaveIp(StringUtils.strip(slaveIpList.toString(), "[]"));
//                }
//            }
//            instanceDtoList.add(instanceDto);
//        }
//        return instanceDtoList;
//    }

    /**
     * 设置相关的应用类型的数据库名
     * @param appKind
     * @param instName
     * @return
     */
    private String getInstName(AppKind appKind, String instName) {
        if (appKind == AppKind.MongoDB || appKind == AppKind.MongoDB_Cluster) {
            return "admin";
        } else if (appKind == AppKind.OpenGauss || appKind == AppKind.PostgreSQL) {
            return "postgres";
        } else if (appKind == AppKind.MYSQL_HA || appKind == AppKind.MYSQL_MGR) {
            return "-";
        } else {
            return instName;
        }
    }

//    public List<CloudManageClusterDTO.InstanceDTO> scaleOutCreateInstance(CloudAppVO vo ,List<PodDTO> podList) {
//        KubeClient client = kubeClientService.get(vo.getKubeId());
//        AppKind appKind = AppKind.valueOf(vo.getKind(), vo.getArch());
//        List<CloudManageClusterDTO.InstanceDTO> instanceDtoList = new ArrayList<>();
//        if(!CollectionUtils.isEmpty(podList)) {
//            for (PodDTO pod : podList) {
//                // new instance class
//                CloudManageClusterDTO.InstanceDTO instanceDto = new CloudManageClusterDTO.InstanceDTO();
//                // add value
//                if(StringUtils.isEmpty(vo.getUsername())) {
//                    instanceDto.setUsername(!vo.getKind().equalsIgnoreCase("mysql") ? "" : "k8sadmin");
//                } else {
//                    instanceDto.setUsername(vo.getUsername());
//                }
//
//                if(StringUtils.isEmpty(vo.getPassword())) {
//                    instanceDto.setPassword(!vo.getKind().equalsIgnoreCase("mysql") ? "" : "k8sadmin");
//                } else {
//                    instanceDto.setPassword(vo.getPassword());
//                }
//
//                instanceDto.setOperating(INSTANCE_OPERATOR_ADD);   //ADD、DEL、UPDATE
//                instanceDto.setVersion(vo.getVersion());
//                String arch;
//                if (appKind == AppKind.Redis){
//                    arch = REDIS_ARCH_HA;
//                } else if (appKind == AppKind.Redis_Cluster){
//                    arch = REDIS_ARCH_CLUSTER;
//                } else {
//                    arch = vo.getArch();
//                }
//                instanceDto.setArch(arch);
//
//                //将pod信息转换为实例信息
//                AppInstanceVO instanceVO = appService.podToCloudAppInstance(vo, pod);
//                instanceDto.setInstName(instanceVO.getPodName());
//                instanceDto.setInstAlias(instanceVO.getPodName());
//                instanceDto.setIp(instanceVO.getIp());
//                // TODO--修改port获取
////                instanceDto.setPort(instanceVO.getPort());
//                instanceDto.setPort(appKind.getDbPort() + "");
//                instanceDto.setInstRole(INSTANCE_ROLE_SLAVE);
//                try{
//                    //获取nodeip
//                    Node node = client.getNode(instanceVO.getNode());
//                    //getAddresses是数组，但是只获取第一个，查看是否获取到nodeIp
//                    instanceDto.setVip(node.getStatus().getAddresses().get(0).getAddress());
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    log.error("get nodeName error", e);
//                }
//                //网络分区，传kubeId
//                instanceDto.setNetworkPartition(vo.getKubeId());
//                instanceDto.setSlaveIp("");
//                instanceDtoList.add(instanceDto);
//            }
//        }
//        return instanceDtoList;
//    }

    public List<AppInstanceVO> findInstanceList(Integer appId, AppKind appKind) {
        // fetch role of each app instance
        AppKindService instance = AppServiceLoader.getInstance(appKind);
        return instance.findInstanceList(appId, null, null);
    }

    /**
     *
     * @param vo
     * @param podList
     * @param ports key是podip,value是nodeport
     * @return
     */
//    private List<CloudManageClusterDTO.InstanceDTO> scaleOutDealInstance(CloudAppVO vo, List<AppInstanceVO> podList, Map<String,Integer> ports) {
//        KubeClient client = kubeClientService.get(vo.getKubeId());
//        AppKind kind = AppKind.valueOf(vo.getKind(), vo.getArch());
//        List<CloudManageClusterDTO.InstanceDTO> instanceDtoList = new ArrayList<>();
//        if(!CollectionUtils.isEmpty(podList)) {
//            for (AppInstanceVO instanceVO : podList) {
//                // new instance class
//                CloudManageClusterDTO.InstanceDTO instanceDto = new CloudManageClusterDTO.InstanceDTO();
//                // add value
//                if(StringUtils.isEmpty(vo.getUsername())) {
//                    instanceDto.setUsername(!vo.getKind().equalsIgnoreCase("mysql") ? "" : "k8sadmin");
//                } else {
//                    instanceDto.setUsername(vo.getUsername());
//                }
//
//                if(StringUtils.isEmpty(vo.getPassword())) {
//                    instanceDto.setPassword(!vo.getKind().equalsIgnoreCase("mysql") ? "" : "k8sadmin");
//                } else {
//                    instanceDto.setPassword(vo.getPassword());
//                }
//
//                instanceDto.setOperating(INSTANCE_OPERATOR_ADD);    //ADD、DEL、UPDATE
//                instanceDto.setVersion(vo.getVersion());
//                String arch;
//                if (kind == AppKind.Redis){
//                    arch = REDIS_ARCH_HA;
//                } else if (kind == AppKind.Redis_Cluster){
//                    arch = REDIS_ARCH_CLUSTER;
//                } else {
//                    arch = vo.getArch();
//                }
//                instanceDto.setArch(arch);
//                //将pod信息转换为实例信息
////                AppInstanceVO instanceVO = appService.podToCloudAppInstance(vo, pod);
//                instanceDto.setInstName(instanceVO.getPodName());
//                instanceDto.setInstAlias(instanceVO.getPodName());
//                instanceDto.setIp(instanceVO.getIp());
//
//                if (!CollectionUtils.isEmpty(ports) && ports.get(instanceVO.getIp()) != null){
//                    instanceDto.setVip_port(ports.get(instanceVO.getIp()).toString());
//                }
//                // TODO--修改port获取
////                instanceDto.setPort(instanceVO.getPort());
//                instanceDto.setPort(kind.getDbPort() + "");
//                if (ROLE_PRIMARY.equals(instanceVO.getRole())) {
//                    instanceDto.setInstRole(INSTANCE_ROLE_MASTER);
//                } else {
//                    instanceDto.setInstRole(INSTANCE_ROLE_SLAVE);
//                }
//                try{
//                    //获取nodeip
//                    Node node = client.getNode(instanceVO.getNode());
//                    //getAddresses是数组，但是只获取第一个，查看是否获取到nodeIp
//                    instanceDto.setVip(node.getStatus().getAddresses().get(0).getAddress());
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    log.error("get nodeName error", e);
//                }
//                //网络分区，传kubeId
//                instanceDto.setNetworkPartition(vo.getKubeId());
//                instanceDto.setSlaveIp("");
//                instanceDtoList.add(instanceDto);
//            }
//        }
//        return instanceDtoList;
//    }

    /**
     * 根据appId和kubeId获取该应用所有pod
     * @param vo
     * @return
     */
    private List<PodDTO> getPodList(CloudAppVO vo) {
        AppKind appKind = AppKind.valueOf(vo.getKind(), vo.getArch());
        return kubeClientService.get(vo.getKubeId()).listPod(vo.getNamespace(), appKind.labelOfPod(vo));
    }


    /**
     * 调用DMP纳管集群接口
     * @param vo
     * @return
     */
//    public CloudManageClusterDTO.ClusterDTO managedCluster(CloudAppVO vo) {
//        CloudManageClusterDTO.ClusterDTO clusterDto = new CloudManageClusterDTO.ClusterDTO();
//        clusterDto.setCluName(vo.getName());
//        log.info("vo{}" + vo.toString());
//        log.info("appId{} " + vo.getId());
//        clusterDto.setAppId(vo.getId());
//        //是数据库还是中间件
//        clusterDto.setCluType(getDbOrMid(vo));
//        clusterDto.setDbType(vo.getKind().toUpperCase());
//        clusterDto.setOperating(CLUSTER_OPERATOR_ADD);
//        CloudTenant tenant = tenantService.findById(vo.getOwnerTenant());
//        clusterDto.setDeptId(tenant.getExternalId());
//        log.info("managedCluster-------------------------tenantId {}" + tenant.getExternalId());
//        return clusterDto;
//    }

    /**
     * 判断应用是数据库还是中间件
     * @param vo
     * @return
     */
    private String getDbOrMid(CloudAppVO vo) {
        AppKind appKind = AppKind.valueOf(vo.getKind(), vo.getArch());
        if(AppKind.MYSQL_HA == appKind
                || AppKind.Redis_Cluster.equals(appKind)
                || AppKind.Redis.equals(appKind)
                || AppKind.MongoDB.equals(appKind)
                || AppKind.MongoDB_Cluster.equals(appKind)
                || AppKind.OpenGauss.equals(appKind)
                || AppKind.PostgreSQL.equals(appKind)) {
            return CLUSTER_TYPE_DB;
        } else {
            return CLUSTER_TYPE_MID;
        }
    }

    /**
     * 调用CMDB接口，删除应用，该应用下所有实例对应DB删除，对应集群删除
     * @param appId 应用ID，用来找到对应dbId
     */
    public void deleteInstance(Integer appId, CloudAppVO vo) {
//        // cloud_app_pdbinfo表中查询appId找到对应dbId
//        List<AppPdbinfo> appPdbinfoList = findByAppId(appId);
//        if(CollectionUtils.isEmpty(appPdbinfoList)) {
//            return;
////            throw new CustomException(600, "没有找到纳管记录");
//        }
//        int cluId = appPdbinfoList.get(0).getCluId();
//        CloudManageClusterDTO cloudManageClusterDto = new CloudManageClusterDTO();
//        CloudManageClusterDTO.ClusterDTO clusterDto = new CloudManageClusterDTO.ClusterDTO();
//        clusterDto.setOperating(CLUSTER_OPERATOR_DEL);
//
//        clusterDto.setCluType(getDbOrMid(vo));
//        clusterDto.setCluId(cluId);
//        cloudManageClusterDto.setClusterDto(clusterDto);
//        try {
//            CloudManageClusterVO cloudManageClusterVO = cmdbUtil.cloudManageCluster(cloudManageClusterDto);
//            appPdbinfoMapper.updateStatus(SCHEMA, CLOUD_APP_PDBINFO, cloudManageClusterVO.getInstIdList());
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("manage error", e);
//            throw new CustomException(600, "取消纳管失败！");
//        }
    }


    /**
     * 扩容: cluId  operating: UPDATE    podIp  operating: ADD  AllAttr
     * scaleInBeforePodList: 缩容前所有pod
     * @param podIpAndNodePorts podip和nodeport 映射,传null时认为没有访问管理
     *
     */
    public void scaleOut(CloudApp app, List<String> scaleOutBeforePodList, Map<String,Integer> podIpAndNodePorts, Integer cluId) {
//        log.info("[OperationUtil.scaleOut] appId:{},scaleOutBeforePodList:{},podIpAndNodePorts:{}",app.getId(),JSONObject.toJSONString(scaleOutBeforePodList),podIpAndNodePorts);
//        CloudManageClusterDTO cloudManageClusterDto = new CloudManageClusterDTO();
//        CloudManageClusterDTO.ClusterDTO clusterDto = new CloudManageClusterDTO.ClusterDTO();
//        clusterDto.setCluId(cluId);
//        clusterDto.setDbType(app.getKind().toUpperCase());
//        clusterDto.setAppId(app.getId());
//        CloudAppVO vo = new CloudAppVO();
//        BeanUtils.copyProperties(app, vo);
//        clusterDto.setCluType(getDbOrMid(vo));
//        clusterDto.setOperating(CLUSTER_OPERATOR_UPDATE);
//        clusterDto.setAppId(app.getId());
//        CloudTenant tenant = tenantService.findById(vo.getOwnerTenant());
//        clusterDto.setDeptId(tenant.getExternalId());
//        cloudManageClusterDto.setClusterDto(clusterDto);
////        List<PodDTO> podList = getPodList(vo);
//        List<AppInstanceVO> addPodList = null;
//        AppKind kind = AppKind.valueOf(vo.getKind(), vo.getArch());
//        List<AppInstanceVO> instanceList = findInstanceList(app.getId(), kind);
//        if (AppKind.Elasticsearch.equals(kind) || AppKind.MongoDB.equals(kind)){
//            // es、mongodb replicaset直接根据podname进行过滤
//            addPodList = instanceList.stream()
//                    .filter(pod -> !scaleOutBeforePodList.contains(pod.getPodName()))
//                    .collect(Collectors.toList());
//        } else {
//            addPodList = instanceList.stream()
//                    .filter(pod -> !scaleOutBeforePodList.contains(pod.getIp()))
//                    .collect(Collectors.toList());
//        }
//        // get add pods
//        List<CloudManageClusterDTO.InstanceDTO> instanceDtoList = scaleOutDealInstance(vo, addPodList, podIpAndNodePorts);
//       /* if (StringUtils.isEmpty(podIpAndNodePorts)){
//            instanceDtoList = scaleOutCreateInstance(vo,addPodList);
//        }else {
//        }*/
//        log.info("[OperationUtil.scaleOut] 扩容纳管实例列表 instanceDtoList:{}", JSONObject.toJSONString(instanceDtoList));
//        cloudManageClusterDto.setInstanceDTOList(instanceDtoList);
//        List<AppPdbinfo> appPdbinfoList;
//        List<Integer> newDbId =null;
//        try {
//            /*总体逻辑：
//            1.调度第一次进来为了调用cmdb进行纳管
//            2.调度第二次进来为了修改上一次纳管的为1的节点的状态*/
//            Map<String, Object> map = new HashMap<>();
//            map.put("appId", vo.getId());
//            appPdbinfoList = appPdbinfoMapper.findByMap(SCHEMA, CLOUD_APP_PDBINFO, map);
//            List<AppPdbinfo> cannulaing = appPdbinfoList.stream().filter(e -> e.getRealStatus() == 0).collect(Collectors.toList()); // 纳管中
//            List<AppPdbinfo> dmpCannulaed = appPdbinfoList.stream().filter(e -> e.getRealStatus() == 1).collect(Collectors.toList());//dmp纳管完成
//            List<AppPdbinfo> cloudCannulaed = appPdbinfoList.stream().filter(e -> e.getRealStatus() == 2).collect(Collectors.toList());//容器云纳管完成
//            if (cloudCannulaed.size() == instanceList.size()) {
//                //当纳管完成的状态为2的数据与实际的pod数量相同时则退出纳管
//                return;
//            }
//            if (cannulaing.size() + dmpCannulaed.size() + cloudCannulaed.size() < instanceList.size()) {
//                //当查询出来的pdbinfo数量小于实际的pod数量，说明最新扩容的节点还未被纳管，需要纳管
//                cmdbUtil.cloudManageCluster(cloudManageClusterDto);
//                throw new CustomException(600, "此次纳管完成等待下一次触发更新状态");
//            } else if (dmpCannulaed.size() + cloudCannulaed.size() == instanceList.size()) {
//                if ((AppKind.Kafka.equals(kind) ||
//                        AppKind.Broker.equals(kind))){
//                    //在未更新前查出上一次调用新增的status为1的dbid
//                    appPdbinfoList = appPdbinfoMapper.findByMap(SCHEMA, CLOUD_APP_PDBINFO, map);
//                    newDbId = appPdbinfoList.stream().filter(e->e.getRealStatus() == 1).map(t->t.getDbId()).collect(Collectors.toList());
//                }
//                //当数量相同的时候，代表已经纳管过了，此时需要去更新状态
//                dmpCannulaed.stream().forEach(e -> {
//                    e.setKubeId(vo.getKubeId());
//                    e.setKind(vo.getKind());
//                    e.setRealStatus(2);
//                    appPdbinfoMapper.update(SCHEMA, CLOUD_APP_PDBINFO, e);
//                });
//            }
//
//        } catch (Exception e) {
//            log.error("纳管集群到DMP失败！" + e.getMessage());
//            if (e.getMessage().contains("Duplicate entry")){
//                // 纳管对象已存在，正常返回·
//                return;
//            }
//            throw new CustomException(600, "纳管集群到DMP失败！");
//        }

    }

    /**
     * 迁移功能专用的扩容，
     * @param scaleOutBeforePodList：扩容前所有pod
     * @param podIpAndNodePorts：podip和nodeport映射
     * @param cluId：集群id
     */
    public void scaleOut4Migrate(CloudApp app, List<String> scaleOutBeforePodList, Map<String,Integer> podIpAndNodePorts, Integer cluId) {
//        log.info("[OperationUtil.scaleOut] appId:{},scaleOutBeforePodList:{},podIpAndNodePorts:{}", app.getId(), JsonUtil.toJson(scaleOutBeforePodList), podIpAndNodePorts);
//        CloudManageClusterDTO cloudManageClusterDto = new CloudManageClusterDTO();
//        CloudManageClusterDTO.ClusterDTO clusterDto = new CloudManageClusterDTO.ClusterDTO();
//        clusterDto.setCluId(cluId);
//        clusterDto.setDbType(app.getKind().toUpperCase());
//        clusterDto.setAppId(app.getId());
//        CloudAppVO vo = new CloudAppVO();
//        BeanUtils.copyProperties(app, vo);
//        clusterDto.setCluType(getDbOrMid(vo));
//        clusterDto.setOperating(CLUSTER_OPERATOR_UPDATE);
//        clusterDto.setAppId(app.getId());
//        CloudTenant tenant = tenantService.findById(vo.getOwnerTenant());
//        clusterDto.setDeptId(tenant.getExternalId());
//        cloudManageClusterDto.setClusterDto(clusterDto);
//        AppKind kind = AppKind.valueOf(vo.getKind(), vo.getArch());
//        List<AppInstanceVO> allInstanceList = findInstanceList(app.getId(), kind);
//        List<AppInstanceVO> addInstanceList;
//
//        if (AppKind.Elasticsearch.equals(kind) || AppKind.MongoDB.equals(kind)){
//            // es、mongodb replicaset直接根据podname进行过滤
//            addInstanceList = allInstanceList.stream().filter(pod -> !scaleOutBeforePodList.contains(pod.getPodName())).collect(Collectors.toList());
//        } else {
//            addInstanceList = allInstanceList.stream().filter(pod -> !scaleOutBeforePodList.contains(pod.getIp())).collect(Collectors.toList());
//        }
//        // get add pods
//        List<CloudManageClusterDTO.InstanceDTO> instanceDtoList = scaleOutDealInstance(vo, addInstanceList, podIpAndNodePorts);
//        log.info("[OperationUtil.scaleOut] 扩容纳管实例列表 instanceDtoList:{}", JsonUtil.toJson(instanceDtoList));
//        cloudManageClusterDto.setInstanceDTOList(instanceDtoList);
//        List<AppPdbinfo> appPdbinfoList;
//        try {
//            // 查询cmdb已纳管的实例，对比本次纳管的实例，如果包含本次纳管的全部实例则退出
//            List<Integer> dbIdByCluId = cmdbUtil.getDbIdByCluId(cluId);
//            List<MetaVO> cmdbClusterData = cmdbUtil.getIpByDbid(dbIdByCluId.toArray(new Integer[0]));
//            List<String> cmdbInstanceIp = cmdbClusterData.stream().map(MetaVO::getIp).collect(Collectors.toList());
//            if (addInstanceList.stream().allMatch(podDTO -> cmdbInstanceIp.contains(podDTO.getIp()))) {
//                //当目前的pod被已纳管时则认为已完成。dmp侧冗余实例由后续缩容操作去除
//                return;
//            }
//            // 调用cmdb 纳管实例
//            cmdbUtil.cloudManageCluster(cloudManageClusterDto);
//            appPdbinfoList = appPdbinfoMapper.findByMap(SCHEMA, CLOUD_APP_PDBINFO, ImmutableMap.of("appId", vo.getId()));
//            List<AppPdbinfo> dmpCannulaed = appPdbinfoList.stream().filter(e -> e.getRealStatus() == 1).collect(Collectors.toList());//dmp纳管完成
//            if (dmpCannulaed.size() == addInstanceList.size()) {
//                dmpCannulaed.forEach(e -> {
//                    e.setKubeId(vo.getKubeId());
//                    e.setKind(vo.getKind());
//                    e.setRealStatus(2);
//                    appPdbinfoMapper.update(SCHEMA, CLOUD_APP_PDBINFO, e);
//                });
//            } else {
//                // 本次调用cmdb结果未达到预期，等待下一次触发
//                throw new CustomException(600, "此次纳管完成等待下一次触发更新状态");
//            }
//        } catch (Exception e) {
//            if (e.getMessage().contains("Duplicate entry")){
//                // 纳管对象已存在，正常返回·
//                return;
//            }
//            log.error("纳管集群到DMP失败！" + e.getMessage());
//            throw new CustomException(600, "纳管集群到DMP失败！");
//        }
    }

    /**
     *
     * @param app
     * @param ipList
     * @param podIpAndNodePorts podip和nodeport 映射,传null时认为没有访问管理
     */
    public void scaleOut(CloudApp app, List<String> ipList, Map<String,Integer> podIpAndNodePorts) {
        List<AppPdbinfo> infos = findByAppId(app.getId());
        int cluId = infos.get(0).getCluId();
//        scaleOut(app, ipList, podIpAndNodePorts, cluId);
    }

    /**
     *
     * scaleInBeforePodNameList代表缩容前的所有pod
     */
    public void scaleIn(CloudApp app, List<String> scaleInBeforePodNameList) {
//        CloudManageClusterDTO cloudManageClusterDto = new CloudManageClusterDTO();
//        CloudManageClusterDTO.ClusterDTO clusterDto = new CloudManageClusterDTO.ClusterDTO();
//        List<AppPdbinfo> infos = findByAppId(app.getId());
//        int cluId = infos.get(0).getCluId();
//        clusterDto.setCluId(cluId);
//        clusterDto.setAppId(app.getId());
//        clusterDto.setDbType(app.getKind().toUpperCase());
//        clusterDto.setOperating(CLUSTER_OPERATOR_UPDATE);
//        CloudAppVO vo = new CloudAppVO();
//        BeanUtils.copyProperties(app, vo);
//        clusterDto.setCluType(getDbOrMid(vo));
//        CloudTenant tenant = tenantService.findById(vo.getOwnerTenant());
//        clusterDto.setDeptId(tenant.getExternalId());
//        cloudManageClusterDto.setClusterDto(clusterDto);
//        List<CloudManageClusterDTO.InstanceDTO> instanceDtoList = new ArrayList<>();
//        List<PodDTO> podList = getPodList(vo);
//        //过滤出被缩掉的pod，然后查询出它的dbid
//        List<String> scaleInAfterPodNameList = podList.stream().map(PodDTO::getPodName).collect(Collectors.toList());
//        List<String> delPodList = scaleInBeforePodNameList.stream().filter(e-> !scaleInAfterPodNameList.contains(e)).collect(Collectors.toList());
//        //根据podname删除cmdb中的记录
//        for (String s : delPodList) {
//            CloudManageClusterDTO.InstanceDTO instanceDto = new CloudManageClusterDTO.InstanceDTO();
//            instanceDto.setInstName(s);
//            instanceDto.setOperating(INSTANCE_OPERATOR_DEL);
//            instanceDto.setNetworkPartition(app.getKubeId());
//            instanceDtoList.add(instanceDto);
//        }
//        cloudManageClusterDto.setInstanceDTOList(instanceDtoList);
//        try {
//            CloudManageClusterVO cloudManageClusterVO = cmdbUtil.cloudManageCluster(cloudManageClusterDto);
//            if (!CollectionUtils.isEmpty(cloudManageClusterVO.getInstIdList())) {
//                appPdbinfoMapper.deletePdbInfoByDbId(SCHEMA, CLOUD_APP_PDBINFO, cloudManageClusterVO.getInstIdList());
//            }
//        } catch (Exception e) {
//            log.error("纳管实例到DMP失败！" + e.getMessage());
//            throw new CustomException(600, "纳管实例到DMP失败！");
//        }
    }

    /**
     * 节点迁移: cluId  operating: UPDATE    podIp  operating: DEL   删除旧的，添加新的  此时nodeIP也会改变
     *
     */
    public void nodeMigration() {

    }

    /**
     * 创建service后推送vipPort给cmdb   需要ip
     * 访问管理相关  推送nodePort(vipPort)
     * 1. 新建service,推送nodePort到cmdb,推送其他关联参数,podIp等
     * 2. 删除service,
     * 3. 修改service
     * @param app
     * @param nodePort
     */
    public void managerVipPort(CloudApp app, String nodePort, ActionEnum action) {
//        CloudManageClusterDTO cloudManageClusterDto = new CloudManageClusterDTO();
//        CloudManageClusterDTO.ClusterDTO clusterDto = new CloudManageClusterDTO.ClusterDTO();
//        List<AppPdbinfo> infos = findByAppId(app.getId());
//        if (CollectionUtils.isEmpty(infos)){
//            return;
//        }
//        int cluId = infos.get(0).getCluId();
//        clusterDto.setCluId(cluId);
//        clusterDto.setDbType(app.getKind().toUpperCase());
//        clusterDto.setOperating(CLUSTER_OPERATOR_UPDATE);
//        clusterDto.setAppId(app.getId());
//        CloudAppVO vo = new CloudAppVO();
//        BeanUtils.copyProperties(app, vo);
//        clusterDto.setCluType(getDbOrMid(vo));
//        CloudTenant tenant = tenantService.findById(vo.getOwnerTenant());
//        clusterDto.setDeptId(tenant.getExternalId());
//        cloudManageClusterDto.setClusterDto(clusterDto);
//        List<CloudManageClusterDTO.InstanceDTO> instanceDtoList = new ArrayList<>();
//        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
//        boolean isMultiPort = false;
//        if(AppKind.Broker.equals(appKind) || AppKind.Kafka.equals(appKind) || AppKind.MYSQL_HA.equals(appKind)
//                || AppKind.OpenGauss.equals(appKind) || AppKind.PostgreSQL.equals(appKind) ||
//            AppKind.Redis.equals(appKind) || AppKind.Redis_Cluster.equals(appKind)) {
//            isMultiPort = true;
//        }
//        //实例名称与角色map
//        Map<String, String> instanceRoleMap = new HashMap<>();
//        //dbid与角色map
//        Map<Integer, String> dbidAndRoleMap = new HashMap<>();
//        //dbid与ip
//        KubeClient kubeClient = clientService.get(app.getKubeId());
//        Map<Integer, String> dbidAndIpMap = new HashMap<>();
//        if (AppKind.MYSQL_HA.equals(appKind) || AppKind.PostgreSQL.equals(appKind) || AppKind.OpenGauss.equals(appKind)) {
//            List<PodDTO> pods = kubeClient.listPod(app.getNamespace(),appKind.labelOfPod(app));
//            for (PodDTO pod : pods) {
//                String role;
//                if (AppKind.OpenGauss.equals(appKind)) {
//                    role = pod.getLabels().get("opengauss.role");
//                } else {
//                    role = pod.getLabels().get("app.kubernetes.io/role");
//                }
//                instanceRoleMap.put(pod.getPodIp(), role);
//            }
//        }
//        //获取所有dbid，用来查询mysql的角色
//        Integer[] dbidsArr = infos.stream().map(AppPdbinfo::getDbId).toArray(Integer[]::new);
//        try {
//            List<PDbInfo> pDbByPDbids = cmdbUtil.getPDbByPDbids(dbidsArr);
//            for(PDbInfo pDbInfo : pDbByPDbids){
//                Integer dbid = pDbInfo.getDbid();
//                String role = instanceRoleMap.get(pDbInfo.getIp());
//                dbidAndRoleMap.put(dbid, role);
//                dbidAndIpMap.put(dbid, pDbInfo.getIp());
//            }
//        } catch (Exception e) {
//            log.error("根据dbids获取dbinfo信息失败！" + e.getMessage());
//            throw new CustomException(600, "根据dbids获取dbinfo信息失败！");
//        }
//        List<PodDTO> pods = kubeClient.listPod(app.getNamespace(),appKind.labelOfPod(app));
//        String hostIP = pods.get(0).getPod().getStatus().getHostIP();
//        for (AppPdbinfo info : infos) {
//            CloudManageClusterDTO.InstanceDTO instanceDto = new CloudManageClusterDTO.InstanceDTO();
//            instanceDto.setInstId(info.getDbId());
//            if (isMultiPort) {
//                String vipPort = "";
//                if (!DELETE_SERVICE.equals(action)) {
//                    String ip = dbidAndIpMap.get(info.getDbId());
//                    instanceDto.setIp(ip);
//                    instanceDto.setPort(appKind.getDbPort() + "");
//                    if (AppKind.Kafka.equals(appKind)) {
//                        Kafka kafkaCrByYaml = YamlEngine.unmarshal(app.getCrRun(), Kafka.class);
//                        Integer port = kafkaCrByYaml.getSpec().getIpList().stream().filter(ipListConfig -> ipListConfig.getIp().equals(ip))
//                                .findFirst().orElseThrow(() ->new CustomException(600, "根据获取kafka ip信息失败！")).getPort();
//                        vipPort = port.toString();
//                    } else if (AppKind.Broker.equals(appKind)) {
//                        Broker brokerFromAppCr = YamlEngine.unmarshal(app.getCrRun(), Broker.class);
//                        Integer port = Arrays.stream(brokerFromAppCr.getSpec().getIpList()).filter(ipListConfig ->
//                                ipListConfig.getIp().equals(ip))
//                                .findFirst().orElseThrow(() ->new CustomException(600, "根据获取Broker ip信息失败！")).getPort();
//                        vipPort = port.toString();
//                    } else if (AppKind.Redis.equals(appKind)) {
//                        Redis redisCrByYaml = YamlEngine.unmarshal(app.getCrRun(), Redis.class);
//                        Integer port = redisCrByYaml.getSpec().getIpList().stream().filter(ipListConfig ->
//                                ipListConfig.getIp().equals(ip))
//                                .findFirst().orElseThrow(() ->new CustomException(600, "根据获取Redis ip信息失败！")).getPort();
//                        vipPort = port.toString();
//                    } else if (AppKind.Redis_Cluster.equals(appKind)) {
//                        RedisCluster redisClusterCrByYaml = YamlEngine.unmarshal(app.getCrRun(), RedisCluster.class);
//                        Integer port =  redisClusterCrByYaml.getSpec().getIpList().stream().filter(ipListConfig ->
//                                ipListConfig.getIp().equals(ip))
//                                .findFirst().orElseThrow(() ->new CustomException(600, "根据获取Redis Cluster ip信息失败！")).getPort();
//                        vipPort = port.toString();
//                    }  else if (AppKind.MYSQL_HA.equals(appKind) || AppKind.OpenGauss.equals(appKind) || appKind.PostgreSQL.equals(appKind)) {
//                        List<String> nodePorts = Arrays.asList(nodePort.split(","));
//                        String role = dbidAndRoleMap.get(info.getDbId());
//                        //获取角色，如果角色为主，则传写端口；如果角色为从，则传读端口
//                        if ("source".equalsIgnoreCase(role) || "primary".equals(role)) {
//                            vipPort = nodePorts.get(0);
//                        } else {
//                            vipPort = nodePorts.get(1);
//                        }
//                    }
//                    instanceDto.setVip(hostIP);
//                    instanceDto.setVip_port(vipPort);
//                } else {
//                    // 删除访问管理要清理dmp侧vip、vip_port
//                    instanceDto.setVip("");
//                    instanceDto.setVip_port("");
//                }
//            } else {
//                if (!DELETE_SERVICE.equals(action)) {
//                    instanceDto.setVip(hostIP);
//                    instanceDto.setVip_port(nodePort);
//                } else {
//                    // 删除访问管理要清理dmp侧vip、vip_port
//                    instanceDto.setVip("");
//                    instanceDto.setVip_port("");
//                }
//            }
//            instanceDto.setOperating(INSTANCE_OPERATOR_UPDATE);
//            instanceDto.setNetworkPartition(app.getKubeId());
//            instanceDtoList.add(instanceDto);
//        }
//        cloudManageClusterDto.setInstanceDTOList(instanceDtoList);
//        try {
//            cmdbUtil.cloudManageCluster(cloudManageClusterDto);
//        } catch (Exception e) {
//            log.error("同步nodePort信息失败！" + e.getMessage());
//            throw new CustomException(600, "同步nodePort信息失败！");
//        }
    }

    public void scaleOutmanagerVipPort(CloudApp app, String nodePort,List<Integer> newDbId) {
//        //根据dbid查询cmdb表中的数据，
//        String schema = UserUtil.getSchema();
//        CloudManageClusterDTO cloudManageClusterDto = new CloudManageClusterDTO();
//        List<CloudManageClusterDTO.InstanceDTO> instanceDtoList = new ArrayList<>();
//        try {
//            for (int i = 0; i < newDbId.size(); i++) {
//                MetaVO pdbInfo = cmdbUtil.getDbInfoByIdAndSchema(newDbId.get(i), schema);
//                CloudManageClusterDTO.InstanceDTO instanceDto =new  CloudManageClusterDTO.InstanceDTO();
//                instanceDto.setInstName(pdbInfo.getDbname());
//                instanceDto.setIp(pdbInfo.getIp());
//                instanceDto.setInstId(pdbInfo.getDbid());
//                instanceDto.setOperating(INSTANCE_OPERATOR_UPDATE);
//                instanceDto.setNetworkPartition(app.getKubeId());
//                instanceDtoList.add(instanceDto);
//            }
//            cloudManageClusterDto.setInstanceDTOList(instanceDtoList);
//        }catch (Exception e){
//            log.error("根据dbid调用cmdb查询pdb失败");
//        }
//        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
//        if(AppKind.Broker.equals(appKind)){
//            Broker cr= kubeClientService.get(app.getKubeId()).listCustomResource(Broker.class, app.getCrName(), app.getNamespace());
//            Broker.IpListConfig[] ipList = cr.getStatus().getSpec().getIpList();
//            List<Broker.IpListConfig> ipListConfigs = Arrays.asList(ipList);
//            List<String> nodePorts = Arrays.asList(nodePort.split(","));
//            for (int i = 0; i < ipListConfigs.size(); i++) {
//                Broker.IpListConfig ipListConfig = ipListConfigs.get(i);
//                for (int j = 0; j < instanceDtoList.size(); j++) {
//                    if (ipListConfig.getIp().equals(instanceDtoList.get(j).getIp()) && nodePorts.contains(Integer.toString(ipListConfig.getPort()))){
//                        //当ip相同并且在扩容的端口号中的时候，将端口号设置到实例对象中
//                        instanceDtoList.get(j).setVip_port(Integer.toString(ipListConfig.getPort()));
//                    }
//                }
//            }
//        } else if(AppKind.Kafka.equals(appKind)){
//            Kafka cr= kubeClientService.get(app.getKubeId()).listCustomResource(Kafka.class, app.getCrName(), app.getNamespace());
//            List<IpList> ipList = cr.getStatus().getSpec().getIpList();
//            List<String> nodePorts = Arrays.asList(nodePort.split(","));
//            for (int i = 0; i < ipList.size(); i++) {
//                IpList ipListConfig = ipList.get(i);
//                for (int j = 0; j < instanceDtoList.size(); j++) {
//                    if (ipListConfig.getIp().equals(instanceDtoList.get(j).getIp()) && nodePorts.contains(Integer.toString(ipListConfig.getPort()))){
//                        //当ip相同并且在扩容的端口号中的时候，将端口号设置到实例对象中
//                        instanceDtoList.get(j).setVip_port(Integer.toString(ipListConfig.getPort()));
//                    }
//                }
//            }
//        }
//        CloudManageClusterDTO.ClusterDTO clusterDto = new CloudManageClusterDTO.ClusterDTO();
//        List<AppPdbinfo> infos = findByAppId(app.getId());
//        int cluId = infos.get(0).getCluId();
//        clusterDto.setCluId(cluId);
//        clusterDto.setDbType(app.getKind().toUpperCase());
//        clusterDto.setOperating(CLUSTER_OPERATOR_UPDATE);
//        clusterDto.setAppId(app.getId());
//        CloudAppVO vo = new CloudAppVO();
//        BeanUtils.copyProperties(app, vo);
//        clusterDto.setCluType(getDbOrMid(vo));
//        CloudTenant tenant = tenantService.findById(vo.getOwnerTenant());
//        clusterDto.setDeptId(tenant.getExternalId());
//        cloudManageClusterDto.setClusterDto(clusterDto);
//        try {
//            cmdbUtil.cloudManageCluster(cloudManageClusterDto);
//        } catch (Exception e) {
//            log.error("同步nodePort信息失败！" + e.getMessage());
//            throw new CustomException(600, "同步nodePort信息失败！");
//        }
    }

    public List<AppPdbinfo> findByAppId(Integer appId) {
        return appPdbinfoMapper.findByAppId(SCHEMA, CLOUD_APP_PDBINFO, appId);
    }

    /**
     * 修改版本、端口、密码方法
     *
     * @param app
     * @param triggerHis
     */
    public void alterToDMP(CloudApp app, TriggerHis triggerHis) {
        //转化为对象
            // todo 灾备切换是否重新纳管
        if (!CloudAppConstant.ROLE_PRIMARY.equals(app.getRole()))
            return ;
        CloudAppVO vo = new CloudAppVO();
        BeanUtils.copyProperties(app, vo);
        //转化为用户对象UserInfo
        String userInfoStr = triggerHis.getJobDataMap().get("userInfo");
        UserInfo userInfo = JSONObject.parseObject(userInfoStr, UserInfo.class);
        UserUtil.setAsyncUserInfo(userInfo);
        //获取密码，没有则放空
        String extDataStr = triggerHis.getJobDataMap().get("extDataStr");
        JSONObject extDataObj = null;
        if (StringUtils.isNotEmpty(extDataStr)) {
            extDataObj = JsonUtil.toObject(JSONObject.class, extDataStr);
        }
        //mongodb类型没有watch，直接放入了app属性
        if (!ObjectUtils.isEmpty(extDataObj)) {
            String password = extDataObj.getString("password");
            if (!StringUtils.isBlank(password)) {
                vo.setPassword(password);
            }
        }
        alterApp(vo);
    }

    public void alterApp(CloudAppVO vo) {
        // 1.构造纳管对象
        CloudInstDTO cloudInstDTO = new CloudInstDTO();
        //根据namespace查询部门id
//        Optional<CloudTenant> cloudTenant = tenantService.findByNamespace(vo.getNamespace());
//        if (!cloudTenant.isPresent()) {
//            throw new CustomException(500, "变更纳管应用失败！未找到租户！");
//        }
//        Integer externalId = cloudTenant.get().getExternalId();
//        ArrayList<Integer> externalIds = new ArrayList<>();
//        externalIds.add(externalId);
//        cloudInstDTO.setOrgIds(externalIds);
        cloudInstDTO.setExternalId(vo.getLogicAppId().toString());
        //放入版本、端口、密码
        cloudInstDTO.setVersion(vo.getVersion());
        //查询访问管理
        List<ServiceManager> serviceManagers = accessManagementService.AZServiceList(vo.getLogicAppId());
        if (CollectionUtils.isEmpty(serviceManagers)) {
            throw new CustomException(500, "变更纳管应用失败！未找到访问管理信息！");
        }
        List<ServiceManager> serviceManagerWrite = serviceManagers.stream().filter(
                serviceManager -> serviceManager.getAppId().equals(vo.getId())
                        && serviceManager.getPurpose().equals(CloudAppConstant.ServicePurpose.WRITE))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(serviceManagerWrite)) {
            throw new CustomException(500, "变更纳管应用失败！未找到访问管理writeService！");
        }
        cloudInstDTO.setAccessIp(serviceManagerWrite.get(0).getServiceIps());
        cloudInstDTO.setAccessPort(ObjectUtils.isEmpty(serviceManagerWrite.get(0).getPort()) ? null : String.valueOf(serviceManagerWrite.get(0).getPort()));
        cloudInstDTO.setPassword(vo.getPassword());
        log.info("变更纳管对象：" + cloudInstDTO.toString());
        // 2.纳管
        ResponseResult<Integer> resp = managerService.save(cloudInstDTO);
        // 判断结果
        if (!(StringUtils.isNotEmpty(resp.getMsg()) && resp.getMsg().equalsIgnoreCase("success"))) {
            log.error("纳管日志：" + resp.getMsg());
            throw new CustomException(500, "变更纳管失败！");
        }
    }

    public static class ManageUnfinishedException extends RuntimeException{
        public ManageUnfinishedException(String msg) {
            super(msg);
        }
    }

    public static class ManageFailedException extends RuntimeException{
        public ManageFailedException(Exception e) {
            super(e);
        }
    }

    public static class UnexpectedManageException extends RuntimeException{
        public UnexpectedManageException(String msg) {
            super(msg);
        }
    }
}
