
2025-07-25 09:30:06.954       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 09:30:06.978       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 09:30:06.967       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 09:30:06.988       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 09:30:11.076       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 09:30:11.079       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 09:30:11.079       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 09:30:11.083       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 09:31:41.552       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 09:31:41.558       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 09:31:41.814       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 09:31:41.816       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 09:46:01.608       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 09:46:01.624       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 09:46:01.674       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 09:46:01.687       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 09:46:02.767       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 09:46:02.769       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 09:46:02.840       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 09:46:02.841       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 10:15:33.213       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 10:15:33.213       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 10:15:33.309       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 10:15:33.310       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 10:37:24.052       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 10:37:24.059       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 10:37:24.139       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 10:37:24.139       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 10:37:33.828       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 10:37:33.831       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 10:37:33.900       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 10:37:33.901       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 10:50:49.207       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 10:50:49.208       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 10:50:49.274       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 10:50:49.275       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 10:51:12.651       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 10:51:12.658       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 10:51:12.714       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 10:51:12.715       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 10:58:04.538       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 10:58:04.540       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 10:58:04.589       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 10:58:04.590       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 10:58:13.321       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 10:58:13.328       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 10:58:13.377       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 10:58:13.378       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:02:07.443       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:02:07.443       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:02:07.504       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:02:07.505       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:03:20.801       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:03:20.800       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:03:20.866       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:03:20.867       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:22:33.136       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:22:33.143       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:22:33.144       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:22:33.146       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:22:34.196       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:22:34.197       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:22:34.200       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:22:34.201       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:22:38.522       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.repository.KubeApiClientFabric  LINE:156
				MESSAGE:Failure executing: GET at: https://192.168.12.76:6443/apis/metrics.k8s.io/v1beta1/namespaces/lianzb-tt/pods. Message: 404 page not found
.

2025-07-25 11:22:38.789       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.repository.KubeApiClientFabric  LINE:156
				MESSAGE:Failure executing: GET at: https://192.168.12.76:6443/apis/metrics.k8s.io/v1beta1/namespaces/lianzb-tt/pods. Message: 404 page not found
.

2025-07-25 11:22:38.990       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.repository.KubeApiClientFabric  LINE:156
				MESSAGE:Failure executing: GET at: https://192.168.12.76:6443/apis/metrics.k8s.io/v1beta1/namespaces/lianzb-tt/pods. Message: 404 page not found
.

2025-07-25 11:22:53.559       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:22:53.603       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:22:53.637       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:22:53.637       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:22:59.768       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:22:59.773       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:22:59.808       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:22:59.809       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:22:59.976       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:22:59.982       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:23:00.023       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:23:00.024       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:23:02.360       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:23:02.360       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:23:02.398       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:23:02.408       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:23:03.356       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:23:03.357       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:23:03.380       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:23:03.380       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:23:18.278       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:23:18.286       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:23:18.318       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:23:18.319       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:23:21.474       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:23:21.476       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:23:21.519       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:23:21.519       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:23:23.895       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:23:23.899       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:23:23.934       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:23:23.934       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:23:29.634       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:23:29.634       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:23:29.666       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:23:29.666       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:31:04.804       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.repository.PrometheusClient  LINE:540
				MESSAGE:
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://192.168.12.76:30911/-/healthy": connect timed out; nested exception is java.net.SocketTimeoutException: connect timed out
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:791)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:717)
	at org.springframework.web.client.RestTemplate.getForEntity(RestTemplate.java:367)
	at cn.newdt.cloud.repository.PrometheusClient.checkHealth(PrometheusClient.java:536)
	at cn.newdt.cloud.service.impl.KubeConfigServiceImpl.checkPrometheus(KubeConfigServiceImpl.java:1200)
	at cn.newdt.cloud.service.impl.KubeConfigServiceImpl.lambda$checkCluster$23(KubeConfigServiceImpl.java:798)
	at cn.newdt.cloud.config.FutureService.lambda$submit$0(FutureService.java:30)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at sun.net.NetworkClient.doConnect(NetworkClient.java:175)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:463)
	at sun.net.www.http.HttpClient.openServer(HttpClient.java:558)
	at sun.net.www.http.HttpClient.<init>(HttpClient.java:242)
	at sun.net.www.http.HttpClient.New(HttpClient.java:339)
	at sun.net.www.http.HttpClient.New(HttpClient.java:357)
	at sun.net.www.protocol.http.HttpURLConnection.getNewHttpClient(HttpURLConnection.java:1228)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect0(HttpURLConnection.java:1162)
	at sun.net.www.protocol.http.HttpURLConnection.plainConnect(HttpURLConnection.java:1056)
	at sun.net.www.protocol.http.HttpURLConnection.connect(HttpURLConnection.java:990)
	at org.springframework.http.client.SimpleBufferingClientHttpRequest.executeInternal(SimpleBufferingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:66)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:782)
	... 12 common frames omitted

2025-07-25 11:31:19.837       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:31:19.841       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:31:19.884       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:31:19.885       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:31:21.964       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:31:21.965       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:31:22.006       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:31:22.007       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:31:22.143       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:31:22.143       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:31:22.180       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:31:22.181       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:32:23.608       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:32:23.611       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:32:23.646       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:32:23.647       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:32:32.225       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:32:32.225       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:32:32.231       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:32:32.231       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:32:33.040       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:32:33.041       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:32:33.145       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:32:33.146       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:32:39.987       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:32:39.992       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:32:40.047       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:32:40.048       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:33:05.288       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:33:05.288       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:33:05.338       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:33:05.339       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:34:43.816       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:34:43.821       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:34:43.879       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:34:43.880       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:36:57.323       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertConfigSyncService  LINE:123
				MESSAGE:[alert config sync] failed
java.lang.NullPointerException: null
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.lambda$newRuleGroup$14(AlertConfigSyncService.java:540)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:174)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.newRuleGroup(AlertConfigSyncService.java:560)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.syncRules(AlertConfigSyncService.java:387)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.syncConfig(AlertConfigSyncService.java:192)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.lambda$enqueueSyncTask$3(AlertConfigSyncService.java:119)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 11:37:34.598       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertConfigSyncService  LINE:123
				MESSAGE:[alert config sync] failed
java.lang.NullPointerException: null
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.lambda$newRuleGroup$14(AlertConfigSyncService.java:540)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:174)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.newRuleGroup(AlertConfigSyncService.java:560)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.syncRules(AlertConfigSyncService.java:387)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.syncConfig(AlertConfigSyncService.java:192)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.lambda$enqueueSyncTask$3(AlertConfigSyncService.java:119)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 11:39:05.667       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:39:05.667       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:39:05.719       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:39:05.721       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:39:50.476       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:39:50.476       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:39:50.530       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:39:50.530       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:39:56.611       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:39:56.615       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:39:56.652       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:39:56.653       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:40:00.622       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:40:00.626       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:40:00.785       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:40:00.789       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:40:53.119       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:40:53.123       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:40:53.169       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:40:53.171       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:40:56.002       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:40:56.011       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:40:56.045       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:40:56.046       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:41:18.029       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:41:18.032       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:41:18.080       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:41:18.081       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:41:38.843       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:41:38.847       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:41:38.920       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:41:38.921       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:41:39.215       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:41:39.226       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:41:39.261       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:41:39.263       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:41:50.704       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:41:50.708       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:41:50.755       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:41:50.756       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:41:51.721       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:41:51.727       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:41:51.771       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:41:51.773       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:41:54.371       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:41:54.373       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:41:54.411       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:41:54.412       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:41:54.511       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:41:54.514       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:41:54.544       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:41:54.544       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:42:36.692       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:42:36.707       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:42:36.757       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:42:36.757       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:42:45.712       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:42:45.715       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:42:45.766       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:42:45.768       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:42:45.882       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:42:45.882       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:42:45.922       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:42:45.924       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:26.366       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:43:26.368       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:43:26.405       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:26.405       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:26.509       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:43:26.513       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:43:26.575       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:26.576       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:27.465       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:43:27.472       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:43:27.505       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:27.507       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:29.303       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:43:29.305       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:43:29.347       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:29.347       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:29.453       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:43:29.454       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:43:29.504       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:29.505       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:30.641       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:43:30.644       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:43:30.704       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:30.704       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:31.557       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:43:31.562       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:43:31.604       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:31.604       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:32.276       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:43:32.280       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:43:32.318       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:32.319       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:33.600       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:43:33.603       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:43:33.638       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:33.639       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:33.727       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:43:33.727       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:43:33.763       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:33.764       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:34.221       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:43:34.225       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:43:34.266       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:34.268       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:36.361       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:43:36.361       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:43:37.038       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:37.039       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:44.419       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:43:44.419       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:43:44.466       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:43:44.467       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:44:20.217       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:44:20.217       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:44:20.255       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:44:20.256       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:45:26.019       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:45:26.024       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:45:26.066       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:45:26.067       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:45:45.741       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:45:45.742       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:45:45.748       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:45:45.760       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:45:46.743       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:45:46.744       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:45:46.841       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:45:46.842       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:46:19.367       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:46:19.370       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:46:19.408       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:46:19.410       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:46:26.627       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:46:26.632       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:46:26.664       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:46:26.665       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:47:31.158       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:47:31.161       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:47:31.161       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:47:31.172       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:47:32.147       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:47:32.152       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:47:32.200       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:47:32.202       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:51:35.128       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:51:35.132       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:51:35.161       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:51:35.162       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:59:21.983       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 11:59:21.997       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 11:59:23.485       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 11:59:23.486       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 12:02:58.104       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 12:02:58.105       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 12:02:58.994       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 12:02:58.996       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 13:43:00.612       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 13:43:00.602       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 13:43:00.809       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 13:43:00.809       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 13:43:27.297       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 13:43:27.298       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 13:43:27.299       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 13:43:27.343       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 13:43:29.354       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 13:43:29.356       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 13:43:29.362       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 13:43:29.408       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 13:45:00.418       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 13:45:00.413       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 13:45:00.489       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 13:45:00.490       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 13:45:59.330       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 13:45:59.342       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 13:45:59.382       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 13:45:59.384       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 13:46:47.011       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 13:46:47.014       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 13:46:47.054       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 13:46:47.055       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 13:49:47.184       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 13:49:47.189       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 13:49:47.241       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 13:49:47.242       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 13:54:00.298       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 13:54:00.300       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 13:54:00.341       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 13:54:00.342       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:13:41.945       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 14:13:41.948       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 14:13:42.023       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:13:42.024       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:14:17.967       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 14:14:17.972       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 14:14:18.015       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:14:18.016       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:15:45.015       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 14:15:45.021       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 14:15:45.122       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:15:45.123       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:18:34.574       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 14:18:34.602       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 14:18:34.636       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:18:34.637       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:35:30.362       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 14:35:30.361       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 14:35:30.475       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:35:30.476       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:36:31.724       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 14:36:31.724       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 14:36:32.419       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:36:32.420       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:38:36.101       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 14:38:36.106       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 14:38:36.127       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 14:38:36.103       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 14:38:37.931       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:38:37.937       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:38:38.021       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:38:38.033       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:38:41.196       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 14:38:41.268       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 14:38:41.372       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:38:41.374       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:38:48.188       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 14:38:48.203       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 14:38:48.249       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:38:48.251       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:46:52.109       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 14:46:52.114       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 14:46:52.505       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:46:52.507       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 14:58:38.770       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertConfigSyncService  LINE:123
				MESSAGE:[alert config sync] failed
java.lang.NullPointerException: null
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.lambda$newRuleGroup$14(AlertConfigSyncService.java:540)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:174)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.newRuleGroup(AlertConfigSyncService.java:560)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.syncRules(AlertConfigSyncService.java:387)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.syncConfig(AlertConfigSyncService.java:192)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.lambda$enqueueSyncTask$3(AlertConfigSyncService.java:119)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 15:06:14.744       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 15:06:14.744       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 15:06:14.834       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:06:14.835       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:08:32.439       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 15:08:32.442       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 15:08:32.483       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:08:32.484       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:16:42.562       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 15:16:42.573       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 15:16:42.658       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:16:42.659       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:18:53.759       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 15:18:53.764       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 15:18:54.666       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:18:54.668       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:19:47.980       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 15:19:48.003       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 15:19:49.088       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:19:49.090       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:20:20.181       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 15:20:20.263       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 15:20:21.265       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:20:21.276       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:20:42.777       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 15:20:42.782       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 15:20:43.654       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:20:43.656       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:20:45.559       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 15:20:45.567       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 15:20:46.528       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:20:46.529       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:21:37.378       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 15:21:37.378       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 15:21:37.436       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:21:37.437       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:25:05.385       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 15:25:05.432       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 15:25:05.510       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:25:05.511       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:41:43.652       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 15:41:43.658       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 15:41:43.771       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:41:43.773       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:45:31.914       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 15:45:31.916       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 15:45:31.962       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:45:31.963       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:54:25.564       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 15:54:25.569       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 15:54:25.572       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 15:54:25.586       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 15:54:27.587       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:54:27.588       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:54:27.640       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:54:27.641       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:54:34.079       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:180
				MESSAGE:异常ID:06489b82-46c6-4781-8d73-6370597fa9c9, 请求地址:http://192.168.2.217:8300/cloudmanage/inspect/rules
com.google.common.util.concurrent.UncheckedExecutionException: org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'schedule' in 'field list'
### The error may exist in file [C:\Users\<USER>\workspace\pass\shindatacloudservice\target\classes\mybatis\InspectRuleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select             id, name, category, description, kind, type, enabled, created_at, updated_at, severity,      schedule           ,             rule           from cust1.cloud_inspect_rule
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'schedule' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'schedule' in 'field list'
	at com.google.common.cache.LocalCache$Segment.get(LocalCache.java:2087)
	at com.google.common.cache.LocalCache.get(LocalCache.java:4019)
	at com.google.common.cache.LocalCache.getOrLoad(LocalCache.java:4042)
	at com.google.common.cache.LocalCache$LocalLoadingCache.get(LocalCache.java:5024)
	at cn.newdt.cloud.service.inspect.InspectConfig$Cache.get(InspectConfig.java:135)
	at cn.newdt.cloud.service.inspect.InspectConfig$Config.getInspectrules(InspectConfig.java:64)
	at cn.newdt.cloud.service.inspect.InspectConfig.getEnabledRules(InspectConfig.java:31)
	at cn.newdt.cloud.service.inspect.InspectService.getRules(InspectService.java:232)
	at cn.newdt.cloud.web.InspectController.getRules(InspectController.java:48)
	at cn.newdt.cloud.web.InspectController$$FastClassBySpringCGLIB$$e79221c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.InspectController$$EnhancerBySpringCGLIB$$54e8b6e5.getRules(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.filter.CloudManageFilter.doFilter(CloudManageFilter.java:35)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'schedule' in 'field list'
### The error may exist in file [C:\Users\<USER>\workspace\pass\shindatacloudservice\target\classes\mybatis\InspectRuleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select             id, name, category, description, kind, type, enabled, created_at, updated_at, severity,      schedule           ,             rule           from cust1.cloud_inspect_rule
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'schedule' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'schedule' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:236)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at cn.newdt.commons.config.CustomMyBatisExceptionTranslator.translateExceptionIfPossible(CustomMyBatisExceptionTranslator.java:50)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446)
	at com.sun.proxy.$Proxy148.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:230)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:139)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:76)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59)
	at com.sun.proxy.$Proxy260.list(Unknown Source)
	at cn.newdt.cloud.service.inspect.InspectRuleService.listEnabled(InspectRuleService.java:20)
	at cn.newdt.cloud.service.inspect.InspectConfig$Config.lambda$new$0(InspectConfig.java:60)
	at cn.newdt.cloud.service.inspect.InspectConfig$Cache$1.load(InspectConfig.java:129)
	at cn.newdt.cloud.service.inspect.InspectConfig$Cache$1.load(InspectConfig.java:126)
	at com.google.common.cache.LocalCache$LoadingValueReference.loadFuture(LocalCache.java:3576)
	at com.google.common.cache.LocalCache$Segment.loadSync(LocalCache.java:2318)
	at com.google.common.cache.LocalCache$Segment.lockedGetOrLoad(LocalCache.java:2191)
	at com.google.common.cache.LocalCache$Segment.get(LocalCache.java:2081)
	... 123 common frames omitted
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'schedule' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:95)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:960)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:388)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:63)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:326)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:169)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy303.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141)
	at sun.reflect.GeneratedMethodAccessor77.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433)
	... 137 common frames omitted

2025-07-25 15:54:38.415       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:180
				MESSAGE:异常ID:9e288be3-6d38-41ff-b51c-421bc5025f8e, 请求地址:http://192.168.2.217:8300/cloudmanage/inspect/task/64/result
com.google.common.util.concurrent.UncheckedExecutionException: org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'schedule' in 'field list'
### The error may exist in file [C:\Users\<USER>\workspace\pass\shindatacloudservice\target\classes\mybatis\InspectRuleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select             id, name, category, description, kind, type, enabled, created_at, updated_at, severity,      schedule           ,             rule           from cust1.cloud_inspect_rule
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'schedule' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'schedule' in 'field list'
	at com.google.common.cache.LocalCache$Segment.get(LocalCache.java:2087)
	at com.google.common.cache.LocalCache.get(LocalCache.java:4019)
	at com.google.common.cache.LocalCache.getOrLoad(LocalCache.java:4042)
	at com.google.common.cache.LocalCache$LocalLoadingCache.get(LocalCache.java:5024)
	at cn.newdt.cloud.service.inspect.InspectConfig$Cache.get(InspectConfig.java:135)
	at cn.newdt.cloud.service.inspect.InspectConfig$Config.getInspectrules(InspectConfig.java:64)
	at cn.newdt.cloud.service.inspect.InspectConfig.getEnabledRules(InspectConfig.java:31)
	at cn.newdt.cloud.service.inspect.InspectJobTaskService.getResults(InspectJobTaskService.java:70)
	at cn.newdt.cloud.service.inspect.InspectJobTaskService$$FastClassBySpringCGLIB$$6a83bc3a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at cn.newdt.cloud.service.inspect.InspectJobTaskService$$EnhancerBySpringCGLIB$$6b4ca428.getResults(<generated>)
	at cn.newdt.cloud.service.inspect.InspectService.getTaskResults(InspectService.java:236)
	at cn.newdt.cloud.web.InspectController.taskResult(InspectController.java:53)
	at cn.newdt.cloud.web.InspectController$$FastClassBySpringCGLIB$$e79221c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.InspectController$$EnhancerBySpringCGLIB$$54e8b6e5.taskResult(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.filter.CloudManageFilter.doFilter(CloudManageFilter.java:35)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'schedule' in 'field list'
### The error may exist in file [C:\Users\<USER>\workspace\pass\shindatacloudservice\target\classes\mybatis\InspectRuleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select             id, name, category, description, kind, type, enabled, created_at, updated_at, severity,      schedule           ,             rule           from cust1.cloud_inspect_rule
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'schedule' in 'field list'
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Unknown column 'schedule' in 'field list'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:236)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73)
	at cn.newdt.commons.config.CustomMyBatisExceptionTranslator.translateExceptionIfPossible(CustomMyBatisExceptionTranslator.java:50)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:446)
	at com.sun.proxy.$Proxy148.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:230)
	at org.apache.ibatis.binding.MapperMethod.executeForMany(MapperMethod.java:139)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:76)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59)
	at com.sun.proxy.$Proxy260.list(Unknown Source)
	at cn.newdt.cloud.service.inspect.InspectRuleService.listEnabled(InspectRuleService.java:20)
	at cn.newdt.cloud.service.inspect.InspectConfig$Config.lambda$new$0(InspectConfig.java:60)
	at cn.newdt.cloud.service.inspect.InspectConfig$Cache$1.load(InspectConfig.java:129)
	at cn.newdt.cloud.service.inspect.InspectConfig$Cache$1.load(InspectConfig.java:126)
	at com.google.common.cache.LocalCache$LoadingValueReference.loadFuture(LocalCache.java:3576)
	at com.google.common.cache.LocalCache$Segment.loadSync(LocalCache.java:2318)
	at com.google.common.cache.LocalCache$Segment.lockedGetOrLoad(LocalCache.java:2191)
	at com.google.common.cache.LocalCache$Segment.get(LocalCache.java:2081)
	... 130 common frames omitted
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'schedule' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:95)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:960)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:388)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:63)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:326)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:169)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy303.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141)
	at sun.reflect.GeneratedMethodAccessor77.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433)
	... 144 common frames omitted

2025-07-25 15:55:04.345       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 15:55:04.354       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 15:55:04.405       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:55:04.406       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:55:43.301       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 15:55:43.302       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 15:55:43.320       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 15:55:43.320       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 15:55:44.178       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:55:44.180       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:55:44.236       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 15:55:44.237       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:17:57.986       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:17:57.995       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:18:02.456       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:18:02.460       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:18:27.033       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:18:27.087       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:18:27.445       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:18:27.500       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:18:44.082       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:18:44.097       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:18:44.441       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:18:44.444       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:19:07.073       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:19:07.089       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:19:07.852       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:19:07.860       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:19:33.564       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:19:33.564       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:19:33.605       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:19:33.621       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:19:35.142       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:19:35.150       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:19:35.423       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:19:35.424       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:20:47.188       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:20:47.195       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:20:47.379       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:20:47.380       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:20:52.568       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:20:52.612       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:20:52.730       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:20:52.731       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:21:03.581       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:21:03.591       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:21:03.636       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:21:03.637       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:21:12.399       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:21:12.405       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:21:12.453       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:21:12.453       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:21:28.111       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:21:28.142       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:21:28.271       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:21:28.273       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:22:35.453       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:22:35.463       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:22:35.520       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:22:35.522       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:22:59.444       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:22:59.456       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:23:00.807       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:23:00.808       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:23:09.217       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:23:09.227       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:23:09.773       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:23:09.775       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:23:20.037       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:23:20.043       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:23:21.170       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:23:21.172       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:24:40.127       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:24:40.140       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:24:41.005       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:24:41.007       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:24:48.584       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:180
				MESSAGE:异常ID:, 请求地址:http://192.168.2.217:8300/cloudmanage/alert/silence/8afc5672-6717-4eab-a6b2-21bef6a31c08
org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'resourceId' for method parameter type Integer is not present
	at org.springframework.web.method.annotation.RequestParamMethodArgumentResolver.handleMissingValueInternal(RequestParamMethodArgumentResolver.java:218)
	at org.springframework.web.method.annotation.RequestParamMethodArgumentResolver.handleMissingValue(RequestParamMethodArgumentResolver.java:193)
	at org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:114)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:179)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:146)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doDelete(FrameworkServlet.java:931)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:561)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.filter.CloudManageFilter.doFilter(CloudManageFilter.java:35)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 16:25:20.032       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:25:20.044       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:25:21.044       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:25:21.046       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:26:00.501       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:26:00.503       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:26:01.273       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:26:01.274       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:29:44.789       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:29:44.796       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:29:45.309       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:29:45.310       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:29:47.185       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:29:47.193       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:29:47.250       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:29:47.251       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:30:06.707       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertConfigSyncService  LINE:123
				MESSAGE:[alert config sync] failed
java.lang.NullPointerException: null
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.lambda$newRuleGroup$14(AlertConfigSyncService.java:540)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:174)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1384)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.newRuleGroup(AlertConfigSyncService.java:560)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.syncRules(AlertConfigSyncService.java:387)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.syncConfig(AlertConfigSyncService.java:192)
	at cn.newdt.cloud.service.alert.AlertConfigSyncService.lambda$enqueueSyncTask$3(AlertConfigSyncService.java:119)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 16:30:19.018       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:30:19.067       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:30:19.231       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:30:19.250       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:30:19.256       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:30:19.257       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:30:19.382       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:30:19.383       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:30:29.002       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:30:29.035       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:30:29.035       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:30:29.051       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:30:30.777       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:30:30.780       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:30:30.831       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:30:30.832       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:32:57.385       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:32:57.385       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:32:57.393       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:32:57.393       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:32:57.513       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:32:57.514       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:32:57.537       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:32:57.538       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:34:06.639       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:34:06.655       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:34:06.717       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:34:06.718       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:34:20.639       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.utils.BackupStorageUtils  LINE:116
				MESSAGE:[downloadBackupFile] 从S3获取文件流失败
io.minio.errors.ErrorResponseException: The specified key does not exist.
	at io.minio.S3Base$1.onResponse(S3Base.java:789)
	at io.minio.S3Base$1.onResponse(S3Base.java:625)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 16:34:24.430       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.utils.BackupStorageUtils  LINE:116
				MESSAGE:[downloadBackupFile] 从S3获取文件流失败
io.minio.errors.ErrorResponseException: The specified key does not exist.
	at io.minio.S3Base$1.onResponse(S3Base.java:789)
	at io.minio.S3Base$1.onResponse(S3Base.java:625)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 16:35:14.269       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.utils.BackupStorageUtils  LINE:116
				MESSAGE:[downloadBackupFile] 从S3获取文件流失败
io.minio.errors.ErrorResponseException: The specified key does not exist.
	at io.minio.S3Base$1.onResponse(S3Base.java:789)
	at io.minio.S3Base$1.onResponse(S3Base.java:625)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 16:40:25.027       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.utils.BackupStorageUtils  LINE:116
				MESSAGE:[downloadBackupFile] 从S3获取文件流失败
io.minio.errors.ErrorResponseException: The specified key does not exist.
	at io.minio.S3Base$1.onResponse(S3Base.java:789)
	at io.minio.S3Base$1.onResponse(S3Base.java:625)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 16:43:17.167       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:43:17.166       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:43:17.370       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:43:17.372       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:51:44.677       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:51:44.685       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:51:44.729       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 16:51:44.776       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 16:51:45.421       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:51:45.515       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:51:45.828       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 16:51:45.829       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:03:21.111       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:03:21.137       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:03:21.253       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:03:21.254       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:05:48.026       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:05:48.026       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:05:48.031       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:05:48.031       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:05:48.082       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:05:48.083       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:05:48.084       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:05:48.085       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:06:25.404       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:06:25.542       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:06:25.665       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:06:25.679       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:06:25.741       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:06:25.742       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:06:25.826       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:06:25.827       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:07:58.335       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:07:58.335       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:07:58.340       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:07:58.340       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:07:58.375       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:07:58.376       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:07:58.376       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:07:58.376       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:09:37.339       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:09:37.340       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:09:37.348       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:09:37.358       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:09:37.381       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:09:37.381       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:09:37.396       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:09:37.396       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:14:51.720       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:14:51.723       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:14:51.726       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:14:51.726       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:14:51.767       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:14:51.767       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:14:51.768       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:14:51.768       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:22:26.671       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:22:26.685       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:22:26.785       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:22:26.787       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:27:04.496       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:27:04.500       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:27:04.600       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:27:04.603       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:27:36.234       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:27:36.250       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:27:36.302       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:27:36.303       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:30:24.253       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:30:24.264       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:30:24.312       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:30:24.314       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:34:42.180       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:34:42.180       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:34:42.194       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:34:42.217       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:34:42.298       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:34:42.298       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:34:42.348       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:34:42.352       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:36:06.424       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:36:06.438       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:36:06.519       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:36:06.519       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:36:40.323       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:36:40.363       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:36:40.451       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:36:40.458       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:40:41.848       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:40:41.859       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:40:41.911       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:40:41.911       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:40:58.157       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.inspect.CommandRuleExecutor  LINE:70 
				MESSAGE:
java.util.concurrent.ExecutionException: io.fabric8.kubernetes.client.KubernetesClientException: Failure executing: POST at: https://192.168.12.9:16443/api/v1/namespaces/ndt-operator-system/pods. Message: namespaces "ndt-operator-system" not found. Received status: Status(apiVersion=v1, code=404, details=StatusDetails(causes=[], group=null, kind=namespaces, name=ndt-operator-system, retryAfterSeconds=null, uid=null, additionalProperties={}), kind=Status, message=namespaces "ndt-operator-system" not found, metadata=ListMeta(_continue=null, remainingItemCount=null, resourceVersion=null, selfLink=null, additionalProperties={}), reason=NotFound, status=Failure, additionalProperties={}).
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.execute(CommandRuleExecutor.java:68)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:364)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:353)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.fabric8.kubernetes.client.KubernetesClientException: Failure executing: POST at: https://192.168.12.9:16443/api/v1/namespaces/ndt-operator-system/pods. Message: namespaces "ndt-operator-system" not found. Received status: Status(apiVersion=v1, code=404, details=StatusDetails(causes=[], group=null, kind=namespaces, name=ndt-operator-system, retryAfterSeconds=null, uid=null, additionalProperties={}), kind=Status, message=namespaces "ndt-operator-system" not found, metadata=ListMeta(_continue=null, remainingItemCount=null, resourceVersion=null, selfLink=null, additionalProperties={}), reason=NotFound, status=Failure, additionalProperties={}).
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.requestFailure(OperationSupport.java:697)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.requestFailure(OperationSupport.java:676)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.assertResponseCode(OperationSupport.java:629)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleResponse(OperationSupport.java:566)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleResponse(OperationSupport.java:527)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleCreate(OperationSupport.java:315)
	at io.fabric8.kubernetes.client.dsl.base.BaseOperation.handleCreate(BaseOperation.java:651)
	at io.fabric8.kubernetes.client.dsl.base.BaseOperation.handleCreate(BaseOperation.java:91)
	at io.fabric8.kubernetes.client.dsl.base.CreateOnlyResourceOperation.create(CreateOnlyResourceOperation.java:61)
	at cn.newdt.cloud.repository.KubeApiClientFabric.createPob(KubeApiClientFabric.java:532)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at cn.newdt.cloud.repository.KubeClientManager$KubeClientInterceptor.invoke(KubeClientManager.java:98)
	at com.sun.proxy.$Proxy237.createPob(Unknown Source)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.executeOnNode(CommandRuleExecutor.java:108)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.lambda$execute$0(CommandRuleExecutor.java:68)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1596)
	at java.util.concurrent.ForkJoinTask.doExec$$$capture(ForkJoinTask.java:289)
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java)
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1056)
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1692)
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:175)

2025-07-25 17:40:58.160       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.inspect.CommandRuleExecutor  LINE:70 
				MESSAGE:
java.util.concurrent.ExecutionException: io.fabric8.kubernetes.client.KubernetesClientException: Failure executing: POST at: https://192.168.12.9:16443/api/v1/namespaces/ndt-operator-system/pods. Message: namespaces "ndt-operator-system" not found. Received status: Status(apiVersion=v1, code=404, details=StatusDetails(causes=[], group=null, kind=namespaces, name=ndt-operator-system, retryAfterSeconds=null, uid=null, additionalProperties={}), kind=Status, message=namespaces "ndt-operator-system" not found, metadata=ListMeta(_continue=null, remainingItemCount=null, resourceVersion=null, selfLink=null, additionalProperties={}), reason=NotFound, status=Failure, additionalProperties={}).
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.execute(CommandRuleExecutor.java:68)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:364)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:353)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.fabric8.kubernetes.client.KubernetesClientException: Failure executing: POST at: https://192.168.12.9:16443/api/v1/namespaces/ndt-operator-system/pods. Message: namespaces "ndt-operator-system" not found. Received status: Status(apiVersion=v1, code=404, details=StatusDetails(causes=[], group=null, kind=namespaces, name=ndt-operator-system, retryAfterSeconds=null, uid=null, additionalProperties={}), kind=Status, message=namespaces "ndt-operator-system" not found, metadata=ListMeta(_continue=null, remainingItemCount=null, resourceVersion=null, selfLink=null, additionalProperties={}), reason=NotFound, status=Failure, additionalProperties={}).
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.requestFailure(OperationSupport.java:697)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.requestFailure(OperationSupport.java:676)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.assertResponseCode(OperationSupport.java:629)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleResponse(OperationSupport.java:566)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleResponse(OperationSupport.java:527)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleCreate(OperationSupport.java:315)
	at io.fabric8.kubernetes.client.dsl.base.BaseOperation.handleCreate(BaseOperation.java:651)
	at io.fabric8.kubernetes.client.dsl.base.BaseOperation.handleCreate(BaseOperation.java:91)
	at io.fabric8.kubernetes.client.dsl.base.CreateOnlyResourceOperation.create(CreateOnlyResourceOperation.java:61)
	at cn.newdt.cloud.repository.KubeApiClientFabric.createPob(KubeApiClientFabric.java:532)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at cn.newdt.cloud.repository.KubeClientManager$KubeClientInterceptor.invoke(KubeClientManager.java:98)
	at com.sun.proxy.$Proxy237.createPob(Unknown Source)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.executeOnNode(CommandRuleExecutor.java:108)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.lambda$execute$0(CommandRuleExecutor.java:68)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1596)
	at java.util.concurrent.ForkJoinTask.doExec$$$capture(ForkJoinTask.java:289)
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java)
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1056)
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1692)
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:175)

2025-07-25 17:41:28.236       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.inspect.CommandRuleExecutor  LINE:70 
				MESSAGE:
java.util.concurrent.TimeoutException: null
	at java.util.concurrent.CompletableFuture.timedGet(CompletableFuture.java:1784)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.execute(CommandRuleExecutor.java:68)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:364)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:353)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 17:41:28.236       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.inspect.CommandRuleExecutor  LINE:70 
				MESSAGE:
java.util.concurrent.TimeoutException: null
	at java.util.concurrent.CompletableFuture.timedGet(CompletableFuture.java:1784)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.execute(CommandRuleExecutor.java:68)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:364)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:353)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 17:41:28.236       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.inspect.CommandRuleExecutor  LINE:70 
				MESSAGE:
java.util.concurrent.TimeoutException: null
	at java.util.concurrent.CompletableFuture.timedGet(CompletableFuture.java:1784)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.execute(CommandRuleExecutor.java:68)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:364)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:353)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 17:41:28.314       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.inspect.CommandRuleExecutor  LINE:70 
				MESSAGE:
java.util.concurrent.TimeoutException: null
	at java.util.concurrent.CompletableFuture.timedGet(CompletableFuture.java:1784)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.execute(CommandRuleExecutor.java:68)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:364)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:353)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 17:46:31.853       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:46:31.862       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:46:31.870       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:46:31.870       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:46:31.926       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:46:31.928       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:46:31.938       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:46:31.939       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:47:02.271       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:47:02.286       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:47:02.368       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:47:02.368       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:47:23.080       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:47:23.089       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:47:23.147       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:47:23.148       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:53:00.760       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:53:00.760       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:53:00.760       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 17:53:00.764       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 17:53:00.800       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:53:00.801       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:53:00.809       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:53:00.810       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 17:56:24.741       LEVEL:ERROR ThreadId:
				POSITION:org.springframework.scheduling.quartz.LocalDataSourceJobStore  LINE:4017
				MESSAGE:MisfireHandler: Error handling misfires: Couldn't retrieve trigger: No record found for selection of Trigger with key: 'backup-timer.Dameng-backup-timer-2682-full' and statement: SELECT * FROM ndtmdb.CLOUD_QRTZ_CRON_TRIGGERS WHERE SCHED_NAME = 'schedulerFactoryBean' AND TRIGGER_NAME = ? AND TRIGGER_GROUP = ?
org.quartz.JobPersistenceException: Couldn't retrieve trigger: No record found for selection of Trigger with key: 'backup-timer.Dameng-backup-timer-2682-full' and statement: SELECT * FROM ndtmdb.CLOUD_QRTZ_CRON_TRIGGERS WHERE SCHED_NAME = 'schedulerFactoryBean' AND TRIGGER_NAME = ? AND TRIGGER_GROUP = ?
	at org.quartz.impl.jdbcjobstore.JobStoreSupport.retrieveTrigger(JobStoreSupport.java:1538)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport.recoverMisfiredJobs(JobStoreSupport.java:984)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport.doRecoverMisfires(JobStoreSupport.java:3264)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport$MisfireHandler.manage(JobStoreSupport.java:4012)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport$MisfireHandler.run(JobStoreSupport.java:4033)
Caused by: java.lang.IllegalStateException: No record found for selection of Trigger with key: 'backup-timer.Dameng-backup-timer-2682-full' and statement: SELECT * FROM ndtmdb.CLOUD_QRTZ_CRON_TRIGGERS WHERE SCHED_NAME = 'schedulerFactoryBean' AND TRIGGER_NAME = ? AND TRIGGER_GROUP = ?
	at org.quartz.impl.jdbcjobstore.CronTriggerPersistenceDelegate.loadExtendedTriggerProperties(CronTriggerPersistenceDelegate.java:107)
	at org.quartz.impl.jdbcjobstore.StdJDBCDelegate.selectTrigger(StdJDBCDelegate.java:1819)
	at org.quartz.impl.jdbcjobstore.JobStoreSupport.retrieveTrigger(JobStoreSupport.java:1536)
	... 4 common frames omitted

2025-07-25 17:57:31.783       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.repository.KubeApiClientFabric  LINE:1522
				MESSAGE:exec returns error yantest/pg-yan-dev-172-16-8-104, cmd [sh, -c, psql -U postgres -d postgres -c "create user yhj with password 'Yanhj123' SUPERUSER"]
java.util.concurrent.ExecutionException: cn.newdt.cloud.common.ExecCommandException: ERROR:  role "yhj" already exists

	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.repository.KubeApiClientFabric.execCmd(KubeApiClientFabric.java:1516)
	at cn.newdt.cloud.repository.KubeApiClientFabric.execCmd(KubeApiClientFabric.java:1506)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at cn.newdt.cloud.repository.KubeClientManager$KubeClientInterceptor.invoke(KubeClientManager.java:98)
	at com.sun.proxy.$Proxy237.execCmd(Unknown Source)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.createRootUser(PostgreSqlInstallWatch.java:69)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.doStopWatchResource(PostgreSqlInstallWatch.java:42)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.stopWatchResource(PostgreSqlWatch.java:99)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.postProcess(PostgreSqlWatch.java:45)
	at cn.newdt.cloud.job.KubeJob$1.doInTransactionWithoutResult(KubeJob.java:95)
	at org.springframework.transaction.support.TransactionCallbackWithoutResult.doInTransaction(TransactionCallbackWithoutResult.java:36)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:91)
	at cn.newdt.cloud.job.AbstractJob.execute(AbstractJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: cn.newdt.cloud.common.ExecCommandException: ERROR:  role "yhj" already exists

	at cn.newdt.cloud.config.KubeExecSimpleListener.onClose(KubeExecSimpleListener.java:64)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.onClosed(ExecWebSocketListener.java:267)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.close(ExecWebSocketListener.java:122)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.onClosing(ExecWebSocketListener.java:251)
	at okhttp3.internal.ws.RealWebSocket.onReadClose(RealWebSocket.kt:378)
	at okhttp3.internal.ws.WebSocketReader.readControlFrame(WebSocketReader.kt:220)
	at okhttp3.internal.ws.WebSocketReader.processNextFrame(WebSocketReader.kt:104)
	at okhttp3.internal.ws.RealWebSocket.loopReader(RealWebSocket.kt:293)
	at okhttp3.internal.ws.RealWebSocket$connect$1.onResponse(RealWebSocket.kt:195)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 17:57:31.789       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch  LINE:71 
				MESSAGE:创建用户失败！

2025-07-25 17:57:31.811       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.job.AbstractJob  LINE:44 
				MESSAGE:jobKey:KubeJob.KubeJob;triggerKey:yan-dev.Create_8245
java.lang.RuntimeException: kubeJob exception
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:123)
	at cn.newdt.cloud.job.AbstractJob.execute(AbstractJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: cn.newdt.commons.exception.CustomException: 创建用户失败！
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.createRootUser(PostgreSqlInstallWatch.java:72)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.doStopWatchResource(PostgreSqlInstallWatch.java:42)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.stopWatchResource(PostgreSqlWatch.java:99)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.postProcess(PostgreSqlWatch.java:45)
	at cn.newdt.cloud.job.KubeJob$1.doInTransactionWithoutResult(KubeJob.java:95)
	at org.springframework.transaction.support.TransactionCallbackWithoutResult.doInTransaction(TransactionCallbackWithoutResult.java:36)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:91)
	... 3 common frames omitted

2025-07-25 17:58:01.037       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.repository.KubeApiClientFabric  LINE:1522
				MESSAGE:exec returns error yantest/pg-yan-dev-172-16-8-104, cmd [sh, -c, psql -U postgres -d postgres -c "create user yhj with password 'Yanhj123' SUPERUSER"]
java.util.concurrent.ExecutionException: cn.newdt.cloud.common.ExecCommandException: ERROR:  role "yhj" already exists

	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.repository.KubeApiClientFabric.execCmd(KubeApiClientFabric.java:1516)
	at cn.newdt.cloud.repository.KubeApiClientFabric.execCmd(KubeApiClientFabric.java:1506)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at cn.newdt.cloud.repository.KubeClientManager$KubeClientInterceptor.invoke(KubeClientManager.java:98)
	at com.sun.proxy.$Proxy237.execCmd(Unknown Source)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.createRootUser(PostgreSqlInstallWatch.java:69)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.doStopWatchResource(PostgreSqlInstallWatch.java:42)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.stopWatchResource(PostgreSqlWatch.java:99)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.postProcess(PostgreSqlWatch.java:45)
	at cn.newdt.cloud.job.KubeJob$1.doInTransactionWithoutResult(KubeJob.java:95)
	at org.springframework.transaction.support.TransactionCallbackWithoutResult.doInTransaction(TransactionCallbackWithoutResult.java:36)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:91)
	at cn.newdt.cloud.job.AbstractJob.execute(AbstractJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: cn.newdt.cloud.common.ExecCommandException: ERROR:  role "yhj" already exists

	at cn.newdt.cloud.config.KubeExecSimpleListener.onClose(KubeExecSimpleListener.java:64)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.onClosed(ExecWebSocketListener.java:267)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.close(ExecWebSocketListener.java:122)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.onClosing(ExecWebSocketListener.java:251)
	at okhttp3.internal.ws.RealWebSocket.onReadClose(RealWebSocket.kt:378)
	at okhttp3.internal.ws.WebSocketReader.readControlFrame(WebSocketReader.kt:220)
	at okhttp3.internal.ws.WebSocketReader.processNextFrame(WebSocketReader.kt:104)
	at okhttp3.internal.ws.RealWebSocket.loopReader(RealWebSocket.kt:293)
	at okhttp3.internal.ws.RealWebSocket$connect$1.onResponse(RealWebSocket.kt:195)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 17:58:01.039       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch  LINE:71 
				MESSAGE:创建用户失败！

2025-07-25 17:58:01.055       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.job.AbstractJob  LINE:44 
				MESSAGE:jobKey:KubeJob.KubeJob;triggerKey:yan-dev.Create_8245
java.lang.RuntimeException: kubeJob exception
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:123)
	at cn.newdt.cloud.job.AbstractJob.execute(AbstractJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: cn.newdt.commons.exception.CustomException: 创建用户失败！
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.createRootUser(PostgreSqlInstallWatch.java:72)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.doStopWatchResource(PostgreSqlInstallWatch.java:42)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.stopWatchResource(PostgreSqlWatch.java:99)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.postProcess(PostgreSqlWatch.java:45)
	at cn.newdt.cloud.job.KubeJob$1.doInTransactionWithoutResult(KubeJob.java:95)
	at org.springframework.transaction.support.TransactionCallbackWithoutResult.doInTransaction(TransactionCallbackWithoutResult.java:36)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:91)
	... 3 common frames omitted

2025-07-25 17:58:30.821       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.repository.KubeApiClientFabric  LINE:1522
				MESSAGE:exec returns error yantest/pg-yan-dev-172-16-8-104, cmd [sh, -c, psql -U postgres -d postgres -c "create user yhj with password 'Yanhj123' SUPERUSER"]
java.util.concurrent.ExecutionException: cn.newdt.cloud.common.ExecCommandException: ERROR:  role "yhj" already exists

	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.repository.KubeApiClientFabric.execCmd(KubeApiClientFabric.java:1516)
	at cn.newdt.cloud.repository.KubeApiClientFabric.execCmd(KubeApiClientFabric.java:1506)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at cn.newdt.cloud.repository.KubeClientManager$KubeClientInterceptor.invoke(KubeClientManager.java:98)
	at com.sun.proxy.$Proxy237.execCmd(Unknown Source)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.createRootUser(PostgreSqlInstallWatch.java:69)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.doStopWatchResource(PostgreSqlInstallWatch.java:42)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.stopWatchResource(PostgreSqlWatch.java:99)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.postProcess(PostgreSqlWatch.java:45)
	at cn.newdt.cloud.job.KubeJob$1.doInTransactionWithoutResult(KubeJob.java:95)
	at org.springframework.transaction.support.TransactionCallbackWithoutResult.doInTransaction(TransactionCallbackWithoutResult.java:36)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:91)
	at cn.newdt.cloud.job.AbstractJob.execute(AbstractJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: cn.newdt.cloud.common.ExecCommandException: ERROR:  role "yhj" already exists

	at cn.newdt.cloud.config.KubeExecSimpleListener.onClose(KubeExecSimpleListener.java:64)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.onClosed(ExecWebSocketListener.java:267)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.close(ExecWebSocketListener.java:122)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.onClosing(ExecWebSocketListener.java:251)
	at okhttp3.internal.ws.RealWebSocket.onReadClose(RealWebSocket.kt:378)
	at okhttp3.internal.ws.WebSocketReader.readControlFrame(WebSocketReader.kt:220)
	at okhttp3.internal.ws.WebSocketReader.processNextFrame(WebSocketReader.kt:104)
	at okhttp3.internal.ws.RealWebSocket.loopReader(RealWebSocket.kt:293)
	at okhttp3.internal.ws.RealWebSocket$connect$1.onResponse(RealWebSocket.kt:195)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 17:58:30.823       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch  LINE:71 
				MESSAGE:创建用户失败！

2025-07-25 17:58:30.870       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.job.AbstractJob  LINE:44 
				MESSAGE:jobKey:KubeJob.KubeJob;triggerKey:yan-dev.Create_8245
java.lang.RuntimeException: kubeJob exception
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:123)
	at cn.newdt.cloud.job.AbstractJob.execute(AbstractJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: cn.newdt.commons.exception.CustomException: 创建用户失败！
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.createRootUser(PostgreSqlInstallWatch.java:72)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.doStopWatchResource(PostgreSqlInstallWatch.java:42)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.stopWatchResource(PostgreSqlWatch.java:99)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.postProcess(PostgreSqlWatch.java:45)
	at cn.newdt.cloud.job.KubeJob$1.doInTransactionWithoutResult(KubeJob.java:95)
	at org.springframework.transaction.support.TransactionCallbackWithoutResult.doInTransaction(TransactionCallbackWithoutResult.java:36)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:91)
	... 3 common frames omitted

2025-07-25 17:59:00.934       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.repository.KubeApiClientFabric  LINE:1522
				MESSAGE:exec returns error yantest/pg-yan-dev-172-16-8-104, cmd [sh, -c, psql -U postgres -d postgres -c "create user yhj with password 'Yanhj123' SUPERUSER"]
java.util.concurrent.ExecutionException: cn.newdt.cloud.common.ExecCommandException: ERROR:  role "yhj" already exists

	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.repository.KubeApiClientFabric.execCmd(KubeApiClientFabric.java:1516)
	at cn.newdt.cloud.repository.KubeApiClientFabric.execCmd(KubeApiClientFabric.java:1506)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at cn.newdt.cloud.repository.KubeClientManager$KubeClientInterceptor.invoke(KubeClientManager.java:98)
	at com.sun.proxy.$Proxy237.execCmd(Unknown Source)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.createRootUser(PostgreSqlInstallWatch.java:69)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.doStopWatchResource(PostgreSqlInstallWatch.java:42)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.stopWatchResource(PostgreSqlWatch.java:99)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.postProcess(PostgreSqlWatch.java:45)
	at cn.newdt.cloud.job.KubeJob$1.doInTransactionWithoutResult(KubeJob.java:95)
	at org.springframework.transaction.support.TransactionCallbackWithoutResult.doInTransaction(TransactionCallbackWithoutResult.java:36)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:91)
	at cn.newdt.cloud.job.AbstractJob.execute(AbstractJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: cn.newdt.cloud.common.ExecCommandException: ERROR:  role "yhj" already exists

	at cn.newdt.cloud.config.KubeExecSimpleListener.onClose(KubeExecSimpleListener.java:64)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.onClosed(ExecWebSocketListener.java:267)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.close(ExecWebSocketListener.java:122)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.onClosing(ExecWebSocketListener.java:251)
	at okhttp3.internal.ws.RealWebSocket.onReadClose(RealWebSocket.kt:378)
	at okhttp3.internal.ws.WebSocketReader.readControlFrame(WebSocketReader.kt:220)
	at okhttp3.internal.ws.WebSocketReader.processNextFrame(WebSocketReader.kt:104)
	at okhttp3.internal.ws.RealWebSocket.loopReader(RealWebSocket.kt:293)
	at okhttp3.internal.ws.RealWebSocket$connect$1.onResponse(RealWebSocket.kt:195)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 17:59:00.935       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch  LINE:71 
				MESSAGE:创建用户失败！

2025-07-25 17:59:00.962       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.job.AbstractJob  LINE:44 
				MESSAGE:jobKey:KubeJob.KubeJob;triggerKey:yan-dev.Create_8245
java.lang.RuntimeException: kubeJob exception
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:123)
	at cn.newdt.cloud.job.AbstractJob.execute(AbstractJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: cn.newdt.commons.exception.CustomException: 创建用户失败！
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.createRootUser(PostgreSqlInstallWatch.java:72)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.doStopWatchResource(PostgreSqlInstallWatch.java:42)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.stopWatchResource(PostgreSqlWatch.java:99)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.postProcess(PostgreSqlWatch.java:45)
	at cn.newdt.cloud.job.KubeJob$1.doInTransactionWithoutResult(KubeJob.java:95)
	at org.springframework.transaction.support.TransactionCallbackWithoutResult.doInTransaction(TransactionCallbackWithoutResult.java:36)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:91)
	... 3 common frames omitted

2025-07-25 17:59:30.814       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.repository.KubeApiClientFabric  LINE:1522
				MESSAGE:exec returns error yantest/pg-yan-dev-172-16-8-104, cmd [sh, -c, psql -U postgres -d postgres -c "create user yhj with password 'Yanhj123' SUPERUSER"]
java.util.concurrent.ExecutionException: cn.newdt.cloud.common.ExecCommandException: ERROR:  role "yhj" already exists

	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.repository.KubeApiClientFabric.execCmd(KubeApiClientFabric.java:1516)
	at cn.newdt.cloud.repository.KubeApiClientFabric.execCmd(KubeApiClientFabric.java:1506)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at cn.newdt.cloud.repository.KubeClientManager$KubeClientInterceptor.invoke(KubeClientManager.java:98)
	at com.sun.proxy.$Proxy237.execCmd(Unknown Source)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.createRootUser(PostgreSqlInstallWatch.java:69)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.doStopWatchResource(PostgreSqlInstallWatch.java:42)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.stopWatchResource(PostgreSqlWatch.java:99)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.postProcess(PostgreSqlWatch.java:45)
	at cn.newdt.cloud.job.KubeJob$1.doInTransactionWithoutResult(KubeJob.java:95)
	at org.springframework.transaction.support.TransactionCallbackWithoutResult.doInTransaction(TransactionCallbackWithoutResult.java:36)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:91)
	at cn.newdt.cloud.job.AbstractJob.execute(AbstractJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: cn.newdt.cloud.common.ExecCommandException: ERROR:  role "yhj" already exists

	at cn.newdt.cloud.config.KubeExecSimpleListener.onClose(KubeExecSimpleListener.java:64)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.onClosed(ExecWebSocketListener.java:267)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.close(ExecWebSocketListener.java:122)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.onClosing(ExecWebSocketListener.java:251)
	at okhttp3.internal.ws.RealWebSocket.onReadClose(RealWebSocket.kt:378)
	at okhttp3.internal.ws.WebSocketReader.readControlFrame(WebSocketReader.kt:220)
	at okhttp3.internal.ws.WebSocketReader.processNextFrame(WebSocketReader.kt:104)
	at okhttp3.internal.ws.RealWebSocket.loopReader(RealWebSocket.kt:293)
	at okhttp3.internal.ws.RealWebSocket$connect$1.onResponse(RealWebSocket.kt:195)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 17:59:30.816       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch  LINE:71 
				MESSAGE:创建用户失败！

2025-07-25 17:59:30.833       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.job.AbstractJob  LINE:44 
				MESSAGE:jobKey:KubeJob.KubeJob;triggerKey:yan-dev.Create_8245
java.lang.RuntimeException: kubeJob exception
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:123)
	at cn.newdt.cloud.job.AbstractJob.execute(AbstractJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: cn.newdt.commons.exception.CustomException: 创建用户失败！
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.createRootUser(PostgreSqlInstallWatch.java:72)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.doStopWatchResource(PostgreSqlInstallWatch.java:42)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.stopWatchResource(PostgreSqlWatch.java:99)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.postProcess(PostgreSqlWatch.java:45)
	at cn.newdt.cloud.job.KubeJob$1.doInTransactionWithoutResult(KubeJob.java:95)
	at org.springframework.transaction.support.TransactionCallbackWithoutResult.doInTransaction(TransactionCallbackWithoutResult.java:36)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:91)
	... 3 common frames omitted

2025-07-25 18:00:01.210       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.repository.KubeApiClientFabric  LINE:1522
				MESSAGE:exec returns error yantest/pg-yan-dev-172-16-8-104, cmd [sh, -c, psql -U postgres -d postgres -c "create user yhj with password 'Yanhj123' SUPERUSER"]
java.util.concurrent.ExecutionException: cn.newdt.cloud.common.ExecCommandException: ERROR:  role "yhj" already exists

	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.repository.KubeApiClientFabric.execCmd(KubeApiClientFabric.java:1516)
	at cn.newdt.cloud.repository.KubeApiClientFabric.execCmd(KubeApiClientFabric.java:1506)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at cn.newdt.cloud.repository.KubeClientManager$KubeClientInterceptor.invoke(KubeClientManager.java:98)
	at com.sun.proxy.$Proxy237.execCmd(Unknown Source)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.createRootUser(PostgreSqlInstallWatch.java:69)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.doStopWatchResource(PostgreSqlInstallWatch.java:42)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.stopWatchResource(PostgreSqlWatch.java:99)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.postProcess(PostgreSqlWatch.java:45)
	at cn.newdt.cloud.job.KubeJob$1.doInTransactionWithoutResult(KubeJob.java:95)
	at org.springframework.transaction.support.TransactionCallbackWithoutResult.doInTransaction(TransactionCallbackWithoutResult.java:36)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:91)
	at cn.newdt.cloud.job.AbstractJob.execute(AbstractJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: cn.newdt.cloud.common.ExecCommandException: ERROR:  role "yhj" already exists

	at cn.newdt.cloud.config.KubeExecSimpleListener.onClose(KubeExecSimpleListener.java:64)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.onClosed(ExecWebSocketListener.java:267)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.close(ExecWebSocketListener.java:122)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.onClosing(ExecWebSocketListener.java:251)
	at okhttp3.internal.ws.RealWebSocket.onReadClose(RealWebSocket.kt:378)
	at okhttp3.internal.ws.WebSocketReader.readControlFrame(WebSocketReader.kt:220)
	at okhttp3.internal.ws.WebSocketReader.processNextFrame(WebSocketReader.kt:104)
	at okhttp3.internal.ws.RealWebSocket.loopReader(RealWebSocket.kt:293)
	at okhttp3.internal.ws.RealWebSocket$connect$1.onResponse(RealWebSocket.kt:195)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:00:01.211       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch  LINE:71 
				MESSAGE:创建用户失败！

2025-07-25 18:00:01.259       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.job.AbstractJob  LINE:44 
				MESSAGE:jobKey:KubeJob.KubeJob;triggerKey:yan-dev.Create_8245
java.lang.RuntimeException: kubeJob exception
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:123)
	at cn.newdt.cloud.job.AbstractJob.execute(AbstractJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: cn.newdt.commons.exception.CustomException: 创建用户失败！
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.createRootUser(PostgreSqlInstallWatch.java:72)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.doStopWatchResource(PostgreSqlInstallWatch.java:42)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.stopWatchResource(PostgreSqlWatch.java:99)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.postProcess(PostgreSqlWatch.java:45)
	at cn.newdt.cloud.job.KubeJob$1.doInTransactionWithoutResult(KubeJob.java:95)
	at org.springframework.transaction.support.TransactionCallbackWithoutResult.doInTransaction(TransactionCallbackWithoutResult.java:36)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:91)
	... 3 common frames omitted

2025-07-25 18:00:31.856       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.repository.KubeApiClientFabric  LINE:1522
				MESSAGE:exec returns error yantest/pg-yan-dev-172-16-8-104, cmd [sh, -c, psql -U postgres -d postgres -c "create user yhj with password 'Yanhj123' SUPERUSER"]
java.util.concurrent.ExecutionException: cn.newdt.cloud.common.ExecCommandException: ERROR:  role "yhj" already exists

	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.repository.KubeApiClientFabric.execCmd(KubeApiClientFabric.java:1516)
	at cn.newdt.cloud.repository.KubeApiClientFabric.execCmd(KubeApiClientFabric.java:1506)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at cn.newdt.cloud.repository.KubeClientManager$KubeClientInterceptor.invoke(KubeClientManager.java:98)
	at com.sun.proxy.$Proxy237.execCmd(Unknown Source)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.createRootUser(PostgreSqlInstallWatch.java:69)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.doStopWatchResource(PostgreSqlInstallWatch.java:42)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.stopWatchResource(PostgreSqlWatch.java:99)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.postProcess(PostgreSqlWatch.java:45)
	at cn.newdt.cloud.job.KubeJob$1.doInTransactionWithoutResult(KubeJob.java:95)
	at org.springframework.transaction.support.TransactionCallbackWithoutResult.doInTransaction(TransactionCallbackWithoutResult.java:36)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:91)
	at cn.newdt.cloud.job.AbstractJob.execute(AbstractJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: cn.newdt.cloud.common.ExecCommandException: ERROR:  role "yhj" already exists

	at cn.newdt.cloud.config.KubeExecSimpleListener.onClose(KubeExecSimpleListener.java:64)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.onClosed(ExecWebSocketListener.java:267)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.close(ExecWebSocketListener.java:122)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.onClosing(ExecWebSocketListener.java:251)
	at okhttp3.internal.ws.RealWebSocket.onReadClose(RealWebSocket.kt:378)
	at okhttp3.internal.ws.WebSocketReader.readControlFrame(WebSocketReader.kt:220)
	at okhttp3.internal.ws.WebSocketReader.processNextFrame(WebSocketReader.kt:104)
	at okhttp3.internal.ws.RealWebSocket.loopReader(RealWebSocket.kt:293)
	at okhttp3.internal.ws.RealWebSocket$connect$1.onResponse(RealWebSocket.kt:195)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:00:31.860       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch  LINE:71 
				MESSAGE:创建用户失败！

2025-07-25 18:00:31.895       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.job.AbstractJob  LINE:44 
				MESSAGE:jobKey:KubeJob.KubeJob;triggerKey:yan-dev.Create_8245
java.lang.RuntimeException: kubeJob exception
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:123)
	at cn.newdt.cloud.job.AbstractJob.execute(AbstractJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: cn.newdt.commons.exception.CustomException: 创建用户失败！
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.createRootUser(PostgreSqlInstallWatch.java:72)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.doStopWatchResource(PostgreSqlInstallWatch.java:42)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.stopWatchResource(PostgreSqlWatch.java:99)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.postProcess(PostgreSqlWatch.java:45)
	at cn.newdt.cloud.job.KubeJob$1.doInTransactionWithoutResult(KubeJob.java:95)
	at org.springframework.transaction.support.TransactionCallbackWithoutResult.doInTransaction(TransactionCallbackWithoutResult.java:36)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:91)
	... 3 common frames omitted

2025-07-25 18:01:00.842       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.repository.KubeApiClientFabric  LINE:1522
				MESSAGE:exec returns error yantest/pg-yan-dev-172-16-8-104, cmd [sh, -c, psql -U postgres -d postgres -c "create user yhj with password 'Yanhj123' SUPERUSER"]
java.util.concurrent.ExecutionException: cn.newdt.cloud.common.ExecCommandException: ERROR:  role "yhj" already exists

	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.repository.KubeApiClientFabric.execCmd(KubeApiClientFabric.java:1516)
	at cn.newdt.cloud.repository.KubeApiClientFabric.execCmd(KubeApiClientFabric.java:1506)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at cn.newdt.cloud.repository.KubeClientManager$KubeClientInterceptor.invoke(KubeClientManager.java:98)
	at com.sun.proxy.$Proxy237.execCmd(Unknown Source)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.createRootUser(PostgreSqlInstallWatch.java:69)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.doStopWatchResource(PostgreSqlInstallWatch.java:42)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.stopWatchResource(PostgreSqlWatch.java:99)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.postProcess(PostgreSqlWatch.java:45)
	at cn.newdt.cloud.job.KubeJob$1.doInTransactionWithoutResult(KubeJob.java:95)
	at org.springframework.transaction.support.TransactionCallbackWithoutResult.doInTransaction(TransactionCallbackWithoutResult.java:36)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:91)
	at cn.newdt.cloud.job.AbstractJob.execute(AbstractJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: cn.newdt.cloud.common.ExecCommandException: ERROR:  role "yhj" already exists

	at cn.newdt.cloud.config.KubeExecSimpleListener.onClose(KubeExecSimpleListener.java:64)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.onClosed(ExecWebSocketListener.java:267)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.close(ExecWebSocketListener.java:122)
	at io.fabric8.kubernetes.client.dsl.internal.ExecWebSocketListener.onClosing(ExecWebSocketListener.java:251)
	at okhttp3.internal.ws.RealWebSocket.onReadClose(RealWebSocket.kt:378)
	at okhttp3.internal.ws.WebSocketReader.readControlFrame(WebSocketReader.kt:220)
	at okhttp3.internal.ws.WebSocketReader.processNextFrame(WebSocketReader.kt:104)
	at okhttp3.internal.ws.RealWebSocket.loopReader(RealWebSocket.kt:293)
	at okhttp3.internal.ws.RealWebSocket$connect$1.onResponse(RealWebSocket.kt:195)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:01:00.843       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch  LINE:71 
				MESSAGE:创建用户失败！

2025-07-25 18:01:00.860       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.job.AbstractJob  LINE:44 
				MESSAGE:jobKey:KubeJob.KubeJob;triggerKey:yan-dev.Create_8245
java.lang.RuntimeException: kubeJob exception
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:123)
	at cn.newdt.cloud.job.AbstractJob.execute(AbstractJob.java:38)
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202)
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573)
Caused by: cn.newdt.commons.exception.CustomException: 创建用户失败！
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.createRootUser(PostgreSqlInstallWatch.java:72)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlInstallWatch.doStopWatchResource(PostgreSqlInstallWatch.java:42)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.stopWatchResource(PostgreSqlWatch.java:99)
	at cn.newdt.cloud.service.sched.impl.pg.PostgreSqlWatch.postProcess(PostgreSqlWatch.java:45)
	at cn.newdt.cloud.job.KubeJob$1.doInTransactionWithoutResult(KubeJob.java:95)
	at org.springframework.transaction.support.TransactionCallbackWithoutResult.doInTransaction(TransactionCallbackWithoutResult.java:36)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at cn.newdt.cloud.job.KubeJob.safeExecute(KubeJob.java:91)
	... 3 common frames omitted

2025-07-25 18:15:15.768       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.utils.BackupStorageUtils  LINE:116
				MESSAGE:[downloadBackupFile] 从S3获取文件流失败
io.minio.errors.ErrorResponseException: The specified key does not exist.
	at io.minio.S3Base$1.onResponse(S3Base.java:789)
	at io.minio.S3Base$1.onResponse(S3Base.java:625)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:16:19.640       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.utils.BackupStorageUtils  LINE:116
				MESSAGE:[downloadBackupFile] 从S3获取文件流失败
io.minio.errors.ErrorResponseException: The specified key does not exist.
	at io.minio.S3Base$1.onResponse(S3Base.java:789)
	at io.minio.S3Base$1.onResponse(S3Base.java:625)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:17:03.848       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.utils.BackupStorageUtils  LINE:116
				MESSAGE:[downloadBackupFile] 从S3获取文件流失败
io.minio.errors.ErrorResponseException: The specified key does not exist.
	at io.minio.S3Base$1.onResponse(S3Base.java:789)
	at io.minio.S3Base$1.onResponse(S3Base.java:625)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:18:29.049       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.utils.BackupStorageUtils  LINE:116
				MESSAGE:[downloadBackupFile] 从S3获取文件流失败
io.minio.errors.ErrorResponseException: The specified key does not exist.
	at io.minio.S3Base$1.onResponse(S3Base.java:789)
	at io.minio.S3Base$1.onResponse(S3Base.java:625)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:19:07.970       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.utils.BackupStorageUtils  LINE:116
				MESSAGE:[downloadBackupFile] 从S3获取文件流失败
io.minio.errors.ErrorResponseException: The specified key does not exist.
	at io.minio.S3Base$1.onResponse(S3Base.java:789)
	at io.minio.S3Base$1.onResponse(S3Base.java:625)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:22:02.060       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:22:02.063       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:22:02.063       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:22:02.068       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:22:03.181       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:03.182       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:03.183       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:03.184       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:03.554       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:22:03.562       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:22:03.575       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:22:03.582       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:22:04.796       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:04.797       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:04.798       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:04.799       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:07.691       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:22:07.695       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:22:07.834       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:07.835       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:20.134       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:22:20.136       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:22:20.173       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:20.174       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:21.981       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:22:21.983       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:22:22.040       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:22.044       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:28.109       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:22:28.114       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:22:29.223       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:29.224       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:49.526       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:22:49.531       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:22:49.964       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:49.965       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:51.984       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:22:51.992       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:22:52.823       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:22:52.825       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:04.140       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:23:04.141       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:23:04.191       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:04.193       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:04.296       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:23:04.300       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:23:04.344       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:04.344       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:07.401       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:23:07.404       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:23:08.290       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:08.291       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:50.951       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:23:50.953       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:23:50.997       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:50.998       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:51.087       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:23:51.092       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:23:51.138       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:51.139       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:52.999       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:23:53.000       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:23:53.044       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:53.045       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:53.226       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:23:53.228       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:23:53.272       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:53.272       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:54.246       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:23:54.249       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:23:54.289       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:54.290       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:56.920       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:23:56.920       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:23:56.973       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:56.974       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:57.401       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:23:57.406       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:23:57.445       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:57.446       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:59.151       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:23:59.156       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:23:59.184       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:23:59.184       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:24:00.295       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:24:00.313       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:24:00.495       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:24:00.498       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:24:01.390       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:24:01.398       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:24:01.462       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:24:01.464       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:24:13.135       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:24:13.139       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:24:13.187       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:24:13.188       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:24:17.511       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:24:17.516       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:24:17.558       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:24:17.558       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:24:22.835       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.utils.BackupStorageUtils  LINE:116
				MESSAGE:[downloadBackupFile] 从S3获取文件流失败
io.minio.errors.ErrorResponseException: The specified key does not exist.
	at io.minio.S3Base$1.onResponse(S3Base.java:789)
	at io.minio.S3Base$1.onResponse(S3Base.java:625)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:24:23.228       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:24:23.234       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:24:23.234       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:24:23.241       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:24:24.315       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:24:24.316       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:24:24.330       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:24:24.331       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:24:25.592       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:24:25.595       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:24:25.603       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:24:25.616       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:24:26.625       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:24:26.626       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:24:26.719       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:24:26.720       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:27:15.258       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:27:15.273       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:27:15.282       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:27:15.301       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:27:15.420       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:27:15.421       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:27:15.431       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:27:15.432       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:27:39.836       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.utils.BackupStorageUtils  LINE:116
				MESSAGE:[downloadBackupFile] 从S3获取文件流失败
io.minio.errors.ErrorResponseException: The specified key does not exist.
	at io.minio.S3Base$1.onResponse(S3Base.java:789)
	at io.minio.S3Base$1.onResponse(S3Base.java:625)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:28:29.828       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.utils.BackupStorageUtils  LINE:116
				MESSAGE:[downloadBackupFile] 从S3获取文件流失败
io.minio.errors.ErrorResponseException: The specified key does not exist.
	at io.minio.S3Base$1.onResponse(S3Base.java:789)
	at io.minio.S3Base$1.onResponse(S3Base.java:625)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:29:29.593       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.utils.BackupStorageUtils  LINE:116
				MESSAGE:[downloadBackupFile] 从S3获取文件流失败
io.minio.errors.ErrorResponseException: The specified key does not exist.
	at io.minio.S3Base$1.onResponse(S3Base.java:789)
	at io.minio.S3Base$1.onResponse(S3Base.java:625)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:31:18.734       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.utils.BackupStorageUtils  LINE:116
				MESSAGE:[downloadBackupFile] 从S3获取文件流失败
io.minio.errors.ErrorResponseException: The specified key does not exist.
	at io.minio.S3Base$1.onResponse(S3Base.java:789)
	at io.minio.S3Base$1.onResponse(S3Base.java:625)
	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:52:07.490       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.inspect.CommandRuleExecutor  LINE:70 
				MESSAGE:
java.util.concurrent.ExecutionException: io.fabric8.kubernetes.client.KubernetesClientException: Failure executing: POST at: https://192.168.12.9:16443/api/v1/namespaces/ndt-operator-system/pods. Message: namespaces "ndt-operator-system" not found. Received status: Status(apiVersion=v1, code=404, details=StatusDetails(causes=[], group=null, kind=namespaces, name=ndt-operator-system, retryAfterSeconds=null, uid=null, additionalProperties={}), kind=Status, message=namespaces "ndt-operator-system" not found, metadata=ListMeta(_continue=null, remainingItemCount=null, resourceVersion=null, selfLink=null, additionalProperties={}), reason=NotFound, status=Failure, additionalProperties={}).
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.execute(CommandRuleExecutor.java:68)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:364)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:353)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.fabric8.kubernetes.client.KubernetesClientException: Failure executing: POST at: https://192.168.12.9:16443/api/v1/namespaces/ndt-operator-system/pods. Message: namespaces "ndt-operator-system" not found. Received status: Status(apiVersion=v1, code=404, details=StatusDetails(causes=[], group=null, kind=namespaces, name=ndt-operator-system, retryAfterSeconds=null, uid=null, additionalProperties={}), kind=Status, message=namespaces "ndt-operator-system" not found, metadata=ListMeta(_continue=null, remainingItemCount=null, resourceVersion=null, selfLink=null, additionalProperties={}), reason=NotFound, status=Failure, additionalProperties={}).
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.requestFailure(OperationSupport.java:697)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.requestFailure(OperationSupport.java:676)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.assertResponseCode(OperationSupport.java:629)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleResponse(OperationSupport.java:566)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleResponse(OperationSupport.java:527)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleCreate(OperationSupport.java:315)
	at io.fabric8.kubernetes.client.dsl.base.BaseOperation.handleCreate(BaseOperation.java:651)
	at io.fabric8.kubernetes.client.dsl.base.BaseOperation.handleCreate(BaseOperation.java:91)
	at io.fabric8.kubernetes.client.dsl.base.CreateOnlyResourceOperation.create(CreateOnlyResourceOperation.java:61)
	at cn.newdt.cloud.repository.KubeApiClientFabric.createPob(KubeApiClientFabric.java:532)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at cn.newdt.cloud.repository.KubeClientManager$KubeClientInterceptor.invoke(KubeClientManager.java:98)
	at com.sun.proxy.$Proxy237.createPob(Unknown Source)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.executeOnNode(CommandRuleExecutor.java:108)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.lambda$execute$0(CommandRuleExecutor.java:68)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1596)
	at java.util.concurrent.ForkJoinTask.doExec$$$capture(ForkJoinTask.java:289)
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java)
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1056)
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1692)
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:175)

2025-07-25 18:52:07.490       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.inspect.CommandRuleExecutor  LINE:70 
				MESSAGE:
java.util.concurrent.ExecutionException: io.fabric8.kubernetes.client.KubernetesClientException: Failure executing: POST at: https://192.168.12.9:16443/api/v1/namespaces/ndt-operator-system/pods. Message: namespaces "ndt-operator-system" not found. Received status: Status(apiVersion=v1, code=404, details=StatusDetails(causes=[], group=null, kind=namespaces, name=ndt-operator-system, retryAfterSeconds=null, uid=null, additionalProperties={}), kind=Status, message=namespaces "ndt-operator-system" not found, metadata=ListMeta(_continue=null, remainingItemCount=null, resourceVersion=null, selfLink=null, additionalProperties={}), reason=NotFound, status=Failure, additionalProperties={}).
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.execute(CommandRuleExecutor.java:68)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:364)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:353)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.fabric8.kubernetes.client.KubernetesClientException: Failure executing: POST at: https://192.168.12.9:16443/api/v1/namespaces/ndt-operator-system/pods. Message: namespaces "ndt-operator-system" not found. Received status: Status(apiVersion=v1, code=404, details=StatusDetails(causes=[], group=null, kind=namespaces, name=ndt-operator-system, retryAfterSeconds=null, uid=null, additionalProperties={}), kind=Status, message=namespaces "ndt-operator-system" not found, metadata=ListMeta(_continue=null, remainingItemCount=null, resourceVersion=null, selfLink=null, additionalProperties={}), reason=NotFound, status=Failure, additionalProperties={}).
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.requestFailure(OperationSupport.java:697)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.requestFailure(OperationSupport.java:676)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.assertResponseCode(OperationSupport.java:629)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleResponse(OperationSupport.java:566)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleResponse(OperationSupport.java:527)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleCreate(OperationSupport.java:315)
	at io.fabric8.kubernetes.client.dsl.base.BaseOperation.handleCreate(BaseOperation.java:651)
	at io.fabric8.kubernetes.client.dsl.base.BaseOperation.handleCreate(BaseOperation.java:91)
	at io.fabric8.kubernetes.client.dsl.base.CreateOnlyResourceOperation.create(CreateOnlyResourceOperation.java:61)
	at cn.newdt.cloud.repository.KubeApiClientFabric.createPob(KubeApiClientFabric.java:532)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at cn.newdt.cloud.repository.KubeClientManager$KubeClientInterceptor.invoke(KubeClientManager.java:98)
	at com.sun.proxy.$Proxy237.createPob(Unknown Source)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.executeOnNode(CommandRuleExecutor.java:108)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.lambda$execute$0(CommandRuleExecutor.java:68)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1596)
	at java.util.concurrent.ForkJoinTask.doExec$$$capture(ForkJoinTask.java:289)
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java)
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1056)
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1692)
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:175)

2025-07-25 18:53:46.305       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.inspect.CommandRuleExecutor  LINE:70 
				MESSAGE:
java.util.concurrent.ExecutionException: io.fabric8.kubernetes.client.KubernetesClientException: Failure executing: POST at: https://192.168.12.9:16443/api/v1/namespaces/ndt-operator-system/pods. Message: namespaces "ndt-operator-system" not found. Received status: Status(apiVersion=v1, code=404, details=StatusDetails(causes=[], group=null, kind=namespaces, name=ndt-operator-system, retryAfterSeconds=null, uid=null, additionalProperties={}), kind=Status, message=namespaces "ndt-operator-system" not found, metadata=ListMeta(_continue=null, remainingItemCount=null, resourceVersion=null, selfLink=null, additionalProperties={}), reason=NotFound, status=Failure, additionalProperties={}).
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.execute(CommandRuleExecutor.java:68)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:364)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:353)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.fabric8.kubernetes.client.KubernetesClientException: Failure executing: POST at: https://192.168.12.9:16443/api/v1/namespaces/ndt-operator-system/pods. Message: namespaces "ndt-operator-system" not found. Received status: Status(apiVersion=v1, code=404, details=StatusDetails(causes=[], group=null, kind=namespaces, name=ndt-operator-system, retryAfterSeconds=null, uid=null, additionalProperties={}), kind=Status, message=namespaces "ndt-operator-system" not found, metadata=ListMeta(_continue=null, remainingItemCount=null, resourceVersion=null, selfLink=null, additionalProperties={}), reason=NotFound, status=Failure, additionalProperties={}).
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.requestFailure(OperationSupport.java:697)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.requestFailure(OperationSupport.java:676)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.assertResponseCode(OperationSupport.java:629)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleResponse(OperationSupport.java:566)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleResponse(OperationSupport.java:527)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleCreate(OperationSupport.java:315)
	at io.fabric8.kubernetes.client.dsl.base.BaseOperation.handleCreate(BaseOperation.java:651)
	at io.fabric8.kubernetes.client.dsl.base.BaseOperation.handleCreate(BaseOperation.java:91)
	at io.fabric8.kubernetes.client.dsl.base.CreateOnlyResourceOperation.create(CreateOnlyResourceOperation.java:61)
	at cn.newdt.cloud.repository.KubeApiClientFabric.createPob(KubeApiClientFabric.java:532)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at cn.newdt.cloud.repository.KubeClientManager$KubeClientInterceptor.invoke(KubeClientManager.java:98)
	at com.sun.proxy.$Proxy237.createPob(Unknown Source)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.executeOnNode(CommandRuleExecutor.java:108)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.lambda$execute$0(CommandRuleExecutor.java:68)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1596)
	at java.util.concurrent.ForkJoinTask.doExec$$$capture(ForkJoinTask.java:289)
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java)
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1056)
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1692)
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:175)

2025-07-25 18:53:46.374       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.inspect.CommandRuleExecutor  LINE:70 
				MESSAGE:
java.util.concurrent.ExecutionException: io.fabric8.kubernetes.client.KubernetesClientException: Failure executing: POST at: https://192.168.12.9:16443/api/v1/namespaces/ndt-operator-system/pods. Message: namespaces "ndt-operator-system" not found. Received status: Status(apiVersion=v1, code=404, details=StatusDetails(causes=[], group=null, kind=namespaces, name=ndt-operator-system, retryAfterSeconds=null, uid=null, additionalProperties={}), kind=Status, message=namespaces "ndt-operator-system" not found, metadata=ListMeta(_continue=null, remainingItemCount=null, resourceVersion=null, selfLink=null, additionalProperties={}), reason=NotFound, status=Failure, additionalProperties={}).
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.execute(CommandRuleExecutor.java:68)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:364)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:353)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.fabric8.kubernetes.client.KubernetesClientException: Failure executing: POST at: https://192.168.12.9:16443/api/v1/namespaces/ndt-operator-system/pods. Message: namespaces "ndt-operator-system" not found. Received status: Status(apiVersion=v1, code=404, details=StatusDetails(causes=[], group=null, kind=namespaces, name=ndt-operator-system, retryAfterSeconds=null, uid=null, additionalProperties={}), kind=Status, message=namespaces "ndt-operator-system" not found, metadata=ListMeta(_continue=null, remainingItemCount=null, resourceVersion=null, selfLink=null, additionalProperties={}), reason=NotFound, status=Failure, additionalProperties={}).
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.requestFailure(OperationSupport.java:697)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.requestFailure(OperationSupport.java:676)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.assertResponseCode(OperationSupport.java:629)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleResponse(OperationSupport.java:566)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleResponse(OperationSupport.java:527)
	at io.fabric8.kubernetes.client.dsl.base.OperationSupport.handleCreate(OperationSupport.java:315)
	at io.fabric8.kubernetes.client.dsl.base.BaseOperation.handleCreate(BaseOperation.java:651)
	at io.fabric8.kubernetes.client.dsl.base.BaseOperation.handleCreate(BaseOperation.java:91)
	at io.fabric8.kubernetes.client.dsl.base.CreateOnlyResourceOperation.create(CreateOnlyResourceOperation.java:61)
	at cn.newdt.cloud.repository.KubeApiClientFabric.createPob(KubeApiClientFabric.java:532)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at cn.newdt.cloud.repository.KubeClientManager$KubeClientInterceptor.invoke(KubeClientManager.java:98)
	at com.sun.proxy.$Proxy237.createPob(Unknown Source)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.executeOnNode(CommandRuleExecutor.java:108)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.lambda$execute$0(CommandRuleExecutor.java:68)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1604)
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1596)
	at java.util.concurrent.ForkJoinTask.doExec$$$capture(ForkJoinTask.java:289)
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java)
	at java.util.concurrent.ForkJoinPool$WorkQueue.runTask(ForkJoinPool.java:1056)
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1692)
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:175)

2025-07-25 18:54:16.369       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.inspect.CommandRuleExecutor  LINE:70 
				MESSAGE:
java.util.concurrent.TimeoutException: null
	at java.util.concurrent.CompletableFuture.timedGet(CompletableFuture.java:1784)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1928)
	at cn.newdt.cloud.service.inspect.CommandRuleExecutor.execute(CommandRuleExecutor.java:68)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:364)
	at cn.newdt.cloud.service.inspect.InspectService$ExecutorTask.call(InspectService.java:353)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

2025-07-25 18:59:24.172       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:59:24.176       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:59:24.987       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:59:24.988       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:59:59.593       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 18:59:59.593       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 18:59:59.649       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 18:59:59.650       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 19:00:15.490       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 19:00:15.490       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 19:00:16.472       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 19:00:16.474       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 19:08:33.939       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 19:08:33.950       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 19:08:35.023       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 19:08:35.025       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 19:16:53.137       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 19:16:53.137       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 19:16:53.140       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 19:16:53.139       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 19:16:53.215       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 19:16:53.215       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 19:16:53.216       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 19:16:53.217       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 19:17:00.484       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 19:17:00.487       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 19:17:01.620       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 19:17:01.622       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 19:17:20.097       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 19:17:20.102       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 19:17:20.966       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 19:17:20.967       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 19:17:27.321       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 19:17:27.325       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 19:17:28.142       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 19:17:28.145       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 19:23:53.760       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [29]

2025-07-25 19:23:53.760       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:79 
				MESSAGE:获取Alertmanager服务错误, [27]

2025-07-25 19:23:54.615       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)

2025-07-25 19:23:54.617       LEVEL:ERROR ThreadId:
				POSITION:cn.newdt.cloud.service.alert.AlertService  LINE:276
				MESSAGE:fetch silent alert error - CustomException(status=600, detailMessage=null)
