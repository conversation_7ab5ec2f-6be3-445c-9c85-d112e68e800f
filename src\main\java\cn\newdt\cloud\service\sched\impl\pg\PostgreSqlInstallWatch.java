package cn.newdt.cloud.service.sched.impl.pg;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.utils.BeanUtil;
import cn.newdt.cloud.vo.CloudAppVO;
import cn.newdt.commons.bean.UserInfo;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.UserUtil;
import com.alibaba.fastjson.JSONObject;
import com.shindata.postgre.v1.PostgreSql;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@Slf4j
public class PostgreSqlInstallWatch extends PostgreSqlWatch{

    @Override
    public void doStopWatchResource(CloudApp app, TriggerHis triggerHis, OpsResultDTO.Builder result, PostgreSql cr) {
        logInfo(app, ActionEnum.CREATE, "execute stopping processor");
        if (result.isSuccessful()) {
            appMultiAZService.enableAlert(app);
            serviceManageOperationWatcherHelper.doStopWatchSvc(app, triggerHis, result.isSuccessful(), null);

            Map<String, String> mergedJobDataMap = triggerHis.returnMergedJobDataMap();
            if (StringUtils.isEmpty(mergedJobDataMap.get("backupHisId"))) {
                String username = mergedJobDataMap.get("username");
                String password = mergedJobDataMap.get("password"); // todo 加解密
                if (StringUtils.isNoneBlank(username, password)) {
                    if (!dbUserService.findDbUserByName(app.getId(), username).isPresent()) {
                        result.msg("create user " + username);
                        dbUserService.createUser(app.getId(), username, "", CloudAppConstant.UserRole.ADMIN);
                        createRootUser(username, password, app);
                    }
                }

                //创建dmp监控用户
                Map<String, String> dmpMonitorUser = operationUtil.createDMPMonitorUser();
                createRootUser(dmpMonitorUser.get("username"), dmpMonitorUser.get("password"), app);
            }

            // 纳管到dmp
            if (autoManagement) {
                logInfo(app, ActionEnum.CREATE, "app will be managed into CMDB");
                result.msg("sync cloud app to DMP");
                result.msg(operationUtil.syncToDMP(app, triggerHis));
            }
        }

    }

    private void createRootUser(String username, String password, CloudApp app) {
        KubeClient client = kubeClientService.get(app.getKubeId());
        //获取主节点
        List<PodDTO> pods = client.listPod(app.getNamespace(), AppKind.PostgreSQL.labelOfPod(app));
        PodDTO primaryPod = pods.stream().filter(pod -> "primary".equalsIgnoreCase(pod.getPod().getMetadata().getLabels().get(CloudAppConstant.CustomLabels.ROLE))).findFirst()
                .orElseThrow(() -> new IllegalStateException("create user failed, non-exist "));
        try {
            String createUserSql = "psql -U postgres -d postgres -c \"create user " + username + " with password '" + password + "' SUPERUSER\""; // A database superuser bypasses all permission checks, except the right to log in
            client.execCmd(app.getNamespace(), primaryPod.getPodName(), AppKind.PostgreSQL.getContainerName(), 30, "sh", "-c", createUserSql);
        } catch (Exception e) {
            log.error("创建用户失败！");
            throw new CustomException(600, "创建用户失败！");
        }
    }

    void syncToDMP(CloudApp app, TriggerHis triggerHis, String username, String password) {
        // 转化为用户对象UserInfo
        String userInfoStr = triggerHis.getJobDataMap().get("userInfo");
        UserInfo userInfo = JSONObject.parseObject(userInfoStr, UserInfo.class);
        UserUtil.setAsyncUserInfo(userInfo);
        CloudAppVO vo = new CloudAppVO();
        BeanUtil.copyNonNullProperties(app, vo);
        vo.setUsername(username);
        vo.setPassword(password);
//        operationUtil.doActionToManage(vo);
    }

}
