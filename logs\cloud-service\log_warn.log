
2025-07-28 11:14:32.162       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-28 11:14:32.178       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-28 11:15:12.153       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-28 11:15:17.695       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 11:15:17.699       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 11:15:18.353       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-28 11:15:18.366       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-28 11:15:19.518       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 11:15:19.522       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 11:23:14.004       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .async-search, .kibana_7.12.0_001, .security-7, .apm-agent-configuration, .apm-custom-link, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 11:23:42.515       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .security-7, .async-search, .apm-agent-configuration, .apm-custom-link, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 11:27:25.148       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .tasks, .async-search, .apm-custom-link, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 11:27:34.835       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-custom-link, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .async-search, .kibana_7.12.0_001, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 11:28:10.133       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .async-search, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .security-7, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 11:28:13.276       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-custom-link, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .kibana_7.12.0_001, .async-search, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 11:29:20.714       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .security-7, .async-search, .kibana_7.12.0_001, .tasks, .kibana_task_manager_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 11:29:49.652       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .async-search, .apm-custom-link, .tasks, .security-7, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 11:30:43.482       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .security-7, .async-search, .tasks, .kibana_7.12.0_001, .apm-custom-link, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 13:49:59.184       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-28 13:49:59.201       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-28 13:51:10.269       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-28 13:51:15.611       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 13:51:15.611       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 13:51:16.670       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-28 13:51:16.680       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-28 13:51:18.511       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 13:51:18.511       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 13:52:03.644       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-28 13:52:03.644       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-28 13:52:03.648       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-28 13:52:03.650       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-28 13:52:34.784       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-28 13:52:34.799       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-28 13:53:21.103       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-28 13:53:26.189       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 13:53:26.189       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 13:53:26.713       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-28 13:53:26.729       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-28 13:53:27.799       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 13:53:27.799       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 13:54:57.502       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .tasks, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .async-search, .apm-custom-link, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:16:51.989       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.scheduling.quartz.LocalDataSourceJobStore  LINE:3411
				MESSAGE:This scheduler instance (LAPTOP-OUTBTI751753681978980) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior.

2025-07-28 14:18:53.797       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.scheduling.quartz.LocalDataSourceJobStore  LINE:3411
				MESSAGE:This scheduler instance (LAPTOP-OUTBTI751753681978980) is still active but was recovered by another instance in the cluster.  This may cause inconsistent behavior.

2025-07-28 14:30:27.159       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .async-search, .security-7, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:30:27.167       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-custom-link, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .tasks, .apm-agent-configuration, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:30:27.261       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 14:30:27.311       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 14:30:27.352       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 14:30:27.676       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 14:30:33.102       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .apm-custom-link, .kibana_7.12.0_001, .async-search, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:30:33.270       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-agent-configuration, .security-7, .async-search, .apm-custom-link, .tasks, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:32:26.859       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-28 14:32:26.859       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-28 14:32:26.861       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-28 14:32:26.862       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-28 14:32:53.627       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-28 14:32:53.643       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-28 14:33:38.992       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-28 14:33:46.475       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 14:33:46.490       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 14:33:47.950       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-28 14:33:47.988       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-28 14:33:50.550       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 14:33:50.560       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 14:33:55.852       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 14:33:56.477       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 14:33:56.868       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 14:33:57.689       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .async-search, .kibana_7.12.0_001, .apm-custom-link, .tasks, .kibana_task_manager_7.12.0_001, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:33:57.775       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .tasks, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .async-search, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:33:58.387       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 14:33:58.462       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 14:33:58.504       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .kibana_7.12.0_001, .security-7, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:33:58.504       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 14:33:58.541       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .security-7, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-agent-configuration, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:33:58.785       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 14:34:10.412       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-custom-link, .security-7, .tasks, .apm-agent-configuration, .kibana_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:34:10.411       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-agent-configuration, .security-7, .apm-custom-link, .tasks, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:34:16.724       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":27}

2025-07-28 14:34:16.761       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":27}

2025-07-28 14:34:16.775       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .apm-custom-link, .security-7, .tasks, .kibana_task_manager_7.12.0_001, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:34:16.785       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-custom-link, .security-7, .tasks, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:34:16.802       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":27}

2025-07-28 14:34:16.975       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":27}

2025-07-28 14:35:21.209       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .security-7, .apm-custom-link, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:35:21.209       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .security-7, .apm-custom-link, .async-search, .apm-agent-configuration, .tasks, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:40:47.673       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-28 14:40:47.674       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-28 14:40:47.675       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-28 14:40:47.680       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-28 14:41:16.649       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-28 14:41:16.658       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-28 14:41:49.998       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-28 14:41:54.312       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 14:41:54.316       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 14:41:55.093       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-28 14:41:55.110       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-28 14:41:56.064       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 14:41:56.070       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 14:44:41.855       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_task_manager_7.12.0_001, .async-search, .apm-agent-configuration, .tasks, .apm-custom-link, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:45:11.101       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .async-search, .tasks, .security-7, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:54:28.646       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .async-search, .kibana_task_manager_7.12.0_001, .apm-custom-link, .kibana_7.12.0_001, .security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:54:45.715       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-custom-link, .kibana_task_manager_7.12.0_001, .security-7, .apm-agent-configuration, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:56:08.052       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-custom-link, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .security-7, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:56:23.393       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_task_manager_7.12.0_001, .tasks, .kibana_7.12.0_001, .security-7, .async-search, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:56:58.744       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .kibana_task_manager_7.12.0_001, .security-7, .kibana_7.12.0_001, .apm-custom-link, .tasks, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 14:59:05.985       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-agent-configuration, .kibana_7.12.0_001, .apm-custom-link, .security-7, .kibana_task_manager_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:02:21.642       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-agent-configuration, .tasks, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .async-search, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:02:26.581       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .async-search, .kibana_task_manager_7.12.0_001, .apm-custom-link, .security-7, .apm-agent-configuration, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:02:43.439       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .apm-custom-link, .security-7, .async-search, .kibana_task_manager_7.12.0_001, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:02:43.450       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-agent-configuration, .apm-custom-link, .kibana_7.12.0_001, .async-search, .tasks, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:02:43.522       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:02:43.559       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:02:43.606       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:02:43.789       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:03:10.625       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:03:10.667       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:03:10.672       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-custom-link, .security-7, .tasks, .apm-agent-configuration, .async-search, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:03:10.689       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .async-search, .kibana_task_manager_7.12.0_001, .security-7, .kibana_7.12.0_001, .apm-custom-link, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:03:10.699       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:03:10.820       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:04:05.763       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .kibana_7.12.0_001, .tasks, .apm-agent-configuration, .async-search, .security-7, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:04:05.775       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .security-7, .apm-agent-configuration, .kibana_7.12.0_001, .apm-custom-link, .tasks, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:04:34.289       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:04:34.344       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .apm-agent-configuration, .async-search, .security-7, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:04:34.369       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:04:34.369       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-agent-configuration, .security-7, .apm-custom-link, .kibana_task_manager_7.12.0_001, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:04:34.413       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:04:34.519       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:18:48.789       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:18:48.825       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:18:48.826       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-agent-configuration, .apm-custom-link, .kibana_7.12.0_001, .tasks, .security-7, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:18:48.831       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .security-7, .tasks, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .async-search, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:18:48.864       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:18:48.986       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:19:00.906       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_task_manager_7.12.0_001, .tasks, .async-search, .apm-custom-link, .security-7, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:19:00.937       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .async-search, .apm-agent-configuration, .security-7, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:20:01.943       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:20:02.024       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:20:02.152       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:20:02.204       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .tasks, .kibana_7.12.0_001, .security-7, .apm-agent-configuration, .apm-custom-link, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:20:02.797       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-agent-configuration, .apm-custom-link, .async-search, .tasks, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:20:02.934       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:21:24.532       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_task_manager_7.12.0_001, .tasks, .apm-custom-link, .apm-agent-configuration, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:21:24.537       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .kibana_task_manager_7.12.0_001, .apm-custom-link, .security-7, .apm-agent-configuration, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:22:24.156       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":27}

2025-07-28 15:22:24.204       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":27}

2025-07-28 15:22:24.209       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .security-7, .apm-agent-configuration, .apm-custom-link, .kibana_task_manager_7.12.0_001, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:22:24.210       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .apm-custom-link, .security-7, .kibana_task_manager_7.12.0_001, .async-search, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:22:24.260       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":27}

2025-07-28 15:22:24.404       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":27}

2025-07-28 15:22:26.623       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .async-search, .security-7, .apm-custom-link, .apm-agent-configuration, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:22:26.643       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .security-7, .async-search, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:28:31.942       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-28 15:28:31.943       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-28 15:28:31.943       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-28 15:28:31.947       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-28 15:30:21.504       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-28 15:30:21.513       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-28 15:30:45.009       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext  LINE:599
				MESSAGE:Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'inspectFunctionRuleDef': Unsatisfied dependency expressed through field 'alertApi'; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'cn.newdt.cloud.service.alertmanager.AlertApi' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}

2025-07-28 15:30:45.356       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-28 15:30:45.356       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-28 15:30:45.359       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-28 15:31:31.664       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-28 15:31:31.672       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-28 15:32:08.916       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-28 15:32:13.555       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 15:32:13.558       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 15:32:14.669       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-28 15:32:14.691       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-28 15:32:15.742       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 15:32:15.748       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 15:34:48.538       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .tasks, .security-7, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:34:51.215       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .async-search, .apm-custom-link, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:35:53.061       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .async-search, .security-7, .apm-custom-link, .tasks, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:35:56.073       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .tasks, .security-7, .async-search, .apm-custom-link, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:36:34.807       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-agent-configuration, .kibana_7.12.0_001, .tasks, .security-7, .async-search, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:36:34.818       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .kibana_7.12.0_001, .apm-agent-configuration, .tasks, .security-7, .async-search, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:36:34.832       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:36:34.879       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:36:34.927       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:36:35.063       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:49:19.733       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:49:19.781       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:49:19.787       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-custom-link, .tasks, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .security-7, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:49:19.786       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .async-search, .tasks, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-custom-link, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:49:19.812       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:49:19.988       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:49:26.691       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-custom-link, .security-7, .kibana_task_manager_7.12.0_001, .async-search, .kibana_7.12.0_001, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:49:26.876       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-custom-link, .security-7, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:49:38.729       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .apm-custom-link, .tasks, .async-search, .security-7, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:49:45.738       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .security-7, .async-search, .apm-custom-link, .apm-agent-configuration, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:51:01.005       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-custom-link, .tasks, .async-search, .kibana_7.12.0_001, .security-7, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:51:14.305       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_7.12.0_001, .tasks, .apm-custom-link, .async-search, .security-7, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:52:13.607       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-custom-link, .apm-agent-configuration, .async-search, .tasks, .security-7, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:53:30.528       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .tasks, .apm-custom-link, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .async-search, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:53:57.726       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:53:57.761       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:53:57.777       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_task_manager_7.12.0_001, .apm-custom-link, .tasks, .apm-agent-configuration, .kibana_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:53:57.793       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .security-7, .tasks, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-custom-link, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:53:57.811       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:53:57.939       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 15:54:02.086       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .tasks, .apm-custom-link, .security-7, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:54:02.091       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-custom-link, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .async-search, .tasks, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:55:44.104       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-agent-configuration, .async-search, .kibana_task_manager_7.12.0_001, .apm-custom-link, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:55:49.870       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .async-search, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .security-7, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 15:57:12.620       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.commons.exception.ExceptionAdvice  LINE:175
				MESSAGE:异常ID:92c1b4e7-5ccc-48b3-9e34-a518dfc79960, 请求地址:http://192.168.2.217:8300/cloudnormal/alert/rules
java.lang.NullPointerException: null
	at cn.newdt.cloud.service.alert.AlertConfigProcessor.apply(AlertConfigProcessor.java:41)
	at cn.newdt.cloud.service.alert.AlertConfigProcessor$$FastClassBySpringCGLIB$$bd2d8af9.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.service.alert.AlertConfigProcessor$$EnhancerBySpringCGLIB$$9cf7a966.apply(<generated>)
	at cn.newdt.cloud.web.AlertConfigController.postRules(AlertConfigController.java:125)
	at cn.newdt.cloud.web.AlertConfigController$$FastClassBySpringCGLIB$$72193146.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at cn.newdt.cloud.web.AlertConfigController$$EnhancerBySpringCGLIB$$3cc7b323.postRules(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at cn.newdt.cloud.config.CloudRequestContextFilter.doFilter(CloudRequestContextFilter.java:62)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.www.BasicAuthenticationFilter.doFilterInternal(BasicAuthenticationFilter.java:196)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)

2025-07-28 16:07:23.758       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_7.12.0_001, .tasks, .apm-agent-configuration, .apm-custom-link, .kibana_task_manager_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:07:29.995       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .tasks, .kibana_7.12.0_001, .apm-custom-link, .async-search, .security-7, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:09:32.448       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:09:32.500       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:09:32.545       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .kibana_7.12.0_001, .security-7, .async-search, .kibana_task_manager_7.12.0_001, .tasks, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:09:32.545       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-agent-configuration, .tasks, .kibana_7.12.0_001, .security-7, .apm-custom-link, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:09:32.559       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:09:32.699       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:09:34.434       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-custom-link, .kibana_7.12.0_001, .apm-agent-configuration, .security-7, .async-search, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:09:34.434       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-agent-configuration, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-custom-link, .async-search, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:09:40.213       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-custom-link, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .tasks, .security-7, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:09:42.695       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .tasks, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .async-search, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:10:16.569       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .security-7, .kibana_7.12.0_001, .apm-custom-link, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:10:16.821       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .security-7, .async-search, .kibana_7.12.0_001, .tasks, .kibana_task_manager_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:11:43.235       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .async-search, .tasks, .kibana_7.12.0_001, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:11:45.812       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .tasks, .kibana_7.12.0_001, .async-search, .apm-custom-link, .security-7, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:11:47.457       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .async-search, .security-7, .apm-agent-configuration, .apm-custom-link, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:12:08.966       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_7.12.0_001, .apm-custom-link, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .tasks, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:12:15.065       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .kibana_task_manager_7.12.0_001, .security-7, .async-search, .apm-agent-configuration, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:12:25.314       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .security-7, .async-search, .apm-custom-link, .apm-agent-configuration, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:12:27.042       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-custom-link, .tasks, .security-7, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:12:28.662       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-agent-configuration, .kibana_7.12.0_001, .async-search, .security-7, .apm-custom-link, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:14:26.378       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-custom-link, .apm-agent-configuration, .security-7, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:16:28.763       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:16:28.824       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:16:28.861       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .async-search, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .tasks, .security-7, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:16:28.870       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:16:28.870       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .tasks, .kibana_7.12.0_001, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .async-search, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:16:29.162       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:19:43.453       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-agent-configuration, .tasks, .async-search, .apm-custom-link, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:20:45.586       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .tasks, .async-search, .kibana_7.12.0_001, .apm-custom-link, .kibana_task_manager_7.12.0_001, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:21:48.715       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-custom-link, .apm-agent-configuration, .tasks, .kibana_7.12.0_001, .async-search, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:22:33.865       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .security-7, .apm-agent-configuration, .apm-custom-link, .async-search, .tasks, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:23:07.706       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:23:07.744       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:23:07.747       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-agent-configuration, .async-search, .kibana_task_manager_7.12.0_001, .apm-custom-link, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:23:07.749       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .security-7, .async-search, .kibana_7.12.0_001, .apm-custom-link, .apm-agent-configuration, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:23:07.777       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:23:07.877       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:26:27.019       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:26:27.061       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:26:27.074       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-custom-link, .security-7, .async-search, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:26:27.074       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .kibana_7.12.0_001, .apm-custom-link, .async-search, .kibana_task_manager_7.12.0_001, .security-7, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:26:27.110       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:26:27.311       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:26:31.336       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .async-search, .tasks, .apm-agent-configuration, .kibana_7.12.0_001, .security-7, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:26:31.365       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .apm-custom-link, .kibana_task_manager_7.12.0_001, .security-7, .async-search, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:27:09.500       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:27:09.530       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:27:09.534       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .tasks, .kibana_task_manager_7.12.0_001, .security-7, .apm-agent-configuration, .apm-custom-link, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:27:09.538       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .async-search, .kibana_7.12.0_001, .tasks, .apm-custom-link, .kibana_task_manager_7.12.0_001, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:27:09.561       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:27:09.680       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:27:10.794       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .kibana_7.12.0_001, .security-7, .async-search, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:27:10.802       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .kibana_task_manager_7.12.0_001, .apm-custom-link, .security-7, .apm-agent-configuration, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:27:33.673       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .tasks, .apm-agent-configuration, .security-7, .apm-custom-link, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:29:22.548       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-custom-link, .tasks, .security-7, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:29:28.779       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .async-search, .apm-custom-link, .kibana_task_manager_7.12.0_001, .security-7, .apm-agent-configuration, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:36:25.260       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .security-7, .tasks, .apm-custom-link, .apm-agent-configuration, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:36:25.261       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .security-7, .apm-custom-link, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:42:19.596       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:42:19.646       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:42:19.667       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .async-search, .apm-custom-link, .apm-agent-configuration, .tasks, .kibana_7.12.0_001, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:42:19.679       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .async-search, .security-7, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:42:19.683       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:42:19.800       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:43:09.232       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .tasks, .async-search, .apm-custom-link, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:43:09.244       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .async-search, .apm-agent-configuration, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .security-7, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:43:09.264       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .apm-custom-link, .tasks, .kibana_task_manager_7.12.0_001, .async-search, .security-7, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:43:10.152       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:43:10.184       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-agent-configuration, .async-search, .kibana_7.12.0_001, .apm-custom-link, .kibana_task_manager_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:43:10.184       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .security-7, .tasks, .apm-custom-link, .apm-agent-configuration, .kibana_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:43:10.198       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:43:10.232       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:43:10.350       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:44:05.251       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .tasks, .security-7, .kibana_7.12.0_001, .apm-agent-configuration, .async-search, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:44:05.251       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-agent-configuration, .security-7, .kibana_task_manager_7.12.0_001, .apm-custom-link, .kibana_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:44:05.251       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_task_manager_7.12.0_001, .async-search, .apm-custom-link, .apm-agent-configuration, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:44:06.232       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:44:06.278       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .apm-agent-configuration, .tasks, .async-search, .security-7, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:44:06.278       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_7.12.0_001, .apm-custom-link, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .tasks, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:44:06.289       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:44:06.321       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:44:06.407       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:45:08.676       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .async-search, .kibana_task_manager_7.12.0_001, .security-7, .tasks, .apm-agent-configuration, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:45:08.676       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .tasks, .security-7, .kibana_7.12.0_001, .async-search, .apm-custom-link, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:45:08.693       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-custom-link, .security-7, .apm-agent-configuration, .tasks, .kibana_task_manager_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:45:09.675       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:45:09.706       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:45:09.713       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .apm-agent-configuration, .apm-custom-link, .async-search, .security-7, .kibana_task_manager_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:45:09.713       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .kibana_7.12.0_001, .apm-agent-configuration, .security-7, .apm-custom-link, .kibana_task_manager_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:45:09.739       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:45:09.875       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:48:00.774       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .tasks, .async-search, .apm-custom-link, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:48:00.774       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-custom-link, .async-search, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:48:00.791       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .security-7, .apm-custom-link, .apm-agent-configuration, .kibana_7.12.0_001, .tasks, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:48:01.748       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:48:01.782       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-custom-link, .async-search, .apm-agent-configuration, .security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:48:01.782       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .security-7, .tasks, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:48:01.785       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:48:01.820       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:48:01.938       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:48:05.166       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-custom-link, .kibana_7.12.0_001, .async-search, .tasks, .apm-agent-configuration, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:48:05.173       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .async-search, .security-7, .apm-agent-configuration, .apm-custom-link, .tasks, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:48:43.550       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .security-7, .kibana_7.12.0_001, .apm-custom-link, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:49:09.453       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .async-search, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:49:43.111       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-agent-configuration, .security-7, .apm-custom-link, .kibana_task_manager_7.12.0_001, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:49:43.113       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_task_manager_7.12.0_001, .security-7, .apm-custom-link, .kibana_7.12.0_001, .tasks, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:49:43.113       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .async-search, .kibana_7.12.0_001, .apm-custom-link, .security-7, .apm-agent-configuration, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:49:44.061       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:49:44.121       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .security-7, .tasks, .kibana_task_manager_7.12.0_001, .apm-custom-link, .apm-agent-configuration, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:49:44.121       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:49:44.121       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .security-7, .apm-custom-link, .async-search, .kibana_task_manager_7.12.0_001, .tasks, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:49:44.173       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:49:44.284       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:50:32.458       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .tasks, .apm-custom-link, .kibana_7.12.0_001, .async-search, .kibana_task_manager_7.12.0_001, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:50:32.469       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .async-search, .tasks, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .security-7, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:50:32.473       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .security-7, .apm-agent-configuration, .kibana_7.12.0_001, .apm-custom-link, .tasks, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:50:33.437       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:50:33.472       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:50:33.478       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .kibana_task_manager_7.12.0_001, .tasks, .kibana_7.12.0_001, .security-7, .async-search, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:50:33.478       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .security-7, .async-search, .kibana_7.12.0_001, .apm-custom-link, .kibana_task_manager_7.12.0_001, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:50:33.502       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:50:33.649       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:50:37.257       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .async-search, .security-7, .apm-agent-configuration, .kibana_7.12.0_001, .tasks, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:50:37.272       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .security-7, .tasks, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:51:40.403       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .tasks, .kibana_7.12.0_001, .security-7, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:51:40.403       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .async-search, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .apm-custom-link, .security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:51:40.423       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .security-7, .async-search, .apm-custom-link, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:51:41.378       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:51:41.427       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:51:41.427       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .async-search, .kibana_7.12.0_001, .tasks, .security-7, .apm-custom-link, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:51:41.448       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .security-7, .tasks, .async-search, .kibana_7.12.0_001, .apm-custom-link, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:51:41.491       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:51:41.580       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:54:36.445       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-custom-link, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .tasks, .kibana_7.12.0_001, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:54:36.445       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .async-search, .tasks, .kibana_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:57:25.640       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-custom-link, .async-search, .apm-agent-configuration, .security-7, .tasks, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:57:25.652       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .security-7, .kibana_task_manager_7.12.0_001, .async-search, .apm-agent-configuration, .apm-custom-link, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:57:25.809       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-agent-configuration, .apm-custom-link, .security-7, .tasks, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:57:26.543       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:57:26.567       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .tasks, .async-search, .security-7, .apm-agent-configuration, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:57:26.567       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .security-7, .tasks, .kibana_7.12.0_001, .apm-agent-configuration, .async-search, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:57:26.571       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:57:26.604       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:57:26.702       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:59:05.729       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .security-7, .kibana_task_manager_7.12.0_001, .apm-custom-link, .tasks, .kibana_7.12.0_001, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:59:05.734       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-agent-configuration, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .security-7, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:59:05.745       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .security-7, .kibana_7.12.0_001, .apm-agent-configuration, .tasks, .async-search, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:59:06.663       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:59:06.691       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .security-7, .tasks, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .apm-custom-link, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:59:06.695       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_7.12.0_001, .tasks, .apm-custom-link, .security-7, .kibana_task_manager_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 16:59:06.710       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:59:06.760       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 16:59:06.858       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:00:07.482       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-agent-configuration, .async-search, .apm-custom-link, .kibana_7.12.0_001, .security-7, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:08:02.147       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-custom-link, .kibana_7.12.0_001, .security-7, .async-search, .apm-agent-configuration, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:08:02.147       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .tasks, .kibana_task_manager_7.12.0_001, .security-7, .apm-agent-configuration, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:08:02.147       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .tasks, .kibana_7.12.0_001, .apm-custom-link, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:08:03.092       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:08:03.137       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:08:03.146       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .tasks, .security-7, .apm-custom-link, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:08:03.146       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .kibana_7.12.0_001, .security-7, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .tasks, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:08:03.178       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:08:03.283       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:09:30.787       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .async-search, .tasks, .apm-agent-configuration, .apm-custom-link, .kibana_task_manager_7.12.0_001, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:09:30.800       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_7.12.0_001, .async-search, .apm-agent-configuration, .tasks, .kibana_task_manager_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:09:30.807       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_7.12.0_001, .tasks, .apm-custom-link, .kibana_task_manager_7.12.0_001, .security-7, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:09:31.789       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:09:31.823       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .security-7, .apm-agent-configuration, .async-search, .tasks, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:09:31.830       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:09:31.836       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .apm-custom-link, .tasks, .kibana_task_manager_7.12.0_001, .security-7, .kibana_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:09:31.858       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:09:31.950       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:10:01.593       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .async-search, .tasks, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-custom-link, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:10:01.594       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .tasks, .kibana_7.12.0_001, .async-search, .kibana_task_manager_7.12.0_001, .security-7, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:10:01.611       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .security-7, .apm-agent-configuration, .tasks, .apm-custom-link, .async-search, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:10:02.518       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:10:02.569       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:10:02.578       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .apm-custom-link, .tasks, .async-search, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:10:02.578       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .async-search, .security-7, .apm-custom-link, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:10:02.600       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:10:02.714       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:12:31.730       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-custom-link, .security-7, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .async-search, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:16:04.708       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .apm-agent-configuration, .kibana_7.12.0_001, .tasks, .async-search, .security-7, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:16:04.718       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .async-search, .apm-custom-link, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .kibana_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:16:04.735       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .security-7, .kibana_task_manager_7.12.0_001, .apm-custom-link, .kibana_7.12.0_001, .async-search, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:16:06.320       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:16:06.394       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:16:06.394       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .tasks, .kibana_7.12.0_001, .async-search, .apm-custom-link, .apm-agent-configuration, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:16:06.395       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .async-search, .apm-agent-configuration, .apm-custom-link, .kibana_7.12.0_001, .security-7, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:16:06.447       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:16:06.700       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:18:11.998       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .async-search, .apm-agent-configuration, .tasks, .security-7, .apm-custom-link, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:18:12.007       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_task_manager_7.12.0_001, .async-search, .apm-custom-link, .kibana_7.12.0_001, .tasks, .apm-agent-configuration], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:43:48.517       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:43:48.564       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:43:48.574       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .kibana_7.12.0_001, .tasks, .security-7, .kibana_task_manager_7.12.0_001, .apm-agent-configuration, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:43:48.574       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .async-search, .kibana_task_manager_7.12.0_001, .tasks, .apm-custom-link, .apm-agent-configuration, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:43:48.605       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:43:48.702       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:46:22.971       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_7.12.0_001, .tasks, .security-7, .apm-agent-configuration, .apm-custom-link, .kibana_task_manager_7.12.0_001, .async-search], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:46:23.096       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-agent-configuration, .async-search, .tasks, .kibana_7.12.0_001, .apm-custom-link, .security-7, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:46:23.139       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .security-7, .apm-agent-configuration, .kibana_7.12.0_001, .async-search, .apm-custom-link, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:46:24.074       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:46:24.124       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:46:24.128       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .kibana_7.12.0_001, .kibana_task_manager_7.12.0_001, .async-search, .apm-agent-configuration, .tasks, .security-7], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:46:24.128       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .async-search, .security-7, .apm-custom-link, .kibana_7.12.0_001, .apm-agent-configuration, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:46:24.170       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:46:24.324       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:53:29.096       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .security-7, .kibana_7.12.0_001, .apm-custom-link, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:53:29.100       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .async-search, .apm-custom-link, .security-7, .apm-agent-configuration, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:53:29.103       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .async-search, .security-7, .apm-agent-configuration, .tasks, .kibana_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:53:30.040       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:53:30.065       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .kibana_7.12.0_001, .security-7, .tasks, .apm-agent-configuration, .apm-custom-link, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:53:30.071       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-custom-link, .kibana_7.12.0_001, .tasks, .apm-agent-configuration, .security-7, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:53:30.076       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:53:30.107       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:237
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:53:30.202       LEVEL:WARN  ThreadId:
				POSITION:cn.newdt.cloud.service.impl.MetricServiceImpl  LINE:290
				MESSAGE:pod name collection is empty, map = {"kind":"","kubeId":29}

2025-07-28 17:53:30.971       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.async-search, .apm-custom-link, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001, .security-7, .apm-agent-configuration, .tasks], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:53:30.971       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .kibana_task_manager_7.12.0_001, .async-search, .apm-agent-configuration, .apm-custom-link, .security-7, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:53:40.250       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.tasks, .apm-custom-link, .security-7, .async-search, .apm-agent-configuration, .kibana_task_manager_7.12.0_001, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 17:54:08.622       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-28 17:54:08.623       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-28 17:54:08.624       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-28 17:54:08.631       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-28 17:54:39.681       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-28 17:54:39.690       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-28 17:55:14.688       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-28 17:55:19.087       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 17:55:19.119       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 17:55:19.784       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-28 17:55:19.804       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-28 17:55:20.837       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 17:55:20.837       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 17:56:39.115       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-28 17:56:39.115       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-28 17:56:39.117       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-28 17:56:39.121       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-28 17:57:06.595       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-28 17:57:06.595       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-28 17:57:14.679       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-28 17:57:14.679       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-28 17:57:14.679       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-28 17:57:14.679       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-28 17:57:40.229       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-28 17:57:44.704       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 17:57:44.704       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 17:57:45.256       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-28 17:57:45.273       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-28 17:57:46.207       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 17:57:46.207       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 17:58:07.893       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-28 17:58:07.899       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-28 17:58:43.952       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-28 17:58:48.288       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 17:58:48.288       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 17:58:48.834       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-28 17:58:48.854       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-28 17:58:49.934       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 17:58:49.937       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 18:07:09.565       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-28 18:07:09.565       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-28 18:07:09.575       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-28 18:07:09.578       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-28 18:07:29.291       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-28 18:07:29.307       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-28 18:08:10.490       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-28 18:08:15.029       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 18:08:15.035       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 18:08:15.839       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-28 18:08:15.861       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-28 18:08:16.996       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 18:08:16.996       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 18:17:55.224       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-28 18:17:55.224       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-28 18:17:55.229       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-28 18:17:55.230       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-28 18:18:25.061       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-28 18:18:25.070       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-28 18:19:07.592       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-28 18:19:12.367       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 18:19:12.367       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 18:19:13.786       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-28 18:19:13.830       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-28 18:19:15.386       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 18:19:15.386       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 18:20:20.115       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.kibana_task_manager_7.12.0_001, .async-search, .apm-agent-configuration, .tasks, .security-7, .kibana_7.12.0_001, .apm-custom-link], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 18:21:04.391       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.apm-custom-link, .security-7, .kibana_task_manager_7.12.0_001, .async-search, .apm-agent-configuration, .tasks, .kibana_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 18:23:43.989       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-28 18:23:43.989       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-28 18:23:43.990       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-28 18:23:43.992       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end

2025-07-28 18:24:12.930       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat] & group[DEFAULT_GROUP]

2025-07-28 18:24:12.945       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder  LINE:87 
				MESSAGE:Ignore the empty nacos configuration and get it based on dataId[cloud-service-yan-feat.yaml] & group[DEFAULT_GROUP]

2025-07-28 18:24:47.778       LEVEL:WARN  ThreadId:
				POSITION:org.springframework.cloud.loadbalancer.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger  LINE:82 
				MESSAGE:Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.

2025-07-28 18:24:51.950       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 18:24:51.954       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.List<java.lang.String>

2025-07-28 18:24:52.600       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.Integer[]

2025-07-28 18:24:52.615       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.lang.String[]

2025-07-28 18:24:53.620       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 18:24:53.626       LEVEL:WARN  ThreadId:
				POSITION:springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader  LINE:92 
				MESSAGE:Trying to infer dataType java.util.Map<java.lang.String,java.lang.String>

2025-07-28 18:25:23.375       LEVEL:WARN  ThreadId:
				POSITION:org.elasticsearch.client.RestClient  LINE:65 
				MESSAGE:request [GET http://192.168.12.76:32766/_alias?ignore_throttled=false&ignore_unavailable=false&expand_wildcards=open%2Cclosed&allow_no_indices=true] returned 1 warnings: [299 Elasticsearch-7.12.0-78722783c38caa25a70982b5b042074cde5d3b3a "this request accesses system indices: [.security-7, .kibana_7.12.0_001, .tasks, .apm-custom-link, .async-search, .apm-agent-configuration, .kibana_task_manager_7.12.0_001], but in a future major version, direct access to system indices will be prevented by default"]

2025-07-28 18:56:09.470       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:102
				MESSAGE:[HttpClientBeanHolder] Start destroying common HttpClient

2025-07-28 18:56:09.470       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:136
				MESSAGE:[NotifyCenter] Start destroying Publisher

2025-07-28 18:56:09.481       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.notify.NotifyCenter  LINE:153
				MESSAGE:[NotifyCenter] Destruction of the end

2025-07-28 18:56:09.490       LEVEL:WARN  ThreadId:
				POSITION:com.alibaba.nacos.common.http.HttpClientBeanHolder  LINE:111
				MESSAGE:[HttpClientBeanHolder] Destruction of the end
