package cn.newdt.cloud.domain;

import cn.newdt.cloud.constant.CloudAppConstant;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Data
public class CloudApp extends AppMetadata implements Serializable {
    private static ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
//        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    private Integer id;

    private Integer logicAppId;

    private String name;

    private String appSystemName;

    private String kubeName;

    private String ipList;

    /**
     * 即创建人
     */
    private Integer ownerUser;

    /**
     * 创建人姓名
     */
    private String ownerName;

    /**
     * 创建人隶属租户ID
     */
    private Integer ownerTenant;

    /**
     * DB参数模板主键 (MySQL-mysqld.cnf)
     */
    private Integer dbParamTemplateId;

    public String getYaml() {
        return StringUtils.isEmpty(cr) ? crRun : cr;
    }

    @Data
    public static class IpNode {
        private String node;
        private String ip;

        public IpNode() {
        }

        public IpNode(String node, String ip) {
            this.node = node;
            this.ip = ip;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            IpNode ipNode = (IpNode) o;
            return Objects.equals(node, ipNode.node) && Objects.equals(ip, ipNode.ip);
        }

        @Override
        public int hashCode() {
            return Objects.hash(node, ip);
        }

    }

    public IpNode[] getIpNode() {
        if (StringUtils.isEmpty(ipList)) {
            return null;
        }
        IpNode[] ipNodes = null;
        try {
            ipNodes = objectMapper.readValue(ipList, IpNode[].class);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Objects.requireNonNull(ipNodes, "cannot parse param:ipList");
    }

    private Integer readPort;

    private Integer writePort;

    /**
     * db容器资源申请量, 带单位
     */
    private String cpu;
    /**
     * db容器资源申请量, 带单位
     */
    private String memory;
    /**
     * db容器资源申请量, 带单位
     */
    private String disk;

    @JsonIgnore
    private String cr;
    @JsonIgnore
    private String crRun;

    private String version;

//    private boolean deleted;

    public void setDeleted(boolean deleted) {
        this.isDeleted = deleted ? CloudAppConstant.AppDeleteStatus.DELETED : CloudAppConstant.AppDeleteStatus.NORMAL;
    }
    public Boolean getDeleted() {
        return this.isDeleted == CloudAppConstant.AppDeleteStatus.DELETED;
    }

    /**
     * 实例的生命周期状态. {@link CloudAppConstant.AppDeleteStatus}
     * 0: 正常, 1: 清理, 2: 删除进入回收站
     */
    private int isDeleted;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    public String getAlertStatus() {
        return alertStatus;
    }

    public void setAlertStatus(String alertStatus) {
        this.alertStatus = alertStatus;
    }

    /**
     * 告警状态
     */
    private String alertStatus;

    /**
     * 操作状态
     */
    private String status;

    /**
     *持久化存储类名称
     */
    private String storageClassName;

    /**
     * 分片个数
     */
    private Integer masterSize;

    /**
     * 备库个数
     */
    private Integer spareSize;

    /**
     * 纳管使用的nodeport集合
     */
    private String nodeportList;


    private String deployType;

    private String backupDisk;
    private String backupStorageclassname;
    /**
     * 调度策略id
     */
    private Integer KubeSchedulerId;
    private String kubeSchedulerName;

    /**
     * 灾备角色: primary, standby, 可通过灾备切换改变
     */
    private String role;

}