package cn.newdt.cloud.web;

import cn.newdt.cloud.domain.alert.json.Resource;
import cn.newdt.cloud.vo.AlertRuleConfigVo;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.service.alert.AlertConfigService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("cloudnormal/alert/rule/template")
@Slf4j
public class AlertConfigTemplateController {

    @Autowired
    private AlertConfigService configService;

    @ApiOperation("查询告警配置模板列表")
    @PostMapping("list")
    public List<AlertRuleConfigVo> getRules(@RequestBody PageDTO pageDTO) {
        pageDTO.getCondition().put("status", 1);
        return configService.getAlertRuleSetTemaplteVoList(pageDTO);
    }


    @ApiOperation("指定模板批量关联资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "模板id"),
    })
    @PostMapping("{id}")
    @PreAuthorize("@dataAuthorizeService.hasPermission(#resources)")
    public void createRules(@RequestBody @ApiParam List<Resource> resources, @PathVariable("id") int templateId) {
        configService.createNewConfigFromTemplate(resources, templateId, null);
    }

}
