package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.StatusConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.ResourceChangeHis;
import cn.newdt.cloud.dto.NodeDTO;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.impl.OpenGaussDbServiceImpl;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.utils.AppUtil;
import cn.newdt.cloud.utils.JsonUtil;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.bean.UserInfo;
import cn.newdt.commons.utils.UserUtil;
import com.alibaba.fastjson.JSONObject;
import com.shindata.opengauss.v1.OpenGaussCluster;
import com.shindata.opengauss.v1.OpenGaussClusterStatus;
import io.fabric8.kubernetes.client.CustomResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.ScheduleConstant.JOB_DATA_KEY_CHANGE_ID;

@Slf4j
public class OpenGaussWatch extends OpsProcessorContext implements OpsPostProcessor<OpenGaussCluster> {
    @Autowired
    protected OpenGaussDbServiceImpl openGaussDbService;

    @Override
    public OpsResultDTO postProcess(TriggerHis triggerHis) throws Exception {
        Map<String, String> jobDataMap = triggerHis.getJobDataMap();
        String appId = jobDataMap.get("appId");
        CloudApp app = appService.get(Integer.valueOf(appId));
        int kubeId = app.getKubeId();

        //转化为用户对象UserInfo
        String userInfoStr = triggerHis.getJobDataMap().get("userInfo");
        UserInfo userInfo = JSONObject.parseObject(userInfoStr, UserInfo.class);
        UserUtil.setAsyncUserInfo(userInfo);

        log.debug(String.format("[openGaussWatch]kubeid:%d,appid:%s,postProcessing", kubeId, appId));

        //获取cr对象
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        OpenGaussCluster actCr = kubeClient.listCustomResource(
                OpenGaussCluster.class, app.getCrName(), app.getNamespace());
        OpsResultDTO.Builder watchResult = evalOpsResult(triggerHis, actCr, app, kubeClient);

        // 根据备份文件创建应用的恢复操作
        getIsReStoreSuccess(jobDataMap, app, watchResult);
        OpsResultDTO build = watchResult.build();
        if (build.getStopJob()) {
            stopWatch(triggerHis, app, StatusConstant.SUCCESS.equals(build.getStatus()), build);
        } else {
            checkTimeout(triggerHis, watchResult);
        }
        return build;


    }

    /**
     * 涉及到 pod 增减的操作，需要更新 iplist 列的 nodename 值
     * @param app
     */
    protected void updateIpNode(CloudApp app) {
        KubeClient client = kubeClientService.get(app.getKubeId());
        Map<String, String> nodeNameMap = client.listSimpleNodes(
                kubeSchedulerService.queryById(app.getKubeSchedulerId()))
                .stream().collect(Collectors.toMap(NodeDTO::getNodeIp, NodeDTO::getHostname));
        List<CloudApp.IpNode> collect = client.listPod(
                app.getNamespace(),
                openGaussDbService.getKind().labelOfPod(app)).stream()
                .map(p -> new CloudApp.IpNode(nodeNameMap.get(p.getPod().getStatus().getHostIP()), p.getPodIp()))
                .collect(Collectors.toList());
        app.setIpList(JsonUtil.toJson(collect));
        appService.update(app);
    }

    private void stopWatch(TriggerHis triggerHis, CloudApp app, boolean success, OpsResultDTO build) {
        ResourceChangeHis his = resourceChangeHisService.get(
                Integer.parseInt(triggerHis.getJobDataMap().get(JOB_DATA_KEY_CHANGE_ID)));
        ActionEnum actionEnum = ActionEnum.actionTypeOf(his.getAction().toUpperCase());

        //由于安装、扩容、缩容、迁移会涉及到pod的增减，所以需要更新 iplist 列的 nodename 值
        if (actionEnum == ActionEnum.CREATE || actionEnum == ActionEnum.SCALE_OUT
                || actionEnum == ActionEnum.SCALE_IN || actionEnum == ActionEnum.MIGRATE) {
            updateIpNode(app);
        }

        // do stop before handle result
        doStopWatch(app, success, triggerHis, build);
        appService.handleWatchResult(app.getId(), success);

        if (autoManagement && actionEnum != ActionEnum.CREATE) {
            //修改操作调用dmp更新，包括不限于密码变动、扩容、缩容、迁移等
            operationUtil.alterToDMP(app, triggerHis);
        }
    }

    protected void doStopWatch(CloudApp app, boolean success, TriggerHis triggerHis, OpsResultDTO build) {
        log.debug("default doStopWatch does nothing");
    }

/**
 * 评估操作结果并构建相应的结果DTO
 * 此方法用于评估OpenGauss数据库集群的操作结果，并构建一个包含操作状态和消息的DTO对象
 *
 * @param triggerHis 触发历史记录对象，用于获取触发事件的相关信息
 * @param genericCr 自定义资源对象，代表当前的OpenGauss集群状态
 * @param app 云应用对象，包含应用的元数据信息
 * @return 返回一个构建中的OpsResultDTO对象，包含操作的状态和消息
 * @throws Exception 如果操作结果评估过程中发生异常，则抛出此异常
 */
public OpsResultDTO.Builder evalOpsResult(TriggerHis triggerHis, CustomResource genericCr, CloudApp app, KubeClient kubeClient)
        throws Exception{
    // 创建一个OpsResultDTO构建器，用于逐步构建操作结果DTO
    OpsResultDTO.Builder result = OpsResultDTO.builder();

    // 检查自定义资源是否为空，如果为空，则记录错误日志并返回失败结果
    if (null == genericCr) {
        String crLoseFailMsg = String.format("[OpenGauss]实例名称：==%s，实例信息丢失！", app.getCrName());
        log.error(crLoseFailMsg);
        result.stopJob(true).status(StatusConstant.FAIL).msg(crLoseFailMsg);
        return result.stopJob(true).status(StatusConstant.FAIL).msg(crLoseFailMsg);
    }

    // 将自定义资源转换为OpenGaussCluster对象，并解析当前运行的集群配置
    OpenGaussCluster cr = (OpenGaussCluster) genericCr;
    OpenGaussCluster actCr = YamlEngine.unmarshal(app.getCrRun(), OpenGaussCluster.class);

    // 获取集群的状态信息
    OpenGaussClusterStatus status = cr.getStatus();
    // 如果状态信息为空或状态为空字符串，则返回资源未找到的消息
    if (null == status || StringUtils.isEmpty(status.getState()))
        return result.msg(String.format(RESOURCE_NOT_FOUND_MSG, app.getKind(), app.getCrName()));

    // 构建操作结果的消息部分，包含最后更新时间和当前状态
    result.msg(status.getLastUpdateTime() + " App is " + status.getState() + ", message: " + status.getMessage());

    // 检查当前状态是否已经过时
    if (isStatusOutDated(triggerHis, status.getLastUpdateTime())) {
        return result;
    }

    // 根据集群的状态采取不同的操作
    String state = status.getState();
    if ("ready".equalsIgnoreCase(state)) {
        // 如果集群状态为ready且实际配置与状态配置一致，则停止任务并标记为成功
        if (AppUtil.compareBean(actCr.getSpec(), status.getSpec(), null)) {
            result.stopJob(true).status(StatusConstant.SUCCESS);
        }
    }
    // 如果集群状态为invalid，则停止任务并标记为失败
    if ("invalid".equalsIgnoreCase(status.getState())) {
        result.stopJob(true).status(StatusConstant.FAIL);
    }
    // 如果集群状态为failed，则停止任务并标记为失败
    if ("failed".equalsIgnoreCase(status.getState())) {
        result.stopJob(true).status(StatusConstant.FAIL);
    }
    // 返回构建的操作结果DTO
    return result;
}


}
