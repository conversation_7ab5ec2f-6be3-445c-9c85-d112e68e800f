package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.BackupHis;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.RestoreHis;
import cn.newdt.cloud.domain.cr.Elasticsearch;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.mapper.BackupMapper;
import cn.newdt.cloud.mapper.RestoreMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.CloudAppService;
import cn.newdt.cloud.service.KubeClientService;
import cn.newdt.cloud.service.KubeConfigService;
import cn.newdt.cloud.service.RestoreServiceImpl;
import cn.newdt.cloud.service.impl.ElasticsearchClusterService;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.AppInstanceVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.newdt.cloud.constant.DatasourceConstant.CLOUD_BACKUP_HIS;
import static cn.newdt.cloud.constant.DatasourceConstant.SCHEMA;

@Slf4j
public class ElasticsearchBackupAndRestoreWatch implements OpsPostProcessor<Elasticsearch> {

    @Autowired
    private CloudAppService cloudAppService;

    @Autowired
    private KubeClientService kubeClientService;

    @Autowired
    private SSHUtil sshUtil;

    @Autowired
    private BackupMapper backupMapper;

    @Autowired
    private RestoreMapper restoreMapper;

    @Autowired
    private KubeConfigService kubeConfigService;

    @Autowired
    private BackupUtil backupUtil;

    @Autowired
    private RestoreServiceImpl restoreServiceImpl;

    @Autowired
    private ElasticsearchClusterService elasticSearchClusterService;


    @Override
    public OpsResultDTO postProcess(TriggerHis triggerHis) throws Exception {
        //创建返回结果
        OpsResultDTO.Builder result = OpsResultDTO.builder().stopJob(false);  //.stopJob是是否停止任务的标识，TRUE为停止

        //获取定时调度中的参数
        Map<String, String> jobDataMap = triggerHis.getJobDataMap();
        String appId = jobDataMap.get("appId");                 //应用id

        //获取应用
        CloudApp app = cloudAppService.get(Integer.valueOf(appId));

        //创建k8s客户端
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());

        //修改crRun
        CloudApp cloudApp = new CloudApp();
        cloudApp.setCrRun(app.getCr());
        cloudApp.setId(app.getId());
        cloudAppService.update(cloudApp);
        //获取操作类型
        String triggerName = triggerHis.getTriggerName();
        String handType = triggerName.substring(0, triggerName.lastIndexOf("_"));
        //当前时间
        Date nowDate = new Date();
        //获取超时时间
        Integer backupTimeOut = backupUtil.getBackupTimeOut(app.getId());
        if (ActionEnum.BACKUP.toString().equalsIgnoreCase(handType)){
            //备份的轮询操作
            //获取所有需要参数
            String extDataStr = jobDataMap.get("extDataStr");
            JSONObject extData = JSON.parseObject(extDataStr);
            Integer backupHisId = extData.getInteger("backupHisId");
            String changeIdStr = extData.getString("changeId");
            String backupTimestamp = extData.getString("backupTimestamp");
            String backupPodName = extData.getString("backupPodName");
            String backupIndex = extData.getString("backupIndex");
            String backupFtpPath = extData.getString("backupFtpPath");
            Integer changeId = Integer.valueOf(changeIdStr);
            //查询备份历史
            BackupHis backupHis = backupMapper.getBackupHisById(SCHEMA, CLOUD_BACKUP_HIS, backupHisId);
            //记录备份信息：数据库名称、备份路径、备份文件名称
            JSONObject messageObj = new JSONObject();
            if(StringUtils.isEmpty(backupHis.getMessage())){
                backupHis.setMessage(JsonUtil.toJson(messageObj));
            }
            //判断备份是否超时
            boolean isTimeout = backupUtil.checkBackupAndRestoreTimeout(backupHis.getStartTime(), nowDate, backupTimeOut);
            if(isTimeout){
                //补全备份历史
                messageObj.put("msg","备份超时！");
                backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "es备份超时！");
                result.stopJob(true).msg("备份超时！").status(StatusConstant.FAIL);
                OpsResultDTO dto = result.build();
                if (dto.getStopJob()){
                    cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                }
                return dto;
            }else{
                result.stopJob(false).msg("备份中...").status(StatusConstant.RUNNING);
            }
            //获取备份结果
            Map<String, String> checkBackupRes = backupUtil.execCmd(kubeClient, app.getNamespace(), backupPodName, "dump", "sh", "-c", "-c", "cat /data/tmp/backup_result.log");
            if (StringUtils.isEmpty(checkBackupRes.get("success")) || "yes".equals(checkBackupRes.get("success"))) {
                //校验脚本执行成功
                String checkBackup = checkBackupRes.get("result");
                int lastIndexOf = checkBackup.lastIndexOf("is");
                String backupRes = checkBackup.substring(lastIndexOf + 3).replaceAll("\n", "");
                //判断备份结果与索引是否符合要求： 索引 × 4 = 备份结果
                if (backupRes.equals(String.valueOf(Integer.parseInt(backupIndex) * 4))) {
                    //将message中的备份路径补全
                    messageObj.put("backupPath", AppUtil.getESBackupRootPath(app.getNamespace(), app.getCrName()) + "/dump-" + backupTimestamp + ".tar.gz");
                    //补全备份历史
                    messageObj.put("msg", "备份成功！");
                    messageObj.put(CloudAppConstant.BACKUP_PATH_KEY, backupFtpPath);

                    Map<String, Long> podDataSizeMap = new HashMap<>();
                    List<AppInstanceVO> dataPod = elasticSearchClusterService.getDataPod(app);
                    for (AppInstanceVO appInstanceVO : dataPod) {
                        long dataSize = KubeClientUtil.dfUsage(kubeClient, appInstanceVO.getPodName(), AppKind.Elasticsearch.getContainerName(), app.getNamespace(), CloudAppConstant.ELASTICSEARCH_DATA_MOUNT_PATH);
                        podDataSizeMap.put(appInstanceVO.getPodName(), dataSize);
                    }
                    messageObj.put("dataSize", podDataSizeMap);
                    backupHis.setMessage(JsonUtil.toJson(messageObj));
                    backupUtil.backupReturn(backupHis, changeId, StatusConstant.SUCCESS, "dump-" + backupTimestamp + ".tar.gz", "es备份成功！");
                    result.stopJob(true).msg("备份成功！").status(StatusConstant.SUCCESS);
                } else if (backupRes.indexOf("Failed") != -1) {
                    //补全备份历史
                    messageObj.put("msg", "备份失败！" + backupRes);
                    backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "es备份失败！" + backupRes);
                    result.stopJob(true).msg("备份失败！").status(StatusConstant.FAIL);
                    OpsResultDTO dto = result.build();
                    if (dto.getStopJob()) {
                        cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                    }
                    return dto;
                }
            }
        }else if(ActionEnum.RESTORE.toString().equalsIgnoreCase(handType)){
            //恢复的轮询操作
            //时间转换
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
            //获取所有需要参数
            String extDataStr = jobDataMap.get("extDataStr");
            JSONObject extData = JSON.parseObject(extDataStr);
            Integer restoreHisId = extData.getInteger("restoreHisId");
            RestoreHis restoreHis = restoreServiceImpl.get(restoreHisId);
            String resourceChangeIdStr = extData.getString("resourceChangeId");
            Integer resourceChangeId = Integer.valueOf(resourceChangeIdStr);
            String restoreTimestamp = extData.getString("restoreTimestamp");
            String restorePodName = extData.getString("restorePodName");
            String restoreStartDateSDF = extData.getString("restoreStartDateSDF");
            Date restoreStartDate = sdf.parse(restoreStartDateSDF);
            //恢复尚未成功，判断是否超过五分钟
            boolean isTimeout = backupUtil.checkBackupAndRestoreTimeout(restoreStartDate, nowDate, backupTimeOut);
            if(isTimeout){
                //补全恢复历史
                backupUtil.restoreReturn(restoreHis, resourceChangeId, "恢复超时！", StatusConstant.FAIL);
                result.stopJob(true).msg("恢复超时！").status(StatusConstant.FAIL);
                OpsResultDTO dto = result.build();
                if (dto.getStopJob()){
                    cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                }
                return dto;
            }else{
                result.stopJob(false).msg("恢复中...").status(StatusConstant.RUNNING);
            }
            //执行查询恢复的命令，判断是否成功
            log.debug("[es Restore]Check restore result log...");
            Map<String, String> checkBackupRes = backupUtil.execCmd(kubeClient, app.getNamespace(), restorePodName, "dump", "sh", "-c", "-c", "cat /data/tmp/restore_result.log");
            if (StringUtils.isEmpty(checkBackupRes.get("success")) || "yes".equals(checkBackupRes.get("success"))) {
                //校验脚本执行成功
                String checkRestore = checkBackupRes.get("result");
                String checkRestoreRes = checkRestore.replaceAll("\n", "");
                if((restoreTimestamp + "-done.").equals(checkRestoreRes)){
                    //成功
                    backupUtil.restoreReturn(restoreHis, resourceChangeId, "恢复成功！", StatusConstant.SUCCESS);
                    result.stopJob(true).msg("恢复成功！").status(StatusConstant.SUCCESS);
                }
            }
        }
        OpsResultDTO dto = result.build();
        if (dto.getStopJob()){
            cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
        }
        return dto;
    }

    /**
     * 补全备份历史
     * @param backupHisId
     * @param backupEndDate
     * @param backupStartDate
     * @param lastBackupFileName
     */
    public void updateBackupHis(Integer backupHisId, Date backupStartDate, Date backupEndDate,
                                String lastBackupFileName,String isSuccess){
        BackupHis backupHis = new BackupHis();
         backupHis.setBackupHisId(backupHisId);
        //用时
        long interval = (backupEndDate.getTime() - backupStartDate.getTime())/1000;
        String durationStr = String.valueOf(interval);
        int duration = Integer.parseInt(durationStr);
        //备份状态
        backupHis.setStatus(isSuccess);
        //备份结束时间
        backupHis.setEndTime(new Timestamp(System.currentTimeMillis()));
        //备份用时
        backupHis.setDuration(duration);
        //备份文件名称
        if(!"".equalsIgnoreCase(lastBackupFileName)){
            backupHis.setFileName(lastBackupFileName);
        }
        backupMapper.updateBackupHis(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_HIS, backupHis);
    }

}