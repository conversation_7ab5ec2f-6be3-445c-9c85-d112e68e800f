package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.StatusConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.ResourceChangeHis;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.DamengService;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.utils.BeanUtil;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import com.google.common.collect.ImmutableSet;
import com.shindata.cloud.dameng.v1.Dameng;
import com.shindata.cloud.dameng.v1.DamengSpec;
import com.shindata.common.spec.Entries;
import io.fabric8.kubernetes.client.CustomResource;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.ScheduleConstant.JOB_DATA_KEY_CHANGE_ID;

public class DamengResourceWatch extends OpsProcessorContext implements OpsPostProcessor<Dameng> {
    final String RESOURCE_NOT_FOUND_MSG = "%s [%s] not found";

    @Override
    public OpsResultDTO postProcess(TriggerHis triggerHis) throws Exception {
        Map<String, String> jobDataMap = triggerHis.returnMergedJobDataMap();
        String appIdData = jobDataMap.get("appId");
        int appId = Integer.parseInt(appIdData);
        CloudApp app = appService.get(appId);
        KubeClient client = kubeClientService.get(app.getKubeId());

        CustomResource currentDeployCr = client.listCustomResource(Dameng.class, app.getCrName(), app.getNamespace());
        if (currentDeployCr == null)
            return OpsResultDTO.builder().msg(String.format(RESOURCE_NOT_FOUND_MSG, app.getKind(), app.getCrName())).build();
        Dameng dameng = (Dameng) currentDeployCr;

        // todo check outdated only if install
//        if (isStatusOutDated(triggerHis, flink.getStatus().getReconciliationStatus().getReconciliationTimestamp())) {
//            return OpsResultDTO.builder().msg("status is not updated").build();
//        }
        OpsResultDTO.Builder rb = evalOpsResult(triggerHis, dameng, app, client);
        // 根据备份文件创建应用的恢复操作
        getIsReStoreSuccess(jobDataMap, app, rb);
        if (rb.isStopped()) {
            tryComplete(app, triggerHis, rb, dameng);
        }
        return rb.build();

    }

    protected void tryComplete(CloudApp app, TriggerHis triggerHis, OpsResultDTO.Builder result, CustomResource cr) {
        try {
            doStopWatchResource(app, triggerHis, result, cr);
        } catch (Exception e) {
            result.stopJob(false);
            result.msg(e.getMessage());
            result.status(StatusConstant.RUNNING);
            return;
        }
        appService.handleWatchResult(app.getId(), result.isSuccessful());
    }

    void doStopWatchResource(CloudApp app, TriggerHis triggerHis, OpsResultDTO.Builder result, CustomResource cr) {

    }

    protected static Entries dataEntryPeek(DamengSpec spec) {
        return spec.getEntries().stream().filter(e -> e.getName().equals(DamengService.Name.ENTRY_DATABASE)).findAny().orElseThrow(IllegalStateException::new);
//                .peek(entryConsumer)
//                .findAny()
//                .flatMap(e -> e.getPodTemplate().getSpec().getContainers().stream().filter(c -> c.getName().equals(DamengService.Name.CONTAINER_DM)).findFirst())
//                .ifPresent(containerConsumer);
    }

    public OpsResultDTO.Builder evalOpsResult(TriggerHis triggerHis, CustomResource currentDeployCr, CloudApp app, KubeClient kubeClient) throws Exception {
        OpsResultDTO.Builder resultBuilder = OpsResultDTO.builder();
        String crRun = StringUtils.isEmpty(app.getCrRun()) ? app.getCr() : app.getCrRun();
        Dameng expectedDeployCr = YamlEngine.unmarshal(crRun, Dameng.class);
        ConditionCheckerRunner runner = new ConditionCheckerRunner();
        runner.addChecker(statusChecker(currentDeployCr));
        runner.addChecker(specChecker(kubeClient, currentDeployCr, expectedDeployCr));
        runner.addChecker(new ConditionChecker("check role", () -> checkRole(kubeClient, app, currentDeployCr)));

        OpsResultDTO opsResultDTO = runner.runChecks();
        resultBuilder.withResult(opsResultDTO);
        if (StatusConstant.SUCCESS.equals(opsResultDTO.getStatus()))
            resultBuilder.stopJob(true);
        return resultBuilder;
    }

    /**
     * 检查指定应用程序中的Pod是否包含主节点角色
     * 此方法用于确定在Kubernetes集群中，特定应用程序的Pods是否存在主节点（primary role）
     * 它通过调用KubeClient的listPod方法列出应用程序的所有Pod，并检查每个Pod的标签来确定其角色
     *
     * @param client          KubeClient实例，用于与Kubernetes API进行通信
     * @param app             CloudApp对象，代表要检查的应用程序
     * @param currentDeployCr
     * @return 如果Pods中至少有一个主节点角色，则返回true；否则返回false
     */
    private boolean checkRole(KubeClient client, CloudApp app, CustomResource currentDeployCr) {
        // 列出应用程序下所有Pod
        List<PodDTO> pods = client.listPod(app.getNamespace(), AppKind.Dameng.labelOfPod(app))
                .stream().filter(podDTO ->
                        DamengService.Name.ENTRY_DATABASE.equals(podDTO.getLabel(CloudAppConstant.CustomLabels.APP_COMPONENT)))
                .collect(Collectors.toList());

        // 检查Pods中是否存在主节点角色
        return pods.stream().anyMatch(pod -> isABoolean(pod, dataEntryPeek((DamengSpec) currentDeployCr.getSpec()).getReplicas()));
    }

    private static boolean isABoolean(PodDTO pod, int size) {
        String role = pod.getPod().getMetadata().getLabels().get(CloudAppConstant.CustomLabels.ROLE);
        if (size > 1) {
            return CloudAppConstant.ROLE_PRIMARY.equals(role);
        } else {
            return "normal".equalsIgnoreCase(role) || CloudAppConstant.ROLE_PRIMARY.equals(role);
        }
    }

    static String getMainEndpointRole(int size) {
        return size > 1 ? CloudAppConstant.ROLE_PRIMARY : "normal";
    }

    private ConditionChecker[] specChecker(KubeClient client, CustomResource currentDeployCr, CustomResource expectedDeployCr) {
        // 比较 expected spec & current deploy spec
        return new ConditionChecker[]{
                new ConditionChecker(
                        "check spec match",
                        () -> {
                            try {
                                List<String> diffResults = BeanUtil.diff(expectedDeployCr.getSpec(), currentDeployCr.getSpec(),
                                        ImmutableSet.of("maintenance"), true, "");
                                return diffResults.isEmpty();
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        }
                ),
                new ConditionChecker(
                        "check pod",
                        () -> {
                            List<PodDTO> pods = client.listPod(currentDeployCr.getMetadata().getNamespace(),
                                    AppKind.Dameng.labels(currentDeployCr.getMetadata().getName()));
                            // todo check pod numbers and status
                            return !pods.isEmpty();
                        }

                )

        };
    }
    // todo predefined condition checker

    private ConditionChecker[] statusChecker(CustomResource cr) {
        Dameng dameng = (Dameng) cr;
        return new ConditionChecker[]{
                new ConditionChecker(
                        "Check if cr or cr status is not empty",
                        () -> dameng != null && dameng.getStatus() != null,
                        false
                ),
                new ConditionChecker(
                        "check observed generation is ",
                        () -> dameng.getMetadata().getGeneration().equals(dameng.getStatus().getObservedGeneration())
                ),
                new ConditionChecker(
                        "check cr state is ready",
                        () -> AppKind.Dameng.getAlertStatus(dameng) == null,
                        YamlEngine.marshal(dameng.getStatus())
                )
        };
    }

    @AllArgsConstructor
    public static class ConditionChecker {
        private final String description;
        private final Supplier<Boolean> condition;
        // check 的过程中更新result
        private String result = "";
        // still continue if condition fail
        private boolean isContinue = true;
        // stop if condition true
//        private Supplier<Boolean> stopCondition;

        public ConditionChecker(String description, Supplier<Boolean> condition, String result) {
            this.description = description;
            this.condition = condition;
            this.result = result;
        }

        public ConditionChecker(String description, Supplier<Boolean> condition, boolean isContinue) {
            this.description = description;
            this.condition = condition;
            this.isContinue = isContinue;
        }

        public ConditionChecker(String description, Supplier<Boolean> condition) {
            this.description = description;
            this.condition = condition;
        }

        public String getResult() {
            return result;
        }

        public String getDescription() {
            return description;
        }

        public boolean check() {
            return condition.get();
        }
    }

    // refact to dag task framework
    public static class ConditionCheckerRunner {
        private final List<ConditionChecker> checkers = new ArrayList<>();

        public void addChecker(ConditionChecker... checkers) {
            this.checkers.addAll(Arrays.asList(checkers));
        }

        public OpsResultDTO runChecks() {
            StringBuilder sb = new StringBuilder();
            boolean pass = true;
            boolean hasFailure = false;
            for (ConditionChecker checker : checkers) {
                pass = checker.check();
                if (!pass && !hasFailure)
                    hasFailure = true;
                //Checker: SpecChecker
                //Description: Checks if spec match
                //Result: PASS
                String result = pass ? "PASS" : "FAIL" + " " + checker.getResult();
                sb.append("Description: ").append(checker.getDescription()).append("\n")
                        .append("Result: ").append(result).append("\n");
                // if stop

                // if continue
                if (!pass && !checker.isContinue) {
                    break;
                }
            }
            OpsResultDTO.Builder builder = OpsResultDTO.builder();
            builder.msg(sb.toString());
            if (!hasFailure) {
                builder.status(StatusConstant.SUCCESS);
            }
            return builder.build();
        }
    }

    public CloudApp getApp(TriggerHis triggerHis) {
        Map<String, String> jobDataMap = triggerHis.returnMergedJobDataMap();
        String appIdData = jobDataMap.get("appId");
        int appId = Integer.parseInt(appIdData);
        CloudApp app = appService.get(appId);
        return app;
    }

    // refact extract
    public Map<String, Object> getMutableData(TriggerHis triggerHis) {
        ResourceChangeHis his = resourceChangeHisService.get(Integer.parseInt(triggerHis.getJobDataMap().get(JOB_DATA_KEY_CHANGE_ID)));
        Map<String, Object> mutableDataMap = his.mutableDataMap();
        return mutableDataMap;
    }
}
