package cn.newdt.cloud.utils;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.ComponentKindEnum;
import cn.newdt.cloud.domain.AppMetadata;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.cr.*;
import cn.newdt.cloud.dto.Label;
import cn.newdt.cloud.dto.NodeDTO;
import cn.newdt.cloud.dto.PodNodeDTO;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.exception.CustomException;
import com.alibaba.fastjson.JSONPath;
import com.pingcap.v1alpha1.TidbCluster;
import com.pingcap.v1alpha1.tidbclusterspec.Tidb;
import com.shindata.clickhouse.v1.ClickHouseInstallation;
import com.shindata.clickhouse.v1.clickhouseinstallationspec.Templates;
import com.shindata.clickhouse.v1.clickhouseinstallationspec.configuration.clusters.Layout;
import com.shindata.kafka.v1.Kafka;
import com.shindata.kafka.v1.KafkaSpec;
import com.shindata.mysql.v1.MySQLHA;
import com.shindata.mysql.v1.MySQLHASpec;
import com.shindata.opengauss.v1.OpenGaussCluster;
import com.shindata.opengauss.v1.opengaussclusterspec.Iplist;
import com.shindata.postgre.v1.PostgreSql;
import com.shindata.postgre.v1.PostgreSqlSpec;
import com.shindata.redis.v1.RedisClusterSpec;
import com.shindata.redis.v1.RedisSpec;
import com.shindata.redis.v1.SentinelSpec;
import io.fabric8.kubernetes.api.model.Container;
import io.fabric8.kubernetes.api.model.IntOrString;
import io.fabric8.kubernetes.api.model.PodSpec;
import io.fabric8.kubernetes.api.model.Quantity;
import io.fabric8.kubernetes.client.CustomResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.ClassUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.v1beta1.FlinkDeployment;
import org.apache.flink.v1beta1.FlinkDeploymentSpec;

import java.beans.PropertyDescriptor;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class AppUtil {
    public static CloudApp convertOGCrSpecToApp(String crRun) {
        OpenGaussCluster expected = YamlEngine.unmarshal(crRun, OpenGaussCluster.class);
        CloudApp app = new CloudApp();
//        Map<String, Quantity> limits = expected.getSpec().getStatefulSet().getSpec().getTemplate().getSpec().getContainers().get(0).getResources().getLimits();
        app.setCpu(expected.getSpec().getCpu());
        app.setMemory(expected.getSpec().getMemory());
        app.setDisk(expected.getSpec().getStorage());
        app.setBackupDisk(expected.getSpec().getBackupstorage());
       List<Iplist> iplist = expected.getSpec().getIplist();
    /* cr中ipList 结构已不包含node，故改为OpenGaussWatch.watchReturn()中处理ipList
       CloudApp.IpNode[] ipNodes = new CloudApp.IpNode[iplist.size()];
        for (int i = 0; i < iplist.size(); i++) {
            Iplist entry = iplist.get(i);
            ipNodes[i] = new CloudApp.IpNode(entry.getNodename(), entry.getIp());
        }
        app.setIpList(JsonUtil.toJson(ipNodes));*/
        app.setMembers(iplist.size());
        return app;
    }

    /**
     * 根据appName及CloudApp.NodeList信息生成Pod名称
     *
     * @param appName
     * @param ipNodes
     * @return PodNode关联对象列表
     */
    public static List<PodNodeDTO> generateMysqlPodName(String appName, List<CloudApp.IpNode> ipNodes) {
        List<PodNodeDTO> podNodeDTOS = new LinkedList<>();
        for (CloudApp.IpNode ipNode : ipNodes) {
            String podName = "mysql-" + appName + "-" + ipNode.getIp().replaceAll("\\.", "x");
            podNodeDTOS.add(new PodNodeDTO(ipNode, podName));
        }
        return podNodeDTOS;
    }

    public static List<PodNodeDTO> generateOgPodName(String appName, List<CloudApp.IpNode> ipNodes) {
        List<PodNodeDTO> podNodeDTOS = new LinkedList<>();
        for (CloudApp.IpNode ipNode : ipNodes) {
            String podName = "og-" + appName + "-pod-" + ipNode.getIp().replaceAll("\\.", "x");
            podNodeDTOS.add(new PodNodeDTO(ipNode, podName));
        }
        return podNodeDTOS;
    }

    public static List<PodNodeDTO> generateRedisPodName(String appName, List<CloudApp.IpNode> ipNodes) {
        List<PodNodeDTO> podNodeDTOS = new LinkedList<>();
        int i = 0;
        for (CloudApp.IpNode ipNode : ipNodes) {
            String podName = "redis-" + appName + "-" + ipNode.getIp().replaceAll("\\.", "-");
            podNodeDTOS.add(new PodNodeDTO(ipNode, podName));
            i ++;
        }
        return podNodeDTOS;
    }

    public static List<PodNodeDTO> generateSentinelPodName(String appName, List<CloudApp.IpNode> ipNodes) {
        List<PodNodeDTO> podNodeDTOS = new LinkedList<>();
        int i = 0;
        for (CloudApp.IpNode ipNode : ipNodes) {
            String podName = "sentinel-" + appName + "-" + ipNode.getIp().replaceAll("\\.", "-");
            podNodeDTOS.add(new PodNodeDTO(ipNode, podName));
            i ++;
        }
        return podNodeDTOS;
    }

    public static Label[] getPodLabel(CloudApp app) {
        return AppKind.valueOf(app.getKind(), app.getArch()).labelOfPod(app);
    }

    public static String getContainerName(String podName, String kind, String arch) {
        AppKind appKind = AppKind.valueOf(kind, arch);
        Map<String, String> containerByPodName = appKind.getContainersByPodName();
        if (null == containerByPodName || containerByPodName.keySet().isEmpty()){
            return appKind.getContainerName();}

        Set<String> podNamePattern = containerByPodName.keySet();
        String podNamePrefixKey = podNamePattern.stream().filter(podNamePre -> Pattern.compile(podNamePre).matcher(podName).find()).findFirst()
                .orElse(null);
        if (StringUtils.isEmpty(podNamePrefixKey))
            return appKind.getContainerName();
        return containerByPodName.get(podNamePrefixKey);
    }

    public static int getPort(CloudApp app) {
        return AppKind.valueOf(app.getKind(), app.getArch()).getDbPort();
    }

    public static CustomResource createCrObject(CloudApp app) {
        CustomResource cr;
        switch (AppKind.valueOf(app.getKind(), app.getArch())) {
            case MongoDB:
                cr = new MongoDBCommunity(app);
                break;
            default:
                throw new IllegalStateException();
        }
        return cr;
    }

    public static CloudApp.IpNode[] composeNodeAndIp(List<NodeDTO> nodes, List<String> ips){
        if (nodes == null){
            return composeNodeNameAndIp(null, ips);
        }
        List<String> names = nodes.stream().map(NodeDTO::getNodeName).collect(Collectors.toList());
        return composeNodeNameAndIp(names, ips);
    }

    public static CloudApp.IpNode[] composeNodeNameAndIp(List<String> nodes, List<String> ips){
        List<CloudApp.IpNode> ipNodes = new ArrayList<>();
        for (int i = 0; ; i++) {
            String ip = null, node = null;
            if (nodes != null && nodes.size() > i) {
                node = nodes.get(i);
            }
            if (ips != null && ips.size() > i) {
                ip = ips.get(i);
            }
            if (ip != null || node != null) {
                ipNodes.add(new CloudApp.IpNode(node, ip));
            } else {
                break;
            }
        }
        return ipNodes.toArray(new CloudApp.IpNode[0]);
    }

    public static CloudApp convertToApp(String crUpdate, String kind, String arch) {
        // todo refactor
        if (AppKind.MongoDB.getKind().equals(kind) && AppKind.MongoDB.getArch().equals(arch)) {
            return MongoUtil.convertToApp(crUpdate);
        }else if(AppKind.MongoDB_Cluster.getKind().equals(kind) && AppKind.MongoDB_Cluster.getArch().equals(arch)){
            return convertMongoDBClusterCrSpecToApp(crUpdate);
        }else if(AppKind.OpenGauss.getKind().equals(kind)){
            return convertOGCrSpecToApp(crUpdate);
        }else if (AppKind.Redis.getKind().equals(kind) && AppKind.Redis.getArch().equals(arch)){
            com.shindata.redis.v1.Redis redis = YamlEngine.unmarshal(crUpdate, com.shindata.redis.v1.Redis.class);
            RedisSpec spec = redis.getSpec();
            CloudApp app = new CloudApp();
            app.setCpu(spec.getCpu());
            app.setMemory(spec.getMemory());
            app.setDisk(spec.getDataStorage().getSize());
            app.setMembers(spec.getIpList().size());
            CloudApp.IpNode[] ipNodes = AppUtil.composeNodeAndIp(null, spec.getIpList().stream().map(ipList -> ipList.getIp()).collect(Collectors.toList()));
            app.setIpList(JsonUtil.toJson(ipNodes));
            return app;
        } else if (AppKind.Sentinel.getKind().equals(kind)){
            com.shindata.redis.v1.Sentinel sentinel = YamlEngine.unmarshal(crUpdate, com.shindata.redis.v1.Sentinel.class);
            SentinelSpec spec = sentinel.getSpec();
            CloudApp app = new CloudApp();
            app.setCpu(spec.getCpu());
            app.setMemory(spec.getMemory());
            app.setDisk(spec.getDataStorage().getSize());
            app.setMembers(spec.getIpList().size());
            CloudApp.IpNode[] ipNodes = AppUtil.composeNodeAndIp(null, spec.getIpList().stream().map(com.shindata.redis.v1.sentinelspec.IpList::getIp).collect(Collectors.toList()));
            app.setIpList(JsonUtil.toJson(ipNodes));
            return app;
        } else if (AppKind.Redis_Cluster.getKind().equals(kind) && AppKind.Redis_Cluster.getArch().equals(arch)){
            com.shindata.redis.v1.RedisCluster redis = YamlEngine.unmarshal(crUpdate, com.shindata.redis.v1.RedisCluster.class);
            RedisClusterSpec spec = redis.getSpec();
            CloudApp app = new CloudApp();
            app.setCpu(spec.getCpu());
            app.setMemory(spec.getMemory());
            app.setDisk(spec.getDataStorage().getSize());
            app.setMembers(spec.getIpList().size());
            CloudApp.IpNode[] ipNodes = AppUtil.composeNodeAndIp(null, spec.getIpList().stream().map(com.shindata.redis.v1.redisclusterspec.IpList::getIp).collect(Collectors.toList()));
            app.setIpList(JsonUtil.toJson(ipNodes));
            return app;
        } else if (AppKind.valueOf(kind, arch) == AppKind.MYSQL_HA) {
            MySQLHA cr = YamlEngine.unmarshal(crUpdate, MySQLHA.class);
            MySQLHASpec spec = cr.getSpec();
            CloudApp app = new CloudApp();
            app.setCpu(spec.getCpu());
            app.setMemory(spec.getMemory());
            app.setDisk(spec.getStorage().getSize());
            // iplist 由watch更新
            return app;
        }else if (AppKind.Zookeeper.getKind().equals(kind) && AppKind.Zookeeper.getArch().equals(arch)){
            Zookeeper zookeeper = YamlEngine.unmarshal(crUpdate, Zookeeper.class);
            Zookeeper.ZookeeperSpec spec = zookeeper.getSpec();
            CloudApp app = new CloudApp();
            app.setCpu(spec.getCpu());
            app.setMemory(spec.getMemory());
            app.setDisk(spec.getStorage().getSize());
            app.setMembers(spec.getIpList().length);
            CloudApp.IpNode[] ipNodes = AppUtil.composeNodeAndIp(null, Arrays.stream(spec.getIpList()).collect(Collectors.toList()));
            app.setIpList(JsonUtil.toJson(ipNodes));
            return app;
        }else if (AppKind.Kafka.getKind().equals(kind) && AppKind.Kafka.getArch().equals(arch)){
            Kafka kafka = YamlEngine.unmarshal(crUpdate, Kafka.class);
            KafkaSpec spec = kafka.getSpec();
            CloudApp app = new CloudApp();
            app.setCpu(spec.getCpu());
            app.setMemory(spec.getMemory());
            app.setDisk(spec.getStorage().getSize());
            app.setMembers(spec.getIpList().size());
            CloudApp.IpNode[] ipNodes = AppUtil.composeNodeAndIp(null, spec.getIpList().stream().map(com.shindata.kafka.v1.kafkaspec.IpList::getIp).collect(Collectors.toList()));
            app.setIpList(JsonUtil.toJson(ipNodes));
            return app;
        }else if (AppKind.NameServer.getKind().equals(kind) && AppKind.NameServer.getArch().equals(arch)){
            NameServer nameServer = YamlEngine.unmarshal(crUpdate, NameServer.class);
            NameServer.NameServerSpec spec = nameServer.getSpec();
            CloudApp app = new CloudApp();
            app.setCpu(spec.getCpu());
            app.setMemory(spec.getMemory());
            app.setDisk(spec.getStorage().getSize());
            app.setMembers(spec.getIpList().length);
            CloudApp.IpNode[] ipNodes = AppUtil.composeNodeAndIp(null, Arrays.stream(spec.getIpList()).collect(Collectors.toList()));
            app.setIpList(JsonUtil.toJson(ipNodes));
            return app;
        }else if (AppKind.Elasticsearch.getProduct().equals(kind)) {
            Elasticsearch es = YamlEngine.unmarshal(crUpdate, Elasticsearch.class);
            Elasticsearch.ElasticsearchSpec spec = es.getSpec();
            CloudApp patch = new CloudApp();
            Container container = spec.getNodeSets()[1].getPodTemplate().getSpec().getContainers().get(0);
            patch.setCpu(container.getResources().getLimits().get("cpu")+"");
            patch.setMemory(container.getResources().getLimits().get("memory")+"");
            patch.setDisk(spec.getNodeSets()[1].getVolumeClaimTemplates().get(0).getSpec().getResources().getRequests().get("storage")+"");
            spec.getNodeSets()[1].getVolumeClaimTemplates().stream()
                    .filter(pvc->pvc.getMetadata().getName().equals("es-dump-data"))
                    .findAny().ifPresent(pvc -> patch.setBackupDisk(String.valueOf(pvc.getSpec().getResources().getRequests().get("storage"))));
            patch.setVersion(spec.getVersion());
            patch.setMembers(Arrays.stream(spec.getNodeSets()).map(n->n.getCount()).mapToInt(i->i).sum());
            return patch;

        }else if (AppKind.Broker.getKind().equals(kind) && AppKind.Broker.getArch().equals(arch)){
            Broker broker = YamlEngine.unmarshal(crUpdate, Broker.class);
            Broker.BrokerSpec spec = broker.getSpec();
            CloudApp app = new CloudApp();
            app.setCpu(spec.getCpu());
            app.setMemory(spec.getMemory());
            app.setDisk(spec.getStorage().getSize());
            app.setMembers(spec.getIpList().length);
            List<String> ips = new ArrayList<>();
            Arrays.stream(spec.getIpList()).forEach(e -> {
                ips.add(e.getIp());
            });
            CloudApp.IpNode[] ipNodes = AppUtil.composeNodeAndIp(null, ips);
            app.setIpList(JsonUtil.toJson(ipNodes));
            return app;
        } else if (AppKind.Kibana.getKind().equals(kind)){
            return KibanaUtil.convertToApp(crUpdate);
        }else if (AppKind.valueOf(kind, arch) == AppKind.MYSQL_MGR) {
            InnoDBCluster cr = YamlEngine.unmarshal(crUpdate, InnoDBCluster.class);
            InnoDBCluster.InnoDBClusterSpec innoDBClusterSpec = cr.getSpec();
            PodSpec spec = innoDBClusterSpec.getPodSpec();
            CloudApp app = new CloudApp();
            Map<String, Quantity> limits = spec.getContainers().get(0).getResources().getLimits();
            app.setCpu(limits.get("cpu").toString());
            app.setMemory(limits.get("memory").toString());
            app.setDisk(innoDBClusterSpec.getDatadirVolumeClaimTemplate().getResources().getRequests().get("storage").toString());
            app.setMembers(innoDBClusterSpec.getInstances());
            return app;
        } else if (AppKind.valueOf(kind, arch) == AppKind.PostgreSQL) {
            PostgreSql cr = YamlEngine.unmarshal(crUpdate, PostgreSql.class);
            PostgreSqlSpec spec = cr.getSpec();
            CloudApp app = new CloudApp();
            app.setCpu(spec.getCpu());
            app.setMemory(spec.getMemory());
            app.setDisk(spec.getDataStorage().getSize());
            app.setMembers(spec.getIpList().size());
            return app;
        } else if (AppKind.valueOf(kind, arch) == AppKind.Vastbase) {
            return new CloudApp();
        } else if (AppKind.valueOf(kind, arch) == AppKind.Flink) {
            FlinkDeployment cr = YamlEngine.unmarshal(crUpdate, FlinkDeployment.class);
            CloudApp app = new CloudApp();
            FlinkDeploymentSpec spec = cr.getSpec();
            app.setCpu(spec.getJobManager().getResource().getCpu() + "");
            app.setMemory(spec.getJobManager().getResource().getMemory());
            app.setMembers((int) (spec.getJobManager().getReplicas() + spec.getTaskManager().getReplicas()));
//            app.setCpu(spec.getJobManager().getResource().getCpu() + spec.getTaskManager().getResource().getCpu() + "");
//            long totalMemory = MetricUtil.getLongValue(spec.getJobManager().getResource().getMemory())
//                    + MetricUtil.getLongValue(spec.getTaskManager().getResource().getMemory());
//            app.setMemory(MetricUtil.humanReadableByteCountBin(totalMemory).replace("B", "")
//                    .replace(" ", ""));
            return app;
        } else if (AppKind.valueOf(kind, arch) == AppKind.ClickHouse_Zookeeper) {
            Zookeeper zookeeper = YamlEngine.unmarshal(crUpdate, Zookeeper.class);
            Zookeeper.ZookeeperSpec spec = zookeeper.getSpec();
            CloudApp app = new CloudApp();
            app.setCpu(spec.getCpu());
            app.setMemory(spec.getMemory());
            app.setDisk(spec.getStorage().getSize());
            app.setMembers(spec.getIpList().length);
            CloudApp.IpNode[] ipNodes = AppUtil.composeNodeAndIp(null, Arrays.stream(spec.getIpList()).collect(Collectors.toList()));
            app.setIpList(JsonUtil.toJson(ipNodes));
            return app;
        } else if (AppKind.valueOf(kind, arch) == AppKind.Clickhouse) {
            ClickHouseInstallation clickhouseCr = YamlEngine.unmarshal(crUpdate, ClickHouseInstallation.class);
            Templates templates = clickhouseCr.getSpec().getTemplates();
            Map<String, Quantity> clickhouseLimitMap = templates.getPodTemplates().get(0).getSpec().getContainers().stream().filter(c -> c.getName().equals("clickhouse")).findFirst().get().getResources().getLimits();
            Quantity storage = templates.getVolumeClaimTemplates().get(0).getSpec().getResources().getRequests().get("storage");
            CloudApp app = new CloudApp();
            app.setCpu(clickhouseLimitMap.get(CloudAppConstant.ResourceName.CPU).toString());
            app.setMemory(clickhouseLimitMap.get(CloudAppConstant.ResourceName.MEMORY).toString());
            app.setDisk(storage.toString());
            Layout layout = clickhouseCr.getSpec().getConfiguration().getClusters().get(0).getLayout();
            app.setMembers(null == layout ? 1 : layout.getShardsCount().intValue() * layout.getReplicasCount().intValue());
            return app;
        } else if (AppKind.valueOf(kind, arch) == AppKind.TIDB) {
            TidbCluster tidbCr = YamlEngine.unmarshal(crUpdate, TidbCluster.class);
            Tidb tidb = tidbCr.getSpec().getTidb();
            Map<String, IntOrString> requests = tidb.getRequests();
            Map<String, IntOrString> limits = tidb.getLimits();
            CloudApp app = new CloudApp();
            app.setCpu(limits.get(CloudAppConstant.ResourceName.CPU).toString());
            app.setMemory(limits.get(CloudAppConstant.ResourceName.MEMORY).toString());
            // app.setDisk(requests.get(CloudAppConstant.ResourceName.STORAGE).toString());
            app.setMembers(tidb.getReplicas());
            return app;
        } else if (AppKind.valueOf(kind, arch) == AppKind.Dameng) {
            return new CloudApp();
        } else {
            throw new UnsupportedOperationException("have not implement " + kind);
        }
    }

    private static CloudApp convertMongoDBClusterCrSpecToApp(String crUpdate) {
        MongoDBCluster mongodbcluster = YamlEngine.unmarshal(crUpdate, MongoDBCluster.class);
        MongoDBCluster.MongoDBClusterSpec spec = mongodbcluster.getSpec();
        CloudApp app = new CloudApp();
        app.setCpu(spec.getShardServers().getCpu());
        app.setMemory(spec.getShardServers().getMemory());
        app.setDisk(spec.getShardServers().getDataStorage().getSize());
        app.setMembers(spec.getShardServers().getIpList().size());
        CloudApp.IpNode[] ipNodes = AppUtil.composeNodeAndIp(null, spec.getShardServers().getIpList());
        app.setIpList(JsonUtil.toJson(ipNodes));
        return app;
    }

    public static List<PodNodeDTO> generateRedisClusterPodName(String crName, List<CloudApp.IpNode> ipNodes) {
        List<PodNodeDTO> podNodeDTOS = new LinkedList<>();
        int i = 0;
        for (CloudApp.IpNode ipNode : ipNodes) {
            String podName = "rc-" + crName + "-" + ipNode.getIp().replaceAll("\\.", "-");
            podNodeDTOS.add(new PodNodeDTO(ipNode, podName));
            i ++;
        }
        return podNodeDTOS;
    }

    public static List<PodNodeDTO> generateZookeeperPodName(String crName, List<CloudApp.IpNode> ipNodes) {
        List<PodNodeDTO> podNodeDTOS = new LinkedList<>();
        int i = 0;
        for (CloudApp.IpNode ipNode : ipNodes) {
            String podName = "zk-" + crName + "-" + ipNode.getIp().replaceAll("\\.", "-");
            podNodeDTOS.add(new PodNodeDTO(ipNode, podName));
            i++;
        }
        return podNodeDTOS;
    }

    public static List<PodNodeDTO> generateKafkaPodName(String crName, List<CloudApp.IpNode> ipNodes) {
        List<PodNodeDTO> podNodeDTOS = new LinkedList<>();
        int i = 0;
        for (CloudApp.IpNode ipNode : ipNodes) {
            String podName = "kafka-" + crName + "-" + ipNode.getIp().replaceAll("\\.", "-");
            podNodeDTOS.add(new PodNodeDTO(ipNode, podName));
            i ++;
        }
        return podNodeDTOS;
    }

    public static  List<PodNodeDTO> generateBrokerPodName(String crName, List<CloudApp.IpNode> ipNodes) {
        List<PodNodeDTO> podNodeDTOS = new LinkedList<>();
        for (CloudApp.IpNode ipNode : ipNodes) {
            String podName = "rocketmq-" + crName + "-broker-" + ipNode.getIp().replaceAll("\\.", "-");
            podNodeDTOS.add(new PodNodeDTO(ipNode, podName));
        }
        return podNodeDTOS;
    }

    public static List<PodNodeDTO> generateNamesperverPodName(String crName, List<CloudApp.IpNode> ipNodes) {
        List<PodNodeDTO> podNodeDTOS = new LinkedList<>();
        for (CloudApp.IpNode ipNode : ipNodes) {
            String podName = "rocketmq-" + crName + "-nameserver-" + ipNode.getIp().replaceAll("\\.", "-");
            podNodeDTOS.add(new PodNodeDTO(ipNode, podName));
        }
        return podNodeDTOS;
    }

    public static List<String> generateClickHousePodName(String crName, Layout layout) {
        List<String> resutlts = new ArrayList<>();
        for (int i = 0; i < layout.getShardsCount().intValue(); i++) {
            for (int j = 0; j < layout.getReplicasCount().intValue(); j++) {
                resutlts.add("chi-" + crName + "-" + crName + "-" + i + "-" + j + "-0");
            }
        }
        return resutlts;
    }

    public static List<String> generateTidbPodName(AppMetadata app) {
        List<String> resutlts = new ArrayList<>();
        String crYaml = StringUtils.isNotEmpty(app.getCr()) ? app.getCr() : app.getCrRun();
        TidbCluster tidbCluster = YamlEngine.unmarshal(crYaml, TidbCluster.class);

        for (Integer i = 0; i < tidbCluster.getSpec().getPd().getReplicas(); i++) {
            resutlts.add(app.getCrName() + "-" + ComponentKindEnum.PD.name().toLowerCase() + "-" + i);
        }

        for (Integer i = 0; i < tidbCluster.getSpec().getTidb().getReplicas(); i++) {
            resutlts.add(app.getCrName() + "-" + ComponentKindEnum.TIDB.name().toLowerCase() + "-" + i);
        }

        for (Integer i = 0; i < tidbCluster.getSpec().getTikv().getReplicas(); i++) {
            resutlts.add(app.getCrName() + "-" + ComponentKindEnum.TIKV.name().toLowerCase() + "-" + i);
        }
        return resutlts;
    }


    public static int compareVersion(String v1, String v2) {
        String s1 = normalisedVersion(v1);
        String s2 = normalisedVersion(v2);
        int cmp = s1.compareTo(s2);
        String cmpStr = cmp < 0 ? "<" : cmp > 0 ? ">" : "==";
        System.out.printf("'%s' %s '%s'%n", v1, cmpStr, v2);
        return cmp;
    }

    private static String normalisedVersion(String version) {
        return normalisedVersion(version, ".", 3);
    }

    private static String normalisedVersion(String version, String sep, int maxWidth) {
        String[] split = Pattern.compile(sep, Pattern.LITERAL).split(version);
        StringBuilder sb = new StringBuilder();
        for (String s : split) {
            sb.append(String.format("%" + maxWidth + 's', s));
        }
        return sb.toString();
    }

    public static <T, U> boolean compareBean(T bean, U bean_, Set<String> excluded) throws Exception {
        return compareBean(bean, bean_, excluded, null);
    }
    /**
     * compare bean or string or primitivewrapper.
     * @param included root fields to be compared
     * @param excluded root fields to be ignored
     */
    public static <T, U> boolean compareBean(T bean, U bean_, Set<String> excluded, Set<String> included) throws Exception {
        if (bean == null && bean_ == null) return true;
        if (bean == null || bean_ == null)
            return false;
        if (bean.getClass() == bean_.getClass() && bean.getClass() ==String.class
                || ClassUtils.isPrimitiveWrapper(bean.getClass())) {
            return Objects.equals(bean, bean_);
        }
        PropertyDescriptor[] propertyDescriptors = PropertyUtils.getPropertyDescriptors(bean);
//        PropertyDescriptor[] propertyDescriptors1 = PropertyUtils.getPropertyDescriptors(bean);
        boolean b = true;
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            if (excluded!= null && excluded.contains(propertyDescriptor.getName())) {
                continue;
            }
            if (included != null && !included.contains(propertyDescriptor.getName())) {
                continue;
            }
            Object property = PropertyUtils.getProperty(bean, propertyDescriptor.getName());
            Object property1 = PropertyUtils.getProperty(bean_, propertyDescriptor.getName());
            if (property == null || property1 == null) {
                return property1 == property || ("".equals(property) || "".equals(property1));
            } else if (property instanceof Collection || property instanceof Map) {
                if (property instanceof ArrayList) {
                    ArrayList l1 = (ArrayList) property;
                    ArrayList l2 = (ArrayList) property1;
                    ListIterator lit1 = l1.listIterator();
                    ListIterator lit2 = l2.listIterator();
                    while (lit1.hasNext()&&lit2.hasNext()) {
                        Object o1 = lit1.next();
                        Object o2 = lit2.next();
                        if (!compareBean(o1, o2, excluded, null)) {
                            b = false;
                            break;
                        }
                    }
                }
                else b = property.equals(property1); // fixme other collections should also compare element use compareBean instead of equals. equals only work with same class.
            } else if (propertyDescriptor.getPropertyType()==String.class
                    || ClassUtils.isPrimitiveWrapper(propertyDescriptor.getPropertyType())) {
                b = Objects.equals(property, property1);
            } else if (propertyDescriptor.getPropertyType().isPrimitive()) {
                b = property == property1;
                if (!b && ClassUtils.isPrimitiveWrapper(property.getClass())) {// fix get property return wrapper type although actual type is primitive
                    b = Objects.equals(property, property1);
                }
            } else {
                b = compareBean(property,property1, excluded, null);
            }
            log.debug("compare property {}, {}", propertyDescriptor.getName(), b);
            if (!b ) {
                break;
            }
        }
        return b;
    }

    public static String getResourceVersion(CloudApp app) {
        String yaml = StringUtils.isEmpty(app.getCr()) ? app.getCrRun() : app.getCr();
        if (StringUtils.isEmpty(yaml))
            throw new IllegalStateException("app.yaml is empty");
        Matcher matcher = Pattern.compile(CloudAppConstant.CustomLabels.RESOURCE_VERSION + ":.*(\\d+)").matcher(yaml);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    public static boolean isBackupPVC(CloudApp app, String pvc) {
        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
        Pattern pvcPattern = Optional.ofNullable(appKind.getPvcPatterns(app)).map(p -> p.get("backup")).orElse(null);
        return pvcPattern != null && pvc != null && pvcPattern.matcher(pvc).find();
    }

    public static boolean isDataPVC(CloudApp app, String pvc) {
        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
        Pattern pvcPattern = Optional.ofNullable(appKind.getPvcPatterns(app)).map(p -> p.get("data")).orElse(null);
        return pvcPattern != null && pvcPattern.matcher(pvc).find();
    }

    public static String getBackupContainerPath(AppKind appKind) {
        switch (appKind) {
            case OpenGauss:
                return "/gaussdata/backup";
            case Elasticsearch:
                return "/data";
            case Redis:
            case MYSQL_HA:
            case MYSQL_MGR:
            case MongoDB:
            case PostgreSQL:
            case Redis_Cluster:
                return "/backup";
            case MongoDB_Cluster:
                return "/backup";
            default:
                throw new UnsupportedOperationException();
        }
    }

    public static String getMainContainerPath(AppKind appKind) {
        switch (appKind) {
            case OpenGauss:
                return "/gaussdata/openGauss";
            case Elasticsearch:
                return "/usr/share/elasticsearch/data";
            case Redis:
            case MYSQL_HA:
            case MongoDB:
            case PostgreSQL:
            case Redis_Cluster:
                return "/data";
            case MongoDB_Cluster:
                return "/data/mongodb";
            default:
                throw new UnsupportedOperationException();
        }
    }

    public static String getSubstring(String path, int count, String separator) {
        while (count > 0) {
            path = path.substring(0, path.lastIndexOf(separator));
            count --;
        }
        return path;
    }

    public static Object readYamlPropertyByPath(String yamlContent, String keyPath) {
        Map unmarshal = YamlEngine.unmarshal(yamlContent, Map.class);
        return JSONPath.eval(unmarshal, keyPath);
    }

    private void testreadYamlPropertyByPath() {
        String yaml = "fruits:\n" +
                "  - apple\n" +
                "  - banana\n" +
                "  - orange\n" +
                "\n" +
                "# Objects\n" +
                "person:\n" +
                "  name: John Doe\n" +
                "  age: 30\n" +
                "  address:\n" +
                "    street: 123 Main St\n" +
                "    city: New York\n" +
                "    country: USA";
        System.out.println(AppUtil.readYamlPropertyByPath(yaml, "$.fruits[1]"));
        System.out.println(AppUtil.readYamlPropertyByPath(yaml, "$.person.address.city"));
    }

    public static String getESBackupRootPath(String namespace, String crName) {
        String backupRoot = "/backup" + "/";
        backupRoot += AppKind.Elasticsearch.getArch().toLowerCase() + "/" + namespace + "/" + crName; // 共享存储需要自行创建目录层级
        return backupRoot;
    }


    public static String getAlertStatus(CustomResource cr) {
        if (cr == null || cr.getStatus() == null) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        Object state = null;
        try {
            // alertStatus = state == ready ? (health.state == green ? null : health.state) : state
            state = PropertyUtils.getProperty(cr.getStatus(), "state");
            if (state == null || "ready".equalsIgnoreCase((String) state)) {
                Object health = getStringPropertyByPath(cr, "health.state");
                if ("green".equals(health))
                    return null;
                else sb.append(health);
            } else sb.append(state);

            // append health message if exists
            String message = getStringPropertyByPath(cr, "health.message");
            if (StringUtils.isEmpty(message)) {
                message = getStringPropertyByPath(cr, "message");
            }
            if (StringUtils.isNotEmpty(message))
                sb.append("-").append(message);

            return sb.toString();
        } catch (Exception e) {
            log.error("get alert status, " + e.getMessage());
            return sb.append(state).toString();
        }
    }

    private static String getStringPropertyByPath(CustomResource cr, String name) {
        String value = null;
        try {
            value = (String) PropertyUtils.getProperty(cr.getStatus(), name);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return value;
    }
}
