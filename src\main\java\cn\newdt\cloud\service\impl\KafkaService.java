package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.common.OpLogContext;
import cn.newdt.cloud.common.TriFunction;
import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.dto.*;
import cn.newdt.cloud.dto.mapper.KafkaMapper;
import cn.newdt.cloud.mapper.NodePortInfoMapper;
import cn.newdt.cloud.mapper.ServiceManagerMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.service.sched.impl.*;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.*;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.exception.CustomException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.shindata.kafka.v1.Kafka;
import com.shindata.kafka.v1.KafkaSpec;
import com.shindata.kafka.v1.kafkaspec.*;
import com.shindata.kafka.v1.kafkaspec.mm2.Clusters;
import com.shindata.kafka.v1.kafkaspec.mm2.Mirrors;
import io.fabric8.kubernetes.api.model.IntOrString;
import io.fabric8.kubernetes.api.model.ObjectMetaBuilder;
import io.fabric8.kubernetes.api.model.Secret;
import io.fabric8.kubernetes.client.CustomResource;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.kafka.clients.admin.*;
import org.apache.kafka.common.KafkaException;
import org.apache.kafka.common.KafkaFuture;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.config.ConfigResource;
import org.apache.kafka.common.config.TopicConfig;
import org.apache.kafka.common.errors.UnknownTopicOrPartitionException;
import org.elasticsearch.common.collect.Tuple;
import org.quartz.SchedulerException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.newdt.cloud.constant.ImageKindEnum.*;
import static java.util.stream.Collectors.toMap;

@Slf4j
@Service
public class KafkaService extends DefaultAppKindService<Kafka> implements ServiceManageOperation, MultiAZService<Kafka>{

    public static final String SECRET_PASSWORD_KEY = "password";
    public static final String SECRET_USERNAME_KEY = "username";
    public static final String DEFAULT_USER_ADMIN = "admin";
    @Autowired
    private AppOperationHandler appOperationHandler;

    @Autowired
    private NetworkServiceImpl networkServiceImpl;

    @Autowired
    private NodePortInfoMapper nodePortInfoMapper;

    @Autowired
    private OperationUtil operationUtil;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private ResourceManagerService resourceManagerService;

    @Autowired
    private KubeClientService kubeClientService;

    @Autowired
    private KubeConfigService kubeConfigService;

    @Autowired
    private CloudAppConfigService appConfigService;

    @Autowired
    private AccessManagementService accessManagementService;

    @Autowired
    private ServiceManagerMapper serviceManagerMapper;

    @Autowired
    private NodePortService nodePortService;

    @Autowired
    private CloudDatabaseUserService dbUserService;

    @Autowired
    private KafkaMapper kafkaMapper;

    @Override
    protected void completeInstanceProperty(List<AppInstanceVO> instances, int appId) {
        CloudApp cloudApp = appService.get(appId);
        KubeClient kubeClient = kubeClientService.get(cloudApp.getKubeId());
        Kafka cr = kubeClient.listCustomResource(Kafka.class, cloudApp.getCrName(), cloudApp.getNamespace());

        instances.forEach(instance -> {
            instance.setComponentKind(instance.getLabels().get(CloudAppConstant.CustomLabels.APP_COMPONENT));
            // zk 节点 role 查询
            Optional.ofNullable(cr).map(Kafka::getStatus).map(s -> s.getZookeeper().getPrimary())
                    .ifPresent(primary -> {
                        String role = primary.equals(instance.getIp()) ? CloudAppConstant.ROLE_PRIMARY : CloudAppConstant.ROLE_SECONDARY;
                        instance.setRole(role);
                    });
        });
    }

    @Override
    public AppKind getKind() {
        return AppKind.Kafka;
    }

    @Override
    public boolean nodePolicy() {
        return false;
    }

    @Override
    public Kafka doInstall(CloudAppVO app, List<String> ips) throws Exception {
        KafkaVO vo = (KafkaVO) app;

        //1.获取镜像
        Map<ImageKindEnum, String> imageManifest = appConfigService.getImageManifest(getKind(), vo.getVersion());
        String kafkaImage = imageManifest.get(MainImage);
        if(StringUtils.isBlank(kafkaImage)){
            throw new CustomException(600, "Kafka应用未找到对应的镜像信息！镜像版本：" + vo.getVersion());
        }
        String exporterImage = imageManifest.getOrDefault(Exporter, "");
        String filebeatImage = imageManifest.getOrDefault(Filebeat, "");

        appService.update(vo);
        // filebeat config
//        ensureFilebeatCM(vo.getNamespace(), clientService.get(vo.getKubeId()), "kafka-filebeat-cm");
        com.shindata.kafka.v1.kafkaspec.Filebeat filebeat = new com.shindata.kafka.v1.kafkaspec.Filebeat();
        filebeat.setImage(filebeatImage);
        filebeat.setConfigFile("kafka-cluster-filebeat.yaml");
        filebeat.setConfigMap("operator-filebeat-configmap");
        //创建annotations
        HashMap<String, String> annotations = new HashMap<>();
        annotations.put("prometheus.io/port", "9095");
        annotations.put("prometheus.io/path", "/_prometheus/metrics");
        annotations.put("prometheus.io/scrape", "true");

        //4.构建cr
        Kafka cr = new Kafka();
        cr.setMetadata(new ObjectMetaBuilder().withName(vo.getCrName()).withNamespace(vo.getNamespace())
                .withAnnotations(annotations)
                .build());
        KafkaSpec kafkaSpec = new KafkaSpec();
        cr.setSpec(kafkaSpec);
        kafkaSpec.setIpList(ips.stream().limit(vo.getMembers()).map(ip -> {
            IpList ipListConfig = new IpList();
            ipListConfig.setIp(ip);
            return ipListConfig;
        }).collect(Collectors.toList()));
        kafkaSpec.setImage(kafkaImage);
        kafkaSpec.setCpu(vo.getCpu());
        kafkaSpec.setMemory(vo.getMemory());
        kafkaSpec.setFilebeat(filebeat);
        kafkaSpec.setExporterImage(exporterImage);
        com.shindata.kafka.v1.kafkaspec.Storage storage = new com.shindata.kafka.v1.kafkaspec.Storage();
        storage.setStorageClass(vo.getStorageClassName());
        storage.setSize(vo.getDisk());
        if (StringUtils.isNotEmpty(vo.getHostpathRoot()))
            storage.setHostpathRoot(vo.getHostpathRoot());
        kafkaSpec.setStorage(storage);

        com.shindata.kafka.v1.kafkaspec.Schedule schedule = new com.shindata.kafka.v1.kafkaspec.Schedule();
        schedule.setAntiAffinityRequired(vo.getAntiAffinityRequired());
        schedule.setTolerations(convertCRTolerations(vo.getToleration(), com.shindata.kafka.v1.kafkaspec.schedule.Tolerations.class));
        schedule.setNodeAffinity(convertCRNodeAffinity(vo.getSelector(), com.shindata.kafka.v1.kafkaspec.schedule.NodeAffinity.class));
        kafkaSpec.setSchedule(schedule);
        Map<String, String> config = new HashMap<>();
        log.info("[kafka安装]参数模板id为：" +  vo.getDbParamTemplateId());

        if (vo.getDbParamTemplateId() != null ){
            MySQLParamTemplateDTO mysqlParamTemplate = cloudDbParamTemplateService.getMysqlParamTemplate(vo.getDbParamTemplateId());
            config = cloudDbParamTemplateService.composeMysqlCnfUseDbParamTemplateToMap(mysqlParamTemplate);
        }
        overWriteCnfParam(vo, config);
        kafkaSpec.setConfig(config);

        // 创建认证secret
        if (StringUtils.isNotEmpty(vo.getPassword())) {
            com.shindata.kafka.v1.kafkaspec.Secret kafkaSecret = new com.shindata.kafka.v1.kafkaspec.Secret();
            String secretName = getSecretName(vo.getCrName());
            kafkaSecret.setSecretName(secretName);
            kafkaSecret.setUsernameKey(SECRET_USERNAME_KEY);
            kafkaSecret.setPasswordKey(SECRET_PASSWORD_KEY);
            kafkaSpec.setSecret(kafkaSecret);
        }

        Zookeeper zookeeperSpec = new Zookeeper();
        kafkaSpec.setZookeeper(zookeeperSpec);
        zookeeperSpec.setCpu(vo.getZkCpu());
        zookeeperSpec.setMemory(vo.getZkMemory());
        Storage zkStorage = new Storage();
        zkStorage.setSize(vo.getZkDisk());
        zkStorage.setStorageClass(vo.getStorageClassName());
        if (StringUtils.isNotEmpty(vo.getHostpathRoot()))
            zkStorage.setHostpathRoot(vo.getHostpathRoot() + "/zk_data");
        zookeeperSpec.setStorage(zkStorage);
        zookeeperSpec.setImage(imageManifest.get(ZK_Component));
        zookeeperSpec.setExporterImage(imageManifest.get(ZK_Component_Exporter));
        zookeeperSpec.setIpList(ips.subList(vo.getMembers(), ips.size()));
        zookeeperSpec.setSchedule(schedule);
        if (StringUtils.isNotEmpty(vo.getZkUsername())) {
            com.shindata.kafka.v1.kafkaspec.Secret zkSecret = new com.shindata.kafka.v1.kafkaspec.Secret();
            zkSecret.setSecretName(getZKSecretName(vo.getCrName()));
            zkSecret.setUsernameKey(SECRET_USERNAME_KEY);
            zkSecret.setPasswordKey(SECRET_PASSWORD_KEY);
            zookeeperSpec.setSecret(zkSecret);
        }
        com.shindata.kafka.v1.kafkaspec.Filebeat zkFilebeat = new Filebeat();
        zkFilebeat.setImage(filebeatImage);
        zkFilebeat.setConfigFile("zookeeper-cluster-filebeat.yaml");
        zkFilebeat.setConfigMap("operator-filebeat-configmap");
        zookeeperSpec.setFilebeat(zkFilebeat);
//        zookeeperSpec.setConfig(setupDbParamConfig(
//                vo.getZkDbParamTemplateId(),
//                vo.getZkTemplateTmpParam(),
//                AppKind.Zookeeper.getProduct(),
//                ImmutableMap.of("cpu",vo.getZkCpu(), "memory", vo.getZkMemory(), "storage", vo.getZkDisk()),
//                null
//        ));

        // configure multi-az cr spec
        if (vo.getDeployType().equals(AppMultiAZService.DeployType.multi_az.name())) {
            configureMultiAZ(cr, vo);
        }

        return cr;
    }

    @Override
    public boolean isParallel() {
        return false;
    }

    @Override
    public void configureMultiAZ(Kafka current, Kafka remote, CloudAppVO app) {
        KafkaVO currentVO = (KafkaVO) app;
        Objects.requireNonNull(remote, "remote cr is null");
        // 将在 standby 所在集群部署mm2
        if (!"standby".equals(currentVO.getRole())) {
            return;
        }

        // todo default config
        currentVO.setMm2Config(JsonUtil.toObject(defaultMm2Config, new TypeReference<Map<String, String>>() {}));

        KafkaSpec kafkaSpec = current.getSpec();
        Mm2 mm2 = kafkaSpec.getMm2();
        if (mm2 == null) {
            mm2 = new Mm2();
            kafkaSpec.setMm2(mm2);
        }

        mm2.setConfig(parseMm2Config(currentVO, null, "source.", "target.", "mirror."));
        // keep idempotent
        Optional.ofNullable(currentVO.getImageConfig())
                .map(imageInfos -> imageInfos.get(Kafka_MirrorMaker))
                .ifPresent(mm2::setImage);
        Optional.ofNullable(currentVO.getMm2Members()).ifPresent(mm2::setReplicas);
        if (StringUtils.isNotEmpty(currentVO.getMm2Cpu()) && StringUtils.isNotEmpty(currentVO.getMm2Memory())) {
            mm2.setResources(ResourceHelper.getInstance().resourceRequirements(
                    ImmutableMap.of("memory", currentVO.getMm2Memory(),
                            "cpu", currentVO.getMm2Cpu())));
        }

        // remote app
        CloudApp sourceApp = appLogicService.getPhysicApps(currentVO.getLogicAppId())
                .stream().filter(papp -> Objects.equals(currentVO.getId(), papp.getId()))
                .findAny().get();

        // set clusters, idempotent
        com.shindata.kafka.v1.kafkaspec.Secret kafkaSecret = null;
        kafkaSecret = new com.shindata.kafka.v1.kafkaspec.Secret();
        String secretName = getSecretName(current.getMetadata().getName());
        kafkaSecret.setSecretName(secretName);
        kafkaSecret.setUsernameKey(SECRET_USERNAME_KEY);
        kafkaSecret.setPasswordKey(SECRET_PASSWORD_KEY);
        Clusters source = source(remote, kafkaSecret, currentVO);// remote is source
        Clusters target = target(current, kafkaSecret, currentVO);// current is target
        mm2.setClusters(ImmutableList.of(
                source, target
        ));

        // set mirrors, idempotent
        Mirrors mirror = new Mirrors();
        mirror.setSource("source");
        mirror.setTarget("target");
        if (currentVO.getMm2Config() != null) {
            Map<String, IntOrString> config = parseMm2Config(currentVO, "mirror.");
            mirror.setConfig(config);
        }
        mm2.setMirrors(ImmutableList.of(mirror));
    }

    private final String defaultMm2Config = "{\n" +
            "    \"tasks.max\": \"10\",\n" +
            "    \"target.producer.ack\": \"1\",\n" +
            "    \"mirror.topics\": \".*\",\n" +
            "    \"mirror.refresh.groups.interval.seconds\": \"60\",\n" +
            "    \"mirror.refresh.topics.interval.seconds\": \"60\",\n" +
            "    \"mirror.replication.policy.class\": \"org.apache.kafka.connect.mirror.IdentityReplicationPolicy\",\n" +
            "    \"mirror.sync.group.offsets.enabled\": \"true\",\n" +
            "    \"mirror.offset-syncs.topic.location\": \"target\"\n" +
            " }";
    // prefix should contain tailing dot, e.g. 'mirror.'
    private Map<String, IntOrString> parseMm2Config(KafkaVO vo, String prefix, String... ignorePrefix) {
        Map<String, IntOrString> config = vo.getMm2Config().entrySet().stream()
                .filter(e -> prefix == null || e.getKey().startsWith(prefix))
                .filter(e -> ignorePrefix == null
                        || Arrays.stream(ignorePrefix).noneMatch(ip -> e.getKey().startsWith(ip)) )
                .collect(toMap(e -> prefix != null ? e.getKey().replaceAll(prefix, "") : e.getKey(),
                        e -> intOrString(e.getValue())));
        return config;
    }

    @Override
    public void switchAZRole(int logicID, int currentPrimaryAppId) {
        CloudApp primaryApp = appLogicService.getPrimaryApp(logicID);
        if (!primaryApp.getId().equals(currentPrimaryAppId))
            throw new CustomException(600, "当前primary实例设置不正确");
        try {
            Map<Boolean, List<CloudApp>> collect = appLogicService.getPhysicApps(logicID).stream()
                    .collect(Collectors.partitioningBy(p -> p.getId().equals(currentPrimaryAppId)));
            List<Kafka> previousStandby = new ArrayList<>();
            // 更新cr. 切换原来的从为主
            collect.get(false).stream().findFirst().ifPresent(app -> {
                final CloudApp newPrimary = app;
                KubeClient client = kubeClientService.get(newPrimary.getKubeId());
                Kafka cr = YamlEngine.unmarshal(newPrimary.getCr(), Kafka.class);
                previousStandby.add(YamlEngine.unmarshal(newPrimary.getCr(), Kafka.class));
                cr.getSpec().setMm2(null);
                client.updateCustomResource(cr, Kafka.class);
//                app.setCrRun(YamlEngine.marshal(cr));
//                appService.update(app);
//                OpLogContext.instance().CR(YamlEngine.marshal(cr), app.getCr());
//                todo 不支持一个操作涉及多个app的回滚, 回滚时
            });
            // 更新cr. 切换原来的主为从
            collect.get(true).stream().findFirst().ifPresent(app -> {
                final CloudApp newstandby = SerializationUtils.clone(app);
                newstandby.setRole(CloudAppConstant.ROLE_STANDBY);
                KubeClient client = kubeClientService.get(newstandby.getKubeId());
                Kafka cr = YamlEngine.unmarshal(newstandby.getCr(), Kafka.class);

                Kafka remote = previousStandby.get(0);
                KafkaService.KafkaVO kafkaVO = new KafkaService.KafkaVO();
                BeanUtils.copyProperties(newstandby, kafkaVO);
                cr.getSpec().setMm2(remote.getSpec().getMm2()); // 从原mm2配置复制规格属性
                configureMultiAZ(cr, remote, kafkaVO);
                OpLogContext.instance().CR(YamlEngine.marshal(cr), app.getCr());

                try {
                    // 以原主为操作对象提交，回滚时在watch触发standby回滚
                    appService.callScheduler(app, YamlEngine.marshal(cr), null,
                            ActionEnum.SWITCH_AZ_ROLE, getProcessorClass(ActionEnum.SWITCH_AZ_ROLE));
                } catch (SchedulerException | JsonProcessingException e) {
                    throw new RuntimeException(e);
                }

                client.updateCustomResource(cr, Kafka.class);
            });

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Autowired
    MetricService metricService;
    @Override
    public Map<String, Object> getAZSummary(CloudApp app) {
        if (CloudAppConstant.ROLE_PRIMARY.equals(app.getRole())) {
            List<TopicDTO> topics = null;
            topics = getTopics(app.getId());
            return ImmutableMap.<String, Object>builder()
                    .put("id", app.getId())
                    .put("role", app.getRole())
                    .put("topics", topics).build();
        } else {
            // metric reporter
//            MetricClient metricClient = metricService.getMetricClient();
            // format: [{topic: "", partitions: [{"partition":"", "latency":int]}]
//            String server = kubeConfigService.get(app.getKubeId()).getPrometheusServer();
//            Object metric = new MetricReporter(metricClient, server) // todo client integrate server
//                    .get(MetricReporter.MetricExpr.format(
//                                    "sum by (topic,partition)(kafka_connect_mirror_mirrorsourceconnector_replication_latency_ms_avg{job=\"${job}\",namespace=\"${namespace}\"})",
//                                    ImmutableMap.of("job", "kafka-" + app.getCrName(), "namespace", app.getNamespace())
//                            ), response -> {
//                                List<MetricClient.VectorMetric> result = response.getData().getResult();
//                                return result.stream().collect(Collectors.groupingBy(v -> (String) v.getMetric().get("topic"),
//                                                Collectors.mapping(v -> {
//                                                    Map<String, Object> labels = v.getMetric();
//                                                    String partition = (String) labels.get("partition");
//                                                    String val = v.getValue()[1] + "";
//                                                    BigDecimal value = StringUtils.isNumeric(val) ? new BigDecimal(val) : BigDecimal.ZERO;
//                                                    return ImmutableMap.of("partition", partition, "latency", value);
//                                                }, Collectors.toList())))
//                                        .entrySet().stream().map(e -> {
//                                            return ImmutableMap.of("topic", e.getKey(), "partitions", e.getValue()); // Create final topic map
//                                        }).collect(Collectors.toList());
//                            }
//                    );

            // fetch metric from exporter
            final String exporterUrl = "localhost:9097/metrics";
            final KubeClient client = clientService.get(app.getKubeId());
            // fetch from every mm2 instance
            CompletableFuture<Object> metricFuture = CompletableFuture.supplyAsync(() -> {
                return appService.findInstances(app.getId()).stream()
                        .filter(p -> p.getComponentKind().equals("mirror-maker"))
                        .parallel()
                        .flatMap(p -> {
                            String cmd = "curl " + exporterUrl;
                            try {
                                String text = client.execCmd(app.getNamespace(), p.getPodName(), "mm2", "bash", "-c", cmd);
                                return MetricUtil.parseExporterMetric(
                                        text,
                                        "kafka_connect_mirror_mirrorsourceconnector_replication_latency_ms_avg").stream();
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        }).collect(Collectors.groupingBy(
                                sample -> sample.getLabels().get("topic"),
                                Collectors.mapping(
                                        sample -> ImmutableMap.of("partition", sample.getLabels().get("partition"),
                                                "latency", sample.getValue()),
                                        Collectors.toList())
                        )).entrySet().stream()
                        .map(entry -> ImmutableMap.of("topic", entry.getKey(), "partitions", entry.getValue()))
                        .collect(Collectors.toList());
            });
            CompletableFuture<List<TopicDTO>> topicsFuture = CompletableFuture.supplyAsync(() -> {
                return getTopics(app.getId());
            });

            CompletableFuture<String> statusFuture = CompletableFuture.supplyAsync(() -> {
                return appService.findInstances(app.getId()).stream()
                        .filter(p -> p.getComponentKind().equals("mirror-maker"))
                        .filter(p -> !CloudAppConstant.PodStatus.RUNNING.equalsIgnoreCase(p.getStatus()))
                        .findAny()
                        .map(p -> "recovering").orElse("syncing");
            });
            List<TopicDTO> topics = Collections.emptyList();
            try {
                topics = topicsFuture.join();
            } catch (Exception e) {
                log.error("", e);
            }
            Object metric = Collections.emptyList();
            try {
                metric = metricFuture.join();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            String status = "";
            try {
                status = statusFuture.join();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            return ImmutableMap.<String, Object>builder()
                    .put("id", app.getId())
                    .put("role", app.getRole())
                    .put("topics", topics)
                    .put("sync_chan", metric)
                    .put("sync_status", status)
                    .build();

        }
    }

    private List<TopicDTO> getTopics(int id) {
        try {
            return topicCache.get(id).stream()
                    .filter(topic->
                            !topic.getName().contains("internal") &&
                            !topic.getName().equals("__consumer_offsets") &&
                            !topic.getName().equals("heartbeats")
                    )
                    .collect(Collectors.toList());
        } catch (ExecutionException e) {
            log.error("", e);
            return Collections.emptyList();
        }
    }

    @Override
    public KafkaMapper getMapper() {
        return kafkaMapper;
    }

    // change to atomic metric cache
    private LoadingCache<Integer, List<TopicDTO>> topicCache = CacheBuilder.newBuilder()
            .maximumSize(100)
            .refreshAfterWrite(1, TimeUnit.MINUTES)
            .expireAfterAccess(2, TimeUnit.MINUTES)
            .build(new CacheLoader<Integer, List<TopicDTO>>() {
                @Override
                public List<TopicDTO> load(Integer key) throws Exception {
                    CloudApp app = appService.find(key).orElseThrow(() -> new IllegalStateException("app was deleted - " + key));
                    return getTopics(app);
                }
            });

    public List<TopicDTO> getTopics(CloudApp app) {
        // return getTopicsByApi(app);
        // getTopicFromMetric
        KubeClient client = clientService.get(app.getKubeId());

        List<List<MetricUtil.MetricSample>> collect = appService.findInstances(app.getId()).stream()
                .filter(p -> p.getComponentKind().equals("kafka"))
                .flatMap(pod -> ImmutableSet.of("kafka_topic_partition_current_offset", "kafka_topic_partition_oldest_offset")
                        .stream().map(metric -> new ImmutablePair(pod, metric)))
                .parallel().map(pair -> fetchMetric((AppInstanceVO) pair.getLeft(), client, (String) pair.getRight()))
                .collect(Collectors.toList());
        return collect.stream().flatMap(s->s.stream())
                .collect(Collectors.groupingBy(m -> m.getLabels().get("topic"),
                        Collectors.mapping(m -> m,
                                Collectors.groupingBy(m -> m.getLabels().get("partition")))) // {topic: {partition: []}}
                ).entrySet().stream().map(entry -> {
                    return new TopicDTO(entry.getKey(), null, null,
                            entry.getValue().entrySet().stream()
                                    .collect(Collectors.toMap( // {partition: []}.entry -> {partition: {PartitionDTO}}
                                            partitionEntry -> Integer.valueOf(partitionEntry.getKey()),
                                            partitionEntry -> {
                                                String partition = partitionEntry.getKey();
                                                List<MetricUtil.MetricSample> offsets = partitionEntry.getValue();
                                                Map<String, Double> map = offsets.stream()
                                                        .collect(toMap(
                                                                MetricUtil.MetricSample::getName,
                                                                MetricUtil.MetricSample::getValue, Math::max));
                                                return new TopicDTO.Partition(
                                                        Integer.parseInt(partition), null,
                                                        Optional.ofNullable(map.get("kafka_topic_partition_oldest_offset")).map(d -> d.longValue()).orElse(0L),
                                                        Optional.ofNullable(map.get("kafka_topic_partition_current_offset")).map(d -> d.longValue()).orElse(0L)
                                                );
                                            })));
                }).collect(Collectors.toList());

//        ImmutableMap.of(
//                "partition", m.getLabels().get("partition"), "name", m.getName(), "value", m.getValue()


    }

    private List<MetricUtil.MetricSample> fetchMetric(AppInstanceVO pod, KubeClient client, String metric) {
        System.out.println(System.currentTimeMillis() + "-" + Thread.currentThread().getName() + "-pod:" + pod.getPodName() + "-metric:" + metric);
        final String exporterUrl = "localhost:9308/metrics";
        String cmd = "curl -L " + exporterUrl;
        try {
            String text = client.execCmd(pod.getNamespace(), pod.getPodName(), "exporter", 30, "bash", "-c", cmd);
            return MetricUtil.parseExporterMetric(text, metric);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private List<TopicDTO> getTopicsByApi(CloudApp app) {
        // todo service ip
        String serviceIP = IPUtil.getIpFromUrl(kubeConfigService.get(app.getKubeId()).getServer());
        String bootstrapServers = accessManagementService.serviceList(app.getId())
                .stream().map(svc -> serviceIP + ":" + svc.getPort())
                .collect(Collectors.joining(","));

        try (AdminClient a = getKafkaClient(app, bootstrapServers)) {
            // topics offset. this can also get from 2 metric: kafka_topic_partition_oldest_offset, kafka_topic_partition_current_offset
            return getTopics(a);
        } catch (Exception e) {
            log.error("get topics error", e);
            return Collections.emptyList();
        }
    }

    public List<TopicDTO> getTopics(AdminClient a) {
        try {
            Set<String> topics = a.listTopics(new ListTopicsOptions().listInternal(true)).names().get();
            Map<String, TopicDescription> descriptionMap =
                    collectWithExceptionFilter(a.describeTopics(topics).topicNameValues(), UnknownTopicOrPartitionException.class);
            Map<TopicPartition, Long> earliest = listOffsets(a, descriptionMap.values(), OffsetSpec.earliest());
            Map<TopicPartition, Long> latest = listOffsets(a, descriptionMap.values(), OffsetSpec.latest());

            Map<TopicPartition, Offsets> offsets = Sets.intersection(earliest.keySet(), latest.keySet()).stream()
                    .collect(toMap(tp -> tp,
                            tp -> new Offsets(earliest.get(tp), latest.get(tp))));


            List<TopicDTO> collect = topics.stream().map(name ->
                    TopicDTO.from(descriptionMap.get(name), offsets)
            ).collect(Collectors.toList());

            return collect;
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    private Map<TopicPartition, Long> listOffsets(AdminClient a, Collection<TopicDescription> descriptions, OffsetSpec offsetSpec) {
        List<TopicPartition> topicPartitions = descriptions.stream().flatMap(
                        description -> description.partitions().stream().map(
                                topicPartitionInfo ->
                                        new TopicPartition(description.name(), topicPartitionInfo.partition())
                                ))
                .collect(Collectors.toList());
        ListOffsetsResult listOffsetsResult =
                a.listOffsets(topicPartitions.stream().collect(toMap(tp -> tp, tp -> offsetSpec)));

        Map<TopicPartition, KafkaFuture<ListOffsetsResult.ListOffsetsResultInfo>> perPartitionResults = new HashMap<>();
        topicPartitions.forEach(p -> perPartitionResults.put(p, listOffsetsResult.partitionResult(p)));

        Map<TopicPartition, ListOffsetsResult.ListOffsetsResultInfo> r =
                collectWithExceptionFilter(perPartitionResults, UnknownTopicOrPartitionException.class);
        Map<TopicPartition, Long> collect = r.entrySet().stream().collect(toMap(Map.Entry::getKey,
                        e->e.getValue().offset()));
        return collect;
    }

    // filter error futures and return remain result
    <K,V> Map<K,V> collectWithExceptionFilter(Map<K, KafkaFuture<V>> values,
                                   Class<? extends KafkaException>... classes) {
        return values.entrySet().parallelStream()
                .map(e -> {
                    try {
                        return Tuple.tuple(e.getKey(), Optional.<V>of(e.getValue().get(30, TimeUnit.SECONDS)));
                    } catch (Exception ex) {
                        if (ex instanceof CompletionException || ex instanceof ExecutionException)
                            if (Stream.of(classes).anyMatch(clazz -> ex.getClass().isAssignableFrom(clazz))) {
                                return Tuple.tuple(e.getKey(), Optional.empty());
                            }
                        log.error("kafka future finish will error :" + e.getKey(), ex);
                        return Tuple.tuple(e.getKey(), Optional.empty());
//                        throw new RuntimeException(ex);
                    }
                }).filter(t -> t.v2().isPresent())
                .collect(toMap(t -> t.v1(), t -> (V)(t.v2().get())));
    }


    private Clusters source(Kafka sourceCr, com.shindata.kafka.v1.kafkaspec.Secret kafkaSecret, KafkaVO vo) {
        Clusters source = new Clusters();
        source.setAlias("source");
        // 连接source, 通过extip
//        final String masterIP = IPUtil.getIpFromUrl(kubeConfigService.get(sourceApp.getKubeId()).getServer());
        source.setBootstrapServers(
                sourceCr.getSpec().getIpList().stream()
                        .map(ipList -> ipList.getIp() + ":" + getKind().getDbPort())
                        .collect(Collectors.joining(",")));
//                sourceCr.getSpec().getIpList().stream()
//                .map(ipList -> masterIP + ":" + ipList.getPort())
//                .collect(Collectors.joining(",")));
        if (vo.getMm2Config() != null) {
            Map<String, IntOrString> config = parseMm2Config(vo, "target.");
            source.setConfig(config);
        }

        source.setSecurityRef(kafkaSecret);
        return source;
    }

    private Clusters target(Kafka targetCr, com.shindata.kafka.v1.kafkaspec.Secret kafkaSecret, KafkaVO vo) {
        Clusters target;
        target = new Clusters();
        target.setAlias("target");
        // 连接当前集群(podIp or dns or service)
        target.setBootstrapServers(
                targetCr.getSpec().getIpList().stream()
                        .map(ipList -> ipList.getIp() + ":" + getKind().getDbPort())
                        .collect(Collectors.joining(",")));
        target.setSecurityRef(kafkaSecret);
        // divide cluster and mirror config
        if (vo.getMm2Config() != null) {
            Map<String, IntOrString> config = parseMm2Config(vo, "target.");
            target.setConfig(config);
        }
        return target;
    }

    private IntOrString intOrString(String value) {
        if (NumberUtils.isDigits(value))
            return new IntOrString(Integer.valueOf(value));
        return new IntOrString(value);
    }

    @Override
    protected List<String> configNetwork(CloudAppVO vo) {
        KafkaVO kafkaVO = (KafkaVO) vo;
        List<String> ips = networkService.allocateIp(vo, vo.getMembers() + kafkaVO.getZkMembers(), getIpOwnerKind(), getIpReservationTarget()); // 覆盖父类方法，为zk节点配置ip
        vo.setIpList(JsonUtil.toJson(AppUtil.composeNodeNameAndIp(null, ips)));
        return ips;
    }

    @Override
    protected void createCrControlResource(Kafka cr, CloudAppVO vo) {
        if (StringUtils.isNotEmpty(vo.getPassword())) {
            KubeClient client = kubeClientService.get(vo.getKubeId());
            Map<String, String> secretData = new HashMap<>();
            secretData.put(SECRET_USERNAME_KEY, StringUtils.isEmpty(vo.getUsername()) ? DEFAULT_USER_ADMIN : vo.getUsername());
            secretData.put(SECRET_PASSWORD_KEY, vo.getPassword());
            client.createSecret(vo.getNamespace(), getSecretName(vo.getCrName()), null, secretData, cr);
            //用户名 落表
            dbUserService.createUser(vo.getId(), StringUtils.isEmpty(vo.getUsername()) ? DEFAULT_USER_ADMIN : vo.getUsername(), vo.getEncryptedPassword(), CloudAppConstant.UserRole.ADMIN);
        }
    }

//    private void ensureFilebeatCM(String namespace, KubeClient kubeClient, String name) {
//        // 获取k8s 客户端
//        // 获取预置的configMap模板
//        String configMapYaml = sysConfigService.findOne(CloudAppConstant.SysCfgCategory.OPERATOR_CONFIG, "kafka.config");
//        if (StringUtils.isEmpty(configMapYaml)) {
//            return;
//        }
//        // 解析yaml文件为map
//        Map<?, ?> properties = YamlEngine.unmarshal(configMapYaml);
//        // 如果机器上指定namespace上不存在configmap
//        String metadataName = ((Map<String,String>) properties.get("metadata")).get("name");
//        if (metadataName.contains("{")) // 解析自定义名称
//            metadataName = name;
//        if (null == kubeClient.getConfigMap( metadataName, namespace)) {
//            configMapYaml =  configMapYaml.replace("${Namespace}", namespace);
//            Map<String, String> param = new HashMap<>();
//            param.put("NAMESPACE", namespace);
//            param.put("NAME", name);
//            param.put("es_host", esUtil.getEsIp() + ":" + esUtil.getEsPort());
//            param.put("es_username", esUtil.getEsUsername());
//            param.put("es_pwd", esUtil.getEsPassword());
//            param.put("es_protocol", esUtil.getProtocol());
//            configMapYaml = YamlUtil.evaluateTemplate(configMapYaml, param);
//            kubeClient.applyYaml(configMapYaml, namespace);
//            log.info("create kafka-config at ns " + namespace);
//
//        }
//    }

    @Override
    public Class<? extends OpsPostProcessor> getProcessorClass(ActionEnum action) {
        switch (action) {
            case CREATE:
                return KafkaInstallWatch.class;
            case UPDATE:
            case MODIFY_PARAM:
                return KafkaWatch.class;
            case SCALE_OUT:
            case SCALE_IN:
                return KafkaScaleWatch.class;
            case UPDATE_PASSWORD:
                return KafkaModifySecret.class;
            case UPDATE_SERVICE:
            case DELETE_SERVICE:
            case CREATE_SERVICE:
                return KafkaServiceWatch.class;
            case SWITCH_AZ_ROLE:
                return KafkaSwitchAZRoleWatch.class;
            default:
                return super.getProcessorClass(action);
        }
    }

    @Override
    protected boolean supportIPAM(CloudAppVO app) {
        return true;
    }

    @Override
    public void update(Integer id, OverrideSpec overrideSpec) throws Exception{

        if (overrideSpec instanceof KafkaOverrideSpec) {
            KafkaResourceDTO patch = new KafkaResourceDTO();
            KafkaOverrideSpec updateSpec = (KafkaOverrideSpec) overrideSpec;
            patch.setDisk(overrideSpec.getDisk());
            patch.setMemory(overrideSpec.getMemory());
            patch.setCpu(overrideSpec.getCpu());
            patch.setBackupDisk(overrideSpec.getBackupDisk());
            patch.setZkCpu(updateSpec.getZkCpu());
            patch.setZkMemory(updateSpec.getZkMemory());
            patch.setZkDisk(updateSpec.getZkDisk());
            patch.setMm2Cpu(updateSpec.getMm2Cpu());
            patch.setMm2Memory(updateSpec.getMm2Memory());
            patch.setAppId(id);
            update(patch);
        }
    }

    @Override
    public void update(ResourceDTO patch) throws Exception {
        KafkaResourceDTO dto = (KafkaResourceDTO) patch;
        Consumer<Kafka> modifier = (current) -> {
            KafkaSpec spec = current.getSpec();
            spec.setCpu(patch.getCpu());
            spec.setMemory(patch.getMemory());
            Zookeeper zkSpec = spec.getZookeeper();
            zkSpec.setCpu(dto.getZkCpu());
            zkSpec.setMemory(dto.getZkMemory());
            Mm2 mm2 = spec.getMm2();
            if (mm2 != null) {
                mm2.setResources(ResourceHelper.getInstance().resourceRequirements(ImmutableMap.of("cpu", dto.getMm2Cpu(),
                        "memory", dto.getMm2Memory())));
            }
        };
        Consumer<Kafka> storageModifier = (current) -> {
            KafkaSpec spec = current.getSpec();
            if (MetricUtil.lessAndNotZero(spec.getStorage().getSize(), patch.getDisk()))
                spec.getStorage().setSize(patch.getDisk());
            if (MetricUtil.lessAndNotZero(spec.getZookeeper().getStorage().getSize(), dto.getZkDisk()))
                spec.getZookeeper().getStorage().setSize(dto.getZkDisk());
        };
        operationHandler.handleUpdate(patch, modifier, this, Kafka.class, storageModifier);
    }

    private void scaleOut(int appId, int totalMembers, int zkMembers) throws Exception {
        // 获取指定实例的详细信息
        CloudApp cloudApp = appService.get(appId);
        Integer kubeId = cloudApp.getKubeId();
        String serviceType = accessManagementService.usedServiceList(appId).get(0).getServiceType();

        // 计算需要新增的节点数量
        Kafka cr = YamlEngine.unmarshal(cloudApp.getCr(), Kafka.class);
        int deltaMembers = totalMembers - cloudApp.getMembers();
        int zkDeltaMembers = zkMembers > 0 ? zkMembers - cr
                .getSpec().getZookeeper().getIpList().size() : 0;

        TriFunction<CloudApp, List<String>, Kafka, Kafka> modifier = (app, scaledIPs, current) -> {
            List<ServiceManager> serviceManagerList = new ArrayList<>();
            List<IpList> currentIpList = current.getSpec().getIpList();

            // 生成新增成员所需的服务资源列表
            List serviceResourceList =
                    accessManagementService.genServiceResourceByAppKindMember(deltaMembers, getKind(), kubeId, serviceType);

            // 遍历新增成员，为每个成员分配 IP 和端口，并创建对应的服务管理器
            for (int i = 0; i < deltaMembers; i++) {
                String newIp = scaledIPs.get(i);
                IpList newIpList = new IpList();
                newIpList.setIp(newIp);

                // 初始化 ServiceManager 对象，redis ha 只有一个写 service
                ServiceManager serviceManager = new ServiceManager();
                serviceManager.setPurpose(CloudAppConstant.ServicePurpose.WRITE);
                serviceManager.setServiceName(getKind().getWriteServiceName(app.getCrName(), newIp));

                if (serviceType.equals(CloudAppConstant.ServiceType.NODE_PORT)) {
                    // 如果服务类型为 NodePort，则分配节点端口
                    int nodePort = (Integer) serviceResourceList.get(i);
                    newIpList.setPort(nodePort);
                    serviceManager.setPort(nodePort);
                } else if (serviceType.equals(CloudAppConstant.ServiceType.LOAD_BALANCER)) {
                    // 如果服务类型为 LoadBalancer，则分配负载均衡 IP
                    String lbip = String.valueOf(serviceResourceList.get(i));
                    newIpList.setExternalIP(lbip);
                    newIpList.setPort(getKind().getDbPort());
                    serviceManager.setExternalIp(lbip);
                }

                // 将新生成的服务管理器和 IP 列表添加到对应的集合中
                serviceManagerList.add(serviceManager);
                currentIpList.add(newIpList);
            }
            if (zkDeltaMembers > 0) {
                current.getSpec().getZookeeper().getIpList().addAll(scaledIPs.subList(deltaMembers, scaledIPs.size()));
            }
            // 根据服务类型记录操作日志
            if (serviceType.equals(CloudAppConstant.ServiceType.NODE_PORT)) {
                OpLogContext.instance().PORT(serviceResourceList);
            } else if (serviceType.equals(CloudAppConstant.ServiceType.LOAD_BALANCER)) {
                OpLogContext.instance().LBIP(serviceResourceList);
            }

            // 插入新生成的服务资源到访问管理服务中
            accessManagementService.initServices(app, serviceManagerList, serviceType);

            return current;
        };

        // 调用操作处理器完成扩展操作
        operationHandler.handleScaleUp(appId, deltaMembers + zkDeltaMembers, Kafka.class,
                this, getIpOwnerKind(), getIpReservationTarget(), modifier);
    }

    @Override
    public void scale(int appId, OverrideSpec spec, ActionEnum action) throws Exception {
        KafkaOverrideSpec vo = (KafkaOverrideSpec) spec;

        if (action == ActionEnum.SCALE_IN) {
            scaleDown(appId, Arrays.asList(vo.getIpNodes()), vo.getZkIpNodes());
        }
        if (action == ActionEnum.SCALE_OUT) {
//            scaleOut(id, vo.getMembers(), vo.getManualPort(), vo.getPorts());
            scaleOut(appId, vo.getMembers(), vo.getZkMembers());
        }
    }

    public void scaleDown(int appId, List<CloudApp.IpNode> scaled, List<CloudApp.IpNode> zkScaled) throws Exception {
        HashMap<String, Object> map = new HashMap<>();

        TriFunction<CloudApp, List<String>, Kafka, Kafka> modifider = (app, scaledIPs, current) -> {
            Kafka cr = YamlEngine.unmarshal(app.getCr(), Kafka.class);
            if (zkScaled != null)
            current.getSpec().getZookeeper().getIpList().removeIf(
                    ip -> zkScaled.stream().anyMatch(in -> in.getIp().equals(ip))
            );
            current.getSpec().getIpList().removeIf(
                    ipList -> scaledIPs.contains(ipList.getIp()));
            cr.setSpec(current.getSpec());
            //挑选出要释放的 svm
            List<ServiceManager> serviceManagerList = accessManagementService.usedServiceList(appId);
            Set<String> serviceNameSet = scaledIPs.stream()
                    .map(ip -> getKind().getWriteServiceName(app.getCrName(), ip)).collect(Collectors.toSet());
            // 和要缩容 ip 的 ServiceName 进行对比，不是要缩容的则移除
            serviceManagerList.removeIf(serviceManager -> !serviceNameSet.contains(serviceManager.getServiceName()));
            map.put("scaleDownServiceManagerList", JsonUtil.toJson(serviceManagerList));
            return cr;
        };

        operationHandler.handleScaleDown(
                appId, scaled, modifider, Kafka.class, getProcessorClass(ActionEnum.SCALE_IN), map);
    }

    @Override
    public String getIpReservationTarget() {
        return "pod";
    }


    @Override
    public PageInfo<? extends CloudAppVO> searchPage(PageDTO page) {
        PageInfo<? extends CloudAppVO> pageInfo = super.searchPage(page);
        // 查询所有zookeeper pod, 找到与zookeepers属性中的ip对应的pod, 获取pod label - app.kubernetes.io/name
        if (pageInfo.getList().isEmpty()) return pageInfo;
        return PageUtil.page2PageInfo(pageInfo.getList(), KafkaVO.class, (vo -> {
            KafkaVO kafkaVO = getMapper().toVo(reviewSpec(vo));
            BeanUtils.copyProperties(vo, kafkaVO);
            return kafkaVO;
        }));
    }

    private Map<String, PodDTO> getZkPodMap(Map<Integer, Map<String, PodDTO>> zkKubeMap, Integer kubeId) {
        zkKubeMap.putIfAbsent(kubeId, clientService.get(kubeId).listPod(null, new Label(CloudAppConstant.CustomLabels.APP, "zookeeper"))
                .stream().filter(p -> p.getPodIp() != null).collect(toMap(p -> p.getPodIp(), p -> p)));
        return zkKubeMap.get(kubeId);
    }

    /**
     * 记录所用端口信息
     * @param app
     * @return
     */
    @Transactional
    public int occupyPort(CloudApp app, Integer port) {
        NodePortInfo nodePortInfo = new NodePortInfo();
        nodePortInfo.setKubeId(app.getKubeId());
        nodePortInfo.setPort(port);
        nodePortInfo.setPortType(CloudAppConstant.NodePortType.SERVICE_PORT);
        nodePortInfo.setStatus(CloudAppConstant.NodePortStatus.USED);
        nodePortInfo.setAppId(app.getId());
        //记录写端口
        int n = 0;
        if (app.getWritePort() != null) {
            n += nodePortInfoMapper.updatePortStatus(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_NODEPORT_USED_INFO, nodePortInfo);
            log.debug("[port mgr] update " + nodePortInfo.getPort() + " to be used");
        }
        return n;
    }


    private String getSecretName(String crName) {
        return "kafka-" + crName + "-secret";
    }
    private String getZKSecretName(String crName) {
        return "zk-" + crName + "-secret";
    }

    @Override
    public CloudAppVO overrideSpec(CloudAppLogic logicApp, Integer kubeId, InstallAppVo<? extends OverrideSpec> vo) {
        CloudAppVO appVO = super.overrideSpec(logicApp, kubeId, vo);
        KafkaVO kafkaVO = new KafkaVO();
        BeanUtil.copyNonNullProperties(appVO, kafkaVO);
        if (vo.getOverrideSpecs().get(kubeId) instanceof KafkaOverrideSpec) {
            KafkaOverrideSpec overrideSpec = (KafkaOverrideSpec) vo.getOverrideSpecs().get(kubeId);
            kafkaVO.setZkCpu(overrideSpec.getZkCpu());
            kafkaVO.setZkMemory(overrideSpec.getZkMemory());
            kafkaVO.setZkDisk(overrideSpec.getZkDisk());
            kafkaVO.setZkMembers(overrideSpec.getZkMembers());
            kafkaVO.setZkUsername(overrideSpec.getZkUsername());
            kafkaVO.setZkPassword(overrideSpec.getZkPassword());
            kafkaVO.setZkIpNodes(overrideSpec.getZkIpNodes());
            kafkaVO.setZkDbParamTemplateId(overrideSpec.zkDbParamTemplateId);
            kafkaVO.setZkTemplateTmpParam(overrideSpec.zkTemplateTmpParam);

            kafkaVO.setMm2Members(overrideSpec.getMm2Members());
            kafkaVO.setMm2Cpu(overrideSpec.getMm2Cpu());
            kafkaVO.setMm2Memory(overrideSpec.getMm2Memory());
            kafkaVO.setMm2Config(overrideSpec.getMm2Config());
        }
        return kafkaVO;
    }

    @Override
    public KafkaOverrideSpec reviewSpec(CloudApp app) {
        OverrideSpec overrideSpec = super.reviewSpec(app);
        KafkaOverrideSpec kafkaSpec = new KafkaOverrideSpec();
        BeanUtils.copyProperties(overrideSpec, kafkaSpec);
        try {
            String crYaml = StringUtils.isEmpty(app.getCr()) ? app.getCrRun() : app.getCr();
            Kafka cr = YamlEngine.unmarshal(crYaml, Kafka.class);
            Zookeeper zookeeper = cr.getSpec().getZookeeper();
            kafkaSpec.setZkCpu(zookeeper.getCpu());
            kafkaSpec.setZkMemory(zookeeper.getMemory());
            kafkaSpec.setZkDisk(zookeeper.getStorage().getSize());
            kafkaSpec.setZkMembers(zookeeper.getIpList().size());
            kafkaSpec.setZkIpNodes(zookeeper.getIpList().stream()
                    .map(ip->new CloudApp.IpNode(null, ip)).collect(Collectors.toList()));
//            kafkaSpec.setZkUsername(overrideSpec.getZkUsername());
//            kafkaSpec.setZkPassword(overrideSpec.getZkPassword());

            Mm2 mm2 = cr.getSpec().getMm2();
            if (mm2 != null) {
                kafkaSpec.setMm2Cpu(mm2.getResources().getLimits().get("cpu").toString());
                kafkaSpec.setMm2Memory(mm2.getResources().getLimits().get("memory").toString());
                kafkaSpec.setMm2Members(mm2.getReplicas());
            }

        } catch (Exception e) {
            log.error("", e);
        }
        return kafkaSpec;
    }


    @Override
    public InstallAppVo<? extends OverrideSpec> parseInstallVo(String data) {
        InstallAppVo<KafkaOverrideSpec> vo = JsonUtil.toObject(data, new TypeReference<InstallAppVo<KafkaOverrideSpec>>() {
        });
        // 选择集群组安装kafka时, zk的集群即kafka将要安装到的集群
        if (vo != null && vo.getSpec() != null && vo.unscheduled()) {
            CloudApp zk = appService.getAppByCrName(vo.getName(), vo.getNamespace(), AppKind.Zookeeper);
        }
        return vo;
    }

    @Override
    public List<ServiceManager> createService(
            String serviceType, CloudAppVO vo, List<?> serviceResources, CustomResource installCr) {
        //1.校验
        AppKind kind = getKind();
        if (serviceResources.isEmpty()) return Collections.emptyList();
        int serviceManagerNum = kind.getServiceManagerNum(serviceType, vo.getMembers());
        if (CollectionUtils.isEmpty(serviceResources) || serviceResources.size() != serviceManagerNum) {
            throw new CustomException(600, "节点端口类型必须指定 " + serviceManagerNum
                    + " 个端口, 实际数量为 " + serviceResources.size() + ", 类型为 " + serviceType);
        }

        //2 声明构建所需的变量
        List<ServiceManager> svms = new ArrayList<>();
        Kafka cr = (Kafka) installCr;
        List<IpList> ipLists = cr.getSpec().getIpList();

        for (int i = 0; i < serviceResources.size(); i++) {
            Object serviceResource = serviceResources.get(i);
            //2. 固定ip实例类型为iplist中设置,再 set 到 ServiceManager 中
            // 2.1 获取serviceName
            IpList ipList = ipLists.get(i);
            String serviceName = getKind().getWriteServiceName(vo.getCrName(), ipList.getIp());
            Integer nodePort;
            int dbPort = getKind().getDbPort();

            //2.3 初始化 ServiceManager
            ServiceManager serviceManager = new ServiceManager();
            // 没有读写分离，且只需要一个 service
            serviceManager.setPurpose(CloudAppConstant.ServicePurpose.WRITE);
            serviceManager.setServiceName(serviceName);
            serviceManager.setServiceType(serviceType);

            //2.4 根据 ServiceType 填充 NodePort 和 lb
            if (CloudAppConstant.ServiceType.NODE_PORT.equals(serviceType)) {
                //nodeport 方式，serviceResources 结构为 List<Integer>，进行分配 NodePort，只需要一个 NodePort
                nodePort = (Integer) serviceResource;
                serviceManager.setPort(nodePort);
                ipList.setPort(nodePort);
            } else if (CloudAppConstant.ServiceType.LOAD_BALANCER.equals(serviceType)) {
                //lb 方式，serviceResources 结构为 List<String>，进行分配 lbip，只需要一个 lbip，端口为 dbport
                serviceManager.setPort(dbPort);
                String lbip = (String) serviceResource;
                serviceManager.setExternalIp(lbip);
                ipList.setExternalIP(lbip);
            }
            svms.add(serviceManager);
        }

        return svms;
    }

    @Override
    public void updateService(List<ServiceManager> svcMgrs, CloudApp app, Object oldServiceResource) throws Exception {
        ServiceManager serviceManager = svcMgrs.get(0);
        String serviceType = serviceManager.getServiceType();
        Map<String, String> data = new HashMap<>();
        data.put("oldServiceResource", oldServiceResource + "");
        Consumer<Kafka> modifier = kafka -> {
            List<com.shindata.kafka.v1.kafkaspec.IpList> ipList = kafka.getSpec().getIpList();
            ipList.forEach(ipListConfig -> {
                if (serviceType.equalsIgnoreCase(CloudAppConstant.ServiceType.NODE_PORT)) {
                    if (ipListConfig.getPort().equals(oldServiceResource)) {
                        ipListConfig.setPort(serviceManager.getPort());
                    }
                } else if (serviceType.equalsIgnoreCase(CloudAppConstant.ServiceType.LOAD_BALANCER)) {
                    if (ipListConfig.getExternalIP().equals(oldServiceResource)) {
                        ipListConfig.setExternalIP(serviceManager.getExternalIp());
                    }
                }
            });
        };
        operationHandler.handleService(
                app, svcMgrs, modifier, Kafka.class, this, data, ActionEnum.UPDATE_SERVICE);
    }

    @Getter
    @Setter
    @ToString
    public static class KafkaOverrideSpec extends OverrideSpec {
        // kafka关联zookeeper
        private String zkCpu;
        private String zkMemory;
        private String zkDisk;
        private String zkPassword;
        private String zkUsername;
        private int zkMembers;
        private List<CloudApp.IpNode> zkIpNodes;
        private Integer zkDbParamTemplateId;
        private Map<String, String> zkTemplateTmpParam;

        private int mm2Members = 3;
        private String mm2Cpu;
        private String mm2Memory;
        // mm2Config use prefix to contain different category of configs
        // for mirror config start with 'mirror.'
        // for cluster config start with 'cluster.'
        private Map<String, String> mm2Config;
    }

    @Override
    public void modifyConfigParam(Map<String, String> params, Integer appId, String componentKind) throws Exception {

        CustPreconditions.checkState(!CollectionUtils.isEmpty(params), "要修改的参数列表为空");

        CloudApp app = appService.get(appId);
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        Kafka k8sCr = kubeClient.listCustomResource(Kafka.class, app.getCrName(), app.getNamespace());
        Kafka cr = YamlEngine.unmarshal(app.getCr(), Kafka.class);
        Map<String, String> config = k8sCr.getSpec().getConfig();

        if (config == null) {
            config = new HashMap<>();
        }
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String key = entry.getKey();
            String val = entry.getValue();
            this.prohibitParam(key);
            config.put(key, val);
        }

        k8sCr.getSpec().setConfig(config);
        cr.setSpec(k8sCr.getSpec());

        HashMap<Object, Object> map = new HashMap<>();
        map.put("params", params);
        map.put("appId", appId);

        String applyYaml = YamlEngine.marshal(cr);
        OpLogContext.instance().CR(applyYaml, app.getCr());
        appService.callScheduler(app, applyYaml, map, ActionEnum.MODIFY_PARAM, getProcessorClass(ActionEnum.MODIFY_PARAM));

        kubeClient.updateCustomResource(k8sCr, Kafka.class);

    }

    @Override
    public void updatePassword(Password password, int appId) throws Exception {
        CloudApp app = appService.get(appId);
        if (null == app) {
            log.info("未查询到相应的应用信息，appid为：" + appId);
            throw new CustomException(600, "未查询到相应的应用信息");
        }

        //对密码进行解密
        String decrypt = decryptPassword(password.getNewPassword());
        password.setNewPassword(decrypt);
        //去除旧密码校验
/*        if(!StringUtils.isBlank(password.getOldPassword())){
            String oldDecrypt = rsaUtil.decrypt(password.getOldPassword());
            password.setOldPassword(oldDecrypt);
        }*/
        String failMsg = "当前数据库未设置用户名和密码";
        //查询相关用户名和密码操作
        List<CloudDatabaseUser> dbUsers = dbUserService.findDbUser(appId, CloudAppConstant.UserRole.ADMIN);
        if (CollectionUtils.isEmpty(dbUsers)) {
            log.info(failMsg);
            throw new CustomException(600, failMsg);
        }

        String passwordKey = DigestUtils.md5Hex(password.getNewPassword());
        BiConsumer<Secret, Kafka> update = (newsecret, currentCr) -> {
//            com.shindata.kafka.v1.kafkaspec.Secret secret = Optional.ofNullable(currentCr.getSpec().getSecret()).orElse(new com.shindata.kafka.v1.kafkaspec.Secret());
            //初始没有进行设置用户名和密码时，修改密码直接 报错
            com.shindata.kafka.v1.kafkaspec.Secret secret = Optional.ofNullable(currentCr.getSpec().getSecret()).orElseThrow(() -> new CustomException(600, failMsg));
            secret.setPasswordKey(passwordKey);
            secret.setSecretName(newsecret.getMetadata().getName());
            currentCr.getSpec().setSecret(secret);
            Mm2 mm2 = currentCr.getSpec().getMm2();
            if (mm2 != null) {
                mm2.getClusters().stream()
                        .forEach(c -> {
                            c.setSecurityRef(secret);
                        });
            }
        };
        operationHandler.handleUpdatePassword(appId, password, false, passwordKey, this, Kafka.class, update,
                cr -> getSecretName(cr.getMetadata().getName()),
                cr -> cr.getSpec().getSecret().getPasswordKey());
    }

    /**
     * 获取kafka的主题列表
     *
     * @param logicid
     */
    public List<KafkaTopicDetailVO> listTopic(Integer logicid) {
        List<CloudApp> appList = appService.getAppByLogicId(logicid);

        long l = System.currentTimeMillis();

        //获取client
        AdminClient client = getKafkaClient(appList.get(0), basicEnvBuildKafkaServer(appList));

        System.out.println("构造client所需时间" + (System.currentTimeMillis() - l));

        //查询topic
        ListTopicsResult topics = client.listTopics();
        try {
            Set<String> names = topics.names().get();

            System.out.println("查询主题列表名称使用时间" + (System.currentTimeMillis() - l));

            Map<String, TopicDescription> map = new TreeMap<>(client.describeTopics(names).all().get());

            System.out.println("查询主题列表使用时间" + (System.currentTimeMillis() - l));

            return map.entrySet().stream().map(each -> buildKafkaTopicMap(each)).collect(Collectors.toList());
        } catch (Exception e) {
            throw new CustomException(600, "原因: " + e.getMessage());
        } finally {
            ClientCloser.doFinal(client::close);
        }
    }

    /**
     * 创建kafka 主题
     *
     */
    public void createTopic(KafkaTopicDetailVO detail) {
        List<CloudApp> appList = appService.getAppByLogicId(detail.getLogicid());
        String kafkaBootStrap = basicEnvBuildKafkaServer(appList);
        if (detail.getReplicationFactor() > kafkaBootStrap.split(",").length)
            throw new CustomException(600, "创建主题失败，副本数量" + detail.getReplicationFactor() + "大于broker数量" + kafkaBootStrap.split(",").length);

        ResourceChangeHis resourceChangeHis = appService.getResourceChangeHis(appList.get(0), ActionEnum.CREATE_TOPIC, StatusConstant.RUNNING, "", null);
        resourceChangeHisService.add(resourceChangeHis);
        AdminClient client = null;
        try {
            //获取client
            client = getKafkaClient(appList.get(0), kafkaBootStrap);;

            NewTopic newTopic = new NewTopic(detail.getName(), detail.getPartitions(), (short) detail.getReplicationFactor());

            Map<String, String> configs = new HashMap<>();
            configs.put(TopicConfig.RETENTION_MS_CONFIG, String.valueOf(detail.getRetentionMs()));
            configs.put(TopicConfig.RETENTION_BYTES_CONFIG, String.valueOf(detail.getRetentionBytes()));

            newTopic.configs(configs);

            CreateTopicsResult res = client.createTopics(Collections.singletonList(newTopic));

            res.all().get();
            updateResourceChangeHis(resourceChangeHis, StatusConstant.SUCCESS, "创建Kafka" + detail.getName() + "主题成功");
        } catch (Exception e) {
            updateResourceChangeHis(resourceChangeHis, StatusConstant.FAIL, e.getMessage());
            throw new CustomException(600, "创建主题失败" + e.getMessage());
        } finally {
            ClientCloser.doFinal(client::close);
        }
    }

    /**
     * 删除主题
     */
    public void deleteTopic(Integer logicid, String topicName) {

        List<CloudApp> appList = appService.getAppByLogicId(logicid);

        ResourceChangeHis resourceChangeHis = appService.getResourceChangeHis(appList.get(0), ActionEnum.DELETE_TOPIC, StatusConstant.RUNNING, "", null);
        resourceChangeHisService.add(resourceChangeHis);
        AdminClient client = null;

        try {
            //获取client
            client = getKafkaClient(appList.get(0), basicEnvBuildKafkaServer(appList));
            DeleteTopicsResult res = client.deleteTopics(Collections.singletonList(topicName));
            res.all().get();

            updateResourceChangeHis(resourceChangeHis, StatusConstant.SUCCESS, "删除Kafka主题(" + topicName + ")成功");
        } catch (Exception e) {
            updateResourceChangeHis(resourceChangeHis, StatusConstant.FAIL, e.getMessage());
            throw new CustomException(600, "删除主题失败" + e.getMessage());
        } finally {
            ClientCloser.doFinal(client::close);
        }
    }

    /**
     * 查看主题详情
     *
     * @return
     */
    public KafkaTopicDetailVO getTopicDetail(Integer logicid, String topicName) {

        List<CloudApp> appList = appService.getAppByLogicId(logicid);

        //获取client
        AdminClient client = getKafkaClient(appList.get(0), basicEnvBuildKafkaServer(appList));
        DescribeTopicsResult descRes = client.describeTopics(Collections.singletonList(topicName));

        try {

            Map<String, TopicDescription> descMap = descRes.all().get();
            TopicDescription desc = descMap.get(topicName);

            KafkaTopicDetailVO res = new KafkaTopicDetailVO();
            res.setName(topicName);
            res.setPartitions(desc.partitions().size());
            res.setReplicationFactor(desc.partitions().get(0).replicas().size());

            List<ConfigResource> resources = new ArrayList<>();
            ConfigResource resource = new ConfigResource(ConfigResource.Type.TOPIC, topicName);
            resources.add(resource);
            DescribeConfigsResult confDescRes = client.describeConfigs(resources);

            Map<ConfigResource, Config> confMap = confDescRes.all().get();
            Config config = confMap.get(resource);

            res.setRetentionMs(Long.parseLong(config.get(TopicConfig.RETENTION_MS_CONFIG).value()));
            res.setRetentionBytes(Long.parseLong(config.get(TopicConfig.RETENTION_BYTES_CONFIG).value()));

            return res;
        } catch (Exception e) {
            throw new CustomException(600, "原因: " + e.getMessage());
        } finally {
            ClientCloser.doFinal(client::close);
        }
    }

    /**
     * 更新主题
     * @param detail
     */
    public void alterTopic(KafkaTopicDetailVO detail) {
        List<CloudApp> appList = appService.getAppByLogicId(detail.getLogicid());
        ResourceChangeHis resourceChangeHis = appService.getResourceChangeHis(appList.get(0), ActionEnum.UPDATE_TOPIC, StatusConstant.RUNNING, "", null);
        resourceChangeHisService.add(resourceChangeHis);

        AdminClient client = null;

        try {
            //获取client
            client = getKafkaClient(appList.get(0), basicEnvBuildKafkaServer(appList));

            // 再改分区数
            if ("partition".equals(detail.getAlterType())) {
                NewPartitions np = NewPartitions.increaseTo(detail.getPartitions());
                Map<String, NewPartitions> partitionsMap = new HashMap<>();
                partitionsMap.put(detail.getName(), np);
                CreatePartitionsResult partitions = client.createPartitions(partitionsMap);

                partitions.all().get();
                updateResourceChangeHis(resourceChangeHis, StatusConstant.SUCCESS, "更新Kafka主题(" + detail.getName() + ")成功");
            } else if ("config".equals(detail.getAlterType())) {
                //修改其他配置
                Map<ConfigResource, Config> configMap = new HashMap<>();

                ConfigResource resource = new ConfigResource(ConfigResource.Type.TOPIC, detail.getName());
                Config config = new Config(Arrays.asList(new ConfigEntry(TopicConfig.RETENTION_MS_CONFIG, detail.getRetentionMs() + ""),
                        new ConfigEntry(TopicConfig.RETENTION_BYTES_CONFIG, detail.getRetentionBytes() + "")
                ));
                configMap.put(resource, config);

                AlterConfigsResult res = client.alterConfigs(configMap);
                res.all().get();
                updateResourceChangeHis(resourceChangeHis, StatusConstant.SUCCESS, "更新Kafka主题(" + detail.getName() + ")成功");
            } else {
                updateResourceChangeHis(resourceChangeHis, StatusConstant.FAIL, detail.getAlterType() + "不识别");
                throw new CustomException(600, "alter type " + detail.getAlterType() + "不识别");
            }
        } catch (Exception e) {
            updateResourceChangeHis(resourceChangeHis, StatusConstant.FAIL, "修改主题失败，原因：" + e.getMessage());
            throw new CustomException(600, "修改主题失败，原因: " + e.getMessage());
        } finally {
            ClientCloser.doFinal(client::close);
        }
    }

    public List<Map<String, Object>> getVariable(Integer logicid) {
        List<CloudApp> appList = appService.getAppByLogicId(logicid);
        AdminClient client = null;
        try {
            //获取client
            client = getKafkaClient(appList.get(0), basicEnvBuildKafkaServer(appList));
            //查询出broker信息
            Collection<org.apache.kafka.common.Node> nodes = client.describeCluster().nodes().get();
            if (CollectionUtils.isEmpty(nodes))
                throw new CustomException(600,"未查询到相关的kafka信息");

            Integer brokerId = nodes.stream().findFirst().map(org.apache.kafka.common.Node::id).get();
            //根据broker信息查询kafka配置信息
            ConfigResource configResource = new ConfigResource(ConfigResource.Type.BROKER, String.valueOf(brokerId));
            DescribeConfigsResult describeConfigsResult = client.describeConfigs(Collections.singleton(configResource));
            Map<ConfigResource, Config> configResourceConfigMap = describeConfigsResult.all().get();

            //查询到broker的配置信息
            return configResourceConfigMap.values().stream()
                    .flatMap(config -> config.entries().stream())
                    .map(entry -> {
                        Map<String, Object> entryMap = new HashMap();
                        entryMap.put("name", entry.name());
                        entryMap.put("value", entry.value());
                        entryMap.put("isDefault", entry.isDefault());
                        entryMap.put("isSensitive", entry.isSensitive());
                        entryMap.put("isReadOnly", entry.isReadOnly());
                        return entryMap;
                    }).collect(Collectors.toList());
        } catch (Exception e) {
            throw new CustomException(600, "查询kafka的配置信息失败" + e.getMessage());
        }finally {
            ClientCloser.doFinal(client::close);
        }
    }

    /**
     * 根据基本信息构建Kafka client
     *
     * @return
     */
    private String basicEnvBuildKafkaServer(List<CloudApp> appList) {
        String bootstrapServerStr = "";
        for (CloudApp app : appList) {
            String currentAppBootstrapServerStr = accessManagementService
                    .getWriteServiceAddressByAppIdAndKubeId(app.getId(), app.getKubeId())
                    .stream().map(appDBVO -> appDBVO.getIp() + ":" + appDBVO.getPort())
                    .collect(Collectors.joining(","));
            bootstrapServerStr = bootstrapServerStr + "," + currentAppBootstrapServerStr;
        }
        log.debug("[Kafka] 获取 bootstrapServers:{}", bootstrapServerStr);

        return bootstrapServerStr.substring(1);
    }

    /**
     * 根据用户名和密码获取相应的 client
     *
     * @param app
     * @param bootstrapServerStr
     * @return
     */
    private AdminClient getKafkaClient(CloudApp app, String bootstrapServerStr) {
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        String username = "";
        String password = "";
        //获取用户名和密码
        List<CloudDatabaseUser> dbUsers = dbUserService.findDbUser(app.getId(), CloudAppConstant.UserRole.ADMIN);

        if (!CollectionUtils.isEmpty(dbUsers)) {
            //查询相关的secret
            String secretName = getSecretName(app.getCrName());

            //从cr中拿到相关的secret的passwordkey
            Kafka kafka = kubeClient.listCustomResource(Kafka.class, app.getCrName(), app.getNamespace());
            if (null == kafka)
                throw new CustomException(600, "未查询到相关的kafka信息");

            Secret secret = kubeClient.getSecret(app.getNamespace(), secretName);
            username = new String(Base64.getDecoder().decode(secret.getData().get(SECRET_USERNAME_KEY).getBytes()));
            password = new String(Base64.getDecoder().decode(secret.getData().get(kafka.getSpec().getSecret().getPasswordKey()).getBytes()));
        }
        // 构建client
        AdminClient client = KafkaUtil.getClient(bootstrapServerStr, username, password);
        return client;
    }

    /**
     * 构造kafka查询topic返回数据
     * @param each
     * @return
     */
    private KafkaTopicDetailVO buildKafkaTopicMap(Map.Entry<String, TopicDescription> each) {
        KafkaTopicDetailVO kafkaTopicDetailVO = new KafkaTopicDetailVO();
        kafkaTopicDetailVO.setName(each.getKey());
        kafkaTopicDetailVO.setPartitions(each.getValue().partitions().size());
        kafkaTopicDetailVO.setReplicationFactor(each.getValue().partitions().get(0).replicas().size());
        return kafkaTopicDetailVO;
    }

    private void createMM2Config(Kafka target, Kafka source) {
    }

    @Override
    public void delete(CloudApp app) {
        super.delete(app);
        KubeClient client = clientService.get(app.getKubeId());
        client.scaleSts(String.format("kafka-%s-mm2", app.getCrName()), app.getNamespace(), 0);
    }

    @Data
    public static class KafkaVO extends CloudAppVO {
        private String zkCpu;
        private String zkMemory;
        private String zkDisk;
        private String zkPassword;
        private String zkUsername;
        private int zkMembers;
        private List<CloudApp.IpNode> zkIpNodes;
        private Integer zkDbParamTemplateId;
        private Map<String, String> zkTemplateTmpParam;

        private int mm2Members = 3; // default 3
        private String mm2Cpu;
        private String mm2Memory;
        private Map<String, String> mm2Config;
    }

    @Data @AllArgsConstructor
    public static class Offsets {
        Long earliest;
        Long latest;
    }
    @Data
    public static class KafkaResourceDTO extends ResourceDTO{
        private String zkCpu;
        private String zkMemory;
        private String zkDisk;

        private String mm2Cpu;
        private String mm2Memory;
    }

    public void updateStandby() {
        CloudApp current = null;

    }
}
