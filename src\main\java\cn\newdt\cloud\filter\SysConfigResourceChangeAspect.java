package cn.newdt.cloud.filter;

import cn.newdt.cloud.config.CloudRequestContext;
import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.CloudResourceKindEnum;
import cn.newdt.cloud.domain.CloudSysConfig;
import cn.newdt.cloud.domain.ResourceChangeHis;
import cn.newdt.cloud.service.KubeGroupService;
import cn.newdt.cloud.service.ResourceChangeHisService;
import cn.newdt.commons.bean.UserInfo;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.ActionEnum.MODIFY_SYSCONFIG;
import static cn.newdt.cloud.constant.StatusConstant.FAIL;
import static cn.newdt.cloud.constant.StatusConstant.SUCCESS;

@Aspect
@Component
@Slf4j
public class SysConfigResourceChangeAspect {

    @Autowired
    private ResourceChangeHisService resourceChangeHisService;
    @Autowired
    private KubeGroupService kubeGroupService;

    @Pointcut("@annotation(cn.newdt.cloud.filter.ResourceChangeLog)")
    public void pointcut() {
    }

    @Around("pointcut() && @annotation(resourceChangeLog)")
    public Object aroundAdvise(ProceedingJoinPoint jp, ResourceChangeLog resourceChangeLog) throws Throwable {
        ActionEnum action = resourceChangeLog.action();
        if (action == MODIFY_SYSCONFIG) {
            return modifySysConfigAspect(jp);
        }
        return jp.proceed();
    }

    private Object modifySysConfigAspect(ProceedingJoinPoint jp) {
        List<CloudSysConfig> cscList = (ArrayList<CloudSysConfig>) jp.getArgs()[0];
        CloudSysConfig csc = cscList.get(0);
        String finUpdateCscName = csc.getName();
        List<String> cscs = cscList.stream().filter(nowcsc -> nowcsc.isModified()).map(cscobj -> cscobj.getName()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(cscs)) {
            finUpdateCscName = cscs.toString().replace("[", "").replace("]", "");
        }
        ResourceChangeHis rch = getResourceChangeHis(csc.getId(), finUpdateCscName, MODIFY_SYSCONFIG);
        Object proceed;
        try {
            proceed = jp.proceed();
            rch.setStatus(SUCCESS);
            rch.setMsg("修改系统配置成功");
            rch.setUpdateTime(new Timestamp(new Date().getTime()));
            this.resourceChangeHisService.add(rch);
        } catch (Throwable e) {
            rch.setStatus(FAIL);
            rch.setMsg(String.format("修改系统配置失败, 失败信息:%s", e.getMessage()));
            rch.setUpdateTime(new Timestamp(new Date().getTime()));
            this.resourceChangeHisService.add(rch);
            throw new CustomException(600, e.getMessage());
        }
        return proceed;
    }

    private ResourceChangeHis getResourceChangeHis(Integer id, String name, ActionEnum action) {
        ResourceChangeHis rch = new ResourceChangeHis();
        rch.setAction(action.getActionType());
        rch.setCommand(action.getAppOperation());
        rch.setKind(CloudResourceKindEnum.SYS_CONFIG.getKey());
        rch.setAppLogicId(0); // 无效值，仅用于数据库非空校验
        rch.setInsertTime(new Timestamp(new Date().getTime()));
        rch.setUpdateTime(new Timestamp(new Date().getTime()));
        UserInfo currentUser = UserUtil.getCurrentUser();
        rch.setUserId(currentUser.getUserid());
        rch.setUserName(currentUser.getUsername());
        rch.setUserIp(CloudRequestContext.getContext().getUserIp());
        rch.setAppName(name);
        if (id != null && name == null) {
            rch.setAppName(kubeGroupService.selectById(id).getName());
        }
        return rch;
    }


}
