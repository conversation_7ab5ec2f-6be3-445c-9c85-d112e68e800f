package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.common.OpLogContext;
import cn.newdt.cloud.config.CloudRequestContext;
import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.domain.cr.MongoDBCluster;
import cn.newdt.cloud.domain.cr.MongoDBCommunity;
import cn.newdt.cloud.dto.Label;
import cn.newdt.cloud.dto.MySQLParamTemplateDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.dto.ResourceDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.cni.BocCniHelperFactory;
import cn.newdt.cloud.service.cni.BocRestfulHelper;
import cn.newdt.cloud.service.cni.CniConfig;
import cn.newdt.cloud.service.csi.CSILoader;
import cn.newdt.cloud.service.csi.CSIUtil;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.service.sched.impl.*;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.AppInstanceVO;
import cn.newdt.cloud.vo.CloudAppVO;
import cn.newdt.cloud.vo.CloudBackupStorageVO;
import cn.newdt.cloud.vo.OverrideSpec;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.bean.MetaVO;
import cn.newdt.commons.bean.MysqlParamRules;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.JsonMapper;
import cn.newdt.commons.utils.SymmetricEncryptionUtil;
import cn.newdt.commons.utils.UserUtil;
import com.alibaba.fastjson.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.mongodb.MongoClient;
import com.mongodb.client.MongoDatabase;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.api.model.rbac.Role;
import io.fabric8.kubernetes.client.CustomResource;
import io.fabric8.kubernetes.client.utils.Serialization;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.bson.Document;
import org.bson.json.Converter;
import org.bson.json.JsonWriterSettings;
import org.bson.json.StrictJsonWriter;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgCategory.*;
import static cn.newdt.cloud.domain.cr.MongoDBCommunity.*;
import static cn.newdt.cloud.utils.DateUtil.parseUTC;
import static java.util.stream.Collectors.toMap;

@Slf4j
@Service
public class MongoDbService extends DefaultAppKindService<MongoDBCommunity> implements ServiceManageOperation{

    public static final String SECRET_PASSWORD_KEY = "password";
    public static final String MONGODB_REPLICASET_EXPORTER_SVC_NAME_SUFFIX = "-mongo-exporter";

    @Value("${autoManagement:true}")
    protected boolean autoManagement;

    @Autowired
    private KubeClientService kubeClientService;

    @Autowired
    private CloudDatabaseUserService dbUserService;

    @Autowired
    private NetworkService networkService;

    @Autowired
    private KubeConfigService kubeConfigService;

    @Autowired
    private IpPoolConfigService ipPoolConfigService;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private OperationUtil operationUtil;

    @Autowired
    private CloudAppConfigService appConfigService;

    @Autowired
    private CloudDbParamTemplateService cloudDbParamTemplateService;

    @Autowired
    private CloudAppLogicService appLogicService;

    @Autowired
    private AppMultiAZService appMultiAZService;

    @Autowired
    private MongoDBPodService mongoDBPodService;

    @Override
    protected void completeInstanceProperty(AppInstanceVO appInstanceVO, PodDTO pod, CloudApp app, KubeClient client) {
        appInstanceVO.setRole(pod.getLabel(CloudAppConstant.CustomLabels.ROLE));
    }

    public Map<String, String> getMongoDbRoleMap(KubeClient kubeClient, String namespace, String crName, String podName) {
        List<Map<String, String>> statusMembers =
                mongoDBPodService.getMongoDbStatusMembers(kubeClient, namespace, crName, podName, AppKind.MongoDB);
        return getMongoDbRoleMap(statusMembers);
    }

    /**
     *
     * 提取 RoleMap中的主从key信息
     */
    public Map<String, String> getMongoDbRoleMap(List<Map<String, String>> maps) {
        Map<String, String> roles = maps.stream().collect(Collectors.toMap(key -> key.get("name").split("\\.")[0], val -> {
            String stateStr = val.get("stateStr");
            if ("PRIMARY".equals(stateStr)) {
                return "primary";
            }
            if ("SECONDARY".equals(stateStr)) {
                return "secondary";
            }
            return "";
        }));
        return roles;
    }


    /**
     * 从MongoDB的状态信息中获取到相应节点的optime信息
     */
    public Map<String, String> getMongoDbOpTimeByRoleMap(List<Map<String, String>> maps) {
        Map<String, String> optimeMap = maps.stream().collect(Collectors.toMap(key -> key.get("name").split("\\.")[0], val -> {
            //获取到optime中的字符串
            return String.valueOf(JSONPath.read(val.get("optime"), "ts.$timestamp.t"));
        }));
        return optimeMap;
    }

    public List<Map<String, String>> listPv(Integer kubeId, Map<String, String> labels) {
        if (kubeId == null) {
            // 所有集群
            List<Map<String, String>> pvcProps = new LinkedList<>();
            for (KubeConfig each : kubeConfigService.list(null)) {
                pvcProps.addAll(listPvByKube(each.getId(), null));
            }
            return pvcProps;
        } else {
            // 指定集群
            return listPvByKube(kubeId, labels);
        }
    }

    public List<Map<String, String>> listPvByKube(Integer kubeId, Map<String, String> labels) {
        List<Map<String, String>> pvProps = new LinkedList<>();
        if (kubeId == null) {
            return pvProps;
        }
        PersistentVolumeList persistentVolumeList;
        List<Map<String, String>> pvcList;
        try {
            // 所有PV
            persistentVolumeList = kubeClientService.get(kubeId).listPv(labels);
            pvcList = listPvc(kubeId, null, null);
        } catch (Exception e) {
            log.error("kubeId:{} listPvc error:{}", kubeId, e);
            return pvProps;
        }
        // 筛选MongoDbPV
        List<PersistentVolume> pvList = new LinkedList<>();
        for (PersistentVolume each : persistentVolumeList.getItems()) {
            for (Map<String, String> pvc : pvcList) {
                if (each.getMetadata().getName().equals(pvc.get("volumeName"))) {
                    pvList.add(each);
                }
            }
        }

        for (PersistentVolume each : pvList) {
            ObjectMeta metadata = each.getMetadata();
            HashMap<String, String> prop = new HashMap<>();
            Date date = parseUTC(metadata.getCreationTimestamp());
            prop.put("createTimestamp", date == null ? null : date.getTime() + "");
            prop.put("name", metadata.getName());
            PersistentVolumeSpec spec = each.getSpec();
            Quantity storage = spec.getCapacity().get("storage");
            prop.put("capacity", storage == null ? null : storage.getAmount());
            prop.put("accessModes", Arrays.toString(spec.getAccessModes().toArray()));
            prop.put("reclaimPolicy", spec.getPersistentVolumeReclaimPolicy());
            prop.put("namespace", spec.getClaimRef().getNamespace());
            prop.put("pvc", spec.getClaimRef().getName());
            prop.put("storageClassName", spec.getStorageClassName());
            prop.put("status", each.getStatus().getPhase());
            prop.put("volumeMode", spec.getVolumeMode());
            prop.put("kubeId", kubeId + "");
            pvProps.add(prop);
        }
        return pvProps;
    }

    public List<Map<String, String>> listPvc(Integer kubeId, String namespace, Map<String, String> labels) {
        // 所有集群
        if (kubeId == null) {
            List<Map<String, String>> pvcProps = new LinkedList<>();
            for (KubeConfig each : kubeConfigService.list(null)) {
                pvcProps.addAll(listPvcByKube(each.getId(), null, null));
            }
            return pvcProps;
        } else {
            // 指定集群
            return listPvcByKube(kubeId, namespace, labels);
        }
    }

    public List<Map<String, String>> listPvcByKube(Integer kubeId, String namespace, Map<String, String> labels) {
        List<Map<String, String>> pvcProps = new LinkedList<>();
        if (kubeId == null) {
            return pvcProps;
        }
        PersistentVolumeClaimList persistentVolumeClaimList;
        final List<String> pvcTemplateNamesByMongoDb;
        try {
            persistentVolumeClaimList = kubeClientService.get(kubeId).listPvc(namespace, labels);
            pvcTemplateNamesByMongoDb = getPvcTemplateNamesByMongoDb(kubeId);
        } catch (Exception e) {
            log.error("kubeId:{} listPvc error:{}", kubeId, e);
            return pvcProps;
        }
        // 所有的PVC
        // mongoDb的PVC
        List<PersistentVolumeClaim> mongoPvcList = new LinkedList<>();
        for (String each : pvcTemplateNamesByMongoDb) {
            for (PersistentVolumeClaim pvc : persistentVolumeClaimList.getItems()) {
                // todo pvcTemplatePrefix+PodName
                if (pvc.getMetadata().getName().contains(each)) {
                    mongoPvcList.add(pvc);
                }
            }
        }
        // 展示mongo的PVC属性
        for (PersistentVolumeClaim each : mongoPvcList) {
            HashMap<String, String> prop = new HashMap<>();
            ObjectMeta metadata = each.getMetadata();
            prop.put("name", metadata.getName());
            Date date = parseUTC(metadata.getCreationTimestamp());
            prop.put("createTimestamp", date == null ? null : date.getTime() + "");
            prop.put("namespace", metadata.getNamespace());
            PersistentVolumeClaimSpec spec = each.getSpec();
            prop.put("accessModes", Arrays.toString(spec.getAccessModes().toArray()));
            Quantity storage = spec.getResources().getRequests().get("storage");
            prop.put("resourceRequest", storage == null ? null : storage.getAmount());
            prop.put("storageClassName", spec.getStorageClassName());
            prop.put("volumeMode", spec.getVolumeMode());
            prop.put("volumeName", spec.getVolumeName());
            prop.put("status", each.getStatus().getPhase());
            prop.put("kubeId", kubeId + "");
            pvcProps.add(prop);
        }
        return pvcProps;
    }

    public List<String> getPvcTemplateNamesByMongoDb(Integer kubeId) {
        List<String> pvcNames = new LinkedList<>();
        KubernetesResourceList<MongoDBCommunity> mongoList = kubeClientService.get(kubeId).getMongoDBClient().list();
        for (MongoDBCommunity each : mongoList.getItems()) {
            for (PersistentVolumeClaim pvc : each.getSpec().getStatefulSet().getSpec().getVolumeClaimTemplates()) {
                pvcNames.add(pvc.getMetadata().getName());
            }
        }
        return pvcNames;
    }
    //=============================运维操作==================================

    @Override
    public AppKind getKind() {
        return AppKind.MongoDB;
    }

    @Override
    public Class<? extends OpsPostProcessor> getProcessorClass(ActionEnum action) {
        switch (action) {
            case CREATE: return MongoDbInstallWatch.class;
            case UPDATE:
                return MongoDbUpdateWatch.class;
            case UPDATE_PASSWORD:
                return MongodbResourceWatch.class;
            case SCALE_OUT:
            case SCALE_IN:
                return MongodbResourceWatch.class;
            case MODIFY_PARAM: return MongoDBModifyParamsWatch.class;
            case SWITCH_MASTER: return MongoDBSwitchMasterWatch.class;
            case CREATE_SERVICE:
            case UPDATE_SERVICE:
            case DELETE_SERVICE:
                return MongoDBSvcWatch.class;
            default:
                return super.getProcessorClass(action);
        }
    }

    @Override
    public boolean nodePolicy() {
        return false;
    }

    @Override
    protected boolean supportIPAM(CloudAppVO app) {
        // mongo cr使用calico插件无法固定ip 无须分配IP
        return !CloudAppConstant.K8sCNIType.CALICO.equals(app.getCniType());
    }

    @Override
    public MongoDBCommunity doInstall(CloudAppVO vo, List ips) throws Exception {
        Integer kubeId = vo.getKubeId();
        KubeClient kubeClient = kubeClientService.get(kubeId);

        // create rbac for namespace
        String namespace = vo.getNamespace();
        if (!kubeClient.checkRbac("mongodb-database", namespace) || !kubeClient.checkRbac("mongodb-kubernetes-operator", namespace)) {
            String yaml = sysConfigService.findOne("operator.config", "mongodb.rbac");
            // merge label update rbac
            List<KubernetesResource> mongodb_agent = Serialization.unmarshal(
                    new ByteArrayInputStream(yaml.getBytes()), Collections.emptyMap());
            List<KubernetesResource> label_agent = Serialization.unmarshal(
                    new ByteArrayInputStream(new StringSubstitutor(ImmutableMap.of("namespace", namespace, "name", getAgentServiceAccountName()))
                            .replace(getAgentRbacTemplate()).getBytes()), Collections.emptyMap());

            Predicate<KubernetesResource> rolePredicate = res -> res instanceof Role;
            // add to mongodb-database sa
            mongodb_agent.stream().filter(rolePredicate)
                            .findFirst().ifPresent(res -> {
                        ((Role)res).getRules().addAll(label_agent.stream().filter(rolePredicate).findFirst().map(r -> ((Role)r).getRules()).orElse(Collections.emptyList()));
                    });
            kubeClient.applyYaml(mongodb_agent.stream().map(res -> Serialization.asYaml(res)).collect(Collectors.joining("\n---\n")), namespace);

            log.info("create rbac at ns " + namespace);
        }

        // create secrets and user
        String crName = vo.getCrName();
        String admin = MongoUtil.getSecretName(crName, "admin");
        kubeClient.createSecret(admin, "Passw0rd", namespace);
        // 页面填写用户
        String username = vo.getUsername();
        String password = vo.getPassword();
        if (StringUtils.isNotEmpty(username)) {
            if (username.equals("admin")) {
                throw new CustomException(600, "admin为预留用户");
            }
            String appUser = MongoUtil.getSecretName(crName, username);
            String secretRef = kubeClient.createSecret(appUser, password, namespace).getMetadata().getName();
            dbUserService.createUser(vo.getId(), username, "", "admin");
            vo.setPasswdSecretRef(secretRef);
        }
        //创建dmp监控用户
        Map<String, String> dmpMonitorUser = operationUtil.createDMPMonitorUser();
        String dmpUser = MongoUtil.getSecretName(crName, dmpMonitorUser.get("username"));
        kubeClient.createSecret(dmpUser, dmpMonitorUser.get("password"), namespace).getMetadata().getName();

        // create hostpath pv manually
        StorageProperties properties = new StorageProperties();
        properties.setResourceVersion(vo.getResourceVersion());
        properties.setCsiType(vo.getCsiType());
        properties.setHostpathRoot(vo.getHostpathRoot());
//        properties.setBackupCsiType(vo.getBackupCsiType());
//        properties.setBackupHostpathRoot(vo.getBackupHostpathRoot());
        //根据crName判断是否已经创建了pvc
        Label[] labels = MongoUtil.getPodLabel(crName);
        Map<String, String> labelMap = Arrays.stream(labels).collect(Collectors.toMap(Label::getName, Label::getValue));
        PersistentVolumeClaimList mongoPvc = clientService.get(kubeId).listPvc(namespace, labelMap);
        if (null != mongoPvc && 0 != mongoPvc.getItems().size()) {
            //有pvc，报错
            //TODO 当存在之前同名应用创建的pvc时，应该对存储大小和存储插件进行比较，但是情况较为复杂，需要之后进行尝试，并修改此处代码，对pvc进行修改或其他操作
            log.error("已经存在同名应用创建的pvc！");
            throw new CustomException(600, "已经存在同名应用创建的pvc！");
        }
        // todo
//        String finBackupStorageClassName = getBackupStorageClassName(vo.getBackupCsiType());
//        vo.setBackupStorageclassname(finBackupStorageClassName);
        manuallyProvideVolume(vo, kubeClient, vo.getMembers(), 0, properties);
        // new cr
        MongoDBCommunity cr = new MongoDBCommunity(vo);

        cr.getSpec().getStatefulSet().getSpec().getTemplate().getSpec().getContainers().stream()
                .filter(container -> ImmutableSet.of("mongod", "mongodb-agent").contains(container.getName())).map(container -> {
            SecurityContext securityContext = new SecurityContext();
            securityContext.setRunAsNonRoot(false);
            securityContext.setRunAsUser(0L);
            securityContext.setPrivileged(true);
            container.setSecurityContext(securityContext);
            return container;
        }).collect(Collectors.toList());
        if (CloudAppConstant.K8sCNIType.CALICO.equals(vo.getCniType())) {
//            cr.getSpec().getStatefulSet().getSpec().getTemplate().setMetadata(
//                    new ObjectMetaBuilder()
//                            .withAnnotations(Collections.singletonMap("cni.projectcalico.org/ipv4pools", "[\"mongodb-ippool\"]"))
//                            .build()
//            );
        } else if (CloudAppConstant.K8sCNIType.FABRIC.equals(vo.getCniType())) {
            Map<String, String> annotationMap = new HashMap<>();
            annotationMap.put("kubernetes.customized/fabric-networks", "default");
            annotationMap.put("kubernetes.customized/fabric-owner-kind", "statefulset");
            annotationMap.put("kubernetes.customized/fabric-owner-name", crName);
            cr.getSpec().getStatefulSet().getSpec().getTemplate().setMetadata(
                    new ObjectMetaBuilder().withAnnotations(annotationMap).build());
        }

        // 参数模板
        Map<String, String> config = new HashMap<>();
        if (vo.getDbParamTemplateId() != null) {
            log.info("[mongodb安装]参数模板id为：" + vo.getDbParamTemplateId());
            MySQLParamTemplateDTO paramTemplate = cloudDbParamTemplateService.getMysqlParamTemplate(vo.getDbParamTemplateId());
            List<MysqlParamRules> paramRules = paramTemplate.getMysqlParamRules();
            config = paramRules.stream().collect(Collectors.toMap(MysqlParamRules::getParaname, MysqlParamRules::getParavalue));
        }
        // overWrite cnf param
        overWriteCnfParam(vo, config);
        cr.getSpec().setAdditionalMongodConfig(new MongoDBCommunity.MongodConfiguration(config));

        // 创建ftp configmap,name
        String configmap = sysConfigService.findOne(CONFIGMAP_TEMPLATE, getKind().getKind());
        kubeClient.applyYaml(YamlUtil.evaluateTemplate(configmap, ImmutableMap.of("crName", crName,
                "namespace", namespace)), namespace);

        // 创建backup cm
        String backupConfigmap = sysConfigService.findOne(CONFIGMAP_TEMPLATE, getKind().getKind() + ".backup");
        kubeClient.applyYaml(YamlUtil.evaluateTemplate(backupConfigmap, ImmutableMap.of("crName", crName,
                "namespace", namespace)), namespace);

        return cr;
    }

    @Override
    protected void validateStorageClass(CloudAppVO app) {
        super.validateStorageClass(app);
//        if (StringUtils.isEmpty(app.getBackupDisk()))
//            app.setBackupDisk(app.getDisk());
    }
    /**
     * hack way to add custom labels onto pvc(as operator not support)
     * if the object to be created already exist in k8s,
     * @return
     */
    private void manuallyProvideVolume(CloudApp vo, KubeClient kubeClient, Integer members, Integer curMembers, StorageProperties properties) {

        Label[] createLabels = getPVCLabels(vo, properties);
        //没有pvc，进行创建
        //创建pvc的数量和实例数相关
        List<PersistentVolume> pvs= new ArrayList<>();
        List<PersistentVolumeClaim> pvcs= new ArrayList<>();
        buildVolumeItems(vo, members, curMembers, properties, createLabels, pvs, pvcs);
        try {
            Map<String, String> labelMap = Arrays.stream(Arrays.copyOf(createLabels, createLabels.length - 1))
                    .collect(Collectors.toMap(label -> label.getName(), label -> label.getValue()));
            if (!pvcs.isEmpty()) {
                Set<String> existPvcNames = kubeClient.listPvc(vo.getNamespace(), labelMap).getItems().stream().map(pvc -> pvc.getMetadata().getName())
                        .collect(Collectors.toSet());
                pvcs = pvcs.stream().filter(pvc -> !existPvcNames.contains(pvc.getMetadata().getName())).collect(Collectors.toList());
                if (!pvcs.isEmpty())
                    kubeClient.createBatch(pvcs.get(0).getKind(), pvcs.toArray(new PersistentVolumeClaim[0]));
                newlyCreatedPvc.get().addAll(pvcs.stream().map(pvc -> pvc.getMetadata().getName()).collect(Collectors.toList()));
            }
            if (!pvs.isEmpty()) {
                Set<String> existPvNames = kubeClient.listPv(labelMap).getItems().stream()
                        .map(pv -> pv.getMetadata().getName()).collect(Collectors.toSet());
                pvs = pvs.stream().filter(pv -> !existPvNames.contains(pv.getMetadata().getName())).collect(Collectors.toList());
                if (!pvs.isEmpty())
                    kubeClient.createBatch(pvs.get(0).getKind(), pvs.toArray(new PersistentVolume[0]));
            }
        } finally {
            if (!pvcs.isEmpty()) {
                pvcs.forEach(pvc -> OpLogContext.instance().YAML(pvc, null));
            }
            if (!pvs.isEmpty()) {
                pvs.forEach(pv -> OpLogContext.instance().YAML(pv, null));
            }
        }
    }

    private Label[] getPVCLabels(CloudApp vo, StorageProperties properties) {
        Label[] podLabels = getKind().labelOfPod(vo);
        // 创建新的标签列表，并添加原有标签和新标签
        Label[] resultLabels = Arrays.copyOf(podLabels, podLabels.length + 1);
        resultLabels[podLabels.length] = new Label(
                CloudAppConstant.CustomLabels.RESOURCE_VERSION, properties.getResourceVersion());
        return resultLabels;
    }

    public void buildVolumeItems(CloudApp vo, Integer members, Integer curMembers, StorageProperties properties, Label[] createLabels, List<PersistentVolume> pvs, List<PersistentVolumeClaim> pvcs) {
        String podNamePrefix = vo.getCrName();
        String pvSelectorLabel = "mongo-pv";
        if (CSIUtil.isHostpath(properties.getCsiType())) {
            String hostpathRoot = properties.getHostpathRoot();
            List<PersistentVolume> dataPV = KubeClientUtil.provideHostpathPV(DATA_VOLUME, podNamePrefix + "-" + vo.getNamespace(), vo.getCrName(), vo.getNamespace(), vo.getDisk(), hostpathRoot, members, curMembers, null, pvSelectorLabel);
            List<PersistentVolume> logPV = KubeClientUtil.provideHostpathPV(LOGS_VOLUME, podNamePrefix + "-" + vo.getNamespace(), vo.getCrName(), vo.getNamespace(), LOG_PV_SIZE, hostpathRoot, members, curMembers, null, pvSelectorLabel);
            pvs.addAll(dataPV);
            pvs.addAll(logPV);
            List<PersistentVolumeClaim> data = KubeClientUtil.provideHostpathPVC(DATA_VOLUME, podNamePrefix, vo.getNamespace(), vo.getDisk(), createLabels, dataPV, pvSelectorLabel, curMembers);
            List<PersistentVolumeClaim> log = KubeClientUtil.provideHostpathPVC(LOGS_VOLUME, podNamePrefix, vo.getNamespace(), LOG_PV_SIZE, createLabels, logPV, pvSelectorLabel, curMembers);
            pvcs.addAll(data);
            pvcs.addAll(log);
        } else {
            List<PersistentVolumeClaim> data = KubeClientUtil.provideCsiPVC(DATA_VOLUME, podNamePrefix, vo.getNamespace(), vo.getDisk(), createLabels, members, curMembers, vo.getStorageClassName());
            List<PersistentVolumeClaim> log = KubeClientUtil.provideCsiPVC(LOGS_VOLUME, podNamePrefix, vo.getNamespace(), LOG_PV_SIZE, createLabels, members, curMembers, vo.getStorageClassName());
            pvcs.addAll(data);
            pvcs.addAll(log);
        }
//        if (StringUtils.isNotEmpty(properties.getBackupCsiType()))
//            if (CSIUtil.isHostpath(properties.getBackupCsiType())) {
//                String hostpathRoot = properties.getBackupHostpathRoot();
//                List<PersistentVolume> pv = KubeClientUtil.provideHostpathPV(BACKUP_VOLUME, podNamePrefix + "-" + vo.getNamespace(), vo.getCrName(), vo.getNamespace(), vo.getBackupDisk(), hostpathRoot, members, curMembers, null, pvSelectorLabel);
//                List<PersistentVolumeClaim> pvc = KubeClientUtil.provideHostpathPVC(BACKUP_VOLUME, podNamePrefix, vo.getNamespace(), vo.getBackupDisk(), createLabels, pv, pvSelectorLabel, curMembers);
//                pvs.addAll(pv);
//                pvcs.addAll(pvc);
//            } else if (CSIUtil.isSharedType(properties.getBackupCsiType())) {
//                PersistentVolumeClaim pvc = KubeClientUtil.provideSharedCsiPVC(BACKUP_VOLUME, podNamePrefix, vo.getNamespace(), vo.getBackupDisk(), createLabels, vo.getBackupStorageclassname());
//                pvcs.addAll(ImmutableList.of(pvc));
//            } else {
//                List<PersistentVolumeClaim> pvc = KubeClientUtil.provideCsiPVC(BACKUP_VOLUME, podNamePrefix, vo.getNamespace(), vo.getBackupDisk(), createLabels, members, curMembers, vo.getBackupStorageclassname());
//                pvcs.addAll(pvc);
//            }
    }

    @Override
    public List<ServiceManager> createService(
            String serviceType, CloudAppVO vo, List<?> serviceResources, CustomResource installCr) {
        return openSourceKindServiceBuilder(serviceType, vo, serviceResources, null);
    }

    @Override
    protected void createCrControlResource(MongoDBCommunity cr, CloudAppVO vo) {
        KubeClient client = kubeClientService.get(vo.getKubeId());
        CompletableFuture.allOf(CompletableFuture.runAsync(() -> createExporterService(vo, client)),
                CompletableFuture.runAsync(() -> createAgentRbac(client, vo.getNamespace()))
        ).join();
    }

    public void createExporterService(CloudApp app, KubeClient client) {
        // 获取serviceName
        String crName = app.getCrName();
        String svcName = MongoUtil.getExporterServiceName(crName);
        String namespace = app.getNamespace();
        Map<String, String> labelMap = Arrays.stream(getKind().labels(crName))
                .collect(toMap(Label::getName, Label::getValue));
        Map<String, String> selectorLabelMap = Arrays.stream(getKind().labelOfService(app))
                .collect(toMap(Label::getName, Label::getValue));
        String serviceYaml = KubeClientUtil.buildServiceYaml(
                namespace, CloudAppConstant.ServiceType.CLUSTER_IP,
                svcName, null, 9216, MongoUtil.MONGODB_REPLICASET_EXPORTER_SVC_ENDPOINT_NAME,
                labelMap, null, selectorLabelMap, null);

        client.applyYaml(serviceYaml, namespace);
    }

    public void deleteExporterService(CloudApp app) {
        // 获取serviceName
        String crName = app.getCrName();
        KubeClient client = kubeClientService.get(app.getKubeId());
        client.deleteService(MongoUtil.getExporterServiceName(crName), app.getNamespace());
    }

    @Override
    public void updateService(List<ServiceManager> svcMgrs, CloudApp app, Object oldServiceResource) throws Exception {
        openSourceKindUpdateServiceBuilder(svcMgrs, app, oldServiceResource);
    }

    public List<Map<String, Object>> getRuntimeParameters(Integer logicId, String component) {
        // fetch instance of this logic app by id
        CloudAppLogic cloudAppLogic = appLogicService.get(logicId);

        List<AppInstanceVO> instances = appMultiAZService.getInstancesBasicInfo(logicId)
                .stream().filter(i -> CloudAppConstant.PodPhase.RUNNING.equals(i.getStatus()))
                .collect(Collectors.toList());
        CustPreconditions.checkState(!CollectionUtils.isEmpty(instances), "查询失败，没有健康的实例");

        String namespace = cloudAppLogic.getNamespace();
        switch (AppKind.valueOf(cloudAppLogic.getKind(), cloudAppLogic.getArch())) {
            case MongoDB:
                KubeClient kubeClient = clientService.get(instances.get(0).getKubeId());
                return getRuntimeParameters(kubeClient, instances.get(0).getPodName(), namespace,
                        AppKind.MongoDB, AppKind.MongoDB.getDbPort(),
                        "admin", "Passw0rd");
            case MongoDB_Cluster:
                // get pod by component label
                AppInstanceVO componentInstance = instances.stream()
                        .filter(instance ->
                                component.equals(instance.getLabels().get(CloudAppConstant.CustomLabels.APP_COMPONENT)))
                        .findFirst().orElseThrow(() -> new CustomException(600, "查询失败，未找到组件信息"));
                // different component have different port. shard: 27018, config server: 27019, mongos: 27017
                String crYaml = appService.get(componentInstance.getAppId()).getCr();
                MongoDBCluster cr = null;
                try {
                    cr = YamlEngine.unmarshal(crYaml, MongoDBCluster.class);
                } catch (Exception e) {
                    log.error("查询mongocluster实时参数", e);
                }
                int port = 0;
                if (component.equals(CloudAppConstant.MongoDB.SHARD)) {
                    if (cr != null) port = cr.getSpec().getShardServers().getPort();
                    if (port == 0) port = CloudAppConstant.MongoDB.DEFAULT_SHARD_PORT;
                } else if (component.equals(CloudAppConstant.MongoDB.CONFIG)) {
                    if (cr != null) port = cr.getSpec().getConfigServers().getPort();
                    if (port == 0) port = CloudAppConstant.MongoDB.DEFAULT_CONFIG_PORT;
                } else {
                    if (cr != null) port = cr.getSpec().getRouterServers().getPort();
                    if (port == 0) port = CloudAppConstant.MongoDB.DEFAULT_ROUTER_PORT;
                }
                kubeClient = clientService.get(componentInstance.getKubeId());
                return getRuntimeParameters(kubeClient, componentInstance.getPodName(), namespace,
                        AppKind.MongoDB_Cluster, port, "admin", "Passw0rd");
            default:
                throw new CustomException(600, "参数错误");
        }
    }

    private List<Map<String, Object>> getRuntimeParameters(KubeClient client, String pod, String namespace, AppKind kind, int port,
                                                           String user, String password) {
        // 在容器里执行命令获取server parameter(对应config file中setParameter块)
        String cmd = String.format("mongo mongodb://%s:%s@localhost:%d --eval \"db.adminCommand({getParameter:'*'})\" --quiet",
                user, password, port);
        try {
            String bsonString = client.execCmd(namespace, pod, kind.getContainerName(), 10, "sh", "-c", cmd);
            Map<String, Object> resultMap = convertBsonToMap(bsonString);
            // mongodb command response 包含一些不属于server参数response字段
            resultMap.remove("ok");
            resultMap.remove("operationTime");
            resultMap.remove("$clusterTime");

            String prohibitContent = sysConfigService.findOne(PARAM_PROHIBIT, kind.getProduct());
            final Set<String> prohibited = prohibitContent == null ? new HashSet<>() :
                    new HashSet<>(Arrays.asList(prohibitContent.split(",")));
            return resultMap.keySet().stream().map(key -> {
                Map<String, Object> savemap = new HashMap<>();
                // 添加前缀 setParameter. 用于更新时对应到配置文件
                String paraName = "setParameter." + key;
                savemap.put("paraName", paraName);
                savemap.put("paraValue", resultMap.get(key));
                savemap.put("paraDescription", "");
                savemap.put("isupdate", (!prohibited.isEmpty() && prohibited.contains(paraName)) ? Boolean.FALSE : Boolean.TRUE);
                return savemap;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            throw new CustomException(600, "" ,e);
        }
    }

    private Map<String, Object> convertBsonToMap(String bsonString) {

        String json = Document.parse(bsonString).toJson(JsonWriterSettings.builder()
                .int64Converter(new Converter<Long>() {
                    @Override
                    public void convert(Long aLong, StrictJsonWriter strictJsonWriter) {
                        strictJsonWriter.writeNumber(String.valueOf(aLong.longValue()));
                    }
                })
                .build());
        Map<String, Object> map = JsonUtil.toObject(json, new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {
        });
        return JsonUtil.flattenMap(map);

    }

    @Data
    public static class StorageProperties {
        private String resourceVersion;
        private String csiType;
        private String hostpathRoot;
        private String backupCsiType;
        private String backupHostpathRoot;
    }

    @Override
    public void deleteCrControlledResources(CloudApp app) {
        String crName = app.getCrName();
        String namespace = app.getNamespace();
        KubeClient kubeClient = clientService.get(app.getKubeId());
        // delete backup cm
        kubeClient.deleteConfigMap(MongoUtil.getBackupCMName(crName), namespace);
        // delete scripts cm
        kubeClient.deleteConfigMap(MongoUtil.getScriptsCMName(crName), namespace);
        // delete exporter service
        deleteExporterService(app);
        // secret
        String yamlContent = StringUtils.isEmpty(app.getCr()) ? app.getCrRun() : app.getCr();
        if (StringUtils.isNotEmpty(yamlContent)) {
            MongoDBCommunity.MongoDBUser[] users = YamlEngine.unmarshal(yamlContent, MongoDBCommunity.class).getSpec().getUsers();
            if (users != null)
                for (MongoDBCommunity.MongoDBUser user : users) {
                    kubeClient.deleteSecret(MongoUtil.getSecretName(crName, user.getName()), namespace);
                    kubeClient.deleteSecret(MongoUtil.getScramSecretName(crName, user.getName()), namespace);
                }

        } else if (app instanceof CloudAppVO) {
            kubeClient.deleteSecret(MongoUtil.getSecretName(crName, MongoUtil.ADMIN), namespace);
            if (StringUtils.isNotEmpty(((CloudAppVO) app).getUsername()))
                kubeClient.deleteSecret(MongoUtil.getScramSecretName(crName, ((CloudAppVO) app).getUsername()), namespace);
        }
    }

    @Override
    protected void deletePvc(CloudAppVO app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        for (int i = 0; i < app.getMembers(); i++) {
            kubeClient.deletePvc(app.getNamespace(), "data-volume-" + app.getCrName() + "-" + i);
            kubeClient.deletePvc(app.getNamespace(), "logs-volume-" + app.getCrName() + "-" + i);
            if (app instanceof CloudAppVO && CSIUtil.isHostpath(((CloudAppVO) app).getCsiType())) {
                kubeClient.deletePv("data-volume-" + app.getCrName() + "-" + i);
                kubeClient.deletePv("logs-volume-" + app.getCrName() + "-" + i);
            }
        }
    }

    @Override
    public String getIpReservationTarget() {
        return "statefulset";
    }


    public int handleWatchResult(Integer appId, boolean success) {
//        CloudApp app = appCommonService.get(appId);
//        // ip address management
//        List<String> ips = networkService.getAllocatedIpOfApp(app);
//        Set<String> curIps = new HashSet<>(ips);
//        CloudApp.IpNode[] ipNodes = app.getIpNode();
//        Set<String> prevIps = Arrays.stream(ipNodes).map(i -> i.getIp()).collect(Collectors.toSet());
//        curIps.addAll(prevIps); // 避免ipinfo记录不全
//        ArrayList<String> difference = new ArrayList<>(Sets.difference(curIps, prevIps));
//        if (success) {
//            // 缩容成功更新ip信息
//            if (prevIps.size() > curIps.size()) {
////                networkService.reserveLocalIp(difference, app);
////                networkService.releaseLocalIp(difference, app);
//            }
//        } else {
//            // 扩容失败回滚ip信息
//            if (curIps.size() > prevIps.size()) {
//                // ip stay as reserved
////                networkService.releaseLocalIp(difference, app);
//            }
//        }

        return appService.handleWatchResult(appId, success);
    }

    // TODO after 存储升级改造，添加备份存储升级
    @Transactional
    public void update(ResourceDTO resourceDTO) throws Exception {
        Integer id = resourceDTO.getId();
        Integer appId = resourceDTO.getAppId();
        if(null == id){
            if(null == appId){
                CustPreconditions.checkNotNull(null, "given id is null");
            }
            id = appId;
        }
        CloudApp app = appService.get(id);
        // 实时获取cr or statefulset的spec而不使用表中参数
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        MongoDBCommunity cr = kubeClient.listCustomResource(MongoDBCommunity.class, app.getCrName(), app.getNamespace());
        // containers->mongod->resource->limit
        Container container = cr.getSpec().getStatefulSet().getSpec().getTemplate().getSpec().getContainers()
                .stream().filter(c -> MongoUtil.DB_CONTAINER_NAME.equals(c.getName())).findFirst().orElseThrow(() -> new CustomException(600, "mongod容器不存在"));
        Map<String, Quantity> resource = container.getResources().getLimits();

        boolean doChange = false, storageChange;
        Quantity storage = cr.getSpec().getStatefulSet().getSpec().getVolumeClaimTemplates().stream()
                .filter(v -> v.getMetadata().getName().equals("data-volume"))
                .map(o -> o.getSpec().getResources().getRequests().get("storage"))
                .findAny().get();
        log.info(String.format("[MongoDB资源升级]期望资源为：cpu-%s,mem-%s,disk-%s",
                resourceDTO.getCpu(), resourceDTO.getMemory(), resourceDTO.getDisk()));
        if (resourceDTO.getDisk() != null && !new Quantity(resourceDTO.getDisk()).equals(storage)) {
            doChange = true; storageChange = true;
            RegexUtil.checkDisk(resourceDTO.getMemory());
        } else {
            storageChange = false;
        }

        if (resourceDTO.getCpu() != null && !resource.get("cpu").equals(new Quantity(resourceDTO.getCpu()))) {
            doChange = true;
            RegexUtil.checkCPU(resourceDTO.getCpu());
            resource.put("cpu", new Quantity(resourceDTO.getCpu()));
        }
        if (resourceDTO.getMemory() != null && !resource.get("memory").equals(new Quantity(resourceDTO.getMemory()))) {
            doChange = true;
            RegexUtil.checkMemory(resourceDTO.getMemory());
            resource.put("memory", new Quantity(resourceDTO.getMemory()));
        }
        if (!doChange) {
            log.info("没有检查到资源变更，忽略:{}", resourceDTO.toString());
            return;
        }
        container.setResources(ResourceHelper.getInstance().resourceRequirements(resourceDTO));
        // 设置cr_run字段
        if (StringUtils.isEmpty(app.getCr())) {
            log.error("app.cr is null");
            return ;
        }
        // 提交更新
        cr.getSpec().getStatefulSet().getSpec().getTemplate().getSpec().getContainers()
                .stream().filter(c -> MongoUtil.DB_CONTAINER_NAME.equals(c.getName())).findFirst().get()
                .getResources().setLimits(resource);
        updateStorage(resourceDTO, cr, storageChange);

        MongoDBCommunity crInDB = YamlEngine.unmarshal(app.getCr(), MongoDBCommunity.class);
        // 回滚时storage不能回滚
        MongoDBCommunity unmarshalCurCR = YamlEngine.unmarshal(YamlEngine.marshal(crInDB), MongoDBCommunity.class);
        updateStorage(resourceDTO, unmarshalCurCR, storageChange);

        crInDB.setSpec(cr.getSpec());
        String applyYaml = YamlEngine.marshal(crInDB);
        OpLogContext.instance().CR(applyYaml, YamlEngine.marshal(unmarshalCurCR));
        appService.callScheduler(app, applyYaml, ImmutableMap.of("storageChange", storageChange,
                "disk", resourceDTO.getDisk()),
                ActionEnum.UPDATE, getProcessorClass(ActionEnum.UPDATE));
        kubeClient.updateCustomResource(cr, MongoDBCommunity.class);
        // 首先更新cr, 删除就的或被operator重建的sts以更新sts 因为无法对已经存在的sts更新其volumeTemplate
        if (storageChange)
            kubeClient.deleteStatefulset(app.getCrName(), app.getNamespace());
    }

    private static void updateStorage(ResourceDTO resourceDTO, MongoDBCommunity cr, boolean storageChange) {
        if (storageChange)
            cr.getSpec().getStatefulSet().getSpec().getVolumeClaimTemplates().stream()
                    .filter(pvc -> pvc.getMetadata().getName().equals(DATA_VOLUME))
                    .findAny().ifPresent(pvc -> pvc.getSpec().getResources().getRequests().put("storage", new Quantity(resourceDTO.getDisk())));
    }

    @Transactional
    @Override
    public void scale(int id, OverrideSpec vo, ActionEnum action) throws Exception {
        scale(id, vo.getMembers());
    }


    @Transactional
    public void scale(int appId, int members) throws Exception {
        CloudApp app = appService.get(appId);
        String cniType = appService.getCniType(app.getKubeId());
        Integer curMembers = app.getMembers();
        ActionEnum actionEnum = members > curMembers ? ActionEnum.SCALE_OUT : ActionEnum.SCALE_IN;
        log.info("应用-{} {}", appId, actionEnum.getAppOperation());
        boolean needRecall = false;
        // apply to mongo cr
        String yaml = app.getCr();
        MongoDBCommunity cr = YamlEngine.unmarshal(yaml, MongoDBCommunity.class);
        MongoDBCommunity actCr = kubeClientService.get(app.getKubeId()).listCustomResource(MongoDBCommunity.class, app.getCrName(), app.getNamespace());
        actCr.getSpec().setMembers(members);
        cr.setSpec(actCr.getSpec());
        String applyYaml = YamlEngine.marshal(cr);
        if (actionEnum == ActionEnum.SCALE_OUT)
            // 缩容不更新reservation: fabric-admin不能缩减reservation中ip列表, 且无法重建reservation
            needRecall = updateIpAllocation(app, cniType, members);

        OpLogContext.instance().YAML("CR", applyYaml, app.getCr());
        List<PodDTO> podDTOS = kubeClientService.get(app.getKubeId()).listPod(app.getNamespace(), getKind().labelOfPod(app));
        List<String> podList = podDTOS.stream().map(e -> e.getPodName()).collect(Collectors.toList());
        Integer hisId = appService.callScheduler(app, applyYaml, ImmutableMap.of("podList", podList), actionEnum, MongodbScaleWatch.class);

        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        // 手动创建扩容需要的pv
        if (actionEnum == ActionEnum.SCALE_OUT) {
            StorageProperties properties = new StorageProperties();
            properties.setResourceVersion(actCr.getMetadata().getLabels().get(CloudAppConstant.CustomLabels.RESOURCE_VERSION));
            properties.setCsiType(CSILoader.getCsiBySC(app.getStorageClassName()).getType());

            Label[] labels = getPVCLabels(app, properties);
            Map<String, String> labelMap = Arrays.stream(labels).collect(Collectors.toMap(Label::getName, Label::getValue));

            List<PersistentVolumeClaim> items = null;
            if (CSIUtil.isHostpath(properties.getCsiType())) {
                items = kubeClient.listPvc(app.getNamespace(), labelMap).getItems();
                String pvName = items.stream().filter(pvc -> AppUtil.isDataPVC(app, pvc.getMetadata().getName())).map(i -> i.getSpec().getVolumeName()).findAny().orElseThrow(() -> new CustomException(600, "存储pvc未找到"));
                PersistentVolume datapv = kubeClient.getPv(pvName);
                String path = datapv.getSpec().getHostPath().getPath();
                properties.setHostpathRoot(path);
            }
//            properties.setBackupCsiType(CSILoader.getCsiBySC(app.getBackupStorageclassname()).getType());
//            if (CSIUtil.isHostpath(properties.getBackupCsiType())) {
//                if (items == null) items = kubeClient.listPvc(app.getNamespace(), labelMap).getItems();
//                String pvName = items.stream().filter(pvc -> AppUtil.isBackupPVC(app, pvc.getMetadata().getName())).map(i -> i.getSpec().getVolumeName()).findAny().orElseThrow(() -> new CustomException(600, "备份存储pvc未找到"));
//                PersistentVolume backupPv = kubeClient.getPv(pvName);
//                String path = backupPv.getSpec().getHostPath().getPath(); // hostpathRoot/namespace/crName/podName
//                path = AppUtil.getSubstring(path, 3, "/");
//                properties.setBackupHostpathRoot(path);
//            }
            manuallyProvideVolume(app, kubeClient, members - curMembers, curMembers, properties);
            appService.appendOpLogOfHis(hisId);
        }
        kubeClient.updateCustomResource(actCr, MongoDBCommunity.class);
    }


    /**
     * 扩容分配Ip
     */
    private boolean updateIpAllocation(CloudApp app, String cniType, int expectedMembers) {
        if (CloudAppConstant.K8sCNIType.FABRIC.equals(cniType)) {

            IpPoolConfig ipConfig = ipPoolConfigService.selectByKubeIdAndKind(app.getKubeId(), app.getKind());
            BocRestfulHelper cniHelper = (BocRestfulHelper) BocCniHelperFactory.getInstance()
                    .create(app.getKubeId(), new CniConfig() {{
                        setNetwork(ipConfig.getNetwork());
                    }});
            // check if current reservation was enough
            String curReservedIps = cniHelper.getReservationIps(app.getNamespace(), app.getCrName(), "statefulset")
                    .orElseThrow(() -> new CustomException(600, "未找到ip已分配信息"));
            String curIps = String.join(",", networkService.getAllocatedIpOfApp(app));
            log.info("MongoDB scale out update ip allocation, ip info before update- " +
                    "ipam used:{}, cni reserved:{}, expected:{}", curIps, curReservedIps, expectedMembers);
            String[] split = curReservedIps.split(",");
            int curMembers = split.length;

            if (curMembers >= expectedMembers) return false;

            try {
                networkService.updateIpAllocation(app, cniType, Arrays.asList(split),
                        expectedMembers, cniHelper, null); // todo 应用类型到ip pool的映射关系
                log.info("reservation info after update--" + curIps);
                return true;
            } catch (Exception e) {
                throw new RuntimeException("update ip failed", e);
            }
        }
        return false;
    }

    /**
     * 切换镜像版本
     * @param version
     * @param appId
     */
    @Transactional
    public void upgrade(int appId, String version) throws Exception {
        //获取应用
        CloudApp app = appService.get(appId);

        //获取实际cr与表中cr
        MongoDBCommunity crInK8s = appService.getCustomResource(app, MongoDBCommunity.class);
        MongoDBCommunity curCr = YamlEngine.unmarshal(app.getCr(), MongoDBCommunity.class);

        //查询
        CloudAppConfig appConfig = appConfigService.get(getKind(), version);
        if(appConfig == null){
            throw new CustomException(600, "MongoDB应用未找到对应的镜像信息！kubeId：" + app.getKubeId() +"镜像版本：" + version);
        }

        //修改版本
        String oldVersion = crInK8s.getSpec().getVersion();
        //如果镜像未发生改变，则停止执行
        if(oldVersion.equals(version)){
            return;
        }
        curCr.getSpec().setVersion(version);
        crInK8s.getSpec().setVersion(version);

        Map<ImageKindEnum, String> imageManifest = appConfigService.getImageManifest(getKind(), version);
        // todo 更新其他容器镜像
        List<Container> containers = crInK8s.getSpec().getStatefulSet().getSpec().getTemplate().getSpec().getContainers();
        List<Container> mongod = containers.stream().filter(container -> ("mongod").equalsIgnoreCase(container.getName())).collect(Collectors.toList());
        Container mongodContainer = mongod.get(0);
        mongodContainer.setImage(imageManifest.get(ImageKindEnum.MainImage));
        curCr.setSpec(crInK8s.getSpec());
        String applyYaml = YamlEngine.marshal(curCr);
        OpLogContext.instance().YAML("CR", applyYaml, app.getCr());
        appService.callScheduler(app, applyYaml, ImmutableMap.of("newVersion", version), ActionEnum.UPGRADE, MongodbResourceWatch.class);
        //修改资源
        clientService.get(app.getKubeId()).updateCustomResource(crInK8s, MongoDBCommunity.class);

    }


    /**
     * 查询镜像列表
     * @return
     */
    public List<String> imageList(Integer kubeId, String version) {
        //查询镜像
        List<CloudAppConfig> list = appConfigService.get(getKind());
        //对当前版本进行切割
        String[] curVersion = version.split("\\.");
        //筛选出比当前版本高的镜像信息
        List<String> resList = list.stream().filter(item -> {
            if (version.equals(item.getVersion())) {
                return false;
            }
            String[] everyVersion = item.getVersion().split("\\.");
            for (int i = 0; i < everyVersion.length && i < curVersion.length; i++) {
                if (Integer.parseInt(curVersion[i]) > Integer.parseInt(everyVersion[i])) {
                    return false;
                } else if (Integer.parseInt(curVersion[i]) < Integer.parseInt(everyVersion[i])){
                    return true;
                }
            }
            return false;
        }).map(CloudAppConfig :: getVersion).collect(Collectors.toList());
        return resList;
    }
    @Autowired
    private BackupService backupService;

    /*@Override
    public BackupHis backup(int appId) throws Exception {
        CloudApp app = appService.get(appId);
        return backupService.manualBackup(new BackupHis() {{
            setAppId(app.getId());
            setAppName(app.getCrName());
            setAppType(app.getKind());
            setBackupType("full");
        }}, "mongodb");

    }*/

    @Override
    public void modifyConfigParam(Map<String, String> params, Integer appId, String componentKind) throws Exception {
        log.info("[modifyParam] mongoDB修改参数 应用ID:{}，参数{}", appId, JSONObject.toJSONString(params));
        if (CollectionUtils.isEmpty(params)){
            return;
        }
        CloudApp app = appService.get(appId);
//        MongoDBCommunity webCr = YamlEngine.unmarshal(app.getCr(), MongoDBCommunity.class);
        MongoDBCommunity webCr = kubeClientService.get(app.getKubeId()).listCustomResource(MongoDBCommunity.class, app.getCrName(), app.getNamespace());
        MongoDBCommunity.MongodConfiguration additionalMongodConfig = webCr.getSpec().getAdditionalMongodConfig();
        Map<String, String> oldConfig = new HashMap<>();
        if (additionalMongodConfig == null) {
            webCr.getSpec().setAdditionalMongodConfig(new MongoDBCommunity.MongodConfiguration(params) );
        } else {
            oldConfig = new HashMap(additionalMongodConfig);
            params.forEach((key, val) -> {
                this.prohibitParam(key);
                additionalMongodConfig.put(key, val);
            });
        }

        Map<Object, Object> map = new HashMap<>();
        map.put("params", params);
        map.put("appId", appId);
        map.put("startTime", new Date().getTime());

        KubeClient client = clientService.get(app.getKubeId());
        try {
            client.updateCustomResource(webCr, MongoDBCommunity.class);
//            app.setCrRun(YamlEngine.marshal(webCr));
//            appService.update(app);

            String applyYaml = YamlEngine.marshal(webCr);
            OpLogContext.instance().YAML("CR", applyYaml, app.getCr());
            appService.callScheduler(app, applyYaml, map, ActionEnum.MODIFY_PARAM, MongoDBModifyParamsWatch.class);
        } catch (SchedulerException | JsonProcessingException e) {
            // 回滚
            webCr.getSpec().setAdditionalMongodConfig(new MongoDBCommunity.MongodConfiguration(oldConfig));
            client.updateCustomResource(webCr, MongoDBCommunity.class);
            throw new CustomException(600, "MongoDB 修改参数失败，" + e.getMessage());
        }
    }

    /**
     * 切换指定节点为主，需要在主节点上执行。
     * @param appId 应用
     * @param podName 下一任主节点
     */
    public void switchMasterPlanned(Integer appId, String podName) throws Exception {
        if (appId == null || StringUtils.isBlank(podName)){
            throw new CustomException(600, "切换为主参数错误");
        }
        // 如果当前存在切换任务则返回
        ResourceChangeHis latestRcs = resourceChangeHisService.getLatestByAppId(appId);
        CustPreconditions.checkState(!StatusConstant.RUNNING.equals(latestRcs.getStatus()),
                "当前应用正在执行 " + latestRcs.getCommand() + " 操作，请稍候再试");

        String loginStatement = "mongo -u " + CloudAppConstant.UsernameAndPassword.mongoDBUsername
                + " -p " + CloudAppConstant.UsernameAndPassword.mongoDBPassword
                + " --authenticationDatabase admin --quiet  --eval ?"; // ?作为占位符 之后被替换
        CloudApp app = appService.get(appId);
        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());

        //获取相应的Status信息
        List<Map<String, String>> mongoDbStatusMembers = mongoDBPodService.getMongoDbStatusMembers(
                kubeClient, app.getNamespace(), app.getCrName(), podName, appKind);
        if (CollectionUtils.isEmpty(mongoDbStatusMembers)) {
            throw new CustomException(600, "角色获取失败");
        }
        // 查询目前主节点
        Map<String, String> mongoDbRoleMap = getMongoDbRoleMap(mongoDbStatusMembers);

        String primary = mongoDbRoleMap.entrySet().stream()
                .filter(role -> role.getValue().equals("primary"))
                .map(Map.Entry::getKey).findFirst()
                .orElseThrow(() -> new CustomException(600, "角色获取失败"));

        //拿到相应的oplog操作时间
        Map<String, String> mongoDbOpTimeByRoleMap = getMongoDbOpTimeByRoleMap(mongoDbStatusMembers);

        //判断主节点和要切换的从节点的数据操作时间是否一致
        if (checkMasterClusterOplogIsSame(primary, podName, mongoDbOpTimeByRoleMap)) {
            throw new CustomException(600, "当前节点数据与主节点数据不一致，请一致后再进行操作");
        }

        // 查询当前集群配置,并寻找吓人master在集群中的位置
        String queryStatement = "JSON.stringify(rs.conf().members)";
        String[] cmd = loginStatement.split(" ");
        cmd[cmd.length - 1] = queryStatement;
        String result = kubeClient.execCmd(app.getNamespace(), primary, AppKind.MongoDB.getContainerName(), cmd);
        Map<String, String>[] maps = JSONArray.parseObject(result, new TypeReference<Map<String, String>[]>() {
        });
        int targetPodIndex = 1;
        for (int i = 0; i < maps.length; i++) {
            String host = maps[i].get("host");
            if (host.split("\\.")[0].equals(podName)){
                targetPodIndex = i;
                break;
            }
        }

        // 切换主从
        String setMasterStatement = "var cfg = rs.conf();cfg.members["+ targetPodIndex +"].priority = 10;rs.reconfig(cfg);rs.stepDown(30)";
        cmd = loginStatement.split(" ");
        cmd[cmd.length - 1] = setMasterStatement;
        kubeClient.execCmd(app.getNamespace(), primary, AppKind.MongoDB.getContainerName(), cmd);
        Map<String, Object> map = new HashMap<>();
        map.put("podName", podName);
        map.put("startTime", new Date().getTime());
        appService.callScheduler(app, null, map, ActionEnum.SWITCH_MASTER, getProcessorClass(ActionEnum.SWITCH_MASTER), 10);
    }

    /**
     * 修改MongoDb密码
     * @param password
     * @param appId
     * @throws Exception
     */
    @Override
    public void updatePassword(Password password, int appId) throws Exception {
        try {
            //根据appId获取相应的应用信息
            CloudApp cloudApp = appService.get(appId);
            if (null == cloudApp) {
                log.info("未查询到相应的应用信息，appid为：" + appId);
                throw new CustomException(600, "未查询到相应的实例信息");
            }
            String crName = cloudApp.getCrName();
            String failMsg = "未查询到数据库用户信息!";
            //查询到相应用户名
            List<CloudDatabaseUser> users = dbUserService.findDbUser(cloudApp.getId(), CloudAppConstant.UserRole.ADMIN);
            if (CollectionUtils.isEmpty(users)) {
                log.error(String.format("[updatePassword]appid:%d,can't query user info", appId));
                throw new CustomException(600, failMsg);
            }
            // 用户填写的账密只有一个
            CloudDatabaseUser user = users.get(0);

            //对密码进行解密
            String deNewPassword = decryptPassword(password.getNewPassword());
            //获取相应k8s客户端
            KubeClient kubeClient = clientService.get(cloudApp.getKubeId());

            Secret secret = kubeClient.getSecret(cloudApp.getNamespace(), getSecretName(crName, user.getUsername()));
            //保存旧secret，用于回滚
            String oldSecretYaml = YamlEngine.marshal(secret);
            String newSecretYaml;
            if (null != secret) {
                Map<String, String> data = secret.getData();
                data.put(SECRET_PASSWORD_KEY, Base64.getEncoder().encodeToString(
                        deNewPassword.getBytes(StandardCharsets.UTF_8)));
                newSecretYaml = YamlEngine.marshal(secret);
                kubeClient.updateSecret(secret);
            } else {
                log.error(String.format("[updatePassword]appid:%d,can't get user secret", appId));
                throw new CustomException(600, failMsg);
            }

            OpLogContext.instance().YAML("SECRET", newSecretYaml, oldSecretYaml);
            appService.callScheduler(cloudApp, null, null, ActionEnum.UPDATE_PASSWORD,
                    getProcessorClass(ActionEnum.UPDATE_PASSWORD));
        } catch (Exception e) {
            log.error(String.format("[updatePassword]appid:%d,update password error", appId), e);
            throw new CustomException(600, "修改密码失败");
        }
    }

    public List<Map<String, Object>> sqlExecutor(String sql, MetaVO metaVO) {
        try (MongoClient mongoClient = DBConnectManager.getInstance().getMongoDatabase(metaVO)) {
            if (StringUtils.isNotEmpty(sql) && sql.startsWith("/*")) {
                sql = sql.substring(8, sql.length()).trim();
            }
            String[] command = sql.split(":");
            MongoDatabase mongoDatabase = mongoClient.getDatabase(metaVO.getDbname());
            return getSingleDbMsg(command, mongoDatabase);

        } catch (Exception e) {
            log.warn("Select MongoDb {} error! FuncName : {} ", e);
            throw new CustomException(600, "执行mongo 查询 变量信息失败" + e);
        }
    }

    private List<Map<String, Object>> getSingleDbMsg(String[] command, MongoDatabase mongoDatabase) {
        Map<String, Object> mongoDbInfo = new HashMap<>();
        List<Map<String, Object>> mongoDbList = new ArrayList<>();
        Document document;
        String[] commandAndParam = command[0].split("_");
        if (commandAndParam.length > 1) {
            document = mongoDatabase.runCommand(new Document(commandAndParam[0].trim(), commandAndParam[1]));
        } else {
            if (command.length == 1 && "currentOptrue".equalsIgnoreCase(command[0])) {
                document = mongoDatabase.runCommand(new Document(command[0].substring(0, 9).trim(), 1).append("$all", 1));
            } else {
                document = mongoDatabase.runCommand(new Document(command[0].trim(), 1));
            }
        }
        if (document.isEmpty()) {
            return mongoDbList;
        }

        if (command.length > 1) {
            String[] fetchMsg = command[1].split(",");
            int commandParamCount = fetchMsg.length;
            if (commandParamCount == 1) {
                if (document.get(fetchMsg[0]) instanceof List) {
                    List value = (List) document.get(fetchMsg[0]);

                    for (int i = 0; i < value.size(); i++) {
                        buildRecordByMap(value.get(i), null, mongoDbInfo);
                        mongoDbList.add(mongoDbInfo);
                        mongoDbInfo = new HashMap<>();
                    }

                    return mongoDbList;

                } else {
                    buildRecordByMap(document.get(fetchMsg[0]), null, mongoDbInfo);
                }

            } else {
                for (int i = 0; i < commandParamCount; i++) {
                    Object document1 = document.get(fetchMsg[i]);
                    buildRecordByMap(document1, fetchMsg[i].toUpperCase().trim(), mongoDbInfo);
                }
            }
        } else {
            mongoDbInfo.putAll(document);
        }

        mongoDbList.add(mongoDbInfo);

        return mongoDbList;
    }


    public void buildRecordByMap(Object document, String key, Map<String, Object> record) {
        if (org.apache.commons.lang.StringUtils.isNotBlank(key)) {
            key = key.replace(' ', '_').replace('.', '_');
        }

        if (document instanceof Map) {
            commonBuild(document, key, record);

        } else if (document instanceof List) {
            record.put(key.toUpperCase(), JsonMapper.writeValueAsString(document));

        } else if (document instanceof String) {
            record.put(org.apache.commons.lang.StringUtils.isNotBlank(key) ? key.toUpperCase() : "string_name", document);
        } else if (org.apache.commons.lang.StringUtils.isBlank(key)) {
        } else {
            if ("r".equals(key.substring(key.lastIndexOf("_") + 1))) {
                key = key + "r";
            } else if ("w".equals(key.substring(key.lastIndexOf("_") + 1))) {
                key = key + "w";
            }

            record.put(key.toUpperCase(), document);
        }
    }

    private void commonBuild(Object document, String key, Map<String, Object> record) {

        for (Object innerKey : ((Map) document).keySet()) {
            if (org.apache.commons.lang.StringUtils.isBlank(key)) {
                buildRecordByMap(((Map) document).get(innerKey), innerKey.toString(), record);
            } else {
                buildRecordByMap(((Map) document).get(innerKey), key + "_" + innerKey, record);
            }

        }
    }


    /**
     * 构建k8s相应的secret名字
     *
     * @param crName
     * @return
     */
    private String getSecretName(String crName, String userName) {
        return "mongodb-" + crName + "-" + userName + "-password";
    }

    private Boolean checkMasterClusterOplogIsSame(String masterName, String clusterName, Map<String, String> mongoDbOpTimeByRoleMap) {
        String masterDate = mongoDbOpTimeByRoleMap.get(masterName);
        String clusterDate = mongoDbOpTimeByRoleMap.get(clusterName);
        return (StringUtils.isNotEmpty(masterDate) && StringUtils.isNotEmpty(clusterDate)) && !masterDate.equals(clusterDate);
    }

    //修改pv的亲和属性为实际node
    public void updatePvAffinity(CloudApp app) {
        //获取一个实例的node
        KubeClient client = kubeClientService.get(app.getKubeId());
        //生成pv和nodeName名字的map，key是pv名称，value为nodename
        Map<String, String> pvAndNodeMap = new HashMap<>();
        List<PodDTO> pods = client.listPod(app.getNamespace(), AppUtil.getPodLabel(app));
        for (PodDTO pod : pods) {
            String suffix = pod.getPodName().replace(app.getCrName(), app.getCrName() + "-" + app.getNamespace());
            String dataPvName = DATA_VOLUME + "-" + suffix;
            String logPvName = LOGS_VOLUME + "-" + suffix;
            String backupPvName = BACKUP_VOLUME + "-" + suffix;
            pvAndNodeMap.put(dataPvName, pod.getPod().getSpec().getNodeName());
            pvAndNodeMap.put(logPvName, pod.getPod().getSpec().getNodeName());
            pvAndNodeMap.put(backupPvName, pod.getPod().getSpec().getNodeName());
        }
        //获取所有pv
        List<PersistentVolume> pvByLabels = new ArrayList<>();
        for (String label : pvAndNodeMap.keySet()) {
            Label[] labels = new Label[]{
                    new Label("mongo-pv", label)
            };
            Map<String, String> labelMap = Arrays.stream(labels).collect(Collectors.toMap(Label::getName, Label::getValue));
            List<PersistentVolume> pvs = client.listPv(labelMap).getItems();
            if(!CollectionUtils.isEmpty(pvs)){
                pvByLabels.addAll(pvs);
            }
        }

        //遍历所有pv修改亲和
        for(int i = 0;i < pvByLabels.size();i++){
            VolumeNodeAffinity nodeAffinity = pvByLabels.get(i).getSpec().getNodeAffinity();
            List<String> values = new ArrayList<>();
            String nodeName = pvAndNodeMap.get(pvByLabels.get(i).getMetadata().getName());
            values.add(nodeName);
            if(null == nodeAffinity){
                //没有亲和
                VolumeNodeAffinity volumeNodeAffinity = new VolumeNodeAffinity();
                NodeSelector required = new NodeSelector();
                List<NodeSelectorTerm> nodeSelectorTerms = new ArrayList<>();
                NodeSelectorTerm nodeSelectorTerm = new NodeSelectorTerm();
                List<NodeSelectorRequirement> matchExpressions = new ArrayList<>();
                NodeSelectorRequirement nodeSelectorRequirement = new NodeSelectorRequirement();
                nodeSelectorRequirement.setKey("kubernetes.io/hostname");
                nodeSelectorRequirement.setOperator("In");
                nodeSelectorRequirement.setValues(values);

                matchExpressions.add(nodeSelectorRequirement);

                nodeSelectorTerm.setMatchExpressions(matchExpressions);

                nodeSelectorTerms.add(nodeSelectorTerm);

                required.setNodeSelectorTerms(nodeSelectorTerms);

                volumeNodeAffinity.setRequired(required);

                pvByLabels.get(i).getSpec().setNodeAffinity(volumeNodeAffinity);
            }else{
                //有亲和
                pvByLabels.get(i).getSpec().getNodeAffinity().getRequired().getNodeSelectorTerms().get(0).getMatchExpressions().get(0).setValues(values);
            }
            clientService.get(app.getKubeId()).updatePvClaimRef(pvByLabels.get(i), app.getNamespace());
        }
    }

    protected String[] getStsOrDeployNames(CloudApp app) {
        return new String[]{app.getCrName()};
    }

    @Override
    public void delete(CloudApp app) {
        // todo
        MongoDBCommunity cr = kubeClientService.get(app.getKubeId()).listCustomResource(MongoDBCommunity.class, app.getCrName(), app.getNamespace());
        if (cr == null) return;
        cr.getSpec().setMembers(0);
        kubeClientService.get(app.getKubeId()).updateCustomResource(cr, MongoDBCommunity.class);
        super.delete(app);
    }

    @Override
    public void recreate(CloudApp app) {
        MongoDBCommunity cr = kubeClientService.get(app.getKubeId()).listCustomResource(MongoDBCommunity.class, app.getCrName(), app.getNamespace());
        MongoDBCommunity expCr = YamlEngine.unmarshal(app.getCr(), MongoDBCommunity.class);
        cr.getSpec().setMembers(expCr.getSpec().getMembers());
        kubeClientService.get(app.getKubeId()).updateCustomResource(cr, MongoDBCommunity.class);
        super.recreate(app);
    }

    @Override
    public void restore(BackupHis backupHis, Integer appId, String restoreTime, String ftpFilename, String backupType) {
        // 1. 插入恢复历史基本信息
        // 2. 获取主库
        // 3. 将备份文件复制到恢复节点
        // 4. mongo数据恢复命令调用
        // 5. 删除备份本地副本

        // 1.基本信息
        //查询出目标的app
        CloudApp app = appService.get(appId);
        //创建k8s客户端
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        //应用所属集群
        String kubeName = kubeConfigService.getName(app.getKubeId());
        //获取crRun
        MongoDBCommunity cr = kubeClientService.get(app.getKubeId()).listCustomResource(
                MongoDBCommunity.class, app.getCrName(), app.getNamespace());
        String crRun = YamlEngine.marshal(cr);
        //获取备份信息 ftp上备份文件目录、pod上恢复目录、备份文件名
        String message = backupHis.getMessage();
        JSONObject backupMessage = JSON.parseObject(message);
        String backupFilename = backupMessage.getString("backupFilename");
        String restoreDir = "/mnt/share";
        // 恢复历史记录
        Timestamp startTime = new Timestamp(System.currentTimeMillis());
        RestoreHis restoreHis = new RestoreHis() {{
            setStatus(StatusConstant.RUNNING);
            setStartTime(startTime);
            setAppId(appId);
            setAppName(app.getName());
            setAppType(app.getKind());
            setFileDeleted(false);
            setFileName(backupFilename);
            setRestoreDir(restoreDir);
            setKubeName(kubeName);
            setMessage("恢复中...");
        }};
        //插入基本信息
        backupService.commitRestoreHis(restoreHis);
        //插入操作记录
        ResourceChangeHis resourceChangeHis = new ResourceChangeHis(){{
            setInsertTime(startTime);
            setKind(app.getKind());
            setKubeId(app.getKubeId());
            setNamespace(app.getNamespace());
            setCommand("恢复");
            setStatus("2");
            setAction(ActionEnum.RESTORE.getActionType());
            setMsg("恢复中...");
            setAppId(app.getId());
            setAppLogicId(app.getLogicAppId());
            setAppName(app.getName());
            setKubeName(kubeName);
            setYaml(app.getCr());
            setLastEndTimestamp(System.currentTimeMillis());
            setUserIp(CloudRequestContext.getContext().getUserIp());
            setUserName(UserUtil.getCurrentUser().getName());
            setUserId(UserUtil.getCurrentUser().getUserid());
        }};
        Integer changeId = backupUtil.insertResourceChangeHis(resourceChangeHis);

        // 2. 获取主库
        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
        PodDTO primaryPod = backupUtil.getRestorePodMongoDB(app, kubeClient).get(0);
        if (primaryPod == null) {
            backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "mongodb恢复未获取到节点信息！");
            return;
        };
        restoreHis.setPodName(primaryPod.getPod().getMetadata().getName());

//        // 3. 将备份文件复制到恢复节点
//        //获取目标pod放置恢复文件的路径
//        Container containers = primaryPod.getPod().getSpec().getContainers().stream().filter(container ->
//                appKind.getContainerName().equals(container.getName())).findFirst().get();
//        String restoreFilePath = containers.getVolumeMounts().stream().filter(volumeMount -> "backup-volume".equals(
//                volumeMount.getName())).findFirst().get().getMountPath();
//        if (null == restoreFilePath) {
//            log.error("mongodb恢复未获取到恢复路径！");
//            restoreReturn(restoreHis, changeId, "mongodb恢复未获取到恢复路径！", StatusConstant.FAIL);
//            return;
//        }

//        checkRestoreAndRecord(resourceChangeHis, app, backupHis, restoreHis);

//        // 备份文件所在的目录
        String restoreTargetPath = backupMessage.getString("backupPodPath");
//        if (!backupHis.getAppId().equals(appId))
//            try {
//                CloudApp backupApp = appCommonService.get(backupHis.getAppId());
//                String backupFilePath = MongoUtil.getBackupRootPath(backupApp.getNamespace(), backupApp.getCrName()) + "/"
//                        + backupFilename; // 相对路径
//                String res = kubeClient.execCmd(app.getNamespace(), primaryPod.getPodName(), appKind.getContainerName(),
//                        "sh", "-c", "cp -rf " + backupFilePath + " " + restoreTargetPath);
//            } catch (Exception e) {
//                log.error("mongodb恢复调用下载脚本失败！");
//                restoreReturn(restoreHis, changeId, "mongodb恢复调用下载脚本失败！", StatusConstant.FAIL);
//                return;
//            }

        // 临时挂载
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        String restoreScript = "";
        // 4. mongo数据恢复命令调用
        // 获取dblist
        List<String> dbNames = JSONArray.parseArray(backupMessage.getString("dbNames"), String.class);
        // 遍历每一个dbname，进行恢复
        String dBListStr = dbNames.toString().replace("[", "")
                .replace("]", "").replace("\"", "").replace(" ", "");

        if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
            String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
            restoreScript = "bash /scripts/restore.sh "
                    + mountPath
                    + " " + dBListStr
                    + " " + restoreTargetPath
                    + " " + CloudAppConstant.OperatorStorageType.NFS;
        } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
            String region = cloudBackupStorageVO.getRegion();
            if (org.springframework.util.StringUtils.isEmpty(region)) {
                region = "\"\"";
            }
            restoreScript = "bash /scripts/restore.sh "
                    + cloudBackupStorageVO.getServer()
                    + " " + dBListStr
                    + " " + restoreTargetPath
                    + " " + CloudAppConstant.StorageType.S3
                    + " " + cloudBackupStorageVO.getRegion()
                    + " " + cloudBackupStorageVO.getBucket()
                    + " " + cloudBackupStorageVO.getAccessKey()
                    + " " + cloudBackupStorageVO.getSecretKey()
                    + " " + cloudBackupStorageVO.getBackupPath();
        } else {
            throw new CustomException(600, "备份失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
        }
        try {
            kubeClient.execCmd(app.getNamespace(), primaryPod.getPodName(), appKind.getContainerName(), "sh", "-c",
                    restoreScript + " > restore.log 2>&1");
        } catch (Exception e) {
            // 还原失败
            log.error("mongodb恢复失败！" + e.getMessage());
            backupUtil.restoreReturn(restoreHis, changeId, "mongodb恢复失败！", StatusConstant.FAIL);
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("appId", app.getId());
        map.put("restoreHisId", restoreHis.getRestoreHisId());
        map.put("restorePodName", primaryPod.getPodName());
        map.put("changeId", changeId);
        try {
            appService.callScheduler(app, crRun, map, ActionEnum.RESTORE,
                    MongoDBBackupAndRestoreWatch.class, resourceChangeHis);
        } catch (Exception e) {
            // 还原失败
            log.error("mongodb恢复失败！" + e.getMessage());
            backupUtil.restoreReturn(restoreHis, changeId, "mongodb恢复失败！", StatusConstant.FAIL);
            return;
        }

//        backupUtil.restoreReturn(restoreHis, changeId, "恢复成功！", StatusConstant.SUCCESS);
    }
}
