package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.cr.InnoDBCluster;
import cn.newdt.cloud.dto.Label;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.impl.InnoDBClusterService;
import cn.newdt.cloud.utils.AppUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

import static cn.newdt.cloud.utils.MGRUtil.Constants.ROLE_LABEL;

/**
 * @Author: houjun
 * @Date: 2022/6/29 - 9:18
 * @Description:
 */
@Getter
@Slf4j
public class InnoDBClusterInstallWatch extends InnoDBClusterWatch {
    @Autowired
    InnoDBClusterService innoDBClusterService;

    @Override
    public void doStopWatchResource(CloudApp app, TriggerHis triggerHis, OpsResultDTO.Builder result, InnoDBCluster cr) {
        if (result.isSuccessful()) {
            // 告警支持
            appMultiAZService.enableAlert(app);
            serviceManageOperationWatcherHelper.doStopWatchSvc(app, triggerHis, result.isSuccessful(), cr);
            Map<String, String> mergedJobDataMap = triggerHis.returnMergedJobDataMap();
            //判断是否要进行恢复操作
            if (StringUtils.isEmpty(mergedJobDataMap.get("backupHisId"))) {
                String username = mergedJobDataMap.get("username");
                String password = mergedJobDataMap.get("password");
                if (!StringUtils.isEmpty(username) && !StringUtils.isEmpty(password)) {
                    if (!dbUserService.findDbUserByName(app.getId(), username).isPresent()) {
                        dbUserService.createUser(app.getId(), username, "", CloudAppConstant.UserRole.ADMIN);
                    }
                }
                //创建dmp监控用户
                Map<String, String> dmpMonitorUser = operationUtil.createDMPMonitorUser();
                createRootUser(dmpMonitorUser.get("username"), dmpMonitorUser.get("password"), app);
            }
            // data pvc|pv label
            Label[] labels = AppKind.MYSQL_MGR.labelOfPod(app);
            KubeClient kubeClient = kubeClientService.get(app.getKubeId());
            kubeClient.listPv(Label.toMap(labels)).getItems().forEach(pv -> {
                Map<String, String> map = pv.getMetadata().getLabels();
                map.put(CloudAppConstant.CustomLabels.RESOURCE_VERSION, AppUtil.getResourceVersion(app));
                pv.getMetadata().setLabels(map);
                kubeClient.updatePv(pv);
            });

            kubeClient.listPvc(app.getNamespace(), Label.toMap(labels)).getItems().forEach(pvc -> {
                Map<String, String> map = pvc.getMetadata().getLabels();
                map.put(CloudAppConstant.CustomLabels.RESOURCE_VERSION, AppUtil.getResourceVersion(app));
                pvc.getMetadata().setLabels(map);
                kubeClient.updatePvc(pvc, app.getNamespace());
            });

            // 纳管到dmp
            if (autoManagement) {
                logInfo(app, ActionEnum.CREATE, "app will be managed into CMDB");
                result.msg("sync cloud app to DMP");
                result.msg(operationUtil.syncToDMP(app, triggerHis));
            }
        }
    }

    private void createRootUser(String username, String password, CloudApp app) {
        try {
            String createUserCmd = "mysql -uk8sadmin -pk8sadmin -e ?"; // ?作为占位符 之后被替换
            String createUserSql = String.format("CREATE USER IF NOT EXISTS '%s'@'%%' IDENTIFIED BY '%s'", username, password);
            String grantSql = String.format("GRANT ALL ON *.* TO '%s'@'%%' with grant option", username);
            String flushSql = "FLUSH PRIVILEGES";
            KubeClient client = kubeClientService.get(app.getKubeId());
            PodDTO pod = client.listPod(
                    app.getNamespace(), AppKind.MYSQL_MGR.labelOfPod(app))
                    .stream().filter(p ->
                            CloudAppConstant.ROLE_PRIMARY.equalsIgnoreCase(
                                    p.getLabels().get(ROLE_LABEL)))
                    .findFirst().orElseThrow(() -> new IllegalStateException("没有主库label, 因此创建用户未完成"));
            String[] cmd = createUserCmd.split(" ");
            cmd[cmd.length - 1] = createUserSql;
            String createUserRes = client.execCmd(app.getNamespace(), pod.getPodName(), AppKind.MYSQL_MGR.getContainerName(), cmd);
            cmd[cmd.length - 1] = grantSql;
            String grantRes = client.execCmd(app.getNamespace(), pod.getPodName(), AppKind.MYSQL_MGR.getContainerName(), cmd);
            cmd[cmd.length - 1] = flushSql;
            String flushRes = client.execCmd(app.getNamespace(), pod.getPodName(), AppKind.MYSQL_MGR.getContainerName(), cmd);
        } catch (Exception e) {
            throw new RuntimeException("create user failed, will try later. url", e);
        }
    }
}
