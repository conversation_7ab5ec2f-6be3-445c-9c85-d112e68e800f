package cn.newdt.cloud.service;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.domain.CloudSysConfig;
import cn.newdt.cloud.dto.ResourceDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.repository.KubeClientManager;
import cn.newdt.cloud.service.impl.SysConfigService;
import cn.newdt.cloud.utils.MetricUtil;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import io.fabric8.kubernetes.api.model.PodTemplateSpec;
import io.fabric8.kubernetes.api.model.Quantity;
import io.fabric8.kubernetes.api.model.ResourceRequirements;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class ResourceHelper {
    public static final double DEFAULT_CPU_PERCENTILE = 0.25;
    public static final double DEFAULT_MEMORY_PERCENTILE = 0.50;
    private final String CPU = "cpu";
    private final String MEMORY = "memory";

    private final Map<String, String> KEY_MAP = ImmutableMap.of(CPU, "RESOURCE_ALLOCATE_CPU_RATIO", MEMORY, "RESOURCE_ALLOCATE_MEM_RATIO");
    private String CM_NAME;
    private String CM_NAMESPACE;
    private String CM_KEY_PREFIX;
    private Map<String, String> percentileMap = new ConcurrentHashMap<>();

    private static ResourceHelper INSTANCE;
    private SysConfigService sysConfigStore;
    private KubeConfigService kubeConfigService;

    private ResourceHelper() {}
    public static ResourceHelper createInstance(SysConfigService service, KubeConfigService kubeConfigService1) {
        INSTANCE = new ResourceHelper();
        INSTANCE.sysConfigStore = service;
        INSTANCE.kubeConfigService = kubeConfigService1;
        return INSTANCE;
    }
    public static ResourceHelper getInstance() {
        return INSTANCE;
    }

    /**
     * 超配比例更新 同步更新k8s集群中的configMap
     * @param client if client is null, refresh only
     * @param cloudSysConfigs cpu and memory ratio sys_config
     */
    public void updateRatioOn(@Nullable KubeClient client, List<CloudSysConfig> cloudSysConfigs) {
        if (cloudSysConfigs.isEmpty())
            return;
        refresh(cloudSysConfigs);

        // update ConfigMap
        // key: resourceAllocationRatio.[cpu|memory]
        Map<String, String> ratioMap = percentileMap.keySet().stream().collect(Collectors.toMap(
                key -> KEY_MAP.get(key),
                key -> percentileMap.get(key) + ""
        ));
        if (client == null)
            return;
        GlobalConfigMap.getInstance().updateByMerging(client, ratioMap);
    }


    private void refresh(List<CloudSysConfig> cloudSysConfigs) {
        cloudSysConfigs.forEach(sysConfig -> {
            // operator 使用百分比计算，系统配置格式为比例倍数， 做一个转换
            percentileMap.put(sysConfig.getName(), 1.0 / Double.parseDouble(sysConfig.getData()) + "");
        });
    }

    public void updateRatioOnAll(List<CloudSysConfig> cloudSysConfigs) {
        kubeConfigService.findEnabled(null).parallelStream()
                .map(cfg->KubeClientManager.instance(null).createClient(cfg))
                .forEach(client -> updateRatioOn(client, cloudSysConfigs));
    }

     /**
     * 根据limit计算request.
     */
    public ResourceRequirements resourceRequirements(ResourceDTO limits) {
        return resourceRequirements(ImmutableMap.of(CPU, limits.getCpu(), MEMORY, limits.getMemory()));
    }

    /**
     * 根据limit计算request
     * @return ResourceRequirements 对应container.resource对象
     */
    public ResourceRequirements resourceRequirements(Map<String, String> limits) {
        double cpu = Optional.ofNullable(percentileMap.get(CPU))
                .map(Double::parseDouble).orElse(DEFAULT_CPU_PERCENTILE);
        double memory = Optional.ofNullable(percentileMap.get(MEMORY))
                .map(Double::parseDouble).orElse(DEFAULT_MEMORY_PERCENTILE);

        ResourceRequirements resourceRequirements = new ResourceRequirements();

        resourceRequirements.setLimits(ImmutableMap.of(
                CPU, new Quantity(MetricUtil.formatCpu(limits.get(CPU))),
                MEMORY, new Quantity(MetricUtil.formatResource(limits.get(MEMORY)))
        ));
        Quantity parse = Quantity.parse(limits.get(MEMORY));
        resourceRequirements.setRequests(ImmutableMap.of(
                CPU, new Quantity(MetricUtil.formatCpu(MetricUtil.getCpuMilliCores(limits.get(CPU)) * cpu + "m")),
                MEMORY, new Quantity(MetricUtil.formatResource(Double.parseDouble(parse.getAmount()) * memory + parse.getFormat()))
        ));
        return resourceRequirements;
    }

    // copy from cloudappservice
    public List<PodTemplateSpec> getAppComponentTemplates(AppKind kind, @Nullable String component) {
        String configMapYaml = sysConfigStore.findOne(CloudAppConstant.SysCfgCategory.POD_TEMPLATE, "app.pod.template");
        JSONObject podTemplateObj = JSONObject.parseObject(configMapYaml);
        StringBuilder keyPrefix = new StringBuilder();
        keyPrefix.append(kind.getKind()).append("-").append(kind.getArch()).append("-");
        if (StringUtils.isEmpty(component)) {
            component = "DB";
        }
        if (!"All".equalsIgnoreCase(component))
            keyPrefix.append(component.toUpperCase());

        return podTemplateObj.keySet().stream().filter(key -> key.startsWith(keyPrefix.toString()))
                .map(key -> {
                    String podTemplateString = podTemplateObj.getString(key);
                    return YamlEngine.unmarshal(podTemplateString, PodTemplateSpec.class);
                }).collect(Collectors.toList());
    }

    /**
     * 返回指定类型非主容器的默认资源请求，默认值来自模板或limitrange默认值，包括limit和request
     * @return ResourceRequirements 列表, 每个代表一个容器的资源
     */
    public List<ResourceRequirements> getResourceRequirementOfSidecars(AppKind kind, @Nullable String component) {
        List<PodTemplateSpec> appComponentTemplates = getAppComponentTemplates(kind, component);

        Map<Boolean, Map<String, String>> requestAndLimits =
                sysConfigStore.find(CloudAppConstant.SysCfgCategory.LIMIT_RANGE).stream()
                        .collect(Collectors.partitioningBy(sys -> sys.getName().contains("request"),
                                Collectors.toMap(sys -> sys.getName().replaceAll(".*(request|limit)\\.", ""),
                                        sys -> sys.getData())));

        return appComponentTemplates.stream().flatMap(spec -> spec.getSpec().getContainers().stream())
                .filter(c -> !c.getName().equals(kind.getContainerName())) // filter all sidecar container
                .map(c -> {
                    // 如果模板中没有配置，用limitrange替换。
                    if (c.getResources() == null)
                        c.setResources(new ResourceRequirements());
                    Map<String, Quantity> requests = c.getResources().getRequests();
                    if (requests == null) {
                        requests = new HashMap<>();
                        c.getResources().setRequests(requests);
                    }
                    Map<String, String> cpuLR = requestAndLimits.get(Boolean.TRUE);
                    if (cpuLR.containsKey(CPU))
                        requests.putIfAbsent(CPU, new Quantity(cpuLR.get(CPU)));
                    if (cpuLR.containsKey(MEMORY))
                        requests.putIfAbsent(MEMORY, new Quantity(cpuLR.get(MEMORY)));
                    Map<String, Quantity> limits = c.getResources().getLimits();
                    if (limits == null) {
                        limits = new HashMap<>();
                        c.getResources().setLimits(limits);
                    }
                    Map<String, String> memLR = requestAndLimits.get(Boolean.FALSE);
                    if (memLR.containsKey(CPU))
                        limits.putIfAbsent(CPU, new Quantity(memLR.get(CPU)));
                    if (memLR.containsKey(MEMORY))
                        limits.putIfAbsent(MEMORY, new Quantity(memLR.get(MEMORY)));

                    return c.getResources();
                }).collect(Collectors.toList());
    }
}
