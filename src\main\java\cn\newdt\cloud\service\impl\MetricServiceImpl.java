package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.config.CloudRequestContext;
import cn.newdt.cloud.config.CustomerConfigProperties;
import cn.newdt.cloud.config.FutureService;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.CloudQuota;
import cn.newdt.cloud.domain.CloudTenant;
import cn.newdt.cloud.domain.KubeConfig;
import cn.newdt.cloud.dto.NodeDTO;
import cn.newdt.cloud.mapper.CloudQuotaMapper;
import cn.newdt.cloud.mapper.CloudTenantMapper;
import cn.newdt.cloud.repository.MetricClient;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.csi.CSILoader;
import cn.newdt.cloud.service.csi.CSIUtil;
import cn.newdt.cloud.service.csi.CSInterface;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.*;
import cn.newdt.commons.utils.UserUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import io.fabric8.kubernetes.api.model.Quantity;
import io.fabric8.kubernetes.api.model.ResourceQuota;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 一些指标从prometheus和k8s都可以获取，考虑策略使用两个metricclient，例如当用户不具备所需的资源权限从prometheus查或者prometheus访问
 * 从k8s获取。
 */
@Slf4j
@Service
public class MetricServiceImpl implements MetricService, InitializingBean {
    private static final int STEP = 300;
    @Autowired
    private CloudAppService appService;
    @Autowired
    private MetricClient client;
    @Autowired
    private KubeClientService clientService;
    @Autowired
    private TenantService tenantService;
    @Autowired
    private KubeClientService kubeClientService;
    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private CloudTenantMapper cloudTenantMapper;

    @Autowired
    private CloudQuotaService quotaService;

    @Autowired
    private CloudAppService cloudAppService;

    @Autowired
    private CustomerConfigProperties customerConfigProperties;

    @Autowired
    private KubeSchedulerService kubeSchedulerService;

    @Autowired
    private KubeConfigService configService;

    @Autowired
    CloudQuotaMapper cloudQuotaMapper;

    @Autowired
    private KubeConfigService kubeConfigService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public MetricClient getMetricClient() {
        return client;
    }

    @Override
    public MetricVO queryDisk(long timestamp, String kind, int kubeId, Integer tenantId) {
        log.info(String.format("query storage {timestamp:%s, kind:%s, kubeId:%s}", timestamp, kind, kubeId));
        return client.queryDisk(timestamp, kubeId, collectPodNames(kind, kubeId));
    }

    @Override
    public MetricVO queryMemory(long timestamp, String kind, int kubeId, Integer tenantId) {
        log.info(String.format("query memory {timestamp:%s, kind:%s, kubeId:%s}", timestamp, kind, kubeId));
        Set<String> podNames = collectPodNames(kind, kubeId);
        return client.queryMemory(timestamp, kind, kubeId, podNames);
    }

    @Override
    public MetricVO queryCpu(long timestamp, String kind, int kubeId, Integer tenantId) {
        log.info(String.format("query cpu {timestamp:%s, kind:%s, kubeId:%s}", timestamp, kind, kubeId));
        Set<String> podNames = collectPodNames(kind, kubeId);
        return client.queryCpu(timestamp, kind, kubeId, podNames);
    }

    @Override
    public Object queryRangeCpu(long start, long end, int step, String kind, Integer kubeId) {
        // 按类型和集群获取应用
        // 定义不同类型的pod name template.
        // 从prometheus查询时序数据. 范围限定为相关的pod.
        Set<String> podPatterns = collectPodPatterns(kind, kubeId);//collectPodNames(kind, kubeId);
        kubeId = getFederatedPromKubeIdForRangeQuery(kubeId); // todo

        return MetricUtil.formatChartData(client.queryRangeCpuByPods(start, end, step, podPatterns, kind, kubeId), "cpu");
    }

    /**
     * @param kubeId only if kubeId is null
     * @return kubeId which has federated prometheus
     */
    private Integer getFederatedPromKubeIdForRangeQuery(Integer kubeId) {
        if (kubeId == null)
            kubeId = Integer.valueOf(sysConfigService.findOne("alertConfig", "federate_kube_id"));
        return kubeId;
    }

    @Override
    public Object queryRangeMemory(long start, long end, int step, String kind, Integer kubeId) {
        Set<String> podNames = collectPodPatterns(kind, kubeId);
        kubeId = getFederatedPromKubeIdForRangeQuery(kubeId);

        return MetricUtil.formatChartData(client.queryRangeMemoryByPods(start, end, step, podNames, kind, kubeId), "内存");
    }

    @Override
    public Object queryRangeDisk(long start, long end, int step, String kind, Integer kubeId) {
        // find all pods of cloud_app.
        Set<String> podNames = collectPodPatterns(kind, kubeId); // todo 范围查询不应用时点pod限制.应查询范围内的历史pod集
        kubeId = getFederatedPromKubeIdForRangeQuery(kubeId);

        return MetricUtil.formatChartData(client.queryRangeDiskByPods(start, end, step, podNames, kind, kubeId), "存储");
    }

    @Override
    public List<MetricVO.ValueVO> queryCpuByPods(long timestamp, Set<String> podNames, int kubeId) {
        return client.queryCpuByPods(timestamp, podNames, kubeId, MetricClient.CPU_USAGE);
    }

    @Override
    public List<MetricVO.ValueVO> queryMemoryByPods(long timestamp, Set<String> podNames, int kubeId) {
        return client.queryMemoryByPods(timestamp, podNames, kubeId, MetricClient.MEMORY_USAGE);
    }

    @Override
    public List<MetricVO.ValueVO> queryDiskByPods(long timestamp, Set<String> podNames, int kubeId) {
        List<Map<String, Object>> pvcInfos = client.queryPvcsByPods(timestamp, podNames, kubeId, MetricClient.PVC_INFO);
        Set<String> pvcSet = pvcInfos.stream()
                .map(pvc -> pvc.get("namespace") + "/" + pvc.get("pvc") + "").collect(Collectors.toSet());
        Map<Object, Object> pvcPodMap = pvcInfos.stream().collect(Collectors.toMap(pvc -> pvc.get("pvc"), pvc -> pvc.get("pod"),
                (exist, new_) -> exist));
        List<MetricVO.ValueVO> valueVOS = client.queryPvsByPvc(timestamp, pvcSet, kubeId, MetricClient.PVC_USAGE);
        valueVOS.stream().forEach(m -> {
            String pvcName = (String) m.getLabels().get("persistentvolumeclaim");
            m.setPod((String) pvcPodMap.get(pvcName));
            m.setPvc(pvcName);
            m.setNamespace((String) m.getLabels().get("namespace"));
        });
        return valueVOS;
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    public Set<String> collectPodNames(String kind, int kubeId) {
        if (StringUtils.isEmpty(kind)) {
            HashMap map = new HashMap();
            map.put("kind", kind);
            map.put("kubeId", kubeId);
            return collectPodNames(map);
        } else {
            // kind可以包含多个用逗号分隔, e.g. Redis,Redis-Sentinel
            HashMap map = new HashMap();
            map.put("kubeId", kubeId);
            Set<String> podNamesOfAllKind = new HashSet<>();
            for (String each : kind.split(",")) {
                map.put("kind", each);
                podNamesOfAllKind.addAll(collectPodNames(map));
            }
            return podNamesOfAllKind;
        }
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    public Set<String> collectPodNamesForCpu(String kind, int kubeId) {
        if (StringUtils.isEmpty(kind)) {
            HashMap map = new HashMap();
            map.put("kind", kind);
            map.put("kubeId", kubeId);
            return collectPodNamesForCpu(map);
        } else {
            // kind可以包含多个用逗号分隔, e.g. Redis,Redis-Sentinel
            HashMap map = new HashMap();
            map.put("kubeId", kubeId);
            Set<String> podNamesOfAllKind = new HashSet<>();
            for (String each : kind.split(",")) {
                map.put("kind", each);
                podNamesOfAllKind.addAll(collectPodNamesForCpu(map));
            }
            return podNamesOfAllKind;
        }
    }

    /**
     * @deprecated
     */
    @Deprecated
    public Set<String> collectPodNames(Map map) {
        List<CloudApp> apps = appService.findNotDeletedList(map);
        Set<String> podNames = new HashSet<>();
        for (CloudApp app : apps) {
            podNames.addAll(AppKind.valueOf(app.getKind(), app.getArch()).genPodNames(app, app.getIpNode()).stream()
                    .map(i->app.getNamespace() + "/" + i).collect(Collectors.toSet()));
        }
        if (podNames.isEmpty()) {
            log.warn("pod name collection is empty, map = " + JsonUtil.toJson(map));
        }
        return podNames;
    }

    @Deprecated
    public Set<String> collectPodNamesForCpu(Map map) {
        List<CloudApp> apps = appService.findNotDeletedListNoNamespace(map);
        Set<String> podNames = new HashSet<>();
        for (CloudApp app : apps) {
            podNames.addAll(AppKind.valueOf(app.getKind(), app.getArch()).genPodNames(app, app.getIpNode()).stream()
                    .map(i -> app.getNamespace() + "/" + i).collect(Collectors.toSet()));
        }
        if (podNames.isEmpty()) {
            log.warn("pod name collection is empty, map = " + JsonUtil.toJson(map));
        }
        return podNames;
    }

    /**
     * 收集pod前缀用于在prometheus中匹配pod（包括删除的pod）
     *
     * @param kind
     * @param kubeId
     * @return operator创建的pod名称前缀, 即prometheus api中的regex pattern
     */
    public Set<String> collectPodPatterns(String kind, Integer kubeId) {
        HashMap map = new HashMap();
        if (StringUtils.isEmpty(kind)) {
            map.put("kind", kind);
            map.put("kubeId", kubeId);
            return collectPodPatterns(map);
        } else {
            // kind可以包含多个用逗号分隔, e.g. Redis,Redis-Sentinel
            map.put("kubeId", kubeId);
            Set<String> podPrefix = new HashSet<>();
            for (String each : kind.split(",")) {
                map.put("kind", each);
                podPrefix.addAll(collectPodPatterns(map));
            }
            return podPrefix;
        }
    }

    public Set<String> collectPodPatterns(Map map) {
        List<CloudApp> apps = appService.findNotDeletedList(map);
        Set<String> podPrefixs = new HashSet<>();
        for (CloudApp app : apps) {
            for (String podPattern : AppKind.valueOf(app.getKind(), app.getArch()).getPodPattern(app)) {
                podPrefixs.add(app.getNamespace() + "/" + podPattern);
            }
        }
        if (podPrefixs.isEmpty()) {
            log.warn("pod name collection is empty, map = " + JsonUtil.toJson(map));
        }
        return podPrefixs;
    }

    @Override
    public Object queryRangeOfApp(int id, long start, long end, String metric, int kubeId, String kind, int step) {
        Set<String> pods = collectPodPatterns(Collections.singletonMap("id", id));
        CloudApp app = appService.get(id);
        switch (metric) {
            case "cpu":
                return client.queryRangeCpuByPods(start, end, step, pods, kind, kubeId);
            case "memory":
                return client.queryRangeMemoryByPods(start, end, step, pods, kind, kubeId);
            case "disk":
                return client.queryRangeDiskByPodsData(start, end, step, pods, kind, kubeId, app);
            case "backupDisk":
                return client.queryRangeDiskByPodsBackup(start, end, step, pods, kind, kubeId, app);
            default:
                log.error("unsupported metric type");
                return null;
        }
    }

    @Override
    public Object queryRangeOfAppPerPod(CloudApp app, long start, long end, int step,
                                        String metricType) {
        Set<String> podNamePrefix = collectPodPatterns(Collections.singletonMap("id", app.getId()));
        if ("cpu".equals(metricType) || "memory".equals(metricType))
            return queryRangeOfCpuMemoryMetricPerPod(app, start, end, step, podNamePrefix, metricType);
        if ("disk".equals(metricType) || "backupDisk".equals(metricType)) {
            return queryRangeOfDiskPerPod(app, start, end, step, podNamePrefix, metricType);
        }
        throw new IllegalArgumentException();
    }

    /**
     * <p>dcp 报表，查询单库应用所有pod的某一类型指标，同一类型下可能包含多个指标
     * <p>e.g. io read and write bytes metrics in network io category.
     * <p>结果按pod分组，仅计算主容器, 不同类型考虑的统计pod策略可能不同, e.g. MySQL, 主库; RedisCluster, 所有主分片
     */
    @Override
    @SuppressWarnings({"unchecked"})
    public Object queryAppReport(CloudApp app, long start, long end, int step,
                                 String metricType, String hostName) {
        Map<String, String> metricDict = this.metricIndexer.get(metricType);
        Collection<String> metrics = metricDict.keySet();
        // 每种指标大类包含多个具体的指标项
        List<List<List<MetricVO.RangeValueEntry>>> responseOfEachMetric =
                metrics.stream().parallel()
                .map(metric -> {
                    // result 二维数组，[pod series][time series]，每个pod一个该指标的时间序列
                    List<List<MetricVO.RangeValueEntry>> metricsOfPods =
                            (List<List<MetricVO.RangeValueEntry>>) client.queryRangeCpuMemoryPerPod(start, end, step,
                            app.getKind(), app.getKubeId(), ImmutableSet.of(app.getNamespace() + "/" + hostName), metric);
                    return metricsOfPods;
                }).collect(Collectors.toList());

        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                .withZone(ZoneId.systemDefault());

        // for same pod, merge all metrics at same time
        // k: pod, V: [{checktime, metric1, metric2}]
        Map<String, Map<String, MergedMetric>> podReportMap = new HashMap<>();
        int i = 0;
        for (String metric : metrics) {
            if (responseOfEachMetric.size() <= i) break;
            List<List<MetricVO.RangeValueEntry>> podsOfMetric = responseOfEachMetric.get(i++);
            for (List<MetricVO.RangeValueEntry> timeseries : podsOfMetric) {
                for (MetricVO.RangeValueEntry metricData : timeseries) {
                    String time = DateUtil.format(metricData.getTime() * 1000, dateTimeFormatter);
                    String podName = metricData.getLabel();
                    podReportMap
                            .computeIfAbsent(podName, k -> new TreeMap<>())
                            .computeIfAbsent(time, k -> new MergedMetric(time))
                            .addMetric(metricDict.getOrDefault(metric, metric), format(metric, metricData.getValue()));
                }
            }
        }
        Map<String, Object> podReport = new HashMap<>();
        for (String pod : podReportMap.keySet()) {
            Map<String, MergedMetric> stringMergedMetricMap = podReportMap.get(pod);
            List<ImmutableMap<Object, Object>> checktime = stringMergedMetricMap.keySet().stream().map(time -> {
                MergedMetric mergedMetric = stringMergedMetricMap.get(time);
                ImmutableMap.Builder<Object, Object> builder = ImmutableMap.builder();
                builder.put("CHECKTIME", mergedMetric.getTime());
                for (Map.Entry<String, Double> kv : mergedMetric.getMetrics().entrySet()) {
                    builder.put(kv.getKey(), kv.getValue());
                }
                return builder.build();
            }).collect(Collectors.toList());

            podReport.put(pod, checktime);
        }

        return podReport.get(hostName);
    }

    private double format(String metric, double value) {
        if (metric.contains("memory")) {
            value = new BigDecimal(value / 1024 / 1024).setScale(2, RoundingMode.HALF_UP).doubleValue();
        }
        return value;
    }

    public static class MergedMetric {
        String time;

        public String getTime() {
            return time;
        }

        public Map<String, Double> getMetrics() {
            return metrics;
        }

        Map<String, Double> metrics;

        public MergedMetric() {
        }

        public MergedMetric(String time) {
            this.time = time;
            this.metrics = new HashMap<>();
        }

        public void addMetric(String metricName, double value) {
            metrics.put(metricName, value);
        }

        @Override
        public String toString() {
            return "MergedMetric{" +
                    "time='" + time + '\'' +
                    ", metrics=" + metrics +
                    '}';
        }
    }

    private final Map<String, Map<String, String>> metricIndexer = ImmutableMap.of(
            "cpu", ImmutableMap.of("container_cpu_usage_system", "CPU系统使用率", "container_cpu_usage_user", "CPU用户使用率"),
            "memory", ImmutableMap.of("container_memory_free", "空闲内存", "container_memory_swap", "已用SWAP",
                    "container_memory_cache", "BUFF/CACHE", "container_memory_usage", "已使用内存"),
            "network", ImmutableMap.of("container_network_received", "RECV", "container_network_send", "SEND")
//                "disk", ImmutableList.of("container_cpu_usage_system", "container_cpu_usage_user")
    );

    @Override
    public Map<String, Double> queryClusterMetric(Integer id) {
        Set<String> nodeNames = clientService.get(id).listNodes().stream().map(NodeDTO::getNodeName).collect(Collectors.toSet());
        return client.queryClusterMetric(id, nodeNames);
    }

    @Override
    public MetricVO queryNodeCpu(Integer kubeId, String nodeName, long timestamp) {
        return client.queryNodeCpu(kubeId, nodeName, timestamp);
    }

    @Override
    public MetricVO queryNodeMem(Integer kubeId, String nodeName, long timestamp) {
        return client.queryNodeMem(kubeId, nodeName, timestamp);
    }

    @Override
    public List<MetricVO.ValueVO> queryCpuLimitByPods(long now, Set<String> podNameCollection, Integer kubeId) {
        return client.queryCpuByPods(now, podNameCollection, kubeId, MetricClient.CPU_CAPACITY);
    }

    @Override
    public List<MetricVO.ValueVO> queryMemoryLimitByPods(long now, Set<String> podNameCollection, Integer kubeId) {
        return client.queryMemoryByPods(now, podNameCollection, kubeId, MetricClient.MEMORY_CAP);
    }

    @Override
    public List<MetricVO.ValueVO> queryDiskLimitByPods(long timestamp, Set<String> podNameCollection, Integer kubeId) {
        List<Map<String, Object>> pvcInfos = client.queryPvcsByPods(timestamp, podNameCollection, kubeId, MetricClient.PVC_INFO);
        Set<String> pvcSet = pvcInfos.stream()
                .map(pvc -> pvc.get("namespace") + "/" + pvc.get("pvc") + "").collect(Collectors.toSet());
        Map<Object, Object> pvcPodMap = pvcInfos.stream().collect(Collectors.toMap(pvc -> pvc.get("pvc"), pvc -> pvc.get("pod"),
                (exist, new_) -> exist));
        List<MetricVO.ValueVO> valueVOS = client.queryPvsByPvc(timestamp, pvcSet, kubeId, MetricClient.PVC_CAP);
        valueVOS.stream().forEach(m -> {
            String pvcName = (String) m.getLabels().get("persistentvolumeclaim");
            m.setPod((String) pvcPodMap.get(pvcName));
            m.setPvc(pvcName);
            m.setNamespace((String) m.getLabels().get("namespace"));
        });
        return valueVOS;
    }

    @Override
    public MetricVO queryAppMetric(long timestamp, String kind, int kubeId, String type, Integer tenantId) {

        switch (type) {
            case "cpu":
                return queryCpu(timestamp, kind, kubeId, tenantId);
            case "memory":
                return queryMemory(timestamp, kind, kubeId, tenantId);
            case "disk":
                return queryDisk(timestamp, kind, kubeId, tenantId);
        }
        return null;
    }

    @Override
    public Map<String, MetricVO> queryTenantQuota(int tenantId, int kubeId, long timestamp) {
        CloudTenant tenant = tenantService.findByIdWithQuota(tenantId);
//        tenant.getQuotas().stream().filter(quota -> quota.getKubeId() == tenantId).findAny()
        ResourceQuota resourceQuota = clientService.get(kubeId).getResourceQuota(tenant.getNamespace(), tenant.getNamespace() + "-resource-quota");

        if (resourceQuota == null) {
            // 没有配额时，总量为集群总量，申请量为所有pod汇总
            Map<String, MetricVO> result = new HashMap<>();
            if (resourceQuota == null) {
                return result;
            }
            try {
                // 查询namespace pod汇总返回的limit为租户申请汇总
                long now = Instant.now().toEpochMilli();
                FutureService futureService = new FutureService();
                AtomicReference<Double> namespaceCpuRequest = new AtomicReference<>();
                AtomicReference<Double> namespaceMemRequest = new AtomicReference<>();
                AtomicReference<Double> namespacePvcRequest = new AtomicReference<>();
                String nodeNameJoinStr = clientService.get(kubeId).listNodes().stream().map(NodeDTO::getNodeName).collect(Collectors.joining("|"));
                futureService.submit(() -> namespaceCpuRequest.set(client.queryInstantV2(String.format(getMetricSrcLabel(kubeId, MetricClient.TENANT_CPU_CAP), tenant.getNamespace(), nodeNameJoinStr), now, kubeId)));
                futureService.submit(() -> namespaceMemRequest.set(client.queryInstantV2(String.format(getMetricSrcLabel(kubeId, MetricClient.TENANT_MEM_CAP), tenant.getNamespace(), nodeNameJoinStr), now, kubeId)));
                // fixme poc only filter nfs-client
                futureService.submit(() -> namespacePvcRequest.set(queryDisk(now, null, kubeId, tenantId).getCapacity().getValue()));
//                futureService.submit(() -> namespacePvcRequest.set(client.queryInstantV2(String.format(getMetricSrcLabel(kubeId, MetricClient.TENANT_STORAGE_CAP), tenant.getNamespace()), now, kubeId)));
                futureService.await();

                // 查询节点总量, K为cpu | memory
                Map<String, Double> cpuMemCapacityMap = queryNodesCpuMemory(kubeId, 0l).stream()
                        .flatMap(n -> n.getMetrics().get("capacity").entrySet().stream())
                        .collect(Collectors.groupingBy(Map.Entry::getKey, Collectors.summingDouble(entry->((Number)entry.getValue()).doubleValue())));

                Double cpuCapacity = cpuMemCapacityMap.get("cpu");
                Double memoryCapacity = cpuMemCapacityMap.get("memory");
                Double diskCapacity = queryNodesDisk(kubeId, null, null).values().stream()
                        .flatMap(metric -> metric.values().stream()).mapToDouble(metric -> metric.getCapacity().getValue()).sum();
                result.put("limits.cpu", new MetricVO() {{
                    setRequest(ValueVO.builder().value(namespaceCpuRequest.get()).build());
                    setCapacity(ValueVO.builder().value(cpuCapacity).build());
                }});
                result.put("limits.memory", new MetricVO() {{
                    setRequest(ValueVO.builder().value(namespaceMemRequest.get()).build());
                    setCapacity(ValueVO.builder().value(memoryCapacity).build());
                }});
                result.put("requests.storage", new MetricVO() {{
                    setRequest(ValueVO.builder().value(namespacePvcRequest.get()).build());
                    setCapacity(ValueVO.builder().value(diskCapacity).build());
                }});
            } catch (Exception e) {
                log.error("", e);
            }
            return result;
        } else {
            Map<String, Quantity> hard = resourceQuota.getStatus().getHard();
            Map<String, Quantity> used = resourceQuota.getStatus().getUsed();
            return parseQuotaMetric(hard, used);
        }
        // resource quota metric can also get from prometheus
    }

    @Override
    public List<NodeDTO> queryNodesCpuMemory(int kubeId, long timestamp) {
        CompletableFuture<? extends Map<Object, ? extends Number>> cpuUsageFuture =
                CompletableFuture.supplyAsync(() -> queryCpuUsage(kubeId, timestamp));

        CompletableFuture<? extends Map<Object, ? extends Number>> memUsageFuture =
                CompletableFuture.supplyAsync(() -> queryMemoryUsage(kubeId, timestamp));

        CompletableFuture<List<NodeDTO>> describeNodeFuture =
                CompletableFuture.supplyAsync(() -> clientService.get(kubeId).describeNodes(null, null));

        Map<Object, ? extends Number> nodeCpuUsage = cpuUsageFuture.join();
        Map<Object, ? extends Number> nodeMemUsage = memUsageFuture.join();
//        List<MetricVO.ValueVO> cap = client.queryNodesCpuMemoryCap(kubeId, timestamp); // 等于node.status.allocatable
        // 这里也可以查 prometheus, 指标:kube_pod_container_resource_requests
        return describeNodeFuture.join().stream().map(nodeDTO -> {
            ImmutableMap<String, Long> cap =
                    ImmutableMap.of("cpu", nodeDTO.getCpuTotal() / 1000, "memory", nodeDTO.getMemTotal());
            ImmutableMap<String, Long> request =
                    ImmutableMap.of("cpu", nodeDTO.getCpuRequest() / 1000, "memory", nodeDTO.getMemRequest());
            ImmutableMap<String, ? extends Number> usage = NullableImmutableMap.of("cpu", nodeCpuUsage.get(nodeDTO.getNodeName()),
                            "memory", nodeMemUsage.get(nodeDTO.getNodeName()));
            nodeDTO.setMetrics(ImmutableMap.of("request", request, "capacity", cap, "usage", usage));
            return nodeDTO;
        }).collect(Collectors.toList());
    }

    private Map<Object, ? extends Number> queryMemoryUsage(int kubeId, long timestamp) {
        MetricClient.Response<MetricClient.VectorMetric> memUsageResponse =
                client.queryInstant(String.format(getMetricSrcLabel(kubeId, MetricClient.NODE_MEM_USAGE), ".+"),
                        timestamp, kubeId);
        Map<Object, ? extends Number> nodeMemUsage = memUsageResponse.getData().getResult().stream()
                .collect(Collectors.toMap(vm -> vm.getMetric().get("instance"),
                        vm -> NumberUtils.toLong(((String)vm.getValue()[1]))));
        return nodeMemUsage;
    }

    private Map<Object, ? extends Number> queryCpuUsage(int kubeId, long timestamp) {
        MetricClient.Response<MetricClient.VectorMetric> cpuUsageResponse =
                client.queryInstant(String.format(getMetricSrcLabel(kubeId, MetricClient.NODE_CPU_USAGE), ".+"),
                        timestamp, kubeId);
        Map<Object, ? extends Number> nodeCpuUsage = cpuUsageResponse.getData().getResult().stream()
                .collect(Collectors.toMap(vm -> vm.getMetric().get("instance"),
                        vm -> NumberUtils.toDouble(((String)vm.getValue()[1]))));
        return nodeCpuUsage;
    }

    @Override
    public Object queryRangeOfCpuMemoryMetricPerPod(CloudApp app, long start, long end, int step, Set<String> podNamePrefix,
                                                    String metricType) {
        String metricLabel;
        if ("cpu".equals(metricType))
            metricLabel = MetricClient.CPU_USAGE;
        else if ("memory".equals(metricType))
            metricLabel = MetricClient.MEMORY_USAGE;
        else throw new IllegalArgumentException();

        return client.queryRangeCpuMemoryPerPod(start, end, step,
                "all", app.getKubeId(), new HashSet<>(podNamePrefix), metricLabel);
    }

    @Override
    public Object queryRangeOfPodPerContainer(CloudApp app, Integer kubeId, String podName, long start, long end, int step, String metricType, AppKind kind) {
        String metricLabel;
        Pattern pattern = null;
        if ("disk".equals(metricType))
            pattern = AppKind.valueOf(app.getKind(), app.getArch()).getPvcPatterns(app).get(CloudAppConstant.PvcCategory.DATA);
        if("backupDisk".equals(metricType))
            pattern = AppKind.valueOf(app.getKind(), app.getArch()).getPvcPatterns(app).get(CloudAppConstant.PvcCategory.BACKUP);
        switch (metricType) {
            case "cpu":
                metricLabel = MetricClient.CPU_USAGE_CONTAINER;
                break;
            case "memory":
                metricLabel = MetricClient.MEMORY_USAGE_CONTAINER;
                break;
            case "disk":
                metricLabel = MetricClient.PVC_USAGE;
                return client.queryRangeMetricPVCOfPod(start, end, kubeId, ImmutableSet.of(podName), pattern);
            default:
                throw new IllegalArgumentException();
        }

        return client.queryRangeOfContainer(kubeId, podName, start, end, step, metricLabel, kind.getKind());
    }

    @Override
    public String getMetricSrcLabel(int kubeId, String label) {
        return client.getMetricSrcLabel(kubeId, label);
    }

    @Override
    public Object queryRangeOfDiskPerPod(CloudApp app, long start, long end, int step, Set<String> podNamePrefix,
                                         String metricType) {
        Pattern pattern = null;
        if ("disk".equals(metricType))
            pattern = AppKind.valueOf(app.getKind(), app.getArch()).getPvcPatterns(app).get(CloudAppConstant.PvcCategory.DATA);
        if("backupDisk".equals(metricType))
            pattern = AppKind.valueOf(app.getKind(), app.getArch()).getPvcPatterns(app).get(CloudAppConstant.PvcCategory.BACKUP);
        return client.queryRangeDiskPerPod(start, end, step,
                app.getKind(), app.getKubeId(), new HashSet<>(podNamePrefix), metricType, pattern);
    }

    private Map<String, MetricVO> parseQuotaMetric(Map<String, Quantity> hard, Map<String, Quantity> used) {
        Map<String, MetricVO> metricVOMap = new HashMap<>();
        hard.keySet().forEach(kind -> {
            MetricVO metricVO = new MetricVO();
            if (kind.equals(CloudAppConstant.QuotaItems.LIMIT_CPU)) {
                metricVO.setCapacity(new MetricVO.ValueVO(null, (double) MetricUtil.getCpuMilliCores(hard.get(kind).toString()) / 1000));
                metricVO.setRequest(new MetricVO.ValueVO(null, (double) MetricUtil.getCpuMilliCores(used.get(kind).toString()) / 1000));
            } else {
                metricVO.setCapacity(new MetricVO.ValueVO(null, (double) MetricUtil.getResourceLongValue(hard.get(kind).toString())));
                metricVO.setRequest(new MetricVO.ValueVO(null, (double) MetricUtil.getResourceLongValue(used.get(kind).toString())));
            }
            metricVOMap.put(kind, metricVO);
        });

        return metricVOMap;
    }

    @Override
    public Map<String, Map<String, MetricVO>> queryNodesDisk(int kubeId, List<String> nullableNodeNames, CSInterface csiType) {
        List<CSInterface> all = CSILoader.getAll();
        if (csiType != null)
            all = all.stream().filter(csi -> csi.getType().equals(csiType.getType())).collect(Collectors.toList());
        return all.parallelStream().filter(csInterface -> !CSIUtil.isDynamicCsi(csInterface))
                .collect(Collectors.toMap(csi -> csi.getType(), csi -> {
                    try {
                        Map<String, MetricVO> nodeMetrics = csi.getNodeMetrics(client, kubeClientService.get(kubeId), kubeId);
                        if (nodeMetrics != null) return nodeMetrics;
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                    return Collections.emptyMap();
                }));

    }

    /**
     * 对metric label
     */
    private List<MetricClient.Metric> filter(Collection<String> set, MetricClient.Response<MetricClient.Metric> response, String labelMeta) {
        List<MetricClient.Metric> collect = response.getData().getResult();
        if (set != null) {
            collect = response.getData().getResult().stream().filter(metric -> set.contains(metric.getMetric().get(labelMeta))).collect(Collectors.toList());
        }
        return collect;
    }

//    public Object readResponse(MetricClient.Response response, Set<String> set, String labelMeta) {
//        List<MetricClient.Metric> metrics = filter(set, response, labelMeta);
//        if ("matrix".equals(response.getData().getResultType())) {
//            // [[1619493276.542, "33980416"], [1619493286.542, "33980416"]]
//            // [[1619493266.542, "33980016"], [1619493276.542, "33980416"], [1619493286.542, "33980416"]]
//            List<Object[][]> values = metrics.stream().map(o -> ((MetricClient.MatrixMetric) o).getValues()).collect(Collectors.toList());
//            if (values.isEmpty()) {
//                return null;
//            }
//            // map<timestamp, metric_value>
//            Map<Object, Double> collect = new HashMap<>(); //Arrays.stream(largestValue).map(o -> o[0]).collect(Collectors.toMap(o -> o, o -> 0.0));
//
//            for (Object[][] o : values) {
//                for (Object[] valueItem : o) {
//                    double value = Double.parseDouble(valueItem[1] + "");
//                    collect.compute(valueItem[0], (k, v) -> v == null ? value : v + value);
//                }
//            }
//            // 排序
//            collect = new TreeMap<>(collect);
//            return collect;
//        }
//
//        if ("vector".equals(response.getData().getResultType())) {
//            List<Object[]> values = metrics.stream().map(o -> ((MetricClient.VectorMetric) o).getValue()).collect(Collectors.toList());
//            if (values.isEmpty()) {
//                return null;
//            }
//            return values.stream().map(o -> Double.parseDouble(o[1] + "")).reduce(Double::sum).orElse(0.0);
//        }
//        return null;
//    }
//
//    public List<MetricVO.ValueVO> readResponse(MetricClient.Response response, Map set, String filterLabel) {
//        List<MetricClient.Metric> metrics = filter(set.keySet(), response, filterLabel);
//        if ("vector".equals(response.getData().getResultType())) {
//            Map<String,MetricVO.ValueVO> podMetricValue = new HashMap<>();
//            for (MetricClient.Metric m : metrics) {
//                MetricClient.VectorMetric vm = (MetricClient.VectorMetric) m;
//                MetricVO.ValueVO valueVO = new MetricVO.ValueVO();
//                Object o = set.get(m.getMetric().get(filterLabel));
//                if (o != null) {
//                    valueVO.setPod(o + "");
//                    double value = Double.parseDouble(vm.getValue()[1] + "");
//                    valueVO.setValue(value);
//                    podMetricValue.compute(o+"", (k, v) -> {
//                        if (v==null) return valueVO;
//                        v.setValue(v.getValue() + value);
//                        return v;
//                    }) ;
//                }
//            }
//            return new ArrayList<>(podMetricValue.values());
//        }
//        return Collections.emptyList();
//    }


    @Override
    public void afterPropertiesSet() throws Exception {

    }

    private String getQuotaName(String namespace) {
        return namespace + "-resource-quota";
    }

    private List<Map<String, Object>> listCloudListByExternalId(CloudTenant cloudTenant) {
        List<cn.newdt.cloud.vo.KubeConfigVO> kubeInfos = kubeConfigService.findEnabledVO(null);
        Map<Integer, KubeClusterCardVO> kubeMetrics = kubeConfigService.listMetrics(kubeInfos).stream()
                .collect(Collectors.toMap(KubeClusterCardVO::getId, k -> k));
        final Map<Integer, TenantClusterVO> tenantQuotas = new HashMap<>();
        if (cloudTenant.getExternalId() != null) {
            tenantQuotas.putAll(getOwnedClustersOfExternalTenant(cloudTenant.getExternalId())
                    .stream()
                    .collect(Collectors.toMap(cn.newdt.cloud.vo.KubeConfigVO::getId, t -> t)));
        }
        // merge
        List<Map<String, Object>> merged = kubeInfos.stream().map(k -> {
            Map<String, Object> mergedMap = new HashMap<>();
            mergedMap.put("id", k.getId());
            mergedMap.put("name", k.getName());
            mergedMap.put("ip", k.getIp());
            mergedMap.put("version", k.getVersion());
            ResourceMetricVO resourceMetricVO = new ResourceMetricVO();
            if (kubeMetrics.get(k.getId()) != null) {
                BeanUtils.copyProperties(kubeMetrics.get(k.getId()), resourceMetricVO);
                mergedMap.put("metrics", resourceMetricVO);
            }
            mergedMap.put("quotas", tenantQuotas.isEmpty() ? Collections.emptyMap() :
                    new HashMap<String, Object>() {{
                        TenantClusterVO tenantClusterVO = tenantQuotas.get(k.getId());
                        if (tenantClusterVO != null) {
                            put("cpu", tenantClusterVO.getCpuQuota());
                            put("memory", tenantClusterVO.getMemoryQuota());
                            put("disk", tenantClusterVO.getDiskQuota());
                        }
                    }});
            return mergedMap;
        })
                .collect(Collectors.toList());
        return merged;
    }

    /**
     * 查询租户资源配额
     *
     * @param externalId 租户外部系统ID
     */
    public List<TenantClusterVO> getOwnedClustersOfExternalTenant(int externalId) {
        CloudTenant tenant = tenantService.findByExternalId(externalId);
        if (tenant == null) {
            return Collections.emptyList();
        }
        return getOwnedClustersOfTenant(tenant.getId());
    }

    /**
     * 根据云平台的租户id查询租户信息
     */
    public List<TenantClusterVO> getOwnedClustersOfTenant(int tenantId) {
        List<CloudQuota> quotaList = quotaService.findByTenant(tenantId);
        Set<Integer> kubeIds = quotaList.stream().map(t -> t.getKubeId()).collect(Collectors.toSet());
        if (kubeIds.isEmpty())
            return Collections.emptyList();
        // 当前租户拥有的集群列表
        List<KubeConfig> kubeConfigs = configService.findEnabled(null);

        // 部门各集群配额
        Map<Integer, CloudQuota> quotaMap = quotaList.stream().collect(Collectors.toMap(q -> q.getKubeId(), q -> q));

        // 返回集群列表和该集群当前部门配额
        return kubeConfigs.stream()
                .filter(k -> kubeIds.contains(k.getId())) // 过滤部门拥有的集群
                .map(k -> {
                    TenantClusterVO vo = new TenantClusterVO();
                    BeanUtils.copyProperties(k, vo);
                    vo.setIp(RegexUtil.getIp(k.getServer()));
                    vo.setPort(RegexUtil.getPort(k.getServer()));
                    CloudQuota cloudQuota = quotaMap.get(vo.getId());
                    if (cloudQuota != null) {
                        vo.setCpuQuota(cloudQuota.getLimitsCpu() / 1000.0);//new QuantityVO(new BigDecimal(cloudQuota.getLimitsCpu()), "c"));
                        vo.setMemoryQuota(cloudQuota.getLimitsMemory() * 1024 * 1024);//单位byte
                        vo.setDiskQuota(cloudQuota.getRequestsDisk() * 1024 * 1024);//new QuantityVO(new BigDecimal(cloudQuota.getRequestsDisk()), "Ki"));
                    }
                    if (StringUtils.isNotEmpty(k.getCsiType())) {
                        vo.setStoragePlugin(Arrays.asList(k.getCsiType().split(",")));
                    }
                    return vo;
                })
                .collect(Collectors.toList());
    }

    @Override
    public Object getKubeCards(Integer tenantId) {
        final Map<Integer, CloudQuota> tenantQuotaSettingMap;
        List<Integer> tenantIdList;
        if (tenantId > 0) {
            tenantQuotaSettingMap = quotaService.findByTenant(tenantId).stream()
                    .collect(Collectors.toMap(q -> q.getKubeId(), this::convertUnit));
            tenantIdList = Collections.singletonList(tenantId);
        } else {
            tenantQuotaSettingMap = null;
            tenantIdList = CloudRequestContext.getContext().getTenantIdList();
        }
        List<KubeConfigVO> ownedKubeConfig = getOwnedKubeConfigOrAll(tenantIdList);
        List<KubeClusterCardVO> collect = ownedKubeConfig.parallelStream()
                .map(k -> {
                    KubeClusterCardVO vo = new KubeClusterCardVO();
                    vo.setName(k.getName());
                    vo.setVersion(k.getVersion());
                    vo.setId(k.getId());
                    vo.setHost(k.getIp());
                    // get k8s node info
                    int node = 0, nodeNotReady = 0, controlPane = 0, controlPaneNotReady = 0;
                    try {
                        List<NodeDTO> nodes = kubeClientService.get(vo.getId()).listNodes();
                        for (NodeDTO dto : nodes) {
                            if ("master".equalsIgnoreCase(dto.getRole())) {
                                controlPane++;
                                if (!dto.isReady()) {
                                    controlPaneNotReady++;
                                }
                            } else {
                                node++;
                                if (!dto.isReady()) {
                                    nodeNotReady++;
                                }
                            }
                        }
                        vo.setNode(node);
                        vo.setNodeNotReady(nodeNotReady);
                        vo.setControlPane(controlPane);
                        vo.setControlPaneNotReady(controlPaneNotReady);
                    } catch (Exception e) {
                        log.error("failed get info of k8s cluster: {}, error: {}", k, e.getMessage());
                    }
                    try {
                        // get k8s hardware info
                        Map<String, Double> metrics = queryClusterMetric(vo.getId());
                        vo.setCpu(metrics.get("cpu"));
                        vo.setCpuUsage(metrics.get("cpu_usage"));
                        vo.setMemoryUsage(metrics.get("memory_usage"));
                        vo.setMemory(metrics.get("memory"));
                        vo.setDisk(metrics.get("disk"));
                        vo.setDiskUsage(metrics.get("disk_usage"));
                    } catch (Exception e) {
                        log.error("failed get metric of k8s cluster: {}, error: {}", k, e.getMessage());
                    }
                    if (tenantQuotaSettingMap != null)
                        vo.setQuota(tenantQuotaSettingMap.get(k.getId()));
                    return vo;
                })
                .collect(Collectors.toList());
        return collect;
    }

    @Override
    public List<TenantClusterVO> getOwnedClustersOfExternalTenant(Integer externalId) {
        CloudTenant tenant = tenantService.findByExternalId(externalId);
        if (tenant == null) {
            return Collections.emptyList();
        }
        return getOwnedClustersOfTenant(tenant.getId());
    }

    private List<KubeConfigVO> getOwnedKubeConfigOrAll(List<Integer> tenantIdList) {
        if (DepartmentUtil.isAdmin(UserUtil.getCurrentUser()) || CloudRequestContext.getContext().isDisableAcl()) {
            return configService.findEnabledVO(null); // return all
        }

        Map<String, Object> param = new HashMap<>();
        List<Integer> kubeIds;
        if (tenantIdList.isEmpty())
            kubeIds = Collections.emptyList();
        else if (tenantIdList.size() == 1 ) {
            kubeIds = quotaService.findByTenant(tenantIdList.get(0)).stream()
                    .map(t -> t.getKubeId()).collect(Collectors.toList());
        } else {
            kubeIds = quotaService.findAll().stream()
                    .filter(quota -> tenantIdList.contains(quota.getTenantId()))
                    .map(quota -> quota.getKubeId())
                    .distinct().collect(Collectors.toList());
        }
        if (kubeIds.isEmpty())
            return Collections.emptyList();
        param.put("kubes", kubeIds);
        return configService.findEnabledVO(param);
    }

    // quota 单位转换. cpu: 毫核->核, 内存/硬盘: KiB -> B
    private CloudQuota convertUnit(CloudQuota quota) {
        quota.setRequestsCpu(quota.getRequestsCpu() == null ? null : quota.getRequestsCpu() / 1000);
        quota.setRequestsMemory(quota.getRequestsMemory() == null ? null : quota.getRequestsMemory() * 1024);
        quota.setRequestsDisk(quota.getRequestsDisk() == null ? null : quota.getRequestsDisk() * 1024);
        quota.setLimitsCpu(quota.getLimitsCpu() == null ? null : quota.getLimitsCpu() / 1000);
        quota.setLimitsMemory(quota.getLimitsMemory() == null ? null : quota.getLimitsMemory() * 1024);
        quota.setLimitsDisk(quota.getLimitsDisk() == null ? null : quota.getLimitsDisk() * 1024);
        quota.setRequestsCpuUsage(quota.getRequestsCpuUsage() == null ? null : quota.getRequestsCpuUsage() / 1000);
        quota.setRequestsMemoryUsage(quota.getRequestsMemoryUsage() == null ? null : quota.getRequestsMemoryUsage() * 1024);
        quota.setRequestsDiskUsage(quota.getRequestsDiskUsage() == null ? null : quota.getRequestsDiskUsage() * 1024);
        quota.setLimitsCpuUsage(quota.getLimitsCpuUsage() == null ? null : quota.getLimitsCpuUsage() / 1000);
        quota.setLimitsMemoryUsage(quota.getLimitsMemoryUsage() == null ? null : quota.getLimitsMemoryUsage() * 1024);
        quota.setLimitsDiskUsage(quota.getLimitsDiskUsage() == null ? null : quota.getLimitsDiskUsage() * 1024);
        return quota;
    }

}
