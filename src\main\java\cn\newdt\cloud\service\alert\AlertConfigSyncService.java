package cn.newdt.cloud.service.alert;

import cn.newdt.cloud.config.CloudRequestContext;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.alert.*;
import cn.newdt.cloud.domain.alert.json.*;
import cn.newdt.cloud.mapper.AlertMetricMapper;
import cn.newdt.cloud.mapper.AlertRuleResourceMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.CloudAppLogicService;
import cn.newdt.cloud.service.KubeClientService;
import cn.newdt.cloud.service.ResourceManagerService;
import cn.newdt.cloud.service.impl.SysConfigService;
import cn.newdt.cloud.service.impl.TenantService;
import cn.newdt.cloud.utils.DateUtil;
import cn.newdt.cloud.utils.JsonUtil;
import cn.newdt.cloud.utils.MGRUtil;
import cn.newdt.cloud.utils.MongoUtil;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.AsymmetricEncryptionUtil;
import com.coreos.monitoring.v1.PrometheusRule;
import com.coreos.monitoring.v1.PrometheusRuleSpec;
import com.coreos.monitoring.v1.prometheusrulespec.Groups;
import com.coreos.monitoring.v1.prometheusrulespec.groups.Rules;
import com.coreos.monitoring.v1alpha1.AlertmanagerConfig;
import com.coreos.monitoring.v1alpha1.AlertmanagerConfigSpec;
import com.coreos.monitoring.v1alpha1.alertmanagerconfigspec.Receivers;
import com.coreos.monitoring.v1alpha1.alertmanagerconfigspec.Route;
import com.coreos.monitoring.v1alpha1.alertmanagerconfigspec.receivers.EmailConfigs;
import com.coreos.monitoring.v1alpha1.alertmanagerconfigspec.receivers.WechatConfigs;
import com.coreos.monitoring.v1alpha1.alertmanagerconfigspec.receivers.emailconfigs.Headers;
import com.coreos.monitoring.v1alpha1.alertmanagerconfigspec.route.Matchers;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import io.fabric8.kubernetes.api.model.IntOrString;
import io.fabric8.kubernetes.api.model.ObjectMetaBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.AlertConfigConstant.*;
import static cn.newdt.cloud.constant.DatasourceConstant.SCHEMA;

@Service
@Slf4j
public class AlertConfigSyncService {
    public static final String SEPARATOR = "_";
    public static final String CHANNEL_EWECHAT = "ewechat";
    public static final String CHANNEL_EMAIL = "email";
    @Autowired
    private KubeClientService clientService;
    @Autowired
    private AlertMetricMapper alertMetricMapper;
    @Autowired
    private AlertNotifyConfigService notifyConfigService;
    @Autowired
    private AlertConfigService configService;
    @Autowired
    private AlertRuleResourceMapper ruleResourceMapper;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private TenantService tenantService;
    @Autowired
    private CloudAppLogicService logicService;

    ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());

    @PostConstruct
    public void fetchTask() {
        List<AlertRuleConfig> alertRuleConfigList = configService.getAlertRuleSetList(null);
        alertRuleConfigList.stream().flatMap(ar -> {
            String checksum = ar.getChecksum();
            RuleSet ruleSet = ar.parseJson();
            return ar.getInstances().stream()
                    .filter(i -> !checksum.equals(i.getChecksum()))
                    .map(i -> {
                        Resource resource = new Resource().copyFrom(i);
                        ruleSet.setResources(Collections.singletonList(resource));
                        AlertConfig alertConfig = new AlertConfig();
                        alertConfig.setRuleSet(ruleSet);
                        alertConfig.setChecksum(checksum);
                        alertConfig.setKubeId(i.getKubeId());
                        alertConfig.setConfigInstance(i);
                        return alertConfig;
                    });
        }).forEach(this::enqueueSyncTask);
    }

    /**
     * required: configInstance, checksum, ruleset, kubeId
     * todo task has id, same config can only exist one task in the queue
     */
    public void enqueueSyncTask(AlertConfig alertConfig) {
        log.info("[alert config sync] enqueued {}", alertConfig.getConfigInstance());
        String federateKubeId = sysConfigService.findOne(CloudAppConstant.SysCfgCategory.ALERT_CFG, FEDERATE_KUBE_ID);
        if (StringUtils.isEmpty(federateKubeId)) new IllegalStateException("missing system config federate_kube_id");
        AlertRuleResource checkByPassCache = ruleResourceMapper.selectByPrimaryKey(SCHEMA, alertConfig.getConfigInstance().getId());
        if (checkByPassCache == null) {
            return;
        }
        executorService.submit((() -> {
            try {
                KubeClient client = getFederateKube(federateKubeId);
                syncConfig(alertConfig, client);
                configService.updateAlertRuleResourceSynced(alertConfig.getConfigInstance(), alertConfig.getChecksum());
                log.info("[alert config sync] succeed {}", alertConfig.getConfigInstance().getId());
            } catch (Exception e) {
                log.error("[alert config sync] failed", e);
//                try {
//                    Thread.sleep(30 * 1000);
//                } catch (Exception ex) {
//                    throw new RuntimeException(ex);
//                }
                // transmit thread local
                // re-queue
//                enqueueSyncTask(alertConfig);
            } finally {
                CloudRequestContext.clearContext();
            }
        }));
    }

    private KubeClient getFederateKube(String federateKubeId) {
        KubeClient client = null;
        try {
            client = clientService.get(Integer.parseInt(federateKubeId));
        } catch (Exception ignore) {}
        return client;
    }

    /**
     * @param alertConfig each config object to only one config cr for all resource related
     */
    public void syncConfig(AlertConfig alertConfig) {
        syncConfig(alertConfig, clientService.get(alertConfig.getKubeId()));
    }

    public void syncConfig(AlertConfig alertConfig, KubeClient kubeClient) {

        // validate&format ruleSet config
        RuleSet ruleSet = alertConfig.getRuleSet();
        ruleSet.format();

        // ensure pre-requirement
        alertConfig.setGlobalOptions(getGlobalAlertOptions());
        alertConfig.setMetricMetaStore(getMetricStore());
//        configureContacts(alertConfig);
        alertConfig.setKubeClient(kubeClient);

        setChannelOptions(alertConfig);// getChannelOptions());

        // concurrency control
//        ensureChannelSecret(alertConfig); // after channel options

        // end ensure pre-requirement
        // do sync
        ruleSet.getResources().stream()
                .forEach(rs -> {
                    fillResource(rs);
                });

        // 兼容保证, 存量数据kubeId为空
        if (ruleSet.getResources().get(0).getKubeId() == null) {
            ensureAlertmanagerGlobalConfig(alertConfig, kubeClient); // after secret has been created
            syncRules(alertConfig, kubeClient, ruleSet.getResources());
        } else {
            Map<Integer, List<Resource>> batchByKube =
                    ruleSet.getResources().stream().collect(Collectors.groupingBy(res -> res.getKubeId()));

            for (Integer kubeId : batchByKube.keySet()) {
                KubeClient client = clientService.get(kubeId);
                ensureAlertmanagerGlobalConfig(alertConfig, client); // after secret has been created
                List<Resource> batches = batchByKube.get(kubeId);
                syncRules(alertConfig, client, batches);
            }
        }


        // 融合dcp后，告警通知策略配置和通知发送在dcp端，容器云仅配置全局dcp路由
//        try {
//            syncNotification(alertConfig, kubeClient);
//        } catch (AlertConfigException e) {
//            log.error("[syncNotification]skip the notification config, {}", ruleSet.getRuleSetName(), e);
//        }
    }

    private void setChannelOptions(AlertConfig alertConfig) {
        List<AlertChannel> channelSettings = notifyConfigService.findChannel(null);
        Map<String, ChannelConfig> collect = channelSettings.stream().collect(Collectors.toMap(channel -> channel.getChannel(), channel -> {
            // 获取alertConfig实际配置的Channel
            Channel configuredChannel = Optional.ofNullable(alertConfig.getRuleSet().getNotifyStrategy()).flatMap(notifyStrategy ->
                    notifyStrategy.getChannels().stream().filter(ch -> ch.getChannel().equals(channel.getChannel())).findAny()).orElse(null);
            switch (channel.getChannel()) {
                case "email":
                    return emailChConfig(alertConfig, configuredChannel, channelSettings);
                case "ewechat":
                    return ewechatChConfig(alertConfig, configuredChannel, channelSettings);
                default:
                    throw new UnsupportedOperationException();
            }
        }));
        alertConfig.setChannelOptions(collect);
    }

    private void fillResource(Resource rs) {
        String[] type_arch = rs.getType().split(SEPARATOR);
        String kind = type_arch[0];
        String arch = null;
        if (type_arch.length == 2)
            arch = type_arch[1];
        rs.setKind(kind);
        rs.setArch(arch);
        tenantService.findByNamespace(rs.getNamespace()).ifPresent(tenant -> {
            rs.setTenantId(tenant.getId());
        });
        Optional.ofNullable(logicService.get(rs.getLogicAppId())).map(lp -> lp.getOwnerUser())
                        .ifPresent(id -> rs.setUserId(id));
    }

    private void ensureAlertmanagerGlobalConfig(AlertConfig alertConfig, KubeClient kubeClient) {
        Map<String, String> globalOptions = alertConfig.getGlobalOptions();
        String cfgSecretYaml = globalOptions.get(GLOBAL_ALERTMANAGER_YAML);
        String namespacedName = globalOptions.get(NAMESPACE_NAME);
        String namespace = namespacedName.split("_")[0];
        String name = namespacedName.split("_")[1];
        String secretName = "alertmanager-" + name;
        // alertmanager.yaml

        Optional<AlertNotifyChannel.EmailConfig> emailChannelConfig = alertConfig.checkNullableEmailChannelConfig();
        if (emailChannelConfig.isPresent()) {
            AlertNotifyChannel.EmailConfig emailConfig = emailChannelConfig.get();
            ImmutableMap.Builder<String, Object> builder = ImmutableMap.builder();
            if (StringUtils.isNotEmpty(emailConfig.getFrom()))
                builder.put("smtp_from", emailConfig.getFrom());
            if (StringUtils.isNotEmpty(emailConfig.getSmtpHost()))
                builder.put("smtp_host", emailConfig.getSmtpHost());
            if (StringUtils.isNotEmpty(emailConfig.getAuth_user()))
                builder.put("smtp_user", emailConfig.getAuth_user());
            if (StringUtils.isNotEmpty(emailConfig.getAuth_pwd()))
                builder.put("smtp_pwd", AsymmetricEncryptionUtil.getEncryptInstance().decrypt(emailConfig.getAuth_pwd()));
            builder.put("smtp_ssl", emailConfig.isSsl_enable());
            cfgSecretYaml = new StringSubstitutor(builder.build()).replace(cfgSecretYaml);
        } else {
            cfgSecretYaml = cfgSecretYaml
                    .replaceAll("\\$\\{[a-zA-Z_\\s]+}", ""); // remove remain placeholder
        }
        ImmutableMap.Builder<String, String> secretDataBuilder = ImmutableMap.<String, String>builder();
        secretDataBuilder.put("alertmanager.yaml", cfgSecretYaml);
        // xxx.tpl
        for (String template_cfg_name : globalOptions.keySet()) {
            if (template_cfg_name.startsWith(NOTIFY_TEMPLATE_PREFIX)) { // suffix is channel name
                String value = globalOptions.get(template_cfg_name);
                if (StringUtils.isNotEmpty(value))
                    // format xxxxx.<tpl-name>.tpl
                    secretDataBuilder.put(template_cfg_name.substring((NOTIFY_TEMPLATE_PREFIX).length()), value);
            }
        }
        kubeClient.createSecret(namespace, secretName, secretDataBuilder.build());
    }

    private void ensureChannelSecret(AlertConfig alertConfig) {
        for (Channel channel : alertConfig.getRuleSet().getNotifyStrategy().getChannels()) {
            AlertChannel channelCfg = notifyConfigService.getChannel(channel.getChannel());
            if (alertConfig.getChannelOptions().containsKey(channelCfg.getChannel()))
                alertConfig.getChannelOptions().get(channel.getChannel()).init();
        }
    }

    private Map<String, BiConsumer<AlertChannel, AlertConfig>> channelInitStrategy = ImmutableMap.of(
            "email", (channel, alertConfig) -> {
                AlertNotifyChannel.EmailConfig emailConfig = JsonUtil.toObject(AlertNotifyChannel.EmailConfig.class, channel.getConfig());
                String authPwd = emailConfig.getAuth_pwd();
                String name = getGlobalAlertOptions().get(EMAIL_SECRET_NAME);
                String amNamespacedName = alertConfig.getGlobalOptions().get(NAMESPACE_NAME);
                String namespace = amNamespacedName.split("_")[0];
                // ensure email password secret in namespace same with AlertManager
                KubeClient client = alertConfig.getKubeClient();
                authPwd = AsymmetricEncryptionUtil.getEncryptInstance().decrypt(authPwd);
                client.createSecret(name, authPwd, namespace);
            },
            "ewechat", (channel, alertConfig) -> {
                AlertNotifyChannel.EWechatConfig config = JsonUtil.toObject(AlertNotifyChannel.EWechatConfig.class, channel.getConfig());
                String secret = config.getSecret();

                String name = getGlobalAlertOptions().get(EMAIL_SECRET_NAME);
                String amNamespacedName = alertConfig.getGlobalOptions().get(NAMESPACE_NAME);
                String namespace = amNamespacedName.split("_")[0];
                // ensure email password secret in namespace same with AlertManager
                KubeClient client = alertConfig.getKubeClient();
                secret = AsymmetricEncryptionUtil.getEncryptInstance().decrypt(secret);
                client.createSecret(name, namespace, "secret", secret);
            }
    );

    /**
     * 构造Contact对象 填充Userinfo中的联系方式 并设置为 channel contacts属性
     */
    private void configureContacts(AlertConfig alertConfig) {
        for (Channel channel : alertConfig.getRuleSet().getNotifyStrategy().getChannels()) {
            List<Contact> contacts = channel.getContacts();
            if (CollectionUtils.isEmpty(contacts)) continue;
            List<Contact> filledContacts = new ArrayList<>();
            for (Contact contact : contacts) {
                if (contact.getId() != null) {
                    filledContacts.add(filloutContact(contact));
                } else if (contact.getGroupId() != null) {
                    AlertContactGroup alertContactGroup = notifyConfigService.describeGroup(contact.getGroupId());
                    for (AlertContactGroupMember member : alertContactGroup.getMembers()) {
                        Contact contact_n = new Contact();
                        contact_n.setId(member.getUserId());
                        contact_n.setGroupId(member.getGroupId());
                        filledContacts.add(filloutContact(contact_n));
                    }
                }
            }
            channel.setContacts(filledContacts);
        }
    }

    /**
     * 填充contact必要信息
     */
    private Contact filloutContact(Contact contact) {
        notifyConfigService.filloutContactFields(contact);
        return contact;
    }

    public Map<String, AlertMetric> getMetricStore() {
        return alertMetricMapper.listByMap(SCHEMA, Collections.emptyMap()).stream()
                .collect(Collectors.toMap(AlertMetric::getMetricName, Function.identity()));
    }

    private Map<String, String> getGlobalAlertOptions() {
        return sysConfigService.find(CloudAppConstant.SysCfgCategory.ALERT_CFG)
                .stream().collect(Collectors.toMap(cfg -> cfg.getName(), cfg -> cfg.getData()));
//        return ImmutableMap.<String, String>builder()
//                .put(EMAIL_SECRET_NAME, "alertmanager-receiver-email-config")
//                .put(NAMESPACE_NAME, "monitoring_dev")
//                .put(AMCFG_MATCH_LABELS, "example=example")
//                .put(AMCFG_NAMESPACE_MATCH_LABELS, "type=department")
//                .put(PROMETHEUSRULE_MATCH_LABELS, "env=dev,prometheus=k8s,role=alert-rules")
//                .put(GLOBAL_ALERTMANAGER_YAML, "")
//                .put(PROMETHEUS_EVALUATE_INTERVAL_SECONDS, "30")
//                .build();
    }

    private void syncRules(AlertConfig config, KubeClient kubeClient, List<Resource> resources) {
        RuleSet ruleSet = config.getRuleSet();
        List<Rule> rules = ruleSet.getRules();
//        String ruleSetName = ruleSet.getRuleSetName();
        // convert AlertConfig to CustomResources managed by prometheus-operator
        // todo resource 必须有相同的类型和namespace
        Resource biotpl = resources.get(0);
        String prNamespace = biotpl.getNamespace();
        String prName = String.format("%s-%s", biotpl.getKind().toLowerCase(), biotpl.getName());

        PrometheusRule prometheusRule = new PrometheusRule();
        prometheusRule.setMetadata(new ObjectMetaBuilder()
                .withNamespace(prNamespace)
                .withLabels(getPrLabels(config))
                .withName(prName)
                .build());

        PrometheusRuleSpec spec = new PrometheusRuleSpec();
        prometheusRule.setSpec(spec);
        List<Groups> groups;
        if ((groups = spec.getGroups()) == null) {
            groups = new ArrayList<>();
            spec.setGroups(groups);
        }
        for (Resource resource : resources) {
            // each resource convert to rule group
            groups.add(newRuleGroup(config, rules, resource));
        }

        kubeClient.createOrReplaceCustomResource(prometheusRule, PrometheusRule.class, prNamespace);
    }

    private class AlertConfigException extends Exception {
        AlertConfigException(String message) {
            super(message);
        }
    }

    /**
     * one route config per resource. todo resource tree(same resource target different dimension) convert to route tree
     * treat escalation as sub-route of this resource's main tree.
     */
    private void syncNotification(AlertConfig config, KubeClient kubeClient) throws AlertConfigException {
        // for each config.resource generate amc
        RuleSet ruleSet = config.getRuleSet();

        for (Resource scope : ruleSet.getResources()) {
            AlertmanagerConfig alertmanagerConfig = new AlertmanagerConfig();
            String amcNamespace = scope.getNamespace();
            String amcName = String.format("%s-%s", scope.getKind().toLowerCase(), scope.getName()); // type-name
            alertmanagerConfig.setMetadata(new ObjectMetaBuilder()
                    .withLabels(getAmcLabels(config))
                    .withNamespace(amcNamespace)
                    .withName(amcName)
                    .build());
            AlertmanagerConfigSpec spec = new AlertmanagerConfigSpec();
            alertmanagerConfig.setSpec(spec);
            if (ruleSet.getNotifyStrategy() == null) {
                throw new AlertConfigException("ruleset doesn't contain notify strategy config");
            }

            List<Receivers> receivers = receivers(config, ruleSet.getNotifyStrategy());
            Map<String, Receivers> receiverMap = receivers.stream().collect(Collectors.toMap(r -> r.getName(), Function.identity()));

            // parent route(1st level under root)
            Route route;
            String defaultChannel = ruleSet.getNotifyStrategy().getChannels().stream().filter(r -> r.isDefault())
                    .map(c -> receiverMap.get(c.getChannel()))
                    .map(r -> r.getName())
                    .findFirst().orElse("null");
            route = route(config, scope, defaultChannel);
            // child route for each 级别
            List<EscalationStrategy> escalationStrategies = ruleSet.getNotifyStrategy().getEscalationStrategies();
            if (escalationStrategies != null && escalationStrategies.size() > 1) {
                List<Route> routes = new ArrayList<>();
                route.setRoutes(routes);
                for (EscalationStrategy escalationStrategy : escalationStrategies) {
                    routes.add(route(config, scope, escalationStrategy));
                }
            }
            // add to route tree as 1st-level route
            spec.setRoute(route);
            spec.setReceivers(receivers);
//            kubeClient.deleteCustomResource(scope.getNamespace(), alertmanagerConfig.getMetadata().getName(), AlertmanagerConfig.class);
            kubeClient.createOrReplaceCustomResource(alertmanagerConfig, AlertmanagerConfig.class, scope.getNamespace());
        }
    }

    private Route route(AlertConfig config, Resource resource, String defaultChannel) {
        NotifyStrategy notifyStrategy = config.getRuleSet().getNotifyStrategy();
        Route route = new Route();
        route.setGroupBy(Collections.singletonList("alertname")); // 分组标签; 同一告警规则汇总后发送
        route.setGroupInterval(formatDuration(notifyStrategy.getIntervalTime()).trim());
        route.setRepeatInterval(formatDuration(notifyStrategy.getRepeatIntervalTime()).trim());
        route.setReceiver(defaultChannel);
        route.setMatchers(matchers(config, resource));
        return route;
    }

    private Route route(AlertConfig config, Resource scope, EscalationStrategy escalationStrategy) {
        Route route = route(config, scope, escalationStrategy.getChannel());
        // append severity matcher
        List<Matchers> matchers;
        if ((matchers = route.getMatchers()) == null) {
            matchers = new ArrayList<>();
            route.setMatchers(matchers);
        }
        matchers.add(matcher(SEVERITY_LABEL, escalationStrategy.getSeverity()));
        return route;
    }

    /**
     * 告警配置中NotifyStrategy可以为空, 表示不发送告警通知, 此时仅记录告警日志(webhook-log).
     * 如果通道配置不正确, 则跳过该通道. 如仅有通道配置没有联系人配置; 联系人联系方式为空; 通道配置校验失败...;
     */
    private List<Receivers> receivers(AlertConfig config, NotifyStrategy notifystrategy) {
        return notifystrategy.getChannels().stream().map(channel -> {
            try {
                return config.getChannelOptions().get(channel.getChannel())
                        .configureReceiver();
            } catch (Exception e) {
                log.error("there is some issue about this channel config, skip it right now", e);
                return null;
            }
        }).collect(Collectors.toList());
    }


    ChannelConfig emailChConfig(AlertConfig alertConfig, Channel channel, final List<AlertChannel> channelSettings) {

        final String channelName = CHANNEL_EMAIL;
        // 平台上的告警通道配置
        Map<String, String> globalOptions = alertConfig.getGlobalOptions();
        return new ChannelConfig() {
            @Override
            public AlertChannel getChannelSetting() {
                return channelSettings.stream()
                        .filter(ch -> ch.getChannel().equals(channelName))
                        .findAny().orElseThrow(() -> new IllegalStateException("平台上不存在该通道配置: " + channelName));
            }

            @Override
            public String secretName() {
                return globalOptions.get(EMAIL_SECRET_NAME);
            }
            @Override
            public String secretKey() {
                return "password";
            }
            @Override
            public void init() {
                channelInitStrategy.get(channelName).accept(
                        getChannelSetting(),
                        alertConfig);
            }
            @Override
            public Receivers configureReceiver() {
                List<EmailConfigs> collect =
                        channel.getContacts() == null ? Collections.emptyList() : channel.getContacts().stream()
                                .filter(contact -> StringUtils.isNotEmpty(contact.getEMAIL()))
                                .map(contact -> {
                                    AlertChannel alertChannelCfg = getChannelSetting();
                                    String configStr = alertChannelCfg.getConfig();
                                    EmailConfigs emailConfig = new EmailConfigs();

                                    emailConfig.setTo(contact.getEMAIL());
                                    // use default at global section
                                    AlertNotifyChannel.EmailConfig defaultemailConfig = JsonUtil.toObject(AlertNotifyChannel.EmailConfig.class, configStr);
                                    if (defaultemailConfig == null) {
                                        log.error("[channel-email]cannot parse default email channel config");
                                        return null;
                                    }
//                    emailConfig.setFrom(defaultemailConfig.getFrom());
//                    emailConfig.setSmarthost(defaultemailConfig.getSmtpHost());
//                    emailConfig.setAuthUsername(defaultemailConfig.getAuth_user());
//                    AuthPassword authPassword = new AuthPassword();
//                    authPassword.setName(getGlobalAlertOptions().get(EMAIL_SECRET_NAME));
//                    authPassword.setKey("password");
                                    // use global config for email auth to avoid create secret in each namespace
//                    emailConfig.setAuthPassword(authPassword);
                                    Headers header = new Headers();
                                    header.setKey("Subject");
                                    header.setValue("{{ template \"email.custom.subject\" . }}");
                                    emailConfig.setHeaders(Collections.singletonList(header));
                                    // reference template name
                                    if (StringUtils.isNotEmpty(defaultemailConfig.getHtml_key()))
                                        emailConfig.setHtml(defaultemailConfig.getHtml_key());
                                    emailConfig.setSendResolved(true);
                                    return emailConfig;
                                })
                                .filter(Objects::nonNull).collect(Collectors.toList());
                Receivers receiver = new Receivers();
                receiver.setName(channelName);
                receiver.setEmailConfigs(collect);
                return receiver;
            }
        };
    }

    ChannelConfig ewechatChConfig(AlertConfig alertConfig, Channel configuredChannel, final List<AlertChannel> channelSettings) {

        final String channelName = CHANNEL_EWECHAT;
        Map<String, String> globalOptions = alertConfig.getGlobalOptions();
        return new ChannelConfig() {
            @Override
            public AlertChannel getChannelSetting() {
                return channelSettings.stream()
                        .filter(ch -> ch.getChannel().equals(channelName))
                        .findAny().orElseThrow(() -> new IllegalStateException("平台上不存在该通道配置: " + channelName));
            }

            @Override
            public String secretName() {
                return EWECHAT_SECRET_NAME;
            }

            @Override
            public String secretKey() {
                return "secret";
            }

            @Override
            public void init() {
                channelInitStrategy.get(channelName).accept(
                        getChannelSetting(),
                        alertConfig) ;
            }

            @Override
            public Receivers configureReceiver() {
                List<WechatConfigs> collect = configuredChannel.getContacts() == null ? Collections.emptyList() : configuredChannel.getContacts().stream()
                        .filter(contact -> StringUtils.isNotEmpty(contact.getEWECHAT()))
                        .map(contact -> {
                            AlertChannel alertChannelCfg = getChannelSetting();
                            WechatConfigs wechatConfig = new WechatConfigs();
                            AlertNotifyChannel.EWechatConfig defaultemailConfig = JsonUtil.toObject(AlertNotifyChannel.EWechatConfig.class, alertChannelCfg.getConfig());
                            wechatConfig.setAgentID(defaultemailConfig.getApplicationId() + "");
//                            wechatConfig.setApiSecret(defaultemailConfig.getSecret());
                            wechatConfig.setCorpID(defaultemailConfig.getCompanyId());
                            wechatConfig.setApiURL(defaultemailConfig.getApiURL());
                            wechatConfig.setSendResolved(true);
                            // todo send to user or group
                            wechatConfig.setToUser(contact.getEWECHAT());

                            return wechatConfig;
                        }).collect(Collectors.toList());
                Receivers receiver = new Receivers();
                receiver.setName(channelName);
                receiver.setWechatConfigs(collect);
                return receiver;
            }
        };
    }

    private String formatDuration(long durationSecs) {
        Duration duration = Duration.ofSeconds(durationSecs);
        return DateUtil.formatDuration(duration).replaceAll(" ", "");
//        StringBuilder sb = new StringBuilder();
//        long hours = duration.toHours();
//        long minutes = duration.toMinutes() % 60;
//        long seconds = durationSecs % 60;
//        if (hours > 0)
//            sb.append(hours).append("h");
//        if (minutes > 0)
//            sb.append(minutes).append("m");
//        sb.append(seconds).append("s");
//        return sb.toString();
    }

    private List<Matchers> matchers(AlertConfig config, Resource scope) {
        List<Matchers> builder = new ArrayList<>();
        builder.add(matcher("from", "ndt"));
        // namespace matcher auto added by operator
//        if (StringUtils.isNotEmpty(config.getNamespace()))
//            builder.add(matcher("namespace", config.getNamespace()));
        if (StringUtils.isNotEmpty(scope.getType()))
            builder.add(matcher(RESOURCE_TYPE, scope.getType()));
        if (StringUtils.isNotEmpty(scope.getName()))
            builder.add(matcher(RESOURCE_NAME, scope.getName()));
        return builder;
    }

    private Matchers matcher(String name, String value) {
        Matchers matcher = new Matchers();
        matcher.setName(name);
        matcher.setValue(value);
        return matcher;
    }

    private Map<String, String> getAmcLabels(AlertConfig config) {
        String labelKVs = getNonNullOpt(config.getGlobalOptions(), AMCFG_MATCH_LABELS);
        return Arrays.stream(labelKVs.split(","))
                .map(pair -> pair.split("="))
                .collect(Collectors.toMap(pair -> pair[0], pair -> pair[1]));

    }

    /**
     * for each Resource generate a rule group. configure group rules by flattening Escalations of each same rule.
     */
    private Groups newRuleGroup(AlertConfig config, List<Rule> rules, Resource scope) {
        String grpName = String.format("%s-%s-%s", scope.getKind(), scope.getNamespace(), scope.getName());
        Groups group = new Groups();
        group.setName(grpName);
        ImmutableSet<String> kindFilter = ImmutableSet.of("All", scope.getKind());
        List<Rules> collect = rules.stream()
                // 筛选 resource scope 内的规则
                .filter(rule -> kindFilter.contains(config.getMetricMetaStore().get(rule.getMetricName()).getResourceType()))
                // 平台自定义规则由自己判断，不同步到prometheus
                .filter(rule -> !config.getMetricMetaStore().get(rule.getMetricName()).checkCustom())
                .flatMap(rule -> {
                    String metricName = rule.getMetricName();
                    AlertMetric metricMeta = config.getMetricMetaStore().get(metricName);
                    if (metricMeta == null)
                        throw new CustomException(600, "metadata not found for metric " + metricName);
                    return rule.getEscalations().stream().map(escalation -> {
                        Rules groupRule = new Rules();
                        groupRule.setAlert(metricName);
                        int times = escalation.getTimes();
                        int intervalSec = Integer.parseInt(config.getGlobalOptions().getOrDefault(PROMETHEUS_EVALUATE_INTERVAL_SECONDS, "30"));
                        groupRule.set_for(formatDuration(times * intervalSec).trim());
                        groupRule.setExpr(new IntOrString(expr(escalation, metricMeta, scope)));
                        groupRule.setAnnotations(annotations(metricMeta, config));
                        groupRule.setLabels(labels(escalation, scope, config));
                        return groupRule;
                    });
                })
                .collect(Collectors.toList());

        group.setRules(collect);
        return group;
    }

    private Map<String, String> labels(Escalation escalation, Resource scope, AlertConfig config) {
        return new ImmutableMap.Builder<String, String>()
                .put("severity", escalation.getSeverity())
                .put(RESOURCE_TYPE, scope.getType())
                .put(RESOURCE_NAME, scope.getName())
                // resource_id identify alert after alert has fired
                // find kafka id by zookeeperid
                .put(RESOURCE_ID, (scope.getPrimaryLogicId() != null ? scope.getPrimaryLogicId() : scope.getLogicAppId()) + "")
                // fix 由于某些指标没有namespace label导致alertmanagercfg 自动生成的namespace matcher 匹配失败，不能产生告警通知
                .put("namespace", scope.getNamespace())
                .put(RESOURCE_TENANT, scope.getTenantId() + "")
                .put(RESOURCE_OWNER, scope.getUserId() + "")
                .put(PRODUCT, AppKind.valueOf(scope.getKind(), scope.getArch()).getProduct())
                .put("from", "ndt").build(); // 用于告警推送过滤
    }

    private Map<String, String> annotations(AlertMetric metricMeta, AlertConfig config) {
        return new ImmutableMap.Builder<String, String>()
                .put("description", metricMeta.getDescription())
                .put("summary", metricMeta.getSummary())
                .build();
    }

    private final Pattern NAMESPACE_EXPR_PATTERN = Pattern.compile("\\w+\\s*=\\s*#namespace");
    private final Pattern NAME_EXPR_PATTERN = Pattern.compile("\\w+\\s*=\\s*#name");
    private final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\w+\\s*[=!~]+\\s*\"#\\{[\\w-]+}\",?");

    private String expr(Escalation escalation, AlertMetric metricRule, Resource scope) {
        String exprTpl = metricRule.getExpr();

        // 模板替换
        Map<String, String> dataMap = returnValuesMap(scope);
        dataMap.put("shared-sc", getSharedScList());
        exprTpl = new StringSubstitutor(dataMap, "#{", "}")
                .replace(exprTpl);
        // add filter(namespace & name) to metric
        // by replace placeholder or by append new filter
        // sample: elasticsearch_indices_search_query_time_seconds{namespace=#namespace, job=#name}
//        boolean nsExist = NAMESPACE_EXPR_PATTERN.matcher(exprTpl).find();
//        boolean nameExist = NAME_EXPR_PATTERN.matcher(exprTpl).find();
//
//        String namespace = scope.getNamespace();
//        if (StringUtils.isNotEmpty(namespace)) {
//            if (nsExist)
//                exprTpl = exprTpl.replaceFirst("#namespace", namespace);
//            else {
//                exprTpl = addFilter(exprTpl, "namespace", namespace);
//            }
//        }
//        String name = scope.getName();
//        if (StringUtils.isNotEmpty(name)) {
//            if (nameExist)
//                exprTpl = exprTpl.replaceFirst("#name", name);
//            else {
//                exprTpl = addFilter(exprTpl, "job", name);
//            }
//        }
//        // remove left placeholder
//        exprTpl = exprTpl.replaceAll(NAME_EXPR_PATTERN.pattern(), "")
//                .replaceAll(NAMESPACE_EXPR_PATTERN.pattern(), "");
        exprTpl = exprTpl.replaceAll(PLACEHOLDER_PATTERN.pattern(), "");

        return exprTpl + escalation.getOperator() + escalation.getThreshold();
    }

    @Autowired
    private ResourceManagerService resourceManagerService;

    private String getSharedScList() {
        return resourceManagerService.listStorageConfigOnly()
                .stream().map(sc -> sc.getName()).collect(Collectors.joining("|"));
    }

    // 模板一般包括 container, namespace, podPrefix, job(固定前缀 + name)
    private Map<String, String> returnValuesMap(Resource resource) {
        Map<String, String> map = new HashMap<>();
        map.put("namespace", resource.getNamespace());
        String name = resource.getName();
        if (StringUtils.isNotEmpty(resource.getKind()) && StringUtils.isNotEmpty(resource.getArch())) {
            AppKind appKind = AppKind.valueOf(resource.getKind(), resource.getArch());
            map.put("container", appKind.getContainerName());
            map.put("pod", getPodNamePrefix(name, appKind));
            map.put("job", getJob(name, appKind));
            map.put("crName", name);
        }
        return map;
    }

    private String getJob(String name, AppKind appKind) {
        // todo getJob, append prefix,告警job获取exporter service 处理
        switch (appKind) {
            case Redis_Cluster:
                return "rc-" + name;
            case Redis:
                return "redis-" + name;
            case Sentinel:
                return "sentinel-" + name;
            case MYSQL_HA:
                return "mysqlha-" + name;
            case MYSQL_MGR:
                return MGRUtil.getMgrExporterSvcName(name);
            case MongoDB:
                return MongoUtil.getExporterServiceName(name);
            case MongoDB_Cluster:
                return "mc-" + name;
            case PostgreSQL:
                return "pg-" + name;
            case OpenGauss:
                return "og-" + name;
            case Kafka:
                return "kafka-" + name;
            case Zookeeper:
                return "kafka-" + name + "-zookeeper";
            case Broker:
                return "rocketmq-" + name + "-broker";
            case Flink:
                return "flink-" + name;
            case Clickhouse:
            // es
            default:
                return name;
        }
    }

    private String getPodNamePrefix(String name, AppKind appKind) {
        switch (appKind) {
            case MongoDB_Cluster:
                return ImmutableList.of("mc-" + name + "-shard-", "mc-" + name + "-config-", "mc-" + name + "-router-")
                        .stream().map(n -> n + ".+").collect(Collectors.joining("|"));
            case Elasticsearch:
                return ImmutableList.of(name + "-es-").stream().map(n -> n + ".*").collect(Collectors.joining("|"));
            default:
                return appKind.getPodPattern(new CloudApp() {{
                    setCrName(name);
                }}).stream().map(n -> n + ".*").collect(Collectors.joining("|"));
        }
    }

    public String addFilter(String exprTpl, String label, String value) {
        boolean flag = false, inside = false;
        StringBuilder sb = new StringBuilder();
        if (!exprTpl.contains("{")) exprTpl = exprTpl + "{}";
        for (char c : exprTpl.toCharArray()) {
            sb.append(c);
            if (!inside && c == '{') {
                inside = true;
                sb.append(label).append("=").append("\"").append(value).append("\",");
                if (!flag)
                    flag = true;
            }
            if (inside && c == '}') {
                inside = false;
            }
        }
        return sb.toString().replaceAll(",\\s*}", "}");
    }

    private Map<String, String> getPrLabels(AlertConfig config) {
        String labelKVs = getNonNullOpt(config.getGlobalOptions(), PROMETHEUSRULE_MATCH_LABELS);
        return Arrays.stream(labelKVs.split(","))
                .map(pair -> pair.split("="))
                .collect(Collectors.toMap(pair -> pair[0], pair -> pair[1]));
    }

    private String getNonNullOpt(Map<String, String> config, String key) {
        String cfg = config.get(key);
        if (StringUtils.isEmpty(cfg)) {
            throw new CustomException(600, "缺失配置 " + key);
        }
        return cfg;
    }

    /**
     * 取消告警配置同步
     * @param namespace
     * @param kind
     * @param arch
     * @param crName
     * @param ar
     */
    public void unSyncConfig(String namespace, String kind, String arch, String crName, AlertRuleConfig ar) {
        if (ar == null) return;
        String checksum = ar.getChecksum();
        RuleSet ruleSet = ar.parseJson();
        List<AlertRuleResource> removedConfigInstance = ar.getInstances().stream()
                .filter(i -> (kind + SEPARATOR + arch).equals(i.getResourceType())
                        && crName.equals(i.getResourceName()) && namespace.equals(i.getResourceNamespace()))
                .collect(Collectors.toList());
        removedConfigInstance.stream().map(resource -> {
            AlertConfig alertConfig = new AlertConfig();
            alertConfig.setRuleSet(ruleSet);
            alertConfig.setChecksum(checksum);
            alertConfig.setKubeId(resource.getKubeId());
            alertConfig.setConfigInstance(resource);
            return alertConfig;
        }).forEach(config -> {
            unSyncConfig(config);
        });

    }



    public void unSyncConfig(AlertConfig config) {

        // unsync prometheusrule
        RuleSet ruleSet = config.getRuleSet();
        List<Resource> resources = ruleSet.getResources();
        Resource biotpl = resources.get(0);

        // 兼容性代码，确认kubeId不为空
        Integer federateKubeId = Optional.ofNullable(sysConfigService.findOne(CloudAppConstant.SysCfgCategory.ALERT_CFG, FEDERATE_KUBE_ID))
                .map(Integer::valueOf).orElse(null);
        Integer kubeId = biotpl.getKubeId() == null ? federateKubeId : biotpl.getKubeId();

        KubeClient kubeClient = clientService.get(kubeId);
        fillResource(biotpl);
        String prNamespace = biotpl.getNamespace();
        String prName = String.format("%s-%s", biotpl.getKind().toLowerCase(), biotpl.getName());

        kubeClient.deleteCustomResource(prNamespace, prName, PrometheusRule.class);
        // unsync alertmanagerconfig
        String amcNamespace = biotpl.getNamespace();
        String amcName = String.format("%s-%s", biotpl.getKind().toLowerCase(), biotpl.getName());
        kubeClient.deleteCustomResource(amcNamespace, amcName, AlertmanagerConfig.class);

        log.info("[alert config unsync] {}", config.getConfigInstance());
    }

    public interface ChannelConfig {
        AlertChannel getChannelSetting();
        String secretName();
        String secretKey();
        void init();
        Receivers configureReceiver();
    }
}
