package cn.newdt.cloud.domain.cr;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.fabric8.kubernetes.api.model.Namespaced;
import io.fabric8.kubernetes.api.model.NodeAffinity;
import io.fabric8.kubernetes.api.model.ObjectMetaBuilder;
import io.fabric8.kubernetes.api.model.Toleration;
import io.fabric8.kubernetes.client.CustomResource;
import io.fabric8.kubernetes.model.annotation.Group;
import io.fabric8.kubernetes.model.annotation.Version;
import lombok.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Group("rocketmq.shindata.com")
@Version("v1")
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class Broker extends CustomResource<Broker.BrokerSpec, Broker.BrokerStatus> implements Namespaced {

    public Broker() {
    }

    public Broker(String crName, String namespace, Broker.BrokerSpec spec) {
        this.setMetadata(new ObjectMetaBuilder().withName(crName).withNamespace(namespace).build());
        this.setSpec(spec);
    }

    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    @Setter
    public static class BrokerSpec{
        String cpu;                      // cpu资源限额
        String memory;                   // 内存资源限额
        FilebeatConfig filebeat;         // filebeat配置
        String image;                    // broker镜像地址
        IpListConfig[] ipList;           // 集群实例配置
        ScheduleConfig schedule;         // 实例调度配置
        Integer servicePort;             // 服务端口
        StorageConfig storage;           // 实例调度配置
        String flushType;                // 刷新方式
        String[] nameServers;              // nameServer的ipList
        Integer masterSize;                    // 分片数量 最小为1
        boolean syncMode;                // 是否启用异步

        private java.util.Map<java.lang.String, String> config;

        String exporterImage;             //exporter镜像名称
        Secret secret;                    //用来存放用户名和密码以及secret名称

        private Boolean maintenance;
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            BrokerSpec that = (BrokerSpec) o;
            return Objects.equals(cpu, that.getCpu()) &&
                    Arrays.equals(ipList, that.getIpList()) &&
                    Objects.equals(image, that.getImage()) &&
                    Objects.equals(memory, that.getMemory()) &&
                    Objects.equals(storage, that.getStorage()) &&
                    Arrays.equals(nameServers, that.getNameServers()) &&
                    Objects.equals(masterSize, that.getMasterSize());
        }
    }

    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @NoArgsConstructor @AllArgsConstructor
    @Data
    public static class StorageConfig {
        String Size;            //存储资源限额
        String StorageClass;    //存储依赖的CSI对应的StorageClass名称
        String HostpathRoot;    //本地存储根目录
    }

    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @NoArgsConstructor @AllArgsConstructor
    @Data
    public static class Secret {
        String passwordKey;     //密码
        String usernameKey;     //用户名
        String secretName;      //secret名称
    }

    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    @Setter
    public static class FilebeatConfig {
        String Image;       //Filebeat镜像地址
        String ConfigMap;   //Filebeat配置文件所在的ConfigMap名称
        String ConfigFile;  //Filebeat配置文件名称
    }

    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @NoArgsConstructor @AllArgsConstructor
    @Data
    public static class IpListConfig {
        String ip;               // podId
        Integer port = 0;            // nodePort
        String externalIP;          //lb
    }

    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    @Setter
    public static class ScheduleConfig {
        int processTimeout;     // Operator处理事件的最大时间限制
        int gracePeriod;        // 容器停止的体面等待时间
        int toleration;         // 容器对污点的容忍时间
        String[] nodes;         // 亲和节点
        Map<String, String> taints; //污点容忍
        List<Toleration> tolerations; // 容忍
        NodeAffinity nodeAffinity;    // node亲和
        Boolean antiAffinityRequired; // 强制pod反亲和
    }


    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    @Setter
    public static class BrokerStatus{
        BrokerSpec spec;
        String lastupdatetime;
        String state;
        BrokerInstance[] BrokerTopology;
        String message;
        Health health;
    }

    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    @Setter
    public static class BrokerInstance{
        Integer brokerId;        //borker id，0代表主节点
        Integer brokerIndex;       //broker实例的名字
        String extip;            // nodeIp
        String ip;               // podId
        Integer port;            // nodePort
    }

    public enum BrokerState {
        creating,
        updating,
        ready,
        recovering,
        invalid,
        restoring;
    }
}
