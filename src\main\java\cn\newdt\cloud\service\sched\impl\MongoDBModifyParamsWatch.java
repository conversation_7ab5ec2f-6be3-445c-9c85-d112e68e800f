package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.StatusConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.cr.MongoDBCommunity;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.utils.MetricUtil;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class MongoDBModifyParamsWatch extends OpsProcessorContext implements OpsPostProcessor<MongoDBCommunity> {

    @Override
    public OpsResultDTO postProcess(TriggerHis triggerHis) throws Exception {
        log.info("[MongoDB ModifyParamsWatch] 数据库参数修改回调");
        OpsResultDTO.Builder result = OpsResultDTO.builder();
        Map<String, String> jobDataMap = triggerHis.returnMergedJobDataMap();
        String appIdData = jobDataMap.get("appId");
        String params = jobDataMap.get("params");
        Long startTime = Long.parseLong(jobDataMap.get("startTime"));
        Map<String, String> paramsMap = JSONObject.parseObject(params, Map.class);

        int appId = Integer.parseInt(appIdData);
        CloudApp app = appService.get(appId);
        try {
            KubeClient kubeClient = kubeClientService.get(app.getKubeId());
            MongoDBCommunity cr = kubeClient.listCustomResource(MongoDBCommunity.class, app.getCrName(), app.getNamespace());
            String state = cr.getStatus().getPhase();
            if (Objects.equals(state, "Running")) {
                boolean dbUpdated = paramsMap.entrySet().stream().allMatch(item -> {
                    String realParamVal = execCommand(kubeClient, app, item.getKey());
                    String exceptParamVal = item.getValue();
                    if (MetricUtil.endWithUnit(exceptParamVal.toUpperCase()))
                        return MetricUtil.getLongValue(exceptParamVal.toUpperCase()) == Long.parseLong(realParamVal);
                    else
                        return exceptParamVal.equalsIgnoreCase(realParamVal);
                });
                if (dbUpdated) {
                    log.info("[MongoDB ModifyParamsWatch] 数据库参数修改成功");
                    result.msg("db params modify successful .").status(StatusConstant.SUCCESS);
                    result.stopJob(true);
                } else {
                    rollbackModifyParam(kubeClient, result, app, paramsMap, cr);
                }
            } else if (Objects.equals(state, "pending")) {
                long nowTime = new Date().getTime();
                // 耗时超过30分钟不成功就回滚
                if ((nowTime - startTime) > 30 * 60 * 1000) {
                    log.info("[MongoDBModifyParamsWatch] 数据库参数正在修改中，请稍后...");
                    result.msg("db params updating ...");
                } else {
                    rollbackModifyParam(kubeClient, result, app, paramsMap, cr);
                }
            } else if (Objects.equals(state, "filed")) {
                rollbackModifyParam(kubeClient, result, app, paramsMap, cr);
            }
            if (result.isStopped())
                appService.handleWatchResult(app.getId(), result.isSuccessful());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return result.build();
    }


    private void rollbackModifyParam(KubeClient kubeClient, OpsResultDTO.Builder result, CloudApp app, Map<String, String> paramsMap, MongoDBCommunity cr) {
        log.info("[MongoDBModifyParamsWatch] 数据库参数修改失败，恢复之前配置");
        result.stopJob(true).status(StatusConstant.FAIL);
        Set<String> keyError = new HashSet<>();
        paramsMap.forEach((key, value) -> {
            String paramVal = execCommand(kubeClient, app, key);
            if ("".equals(paramVal)) {
                keyError.add(key);
            }
        });
        if (!keyError.isEmpty()) {
            result.msg("db params update fail ，backoff " + "\nparam incorrect: " + JSONObject.toJSONString(keyError));
        } else {
            result.msg("db params update fail ，backoff ");
        }
        cr.getSpec().setAdditionalMongodConfig(YamlEngine.unmarshal(app.getCr(), MongoDBCommunity.class).getSpec().getAdditionalMongodConfig());
        kubeClient.updateCustomResource(cr, MongoDBCommunity.class);
    }

    /**
     * 访问数据库，查询参数当前值
     */
    private String execCommand(KubeClient kubeClient, CloudApp app, String paramKey) {
        try {
            String loginStatement = "mongo -u " + CloudAppConstant.UsernameAndPassword.mongoDBUsername
                    + " -p " + CloudAppConstant.UsernameAndPassword.mongoDBPassword
                    + " --authenticationDatabase admin --quiet  --eval ?"; // ?作为占位符 之后被替换
            String queryStatement = "JSON.stringify(db.serverCmdLineOpts())";
            String[] cmd = loginStatement.split(" ");
            cmd[cmd.length - 1] = queryStatement;
            PodDTO pod = kubeClient.listPod(app.getNamespace(), AppKind.MongoDB.labelOfPod(app)).stream()
                    .findFirst().orElseThrow(() -> new IllegalStateException("没有找到数据库所在Pod"));
            String resp = kubeClient.execCmd(app.getNamespace(), pod.getPodName(), AppKind.MongoDB.getContainerName(), cmd);
            String[] keyArr = paramKey.split("\\.");
            JSONObject parsed = JSONObject.parseObject(resp).getJSONObject("parsed");
            for (int i = 0; i < keyArr.length - 1; i++) {
                parsed = parsed.getJSONObject(keyArr[i]);
            }
            String value = parsed.getString(keyArr[keyArr.length - 1]);
            log.info("[execCommand MongoDB] {},  {}", app.getCrName(), resp);
            return value;
        } catch (Exception e) {
            throw new RuntimeException("exec execCommand failed, will try later. ", e);
        }
    }

}
