package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.common.OpLogContext;
import cn.newdt.cloud.common.TriFunction;
import cn.newdt.cloud.config.FutureService;
import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.ImageKindEnum;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.Password;
import cn.newdt.cloud.domain.ServiceManager;
import cn.newdt.cloud.dto.MigrateDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.dto.ResourceDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.utils.CustPreconditions;
import cn.newdt.cloud.vo.AppInstanceVO;
import cn.newdt.cloud.vo.CloudAppVO;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.SymmetricEncryptionUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.ImmutableMap;
import io.fabric8.kubernetes.api.model.Secret;
import io.fabric8.kubernetes.client.CustomResource;
import io.fabric8.kubernetes.client.utils.Serialization;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.*;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.ActionEnum.DELETE;

@Slf4j
@Service
public class AppOperationHandler {
    @Autowired
    private CloudAppService appService;
    @Autowired
    private KubeClientService clientService;
    @Autowired
    private NetworkService networkService;
    @Autowired
    private CloudAppConfigService appConfigService;
    @Autowired
    private AccessManagementService accessManagementService;

    public <T extends CustomResource> void handleMigrate(MigrateDTO migrateDTO, BiFunction<MigrateDTO.MigrateNode, CloudApp, T> modifier, Class<?> processor, Predicate<String> filter, String ipOwner, String ipReserveTarget, Class<T> crClazz, KubeClient kubeClient, Map<String, Object> extData) {
        CloudApp app = appService.get(migrateDTO.getAppId());
        CustPreconditions.checkState(StringUtils.isNotEmpty(app.getCr()), "获取当前cr失败, 可能是操作未正确结束");
        CustPreconditions.checkState(migrateDTO.getMigrateNodes().size() == 1, "仅支持一次操作迁移一个节点");
//        Arrays.stream(app.getIpNode()).filter(in -> in.getIp().equals(migrateNode.getOldNode().getIp())).findAny().orElseThrow(() -> new CustomException(600, "参数错误"));
        MigrateDTO.MigrateNode migrateNode = migrateDTO.getMigrateNodes().get(0);
        String newIP = allocateNewIp(app, filter, ipOwner, ipReserveTarget);
        migrateNode.getNewNode().setIp(newIP);
        T cr = modifier.apply(migrateNode, app);
        try {
            if (extData == null) extData = new HashMap<>();
            extData.put("oldPodNames", appService.findInstances(app.getId()).stream().map(p -> p.getPodName()).collect(Collectors.toList()));
            extData.put("oldNodeName", migrateNode.getOldNode().getNode());
            String applyYaml = YamlEngine.marshal(cr);
            OpLogContext.instance().YAML("CR", applyYaml, app.getCr());
            appService.callScheduler(app, applyYaml, extData,
                    ActionEnum.MIGRATE, processor);
        } catch (SchedulerException | JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        kubeClient.updateCustomResource(cr, crClazz);
    }

    private String allocateNewIp(CloudApp app, Predicate<String> filter, String ipOwner, String ipReservationTarget) {
        String ip = null;
        Set<String> discardedIps = new HashSet<>();
        try {
            do {
                ip = networkService.allocateIp(app, 1, ipOwner, ipReservationTarget).get(0);
                discardedIps.add(ip);
            } while (!filter.test(ip)); // if tester return true means ip is available
        } finally {
            discardedIps.remove(ip);
            if (!discardedIps.isEmpty())
                networkService.releaseIp(app, discardedIps, ipReservationTarget);
        }
        return ip;
    }

    /**
     * 处理应用缩容事件
     *
     * @param appId 应用ID
     * @param scaled 缩容的ipNode
     * @param modifier 修改自定义资源的函数
     * @param crClass 自定义资源的类类型
     * @param processorClass 处理器类类型，用于调用调度器
     * @param extData 扩展数据，用于传递额外信息
     *
     * 此方法主要用于在应用缩容时更新Kubernetes自定义资源(CR)它通过调用提供的修改器函数来更新CR，
     * 并记录旧的Pod名称以便后续处理
     */
    public <T extends CustomResource> void handleScaleDown(
            Integer appId, List<CloudApp.IpNode> scaled, TriFunction<CloudApp, List<String>, T, T> modifier,
            Class<T> crClass, Class<?> processorClass, Map extData) {
        // 检查要缩减的实例列表是否为空
        if (scaled != null)
            CustPreconditions.checkState(!CollectionUtils.isEmpty(scaled), "要缩减的实例列表为0");
        // 获取应用详情
        CloudApp app = appService.get(appId);
        // 检查应用的CR配置是否为空
        CustPreconditions.checkState(StringUtils.isNotEmpty(app.getCr()), "获取当前cr失败, 可能是操作未正确结束");
        // 提取缩容实例的IP地址
        List<String> scaledIPs = scaled != null ? scaled.stream().map(in -> in.getIp()).collect(Collectors.toList()) : null;
        // 获取Kubernetes客户端
        KubeClient kubeClient = clientService.get(app.getKubeId());
        // 获取当前的自定义资源
        T current = kubeClient.listCustomResource(crClass, app.getCrName(), app.getNamespace());
        // 应用修改函数以更新自定义资源
        T update = modifier.apply(app, scaledIPs, current);
        // 初始化或更新扩展数据
        if (extData == null) extData = new HashMap<>();
        // 序列化更新后的自定义资源为YAML格式
        try {
            String applyYaml = YamlEngine.marshal(update);
            // 记录CR的更新操作日志
            OpLogContext.instance().YAML("CR", applyYaml, app.getCr());
            // 调用调度器处理缩容操作
            appService.callScheduler(app, applyYaml, extData, ActionEnum.SCALE_IN, processorClass);
        } catch (SchedulerException | JsonProcessingException e) {
            // 如果发生调度或序列化异常，则抛出运行时异常
            throw new RuntimeException(e);
        }
        // 更新Kubernetes集群中的自定义资源
        kubeClient.updateCustomResource(update, crClass);
    }

    public <T extends CustomResource> void handleScaleDown(
            Integer appId, BiFunction<CloudApp, T, T> modifier,
            Class<T> crClass, Class<?> processorClass, HashMap<String, Object> extData) {
        handleScaleDown(
                appId,
                null,
                (a, ips, cr ) -> modifier.apply(a, cr),
                crClass,
                processorClass,
                extData
        );
    }

    /**
     * 处理应用扩缩容操作
     *
     * @param appId 应用ID，用于标识特定的应用
     * @param members 要增加的实例数量，必须大于0
     * @param crClass 自定义资源的类，用于处理特定类型的应用资源
     * @param appKindService 应用种类服务，提供与应用种类相关的操作
     * @param ipOwnerKind IP所有者类型，用于IP分配
     * @param ipReservationTarget IP预留目标，指定IP预留的目的
     * @param modifier 修改函数，用于更新自定义资源
     * @throws SchedulerException 当调度操作失败时抛出
     * @throws JsonProcessingException 当JSON处理失败时抛出
     */
    public <T extends CustomResource> void handleScaleUp(
            int appId, int members, Class<T> crClass, AppKindService appKindService,
            String ipOwnerKind, String ipReservationTarget, TriFunction<CloudApp, List<String>, T, T> modifier)
            throws SchedulerException, JsonProcessingException {
        // 检查appId是否为空
        CustPreconditions.checkNotNull(appId, "appId not be null!");
        // 检查要扩出的实例数是否合法
        if (members < 1) {
            log.info("[handle scaleUp] won't scale up, app: {} 扩容数量小于1");
            return;
        }
        // 获取应用详细信息
        CloudApp app = appService.get(appId);
        // 检查应用的CR是否为空
        CustPreconditions.checkState(StringUtils.isNotEmpty(app.getCr()), "获取当前cr失败, 可能是操作未正确结束");
        // 集群中的cr
        T current = clientService.get(app.getKubeId()).listCustomResource(crClass, app.getCrName(), app.getNamespace());
        // 为新实例分配固定Ip
        List<String> ipsAdd = networkService.allocateIp(app, members, ipOwnerKind, ipReservationTarget);

        // 应用CR修改
        T cr = modifier.apply(app, ipsAdd, current);
        // 从存储中获取CR，并更新其spec
        T crInStore = YamlEngine.unmarshal(app.getCr(), crClass);
        crInStore.setSpec(cr.getSpec());
        // 4. 提交watch
        String applyYaml = YamlEngine.marshal(crInStore);
        // 记录操作日志
        OpLogContext.instance().IP(ipsAdd).YAML("CR", applyYaml, app.getCr());
        // 调用调度器进行扩出操作
        appService.callScheduler(app, applyYaml, null, ActionEnum.SCALE_OUT, appKindService.getProcessorClass(ActionEnum.SCALE_OUT));
        // 更新集群中的CR
        clientService.get(app.getKubeId()).updateCustomResource(crInStore, crClass);
    }

    public <T extends CustomResource> void handleScale(int appId, Class<T> crClass, AppKindService appKindService, BiConsumer<CloudApp, T> modifier, ActionEnum actionEnum) throws SchedulerException, JsonProcessingException {
        // 检查appId是否为空
        CustPreconditions.checkNotNull(appId, "appId not be null!");
        // 获取应用详细信息
        CloudApp app = appService.get(appId);
        // 检查应用的CR是否为空
        CustPreconditions.checkState(StringUtils.isNotEmpty(app.getCr()), "获取当前cr失败, 可能是操作未正确结束");
        // 集群中的cr
        T current = clientService.get(app.getKubeId()).listCustomResource(crClass, app.getCrName(), app.getNamespace());

        modifier.accept(app, current);
        T crInStore = YamlEngine.unmarshal(app.getCr(), crClass);
        crInStore.setSpec(current.getSpec());
        // 4. 提交watch
        String applyYaml = YamlEngine.marshal(crInStore);
        OpLogContext.instance().YAML("CR", applyYaml, app.getCr());
        Map extData = new HashMap<>();
        List<AppInstanceVO> instances = appService.findInstances(app.getId());
        extData.put("oldPodNames", instances.stream().map(p -> p.getPodName()).collect(Collectors.toList()));

        appService.callScheduler(app, applyYaml, extData, actionEnum, appKindService.getProcessorClass(actionEnum));
        clientService.get(app.getKubeId()).updateCustomResource(crInStore, crClass);
    }

    public <T extends CustomResource> void handleUpgrade(int appId, String version, BiFunction<Map<ImageKindEnum, String>, T, T> modifier, Class<T> crClass, AppKindService appKindService) {
        CloudApp app = appService.find(appId).orElseThrow(() -> new CustomException(600, "应用不存在"));
        Map<ImageKindEnum, String> imageManifest = appConfigService.getImageManifest(appKindService.getKind(), version);
        KubeClient kubeClient = clientService.get(app.getKubeId());
        T current = kubeClient.listCustomResource(crClass, app.getCrName(), app.getNamespace());
        current = modifier.apply(imageManifest, current);
        T update = YamlEngine.unmarshal(app.getCr(), crClass);
        update.setSpec(current.getSpec());
        try {
            String applyYaml = YamlEngine.marshal(update);
            OpLogContext.instance().YAML("CR", applyYaml, app.getCr());
            appService.callScheduler(app, applyYaml, ImmutableMap.of("version", version), ActionEnum.UPGRADE, appKindService.getProcessorClass(ActionEnum.UPGRADE));
        } catch (SchedulerException | JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        kubeClient.updateCustomResource(update, crClass);
    }

    public void handleStart(int appId, String instanceName, AppKindService appKindService, String startCmd) {
        handleStart(appId, instanceName, appKindService, i -> startCmd, null, null);
    }

    public void handleStart(int appId, String instanceName, AppKindService appKindService, Function<AppInstanceVO, String> cmdResolver) {
        handleStart(appId, instanceName, appKindService, cmdResolver, null, null);
    }

    public void handleStart(int appId, String instanceName, AppKindService appKindService, String startCmd,
                            Predicate<AppInstanceVO> partitioner, Predicate<AppInstanceVO> filter) {
        handleStart(appId, instanceName, appKindService, i -> startCmd, partitioner, filter);
    }


    /**
     * 启动应用或启动实例的处理器方法
     *
     * @param appId          应用id
     * @param instanceName   给出则为启动实例否则启动整体应用
     * @param appKindService 应用service类
     * @param cmdResolver    启动命令，在容器里执行
     * @param partitioner    分区器，将实例分为两部分，分区器计算为true的部分先执行启动命令
     * @param filter         启停目标实例筛选
     */
    public void handleStart(int appId, String instanceName, AppKindService appKindService, Function<AppInstanceVO, String> cmdResolver,
                            Predicate<AppInstanceVO> partitioner, Predicate<AppInstanceVO> filter) {
        CloudApp app = appService.get(appId);
        KubeClient client = clientService.get(app.getKubeId());
        // check instance state at the moment, see if it's stopped
        if (filter == null) filter = i->true;
        List<AppInstanceVO> instanceList =
                //client.listPod(app.getNamespace(), AppKind.valueOf(app.getKind(), app.getArch()).labelOfPod(app)).stream()
                appKindService.findInstanceList(appId, null, null).stream()
                .filter(filter)
                .filter(i -> instanceName == null || i.getPodName().equals(instanceName))
                .filter(i -> i.getLiveness() == 0)
                .collect(Collectors.toList());
        if (instanceList.isEmpty()) {
            CloudApp update = new CloudApp();
            update.setId(appId);
            update.setStatus(CloudAppConstant.AppStatus.SUCCESS);
            appService.update(update);
            throw new CustomException(600, "所有实例已启动");
        }

        // if there are stopped instances, go into container and execute start command
        final Integer hisId ;
        try {
            ActionEnum action = StringUtils.isEmpty(instanceName) ? ActionEnum.START : ActionEnum.START_POD;
            hisId = appService.callScheduler(app, app.getCr(), Collections.singletonMap("instanceName", instanceName), action,
                    appKindService.getProcessorClass(action));
        } catch (SchedulerException | JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        // partition instance list into two [true,false]
        Map<Boolean, List<AppInstanceVO>> partitions = instanceList.stream().collect(Collectors.partitioningBy(Optional.ofNullable(partitioner).orElse(i -> true))); // default go to true partition

        AutoWrapAppendable resultBuf = new AutoWrapAppendable(new StringBuffer());
        AtomicBoolean failed = new AtomicBoolean(false);
        // handle true-partition first
        log.info("[handle start], execute start command");
        Optional.ofNullable(partitions.get(Boolean.TRUE)).orElseThrow(() -> new IllegalStateException("true-partition has nothing to do")).parallelStream()
                .forEach(i -> {
                    try {
                        String result = client.execCmd(app.getNamespace(), i.getPodName(), appKindService.getKind().getContainerName(), true, 10, "sh", "-c", cmdResolver.apply(i));
                        resultBuf.append(result);
                    } catch (Exception e) {
                        failed.compareAndSet(false, true);

                        resultBuf.append(e instanceof TimeoutException ? "timeout" : e.getMessage());
                    }
                });

        // handle false-partition after true-partition complete
        FutureService futureService = new FutureService();
        for (AppInstanceVO i : partitions.get(Boolean.FALSE)) {
            // execute command in a separate thread to wait for the result
            futureService.submit(() -> {
                try {
                    String result = client.execCmd(app.getNamespace(), i.getPodName(), appKindService.getKind().getContainerName(),  "sh", "-c", cmdResolver.apply(i));
                    resultBuf.append(result);
                } catch (Exception e) {
                    failed.compareAndSet(false, true);
                    resultBuf.append(e.getMessage());
                }
            });
        }
        futureService.await();
        if (failed.get()) {
            appService.stopChangeHisAsFailed(hisId, resultBuf.toString());
            throw new CustomException(600, "启动实例 " + resultBuf.toString());
        } else
            appService.appendChangeHisMessage(hisId, resultBuf.toString());
    }

    /**
     * 停止应用或应用实例的处理其方法。停止命令需先确保维护标记打开，且operator已将其置为maintaining状态。该方法并不实际执行启动命令
     */
    public <T extends CustomResource> void handleStop(int appId, String instanceName, AppKindService appKindService,
                                                      String stopCmd, Consumer<T> turnOnMaintenance, Class<T> clazz,
                                                      Predicate<PodDTO> filter) {
        CloudApp app = appService.get(appId);
        KubeClient kubeClient = clientService.get(app.getKubeId());

        // check instance state at the moment, see if it's stopped
//        List<AppInstanceVO> instanceList = appKindService.findInstanceList(appId, null, null).stream()
        List<PodDTO> instanceList = kubeClient.listPod(app.getNamespace(), AppKind.valueOf(app.getKind(), app.getArch()).labelOfPod(app))
                .stream()
                .filter(Optional.ofNullable(filter).orElse(i->true))
                .filter(i -> instanceName == null || i.getPodName().equals(instanceName))
//                .filter(i -> i.getLiveness() != 0)
                .collect(Collectors.toList());
        if (instanceList.isEmpty()) {
            CloudApp update = new CloudApp();
            update.setId(appId);
            update.setStatus(CloudAppConstant.AppStatus.STOPPED);
            appService.update(update);
            throw new CustomException(600, "所有实例已停止");
        }

        // if there are non-stopped instances, go into container and execute stop command
        T cr = kubeClient.listCustomResource(clazz, app.getCrName(), app.getNamespace());
        turnOnMaintenance.accept(cr);
        try {
            ActionEnum action = StringUtils.isEmpty(instanceName) ? ActionEnum.STOP : ActionEnum.STOP_POD;
            T crInStore = YamlEngine.unmarshal(app.getCr(), clazz);
            crInStore.setSpec(cr.getSpec());
            appService.callScheduler(app, YamlEngine.marshal(crInStore), new HashMap<String, String>() {{
                put("instanceName", instanceName);
                put("stopCmd", stopCmd);
            }}, action, appKindService.getProcessorClass(action), 15);
        } catch (SchedulerException | JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        // TODO 多中心需要同时打开其他中心的维护标记
        kubeClient.updateCustomResource(cr, clazz);
    }

    /**
     * @param conditionalStorageModifier 存储仅支持扩容，modifier需判断存储是扩容还是缩容
     */
    public <T extends CustomResource> void handleUpdate(ResourceDTO patch, Consumer<T> cpuMemoryModifier, AppKindService appKindService, Class<T> crClass, Consumer<T> conditionalStorageModifier) {
        handleUpdate(patch, cpuMemoryModifier, appKindService, crClass, conditionalStorageModifier, null);
    }

    public <T extends CustomResource> void handleUpdate(ResourceDTO patch, Consumer<T> cpuMemoryModifier, AppKindService appKindService, Class<T> crClass, Consumer<T> conditionalStorageModifier, Object dataMap) {
        CloudApp app = appService.get(patch.getId() == null ? patch.getAppId() : patch.getId());
        KubeClient kubeClient = clientService.get(app.getKubeId());
        T current = kubeClient.listCustomResource(crClass, app.getCrName(), app.getNamespace());
        T update = YamlEngine.unmarshal(app.getCr(), crClass);
        update.setSpec(current.getSpec());

        // 保持更新前状态
        T snapshot = YamlEngine.unmarshal(YamlEngine.marshal(update), crClass);

        cpuMemoryModifier.accept(update);
        if (conditionalStorageModifier != null) // maybe null as some app don't have storage
            conditionalStorageModifier.accept(update);

        try {
            if (conditionalStorageModifier != null)
                conditionalStorageModifier.accept(snapshot); // 回滚时存储不能缩容
            String applyYaml = Serialization.asYaml(update);
            OpLogContext.instance().CR(applyYaml, Serialization.asYaml(snapshot));

            appService.callScheduler(app, applyYaml, dataMap, ActionEnum.UPDATE, appKindService.getProcessorClass(ActionEnum.UPDATE));
            kubeClient.updateCustomResource(update, crClass);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @param appId
     * @param params
     * @param crClass
     * @param appKindService
     * @param consumer
     * @param <T>
     */
    public <T extends CustomResource> void handleModifyParam(Integer appId, Map<String, String> params, Class<T> crClass, AppKindService appKindService, BiConsumer<T, Function<Map<String, String>, Map<String, String>>> consumer) {
        CloudApp app = appService.get(appId);
        KubeClient kubeClient = clientService.get(app.getKubeId());
        T current = kubeClient.listCustomResource(crClass, app.getCrName(), app.getNamespace());
        appKindService.prohibitParam(String.join(",", params.keySet()));
        consumer.accept(current, (curConfigMap) -> {
            if (curConfigMap == null) curConfigMap = new HashMap<>();
            curConfigMap.putAll(params); // todo 直接替换 or 合并
            return curConfigMap;
        });
        T crInDb = YamlEngine.unmarshal(app.getCr(), crClass);
        crInDb.setSpec(current.getSpec());
        try {
            String applyYaml = YamlEngine.marshal(crInDb);
            OpLogContext.instance().YAML("CR", applyYaml, app.getCr());
            appService.callScheduler(app, applyYaml, null, ActionEnum.MODIFY_PARAM, appKindService.getProcessorClass(ActionEnum.MODIFY_PARAM));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        kubeClient.updateCustomResource(crInDb, crClass);
    }

    /**
     * @param appId
     * @param password
     * @param newSecretPasswordKey
     * @param appKindService
     * @param crClass
     * @param modifier
     * @param nameMapper
     * @param keyMapper
     * @param <T>
     */
    public <T extends CustomResource> void handleUpdatePassword(int appId, Password password, String newSecretPasswordKey, AppKindService appKindService, Class<T> crClass, BiConsumer<Secret, T> modifier, Function<T, String> nameMapper, Function<T, String> keyMapper) {
        handleUpdatePassword(appId, password, false, newSecretPasswordKey, appKindService, crClass, modifier, nameMapper, keyMapper);
    }

    public <T extends CustomResource> void handleUpdatePassword(int appId, Password password, boolean needCheckOld, String newSecretPasswordKey, AppKindService appKindService, Class<T> crClass, BiConsumer<Secret, T> modifier, Function<T, String> nameMapper, Function<T, String> keyMapper) {
        CloudApp app = appService.get(appId);
        KubeClient kubeClient = clientService.get(app.getKubeId());
        T cr = kubeClient.listCustomResource(crClass, app.getCrName(), app.getNamespace());
        String secretName = nameMapper.apply(cr);
        io.fabric8.kubernetes.api.model.Secret secret = kubeClient.getSecret(app.getNamespace(), secretName);

        Secret newSecret;
        if (secret != null) {
//            io.fabric8.kubernetes.api.model.Secret secretBackup = KubeClientUtil.backupResource(secret);
            String s = secret.getData().get(keyMapper.apply(cr));
            String expOldPwd = new String(Base64.getDecoder().decode(s));
            String givenOldPwd = password.getOldPassword();
            CustPreconditions.checkState(!needCheckOld || expOldPwd.equals(givenOldPwd), "原密码不正确");
            secret.getData().put(newSecretPasswordKey, Base64.getEncoder().encodeToString(password.getNewPassword().getBytes(StandardCharsets.UTF_8)));
            newSecret = kubeClient.updateSecret(secret);
//            OpLogContext.instance().YAML(secret, secretBackup);
        } else {
            newSecret = kubeClient.createSecret(secretName, app.getNamespace(), newSecretPasswordKey, password.getNewPassword());
//            OpLogContext.instance().YAML(newSecret, null);
        }

        try {
            if (modifier != null) {
                modifier.accept(newSecret, cr);
            }
            T crInStore = Serialization.unmarshal(app.getCr(), crClass);
            crInStore.setSpec(cr.getSpec());
            String applyYaml = Serialization.asYaml(crInStore);
            OpLogContext.instance().CR(applyYaml, app.getCr());
            Map<String, Object> map = new HashMap<>();
            map.put("password", SymmetricEncryptionUtil.getEncryptInstance().encrypt(password.getNewPassword()));
            appService.callScheduler(app, applyYaml, map, ActionEnum.UPDATE_PASSWORD, appKindService.getProcessorClass(ActionEnum.UPDATE_PASSWORD));
            kubeClient.updateCustomResource(crInStore, crClass);
        } catch (Exception e) {
            if (secret == null)
                kubeClient.deleteSecret(secretName, app.getNamespace()); // rollback
            throw new RuntimeException(e);
        }

    }

    public <T extends CustomResource> void handleUninstall(int id, Class<T> type, AppKindService appKindService) {
        CloudApp app = appService.get(id);
        if (app == null) {
            throw new RuntimeException("app of id did not exist," + id);
        }
        // 暂停现有的watch作业
        appService.deleteCurrentTriggersOfApp(id);
        try {
            appService.callScheduler(app, DELETE, null, appKindService.getProcessorClass(DELETE));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        clientService.get(app.getKubeId()).deleteCustomResource(app.getNamespace(), app.getCrName(), type);
    }

    public <T extends CustomResource> void handleService(
            CloudApp app, List<ServiceManager> deltaSvcMgrs,
            Consumer<T> modifier, Class<T> crClass, AppKindService appKindService, Map data, ActionEnum actionEnum) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        T cr = kubeClient.listCustomResource(crClass, app.getCrName(), app.getNamespace());
        modifier.accept(cr);
        T crInStore = YamlEngine.unmarshal(app.getCr(), crClass);
        crInStore.setSpec(cr.getSpec());
        Map mergeData = data == null ? new HashMap() : data;
        if (actionEnum == ActionEnum.UPDATE_SERVICE) {
            mergeData.put("svms", deltaSvcMgrs);
        }
        try {
            String applyYaml = YamlEngine.marshal(crInStore);
            OpLogContext.instance().CR(applyYaml, app.getCr());
            appService.callScheduler(app, applyYaml, mergeData, actionEnum, appKindService.getProcessorClass(actionEnum));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        kubeClient.updateCustomResource(crInStore, crClass);
    }

    public <T extends CustomResource> void handleServiceOnInstall(CloudAppVO app, List<ServiceManager> deltaSvcMgrs, Consumer<T> modifier, Class<T> crClass, AppKindService appKindService, Map data, ActionEnum actionEnum, T installCr) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        T cr = kubeClient.listCustomResource(crClass, app.getCrName(), app.getNamespace());
        if (ObjectUtils.isEmpty(cr)) {
            cr = installCr;
        }
        modifier.accept(cr);
//        T crInStore = YamlEngine.unmarshal(app.getCr(), crClass);
//        crInStore.setSpec(cr.getSpec());
        Map mergeData = data == null ? new HashMap() : data;
        if (actionEnum == ActionEnum.DELETE_SERVICE) {
            mergeData.put("delPorts", deltaSvcMgrs.stream().map(smgr -> smgr.getPort() + "").collect(Collectors.joining(",")));
            mergeData.put("delIds", deltaSvcMgrs.stream().map(smgr -> smgr.getId() + "").collect(Collectors.joining(",")));
        }
        if (actionEnum == ActionEnum.UPDATE_SERVICE)
            mergeData.put("oldPorts", deltaSvcMgrs.stream().map(smgr -> smgr.getPort() + "").collect(Collectors.joining(",")));
        try {
            String applyYaml = YamlEngine.marshal(cr);
            OpLogContext.instance().CR(applyYaml, app.getCr());
            //放入端口信息
            if (ObjectUtils.isEmpty(app.getExtInstallData())) {
                app.setExtInstallData(mergeData);
            } else {
                app.getExtInstallData().putAll(mergeData);
            }
            appService.update(app);
//            appService.callScheduler(app, applyYaml, mergeData, actionEnum, appKindService.getProcessorClass(actionEnum));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
//        kubeClient.updateCustomResource(crInStore, crClass);
    }

    private static class AutoWrapAppendable {
        private Appendable appendable;

        public AutoWrapAppendable(Appendable appendable) {
            this.appendable = appendable;
        }

        public AutoWrapAppendable append(CharSequence chars) {
            try {
                this.appendable.append(chars).append("\n");
            } catch (IOException ignore) {
            }
            return this;
        }

        public String toString() {
            return appendable.toString();
        }
    }
}
