package cn.newdt.cloud.service;

import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.CloudBackupStorage;
import cn.newdt.cloud.vo.CloudBackupStorageVO;
import cn.newdt.cloud.domain.CloudDataStorage;
import cn.newdt.cloud.dto.ConfigAndStorageResourceDTO;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.dto.ResourceDTO;
import cn.newdt.cloud.vo.CloudAppVO;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * 资源配额管理服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/20 17:43
 */
public interface ResourceManagerService {

    /**
     * 删除PV
     *
     * @param pvNameList
     * @return
     */
    void deletePv(List<ConfigAndStorageResourceDTO> pvNameList);

    boolean deletePv(Integer kubeId, String pvName, Integer appId);

    /**
     * 删除PVC
     *
     * @param namespace
     * @param name
     * @param appId
     * @return
     */
    boolean deletePvc(Integer kubeId, String namespace, String name, Integer appId);

    /**
     * delete Pvc by kubeId and pvcName and namespace
     * 如果pvc不存在storageClassName，先删除pvc， 再通过pvc的volumeName找到对应的pv，删除对应的pv，否则直接删除pvc
     * @param namespace
     * @param pvcNameList
     * @return
     */
    void deletePvcByName(String namespace, List<ConfigAndStorageResourceDTO> pvcNameList);

    /**
     * get kubeId、namespace、labels、kind from appId , then get pvList by kubeId、namespace、crName、kind
     *
     * @param appId
     * @param appLogicId
     * @return List<Map < attributeName, attributeValue>>
     */
    List<Map<String, String>> pvcList(Integer appId, Integer appLogicId);

    /*
        查询所有pvc
     */
    List<Map<String, String>> allPvcList(Integer appId, Integer appLogicId, Boolean other, Integer kubeId, Integer tenantId);

    /**
     * get kubeId、namespace、labels、kind from appId , then get pvList by kubeId、namespace、crName、kind
     *
     * @param appId
     * @param appLogicId
     * @return List<Map < attributeName, attributeValue>>
     */
    List<Map<String, String>> pvList(Integer appId, Integer appLogicId, Boolean other, Integer kubeId, Integer tenantId);

    /**
     * page 作为搜索的条件和分页条件，搜索条件默认kubeId和kind
     * @param page
     * @return
     */
    PageInfo<CloudAppVO> searchPageInfo(PageDTO page);

    PageInfo searchPage4AppView(PageDTO page);

    /**
     * get kubeId, kind, crName, namespace by appId
     * delete Pvc by kubeId, kind, crName, namespace
     * 通过kubeId, kind, namespace拿到所有的pvc，根据crName匹配到具体的pvc
     * 如果pvc不存在storageClassName，先删除pvc， 再通过pvc的volumeName找到对应的pv，删除对应的pv，否则直接删除pvc
     * @param appIdList
     * @param view
     * @return
     */
    void deletePvcByAppInfo(List<Integer> appIdList, String view);

    void updateZookeeperCPUMemory(ResourceDTO patch) throws Exception;

    void cleanStorageScaleInAfter(CloudApp app, List<String> releasedIps);

    PageInfo<CloudDataStorage> listDataStorage(PageDTO pageDTO);

    /**
     * 获取备份存储信息后，如果是s3会将secretKey解密，用于备份以及flink安装等
     *
     * @return
     */
    CloudBackupStorageVO listBackupStorage();

    /**
     * 直接从表中获取备份存储信息，不会对secretKey解密，用于备份存储配置的查询
     *
     * @return
     */
    CloudBackupStorageVO listBackupStorageEncrypt();

    /**
     * 获取表中备份存储配置的方法，被listBackupStorageEncrypt和listBackupStorage引用，刚查询出来的secretKey是加密的
     *
     * @return
     */
    CloudBackupStorageVO listBackupStoragePublic();

    void insertDataStorage(CloudDataStorage cloudDataStorage);

    void insertBackupStorage(CloudBackupStorageVO cloudBackupStorage);

    void updateDataStorage(CloudDataStorage cloudDataStorage);

    void updateBackupStorage(CloudBackupStorageVO cloudBackupStorage);

    void deleteDataStorage(Integer id);

    void deleteBackupStorage(Integer id);

    List<String> getBackupStorageSupport();

    /**
     * 仅查询配置，不查容量
     * @see #listBackupStorage
     */
    List<CloudBackupStorage> listStorageConfigOnly();

    void deletePvcByApp(CloudApp id);

    Map<String, Object> checkMount(CloudBackupStorageVO cloudBackupStorage);
}
