package cn.newdt.cloud.service.alert;

import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.domain.CloudSysConfig;
import cn.newdt.cloud.mapper.CloudSysConfigMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.KubeClientService;
import cn.newdt.cloud.service.KubeConfigService;
import cn.newdt.cloud.service.impl.SysConfigService;
import cn.newdt.cloud.utils.ESUtil;
import cn.newdt.commons.exception.CustomException;
import com.google.common.collect.ImmutableMap;
import io.fabric8.kubernetes.api.model.GroupVersionKind;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.AcknowledgedResponse;
import org.elasticsearch.client.indexlifecycle.*;
import org.elasticsearch.common.unit.TimeValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.newdt.cloud.constant.AlertConfigConstant.*;
import static cn.newdt.cloud.constant.DatasourceConstant.CLOUD_SYS_CONFIG;
import static cn.newdt.cloud.constant.DatasourceConstant.SCHEMA;

/**
 * 监控告警数据清理相关配置项的管理方法——
 * 不同配置(Configuration)修改触发相应的变更事件，封装在ConfigurationListener#onChange。
 */
@Service
@Slf4j
public class RetentionConfigureService {

    @Autowired
    private KubeClientService kubeClientService;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private KubeConfigService kubeConfigService;
    @Autowired
    CloudSysConfigMapper mapper;

    public void modifyRetentionConfiguration(List<Configuration> configurationList) {

        for (Configuration configuration : configurationList) {
            String oldValue = configuration.value;
            if (CloudAppConstant.SysCfgName.PROMETHEUS_RETENTION.equalsIgnoreCase(configuration.name)) {
                configuration.setValue(configuration.value + "d");
            } else if (CloudAppConstant.SysCfgName.PROMETHEUS_RETENTIONSIZE.equalsIgnoreCase(configuration.name)) {
                configuration.setValue(configuration.value + "GB");
            }
            CloudSysConfig cloudSysConfig = new CloudSysConfig();
            cloudSysConfig.setCategory(CloudAppConstant.SysCfgCategory.ALERT_RETENTION_CFG);
            cloudSysConfig.setName(configuration.name);
            cloudSysConfig.setData(oldValue);
            mapper.updateSysConfigByCategoryAndName(SCHEMA, CLOUD_SYS_CONFIG, cloudSysConfig);
        }

        // 可能部分成功
        runAsync(groupConfiguration(configurationList));
    }

    private void runAsync(List<GroupedConfiguration> groupedConfigurations) {
        try {
            CompletableFuture.allOf(groupedConfigurations.stream().map(g -> CompletableFuture.runAsync(g::change))
                    .toArray(CompletableFuture[]::new)).join();
        } catch (Exception e) {
            log.error("", e);
            throw new CustomException(600, e.getCause().getMessage());
        }
    }


    private List<GroupedConfiguration> groupConfiguration(List<Configuration> configurationList) {
        Map<String, String> alertConfig = sysConfigService.find(CloudAppConstant.SysCfgCategory.ALERT_CFG)
                .stream().collect(Collectors.toMap(cfg -> cfg.getName(), cfg -> cfg.getData()));

        String am_ns_name = Optional.ofNullable(alertConfig.get(NAMESPACE_NAME))
                .orElseThrow(() -> new CustomException(600, "查询告警系统配置错误"));
        String pm_ns_name = Optional.ofNullable(alertConfig.get(PM_NAMESPACE_NAME))
                .orElseThrow(() -> new CustomException(600, "查询告警系统配置错误"));

        List<KubeClient> kubeClient = kubeConfigService.findEnabled(null).stream()
                .map(kc -> kubeClientService.get(kc.getId()))
                .collect(Collectors.toList());

        Map<String, ConfigurationChangeListener> listenerContainer = ImmutableMap.of(
                "prometheusListener", prometheusListener(kubeClient, pm_ns_name.split("_")[0],
                        pm_ns_name.split("_")[1]),
                "almListener", almListener(kubeClient, am_ns_name.split("_")[0],
                        am_ns_name.split("_")[1]),
                "ilmPolicyListener", ilmPolicyListener()
        );

        Map<ConfigurationChangeListener, List<Configuration>> collect =
                configurationList.stream().collect(Collectors.groupingBy(c ->
                        listenerContainer.get(listenerMap.get(c.name))));

        return collect.keySet().stream()
                .map(listener -> new GroupedConfiguration(collect.get(listener), listener))
                .collect(Collectors.toList());
    }


    // mapping config name to configure action
    Map<String, String> listenerMap = ImmutableMap.of(
            PROMETHEUS_RETENTION, "prometheusListener", // todo spel configurable
            PROMETHEUS_RETENTION_SIZE, "prometheusListener",
            ALERTMANAGER_RETENTION, "almListener",
            ALERTLOG_RETENTION, "ilmPolicyListener",
            ALERTLOG_RETENTION_THRESHOLD, "ilmPolicyListener"
    );

    @Deprecated
    private ConfigurationChangeListener almListener(List<KubeClient> kubeClient, String namespace, String name) {
        return kubeListener((configurationList, k) -> {
            Map<String, String> specProperties = new HashMap<>();
            for (Configuration configuration : configurationList) {
                specProperties.put(configuration.name.replace("alertmanager_", ""), String.valueOf(configuration.value));
            }
            final GroupVersionKind gvk = new GroupVersionKind("monitoring.coreos.com", "Alertmanager", "v1");
            k.updateGenericResource(gvk, gvk.getKind().toLowerCase() + "s", namespace, name, specProperties);
        }, kubeClient);
    }

    private ConfigurationChangeListener prometheusListener(List<KubeClient> kubeClient, String namespace, String name) {
        return kubeListener((configurationList, k) -> {
            Map<String, String> specProperties = new HashMap<>();
            for (Configuration configuration : configurationList) {
                specProperties.put(configuration.name.replace("prometheus_", ""), String.valueOf(configuration.value));
            }
            // todo update pvc size as well
            final GroupVersionKind gvk = new GroupVersionKind("monitoring.coreos.com", "Prometheus", "v1");
            k.updateGenericResource(gvk, gvk.getKind().toLowerCase() + "es", namespace, name, specProperties);
        }, kubeClient);
    }

    // generic kube listener collect multiple kube client execution results
    private ConfigurationChangeListener kubeListener(BiConsumer<List<Configuration>, KubeClient> listener, List<KubeClient> kubeClients) {
        return configurationList -> {
            List<RuntimeException> exceptions = Collections.synchronizedList(new ArrayList<>());
            List<String> results = Collections.synchronizedList(new ArrayList<>());
            kubeClients.stream().map(k ->
                    CompletableFuture.runAsync(() -> listener.accept(configurationList, k)).whenComplete((r, e) -> {
                        if (e != null) {
                            exceptions.add(new RuntimeException(String.format("Complete with error %s, %s", k.getConfig().getName(), e.getMessage()), e));
                        } else {
                            results.add(String.format("Complete with result %s, %s", k.getConfig().getName(), r));
                        }
                    })).collect(Collectors.toList())
                    .stream()
                    .peek(future -> {
                        try {
                            future.join();
                        } catch (Exception ignore) {
                        }
                    }).collect(Collectors.toList());

            if (!exceptions.isEmpty()) {
                Stream<String> exception = exceptions.stream().map(Throwable::getMessage);
                Stream.concat(exception, results.stream()).peek(log::error).collect(Collectors.joining("<br>"));
                throw new CustomException(600, "更新监控数据清理配置部分失败", "更新监控数据清理配置部分失败");
            }
        };
    }


    @Autowired
    private ESUtil esUtil;
    private ConfigurationChangeListener ilmPolicyListener() {
        return configurationList -> {
            Map<Object, Configuration> any = configurationList.stream()
                    .filter(cfg -> cfg.getName().contains("alertlog_retention"))
                    .collect(Collectors.toMap(cfg -> cfg.getName(), Function.identity()));
            if (!any.keySet().isEmpty()) {
                try (RestHighLevelClient esClient = esUtil.getESClient()) {
                    // 用户指定的保留时间，ilm的rolloverAction.maxAge以及deleteAction.minAge将设置为该保留时间
                    // 实际索引将保留 2 times x retentionTime
                    // 以确保索引rollover时最后一条记录能在retentionTime期间保留
                    Configuration retentionTime = any.get(ALERTLOG_RETENTION);
//                    Configuration rolloverTime = any.get(ALERTLOG_RETENTION_THRESHOLD);
                    Map<String, LifecycleAction> rolloverAction =
                            Collections.singletonMap(RolloverAction.NAME, new RolloverAction(null,
                                    TimeValue.parseTimeValue(retentionTime.getValue(), "maxAge"), null));
                    Map<String, LifecycleAction> deleteAction =
                            Collections.singletonMap(DeleteAction.NAME, new DeleteAction());;
                    Phase hotPhase = new Phase("hot", null, rolloverAction);
                    Phase deletePhase = new Phase("delete",
                            TimeValue.parseTimeValue(retentionTime.getValue(), "retention time"),
                            deleteAction);
                    Map<String, Phase> phases = ImmutableMap.of("hot", hotPhase, "delete", deletePhase);
                    LifecyclePolicy policy = new LifecyclePolicy(NDT_ALERTLOG_ILM_POLICY, phases);
                    PutLifecyclePolicyRequest request = new PutLifecyclePolicyRequest(policy);
                    AcknowledgedResponse response = esClient.indexLifecycle().
                            putLifecyclePolicy(request, RequestOptions.DEFAULT);
                    if (!response.isAcknowledged())
                        log.info("update lifecycle policy failed, response " + response);
                } catch (Exception e) {
                    throw new RuntimeException("告警历史清理策略更新失败", e);
                }
            }
        };
    }

    public Object list() {
        return sysConfigService.find(CloudAppConstant.SysCfgCategory.ALERT_RETENTION_CFG)
                .stream()
                .filter(cfg -> cfg.getName().contains("retention"))
                .map(sysCfg -> {
                    Configuration configuration = new Configuration();
                    configuration.setName(sysCfg.getName());
                    configuration.setValue(sysCfg.getData());
                    configuration.setSummary(sysCfg.getDescription());
                    return configuration;
                }).collect(Collectors.toList());
    }

    @Data
    public static class Configuration {
        private String name;
        private String summary;
        private String value;
        private String previousValue;
    }

    class GroupedConfiguration {
        private List<Configuration> configurationList;

        private ConfigurationChangeListener listener;

        public GroupedConfiguration(List<Configuration> configurationList, ConfigurationChangeListener listener) {
            this.configurationList = configurationList;
            this.listener = listener;
        }

        public void change() {
            listener.onChange(configurationList);
        }
    }

    interface ConfigurationChangeListener {
        void onChange(List<Configuration> configurationList);
    }

}


