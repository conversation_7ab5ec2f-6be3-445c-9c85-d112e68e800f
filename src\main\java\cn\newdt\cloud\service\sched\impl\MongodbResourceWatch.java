package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.StatusConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.CloudAppLogic;
import cn.newdt.cloud.domain.ResourceChangeHis;
import cn.newdt.cloud.domain.cr.MongoDBCommunity;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.utils.MongoUtil;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import io.fabric8.kubernetes.api.model.Container;
import io.fabric8.kubernetes.api.model.Quantity;
import io.fabric8.kubernetes.client.CustomResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.ScheduleConstant.JOB_DATA_KEY_CHANGE_ID;

@Slf4j
public class MongodbResourceWatch extends OpsProcessorContext implements OpsPostProcessor<MongoDBCommunity> {

    @Override
    public OpsResultDTO postProcess(TriggerHis triggerHis) throws Exception {
        Map<String, String> jobDataMap = triggerHis.returnMergedJobDataMap();
        String appId = triggerHis.getJobDataMap().get("appId");
        CloudApp app = appService.get(Integer.valueOf(appId));
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        if (AppKind.MongoDB.getKind().equals(app.getKind())) {
            MongoDBCommunity cr = kubeClient.listCustomResource(MongoDBCommunity.class, app.getCrName(), app.getNamespace());

            OpsResultDTO.Builder result = evalOpsResult(triggerHis, cr, app, kubeClient);
            checkTimeout(triggerHis, result);

            // 根据备份文件创建应用的恢复操作
            getIsReStoreSuccess(jobDataMap, app, result);
            OpsResultDTO dto = result.build();
            if (dto.getStopJob()) {
//            mongoDbService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                stopWatchResource(app, triggerHis, result, dto);
            }
            return dto;
        }
        return OpsResultDTO.builder().build();
    }

    public OpsResultDTO.Builder evalOpsResult(TriggerHis triggerHis, CustomResource genericCr, CloudApp app, KubeClient kubeClient) throws Exception {
        OpsResultDTO.Builder result = OpsResultDTO.builder().stopJob(false);
        if (null == genericCr) {
            String crLoseFailMsg = String.format("[mongoDB]实例名称：==%s，实例信息丢失！", app.getCrName());
            log.error(crLoseFailMsg);
            return result.stopJob(true).status(StatusConstant.FAIL).msg(crLoseFailMsg);
        }

        MongoDBCommunity cr = (MongoDBCommunity) genericCr;
        if (cr == null) {
            String msg = "cr.status not exists";
            return OpsResultDTO.builder().stopJob(true)
                    .msg(msg);
        }
        String crRun = YamlEngine.marshal(cr);
        MongoDBCommunity.MongoDBStatus status = cr.getStatus();
        if (null == status || StringUtils.isEmpty(status.getPhase()))
            return result.msg(String.format("[mongoDB]实例名称：==%s，暂未获取到当前实例的状态信息...", app.getCrName()));

        result.msg(status.getMessage());
        if (MongoDBCommunity.MongoPhase.Running == MongoDBCommunity.MongoPhase.valueOf(status.getPhase())) {
            List<PodDTO> pods = kubeClient.listPod(app.getNamespace(), MongoUtil.getPodLabel(app.getCrName()));

            Pattern compile = Pattern.compile("(members: (\\d+).*)");
            Matcher matcher = compile.matcher(crRun);
            if (matcher.find()) {
                int expected = Integer.parseInt(matcher.group(2));
                if (pods.size() == expected) {
                    result.stopJob(true).msg("success").status(StatusConstant.SUCCESS);
                } else {
                    result.msg(String.format("expected:%d, satisfied:%d", expected, pods.size()));
                }
            }
        } else {
            log.info("watched mongodb's status-" + status.getPhase());
            result.stopJob(false).msg("the cluster was in phase:" + status.getPhase() + " because of " + status.getMessage());
//                if (MongoDBCommunity.MongoPhase.Failed == MongoDBCommunity.MongoPhase.valueOf(status.getPhase())) {
//                    result.msg(status.getMessage());
//                } else {
//                }
        }
        return result;

    }

    /**
     * pod状态是否running
     */
    private boolean matchStatus(PodDTO p) {
        return CloudAppConstant.PodStatus.RUNNING.equalsIgnoreCase(p.getStatus());
    }

    void stopWatchResource(CloudApp app, TriggerHis triggerHis, OpsResultDTO.Builder result, OpsResultDTO build) {
        doStopWatchResource(app, triggerHis, result.isSuccessful(), build);
        appService.handleWatchResult(app.getId(), result.isSuccessful());
    }

    void doStopWatchResource(CloudApp app, TriggerHis triggerHis, boolean successful, OpsResultDTO build) {
        ResourceChangeHis his = resourceChangeHisService.get(Integer.parseInt(triggerHis.getJobDataMap().get(JOB_DATA_KEY_CHANGE_ID)));
        ActionEnum actionEnum = ActionEnum.actionTypeOf(his.getAction());
        if (actionEnum == ActionEnum.UPGRADE) {
            String newVersion = triggerHis.returnMergedJobDataMap().get("newVersion");
            CloudAppLogic cloudAppLogic = new CloudAppLogic();
            cloudAppLogic.setVersion(newVersion);
            cloudAppLogic.setId(app.getLogicAppId());
            appLogicService.update(cloudAppLogic);
            if (autoManagement) {
                CloudApp manageApp = new CloudApp();
                manageApp.setLogicAppId(app.getLogicAppId());
                manageApp.setVersion(newVersion);
                //纳管端口
                operationUtil.alterToDMP(manageApp, triggerHis);
            }
        }
    }


}
