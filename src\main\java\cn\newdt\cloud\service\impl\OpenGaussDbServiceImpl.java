package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.common.TriFunction;
import cn.newdt.cloud.config.CloudRequestContext;
import cn.newdt.cloud.config.FutureService;
import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.dmp_common.utils.SpringBeanUtil;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.dto.*;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.service.sched.impl.*;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.*;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.bean.MetaVO;
import cn.newdt.commons.bean.MysqlParamRules;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.SymmetricEncryptionUtil;
import cn.newdt.commons.utils.UserUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.shindata.opengauss.v1.OpenGaussCluster;
import com.shindata.opengauss.v1.OpenGaussClusterSpec;
import com.shindata.opengauss.v1.opengaussclusterspec.DataStorage;
import com.shindata.opengauss.v1.opengaussclusterspec.Ftp;
import com.shindata.opengauss.v1.opengaussclusterspec.Iplist;
import com.shindata.opengauss.v1.opengaussclusterspec.Schedule;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.client.CustomResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import java.io.BufferedReader;
import java.io.StringReader;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.ActionEnum.*;
import static cn.newdt.cloud.constant.CloudAppConstant.LABEL_NODE_NAME;
import static cn.newdt.cloud.constant.CloudAppConstant.Operator.IN;
import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgCategory.PARAM_PROHIBIT;
import static cn.newdt.cloud.utils.DateUtil.parseUTC;

@Slf4j
@Service
public class OpenGaussDbServiceImpl extends DefaultAppKindService<OpenGaussCluster> implements MigrateOperation, StartStopOperation, ServiceManageOperation {

    private static final String OG_PVC_PATTERN = "og-%s-pod-%s-data-pvc";
    private static final String OG_USER_NAME = "k8sadmin";
    private static final String OG_PASS_WORD = "K8S@admin";
    private static final String OG_OMM_USER_NAME = "omm";

    @Autowired
    private KubeClientService kubeClientService;

    @Autowired
    private KubeConfigService kubeConfigService;

    @Autowired
    CloudDbParamTemplateService cloudDbParamTemplateService;

    @Autowired
    CloudDatabaseUserService cloudDatabaseUserService;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private CloudDatabaseUserService dbUserService;

    @Autowired
    private CloudFTPUtil ftpUtil;

    @Override
    public PageInfo<? extends CloudAppVO> searchPage(PageDTO page) {
        return super.searchPage(page);
    }

    @Autowired
    private AppOperationHandler operationHandler;

    //    /**
//     * 集群扩容
//     *
//     * @param appId
//     * @param scaleOutNum
//     * @return
//     */
//    @Override
//    @Transactional
//    public String clusterScaleOut(Integer appId, Integer scaleOutNum) throws Exception {
//        //判断appid和scaleOutNum是否为null
//        CustPreconditions.checkNotNull(appId, "appId not be null!");
//        CustPreconditions.checkNotNull(scaleOutNum, "scaleOutNum not be null!");
//        //获取app信息
//        CloudApp app = appCommonService.get(appId);
//        //1.分配扩容的节点和Ip
//        List<NodeDTO> nodes = networkService.chooseNode(app, scaleOutNum, false);
//        String cniType = appCommonService.getCniType(app.getKubeId());
//        List<String> ips = networkService.allocateIp(app, cniType, scaleOutNum, "pod");
//        CloudApp.IpNode[] added = AppUtil.composeNodeAndIp(nodes, ips);
//
//        String oldIpList = app.getIpList();
//        CloudApp.IpNode[] merged = ArrayUtils.addAll(app.getIpNode(), added);
//        app.setIpList(JsonUtil.toJson(merged));
//        log.info("扩容后ipList:" + app.getIpList());
//
//        // 预留ip成功标识
//        Map map = new HashMap();
//        map.put("app", app);
//        map.put("type", ActionEnum.SCALE_OUT);
//        map.put("outIpNodeList", JsonUtil.toJson(added));
//        map.put("oldIpNodeList", oldIpList);
//        appCommonService.callScheduler(app, ActionEnum.SCALE_OUT, map, OpenGaussWatch.class);
//        kubeClientService.get(app.getKubeId()).updateCustomResource(new OpenGaussCluster(app), OpenGaussCluster.class);
//        return "提交成功";
//
//    }

    @Override
    protected void completeInstanceProperty(AppInstanceVO appInstanceVO, PodDTO pod, CloudApp app, KubeClient client) {

        AppInstanceVO tmp = null;
        if (pod.getPod().getMetadata().getName().equalsIgnoreCase(appInstanceVO.getPodName())) {
            tmp = appInstanceVO;
        }

        //查询角色
        String roleName = null;
        Map<String, String> labels = pod.getPod().getMetadata().getLabels();
        for (String labelKey : labels.keySet()) {
            if ("opengauss.role".equalsIgnoreCase(labelKey)) {
                roleName = labels.get(labelKey);
                break;
            }
        }
        if ("standby".equalsIgnoreCase(roleName)) {
            tmp.setRole(CloudAppConstant.ROLE_SECONDARY);
        } else if ("primary".equalsIgnoreCase(roleName)) {
            tmp.setRole(CloudAppConstant.ROLE_PRIMARY);
        } else {
            tmp.setRole(roleName);
        }

        // liveness
        try {
            int liveness = 0;
            // String livenessProbe = "sh gauss/files/K8SLivenessProbe.sh && echo 1 || echo 0";
            // 状态信息修改为：直接查询相关进程是否存在
            String ogProcessCmd = "gs_ctl status -D ${PGDATA} | grep -Eo 'PID: ([0-9]+)' | awk -F ': ' '{print $2}'";
            String data = client.execCmd(tmp.getNamespace(), tmp.getPodName(), getKind().getContainerName(), "sh", "-c", ogProcessCmd);
            // data = data != null ? data.replaceAll("\\s", "") : null;
            if (StringUtils.isEmpty(data)) {
                liveness = 0;
            } else {
                liveness = 1;
            }
            tmp.setLiveness(liveness);
        } catch (Exception e) {
            log.error("liveness probe error " + e.getMessage());
            tmp.setLiveness(2);
        }

        String cmd = "";
        String result ="";
        //通过查看是否存在maintenance文件来设置实例的enableMaintenance字段
        try {
            if (AppKind.OpenGauss.getKind().equals(app.getKind())){
                cmd = CloudAppConstant.Cmd.OPENGAUSS_EXIST_MAINTENANCE_FILE;
                result = kubeClientService.get(app.getKubeId()).execCmd
                        (app.getNamespace(), pod.getPodName(), AppKind.OpenGauss.getContainerName(), "sh", "-c",cmd);
            }
        }catch (Exception e){
            log.info(pod.getPodName()+"---> 此pod未开启过运维窗口,maintenance文件不存在");
        }
        if (StringUtils.contains(result,"2")){
            appInstanceVO.setEnableMaintenance(true);
        }
        // status: stopped
        if (tmp.getLiveness() == 0)
            tmp.setStatus(CloudAppConstant.PodStatus.STOPPED);

    }

    @Override
    protected void completeInstanceProperty(List<AppInstanceVO> instances, int appId) {
        CloudApp app = appService.get(appId);
        List<PodDTO> pods = null;
        try {
            pods = kubeClientService.get(app.getKubeId()).listPod(app.getNamespace(), AppUtil.getPodLabel(app));
        } catch (Exception e) {
            log.error("", e);
            return;
        }
        for (PodDTO pod : pods) {
            AppInstanceVO tmp = null;
            for(AppInstanceVO vo : instances){
                if(pod.getPod().getMetadata().getName().equalsIgnoreCase(vo.getPodName())){
                    tmp = vo;
                    break;
                }
            }
            if (tmp == null) continue;

            //查询角色
            String roleName = null;
            Map<String, String> labels = pod.getPod().getMetadata().getLabels();
            for(String labelKey : labels.keySet()){
                if("opengauss.role".equalsIgnoreCase(labelKey)){
                    roleName = labels.get(labelKey);
                    break;
                }
            }
            if("standby".equalsIgnoreCase(roleName)){
                tmp.setRole(CloudAppConstant.ROLE_SECONDARY);
            }else if("primary".equalsIgnoreCase(roleName)){
                tmp.setRole(CloudAppConstant.ROLE_PRIMARY);
            }else{
                tmp.setRole(roleName);
            }
        }
    }

    @Override
    public AppKind getKind() {
        return AppKind.OpenGauss;
    }

    @Override
    public boolean nodePolicy() {
        return true;
    }

    @Override
    protected void setInstallExtData(CloudAppVO vo) {
        if (StringUtils.isNotEmpty(vo.getUsername()) && StringUtils.isNotEmpty(vo.getPassword())) {
            if (vo.getUsername().equalsIgnoreCase(vo.getPassword())){
                throw new CustomException(600, "数据库管理用户与密码不可相同（包括大小写不同形式），请重新设置");
            }
            vo.appendExtInstallKVData("username", vo.getUsername());
            vo.appendExtInstallKVData("password", vo.getPassword());
        }
    }

    @Override
    public OpenGaussCluster doInstall(CloudAppVO vo, List<String> ips) throws Exception {
        vo.setKind(AppKind.OpenGauss.getKind());
        vo.setArch(AppKind.OpenGauss.getArch());
        try {
            // og 创建时需要readport和writeport
            createConfigMap(vo);
            //networkService.allocateNodePort(vo,2);
            // 添加cr需要的额外的属性
            //根据版本查询镜像名称
            CloudAppConfigService appConfigService = SpringBeanUtil.getBean(CloudAppConfigService.class);
            AppKind appKind = AppKind.valueOf(vo.getKind(), vo.getArch());
            String imageName = appConfigService.getImageManifest(appKind, vo.getVersion()).get(ImageKindEnum.MainImage);
            String ftpImage = appConfigService.getImageManifest(appKind, vo.getVersion()).get(ImageKindEnum.FTP);
            String exporterImage = appConfigService.getImageManifest(appKind, vo.getVersion()).get(ImageKindEnum.Exporter);
            String filebeatImage = appConfigService.getImageManifest(appKind, vo.getVersion()).get(ImageKindEnum.Filebeat);
            String mountImage = appConfigService.getImageManifest(appKind, vo.getVersion()).get(ImageKindEnum.Mount);
//        CloudAppService appCommonService = SpringBeanUtil.getBean(CloudAppService.class);
//        String imageName = appCommonService.getImageByVersion(vo.getKubeId(),vo.getKind(),vo.getVersion());
//        String imageName = "harbor.shindata.com/opengauss/container:2.0.0.v7";
            //ipList转换
            String ipList = vo.getIpList();
            JSONArray ipJsonArray = JSONArray.parseArray(ipList);
            List<Iplist> ipNodeEntries = new ArrayList<>();

            for(int i=0;i<ipJsonArray.size();i++) {
                JSONObject everyIpAndNode = ipJsonArray.getJSONObject(i);
                String ip = everyIpAndNode.getString("ip");
                String node = everyIpAndNode.getString("node");
                Iplist ipNodeEntry = new Iplist();
                ipNodeEntry.setIp(ip);
                ipNodeEntry.setNodename(node);
                ipNodeEntries.add(ipNodeEntry);
            }
            OpenGaussClusterSpec spec = new OpenGaussClusterSpec();
            if (StringUtils.isNotEmpty(exporterImage))
                spec.setExporterimage(exporterImage);
            Ftp ftp = new Ftp();
            ftp.setImage(ftpImage);
            ftp.setUrl(ftpUtil.getURL());
            spec.setFtp(ftp);
            spec.setDbport(getKind().getDbPort());
            spec.setImage(imageName);
            spec.setIplist(ipNodeEntries);
            spec.setCpu(vo.getCpu());
            spec.setMemory(vo.getMemory());

            //新增filebeat配置
            Map<String, String> opengaussConfig = new HashMap<>();
            opengaussConfig.put("image", filebeatImage);
            opengaussConfig.put("configMap", "operator-filebeat-configmap");
            opengaussConfig.put("configFile", "opengauss-ha-filebeat.yaml");
            spec.setFilebeat(opengaussConfig);

            //新增mount容器镜像
            spec.setMountimage(mountImage);

            DataStorage dataStorage = new DataStorage();
            dataStorage.setStorageClass(vo.getStorageClassName());
            dataStorage.setSize(vo.getDisk());
            if(StringUtils.isNotEmpty(vo.getHostpathRoot())){
                dataStorage.setHostpathRoot(vo.getHostpathRoot());
            }
            spec.setDataStorage(dataStorage);
            // 设置同城属性
            spec.setLocalrole(vo.getDisasterRecoveryRole());
            if (!CollectionUtils.isEmpty(vo.getRemoteIps())){
                spec.setRemoteiplist(vo.getRemoteIps());
            }
            OpenGaussCluster openGaussCluster = new OpenGaussCluster();
            openGaussCluster.setMetadata(new ObjectMetaBuilder().withName(vo.getCrName()).withNamespace(vo.getNamespace()).build());
            openGaussCluster.setSpec(spec);
            fixSpecConfigForCr(openGaussCluster, vo);
            Schedule schedule = new Schedule();
            schedule.setAntiAffinityRequired(vo.getAntiAffinityRequired());
            schedule.setNodeAffinity(convertCRNodeAffinity(vo.getSelector(), com.shindata.opengauss.v1.opengaussclusterspec.schedule.NodeAffinity.class));
            schedule.setTolerations(convertCRTolerations(vo.getToleration(), com.shindata.opengauss.v1.opengaussclusterspec.schedule.Tolerations.class));
            spec.setSchedule(schedule);
            return openGaussCluster;
        } catch (Exception e) {
            log.error("创建opengauss的cr失败", e);
//            if (needRecall) networkService.releaseIp(vo, "statefulset");
            throw new CustomException(600, e.getMessage());
        }
    }

    /**
     * 创建configmap为了收集日志
     */
    public void createConfigMap(CloudAppVO vo) {
        KubeClient kubeClient = clientService.get(vo.getKubeId());
        //1 判断应用还是否存在，防止秒删应用后依然创建configmap的情况
        CloudApp cloudApp = appService.get(vo.getId());
        CustPreconditions.checkNotNull(cloudApp, "未找到应用");

        //2 获取配置的configmap名称，判断在当前数据空间内是否已经存在了
        String configMapYaml = sysConfigService.findOne("operator.config", "opengauss.config");

        //判断是否已经存在cm
        Map<String, String> param = new HashMap<>();
        param.put("NAMESPACE", vo.getNamespace());
//        param.put("NAME", FILEBEAT_CONFIG_NAME); // name 在configmap中配置
        param.put("es_host", esUtil.getEsIp() + ":" + esUtil.getEsPort());
        param.put("es_username", esUtil.getEsUsername());
        param.put("es_pwd", esUtil.getEsPassword());
        param.put("es_protocol", esUtil.getProtocol());
        configMapYaml = YamlUtil.evaluateTemplate(configMapYaml, param);
        kubeClient.applyYaml(configMapYaml, vo.getNamespace());

    }

    @Override
    public Class<? extends OpsPostProcessor> getProcessorClass(ActionEnum action) {
        switch (action) {
            case CREATE: return OpenGaussInstallWatch.class;
            case SCALE_OUT:
            case SCALE_IN:
                return OpenGaussScaleWatch.class;
            case MIGRATE:
                return OpenGaussMigrateWatch.class;
            case UPDATE:
            case MODIFY_PARAM:
            case DELETE: return OpenGaussWatch.class;
            case START:
            case START_POD:
            case STOP_POD:
            case STOP:
                return OpengaussNewStartStopWatch.class;
            case UPDATE_SERVICE:
            case DELETE_SERVICE:
            case CREATE_SERVICE:
                return OpenGaussSvcWatch.class;
            default:
                return super.getProcessorClass(action);
        }
    }

    @Override
    protected boolean supportIPAM(CloudAppVO app) {
        return true;
    }

    //    /**
//     * 安装集群
//     *
//     * @param app
//     */
//    @Override
//    @Transactional
//    public CloudApp install(CloudAppVO app) {
//        app.setKind(AppKind.OpenGauss.getProduct());
//        app.setArch(AppKind.OpenGauss.getArch());
//        boolean needRecall = false;
//        try {
//            appCommonService.validate(app);
//            appCommonService.add(app);
//            needRecall = true;
//            Map adminUserParamMap = new HashMap();
//            adminUserParamMap.put("username", app.getUsername());
//            adminUserParamMap.put("password", app.getPassword());
//            needRecall = allocateIp(app, null, app.getCniType());
//            allocatePort(app);
//            appCommonService.callScheduler(app, ActionEnum.CREATE, adminUserParamMap, OpenGaussWatch.class);
//            log.info(JSONObject.toJSONString(new OpenGaussCluster(app)));
//            kubeClientService.get(app.getKubeId()).createCustomResource(new OpenGaussCluster(app), OpenGaussCluster.class, app.getNamespace());
//        } catch (Exception e) {
//            log.error("install error", e);
//            if (needRecall) networkService.releaseIp(app, "statefulset");
//            throw new CustomException(600, e.getMessage());
//        }
//        return app;
//    }

    @Override
    @Transactional
    public void update(ResourceDTO patch) throws Exception {
        Consumer<OpenGaussCluster> modifier = (current) -> {
            OpenGaussClusterSpec spec = current.getSpec();
            spec.setCpu(patch.getCpu());
            spec.setMemory(patch.getMemory());
            spec.getDataStorage().setSize(patch.getDisk());
//            spec.getBackupStorage().setSize(patch.getBackupDisk());
        };

        Consumer<OpenGaussCluster> storageModifier = (current) -> {
            OpenGaussClusterSpec spec = current.getSpec();
            if (MetricUtil.lessAndNotZero(spec.getDataStorage().getSize(), patch.getDisk()))
                spec.getDataStorage().setSize(patch.getDisk());
//            if (MetricUtil.lessAndNotZero(spec.getBackupStorage().getSize(), patch.getBackupDisk()))
//                spec.getBackupStorage().setSize(patch.getBackupDisk());
        };

        operationHandler.handleUpdate(patch, modifier, this, OpenGaussCluster.class, storageModifier);
    }

    @Override
    public void scale(int id, OverrideSpec vo, ActionEnum action) throws Exception {
        if (action == ActionEnum.SCALE_OUT) {
            scale(id, vo.getMembers());
        } else if (action == ActionEnum.SCALE_IN) {
            scaleDown(id, Arrays.asList(vo.getIpNodes()));
        }
    }

    @Override
    @Transactional
    public void scale(int appId, int members) throws Exception {
        CloudApp cloudApp = appService.get(appId);
        int deltaMembers = members - cloudApp.getMembers();

        TriFunction<CloudApp, List<String>, OpenGaussCluster, OpenGaussCluster> modifier = (app, scaledIPs, openGauss) -> {
            OpenGaussCluster cr = YamlEngine.unmarshal(app.getCr(), OpenGaussCluster.class);
            cr.setSpec(openGauss.getSpec());
            List<Iplist> currentIpList = cr.getSpec().getIplist();
            for (String scaledIP : scaledIPs) {
                Iplist iplist = new Iplist();
                iplist.setIp(scaledIP);
                currentIpList.add(iplist);
            }

            return cr;
        };
        operationHandler.handleScaleUp(appId, deltaMembers, OpenGaussCluster.class, this, getIpOwnerKind(), getIpReservationTarget(), modifier);
    }

    @Override
    public String getIpReservationTarget() {
        return "pod";
    }

    @Override
    @Transactional
    public void scaleDown(int appId, List<CloudApp.IpNode> scaled) throws Exception{
        TriFunction<CloudApp, List<String>, OpenGaussCluster, OpenGaussCluster> modifier = (app1, scaledIPs, openGauss) -> {
            OpenGaussCluster cr = YamlEngine.unmarshal(app1.getCr(), OpenGaussCluster.class);
            cr.setSpec(openGauss.getSpec());
            cr.getSpec().getIplist().removeIf(ip -> scaledIPs.contains(ip.getIp()));
            return cr;
        };
        operationHandler.handleScaleDown(appId, scaled, modifier, OpenGaussCluster.class, getProcessorClass(SCALE_IN), null);
    }
    public List<Map<String, String>> listPv(Integer kubeId, Map<String, String> labels) {
        if (kubeId == null) {
            // 所有集群
            List<Map<String, String>> pvcProps = new LinkedList<>();
            for (KubeConfig each : kubeConfigService.list(null)) {
                pvcProps.addAll(listPvByKube(each.getId(), null));
            }
            return pvcProps;
        } else {
            // 指定集群
            return listPvByKube(kubeId, labels);
        }
    }

    public List<Map<String, String>> listPvByKube(Integer kubeId, Map<String, String> labels) {
        List<Map<String, String>> pvProps = new LinkedList<>();
        if (kubeId == null) {
            return pvProps;
        }
        PersistentVolumeList persistentVolumeList;
        List<Map<String, String>> pvcList;
        try {
            // 所有PV
            persistentVolumeList = kubeClientService.get(kubeId).listPv(labels);
            pvcList = listPvc(kubeId, null, null);
        } catch (Exception e) {
            log.error("kubeId:{} listPvc error:{}", kubeId, e);
            return pvProps;
        }
        // 筛选MongoDbPV
        List<PersistentVolume> pvList = new LinkedList<>();
        for (PersistentVolume each : persistentVolumeList.getItems()) {
            for (Map<String, String> pvc : pvcList) {
                if (each.getMetadata().getName().equals(pvc.get("volumeName"))) {
                    pvList.add(each);
                }
            }
        }

        for (PersistentVolume each : pvList) {
            ObjectMeta metadata = each.getMetadata();
            HashMap<String, String> prop = new HashMap<>();
            Date date = parseUTC(metadata.getCreationTimestamp());
            prop.put("createTimestamp", date == null ? null : date.getTime() + "");
            prop.put("name", metadata.getName());
            PersistentVolumeSpec spec = each.getSpec();
            Quantity storage = spec.getCapacity().get("storage");
            prop.put("capacity", storage == null ? null : storage.getAmount());
            prop.put("accessModes", Arrays.toString(spec.getAccessModes().toArray()));
            prop.put("reclaimPolicy", spec.getPersistentVolumeReclaimPolicy());
            prop.put("namespace", spec.getClaimRef().getNamespace());
            prop.put("pvc", spec.getClaimRef().getName());
            prop.put("storageClassName", spec.getStorageClassName());
            prop.put("status", each.getStatus().getPhase());
            prop.put("volumeMode", spec.getVolumeMode());
            prop.put("kubeId", kubeId + "");
            pvProps.add(prop);
        }
        return pvProps;
    }

    public List<Map<String, String>> listPvc(Integer kubeId, String namespace, Map<String, String> labels) {
        // 所有集群
        if (kubeId == null) {
            List<Map<String, String>> pvcProps = new LinkedList<>();
            for (KubeConfig each : kubeConfigService.list(null)) {
                pvcProps.addAll(listPvcByKube(each.getId(), null, null));
            }
            return pvcProps;
        } else {
            // 指定集群
            return listPvcByKube(kubeId, namespace, labels);
        }
    }

    public List<Map<String, String>> listPvcByKube(Integer kubeId, String namespace, Map<String, String> labels) {
        List<Map<String, String>> pvcProps = new LinkedList<>();
        if (kubeId == null) {
            return pvcProps;
        }
        PersistentVolumeClaimList persistentVolumeClaimList;
        final List<String> pvcTemplateNamesByOpenGaussCluster;
        try {
            persistentVolumeClaimList = kubeClientService.get(kubeId).listPvc(namespace, labels);
            pvcTemplateNamesByOpenGaussCluster = getPvcTemplateNamesByOpenGaussCluster(kubeId);
        } catch (Exception e) {
            log.error("kubeId:{} listPvc error:{}", kubeId, e);
            return pvcProps;
        }
        // 所有的PVC
        // mongoDb的PVC
        List<PersistentVolumeClaim> mongoPvcList = new LinkedList<>();
        for (String each : pvcTemplateNamesByOpenGaussCluster) {
            for (PersistentVolumeClaim pvc : persistentVolumeClaimList.getItems()) {
                // todo pvcTemplatePrefix+PodName
                if (pvc.getMetadata().getName().contains(each)) {
                    mongoPvcList.add(pvc);
                }
            }
        }
        // 展示mongo的PVC属性
        for (PersistentVolumeClaim each : mongoPvcList) {
            HashMap<String, String> prop = new HashMap<>();
            ObjectMeta metadata = each.getMetadata();
            prop.put("name", metadata.getName());
            Date date = parseUTC(metadata.getCreationTimestamp());
            prop.put("createTimestamp", date == null ? null : date.getTime() + "");
            prop.put("namespace", metadata.getNamespace());
            PersistentVolumeClaimSpec spec = each.getSpec();
            prop.put("accessModes", Arrays.toString(spec.getAccessModes().toArray()));
            Quantity storage = spec.getResources().getRequests().get("storage");
            prop.put("resourceRequest", storage == null ? null : storage.getAmount());
            prop.put("storageClassName", spec.getStorageClassName());
            prop.put("volumeMode", spec.getVolumeMode());
            prop.put("volumeName", spec.getVolumeName());
            prop.put("status", each.getStatus().getPhase());
            prop.put("kubeId", kubeId + "");
            pvcProps.add(prop);
        }
        return pvcProps;
    }

    public List<String> getPvcTemplateNamesByOpenGaussCluster(Integer kubeId) {
        List<String> pvcNames = new LinkedList<>();
        KubernetesResourceList<OpenGaussCluster> ogList = kubeClientService.get(kubeId).getOpenGaussClusterClient().list();
        for (OpenGaussCluster each : ogList.getItems()) {
            pvcNames.add(each.getMetadata().getName());
        }
        return pvcNames;
    }

    /**
     * 获取podName   pod名称：clustername-ip  其中ip中的.替换为x
     *
     * @param clusterName
     * @param ip
     * @return
     */
    public String getPodName(String clusterName, String ip) {
        return clusterName + "-" + ip.replaceAll("\\.", "x");
    }

//    @Override
//    public boolean allocateIp(CloudApp app, OpenGaussCluster cr, String cniType) {
//        List<NodeDTO> nodes = networkService.chooseNode(app, app.getMembers(), true);
//        List<String> ips = networkService.allocateIp(app, cniType, app.getMembers(), "pod");
//        app.setIpList(JsonUtil.toJson(AppUtil.composeNodeAndIp(nodes, ips)));
//        return true;
//    }
//
//    @Override
//    public void reallocateIp(CloudApp app) {
//
//    }

    /**
     * 根据参数模板中的参数在config中增加参数
     * @param openGaussCluster
     * @param vo
     * @return
     */
    public OpenGaussCluster fixSpecConfigForCr(OpenGaussCluster openGaussCluster,  CloudAppVO vo) {
        //get config
        openGaussCluster.getSpec().setHostpathroot(vo.getHostpathRoot());
        Map<String, String> config = openGaussCluster.getSpec().getConfig();
        if(CollectionUtils.isEmpty(config)) {
            config = new HashMap<>();
        }
        List<MysqlParamRules> mysqlParamRules = null;
        if(vo.getDbParamTemplateId() != null) {
            //get dBTemplate or use default dBTemplate
            if(vo.getDbParamTemplateId() == 0) {
                mysqlParamRules = cloudDbParamTemplateService.getValidateMysqlParamTemplate("2.0", "OPENGAUSS", null);
            } else {
                MySQLParamTemplateDTO mysqlParamTemplate = cloudDbParamTemplateService.getMysqlParamTemplate(vo.getDbParamTemplateId());
                mysqlParamRules = mysqlParamTemplate.getMysqlParamRules();
            }
        }
        //get dbTemplateParam
        if(!CollectionUtils.isEmpty(mysqlParamRules)) {
            for (MysqlParamRules mysqlParamRule : mysqlParamRules) {
                String paramName = mysqlParamRule.getName();
                String paramValue = mysqlParamRule.getParavalue();

                if(!StringUtils.isEmpty(paramName) && !StringUtils.isEmpty(paramValue)) {
                    if(paramValue.contains(" ") || paramValue.contains("/") || paramValue.contains("*")) {
                        paramValue = "'" + paramValue + "'";
                    }
                    config.put(paramName, paramValue);
                }
            }
        }
        overWriteCnfParam(vo, config);
        return addAttr(openGaussCluster, config);
    }


    /**
     * 根据openGaussCluster中的属性值在config中添加参数
     * @param config
     * @param openGaussCluster
     */
    public OpenGaussCluster addAttr(OpenGaussCluster openGaussCluster, Map<String, String> config) {
/*
        // 1.根据memory值
        String memory = openGaussCluster.getSpec().getMemory();
        String memoryStr = memory.replaceAll("[a-zA-Z]","");
        Double shared_buffers = Double.parseDouble(memoryStr) * 0.3;
        Double max_process_memory  = Double.parseDouble(memoryStr) * 0.6;
        //shared_buffers = memory * 0.3
        //小数形式的Gi转换为Mi（*1024）
        if (memory.contains("Mi")) {
            config.put("shared_buffers", Math.round(shared_buffers)+"MB");
        } else {
            //四舍五入后的值有变化，说明是小数，需要转换为MB
            if(!shared_buffers.equals((double)Math.round(shared_buffers))) {
                shared_buffers = shared_buffers * 1024;
                config.put("shared_buffers", Math.round(shared_buffers)+"MB");
            } else {
                config.put("shared_buffers", Math.round(shared_buffers)+"GB");
            }
        }

        // max_process_memory = memory * 0.6, 最小2GB
        if (memory.contains("Mi")) {
            //换算为GB,是否大于2GB
            if(max_process_memory / 1024 < 2) {
                config.put("max_process_memory","2GB");
            } else {
                config.put("max_process_memory",Math.round(max_process_memory)+"MB");
            }
        } else {
            if(max_process_memory - 2 < 0) {
                config.put("max_process_memory","2GB");
            } else {
                if(!max_process_memory.equals((double)Math.round(max_process_memory))) {
                    max_process_memory = max_process_memory * 1024;
                    config.put("max_process_memory", Math.round(max_process_memory)+"MB");
                } else {
                    config.put("max_process_memory",Math.round(max_process_memory)+"GB");
                }
            }
        }*/

        // 2. 根据ipList和remoteiplist
        /*OpenGaussCluster.IpNodeEntry[] iplist = openGaussCluster.getSpec().getIplist();
        String[] remoteiplist = openGaussCluster.getSpec().getRemoteiplist();
        if(iplist.length == 1 && ArrayUtils.isEmpty(remoteiplist)) {
            config.put("synchronous_commit", "OFF");
        } else {
            config.put("synchronous_commit", "remote_receive");
        }*/

        openGaussCluster.getSpec().setConfig(config);
        return openGaussCluster;
    }

    @Override
    public void modifyConfigParam(Map<String, String> params, Integer appId, String componentKind) throws Exception {
        BiConsumer<OpenGaussCluster, Function<Map<String, String>, Map<String, String>>> consumer = (cr, func) -> {
            cr.getSpec().setConfig(func.apply(cr.getSpec().getConfig()));
        };
        operationHandler.handleModifyParam(appId, params, OpenGaussCluster.class, this, consumer);
    }

    @Override
    public String migrate(MigrateDTO migrateDTO) throws Exception {
        Integer appId = migrateDTO.getAppId();
        CloudApp app = appService.get(appId);
        KubeClient kubeClient = clientService.get(app.getKubeId());
        BiFunction<MigrateDTO.MigrateNode, CloudApp, OpenGaussCluster> modifer = (migrateNode, cloudApp) -> {
            OpenGaussCluster actCr = kubeClient.listCustomResource(OpenGaussCluster.class, app.getCrName(), app.getNamespace());
            List<Iplist> newIpList = actCr.getSpec().getIplist().stream().filter(ip -> !ip.getIp().equals(migrateNode.getOldNode().getIp()))
                    .collect(Collectors.toList());
            newIpList.add(new Iplist(){{
                setIp(migrateNode.getNewNode().getIp());
//                setNodename(migrateNode.getNewNode().getNode());
            }});
            OpenGaussCluster cr = YamlEngine.unmarshal(app.getCr(), OpenGaussCluster.class);
            cr.setSpec(actCr.getSpec());
            cr.getSpec().setIplist(newIpList);
            cr.getSpec().getSchedule().setNodeAffinity(convertCRNodeAffinity(new SelectorDTO[]{SelectorDTO.builder().key(LABEL_NODE_NAME)
                    .operator(IN).values(Collections.singletonList(migrateNode.getNewNode().getNode())).build()},
                    com.shindata.opengauss.v1.opengaussclusterspec.schedule.NodeAffinity.class));
            return cr;
        };

        // 已存在的pvc列表
        Set<String> curPvcList = kubeClient.listPvc(app.getNamespace(), Label.toMap(AppKind.OpenGauss.labelOfPod(app))).getItems()
                .stream().map(pvc -> pvc.getMetadata().getName()).collect(Collectors.toSet());
        Predicate<String> ipChecker = ip -> {
            String pvcName = String.format(OG_PVC_PATTERN,
                    app.getCrName(), ip.replace(".", "-"));
            return !curPvcList.contains(pvcName);
        };
        operationHandler.handleMigrate(migrateDTO, modifer, getProcessorClass(MIGRATE), ipChecker, getIpOwnerKind(), getIpReservationTarget(), OpenGaussCluster.class, kubeClient, null);

        return MIGRATE.getAppOperation();
    }

    @Override
    public void updatePassword(Password password, int appId) throws Exception {
        CloudApp app = appService.get(appId);
        //1.校验传入的密码是否相同并且校验长度和字符串类型
        CustPreconditions.checkState(!StringUtils.equalsIgnoreCase(password.getOldPassword(),password.getNewPassword()), "旧密码与新密码不能相同");
        //对密码进行解密
        String newDecrypt = decryptPassword(password.getNewPassword());
        String oldDecrypt = decryptPassword(password.getOldPassword());
        password.setNewPassword(newDecrypt);
        password.setOldPassword(oldDecrypt);
        int numCount=0,charCount=0,otherCount=0;
        String numRegular="^[0-9]$",CharRegular="^[a-zA-Z]$";
        Pattern pattern1 =Pattern.compile(numRegular);
        Pattern pattern2 =Pattern.compile(CharRegular);
        char temp;
        for (int i = 0; i < password.getNewPassword().length(); i++) {
            temp=password.getNewPassword().charAt(i);
            if(pattern1.matcher(""+temp).matches()){
                numCount++;
            }else if(pattern2.matcher(""+temp).matches()){
                charCount++;
            }else{
                otherCount++;
            }
        }
        CustPreconditions.checkState(!(charCount + numCount + otherCount < 8), "新密码长度最少8位");
        //CustPreconditions.checkState(!(charCount > 0 && numCount >0 && otherCount >0), "新密码最少需要三种类型字符");

        //2.根据appId查找到cloud_database_user中查找创建应用时添加的用户
//        CloudDatabaseUser dbUser = dbUserService.findDbUser(appId, CloudAppConstant.UserRole.ADMIN).get(0);
        CloudDatabaseUser dbUser = null;
        List<CloudDatabaseUser> dbUsers = dbUserService.findDbUser(appId, CloudAppConstant.UserRole.ADMIN);
        if(null == dbUsers || 0 == dbUsers.size()){
            throw new CustomException(600, "未查询到og用户！");
        }else{
            dbUser = dbUsers.get(0);
        }
        //3.进入pod执行修改密码的命令
        try {
            //使用omm用户登录，别的用户权限不够
            String createUserCmd = "gsql -d postgres -p 5432 -r -c ?"; // ?作为占位符 之后被替换
            String createUserSql = String.format("ALTER USER %s IDENTIFIED BY '%s'", dbUser.getUsername(), password.getNewPassword());
            String sql = String.join(";", new String[]{createUserSql});
            String[] cmd = createUserCmd.split(" ");
            cmd[cmd.length-1] = sql;
            KubeClient client = kubeClientService.get(app.getKubeId());
            PodDTO pod = client.listPod(app.getNamespace(), AppKind.OpenGauss.labelOfPod(app)).stream().filter(p -> CloudAppConstant.ROLE_PRIMARY.equals(p.getLabels().get("opengauss.role")))
                    .findFirst().orElseThrow(() -> new IllegalStateException("没有主库label, 因此修改用户密码未完成"));
            String result = client.execCmd(app.getNamespace(), pod.getPodName(), AppKind.OpenGauss.getContainerName(), cmd);

            //记录操作
            ResourceChangeHis his =appService.getResourceChangeHis(app,ActionEnum.UPDATE_PASSWORD, "",app.getCrRun(),"");
            his.setLastEndTimestamp(System.currentTimeMillis());
            his.setUpdateTime(new Timestamp(System.currentTimeMillis() + 2000));
            if (StringUtils.equals(result,"ALTER ROLE\n")){
                log.info("[Alter user password success --- {}", app.getCrName(), null);
                his.setMsg("alter password success");
                his.setStatus(StatusConstant.SUCCESS);
                app.setStatus(CloudAppConstant.AppStatus.SUCCESS);
            }else {
                log.info("[Alter user password failed --- {}", app.getCrName(), null);
                his.setMsg("alter password failed");
                his.setStatus(StatusConstant.FAIL);
                app.setStatus(CloudAppConstant.AppStatus.FAILED);
            }
            resourceChangeHisService.add(his);
            appService.update(app);
        } catch (Exception e) {
            throw new RuntimeException("Alter user password failed, will try later" , e);
        }
    }

    public void switchMaster(int appId, String podIp) throws Exception {
        //进入从节点执行switchover命令切换主从
        CloudApp app = appService.get(appId);

        KubeClient client = clientService.get(app.getKubeId());
        List<PodDTO> pods = client.listPod(app.getNamespace(), getKind().labelOfPod(app));
        PodDTO standbyPod = pods.stream().
                filter(i -> podIp.equals(i.getPodIp()) && CloudAppConstant.ROLE_STANDBY.equals(i.getLabels().get("opengauss.role"))).
                findAny().orElseThrow(() -> new CustomException(600, "此实例不是从节点"));

        String cmd = "gs_ctl switchover -D gaussdata/openGauss/db1/";
        Map<String, String> params = new HashMap<>();
        params.put("newMasterIp", podIp);
        params.put("newMasterPodName",standbyPod.getPodName());
        try {
            client.execCmdOneway(app.getNamespace(), standbyPod.getPodName(), getKind().getContainerName(), "sh", "-c", cmd);
            appService.callScheduler(app, null, params, ActionEnum.SWITCH_MASTER, OpenGaussSwitchOverWatch.class);
        } catch (Exception e) {
            throw new CustomException(600, "执行切换命令失败 " + e.getMessage());
        }
    }

    @Override
    public void start(int appId, @Nullable String instanceName) throws Exception {
        Function<AppInstanceVO, String> cmdResolver = i -> {
            String startCmd = "gs_ctl start -D gaussdata/openGauss/db1/ -M ##Role##";
            String cmd = "";
            if (i.getRole().equals("primary")){
                cmd = startCmd.replace("##Role##","primary");
            }else if (i.getRole().equals("secondary")){
                cmd = startCmd.replace("##Role##","standby");
            }
            return cmd;
        };
        operationHandler.handleStart(appId, instanceName, this, cmdResolver);
    }

    @Override
    public void stop(int appId, String instanceName) throws Exception {
        String stopCmd = "gs_ctl stop -D gaussdata/openGauss/db1/";
        Consumer<OpenGaussCluster> turnOnMaintain = cr -> {
            cr.getSpec().setMaintenance(true);
        };
        operationHandler.handleStop(appId, instanceName, this, stopCmd, turnOnMaintain, OpenGaussCluster.class, null);
    }

    @Async
    public void startStopExec(int appId, String instanceName, String startCmd, ActionEnum action, Class<?> watchClass)  {
        List<AppInstanceVO> instanceList = findInstanceList(appId, null, null);
        if (StringUtils.isNotEmpty(instanceName)) {
            instanceList = instanceList.stream().filter(i->i.getPodName().equals(instanceName)).collect(Collectors.toList());
        }
        if (instanceList.isEmpty()) {
            log.info("app-{} {} skipped", appId, action);
        }

        CloudApp app = appService.get(appId);
        KubeClient client = clientService.get(app.getKubeId());
        OpenGaussCluster actCr = client.listCustomResource(OpenGaussCluster.class, app.getCrName(), app.getNamespace());

        // 打开运维标记
        CustPreconditions.checkState(StringUtils.isNotEmpty(app.getCr()), "获取当前cr失败, 可能是操作未正确结束");
        //停止
        if (action == ActionEnum.STOP || action == ActionEnum.STOP_POD)
            updateMaintenanceField(app, client, true, instanceName, actCr);

        //开启
        if (action == ActionEnum.START){
            //启动时先启动db进程再更新维护标记，这里执行命令启动进程，调度中开启维护标记，执行命令时需要根据节点主从修改启动语句
            try {
                FutureService futureService = new FutureService();
                instanceList.parallelStream().forEach(i->{
                    String cmd = "";
                    if (i.getRole().equals("primary")){
                        cmd = startCmd.replace("##Role##","primary");
                    }else if (i.getRole().equals("secondary")){
                        cmd = startCmd.replace("##Role##","standby");
                    }
                    String finalCmd = cmd;
                    futureService.submit(()-> {
                        try {
                            client.execCmd(app.getNamespace(), i.getPodName(), AppKind.OpenGauss.getContainerName(), true, 10, "sh", "-c", finalCmd);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    });
                });
                actCr.getSpec().setMaintenance(false);
            } catch (Exception e) {
                throw new CustomException(600, "执行" + action.getAppOperation()+ "命令失败");
            }
        }

        if (action == ActionEnum.START_POD){
            //启动时先启动db进程再更新维护标记，这里执行命令启动进程，调度中开启维护标记，执行命令时需要根据节点主从修改启动语句
            try {
                String role = instanceList.stream().filter(instance -> instance.getPodName().equals(instanceName)).collect(Collectors.toList()).get(0).getRole();
                String cmd = "";
                if (role.equals("primary")){
                    cmd = startCmd.replace("##Role##","primary");
                }else if (role.equals("secondary")){
                    cmd = startCmd.replace("##Role##","standby");
                }
                FutureService futureService = new FutureService();
                String finalCmd = cmd;
                instanceList.parallelStream().forEach(i->{
                    futureService.submit(()-> {
                        try {
                            client.execCmd(app.getNamespace(), instanceName, AppKind.OpenGauss.getContainerName(), true, 10, "sh", "-c", finalCmd);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    });
                });
                actCr.getSpec().setMaintenance(false);
            } catch (Exception e) {
                throw new CustomException(600, "执行" + action.getAppOperation()+ "命令失败");
            }
        }

        //1.关闭先开启维护标记，在调度检测标记开启后再去关闭db进程
        //2.先启动db进程再去关闭维护标记
        try {
            Map map = new HashMap();
            map.put("instanceName",instanceName);
            map.put("startCmd",startCmd);
            map.put("actionEnum",action);
            OpenGaussCluster cr = YamlEngine.unmarshal(app.getCr(), OpenGaussCluster.class);
            cr.setSpec(actCr.getSpec());
            appService.callScheduler(app, YamlEngine.marshal(cr), map, action, watchClass);
        } catch (Exception e) {
            throw new CustomException(600, action.getAppOperation() + "失败"+ e.getCause().getMessage());
        }
    }


    public OpenGaussCluster updateMaintenanceField(CloudApp app, KubeClient kubeClient, boolean isOn, String instanceName, OpenGaussCluster cr) {
        // 关闭maintenance
        cr.getSpec().setMaintenance(isOn);
        kubeClient.updateCustomResource(cr, OpenGaussCluster.class);
        return cr;
    }

    @Override
    public CloudAppVO overrideSpec(CloudAppLogic logicApp, Integer kubeId, InstallAppVo<? extends OverrideSpec> vo) {
        return super.overrideSpec(logicApp, kubeId, vo);
    }

    @Override
    public List<ServiceManager> createService(
            String serviceType, CloudAppVO vo, List<?> serviceResources, CustomResource installCr) {
        //1.校验
        AppKind kind = getKind();
        if (serviceResources.isEmpty()) return Collections.emptyList();
        int serviceManagerNum = kind.getServiceManagerNum(serviceType, vo.getMembers());
        if (CollectionUtils.isEmpty(serviceResources) || serviceResources.size() != serviceManagerNum) {
            throw new CustomException(600, "节点端口类型必须指定 " + serviceManagerNum
                    + " 个端口, 实际数量为 " + serviceResources.size() + ", 类型为 " + serviceType);
        }

        //2 声明构建所需的变量
        List<ServiceManager> svms = new ArrayList<>();
        OpenGaussClusterSpec spec = ((OpenGaussCluster) installCr).getSpec();
        String writeServiceName = kind.getWriteServiceName(vo.getCrName(), null);
        String readServiceName = kind.getReadServiceName(vo.getCrName());
        int dbPort = kind.getDbPort();


        for (int i = 0; i < serviceResources.size(); i++) {
            Object serviceResource = serviceResources.get(i);
            //2. 通过 fabric8 构建 Service,再 set 到 ServiceManager 中

            //2.3 初始化 ServiceManager
            ServiceManager serviceManager = new ServiceManager();
            serviceManager.setServiceType(serviceType);
            String serviceName = null;

            //2.4 根据 ServiceType 填充 NodePort 和 lb
            if (CloudAppConstant.ServiceType.NODE_PORT.equals(serviceType)) {
                //nodeport 方式，serviceResources 结构为 List<Integer>，进行分配 NodePort，读写分离需要两个 NodePort
                Integer nodePort = (Integer) serviceResource;
                serviceManager.setPort(nodePort);
                // 读写分离
                if (spec.getWriteport() == null) {
                    // 先设置写 service
                    serviceManager.setPurpose(CloudAppConstant.ServicePurpose.WRITE);
                    spec.setWriteport(nodePort);
                    serviceName = writeServiceName;
                } else {
                    // 设置了写 service 后设置读 service
                    serviceManager.setPurpose(CloudAppConstant.ServicePurpose.READ);
                    spec.setReadport(nodePort);
                    serviceName = readServiceName;
                }
            } else if (CloudAppConstant.ServiceType.LOAD_BALANCER.equals(serviceType)) {
                //lb 方式，serviceResources 结构为 List<String>，进行分配 lbip，读写分离需要两个 lbip，端口为 dbport
                serviceManager.setPort(dbPort);
                String lbip = (String) serviceResource;
                serviceManager.setExternalIp(lbip);
                // 读写分离
                if (spec.getWriteExternalIP() == null) {
                    // 先设置写 service
                    serviceManager.setPurpose(CloudAppConstant.ServicePurpose.WRITE);
                    spec.setWriteExternalIP(lbip);
                    serviceName = writeServiceName;
                } else {
                    // 设置了写 service 后设置读 service
                    serviceManager.setPurpose(CloudAppConstant.ServicePurpose.READ);
                    spec.setReadExternalIP(lbip);
                    serviceName = readServiceName;
                }
            }

            serviceManager.setServiceName(serviceName);
            svms.add(serviceManager);
        }

        return svms;
    }

    @Override
    public void updateService(List<ServiceManager> svcMgrs, CloudApp app, Object oldServiceResource) throws Exception {
        ServiceManager serviceManager = svcMgrs.get(0);
        String serviceType = serviceManager.getServiceType();
        Map<String, String> data = new HashMap<>();
        data.put("oldServiceResource", oldServiceResource + "");
        Consumer<OpenGaussCluster> modifier = cr -> {
            OpenGaussClusterSpec spec = cr.getSpec();
            if (serviceType.equalsIgnoreCase(CloudAppConstant.ServiceType.NODE_PORT)) {
                if (spec.getWriteport().equals(oldServiceResource)) {
                    spec.setWriteport(serviceManager.getPort());
                } else if (spec.getReadport().equals(oldServiceResource)) {
                    spec.setReadport(serviceManager.getPort());
                }
            } else if (serviceType.equalsIgnoreCase(CloudAppConstant.ServiceType.LOAD_BALANCER)) {
                if (spec.getWriteExternalIP().equals(oldServiceResource)) {
                    spec.setWriteExternalIP(serviceManager.getExternalIp());
                } else if (spec.getReadExternalIP().equals(oldServiceResource)) {
                    spec.setReadExternalIP(serviceManager.getExternalIp());
                }
            }
        };

        operationHandler.handleService(
                app, svcMgrs, modifier, OpenGaussCluster.class, this, data, ActionEnum.UPDATE_SERVICE);
    }

    /**
     * 查询og的用户信息
     *
     * @param logicid
     * @return
     */
    public List<Map<String, String>> getUserInfoForOG(Integer logicid) {
        //查询应用信息
        CloudApp app = appService.getCloudAppByLogicId(logicid);
        String selectUserSql = "\"SELECT usename, usesysid, usecreatedb, usesuper, usecatupd, userepl, valbegin ,valuntil, useconfig, respool, parent, nodegroup FROM pg_user;\"";
        String s = execCmdOGSql(app, selectUserSql, "");
        return parseResultToMap(s).stream().filter(map -> null != map.get("usename") && !OG_USER_NAME.equalsIgnoreCase(map.get("usename")) && !OG_OMM_USER_NAME.equalsIgnoreCase(map.get("usename"))).collect(Collectors.toList());
    }

    /**
     * 查询用户权限信息
     *
     * @param logicid
     * @param username
     * @return
     */
    public List<Map<String, String>> getUserPriAuthForOG(Integer logicid, String username) {
        //查询应用信息
        CloudApp app = appService.getCloudAppByLogicId(logicid);
        String selectUserSql = "\"select * from information_schema.table_privileges where grantee=\'%s\';\"";
        selectUserSql = String.format(selectUserSql, username);
        String s = execCmdOGSql(app, selectUserSql, "");
        return parseResultToMap(s);
    }

    /**
     * 物理库tab中显示左侧导航栏中某個物理庫中的schema列表
     * @param logicid
     * @param pdbname
     * @param sysFlag
     * @return
     */
    public List<String> getPdbSideBarSchemaList(Integer logicid, String pdbname, String sysFlag) {
        //查询应用信息
        CloudApp app = appService.getCloudAppByLogicId(logicid);
        String selectUserSql = "true".equalsIgnoreCase(sysFlag) ? "\"/*NDTM*/ select nspname from pg_namespace order by nspname asc ;\""
                : "\"/*NDTM*/ select nspname from pg_namespace  where nspname not in ('cstore','dbe_perf','snapshot','pg_catalog','information_schema','pg_toast')  order by nspname asc;\"";
        String s = execCmdOGSql(app, selectUserSql, pdbname);
        try {
            List<Map<String, String>> resultList = parseResultToMap(s);
            return CollectionUtils.isEmpty(resultList) ? new ArrayList<>() : resultList.stream().map(tempMap -> {return tempMap.get("nspname").toString();}).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询权限信息失败：" + e.getMessage());
            throw new CustomException(600, "查询权限信息失败");
        }
    }

    /**
     * 查询对象的权限信息
     *
     * @param logicid
     * @param pdbname
     * @param schemaName
     * @param objectType
     * @param offset
     * @param limit
     * @return
     */
    public Map<String, Object> getDataObjectListForSchema(Integer logicid, String pdbname, String schemaName, String objectType, Integer offset, Integer limit) {
        //查询应用信息
        CloudApp app = appService.getCloudAppByLogicId(logicid);
        Map<String, Object> resultMap = new HashMap<>();
        String totalSql = getOGAutuSqlTotalSql(schemaName, objectType);

        //查询总数
        String totalCount = execCmdOGSql(app, totalSql, pdbname);
        Map<String, String> totalMap = parseResultToMap(totalCount).get(0);
        resultMap.putAll(totalMap);

        if ("0".equals(totalMap.get("total")))
            return resultMap;

        //查询具体数据
        String ogAuthDataSql = getOGAuthDataSql(schemaName, offset, limit, objectType);
        String authDataSql = execCmdOGSql(app, ogAuthDataSql, pdbname);
        List<Map<String, String>> result = parseResultToMap(authDataSql);
        resultMap.put("data", result);

        return resultMap;
    }

    /**
     * 进行 权限的 添加 与 回收
     *
     * @return
     */
    public void updateOGUserAuth(List<DataBaseAuthVO> dataBaseAuthVOS) {
        //查询应用信息
        CloudApp app = appService.getCloudAppByLogicId(dataBaseAuthVOS.get(0).getLogicid());
        Boolean isAddAuth = dataBaseAuthVOS.get(0).getIsAddAuth();
        ResourceChangeHis resourceChangeHis = appService.getResourceChangeHis(app, isAddAuth ? ActionEnum.CREATE_APP_AUTH : ActionEnum.DELETE_APP_AUTH, StatusConstant.RUNNING, "", null);
        resourceChangeHisService.add(resourceChangeHis);

        String resultSql = isAddAuth ? "grant %s on %s to %s " : "revoke %s on %s from  %s ";
        String execSql = dataBaseAuthVOS.stream().map(dataBaseAuthVO -> {
            String tempSql = dataBaseAuthVO.getIsAdmin() && dataBaseAuthVO.getIsAddAuth() ? resultSql + " with grant option" : resultSql;
            return String.format(tempSql, dataBaseAuthVO.getAuthStr(), dataBaseAuthVO.getSchemaTable(), dataBaseAuthVO.getUsername());
        }).collect(Collectors.joining(";"));
        try {
            execCmdOGSql(app, "\"" + execSql + ";\"", "");
            updateResourceChangeHis(resourceChangeHis, StatusConstant.SUCCESS, (isAddAuth ? ActionEnum.CREATE_APP_AUTH.getAppOperation() : ActionEnum.DELETE_APP_AUTH.getAppOperation()) + "成功");

        } catch (Exception e) {
            log.error("更新权限失败" + e.getMessage());
            updateResourceChangeHis(resourceChangeHis, StatusConstant.FAIL, (isAddAuth ? ActionEnum.CREATE_APP_AUTH.getAppOperation() : ActionEnum.DELETE_APP_AUTH.getAppOperation()) + "失败:" + e.getMessage());
            throw new CustomException(600, "更新权限失败");
        }
    }

    /**
     * 进行 og 用户 的删除、锁定、解锁操作
     *
     */
    public void operationOGUser(DatabaseUserOperatorVO databaseUserOperatorVO) {
        //查询应用信息
        CloudApp app = appService.getCloudAppByLogicId(databaseUserOperatorVO.getLogicid());
        String operationType = databaseUserOperatorVO.getOperationType();
        String password = databaseUserOperatorVO.getPassword();
        ActionEnum operationTypeEnum = ActionEnum.actionTypeOf(operationType);
        ResourceChangeHis resourceChangeHis = appService.getResourceChangeHis(app, operationTypeEnum, StatusConstant.RUNNING, "", null);
        resourceChangeHisService.add(resourceChangeHis);
        String initSql = "";
        switch (operationTypeEnum) {
            case DELETE_APP_USER:
                initSql = "\"DROP USER  %s  CASCADE;\"";
                break;
            case LOCK_APP_USER:
                initSql = "\"ALTER USER  %s  ACCOUNT LOCK;\"";
                break;
            case UNLOCK_APP_USER:
                initSql = "\"ALTER USER  %s  ACCOUNT UNLOCK;\"";
                break;
            case CREATE_APP_USER:
                if (StringUtils.isEmpty(password)) {
                    updateResourceChangeHis(resourceChangeHis, StatusConstant.FAIL, (operationTypeEnum + "失败:" + "未填写新增用户的密码"));
                    throw new CustomException(600, operationTypeEnum + "失败:" + "未填写新增用户的密码");
                }
                initSql = "\"create user  %s  with password \'%s\';\"";
                break;
            default:
                throw new CustomException(600, "Unexpected operation type : " + operationType);
        }
        initSql = String.format(initSql, databaseUserOperatorVO.getUsername(), decryptPassword(password));

        try {
            execCmdOGSql(app, initSql, "");
            if (CREATE_APP_USER.equals(operationTypeEnum) && !CollectionUtils.isEmpty(databaseUserOperatorVO.getDataBaseAuthVOS())) {
                //做修改权限操作
                updateOGUserAuth(databaseUserOperatorVO.getDataBaseAuthVOS());
            }
            updateResourceChangeHis(resourceChangeHis, StatusConstant.SUCCESS, (operationTypeEnum + "成功"));
        } catch (Exception e) {
            log.error("更新用户信息失败" + e.getMessage());
            updateResourceChangeHis(resourceChangeHis, StatusConstant.FAIL, (operationTypeEnum + "失败:" + e.getMessage()));
            throw new CustomException(600, "更新权限失败");
        }
    }

    public List<Map<String, Object>> getPdbGlobalVariablesInfo(Integer logicid) {
        //查询应用信息
        CloudApp app = appService.getCloudAppByLogicId(logicid);
        List<Map<String,Object>> resultList;
        //设置主要信息
        MetaVO metaVO = getMetaVOByApp(app, null);
        String queryGlobalSql = "/*NDTM*/SELECT name as \"Variable_name\",setting as \"Value\",unit,context,category,short_desc,extra_desc,vartype,source,min_val,max_val, cast(enumvals as varchar) ,boot_val,reset_val,sourcefile , sourceline FROM pg_catalog.pg_settings;";
        try {
            resultList = query(metaVO, queryGlobalSql, "", Boolean.TRUE);
            resultList.parallelStream().forEach(tempMap -> {tempMap.put("isupdate", Boolean.TRUE);});
            String prohibitContent = sysConfigService.findOne(PARAM_PROHIBIT, getKind().getProduct());
            if (org.apache.commons.lang.StringUtils.isEmpty(prohibitContent)) {
                return resultList;
            }
            Set<String> prohibited = new HashSet<>(Arrays.asList(prohibitContent.split(",")));
            //判断是否可修改
            return resultList.parallelStream().map(tempMap -> {
                String variable_name = tempMap.get("Variable_name").toString();
                if (prohibited.contains(variable_name))
                    tempMap.put("isupdate", Boolean.FALSE);
                return tempMap;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            throw new CustomException(600, "查询og全局变量失败" + e);
        }
    }

    /**
     * 拼凑 metavo信息
     *
     * @return
     */
    @Override
    public MetaVO getMetaVOByApp(CloudApp app, String dbName) {
        MetaVO metaVO = super.getMetaVOByApp(app, dbName);
        metaVO.setDbproduct(CloudAppConstant.DbProductType.OPENGAUSS);
        metaVO.setDbversion(app.getVersion());
        metaVO.setUsername(OG_USER_NAME);

        SymmetricEncryptionUtil ss = SymmetricEncryptionUtil.getEncryptInstance();
        String passWord = ss.encrypt(OG_PASS_WORD);
        metaVO.setPassword(passWord);
        metaVO.setDbname(org.apache.commons.lang.StringUtils.isEmpty(dbName) ? "postgres" : dbName);
        return metaVO;
    }


    private String getOGAutuSqlTotalSql(String schemaName, String objectType) {
        String result = "";
        if ("table".equalsIgnoreCase(objectType)) {
            result = "\"  SELECT COUNT(*) TOTAL FROM (/*NDTM*/  SELECT c.relname AS tablename, ( CASE c.reltablespace WHEN 0 THEN d.dataspace ELSE t.spcname END) AS TABLESPACE, 'BASE TABLE' AS tabletype, c.reltuples AS tablerows, ROUND(c.relpages*8/1024/1024,2) AS tablepages,pg_total_relation_size(c.oid)   AS tablesize, c.relnatts AS relnatts, c.parttype AS parttype, c.relcmprs AS relcmprs, o.ctime AS createtime, o.mtime AS changetime, obj_description(c.relfilenode) AS tablecomment FROM pg_namespace n JOIN pg_class c ON n.oid = c.relnamespace AND c.relkind = 'r' AND n.nspname = \'%s\' LEFT JOIN PG_TABLESPACE t ON t.oid = c.reltablespace LEFT JOIN PG_OBJECT o ON c.oid = o.object_oid JOIN ( SELECT tablespace_oid_name(dattablespace)::name AS dataspace FROM pg_database WHERE datname = current_database()) d ON 1 = 1 ORDER BY relpages DESC  ) T ; \"";
        } else if ("view".equalsIgnoreCase(objectType)) {
            result = "\" SELECT COUNT(*) TOTAL FROM (/*NDTM*/   select v.schemaname as schemaname, v.viewname as viewname, v.viewowner as viewowner, s.is_updatable as is_updatable, s.is_insertable_into as is_insertable_into, o.ctime as ctime, o.mtime as mtime, v.definition as definition from PG_CLASS c join pg_namespace n ON n.oid = c.relnamespace join PG_VIEWS v on v.viewname = c.relname and n.nspname = \'%s\' and c.relkind='v' and v.schemaname = n.nspname left join PG_OBJECT o on c.oid = o.object_oid join information_schema.views s on s.table_schema=n.nspname and s.table_name=v.viewname   ) T ; \"";
        } else {
            throw new CustomException(600, "暂时不支持当前类型");
        }
        return String.format(result, schemaName);
    }

    private String getOGAuthDataSql(String schemaName, Integer offset, Integer limit, String objectType) {
        String result = "";
        if ("table".equalsIgnoreCase(objectType)) {
            result = "\" SELECT *  FROM ( /*NDTM*/  SELECT c.relname AS tablename, ( CASE c.reltablespace WHEN 0 THEN d.dataspace ELSE t.spcname END) AS TABLESPACE, 'BASE TABLE' AS tabletype, c.reltuples AS tablerows, ROUND(c.relpages*8/1024/1024,2) AS tablepages,pg_total_relation_size(c.oid)  AS tablesize, c.relnatts AS relnatts, c.parttype AS parttype, c.relcmprs AS relcmprs, o.ctime AS createtime, o.mtime AS changetime, obj_description(c.relfilenode) AS tablecomment FROM pg_namespace n JOIN pg_class c ON n.oid = c.relnamespace AND c.relkind = 'r' AND n.nspname = \'%s\' LEFT JOIN PG_TABLESPACE t ON t.oid = c.reltablespace LEFT JOIN PG_OBJECT o ON c.oid = o.object_oid JOIN ( SELECT tablespace_oid_name(dattablespace)::name AS dataspace FROM pg_database WHERE datname = current_database()) d ON 1 = 1 ORDER BY relpages DESC  ) AS metadatatable LIMIT %s OFFSET %s; \"";
        } else if ("view".equalsIgnoreCase(objectType)) {
            result = "\" SELECT *  FROM ( /*NDTM*/   select v.schemaname as schemaname, v.viewname as viewname, v.viewowner as viewowner, s.is_updatable as is_updatable, s.is_insertable_into as is_insertable_into, o.ctime as ctime, o.mtime as mtime, v.definition as definition from PG_CLASS c join pg_namespace n ON n.oid = c.relnamespace join PG_VIEWS v on v.viewname = c.relname and n.nspname = \'%s\' and c.relkind='v' and v.schemaname = n.nspname left join PG_OBJECT o on c.oid = o.object_oid join information_schema.views s on s.table_schema=n.nspname and s.table_name=v.viewname ) AS metadatatable LIMIT %s OFFSET %s \"";
        } else {
            throw new CustomException(600, "暂时不支持当前类型");
        }
        return String.format(result, schemaName, null == limit ? 99999999 : limit, null == offset ? 0 : offset);
    }

    /**
     * 执行 og 的一些查询sql
     *
     * @param selectUserSql
     * @param pdbname
     * @return
     */
    private String execCmdOGSql(CloudApp app, String selectUserSql, String pdbname) {

        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        //查询用户名和密码信息
        pdbname = StringUtils.isEmpty(pdbname) ? "postgres" : pdbname;
        //登录og
        String loginSql = "gsql -d %s -p 5432 -U %s  -W %s";

        String sql = String.format(loginSql, pdbname, OG_USER_NAME, OG_PASS_WORD) + " -c " + selectUserSql;

        //执行查询用户sql
        PodDTO pod = kubeClient.listPod(app.getNamespace(), AppKind.OpenGauss.labelOfPod(app)).stream().filter(p -> CloudAppConstant.ROLE_PRIMARY.equals(p.getLabels().get("opengauss.role")))
                .findFirst().orElseThrow(() -> new IllegalStateException("没有主库label, 因此无法查询相关的og用户信息"));

        String s = "";
        try {
            s = kubeClient.execCmd(app.getNamespace(), pod.getPodName(), AppKind.OpenGauss.getContainerName(), "sh", "-c", sql);

        } catch (Exception e) {
            log.error("执行查询og用户命令失败：" + e);
            throw new CustomException(600, "查询og用户信息失败");
        }
        return s;
    }


    /**
     * 将og查询到的数据解析成map
     * @param s
     * @return
     */
    private List<Map<String, String>> parseResultToMap(String s) {
        if (StringUtils.isEmpty(s))
            return new ArrayList<>();
        BufferedReader reader = new BufferedReader(new StringReader(s));

        List<Map<String, String>> resultList = new ArrayList<>();
        try {
            //读取首行数据作为 map的key
            String[] mapKey = reader.readLine().split("\\|");

            reader.readLine();
            String line;
            // 逐行读取字符串并处理
            while ((line = reader.readLine()) != null) {
                // 在这里进行处理，例如打印每一行
                if (line.contains("rows)"))
                    break;

                String[] lineData = line.split("\\|");
                Map<String, String> tempMap = buildResultData(mapKey, lineData);
                resultList.add(tempMap);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultList;
    }

    /**
     * 构建一个返回数据
     *
     * @return
     */
    private Map<String, String> buildResultData(String[] mapKey, String[] lineData) {
        Map<String, String> result = new HashMap<>();
        for (int i = 0; i < mapKey.length; i++) {
            result.put(mapKey[i].trim(), lineData[i].trim());
        }
        return result;
    }

    @Override
    public void restore(BackupHis backupHis, Integer appId, String restoreTime, String ftpFilename, String backupType) {
        // 0.对恢复的底层库类型做校验
        Integer backupAppId = backupHis.getAppId();
        CloudApp goalApp = appService.get(appId);
        CloudApp backupApp = appService.get(backupAppId);
        KubeClient kubeClient = kubeClientService.get(goalApp.getKubeId());
        OpenGaussCluster cr = kubeClient.listCustomResource(OpenGaussCluster.class, goalApp.getCrName(), goalApp.getNamespace());
        String backupHisMessage = backupHis.getMessage();
        Map backupHisMessageObj = JsonUtil.toObject(Map.class, backupHisMessage);
        String compatibility = (String) backupHisMessageObj.get("compatibility");
        // 1.恢复历史插入
        RestoreHis restoreHis = new RestoreHis();
        restoreHis.setStatus(StatusConstant.RUNNING);
        Timestamp startTime = new Timestamp(System.currentTimeMillis());
        restoreHis.setStartTime(startTime);

        restoreHis.setAppId(appId);
        restoreHis.setAppName(goalApp.getName());
        restoreHis.setAppType(goalApp.getKind());

        //应用所属集群
        KubeConfig byId = kubeConfigService.get(goalApp.getKubeId());
        restoreHis.setKubeName(byId.getName());
        restoreHis.setMessage("恢复中...");
        restoreHis.setFileDeleted(false);
        String backDirInPod = "/backup/";
        restoreHis.setRestoreDir(backDirInPod);
        restoreHis.setFileName(backupHis.getFileName());

        // 2.插入操作记录
        ResourceChangeHis resourceChangeHis = new ResourceChangeHis() {{
            setInsertTime(startTime);
            setKind(goalApp.getKind());
            setKubeId(goalApp.getKubeId());
            setNamespace(goalApp.getNamespace());
            setCommand("恢复");
            setStatus("2");
            setAction(ActionEnum.RESTORE.getActionType());
            setMsg("恢复中...");
            setAppId(goalApp.getId());
            setAppName(goalApp.getName());
            setUserName(UserUtil.getAsyncUserinfo().getUsername());
            setUserIp(CloudRequestContext.getContext().getUserIp());
            setKubeName(byId.getName());
            setYaml(goalApp.getCr());
            setLastEndTimestamp(System.currentTimeMillis());
            setAppLogicId(goalApp.getLogicAppId());
        }};
        Integer changeId = backupUtil.insertResourceChangeHis(resourceChangeHis);
        try {
            // 3.找到主节点
            KubeClient client = kubeClientService.get(goalApp.getKubeId());
            PodDTO primaryPod = backupUtil.getBackupPodOpengauss(goalApp, client);
            restoreHis.setPodName(primaryPod.getPodName());
            //插入基本信息
            backupService.commitRestoreHis(restoreHis);
            goalApp.setStatus(CloudAppConstant.AppStatus.PENDING);
            appService.update(goalApp);

            // 4.获取备份文件名称,修改对应的cr属性：RestoreFile
            //备份文件名称
            String backupFileName = backupHis.getFileName();
            //构造cr
            OpenGaussClusterSpec spec = cr.getSpec();
            String fullSourceName = backupApp.getNamespace() + "/" + backupApp.getCrName() + "/" + backupFileName;
            com.shindata.opengauss.v1.opengaussclusterspec.Restore restore = new com.shindata.opengauss.v1.opengaussclusterspec.Restore();
            restore.setFullSource(fullSourceName);
            //获取备份存储信息
            CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
            HashMap<String, String> remote = new HashMap<>();
            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                remote.put("type", CloudAppConstant.OperatorStorageType.NFS);
                remote.put("address", cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath());
            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                remote.put("type", CloudAppConstant.StorageType.S3);
                remote.put("address", cloudBackupStorageVO.getServer());
                remote.put("bucket", cloudBackupStorageVO.getBucket());
                remote.put("region", cloudBackupStorageVO.getRegion());
                //获取operator的namespace，因为所有operator都相同，所以统一获取mysql的operatornamespace
                String operatorConfig = sysConfigService.findOne("operator.name", "MySQL");
                String operatorNamespace = operatorConfig.split("/")[0];
                remote.put("secret", operatorNamespace + ":backupstorage-secret");
            } else {
                throw new CustomException(600, "恢复失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
            }
            restore.setRemote(remote);
            spec.setRestore(restore);
            cr.setSpec(spec);
            kubeClient.updateCustomResource(cr, OpenGaussCluster.class);
            //创建定时轮询备份结果
            Map map = new HashMap();
            map.put("restoreHisId", restoreHis.getRestoreHisId());
            map.put("resourceChangeId", changeId);
            appService.callScheduler(goalApp, YamlEngine.marshal(cr), map, ActionEnum.RESTORE, OpenGaussBackupAndRestoreWatch.class, resourceChangeHis);
        } catch (Exception e) {
            backupUtil.restoreReturn(restoreHis, changeId, e.getMessage(), StatusConstant.FAIL);
        }
    }

}
