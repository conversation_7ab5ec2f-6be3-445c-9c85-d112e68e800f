package com.shindata.common.spec;

@com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)
@com.fasterxml.jackson.annotation.JsonPropertyOrder({"backupImage","config","customConfigMap","exporterImage","image","masters","mountImage","name","podTemplate","replicas","services","volumeTemplates"})
@com.fasterxml.jackson.databind.annotation.JsonDeserialize(using = com.fasterxml.jackson.databind.JsonDeserializer.None.class)
public class Entries implements io.fabric8.kubernetes.api.model.KubernetesResource {
    @com.fasterxml.jackson.annotation.JsonProperty("backupImage")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String backupImage;

    public String getBackupImage() {
        return backupImage;
    }

    public void setBackupImage(String backupImage) {
        this.backupImage = backupImage;
    }
    @com.fasterxml.jackson.annotation.JsonProperty("config")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.Map<java.lang.String, String> config;

    public java.util.Map<java.lang.String, String> getConfig() {
        return config;
    }

    public void setConfig(java.util.Map<java.lang.String, String> config) {
        this.config = config;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("customConfigMap")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String customConfigMap;

    public String getCustomConfigMap() {
        return customConfigMap;
    }

    public void setCustomConfigMap(String customConfigMap) {
        this.customConfigMap = customConfigMap;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("exporterImage")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String exporterImage;

    public String getExporterImage() {
        return exporterImage;
    }

    public void setExporterImage(String exporterImage) {
        this.exporterImage = exporterImage;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("image")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String image;

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("masters")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Integer masters;

    public Integer getMasters() {
        return masters;
    }

    public void setMasters(Integer masters) {
        this.masters = masters;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("mountImage")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String mountImage;

    public String getMountImage() {
        return mountImage;
    }

    public void setMountImage(String mountImage) {
        this.mountImage = mountImage;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("name")
    @io.fabric8.generator.annotation.Required()
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * PodTemplateSpec describes the data a pod should have when created from a template
     */
    @com.fasterxml.jackson.annotation.JsonProperty("podTemplate")
    @com.fasterxml.jackson.annotation.JsonPropertyDescription("PodTemplateSpec describes the data a pod should have when created from a template")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private io.fabric8.kubernetes.api.model.Pod podTemplate;

    public io.fabric8.kubernetes.api.model.Pod getPodTemplate() {
        return podTemplate;
    }

    public void setPodTemplate(io.fabric8.kubernetes.api.model.Pod podTemplate) {
        this.podTemplate = podTemplate;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("replicas")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private Integer replicas;

    public Integer getReplicas() {
        return replicas;
    }

    public void setReplicas(Integer replicas) {
        this.replicas = replicas;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("services")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.List<com.shindata.common.spec.entries.Services> services;

    public java.util.List<com.shindata.common.spec.entries.Services> getServices() {
        return services;
    }

    public void setServices(java.util.List<com.shindata.common.spec.entries.Services> services) {
        this.services = services;
    }

    @com.fasterxml.jackson.annotation.JsonProperty("volumeTemplates")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.List<io.fabric8.kubernetes.api.model.PersistentVolumeClaim> volumeTemplates;

    public java.util.List<io.fabric8.kubernetes.api.model.PersistentVolumeClaim> getVolumeTemplates() {
        return volumeTemplates;
    }

    public void setVolumeTemplates(java.util.List<io.fabric8.kubernetes.api.model.PersistentVolumeClaim> volumeTemplates) {
        this.volumeTemplates = volumeTemplates;
    }
}

