package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.config.AppOpsConfig;
import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.StatusConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.ResourceChangeHis;
import cn.newdt.cloud.domain.RestoreHis;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.alert.AlertConfigService;
import cn.newdt.cloud.service.impl.*;
import cn.newdt.cloud.utils.BeanUtil;
import cn.newdt.cloud.utils.OperationUtil;
import cn.newdt.cloud.vo.CloudAppVO;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import io.fabric8.kubernetes.api.model.PodSpec;
import io.fabric8.kubernetes.client.CustomResource;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.math.raw.Mod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.InvocationTargetException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.ActionEnum.*;
import static cn.newdt.cloud.constant.ActionEnum.RESTORE;
import static cn.newdt.cloud.constant.ScheduleConstant.JOB_DATA_KEY_CHANGE_ID;

public abstract class OpsProcessorContext implements BackupCreateAppWatch {
    public static final Set<ActionEnum> ACTION_TO_PROPAGATE = Sets.newHashSet(UPDATE, UPGRADE, MODIFY_PARAM, UPDATE_ALERT_CONFIG, UPDATE_PASSWORD);
    public static final Set<ActionEnum> ACTION_NOT_PROPAGATE = Sets.newHashSet(SCALE_OUT, SCALE_IN);
    public static final Set<ActionEnum> ACTION_BELONGS = Sets.union(ACTION_TO_PROPAGATE, ACTION_NOT_PROPAGATE);
    private static final Logger log = LoggerFactory.getLogger(OpsProcessorContext.class);
    String RESOURCE_NOT_FOUND_MSG = "%s [%s] not found";

    static ThreadLocal<Boolean> flag = ThreadLocal.withInitial(() -> Boolean.FALSE);
    @Autowired
    protected CloudAppService appService;
    @Autowired
    MongoDbClusterService mongoDbClusterService;
    @Autowired
    protected KubeClientService kubeClientService;
    @Autowired
    protected NetworkService networkService;
    @Autowired
    protected MongoDbService mongoDbService;
    @Autowired
    protected KibanaService kibanaService;
    @Autowired
    RedisService redisService;
    @Autowired
    RedisClusterService redisClusterService;
    @Autowired
    SentinelService sentinelService;
    @Autowired
    protected ResourceChangeHisService resourceChangeHisService;
    @Autowired
    PodService podService;
    @Autowired
    protected CloudDatabaseUserService dbUserService;
    @Autowired
    AppOpsConfig opsConfig;
    @Autowired
    protected OperationUtil operationUtil;
    @Autowired
    protected AppMultiAZService appMultiAZService;
    @Autowired
    protected CloudAppLogicService appLogicService;
    @Value("${autoManagement:true}")
    protected boolean autoManagement;
    @Autowired
    protected ResourceManagerService resourceManagerService;
    @Autowired
    protected KubeSchedulerService kubeSchedulerService;
    @Autowired
    protected AlertConfigService alertConfigService;
    @Autowired
    protected ServiceManageOperationWatcherHelper serviceManageOperationWatcherHelper;
    @Autowired
    RestoreServiceImpl restoreService;
    @Autowired
    BackupService backupService;

    /**
     * 检查是否超时 todo 放在kubeJob统一处理
     *
     * @return true not timeout or false timeout
     */
    public void checkTimeout(TriggerHis triggerHis, OpsResultDTO.Builder watchResult) {
        checkTimeout(triggerHis, watchResult, opsConfig.isCheck_timeout());
    }

    public void checkTimeoutForce(TriggerHis triggerHis, OpsResultDTO.Builder watchResult) {
        checkTimeout(triggerHis, watchResult, true);
    }

    private void checkTimeout(TriggerHis triggerHis, OpsResultDTO.Builder watchResult, boolean force) {
        if (!watchResult.isSuccessful() && force) {
            LocalDateTime start = triggerHis.getStartTime().toLocalDateTime();
            long minutes = start.until(LocalDateTime.now(), ChronoUnit.MINUTES);
            if (minutes > opsConfig.getTimeout()) {
                watchResult.msg("\n" + "process time out after " + opsConfig.getTimeout() + "min since "
                        + start.format(DateTimeFormatter.ISO_DATE_TIME)).stopJob(true).status(StatusConstant.FAIL);
                log.debug(String.format("[RedisClusterWatch]kubeid:%s,appid:%s,timeout",
                        triggerHis.getJobDataMap().get("kubeId"), triggerHis.getJobDataMap().get("appId")));
            }
        } else {

        }
    }

    public void logInfo(CloudApp app, ActionEnum action, String msg) {
        log.info("[{}] app: {}/{} in k8s-{}, {}", action.getActionType(), app.getNamespace(), app.getCrName(), app.getKubeId(), msg);
    }

    public ActionEnum getAction(TriggerHis triggerHis) {
        ResourceChangeHis his = resourceChangeHisService.get(Integer.parseInt(triggerHis.getJobDataMap().get(JOB_DATA_KEY_CHANGE_ID)));
        return ActionEnum.actionTypeOf(his.getAction());
    }

    protected boolean matchPodSpec(PodSpec spec, PodSpec expSpec, String image) {
        throw new UnsupportedOperationException();
    }

    /**
     * check if operator has actually handled the recent update
     * operator and cloud-service 所在机器时间需同步
     * 如果cr没有更新会被判断为outdate
     */
    protected boolean isStatusOutDated(TriggerHis triggerHis, String lastupdatetime) {
        if (flag.get() != null && flag.get()) {
            flag.remove();
            return false;
        }
        if (StringUtils.isNotEmpty(lastupdatetime))
            try {
                ResourceChangeHis resourceHis = resourceChangeHisService.get(Integer.parseInt(triggerHis.getJobDataMap().get(JOB_DATA_KEY_CHANGE_ID)));
                long lastUpdateTimeStamp;
                lastUpdateTimeStamp = LocalDateTime.parse(lastupdatetime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).atZone(ZoneId.systemDefault())
                        .toInstant().toEpochMilli();
                if (resourceHis.getLastEndTimestamp() >= lastUpdateTimeStamp) {
                    flag.remove();
                    return true;
                }
            } catch (Exception e) {
                log.error("Check isStatusOutDated error", e);
            }

        flag.remove();
        return false; // status.lastupdatetime > 提交时的timestamp 表示operator作了处理
    }

    protected boolean isStatusOutDated(TriggerHis triggerHis, long lastupdatetime) {
        try {
            ResourceChangeHis resourceHis = resourceChangeHisService.get(Integer.parseInt(triggerHis.getJobDataMap().get(JOB_DATA_KEY_CHANGE_ID)));
            if (resourceHis.getLastEndTimestamp() - lastupdatetime > 10) {
                return true;
            }
        } catch (Exception e) {
            log.error("Check isStatusOutDated error", e);
        }

        return false; // status.lastupdatetime > 提交时的timestamp 表示operator作了处理
    }

    /**
     * todo 模板
     */
    public void process() {
        // log
        // doProcess generate result
        //   check status outdated
        //   check status.state
        //   compare cr.spec and pods' spec
        // trigger post hook if result is success
    }

    public void setFlag(boolean flag) {
        this.flag.set(flag);
    }


    @Override
    public RestoreServiceImpl getRestoreService() {
        return this.restoreService;
    }

    @Override
    public BackupService getBackupService() {
        return this.backupService;
    }

    /**
     * 判断是否需要进行 恢复操作
     *
     * @param jobDataMap
     * @param app
     * @return
     */
    public void getIsReStoreSuccess(Map<String, String> jobDataMap, CloudApp app, OpsResultDTO.Builder result) {
        // 是否恢复操作成功
        if (StringUtils.isNotEmpty(jobDataMap.get("backupHisId")) && result.build().getStopJob()) {
            result.stopJob(false).status(StatusConstant.RUNNING);
            String msg = (StringUtils.isEmpty(result.build().getMsg())) ? "" : result.build().getMsg() + "。App restore msg:";
            result.msg(msg + "The APP is created and is being restored");
            String recoverStatus = executeRecoveryProcess(app.getId(), Integer.valueOf(jobDataMap.get("backupHisId")));
            logInfo(app, ActionEnum.CREATE, "The APP is created and is being restored");
            if (StringUtils.isNotEmpty(recoverStatus)) {
                if (StatusConstant.SUCCESS.equals(recoverStatus)) {
                    result.msg(msg + "The APP was successfully created and restored");
                    result.stopJob(true).status(StatusConstant.SUCCESS);
                } else if (StatusConstant.FAIL.equals(recoverStatus)) {
                    result.msg(msg + "The APP was created successfully, but recovery failed");
                    result.stopJob(true).status(StatusConstant.FAIL);
                }
            }
        }
    }

    public boolean isRecoveryDone(Integer appid) {
        Map<String, Object> searchMap = Collections.singletonMap("appId", appid);
        log.info("[Clone CR]isRecoveryDone-正在进行的恢复操作的应用为=appId:" + appid);
        List<RestoreHis> restoreHisList = getRestoreService().list(searchMap);
        return CollectionUtils.isEmpty(restoreHisList) ? false : true;
    }

    public String getProcessStatus(Integer appid) {
        Map<String, Object> searchMap = Collections.singletonMap("appId", appid);
        log.info("[Clone CR]getProcessStatus-正在进行的恢复操作的应用为=appId:" + appid);
        List<RestoreHis> restoreHisList = getRestoreService().list(searchMap);
        if (CollectionUtils.isEmpty(restoreHisList))
            return StatusConstant.FAIL;

        Set<String> runStatusSet = ImmutableSet.of(StatusConstant.SUCCESS, StatusConstant.FAIL, StatusConstant.RUNNING);
        String status = restoreHisList.get(0).getStatus();
        log.info(String.format("[Clone CR]getProcessStatus-当前appid:%d的恢复操作的状态值为<0:成功；1：失败；2：运行中>：%s", appid, status));
        if (runStatusSet.contains(status)) {
            return status;
        } else {
            return StatusConstant.FAIL;
        }
    }

    public void performRecovery(Integer appid, Integer backupHisId) {
        getBackupService().restore("", appid, backupHisId, null, null, null, "");
    }

    public String executeRecoveryProcess(Integer appid, Integer backupHisId) {
        if (isRecoveryDone(appid)) {
            log.info(String.format("[Clone CR]executeRecoveryProcess-appid:%d已进行过恢复操作，开始校验...", appid));
            return getProcessStatus(appid);
        } else {
            log.info(String.format("[Clone CR]executeRecoveryProcess-appid:%d未进行过恢复操作，开始使用backupHisId:%d执行恢复...",
                    appid, backupHisId));
            performRecovery(appid, backupHisId);
            return StatusConstant.RUNNING;
        }
    }

    protected AppConditionChecker[] genericSpecChecker(KubeClient client, CustomResource currentDeployCr, CustomResource expectedDeployCr) {
        // 比较 expected spec & current deploy spec
        AppKind appKind = Arrays.stream(AppKind.values())
                .filter(kind -> kind.getCrClass() == currentDeployCr.getClass())
                .findFirst().orElseThrow(() -> new RuntimeException("cr type not match any defined appKind"));
        return new AppConditionChecker[]{
                new AppConditionChecker(
                        "check spec match",
                        () -> {
                            try {
                                List<String> diffResults = BeanUtil.diff(expectedDeployCr.getSpec(), currentDeployCr.getSpec(),
                                        null, true, "");
                                return diffResults.isEmpty();
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        }
                ),
                new AppConditionChecker(
                        "check pod",
                        () -> {
                            List<PodDTO> pods = client.listPod(currentDeployCr.getMetadata().getNamespace(),
                                    appKind.labels(currentDeployCr.getMetadata().getName()));
                            // todo check pod numbers and status
                            return !pods.isEmpty();
                        }

                )

        };
    }

    protected AppConditionChecker[] genericStatusChecker(CustomResource cr) {
        AppKind appKind = Arrays.stream(AppKind.values())
                .filter(kind -> kind.getCrClass() == cr.getClass())
                .findFirst().orElseThrow(() -> new RuntimeException("cr type not match any defined appKind"));
        return new AppConditionChecker[]{
                new AppConditionChecker(
                        "Check if cr or cr status is not empty",
                        () -> cr != null && cr.getStatus() != null,
                        false
                ),
                new AppConditionChecker(
                        "check observed generation is ",
                        () -> {
                            try {
                                return cr.getMetadata().getGeneration().equals(BeanUtils.getProperty(cr.getStatus(), "observedGeneration"));
                            } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
                                return true;
                            }
                        }
                ),
                new AppConditionChecker(
                        "check cr state is ready",
                        () -> appKind.getAlertStatus(cr) == null,
                        YamlEngine.marshal(cr.getStatus())
                )
        };
    }


    @AllArgsConstructor
    public static class AppConditionChecker {
        private final String description;
        private final Supplier<Boolean> condition;
        // check 的过程中更新result
        private String result = "";
        // still continue if condition fail
        @Getter
        private boolean continuing = true;
        // stop if condition true
//        private Supplier<Boolean> stopCondition;

        public AppConditionChecker(String description, Supplier<Boolean> condition, String result) {
            this.description = description;
            this.condition = condition;
            this.result = result;
        }

        public AppConditionChecker(String description, Supplier<Boolean> condition, boolean isContinue) {
            this.description = description;
            this.condition = condition;
            this.continuing = isContinue;
        }

        public AppConditionChecker(String description, Supplier<Boolean> condition) {
            this.description = description;
            this.condition = condition;
        }

        public String getResult() {
            return result;
        }

        public String getDescription() {
            return description;
        }

        public boolean check() {
            return condition.get();
        }
    }

    public static class ConditionCheckerRunner {
        private final List<AppConditionChecker> checkers = new ArrayList<>();

        public void addChecker(AppConditionChecker... checkers) {
            this.checkers.addAll(Arrays.asList(checkers));
        }

        public OpsResultDTO runChecks() {
            StringBuilder sb = new StringBuilder();
            boolean pass = true;
            boolean hasFailure = false;
            for (AppConditionChecker checker : checkers) {
                pass = checker.check();
                if (!pass && !hasFailure)
                    hasFailure = true;
                //Checker: SpecChecker
                //Description: Checks if spec match
                //Result: PASS
                String result = pass ? "PASS" : "FAIL" + " " + checker.getResult();
                sb.append("Description: ").append(checker.getDescription()).append("\n")
                        .append("Result: ").append(result).append("\n");
                // if stop

                // if continue
                if (!pass && !checker.isContinuing()) {
                    break;
                }
            }
            OpsResultDTO.Builder builder = OpsResultDTO.builder();
            builder.msg(sb.toString());
            if (!hasFailure) {
                builder.status(StatusConstant.SUCCESS);
            }
            return builder.build();
        }

        public OpsResultDTO runChecks(AppConditionChecker... checkers) {
            addChecker(checkers);
            return runChecks();
        }
    }

    /**
     * 多中心应用，主AZ执行以下运维操作变更需要维护灾备配置, 如扩/缩容, 安装，访问管理，删除/还原
     * 主AZ执行以下运维操作需要同步到灾备AZ, 如升级
     */
    protected  <T extends CustomResource> AppConditionChecker[] standbyAZChecker(CloudApp currentApp, T cr, ActionEnum action) {
        if (!ACTION_BELONGS.contains(action)) return null;
        // check role is primary & there are standby az besides
        if (!CloudAppConstant.ROLE_PRIMARY.equals(currentApp.getRole())) return null;
        List<CloudApp> standbyList = appService.getPhysicApps(currentApp.getLogicAppId()).stream()
                .filter(app -> !app.getId().equals(currentApp.getId()))
                .filter(app -> app.getRole().equals(CloudAppConstant.ROLE_STANDBY))
                .collect(Collectors.toList());
        if (standbyList.isEmpty()) return null;
        Map<Integer, T> standbyCrs = new HashMap<>();
        return new AppConditionChecker[]{
                new AppConditionChecker("Standby app ready", () -> {
                    // 检查 standby cr 状态(todo 检查失败会阻塞primary当前操作成功)
                    for (CloudApp standbyApp : standbyList) {
                        KubeClient k8s = kubeClientService.get(standbyApp.getKubeId());
                        T standbyCr = (T)k8s.listCustomResource(cr.getClass(), standbyApp.getCrName(), standbyApp.getNamespace());
                        standbyCrs.put(standbyApp.getId(), standbyCr);
                        ConditionCheckerRunner runner = new ConditionCheckerRunner();
                        runner.addChecker(genericStatusChecker(standbyCr));
                        OpsResultDTO statusResult = runner.runChecks();
                        if (StatusConstant.SUCCESS.equals(statusResult.getStatus())) {
                            return true;
                        }
                    }
                    return false;
                }, false),
                new AppConditionChecker("Standby zone updated", ()->{
                    // 检查 standby cr 是否更新
                    for (CloudApp standbyApp : standbyList) {
                        KubeClient k8s = kubeClientService.get(standbyApp.getKubeId());
                        AppKindService instance = AppServiceLoader.getInstance(
                                AppKind.valueOf(standbyApp.getKind(), standbyApp.getArch()));
                        // configure standby Cr
                        T actStandbyCr = standbyCrs.get(standbyApp.getId());
                        T expCr = YamlEngine.unmarshal(
                                StringUtils.isEmpty(standbyApp.getCrRun()) ? standbyApp.getCr() : standbyApp.getCrRun(),
                                (Class<T>)cr.getClass());
                        CloudAppVO standbyVo = ((MultiAZService)instance).getMapper().toVo(
                                instance.reviewSpec(applySpecOfPrimaryOrStandby(
                                        currentApp, standbyApp, action))); // copy spec
                        org.springframework.beans.BeanUtils.copyProperties(standbyApp, standbyVo,
                                "cpu", "memory", "disk", "version"); // overwrite populated with standby
                        // sync spec from primary
                        ((MultiAZService)instance).configureMultiAZ(expCr, cr, standbyVo);
                        // todo for ordered operation - propagate primary spec to standby
                        // 即 构造installVo 调用更新接口

                        try {
                            List<String> diff = BeanUtil.diff(actStandbyCr.getSpec(),
                                    expCr.getSpec(),
                                    null,
                                    true,
                                    "");
                            if (diff.isEmpty()) {
                                CloudApp updatedApp = appService.selectForUpdateByID(standbyApp.getId());
                                updatedApp.setCr(YamlEngine.marshal(expCr)); // 可能并发更新(如果standby同时在执行运维操作)，但幂等
                                appService.update(updatedApp);
                                return true;
                            } else {
                                CloudApp updatedApp = appService.selectForUpdateByID(standbyApp.getId());
                                updatedApp.setCrRun(YamlEngine.marshal(expCr));
                                appService.update(updatedApp);
                                k8s.updateCustomResource(expCr, (Class<T>) expCr.getClass());
                            }
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                    return false;
                }, false)
        };
    }

    private static CloudApp applySpecOfPrimaryOrStandby(CloudApp primary, CloudApp standby, ActionEnum action) {
//        if (action == SWITCH_AZ_ROLE)
//            return primary; // 主备切换回滚使用primary的cr_run，其包含mm2的规格 因为switching
//        if (ACTION_TO_PROPAGATE.contains(action)) return primary;
//            return standby;
        return standby;
    }

}