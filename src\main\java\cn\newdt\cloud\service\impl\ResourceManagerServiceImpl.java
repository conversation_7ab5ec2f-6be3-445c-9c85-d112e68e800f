package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.domain.cr.MongoDBCommunity;
import cn.newdt.cloud.domain.cr.Zookeeper;
import cn.newdt.cloud.dto.ConfigAndStorageResourceDTO;
import cn.newdt.cloud.dto.Label;
import cn.newdt.cloud.dto.PageDTO;
import cn.newdt.cloud.dto.ResourceDTO;
import cn.newdt.cloud.filter.ResourceChangeLog;
import cn.newdt.cloud.filter.ResourceView;
import cn.newdt.cloud.mapper.CloudBackupStorageMapper;
import cn.newdt.cloud.mapper.CloudDataStorageMapper;
import cn.newdt.cloud.mapper.CloudQuotaMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.sched.impl.ZookeeperWatch;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.*;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.bean.UserInfo;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.AsymmetricEncryptionUtil;
import cn.newdt.commons.utils.UserUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.mifmif.common.regex.Generex;
import io.fabric8.kubernetes.api.model.ConfigMap;
import io.fabric8.kubernetes.api.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.ActionEnum.DELETE_PERSISTENTVOLUME;
import static cn.newdt.cloud.constant.ActionEnum.DELETE_PERSISTENTVOLUMECLAIM;
import static cn.newdt.cloud.constant.ApiConstant.*;
import static cn.newdt.cloud.utils.MybatisUtil.ORM_RESULT_MAP;

/**
 * 资源配额管理服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/20 17:43
 */
@Slf4j
@Service("mysqlResourceManageSvr")
public class ResourceManagerServiceImpl implements ResourceManagerService {

    @Autowired
    private KubeClientService kubeClientService;

    @Autowired
    private CloudAppService cloudAppService;

    @Autowired
    private TenantService tenantService;

    @Autowired
    private KubeConfigService kubeConfigService;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private ResourceManagerService resourceManagerService;
    @Autowired
    private CloudDataStorageMapper cloudDataStorageMapper;
    @Autowired
    private CloudBackupStorageMapper cloudBackupStorageMapper;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private CloudQuotaMapper cloudQuotaMapper;

    @Value("${dataStorage.cm.name:local-path-config}")
    private String dataStorageCMName;
    @Value("${dataStorage.cm.namespace:local-path-storage}")
    private String dataStorageCMNamespace;


    @Override
    public void deletePv(List<ConfigAndStorageResourceDTO> pvNameList) {
        for (ConfigAndStorageResourceDTO pvDto : pvNameList) {
            Integer appId = pvDto.getAppId();
            Integer kubeId = pvDto.getKubeId();
            String pvName = pvDto.getName();
            resourceManagerService.deletePv(kubeId, pvName, appId);
        }
    }

    @Override
    @ResourceChangeLog(action = DELETE_PERSISTENTVOLUME)
    public boolean deletePv(Integer kubeId, String pvName, Integer appId) {
        if (kubeId == null || StringUtils.isEmpty(pvName))
            return false;
        return kubeClientService.get(kubeId).deletePv(pvName);
    }

    @Override
    @ResourceChangeLog(action = DELETE_PERSISTENTVOLUMECLAIM)
    public boolean deletePvc(Integer kubeId, String namespace, String name, Integer appId) {
        if (kubeId == null || StringUtils.isEmpty(name) || StringUtils.isEmpty(namespace))
            return false;
        return kubeClientService.get(kubeId).deletePvc(namespace, name);
    }

    @Override
    public void deletePvcByName(String namespace, List<ConfigAndStorageResourceDTO> pvcNameList) {
        List<ConfigAndStorageResourceDTO> pvNameList = new ArrayList<>();
        for (ConfigAndStorageResourceDTO dto : pvcNameList) {
            String pvcName = dto.getName();
            Integer kubeId = dto.getKubeId();
            Integer appId = dto.getAppId();
            KubeClient kubeClient = kubeClientService.get(kubeId);
            PersistentVolumeClaim pvc = kubeClient.getPvc(namespace, pvcName);
            String storageClassName = pvc.getSpec().getStorageClassName();
            //通过pvc的volumeName找到对应的pv
            String volumeName = pvc.getSpec().getVolumeName();
            resourceManagerService.deletePvc(kubeId, namespace, pvcName, appId);

            //如果pvc不存在storageClassName，先删除pvc，再删除对应的pv
            if (StringUtils.isEmpty(storageClassName)) {
                PersistentVolume pv = kubeClient.getPv(volumeName);
                ConfigAndStorageResourceDTO pvDTO = new ConfigAndStorageResourceDTO();
                pvDTO.setAppId(appId);
                pvDTO.setKubeId(kubeId);
                pvDTO.setName(pv.getMetadata().getName());
                pvNameList.add(pvDTO);
            }
        }
        deletePv(pvNameList);
    }

    @Autowired
    private CloudAppLogicService appLogicService;

    public List<Map<String, String>> pvcList(Integer appId, Integer appLogicId) {
        if (appLogicId != null && appLogicId != 0) {
            List<CloudApp> physicApps = appLogicService.getPhysicApps(appLogicId);
            List<Map<String, String>> pvcProps = new LinkedList<>();
            for (CloudApp physicApp : physicApps) {
                pvcProps.addAll(pvcList(physicApp.getId(), physicApp));
            }
            return pvcProps;
        }
        return pvcList(appId, cloudAppService.get(appId));
    }

    @Override
    public List<Map<String, String>> allPvcList(Integer appId, Integer appLogicId, Boolean other, Integer kubeId, Integer tenantId) {
        if (null != other && other) {
            //查询其他类型的pvc数据
            return otherPvcList(kubeId, tenantId);
        } else {
            //根据相关应用获取pvc数据
            return pvcList(appId, appLogicId);
        }
    }

    private List<Map<String, String>> otherPvcList(Integer kubeId, Integer tenantId) {
        List<Map<String, String>> pvcProps = new LinkedList<>();
        List<CloudTenantVO> cloudTenantVOS = tenantService.listTenantByUserId();
        for (CloudTenantVO cloudTenantVO : cloudTenantVOS) {
            List<Map<String, String>> everyPvcProps = new LinkedList<>();
            tenantId = cloudTenantVO.getTenantId();
            //获取相关命名空间信息
            String namespace = tenantService.findById(tenantId).getNamespace();
            if (null == kubeId) {
                //应用视图
                for (TenantClusterVO tenantClusterVO : departmentService.getOwnedClustersOfTenant(tenantId)) {
                    List<PersistentVolumeClaim> persistentVolumeClaims = filterOtherPVCList(tenantClusterVO.getId(), namespace);
                    buildPVCResultData(tenantClusterVO.getId(), null, null, tenantClusterVO.getName(), everyPvcProps, persistentVolumeClaims);
                }
            } else {
                //集群视图
                //查询相关集群信息
                KubeConfig kubeConfig = kubeConfigService.get(kubeId);
                if (null == kubeConfig) {
                    log.error("未查询到相关的集群信息，kubeId为：" + kubeId);
                    throw new CustomException(600, "未查询到相关的集群信息");
                }
                List<PersistentVolumeClaim> persistentVolumeClaims = filterOtherPVCList(kubeId, namespace);
                buildPVCResultData(kubeId, null, null, kubeConfig.getName(), everyPvcProps, persistentVolumeClaims);
            }
            pvcProps.addAll(everyPvcProps);
        }
        return pvcProps;
    }

    /*
        过滤出其他类型的pv
     */
    private List<PersistentVolumeClaim> filterOtherPVCList(Integer kubeId, String namespace) {
        PersistentVolumeClaimList persistentVolumeClaimList = kubeClientService.get(kubeId).listPvcWithLabelKeyAndNameSpace(namespace, CloudAppConstant.CustomLabels.APP);
        //过滤出相关不符合要求的数据
        //集群视图
        return persistentVolumeClaimList.getItems().stream().filter(item -> CloudAppConstant.PVCPhase.pending.equalsIgnoreCase(item.getStatus().getPhase())).collect(Collectors.toList());
    }

    private List<Map<String, String>> pvcList(Integer appId, CloudApp app) {
        try {
            List<Map<String, String>> pvcProps = new LinkedList<>();
            PersistentVolumeClaimList persistentVolumeClaimList = kubeClientService.get(app.getKubeId()).listPvc(app.getNamespace(), null);

            List<PersistentVolumeClaim> pvcList = filterPvcList(persistentVolumeClaimList.getItems(), app.getCrName(), AppKind.valueOf(app.getKind(), app.getArch()),
                    AppUtil.getResourceVersion(app));
            List<CloudApp> appList = findAllList(Collections.singletonMap("crName", app.getCrName()));
            List<PersistentVolumeClaim> filterPvcList = filterPvcListByClaimName(pvcList, app, appList);
            return buildPVCResultData(app.getKubeId(),app.getId(), app.getCrName(), app.getKubeName(), pvcProps, filterPvcList);
        } catch (Exception e) {
            throw new CustomException(600, e.getMessage());
        }
    }

    /*
        构建pvc返回值数据
     */
    private List<Map<String, String>> buildPVCResultData(Integer kubeId, Integer appId,String crname,String kubeName, List<Map<String, String>> pvcProps, List<PersistentVolumeClaim> filterPvcList) {
        for (PersistentVolumeClaim pvc : filterPvcList) {
            HashMap<String, String> prop = new HashMap<>();
            ObjectMeta metadata = pvc.getMetadata();
            prop.put("name", metadata.getName());
            Date date = DateUtil.parseUTC(metadata.getCreationTimestamp());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            prop.put("createTimestamp", date == null ? null : sdf.format(date));
            prop.put("namespace", metadata.getNamespace());
            PersistentVolumeClaimSpec spec = pvc.getSpec();
            prop.put("accessModes", Arrays.toString(spec.getAccessModes().toArray()));
            Quantity storage = spec.getResources().getRequests().get("storage");
            prop.put("resourceRequest", storage == null ? null : storage.toString());
            prop.put("storageClassName", spec.getStorageClassName());
            prop.put("volumeMode", spec.getVolumeMode());
            prop.put("volumeName", spec.getVolumeName());
            prop.put("status", pvc.getStatus().getPhase());
            prop.put("kubeId", kubeId+"");
            prop.put("kubeName", kubeName);
            prop.put("crName", crname);
            prop.put("appId", appId != null ? appId + "" : null);
            pvcProps.add(prop);
        }
        return pvcProps;
    }

    public List<Map<String, String>> pvList(Integer appId, Integer appLogicId, Boolean other, Integer kubeId, Integer tenantId) {
        if (null != other && other) {
            return otherPVList(kubeId, tenantId);
        }
        List<Map<String, String>> pvProps = new LinkedList<>();
        List<Map<String, String>> pvcProps = pvcList(appId, appLogicId);
        for (Map<String, String> pvcProp : pvcProps) {
            String volumeName = pvcProp.get("volumeName");
            if (volumeName == null) continue;
            PersistentVolume pv = kubeClientService.get(Integer.valueOf(pvcProp.get("kubeId"))).getPv(volumeName);
            //相关pv返回参数值构建
            HashMap<String, String> prop = buildPVResultData(pvcProp.get("kubeId"), pvcProp.get("kubeName"), pv);
            pvProps.add(prop);
        }
        return pvProps;
    }

    /*
        暂时不考虑存储插件的pv问题，主要考虑hostpath插件的pv问题
        正常情况下，存储插件的pv会随着pvc的删除而删除，而hostpath是会分离
     */
    private List<Map<String, String>> otherPVList(Integer kubeId, Integer tenantId) {
        List<Map<String, String>> pvProps = new LinkedList<>();
        List<CloudTenantVO> cloudTenantVOS = tenantService.listTenantByUserId();
        for (CloudTenantVO cloudTenantVO : cloudTenantVOS) {
            List<Map<String, String>> everyPvProps = new LinkedList<>();
            tenantId = cloudTenantVO.getTenantId();
            //过滤出相关other类型的pv
            if (null == kubeId) {
                if (null == tenantId) {
                    log.error("相关部门id参数为空" + tenantId);
                    throw new CustomException(600, "相关部门id参数不能为空");
                }
                //应用视图
                for (TenantClusterVO tenantClusterVO : departmentService.getOwnedClustersOfTenant(tenantId)) {
                    List<PersistentVolume> persistentVolumes = filterOtherPvList(tenantClusterVO.getId());
                    List<Map<String, String>> tempProps = persistentVolumes.stream().map(otherpv -> {
                        return buildPVResultData(String.valueOf(tenantClusterVO.getId()), tenantClusterVO.getName(), otherpv);
                    }).collect(Collectors.toList());
                    everyPvProps.addAll(tempProps);
                }
            } else {
                //集群视图
                //查询相关集群信息
                KubeConfig kubeConfig = kubeConfigService.get(kubeId);
                if (null == kubeConfig) {
                    log.error("未查询到相关的集群信息，kubeId为：" + kubeId);
                    throw new CustomException(600, "未查询到相关的集群信息");
                }
                List<PersistentVolume> otherPVList = filterOtherPvList(kubeId);
                everyPvProps = otherPVList.stream().map(otherpv -> {
                    return buildPVResultData(String.valueOf(kubeId), kubeConfig.getName(), otherpv);
                }).collect(Collectors.toList());
            }
            pvProps.addAll(everyPvProps);
        }
        return pvProps;
    }

    /*
        构建pv返回值相关信息
     */
    private HashMap<String, String> buildPVResultData(String kubeId, String kubeName, PersistentVolume pv) {

        HashMap<String, String> prop = new HashMap<>();
        ObjectMeta metadata = pv.getMetadata();
        prop.put("name", metadata.getName());
        Date date = DateUtil.parseUTC(metadata.getCreationTimestamp());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        prop.put("createTimestamp", date == null ? null : sdf.format(date));
        prop.put("namespace", metadata.getNamespace());
        PersistentVolumeSpec spec = pv.getSpec();
        if (null != spec.getClaimRef()) {
            prop.put("pvc", spec.getClaimRef().getName());
        }
        prop.put("accessModes", Arrays.toString(spec.getAccessModes().toArray()));
        prop.put("reclaimPolicy", spec.getPersistentVolumeReclaimPolicy());
        prop.put("storageClassName", spec.getStorageClassName());
        prop.put("volumeMode", spec.getVolumeMode());
        Quantity storage = spec.getCapacity().get("storage");
        prop.put("capacity", storage == null ? null : storage.toString());
        prop.put("status", pv.getStatus().getPhase());
        if ("Released".equalsIgnoreCase(pv.getStatus().getPhase())) {
            prop.put("isDel", "true");
        } else {
            prop.put("isDel", "false");
        }
        prop.put("kubeId", kubeId);
        prop.put("kubeName", kubeName);
        return prop;
    }

    /*
        过滤出其他类型的pv
        属于应用类型的，状态值不对的
     */
    private List<PersistentVolume> filterOtherPvList(Integer kubeId) {
        //获取所有pv
        return kubeClientService.get(kubeId).listPvWithLabelKey(CloudAppConstant.CustomLabels.APP).getItems().stream().filter(item -> {
            return null == item.getStatus() || CloudAppConstant.PVPhase.released.equalsIgnoreCase(item.getStatus().getPhase()) || CloudAppConstant.PVPhase.available.equalsIgnoreCase(item.getStatus().getPhase());
        }).collect(Collectors.toList());
    }

    @Override
    @ResourceView
    public PageInfo<CloudAppVO> searchPageInfo(PageDTO pageDTO) {
        //修改时间排序字段为表字段
        if (pageDTO.getPageSortProp().equals("createTimestamp")) {
            pageDTO.setPageSortProp("createTime");
        }
        String sortCol = null;
        if (pageDTO.getPageSortProp() != null) {
            String resultMapId = "CloudApp" + ORM_RESULT_MAP;
            if (MybatisUtil.existResultMap(resultMapId)) {
                sortCol = MybatisUtil.getJavaPropMapDBColumn(resultMapId, pageDTO.getPageSortProp());
            }
        }

        pageDTO.getCondition().put(PAGE_SORT_COL, sortCol);
        pageDTO.getCondition().put(PAGE_NUM, pageDTO.getPageNum());
        pageDTO.getCondition().put(PAGE_SIZE, pageDTO.getPageSize());
        pageDTO.getCondition().put(PAGE_SORT, pageDTO.getPageSort());
        //查找到对应kubeId、kind下的appList
        List<CloudApp> appList = findAllList(pageDTO.getCondition());
        Integer kubeId = Integer.valueOf(pageDTO.getCondition().get("kubeId").toString());
        PageInfo<CloudAppVO> pageInfo = new PageInfo<>();
        if(!CollectionUtils.isEmpty(appList)) {
            List<String> namespaceList = appList.stream().map(a -> a.getNamespace()).distinct().collect(Collectors.toList());
            PersistentVolumeClaimList persistentVolumeClaimList = kubeClientService.get(kubeId).listPvc(namespaceList, null);
            if(Objects.isNull(persistentVolumeClaimList)) {
                return pageInfo;
            }
            //获取当前kubeId所有的pvcList
            List<PersistentVolumeClaim> pvcList = persistentVolumeClaimList.getItems();
            pageInfo = PageUtil.page2PageInfo(appList, CloudAppVO.class, a -> {
                List<PersistentVolumeClaim> pvcList1 = pvcList.stream().filter(p -> p.getMetadata().getNamespace().equals(a.getNamespace())).collect(Collectors.toList());
                List<PersistentVolumeClaim> pvcsOfCurrentApp = filterPvcList(pvcList1, a.getCrName(), AppKind.valueOf(a.getKind(), a.getArch()), AppUtil.getResourceVersion(a));
                List<PersistentVolumeClaim> persistentVolumeClaims = filterPvcListByClaimName(pvcsOfCurrentApp, a, appList);
                if(CollectionUtils.isEmpty(persistentVolumeClaims)) {
                    return null;
                }
                CloudAppVO vo = new CloudAppVO();
                BeanUtils.copyProperties(a, vo);
                //因为cr和cr_run使用了@JsonIgnore注解，所以这里用yaml来接值
                String Yaml = StringUtils.isEmpty(vo.getCr()) ? vo.getCrRun() : vo.getCr();
                vo.setYaml(Yaml);
                return vo;
            });
            //对从数据库中查到的数据进行过滤，并分页
            List<CloudAppVO> voList = pageInfo.getList().stream().filter(app -> Objects.nonNull(app)).collect(Collectors.toList());
            List<CloudAppVO> resultList = new ArrayList<>();
            // 修复全部下载数据为空问题
            if(Objects.isNull(pageDTO.getPageNum()) && Objects.isNull(pageDTO.getPageSize())) {
                resultList.addAll(voList);
            } else {
                if(voList.size() >= (pageDTO.getPageSize() + pageDTO.getPageNum())){
                    for(int i = pageDTO.getPageNum(); i < (pageDTO.getPageSize() + pageDTO.getPageNum()); i++){
                        resultList.add(voList.get(i));
                    }
                }else {
                    for (int i = pageDTO.getPageNum(); i < voList.size(); i++) {
                        resultList.add(voList.get(i));
                    }
                }
            }
            pageInfo.setList(resultList);
            pageInfo.setTotal(voList.size());
        } else {
            pageInfo.setList(new ArrayList<>());
            pageInfo.setTotal(0);
        }
        return pageInfo;
    }

    @Autowired
    private AppMultiAZService appMultiAZService;

    @Override
    @ResourceView
    public PageInfo searchPage4AppView(PageDTO pageDTO) {
        Integer pageNum = pageDTO.getPageNum();
        Integer pageSize = pageDTO.getPageSize();
        pageDTO.setPageNum(null);
        pageDTO.setPageSize(null);
        PageInfo<InstallAppVo> appVOPageInfo = appMultiAZService.searchWithOutAdditionalInfo(pageDTO);
        UserInfo userInfo = UserUtil.getCurrentUser();
        List<InstallAppVo> list = appVOPageInfo.getList().parallelStream().filter(vo -> {
                    UserUtil.setAsyncUserInfo(userInfo);
                    try {
                        return !pvcList(null, vo.getLogicId()).isEmpty();
                    } catch (Exception e) {
                        log.error("filter app int storage management, id=" + vo.getLogicId() + ", msg = " + e.getMessage());
                        return false; // 过滤掉查看pvcList失败的应用
                    }
                }).collect(Collectors.toList());
        int total = list.size();
        if (pageNum != null && pageSize != null)
            list = list.subList(pageNum, Math.min(list.size(), pageNum + pageSize));

        PageInfo pageInfo = new PageInfo(list);
        pageInfo.setTotal(total);
        return pageInfo;
    }

    /**
     * 获取应用信息
     * @param map  分页及搜索条件
     * @return
     */
    private List<CloudApp> findAllList(Map<String, Object> map) {
        if (map == null) {
            map = Collections.emptyMap();
        }
        return cloudAppService.findAllList(map);
    }

    /**
     * 获取应用信息时判断是否存在可删除pvc和pv资源
     * 1. 获取到所有的pvc资源
     * 2. 根据不同类型的pvc做过滤，匹配出每个pvc对应的crName
     * 3. 与app中的crName作比较，如果相等，则可以被删除
     *
     * @param pvcList         集群中的所有pvc资源
     * @param cr_Name         crName
     * @param type            应用类型 redis、mongodb、opengauss、mysql
     * @param resourceVersion
     * @return
     */
    private List<PersistentVolumeClaim> filterPvcList(List<PersistentVolumeClaim> pvcList, String cr_Name, AppKind type, String resourceVersion){
        List<PersistentVolumeClaim> newPvcList = new ArrayList<>();
        for (PersistentVolumeClaim pvc : pvcList) {
            //只保留status为Bound的pvc
//            if(!"Bound".equalsIgnoreCase(pvc.getStatus().getPhase())) {
//                continue;
//            }
            //过滤掉不存在Labels的pvc
            if(CollectionUtils.isEmpty(pvc.getMetadata().getLabels())) {
                continue;
            }
            String crName;
            String labelsValue;
            String name = pvc.getMetadata().getName();
            //mysql
//            if (AppKind.MYSQL_HA.getKind().equalsIgnoreCase(type)) {
//                labelsValue = pvc.getMetadata().getLabels().get("from");
//                if (!StringUtils.isEmpty(labelsValue) && labelsValue.equalsIgnoreCase("mysqlhasync-operator")) {
//                    int endIndex = 0;
//                    try{
//                        Pattern pattern = Pattern.compile("((([1-9]?|1\\d)\\d|2([0-4]\\d|5[0-5]))x){3}(([1-9]?|1\\d)\\d|2([0-4]\\d|5[0-5]))");
//                        Matcher matcher = pattern.matcher(name);
//                        if (matcher.find()) {
//                            endIndex = name.indexOf(matcher.group());
//                        } else {
//                            continue;
//                        }
//                    } catch (Exception e) {
//                        log.error("get index error, msg{}", e);
//                        e.printStackTrace();
//                    }
//                    crName = name.substring(0, endIndex - 1);
//                    if (("mysql-" + cr_Name).equalsIgnoreCase(crName)) {
//                        newPvcList.add(pvc);
//                    }
//                } else {
//                    labelsValue = pvc.getMetadata().getLabels().get("mysql.oracle.com/cluster");
//                    if (!StringUtils.isEmpty(labelsValue) && labelsValue.equalsIgnoreCase(cr_Name)) {
//                        if (cr_Name.equalsIgnoreCase(labelsValue)) {
//                            newPvcList.add(pvc);
//                        }
//                    }
//                }
//            }
            Map<String, String> actLabels = pvc.getMetadata().getLabels();
            if (AppKind.TIDB.equals(type)) {
                if (TidbUtil.isTidbPvc(cr_Name,actLabels)) {
                    newPvcList.add(pvc);
                }
            } else {
                Label[] labels = type.labelOfPod(new CloudApp() {{
                    setCrName(cr_Name);
                }});
                if (Arrays.stream(labels).allMatch(l-> actLabels.containsKey(l.getName()) && actLabels.get(l.getName()).equals(l.getValue()))
                        && Objects.equals(actLabels.get(CloudAppConstant.CustomLabels.RESOURCE_VERSION), resourceVersion)) { // 仅匹配相同version的pvc. fix 1001541app
                    newPvcList.add(pvc);
                }
            }
//            if (AppKind.MongoDB == type) { // todo web端控制pvc的labels
//                //正则匹配出mongo的pvc
//                String regex = "logs-volume-.+.[0-9]$|data-volume-.+.[0-9]$";
//                boolean matches = Pattern.matches(regex, name);
//                //mongodb
//                if (matches) {
//                    // 取crName
//                    String newName = name.substring(0, name.lastIndexOf("-"));
//                    int endIndex = 0;
//                    try{
//                        endIndex = getEndIndex("-", name, 2);
//                    } catch (Exception e) {
//                        log.error("get index error, msg{}", e);
//                        e.printStackTrace();
//                    }
//                    crName = newName.substring(endIndex+1);
//                    if(cr_Name.equalsIgnoreCase(crName)) {
//                        newPvcList.add(pvc);
//                    }
//                }
//            } else {
//                Label[] labels = type.labelOfPod(new CloudApp() {{
//                    setCrName(cr_Name);
//                }});
//                Map<String, String> actLabels = pvc.getMetadata().getLabels();
//                if (Arrays.stream(labels).allMatch(l-> actLabels.containsKey(l.getName()) && actLabels.get(l.getName()).equals(l.getValue()))
//                    && Objects.equals(actLabels.get(CloudAppConstant.CustomLabels.RESOURCE_VERSION), resourceVersion)) { // 仅匹配相同version的pvc. fix 1001541app
//                    newPvcList.add(pvc);
//                }
//            }
        }
        return newPvcList;
    }

    /**
     * 根据匹配的字符和出现次数在字符串中寻找索引
     * @param regex  匹配的字符
     * @param input  进行处理的字符串
     * @param number 第几次出现
     * @return
     */
    private int getEndIndex(String regex, String input, int number) {
        Pattern pattern = Pattern.compile(regex);
        Matcher findMatcher = pattern.matcher(input);

        int indexNum=0;
        while(findMatcher.find()) {
            indexNum++;
            if (indexNum == number) {
                break;
            }
        }
        return findMatcher.start();
    }


    /**
     * 通过appName, namespace, kubeId判断是否存在同名未删除应用
     * 存在，通过valume:persistentVolumeClaim:claimName过滤pvcList
     * 不存在，不做处理
     *
     * @param a   选中的appId
     * @param pvcList 该crName对应所有pvc资源
     * @return
     */
    private List<PersistentVolumeClaim> filterPvcListByClaimName(List<PersistentVolumeClaim> pvcList, CloudApp a, List<CloudApp> appList){
        List<PersistentVolumeClaim> newPvcList;
        //获取app信息
        //获取同一集群、命名空间下同名app信息
        if (!CollectionUtils.isEmpty(appList))
            appList = appList.stream().filter(e -> e.getCrName().equals(a.getCrName()))
                .filter(e -> e.getNamespace().equals(a.getNamespace()))
                .filter(e -> Boolean.FALSE.equals(e.getDeleted()))
                .collect(Collectors.toList());
        //如果该应用已删除，并且不存在同名已删除应用，显示该应用名对应所有pvc资源
        if(Boolean.TRUE.equals(a.getDeleted())) {
            //如果该应用已删除，并且存在同名未删除应用，拿到未删除同名应用的当前ipList，过滤掉.
            if(!CollectionUtils.isEmpty(appList)) {
                // if pvc's relevant pod was deleted, we can't determine which app it belongs to, the current one or old one.
                // but we can just delete it.
                List<String> ipList = appList.stream().map(e -> e.getIpNode()).filter(Objects::nonNull).flatMap(s -> Arrays.stream(s).map(in -> in.getIp()))
                        .collect(Collectors.toList());
                List<PersistentVolumeClaim> persistentVolumeClaims = filterPvcByIpList(appList.get(0), pvcList, ipList);
                pvcList.removeAll(persistentVolumeClaims);
            }
            newPvcList = pvcList;
        } else {
            //如果该应用未删除，拿到应用名对应所有pvc，用ipList过滤
//            newPvcList = filterPvcByIpList(cloudApp, pvcList);
            newPvcList = pvcList;
        }
        return newPvcList;
    }


    /** TODO-- 缩容之后pod中的claimName会随之变化，结果导致会遗漏掉这些pvc，因此需要找到pod和pvc之间其他的对应关系去进行处理
     * 此方法处理的都是同名的应用中存在未删除的应用
     * 处理策略：
     * 1. 通过pod名获取pod信息，再获取spec.volumes.persistentVolumeClaim.claimName
     * 2. 与传入的pvcList作比较，不匹配pvcName的过滤掉
     *
     * @param app   集群中存在的同名未删除应用
     * @param pvcList  该crName对应所有pvc资源
     * @param ipList
     * @return
     */
    private List<PersistentVolumeClaim> filterPvcByIpList(CloudApp app, List<PersistentVolumeClaim> pvcList, List<String> ipList) {
        // es 、MYSQL_MGR直接返回pvcList
        if(AppKind.Elasticsearch.getKind().equalsIgnoreCase(app.getKind()) || app.getArch().equals(AppKind.MYSQL_MGR.getArch())){
            return pvcList;
        }

        List<PersistentVolumeClaim> newPvcList = new ArrayList<>();
        List<String> podNameList = new ArrayList<>();
        //get attr
        String crName = app.getCrName();
        String arch = app.getArch();
        Integer kubeId = app.getKubeId();
        String kind = app.getKind();
        AppKind appKind = AppKind.valueOf(kind, arch);
        String namespace = app.getNamespace();
        //mongoDB通过cr获取其members，拼接成podNname
        if(AppKind.MongoDB.getKind().equalsIgnoreCase(kind)) {
            KubeClient kubeClient = kubeClientService.get(app.getKubeId());
            MongoDBCommunity cr = kubeClient.listCustomResource(MongoDBCommunity.class, app.getCrName(), app.getNamespace());
            if(Objects.nonNull(cr)) {
                int members = cr.getSpec().getMembers();
                for(int i = 0; i < members; i++) {
                    podNameList.add(crName + "-" + i);
                }
            }
        }
        //根据不同应用类型，通过ipList处理返回pvcList
        //非mongoDB类型通过ip拼接成podName
        if(!CollectionUtils.isEmpty(ipList) && !AppKind.MongoDB.getKind().equalsIgnoreCase(kind)
                && !AppKind.Sentinel.getKind().equalsIgnoreCase(kind)) {
            String podName = "";
            String newIp = "";
            for (String ip : ipList) {
                if(AppKind.Redis.getKind().equalsIgnoreCase(kind)) {
                    if (AppKind.Redis_Cluster.getArch().equals(arch)){
                        newIp = ip.replace(".", "-");
                        podName = "rc" + "-" + crName + "-"+ newIp;
                    }else {
                        newIp = ip.replace(".", "-");
                        podName = "redis" + "-" + crName + "-"+ newIp;
                    }
                } else if(AppKind.OpenGauss.getKind().equalsIgnoreCase(kind)) {
                    newIp = ip.replace(".", "x");
                    podName = "og" + "-" + crName + "-pod-"+ newIp;
                } else if (AppKind.Sentinel.getKind().equalsIgnoreCase(kind)){
                    newIp = ip.replace(".", "-");
                    podName = "sentinel" + "-" + crName + "-"+ newIp;
                }else if (AppKind.Zookeeper.getKind().equalsIgnoreCase(kind)){
                    newIp = ip.replace(".", "-");
                    podName = "zk" + "-" + crName + "-"+ newIp;
                }else if (AppKind.Kafka.getKind().equalsIgnoreCase(kind)){
                    newIp = ip.replace(".", "-");
                    podName = "kafka" + "-" + crName + "-"+ newIp;
                }else if (AppKind.Broker.getKind().equalsIgnoreCase(kind)){
                    newIp = ip.replace(".", "-");
                    podName = "rocketmq" + "-" + crName + "-"+"broker"+ newIp;
                }else if (AppKind.NameServer.getKind().equalsIgnoreCase(kind)){
                    newIp = ip.replace(".", "-");
                    podName = "rocketmq" + "-" + crName + "-"+"nameserver"+ newIp;
                } else if (AppKind.MYSQL_HA == appKind) {
                    newIp = ip.replace(".", "-");
                    podName = "mysqlha" + "-" + crName + "-" + newIp;
                } else if (AppKind.PostgreSQL == appKind) {
                    newIp = ip.replace(".", "-");
                    podName = "pg" + "-" + crName + "-" + newIp;
                } else {
                    throw new IllegalArgumentException("不支持的app类型" + kind);
                }
                podNameList.add(podName);
            }
        }

        KubeClient kubeClient = kubeClientService.get(kubeId);
        if(!CollectionUtils.isEmpty(podNameList)) {
            for (String name : podNameList) {
                List<String> pvcNameList = new ArrayList<>();
                //获取volumes
                Pod pod = kubeClient.getPod(namespace, name);
                if(Objects.nonNull(pod)) {
                    List<Volume> volumes = pod.getSpec().getVolumes();
                    if(!CollectionUtils.isEmpty(volumes)) {
                        for (Volume volume : volumes) {
                            if(Objects.nonNull(volume.getPersistentVolumeClaim())) {
                                String pvcName = volume.getPersistentVolumeClaim().getClaimName();
                                pvcNameList.add(pvcName);
                            }
                        }
                    }
                    //过滤pvcList
                    if(!CollectionUtils.isEmpty(pvcNameList)) {
                        for (String pvcName : pvcNameList) {
                            List<PersistentVolumeClaim> filterPvcList = pvcList.stream()
                                    .filter(pvc -> pvc.getMetadata().getName().equalsIgnoreCase(pvcName)).collect(Collectors.toList());
                            newPvcList.addAll(filterPvcList);
                        }
                    }
                }
            }
        }
        return newPvcList;
    }

    @Override
    public void deletePvcByAppInfo(List<Integer> appIdList, String view) {
        List<ConfigAndStorageResourceDTO> pvNameAllList = new ArrayList<>();

        if ("cluster".equals(view)) {
            for (Integer appId : appIdList) {
                List<Map<String, String>> propList = pvcList(appId, 0);
                for (Map<String, String> map : propList) {
                    Integer kubeId = Integer.valueOf(map.get("kubeId"));
                    String namespace = map.get("namespace");
                    String crName = map.get("crName");
                    if (kubeId == null || StringUtils.isEmpty(namespace) || StringUtils.isEmpty(crName)) {
                        throw new CustomException(600, "参数缺失");
                    }
                    String storageClassName = map.get("storageClassName");
                    //通过pvc的volumeName找到对应的pv
                    String volumeName = map.get("volumeName");
                    resourceManagerService.deletePvc(kubeId, namespace, map.get("name"), appId);
                    //如果pvc不存在storageClassName，先删除pvc，再删除对应的pv
                    if (StringUtils.isEmpty(storageClassName) && volumeName != null) {
                        PersistentVolume pv = kubeClientService.get(kubeId).getPv(volumeName);

                        ConfigAndStorageResourceDTO pvDto = new ConfigAndStorageResourceDTO();
                        pvDto.setName(pv.getMetadata().getName());
                        pvDto.setKubeId(kubeId);
                        pvDto.setAppId(appId);
                        pvNameAllList.add(pvDto);
                    }
                }
            }

        }
        if ("application".equals(view)){
            for (Integer logicId : appIdList) {
                List<Map<String, String>> propList = pvcList(0, logicId);
                for (Map<String, String> map : propList) {
                    Integer kubeId = Integer.valueOf(map.get("kubeId"));
                    String namespace = map.get("namespace");
                    String crName = map.get("crName");
                    if (kubeId == null || StringUtils.isEmpty(namespace) || StringUtils.isEmpty(crName)) {
                        throw new CustomException(600, "参数缺失");
                    }
                    String storageClassName = map.get("storageClassName");
                    //通过pvc的volumeName找到对应的pv
                    String volumeName = map.get("volumeName");
                    int appId = Integer.parseInt(map.get("appId"));
                    resourceManagerService.deletePvc(kubeId, namespace, map.get("name"), appId);
                    //如果pvc不存在storageClassName，先删除pvc，再删除对应的pv
                    if (StringUtils.isEmpty(storageClassName) && volumeName != null) {
                        PersistentVolume pv = kubeClientService.get(kubeId).getPv(volumeName);

                        ConfigAndStorageResourceDTO pvDto = new ConfigAndStorageResourceDTO();
                        pvDto.setName(pv.getMetadata().getName());
                        pvDto.setKubeId(kubeId);
                        pvDto.setAppId(appId);
                        pvNameAllList.add(pvDto);
                    }
                }
            }
        }

        if (!CollectionUtils.isEmpty(pvNameAllList))
            deletePv(pvNameAllList);
    }

    @Override
    public void deletePvcByApp(CloudApp app) {
        kubeClientService.get(app.getKubeId())
                .deletePvc(Label.toMap(AppKind.valueOf(app.getKind(), app.getArch()).labelOfPod(app)), app.getNamespace());
    }

    @Override
    public Map<String, Object> checkMount(CloudBackupStorageVO cloudBackupStorage) {
        //结果map
        Map<String, Object> resMap = new HashMap<>();
        //校验参数
        checkBackupStoragePara(cloudBackupStorage);
        //根据备份存储类型类型返回不同值，nas返回容量，s3返回是否挂载成功
        if (CloudAppConstant.StorageType.S3.equalsIgnoreCase(cloudBackupStorage.getStorageType())) {
            //对密码解密
            String decryptMasterPassword = AsymmetricEncryptionUtil.getEncryptInstance().decrypt(cloudBackupStorage.getSecretKey());
            //生成认证文件
            try {
                String makePassfile = "echo \"" + cloudBackupStorage.getAccessKey() + ":" + decryptMasterPassword + "\" > /opt/s3pwd;chmod 600 /opt/s3pwd;mkdir -p " + CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH;
                ProcessBuilder processBuilder = new ProcessBuilder();
                String osName = System.getProperty("os.name");
                if (osName.contains("Windows")) {
                    processBuilder.command("CMD", "/c", "dir *.txt");
                } else {
                    processBuilder.command("sh", "-c", makePassfile);
                }
                Process process = processBuilder.start();
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                StringBuilder output = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line);
                }

                int exitCode = process.waitFor();

                log.info("mount Exit Code: " + exitCode);
                log.info("mount Output:\n" + output.toString());

                if (0 != exitCode) {
                    resMap.put("status", false);
                    resMap.put("msg", "挂载失败，生成认证信息失败");
                    return resMap;
                }
            } catch (Exception e) {
                throw new CustomException(600, "生成认证文件失败！错误信息为：" + e.getMessage());
            }

            //挂载
            try {
                String mountCmd = "s3fs -o passwd_file=/opt/s3pwd -o use_path_request_style -o endpoint=" + cloudBackupStorage.getRegion() + " -o url=" + cloudBackupStorage.getServer() + " -o allow_other -o no_check_certificate " + cloudBackupStorage.getBucket() + " " + CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH + "/ -o ssl_verify_hostname=0;sleep 1;df |grep " + CloudAppConstant.CLOUD_MOUNT_PATH.S3_PATH;
                ProcessBuilder processBuilder = new ProcessBuilder();
                String osName = System.getProperty("os.name");
                if (osName.contains("Windows")) {
                    processBuilder.command("CMD", "/c", "dir *.txt");
                } else {
                    processBuilder.command("sh", "-c", mountCmd);
                }
                Process process = processBuilder.start();
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                StringBuilder output = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line);
                }

                int exitCode = process.waitFor();

                log.info("mount Exit Code: " + exitCode);
                log.info("mount Output:\n" + output.toString());

                if (0 != exitCode || StringUtils.isEmpty(output.toString())) {
                    resMap.put("status", false);
                    resMap.put("msg", "挂载失败");
                    return resMap;
                }
                resMap.put("status", true);
                resMap.put("msg", "挂载成功");
                return resMap;
            } catch (Exception e) {
                throw new CustomException(600, "挂载失败！错误信息为：" + e.getMessage());
            }
        } else if (CloudAppConstant.StorageType.NAS.equalsIgnoreCase(cloudBackupStorage.getStorageType())) {
            Map<String, String> backupStorageInfo = findBackupStorageInfo(cloudBackupStorage);
            cloudBackupStorage.setRequest(backupStorageInfo.get("request"));
            cloudBackupStorage.setCapacity(backupStorageInfo.get("capacity"));
            resMap.put("request", backupStorageInfo.get("request"));
            resMap.put("capacity", backupStorageInfo.get("capacity"));
        } else {
            throw new CustomException(600, "校验挂载错误，不支持当前备份类型：" + cloudBackupStorage.getStorageType());
        }
        return resMap;
    }

    private void checkBackupStoragePara(CloudBackupStorageVO cloudBackupStorage) {
        //校验参数
        if (ObjectUtils.isEmpty(cloudBackupStorage.getStorageType())) {
            throw new CustomException(600, "未填写存储资源类型！");
        }
        //校验认证信息是否填写
        if (CloudAppConstant.StorageType.S3.equalsIgnoreCase(cloudBackupStorage.getStorageType())) {
            if (ObjectUtils.isEmpty(cloudBackupStorage.getServer())) {
                throw new CustomException(600, "未填写server！");
            }
            if (ObjectUtils.isEmpty(cloudBackupStorage.getBucket())) {
                throw new CustomException(600, "未填写bucket！");
            }
            if (ObjectUtils.isEmpty(cloudBackupStorage.getAccessKey())) {
                throw new CustomException(600, "未填写accessKey！");
            }
            if (ObjectUtils.isEmpty(cloudBackupStorage.getSecretKey())) {
                throw new CustomException(600, "未填写secretKey！");
            }
        } else if (CloudAppConstant.StorageType.NAS.equalsIgnoreCase(cloudBackupStorage.getStorageType())) {
            if (ObjectUtils.isEmpty(cloudBackupStorage.getServer())) {
                throw new CustomException(600, "未填写存储资源IP！");
            }
            if (ObjectUtils.isEmpty(cloudBackupStorage.getMountPath())) {
                throw new CustomException(600, "未填写存储资源挂载路径！");
            }
        } else {
            throw new CustomException(600, "校验参数失败，不支持当前备份类型：" + cloudBackupStorage.getStorageType());
        }
    }

    @Override
    public void updateZookeeperCPUMemory(ResourceDTO patch) throws Exception {
        CloudApp app = cloudAppService.get(patch.getAppId());
        // 实时获取CR参数而不使用表中参数
        Zookeeper actCr = kubeClientService.get(app.getKubeId())
                .listCustomResource(Zookeeper.class, app.getCrName(), app.getNamespace());
        Zookeeper.ZookeeperStatus crStatus = actCr.getStatus();
        Zookeeper.ZookeeperSpec crStatusSpec = crStatus.getSpec();
        boolean doChange = false;
        if (patch.getMemory() != null && !crStatusSpec.getMemory().equals(patch.getMemory())) {
            doChange = true;
            RegexUtil.checkMemory(patch.getMemory());
            crStatusSpec.setMemory(patch.getMemory());
        }
        if (patch.getCpu() != null && !crStatusSpec.getCpu().equals(patch.getCpu())) {
            doChange = true;
            RegexUtil.checkCPU(patch.getCpu());
            crStatusSpec.setCpu(patch.getCpu());
        }
        if (!doChange) {
            log.info("未涉及CPU、Memory变更:{}", patch.toString());
            return;
        }
        Zookeeper zookeeper = new Zookeeper(app.getCrName(), app.getNamespace(), crStatusSpec);
        app.setCrRun(YamlEngine.marshal(zookeeper));
        cloudAppService.callScheduler(app, ActionEnum.UPDATE, null, ZookeeperWatch.class);
        kubeClientService.get(app.getKubeId()).updateCustomResource(zookeeper, Zookeeper.class);
    }

    /**
     * 删除缩容掉的实例存储，不适用mongo replicaset、es
     * @param app
     * @param releasedIps
     */
    @Override
    public void cleanStorageScaleInAfter(CloudApp app, List<String> releasedIps) {
        if (CollectionUtils.isEmpty(releasedIps)) {
            log.error("[cleanStorageScaleInAfter]缩容节点存储失败,crname:{},ips:{}", app.getName(), releasedIps);
            throw new CustomException(600, "缩容节点存储失败");
        }
        AppKind kind = AppKind.valueOf(app.getKind(), app.getArch());
        if (kind == AppKind.Elasticsearch || kind == AppKind.MongoDB) {
            log.error("[cleanStorageScaleInAfter] 不支持清理指定应用类型存储 {}", kind);
            return;
        }

        // 删除pvc
        ArrayList<PersistentVolume> pvs = new ArrayList<>();
        ArrayList<PersistentVolumeClaim> pvcs = new ArrayList<>();
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        this.buildVolumeItems(app, releasedIps, pvs, pvcs);

        if (!pvcs.isEmpty())
            kubeClient.deleteBatch(pvcs.get(0).getKind(), pvcs.toArray(new PersistentVolumeClaim[0]));
        if (!pvs.isEmpty())
            kubeClient.deleteBatch(pvs.get(0).getKind(), pvs.toArray(new PersistentVolume[0]));
    }


    public void buildVolumeItems(CloudApp vo, List<String> releaseIps, List<PersistentVolume> pvs, List<PersistentVolumeClaim> pvcs) {
        AppKind kind = AppKind.valueOf(vo.getKind(), vo.getArch());
        List<String> pvcNames = getPvcNames(kind, kind.getPvcPatterns(vo), releaseIps);
        if (CollectionUtils.isEmpty(pvcNames)){
            log.error("[cleanStorageScaleInAfter]查询缩容节点的存储失败,crname:{},ips:{}" ,vo.getName(), releaseIps);
            throw new CustomException(600,"查询缩容节点的存储失败");
        }
        pvcs.addAll(pvcNames.stream().map(pvcName -> kubeClientService.get(vo.getKubeId()).getPvc(vo.getNamespace(), pvcName)).filter(Objects::nonNull).collect(Collectors.toList()));

        for (PersistentVolumeClaim pvc : pvcs) {
            if (!pvc.getMetadata().getOwnerReferences().isEmpty()){
                break;
            }
            String volumeName = pvc.getSpec().getVolumeName();
            String storageClassName = pvc.getSpec().getStorageClassName();
            // 仅主动删除hostpath 类型pv
            if (StringUtils.isNoneEmpty(volumeName) && StringUtils.isEmpty(storageClassName)) {
               pvs.add(new PersistentVolumeBuilder().withNewMetadata().withName(volumeName).endMetadata().build());
            }
        }
    }

    private List<String> getPvcNames(AppKind kind,Map<String, Pattern> pvcPatterns, List<String> ips) {
        String ipPattern = "(-[0-9]{1,3}){4}";// ip 正则
        List<String> list = new ArrayList<>();
        for (String ip : ips) {
            ip = ip.replace(".","-");
            for (Map.Entry<String, Pattern> patternEntry : pvcPatterns.entrySet()) {
                Pattern pattern = patternEntry.getValue();
                String replaceIpPattern = pattern.pattern().replace(ipPattern, "-" + ip);
                Generex generex = new Generex(replaceIpPattern);
                List<String> allMatchedStrings = generex.getAllMatchedStrings();
                list.addAll(allMatchedStrings);
            }
        }
        return list;
    }

    /**
     * 获取数据存储资源列表
     *
     * @param pageDTO
     * @return
     */
    @Override
    public PageInfo<CloudDataStorage> listDataStorage(PageDTO pageDTO) {
        List<CloudDataStorage> results = cloudDataStorageMapper.listByMap(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_DATA_STORAGE, pageDTO.getCondition());
        return new PageInfo<>(results);
    }

    @Override
    public CloudBackupStorageVO listBackupStorage() {
        CloudBackupStorageVO cloudBackupStorageVO = listBackupStoragePublic();
        if (CloudAppConstant.StorageType.S3.equalsIgnoreCase(cloudBackupStorageVO.getStorageType())) {
            cloudBackupStorageVO.setSecretKey(AsymmetricEncryptionUtil.getEncryptInstance().decrypt(cloudBackupStorageVO.getSecretKey()));
        }
        return cloudBackupStorageVO;
    }

    public CloudBackupStorageVO listBackupStorageEncrypt() {
        CloudBackupStorageVO cloudBackupStorageVO = listBackupStoragePublic();
        return cloudBackupStorageVO;
    }

    public CloudBackupStorageVO listBackupStoragePublic() {
        List<CloudBackupStorage> results = cloudBackupStorageMapper.listByMap(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_STORAGE, new HashMap<>());
        List<CloudBackupStorageVO> res = results.stream().map(backupStorage -> {
            CloudBackupStorageVO cloudBackupStorageVO = new CloudBackupStorageVO();
            JSONObject storageConfig = JsonUtil.toObject(JSONObject.class, backupStorage.getStorageConfig());
            BeanUtils.copyProperties(backupStorage, cloudBackupStorageVO);
            //NAS查询总量与使用量
            if (CloudAppConstant.StorageType.NAS.equalsIgnoreCase(backupStorage.getStorageType())) {
//                Map<String, String> backupStorageInfo = findBackupStorageInfo(backupStorage);
//                cloudBackupStorageVO.setRequest(backupStorageInfo.get("request"));
//                cloudBackupStorageVO.setCapacity(backupStorageInfo.get("capacity"));
                cloudBackupStorageVO.setServer(storageConfig.getString("server"));
                cloudBackupStorageVO.setMountPath(storageConfig.getString("mountPath"));
            } else if (CloudAppConstant.StorageType.S3.equalsIgnoreCase(backupStorage.getStorageType())) {
                cloudBackupStorageVO.setServer(storageConfig.getString("server"));
                cloudBackupStorageVO.setRegion(storageConfig.getString("region"));
                cloudBackupStorageVO.setBucket(storageConfig.getString("bucket"));
                cloudBackupStorageVO.setAccessKey(storageConfig.getString("accessKey"));
                cloudBackupStorageVO.setSecretKey(storageConfig.getString("secretKey"));
                cloudBackupStorageVO.setBackupPath(storageConfig.getString("backupPath"));
            } else {
                throw new CustomException(600, "获取备份存储失败，不支持当前备份类型：" + backupStorage.getStorageType());
            }
            return cloudBackupStorageVO;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(res)) {
            return new CloudBackupStorageVO();
        } else {
            return res.get(0);
        }
    }

    /**
     * 添加数据存储资源
     *
     * @param cloudDataStorage
     */
    @Override
    public void insertDataStorage(CloudDataStorage cloudDataStorage) {
        //校验参数
        if (StringUtils.isBlank(cloudDataStorage.getScName())) {
            throw new CustomException(600, "未填写StorageClass名称！");
        }
        if (ObjectUtils.isEmpty(cloudDataStorage.getKubeId())) {
            throw new CustomException(600, "未选择集群！");
        }
        //校验sc名称，同一集群下不能存在同名sc
        HashMap<String, Object> condition = new HashMap<>();
        condition.put("scName", cloudDataStorage.getScName());
        condition.put("kubeId", cloudDataStorage.getKubeId());
        List<CloudDataStorage> cloudDataStorages = cloudDataStorageMapper.listByMap(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_DATA_STORAGE, condition);
        if (!CollectionUtils.isEmpty(cloudDataStorages)) {
            throw new CustomException(600, "已存在同名存储资源！");
        }
        //获取client
        KubeClient client = kubeClientService.get(cloudDataStorage.getKubeId());
        //获取配置中的scyaml，用来创建sc
        String scYaml = sysConfigService.findOne("storage.config", "localpathstorage.config");
        String newScYaml = scYaml.replace("${scName}", cloudDataStorage.getScName()).replace("${mountPath}", cloudDataStorage.getMountPath());
        client.applyYaml(newScYaml);
        //修改configmap的nodepathMap属性
        ConfigMap dataStorageCM = client.getConfigMap(dataStorageCMName, dataStorageCMNamespace);
        if (ObjectUtils.isEmpty(dataStorageCM)) {
            throw new CustomException(600, "未找到数据存储ConfigMap");
        }
        String config = dataStorageCM.getData().get("config.json");
        JSONObject configObj = JsonUtil.toObject(JSONObject.class, config);
        List<LinkedHashMap<String, Object>> nodePathMap = (List<LinkedHashMap<String, Object>>) configObj.get("nodePathMap");
        for (LinkedHashMap<String, Object> np : nodePathMap) {
            if ("DEFAULT_PATH_FOR_NON_LISTED_NODES".equalsIgnoreCase(String.valueOf(np.get("node")))) {
                String pathsStr = String.valueOf(np.get("paths"));
                String newPathStr = pathsStr.substring(0, pathsStr.length() - 1) + "," + cloudDataStorage.getMountPath() + "]";
                np.put("paths", newPathStr);
            }
        }
        configObj.put("nodePathMap", nodePathMap);
        dataStorageCM.getData().put("config.json", JsonUtil.toJson(configObj));
        client.applyConfigMap(dataStorageCMName, dataStorageCMNamespace, dataStorageCM.getData());
        //插入数据
        cloudDataStorage.setInsertTime(new Timestamp(System.currentTimeMillis()));
        cloudDataStorageMapper.insertDataStorage(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_DATA_STORAGE, cloudDataStorage);
    }

    /**
     * 添加备份存储资源
     *
     * @param cloudBackupStorage
     */
    @Override
    public void insertBackupStorage(CloudBackupStorageVO cloudBackupStorage) {
//        //校验参数
//        if (ObjectUtils.isEmpty(cloudBackupStorage.getStorageType())) {
//            throw new CustomException(600, "未填写存储资源类型！");
//        }
//        //校验认证信息是否填写
//        checkBackupStoragePara(cloudBackupStorage);
//        //校验是否已经存在同种类型的备份存储资源
//        HashMap<String, Object> conditionType = new HashMap<>();
//        conditionType.put("storageType", cloudBackupStorage.getStorageType());
//        List<CloudBackupStorage> checkTypeList = cloudBackupStorageMapper.listByMap(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_STORAGE, conditionType);
//        if (!CollectionUtils.isEmpty(checkTypeList)) {
//            throw new CustomException(600, "添加失败！已存在同类型存储资源");
//        }
//        //校验sc名称，全局不能存在同名sc
//        HashMap<String, Object> condition = new HashMap<>();
//        condition.put("name", cloudBackupStorage.getName());
//        List<CloudBackupStorage> cloudBackupStorages = cloudBackupStorageMapper.listByMap(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_STORAGE, condition);
//        if (!CollectionUtils.isEmpty(cloudBackupStorages)) {
//            throw new CustomException(600, "已存在同名存储资源！");
//        }
//        //将ip与mountPath放入config
//        JSONObject storageConfigObj = new JSONObject();
//        storageConfigObj.put("ip", cloudBackupStorage.getServer());
//        storageConfigObj.put("mountPath", cloudBackupStorage.getMountPath());
//        if (CloudAppConstant.StorageType.S3.equalsIgnoreCase(cloudBackupStorage.getStorageType())) {
//            //s3需要额外放入用户名密码
//            storageConfigObj.put("accessKey", cloudBackupStorage.getAccessKey());
//            storageConfigObj.put("secretKey", cloudBackupStorage.getSecretKey());
//        }
//        String storageConfigJson = JsonUtil.toJson(storageConfigObj);
//        cloudBackupStorage.setStorageConfig(storageConfigJson);
//        cloudBackupStorage.setInsertTime(new Timestamp(System.currentTimeMillis()));
//        cloudBackupStorageMapper.insertBackupStorage(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_STORAGE, cloudBackupStorage);
    }

    /**
     * 修改数据存储资源
     *
     * @param cloudDataStorage
     */
    @Override
    public void updateDataStorage(CloudDataStorage cloudDataStorage) {
        //校验是否存在id
        if (ObjectUtils.isEmpty(cloudDataStorage.getId())) {
            throw new CustomException(600, "未获取到存储资源ID！");
        }
        //修改
        cloudDataStorage.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        cloudDataStorageMapper.updateDataStorageById(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_DATA_STORAGE, cloudDataStorage);
    }

    /**
     * 修改备份存储资源
     *
     * @param cloudBackupStorage
     */
    @Override
    public void updateBackupStorage(CloudBackupStorageVO cloudBackupStorage) {
        // 1.修改cm
        //获取operator所在的namespace，由于所有应用的operator都处于同一个namespace，所以取其一
        String operatorConfig = sysConfigService.findOne("operator.name", "MySQL");
        String operatorNamespace = operatorConfig.split("/")[0];
        //configmap的yaml模板
        //步骤：1.先根据备份存储类型到啥意思从config表中获取对应cm  2.不会直接applyyaml，而是作为data添加到globalcm中，通过updateGlobalConfigMap方法
        String backupStorageConfigmapYaml = "";
        if (CloudAppConstant.StorageType.NAS.equalsIgnoreCase(cloudBackupStorage.getStorageType())) {
            backupStorageConfigmapYaml = sysConfigService.findOne("storage.config", "backupStorageConfigmapNAS");
        } else if (CloudAppConstant.StorageType.S3.equalsIgnoreCase(cloudBackupStorage.getStorageType())) {
            backupStorageConfigmapYaml = sysConfigService.findOne("storage.config", "backupStorageConfigmapS3");
        } else {
            throw new CustomException(600, "获取备份存储配置失败，不支持当前备份类型：" + cloudBackupStorage.getStorageType());
        }
        //获取所有可用的kubeId
        List<KubeConfig> kubeConfigList = kubeConfigService.list(Collections.singletonMap("state", 1));
        //遍历修改cm
        if (CloudAppConstant.StorageType.S3.equalsIgnoreCase(cloudBackupStorage.getStorageType())) {
            //S3
            //对密码解密
            String decryptMasterPassword = AsymmetricEncryptionUtil.getEncryptInstance().decrypt(cloudBackupStorage.getSecretKey());
            for (KubeConfig kubeConfig : kubeConfigList) {
                //获取kubeId
                String finBackupStorageConfigmapYaml = backupStorageConfigmapYaml
                        .replace("${namespace}", operatorNamespace)
                        .replace("${type}", "s3")
                        .replace("${server}", cloudBackupStorage.getServer())
                        .replace("${bucket}", cloudBackupStorage.getBucket())
                        .replace("${secretName}", "backupstorage-secret")
                        .replace("${backupPath}", "")
                        .replace("${region}", cloudBackupStorage.getRegion());
                //修改configmap
                updateGlobalConfigMap(finBackupStorageConfigmapYaml, kubeClientService.get(kubeConfig.getId()));
                //修改secret
                String backupStorageSecretYaml = sysConfigService.findOne("storage.config", "backupStorageSecret");
                String finBackupStorageSecretYaml = backupStorageSecretYaml
                        .replace("${namespace}", operatorNamespace)
                        .replace("${accessKey}", cloudBackupStorage.getAccessKey())
                        .replace("${secretKey}", decryptMasterPassword);
                kubeClientService.get(kubeConfig.getId()).applyYaml(finBackupStorageSecretYaml);
            }
            //修改
            //将ip与mountPath放入config
            JSONObject storageConfigObj = new JSONObject();
            storageConfigObj.put("server", cloudBackupStorage.getServer());
            storageConfigObj.put("region", cloudBackupStorage.getRegion());
            storageConfigObj.put("bucket", cloudBackupStorage.getBucket());
            storageConfigObj.put("accessKey", cloudBackupStorage.getAccessKey());
            storageConfigObj.put("secretKey", cloudBackupStorage.getSecretKey());
            storageConfigObj.put("backupPath", "");
            String storageConfigJson = JsonUtil.toJson(storageConfigObj);
            cloudBackupStorage.setStorageConfig(storageConfigJson);
        } else if (CloudAppConstant.StorageType.NAS.equalsIgnoreCase(cloudBackupStorage.getStorageType())) {
            //NAS
            //修改cm
            //configmap的yaml模板
            //获取所有可用的kubeId
            for (KubeConfig kubeConfig : kubeConfigList) {
                //获取kubeId
                String finBackupStorageConfigmapYaml = backupStorageConfigmapYaml
                        .replace("${namespace}", operatorNamespace)
                        .replace("${type}", "nfs")
                        .replace("${server}", cloudBackupStorage.getServer() + ":" + cloudBackupStorage.getMountPath());

                //创建configmap
                updateGlobalConfigMap(finBackupStorageConfigmapYaml, kubeClientService.get(kubeConfig.getId()));
            }
            //修改
            //将ip与mountPath放入config
            JSONObject storageConfigObj = new JSONObject();
            storageConfigObj.put("server", cloudBackupStorage.getServer());
            storageConfigObj.put("mountPath", cloudBackupStorage.getMountPath());
            String storageConfigJson = JsonUtil.toJson(storageConfigObj);
            cloudBackupStorage.setStorageConfig(storageConfigJson);
        } else {
            throw new CustomException(600, "修改备份存储失败，不支持当前备份类型：" + cloudBackupStorage.getStorageType());
        }
        //校验认证信息是否填写
        checkBackupStoragePara(cloudBackupStorage);
        //获取备份存储信息进行对比，查看是否需要更改cm
        Map<String, Object> condition = new HashMap<>();
        condition.put("id", cloudBackupStorage.getId());
        List<CloudBackupStorage> backupStorageList = cloudBackupStorageMapper.listByMap(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_STORAGE, condition);
        if (!CollectionUtils.isEmpty(backupStorageList)) {
            //修改
            cloudBackupStorage.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            cloudBackupStorageMapper.updateBackupStorageById(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_STORAGE, cloudBackupStorage);
        } else {
            //新增
            cloudBackupStorage.setName("");
            cloudBackupStorage.setStatus(0);
            cloudBackupStorage.setInsertTime(new Timestamp(System.currentTimeMillis()));
            cloudBackupStorageMapper.insertBackupStorage(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_STORAGE, cloudBackupStorage);
        }
    }

    private void updateGlobalConfigMap(String finBackupStorageConfigmapYaml, KubeClient kubeClient) {
        ConfigMap update = YamlEngine.unmarshal(finBackupStorageConfigmapYaml, ConfigMap.class);
        Map<String, String> data = update.getData();
        // todo 删除已有的配置项
        GlobalConfigMap.getInstance().updateByMerging(kubeClient, data);
    }

    /**
     * 删除数据存储资源
     *
     * @param id
     */
    @Override
    public void deleteDataStorage(Integer id) {
        //校验参数
        if (ObjectUtils.isEmpty(id)) {
            throw new CustomException(600, "未获取到需要删除的存储资源ID！");
        }
        //获取数据存储资源
        HashMap<String, Object> condition = new HashMap<>();
        condition.put("id", id);
        List<CloudDataStorage> cloudDataStorages = cloudDataStorageMapper.listByMap(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_DATA_STORAGE, condition);
        if (CollectionUtils.isEmpty(cloudDataStorages)) {
            throw new CustomException(600, "未获取到需要删除的存储资源！");
        }
        CloudDataStorage cloudDataStorage = cloudDataStorages.get(0);
        //获取client
        KubeClient client = kubeClientService.get(cloudDataStorage.getKubeId());
        //判断相关pv是否都已经被删除
        List<PersistentVolume> pvList = client.listPv(cloudDataStorage.getScName());
        if (!CollectionUtils.isEmpty(pvList)) {
            throw new CustomException(600, "存在存活PV，无法删除！");
        }
        //修改configmap的nodepathMap属性
        ConfigMap dataStorageCM = client.getConfigMap(dataStorageCMName, dataStorageCMNamespace);
        if (ObjectUtils.isEmpty(dataStorageCM)) {
            throw new CustomException(600, "未找到数据存储ConfigMap");
        }
        String config = dataStorageCM.getData().get("config.json");
        JSONObject configObj = JsonUtil.toObject(JSONObject.class, config);
        List<LinkedHashMap<String, Object>> nodePathMap = (List<LinkedHashMap<String, Object>>) configObj.get("nodePathMap");
        for (LinkedHashMap<String, Object> np : nodePathMap) {
            if ("DEFAULT_PATH_FOR_NON_LISTED_NODES".equalsIgnoreCase(String.valueOf(np.get("node")))) {
                String[] pathArr = String.valueOf(np.get("paths")).replace("[", "").replace("]", "").split(",");
                List<String> pathList = Arrays.asList(pathArr);
                pathList = pathList.stream().filter(path -> !path.equals(cloudDataStorage.getMountPath())).collect(Collectors.toList());
                String newPathStr = pathList.toString().replace("\"", "").replace(" ", "");
                np.put("paths", newPathStr);
            }
        }
        configObj.put("nodePathMap", nodePathMap);
        dataStorageCM.getData().put("config.json", JsonUtil.toJson(configObj));
        client.applyConfigMap(dataStorageCMName, dataStorageCMNamespace, dataStorageCM.getData());
        //删除sc
        client.deleteStorageClass(cloudDataStorage.getScName());
        cloudDataStorageMapper.deleteDataStorageById(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_DATA_STORAGE, id);
    }

    /**
     * 删除备份存储资源
     *
     * @param id
     */
    @Override
    public void deleteBackupStorage(Integer id) {
        //查询备份存储
        HashMap<String, Object> storageCondition = new HashMap<>();
        storageCondition.put("id", id);
        List<CloudBackupStorage> cloudBackupStorages = cloudBackupStorageMapper.listByMap(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_STORAGE, storageCondition);
        if (CollectionUtils.isEmpty(cloudBackupStorages)) {
            throw new CustomException(600, "未获取到需要删除的备份资源！");
        }
        CloudBackupStorage cloudBackupStorage = cloudBackupStorages.get(0);
        //查询应用列表中是否还存在引用当前备份存储的存活应用
        List<CloudApp> appList = cloudAppService.listAppByBackupStorage(cloudBackupStorage.getName());
        if (!CollectionUtils.isEmpty(appList)) {
            throw new CustomException(600, "当前备份存储资源存在引用的应用！");
        }
        //判断是否还有应用引用当前备份存储
        cloudBackupStorageMapper.deleteBackupStorageById(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_STORAGE, id);
    }

    @Override
    public List<String> getBackupStorageSupport() {
        String backupStorageSupport = sysConfigService.findOne("storage.config", "backupStorageSupport");
        String[] backupStorageSupportArr = backupStorageSupport.split(",");
        List<String> backupStorageSupportList = Arrays.asList(backupStorageSupportArr);
        return backupStorageSupportList;
    }

    @Override
    public List<CloudBackupStorage> listStorageConfigOnly() {
        return cloudBackupStorageMapper.listByMap(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_STORAGE, Collections.emptyMap());
    }

    /**
     * 检查是否已经mount了NAS服务器
     *
     * @param nasIp
     * @param nasPath
     * @return
     */
    public boolean checkIsMount(String nasIp, String nasPath) {
        String command = "df | grep -E \"" + nasIp + ":" + nasPath + ".*\\" + CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + "\"";
        ProcessBuilder processBuilder = new ProcessBuilder();
        String osName = System.getProperty("os.name");
        if (osName.contains("Windows")) {
            processBuilder.command("CMD", "/c", "dir *.txt");
        } else {
            processBuilder.command("sh", "-c", command);
        }
        try {
            Process process = processBuilder.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder output = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line);
            }

            int exitCode = process.waitFor();

            log.info("mount Exit Code: " + exitCode);
            log.info("mount Output:\n" + output.toString());

            if (0 != exitCode || StringUtils.isBlank(output.toString())) {
                return false;
            } else {
                return true;
            }
        } catch (Exception e) {
            throw new CustomException(600, "检查挂载状态失败！错误信息为：" + e.getMessage());
        }
    }

    /**
     * 挂载到NAS服务器
     *
     * @param nasIp
     * @param nasPath
     */
    public void mountToNas(String nasIp, String nasPath) {
        String command = "mount -t nfs " + nasIp + ":" + nasPath + " " + CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH;
        ProcessBuilder processBuilder = new ProcessBuilder();
        String osName = System.getProperty("os.name");
        if (osName.contains("Windows")) {
            processBuilder.command("CMD", "/c", "dir *.txt");
        } else {
            processBuilder.command("sh", "-c", command);
        }
        try {
            Process process = processBuilder.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder output = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line);
            }

            int exitCode = process.waitFor();

            log.info("mount Exit Code: " + exitCode);
            log.info("mount Output:\n" + output.toString());

            if (0 != exitCode) {
                throw new CustomException(600, "挂载到NAS服务器失败！错误信息为：" + output.toString());
            }
        } catch (Exception e) {
            throw new CustomException(600, "挂载到NAS服务器失败！错误信息为：" + e.getMessage());
        }
    }

    /**
     * 取消挂载到NAS服务器
     *
     * @param nasIp
     * @param nasPath
     */
    public void umountToNas(String nasIp, String nasPath) {
        String command = "umount -t nfs " + nasIp + ":" + nasPath + " " + CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH;
        ProcessBuilder processBuilder = new ProcessBuilder();
        String osName = System.getProperty("os.name");
        if (osName.contains("Windows")) {
            processBuilder.command("CMD", "/c", "dir *.txt");
        } else {
            processBuilder.command("sh", "-c", command);
        }
        try {
            Process process = processBuilder.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder output = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line);
            }

            int exitCode = process.waitFor();

            log.info("mount Exit Code: " + exitCode);
            log.info("mount Output:\n" + output.toString());
        } catch (Exception e) {
            throw new CustomException(600, "取消挂载到NAS服务器失败！错误信息为：" + e.getMessage());
        }
    }

    /**
     * 查询备份存储资源使用情况
     *
     * @param cloudBackupStorage
     * @return
     */
    public Map<String, String> findBackupStorageInfo(CloudBackupStorageVO cloudBackupStorage) {
        HashMap<String, String> backupStorageMap = new HashMap<>();
        if (CloudAppConstant.StorageType.NAS.equalsIgnoreCase(cloudBackupStorage.getStorageType())) {
            String nasIp = cloudBackupStorage.getServer();
            String nasPath = cloudBackupStorage.getMountPath();
            //查询是否已经挂载
            boolean isMount = checkIsMount(nasIp, nasPath);
            if (!isMount) {
                //挂载
                mountToNas(nasIp, nasPath);
            }
            //查询占用比例
            String command = "df " + CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + "  |grep " + CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + " | awk '{print $(NF-3)\",\"$(NF-4)}'";
            ProcessBuilder processBuilder = new ProcessBuilder();
            String osName = System.getProperty("os.name");
            if (osName.contains("Windows")) {
                processBuilder.command("CMD", "/c", "dir *.txt");
            } else {
                processBuilder.command("sh", "-c", command);
            }
            try {
                Process process = processBuilder.start();
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                StringBuilder output = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line);
                }

                int exitCode = process.waitFor();

                log.info("install Exit Code: " + exitCode);
                log.info("install Output:\n" + output.toString());

                String res = "";
                if (osName.contains("Windows")) {
                    res = "283312128,542095360";
                } else {
                    res = output.toString();
                }
                String[] usedAndSize = res.split(",");
                backupStorageMap.put("request", String.valueOf(Long.valueOf(usedAndSize[0]) * 1024));
                backupStorageMap.put("capacity", String.valueOf(Long.valueOf(usedAndSize[1]) * 1024));
            } catch (Exception e) {
                throw new CustomException(600, "获取存储已使用信息与资源总量信息失败！" + e.getMessage());
            }
//            umountToNas(nasIp, nasPath);
        }
        return backupStorageMap;
    }
}
