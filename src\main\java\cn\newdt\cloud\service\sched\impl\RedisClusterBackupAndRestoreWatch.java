package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.BackupHis;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.RestoreHis;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.mapper.BackupMapper;
import cn.newdt.cloud.mapper.RestoreMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.CloudAppService;
import cn.newdt.cloud.service.KubeClientService;
import cn.newdt.cloud.service.KubeConfigService;
import cn.newdt.cloud.service.RestoreServiceImpl;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.utils.BackupUtil;
import cn.newdt.cloud.utils.JsonUtil;
import cn.newdt.cloud.utils.KubeClientUtil;
import cn.newdt.cloud.utils.SSHUtil;
import cn.newdt.cloud.vo.CloudBackupStorageVO;
import cn.newdt.commons.bean.UserInfo;
import cn.newdt.commons.utils.UserUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shindata.redis.v1.RedisCluster;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.io.File;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.Map;

import static cn.newdt.cloud.constant.DatasourceConstant.CLOUD_BACKUP_HIS;
import static cn.newdt.cloud.constant.DatasourceConstant.SCHEMA;

@Slf4j
public class RedisClusterBackupAndRestoreWatch extends OpsProcessorContext implements OpsPostProcessor<RedisCluster> {
    @Value("${backupTimemout.redis:1440}")
    private String backupTimeout;

    @Autowired
    private CloudAppService cloudAppService;

    @Autowired
    private KubeClientService kubeClientService;

    @Autowired
    private SSHUtil sshUtil;

    @Autowired
    private BackupMapper backupMapper;

    @Autowired
    private RestoreMapper restoreMapper;

    @Autowired
    private KubeConfigService kubeConfigService;

    @Autowired
    private BackupUtil backupUtil;

    @Autowired
    private RestoreServiceImpl restoreServiceImpl;


    @Override
    public OpsResultDTO postProcess(TriggerHis triggerHis) throws Exception {
        // set async userinfo
        String userInfoStr = triggerHis.getJobDataMap().get("userInfo");
        UserInfo userInfo = JSONObject.parseObject(userInfoStr, UserInfo.class);
        UserUtil.setAsyncUserInfo(userInfo);
        //创建返回结果
        OpsResultDTO.Builder result = OpsResultDTO.builder().stopJob(false);  //.stopJob是是否停止任务的标识，TRUE为停止让
        //根据appId获取app对象
        Map<String, String> jobDataMap = triggerHis.getJobDataMap();
        String appId = jobDataMap.get("appId");
        CloudApp app = cloudAppService.get(Integer.valueOf(appId));
//        GoAgentClient agentClient = new GoAgentClient();
//        String agentServer = kubeConfigService.get(app.getKubeId()).getAgentServer();
        //修改crRun
        CloudApp cloudApp = new CloudApp();
        cloudApp.setCrRun(app.getCr());
        cloudApp.setId(app.getId());
        cloudAppService.update(cloudApp);
        //获取操作类型
        String triggerName = triggerHis.getTriggerName();
        String handType = triggerName.substring(0, triggerName.lastIndexOf("_"));
        //获取cr对象
        KubeClient kubeClient = kubeClientService.get(app.getKubeId());
        String namespace = app.getNamespace();
        RedisCluster cr = kubeClient.listCustomResource(RedisCluster.class, app.getCrName(), namespace);
        //当前时间
        Date nowDate = new Date();
        //获取超时时间
        Integer backupTimeOut = backupUtil.getBackupTimeOut(app.getId());
        if (ActionEnum.BACKUP.toString().equalsIgnoreCase(handType)){
            //备份的轮询操作
            //时间转换
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
            //获取所有需要参数
            String extDataStr = jobDataMap.get("extDataStr");
            JSONObject extData = JSON.parseObject(extDataStr);
            Integer backupHisId = extData.getInteger("backupHisId");
            String changeIdStr = extData.getString("changeId");
            Integer changeId = Integer.valueOf(changeIdStr);
            String backupStartDateSDF = extData.getString("backupStartDateSDF");
            String backupDirName = extData.getString("backupDirName");
            //20250604105531
            String backupFilename = extData.getString("backupFilename");
            String backupPodName = extData.getString("backupPodName");
            String backupFtpPath = extData.getString("backupFtpPath");
            Date backupStartDate = sdf.parse(backupStartDateSDF);
            //查询备份存储类型
            CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
            //查询备份历史
            BackupHis backupHis = backupMapper.getBackupHisById(SCHEMA, CLOUD_BACKUP_HIS, backupHisId);
            //记录备份信息：数据库名称、备份路径、备份文件名称
            JSONObject messageObj = JSONObject.parseObject(backupHis.getMessage());
            //查询备份日志，是否成功备份
            String[] backupPodNamArr = backupPodName.replace(" ", "").split(",");
            //遍历每一个pod，查询备份日志
            //是否备份成功
            Boolean isBackupSuc = null;
            //是否压缩成功
            Boolean isCompressSuccessed = null;
            //判断app还是否存在
            if(app.getDeleted()){
                //补全备份历史
                messageObj.put("msg","应用不存在！");
                backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "应用不存在！");
                result.stopJob(true).msg("应用不存在！").status(StatusConstant.FAIL);
                OpsResultDTO dto = result.build();
                cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                return dto;
            }
            //判断是否超时
            boolean isTimeout = backupUtil.checkBackupAndRestoreTimeout(backupStartDate, nowDate, backupTimeOut);
            if(isTimeout){
                //补全备份历史
                messageObj.put("msg","备份超时！");
                backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "redis备份超时！");
                result.stopJob(true).msg("备份超时！").status(StatusConstant.FAIL);
                OpsResultDTO dto = result.build();
                cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
                return dto;
            }else{
                log.info("[redis-cluster备份]：定时回调，备份中...");
                result.stopJob(false).msg("备份中...").status(StatusConstant.RUNNING);
            }
            AppKind kind = AppKind.Redis_Cluster;
            String containerName = kind.getContainerName();
            for(String podName : backupPodNamArr){
                //到每一个分片的pod中获取日志
                String redisBackupLog = kubeClient.execCmd(namespace, podName, containerName, "sh", "-c", "cat /data/tmp/backup.log");
                if(redisBackupLog.contains("Backup complete")){
                    //成功
                    isBackupSuc = true;
                } else if (redisBackupLog.contains("Failed")) {
                    //失败
                    isBackupSuc = false;
                    break;
                }
            }

            //判断是否成功备份，成功后进行压缩
            String containerBackupStorageRootDir = "/mnt/share";
            //到第一个分片的pod中获取压缩日志
            String compressPodName = backupPodNamArr[0];
            if(null != isBackupSuc && isBackupSuc) {
                String compressLogFilePath = "/data/tmp/" + backupFilename + "-compress.log";
                String isCompressLogExistResult = kubeClient.execCmd(namespace, compressPodName, containerName,
                        "sh", "-c", "test -f " + compressLogFilePath + " && echo \"File is exist\" || echo \"File is not exist\"");
                if (isCompressLogExistResult.contains("File is exist")) {
                    log.debug("[redis-cluster 备份]：Check Compress Result");
                    //文件存在，读取最后一行日志，判断是否包含 "Compress Success" 或 "Compress Failed" 来确定压缩状态。
                    String redisCompressLog = kubeClient.execCmd(namespace, compressPodName, containerName,
                            "sh", "-c", "tail -1 " + compressLogFilePath + " 2>/dev/null");
                    if(redisCompressLog.contains(backupFilename + " Compress Success")){
                        //成功
                        isCompressSuccessed = true;
                        log.debug("[redis-cluster 备份]：Check Compress Result: " + isCompressSuccessed);
                    } else if (redisCompressLog.contains(backupFilename + " Compress Failed")) {
                        //失败
                        isCompressSuccessed = false;
                        log.debug("[redis-cluster 备份]：Check Compress Result: " + isCompressSuccessed);
                    }
                } else {
                    //文件不存在，说明压缩尚未开始。进行压缩
                    String mountScript = backupUtil.buildMountScriptCMD(
                            cloudBackupStorageVO, containerBackupStorageRootDir, "bash", true);

                    //到第一个分片的pod中执行 mount 命令，然后把整体进行压缩
                    try {
                        //到第一个分片的pod中执行 mount 命令
                        log.debug("[redis cluster 备份] 备份压缩-进行 mount");
                        //由于脚本构建，不能使用sh，需要使用bash
                        kubeClient.execCmd(namespace, compressPodName, containerName,
                                "bash", "-c", mountScript);

                        //执行压缩
                        log.debug("[redis cluster 备份] 备份压缩-进行压缩");
                        String backupFileContextPath = kind.getBackupFilePath(
                                namespace, app.getName(), backupFilename);
                        String backupFileContextDir =
                                backupFileContextPath.substring(0, backupFileContextPath.lastIndexOf("/"));
                        String backupDir = containerBackupStorageRootDir + File.separator + backupFileContextDir;
                        //异步执行压缩，正常结束exit 0的话输出压缩成功，异常exit 1的话输出压缩失败
                        kubeClient.execCmdOneway(
                                namespace, compressPodName, containerName,
                                "bash", "-c", "touch " + compressLogFilePath + " && cd "
                                        + backupDir + " && tar -czf " + backupFilename + ".tar.gz" + " * > /dev/null 2>&1"
                                        + " && echo \""+ backupFilename + " Compress Success\" > " + compressLogFilePath
                                        + " || echo \"" + backupFilename + " Compress Failed\" > " + compressLogFilePath);

                    } catch (Exception e) {
                        isCompressSuccessed = false;
                        log.warn("[redis cluster 备份] 备份成功但压缩失败！", e);
                    }
                }

            }

            //判断是否成功进行了备份和压缩
            if(null != isBackupSuc && isBackupSuc && null != isCompressSuccessed && isCompressSuccessed) {
                //成功
                messageObj.put(CloudAppConstant.BACKUP_PATH_KEY, backupFtpPath);
                backupHis.setMessage(JsonUtil.toJson(messageObj));
                long dataSize = KubeClientUtil.dfUsage(kubeClient, compressPodName,
                        AppKind.Redis.getContainerName(), namespace, "/data"); // todo magic literal
                backupUtil.appendBackupHisMessage(backupHis,
                        "dataSize", Collections.singletonMap(backupHis.getPodName(), dataSize));
                backupUtil.backupReturn(backupHis, changeId, StatusConstant.SUCCESS, backupFilename, "redis备份成功！");
                result.stopJob(true).msg("备份成功！").status(StatusConstant.SUCCESS);
            } else if(null != isBackupSuc && isBackupSuc && null != isCompressSuccessed && !isCompressSuccessed){
                //备份成功压缩失败
                String compressFailedMsg = "压缩失败！";
                messageObj.put(CloudAppConstant.BACKUP_PATH_KEY, backupFtpPath);
                backupHis.setMessage(JsonUtil.toJson(messageObj));
                long dataSize = KubeClientUtil.dfUsage(kubeClient, compressPodName,
                        AppKind.Redis.getContainerName(), namespace, "/data"); // todo magic literal
                backupUtil.appendBackupHisMessage(backupHis,
                        "dataSize", Collections.singletonMap(backupHis.getPodName(), dataSize));
                backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL,
                        backupFilename, "redis备份成功！" + compressFailedMsg);
                result.stopJob(true).msg("备份成功！" + compressFailedMsg).status(StatusConstant.FAIL);
            } else if (null != isBackupSuc && !isBackupSuc) {
                //失败
                messageObj.put("msg","备份失败！");
                backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "redis备份失败！");
                result.stopJob(true).msg("备份失败！").status(StatusConstant.FAIL);
            }

            //存在压缩操作，最终进行卸载
            if (null != isCompressSuccessed) {
                String mountScript = backupUtil.buildMountScriptCMD(
                        cloudBackupStorageVO, containerBackupStorageRootDir, "bash", false);
                //到第一个分片的pod中执行 umount 命令
                log.debug("[redis cluster 备份] 备份压缩-进行 umount");
                kubeClient.execCmd(namespace, compressPodName, containerName,
                        "bash", "-c", mountScript);
            }
        }else if(ActionEnum.RESTORE.toString().equalsIgnoreCase(handType)){
            //恢复的轮询操作
            //时间转换
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
            //获取所有需要参数
            String extDataStr = jobDataMap.get("extDataStr");
            JSONObject extData = JSON.parseObject(extDataStr);
            Integer restoreHisId = extData.getInteger("restoreHisId");
            RestoreHis restoreHis = restoreServiceImpl.get(restoreHisId);
            String restoreStartDateSDF = extData.getString("restoreStartDateSDF");
            String resourceChangeIdStr = extData.getString("resourceChangeId");
            String crLastUpdateTime = extData.getString("crLastUpdateTime");
            Integer resourceChangeId = Integer.valueOf(resourceChangeIdStr);
            Date restoreStartDate = sdf.parse(restoreStartDateSDF);
            //判断cr属性RestorePhase是否为success
            String state = cr.getStatus().getState();
            log.info("[redis-cluster恢复] CR status.restore:{}", state);

            result.stopJob(false).msg("恢复中...").status(StatusConstant.RUNNING);

            //恢复尚未成功，判断是否超过五分钟
            boolean isTimeout = backupUtil.checkBackupAndRestoreTimeout(restoreStartDate, nowDate, backupTimeOut);
            if(isTimeout){
                //补全恢复历史
//                    updateRestoreHis(restoreHisId,restoreStartDate,nowDate,StatusConstant.FAIL);
                backupUtil.restoreReturn(restoreHis, resourceChangeId, "恢复超时！", StatusConstant.FAIL);
                result.stopJob(true).msg("恢复超时！").status(StatusConstant.FAIL);
            }
            if ("ready".equalsIgnoreCase(state) && !crLastUpdateTime.equalsIgnoreCase(cr.getStatus().getLastUpdateTime())) {
                //恢复成功
                //补全恢复历史
                backupUtil.restoreReturn(restoreHis, resourceChangeId, "恢复成功！", StatusConstant.SUCCESS);
                result.stopJob(true).msg("恢复成功！").status(StatusConstant.SUCCESS);
            }
        }
        OpsResultDTO dto = result.build();
        if (dto.getStopJob()){
            cloudAppService.handleWatchResult(app.getId(), StatusConstant.SUCCESS.equals(dto.getStatus()));
        }
        return dto;
    }

    /**
     * 补全备份历史
     * @param backupHisId
     * @param backupEndDate
     * @param backupStartDate
     * @param lastBackupFileName
     */
    public void updateBackupHis(Integer backupHisId, Date backupStartDate, Date backupEndDate,
                                String lastBackupFileName,String isSuccess){
        BackupHis backupHis = new BackupHis();
         backupHis.setBackupHisId(backupHisId);
        //用时
        long interval = (backupEndDate.getTime() - backupStartDate.getTime())/1000;
        String durationStr = String.valueOf(interval);
        int duration = Integer.parseInt(durationStr);
        //备份状态
        backupHis.setStatus(isSuccess);
        //备份结束时间
        backupHis.setEndTime(new Timestamp(System.currentTimeMillis()));
        //备份用时
        backupHis.setDuration(duration);
        //备份文件名称
        if(!"".equalsIgnoreCase(lastBackupFileName)){
            backupHis.setFileName(lastBackupFileName);
        }
        backupMapper.updateBackupHis(DatasourceConstant.SCHEMA, DatasourceConstant.CLOUD_BACKUP_HIS, backupHis);
    }

}