package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.StatusConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.ResourceChangeHis;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.DamengService;
import cn.newdt.cloud.utils.JsonUtil;
import com.shindata.cloud.dameng.v1.Dameng;
import com.shindata.common.spec.Entries;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.api.model.apps.StatefulSet;

import java.util.*;

import static cn.newdt.cloud.constant.ScheduleConstant.JOB_DATA_KEY_CHANGE_ID;

public class DamengMigrateWatch extends DamengResourceWatch {

    public static final String MIGRATE_PODNAME = "thePod";
    public static final String NODE_NAME = "nodeName";
    public static final String CR = "cr";

    @Override
    public OpsResultDTO postProcess(TriggerHis triggerHis) throws Exception {
        OpsResultDTO.Builder resultBuilder = OpsResultDTO.builder();
        ResourceChangeHis his = resourceChangeHisService.get(Integer.parseInt(triggerHis.getJobDataMap().get(JOB_DATA_KEY_CHANGE_ID)));
        Map<String, Object> mutableData = his.mutableDataMap();
        Map<String, String> jobData = triggerHis.returnMergedJobDataMap();
        CloudApp app = getApp(triggerHis);

        // prepare: delete pvc -> mutate statefulset affinity -> wait operator recreate instance
        // post:  update cr affinity after migrating finish(all instance will recreate) -> wait cr ready again
        ConditionCheckerRunner runner = new ConditionCheckerRunner();
        KubeClient client = kubeClientService.get(app.getKubeId());
        runner.addChecker(prepareMigrate(app, mutableData, jobData, client));
        runner.addChecker(watchMigrate(app, mutableData, jobData, client));
        runner.addChecker(postMigrate(triggerHis, app, mutableData, jobData, client));

        OpsResultDTO opsResultDTO = runner.runChecks();
        his.setDataMap(JsonUtil.toJson(mutableData));
        resourceChangeHisService.update(his);

        resultBuilder.withResult(opsResultDTO);
        if (StatusConstant.SUCCESS.equals(opsResultDTO.getStatus())) {
            // origin pod name
            String podName = jobData.get(MIGRATE_PODNAME);
            // target node name
            String targetNodeName = jobData.get(NODE_NAME);
            resultBuilder.msg(String.format("migrate %s to %s", podName, targetNodeName));
            resultBuilder.stopJob(true);
        }
        if (resultBuilder.isStopped())
            appService.handleWatchResult(app.getId(), resultBuilder.isSuccessful());

        return resultBuilder.build();
    }

    private ConditionChecker[] postMigrate(TriggerHis triggerHis, CloudApp app, Map<String, Object> mutableData, Map<String, String> jobData, KubeClient client) {
        return new ConditionChecker[]{
                new ConditionChecker("post migrate instance", () -> {
                    // possible value of cr: true,<generation>,empty
                    if (Boolean.TRUE.equals(mutableData.get(CR)))
                        return true;

                    Dameng dameng = client.listCustomResource(Dameng.class, app.getCrName(), app.getNamespace());
                    mutableData.put(CR, dameng.getMetadata().getGeneration());
                    if (mutableData.containsKey(CR)) {
                        // generation: we may update cr during watch, check cr status if generation match
                        Long generation = (Long) mutableData.get(CR);
                        if (generation.compareTo(dameng.getMetadata().getGeneration()) <= 0) {
                            try {
                                OpsResultDTO.Builder opsResult = super.evalOpsResult(triggerHis, dameng, app, client);
                                if (opsResult.isSuccessful()) {
                                    mutableData.put(CR, true);
                                    return true;
                                }
                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        } else
                            return false; // un-reach
                    } else {
                        // update cr schedule
                        Entries databaseEntry = dameng.getSpec().getEntries().stream()
                                .filter(entry -> entry.getName().equals(DamengService.Name.ENTRY_DATABASE)).findFirst().get();
//                            todo 迁移会影响调度策略; kubeSchedulerService.queryById(app.getKubeSchedulerId()).toK8sAffinity(app)
                        // target node name
                        String targetNodeName = jobData.get(NODE_NAME);

                        // merge node affinity from current spec with target node

                        // check if need merge
                        Affinity affinity = databaseEntry.getPodTemplate().getSpec().getAffinity();
                        if (affinity == null) {
                            mutableData.put(CR, dameng.getMetadata().getGeneration());
                            return false;
                        }
                        NodeAffinity nodeAffinity = affinity.getNodeAffinity();
                        if (nodeAffinity == null) {
                            mutableData.put(CR, dameng.getMetadata().getGeneration());
                            return false;
                        }
                        List<String> currentNodes = Optional.ofNullable(nodeAffinity.getRequiredDuringSchedulingIgnoredDuringExecution())
                                .map(ne -> ne.getNodeSelectorTerms())
                                .flatMap(nst -> nst.stream().findFirst())
                                .map(nst -> nst.getMatchExpressions())
                                .flatMap(me -> me.stream().findFirst())
                                .map(nsr -> nsr.getValues()).orElse(new ArrayList<>());
                        if (currentNodes.isEmpty() || new HashSet<>(currentNodes).contains(targetNodeName)){
                            mutableData.put(CR, dameng.getMetadata().getGeneration());
                            return false;
                        }

                        // need merge
                        currentNodes.add(targetNodeName);
                        nodeAffinity.setRequiredDuringSchedulingIgnoredDuringExecution(
                                new NodeSelectorBuilder()
                                        .withNodeSelectorTerms(
                                                new NodeSelectorTermBuilder()
                                                        .addNewMatchExpression()
                                                        .withKey("kubernetes.io/hostname")
                                                        .withOperator("In")
                                                        .withValues(currentNodes)
                                                        .endMatchExpression()
                                                        .build()
                                        ).build()
                        );
                        affinity.setNodeAffinity(nodeAffinity);
                        databaseEntry.getPodTemplate().getSpec().setAffinity(affinity);
                        Long newGeneration = client.updateCustomResource(dameng, Dameng.class).getMetadata().getGeneration();
                        mutableData.put(CR, newGeneration);
                        app.setCrRun(JsonUtil.toJson(dameng));
                        appService.update(app);
                    }
                    return false;
                })
        };
    }

    private ConditionChecker[] watchMigrate(CloudApp app, Map<String, Object> mutableData, Map<String, String> jobData, KubeClient client) {
        // origin pod name
        String podName = jobData.get(MIGRATE_PODNAME);
        String ssetName = podName.replaceAll("-\\d+$", "");
        return new ConditionChecker[]{
                new ConditionChecker("watch migrate instance", () -> {
                    if (Boolean.TRUE.equals(mutableData.getOrDefault("sts", Boolean.FALSE)))
                        return true;
                    Optional.ofNullable(mutableData.get("sts")).ifPresent(generation -> {
                        StatefulSet statefulSet = client.getStatefulSet(ssetName, app.getNamespace());
                        if (statefulSet.getStatus().getObservedGeneration().compareTo(Long.parseLong(generation + "")) >= 0
                                && Objects.equals(statefulSet.getStatus().getReadyReplicas(), statefulSet.getSpec().getReplicas())
                                && Objects.equals(statefulSet.getStatus().getCurrentRevision(), statefulSet.getStatus().getUpdateRevision())
                        ) {
                            mutableData.put("sts", true);
                        }
                    });
                    return false;
                }, false)
        };
    }

    private ConditionChecker[] prepareMigrate(CloudApp app,
                                              Map<String, Object> mutableData,
                                              Map<String, String> jobData,
                                              KubeClient client) {

        // origin pod name
        String podName = jobData.get(MIGRATE_PODNAME);
        // target node name
        String targetNodeName = jobData.get(NODE_NAME);
        return new ConditionChecker[]{
                new ConditionChecker("prepare migrate instance - check primary", () -> {
                    if (Boolean.TRUE.equals(mutableData.get("primary"))) // what if switch failed
                        return true;
                    List<PodDTO> pods = client.listPod(app.getNamespace(), AppKind.Dameng.labelOfPod(app));
                    Optional<PodDTO> any = pods.stream().filter(podDTO -> podDTO.getPodName().equals(podName)
                                    && podDTO.getLabel(CloudAppConstant.CustomLabels.ROLE).equals(CloudAppConstant.ROLE_PRIMARY))
                            .findAny();
                    if (any.isPresent()) {
                        try {
                            PodDTO monitorPod = DamengService.Util.getMonitor(pods);
                            String cmd = switchOver(monitorPod.getPodIp());
                            client.execCmd(app.getNamespace(), monitorPod.getPodName(), "dm", "sh", "-c", cmd);
                            mutableData.put("primary", true);
                            return true;
                        } catch (Exception e) {
                            mutableData.put("primary", false);
                            return false;
                        }
                    }
                    return true;
                }, false),
                new ConditionChecker("prepare migrate instance - remove  pvc", () -> {
                    if (mutableData.containsKey("pvc") && Boolean.TRUE.equals(mutableData.get("pvc")))
                        return true;
                    client.deletePvc(app.getNamespace(), "dm-data-" + podName);
                    mutableData.put("pvc", true);
                    return true;
                }, false),
                new ConditionChecker("prepare migrate instance", () -> {
                    if (mutableData.containsKey("sts"))
                        return true;
                    String ssetName = podName.replaceAll("-\\d+$", "");
                    StatefulSet statefulSet = client.getStatefulSet(ssetName, app.getNamespace());
                    statefulSet.getSpec().getTemplate().getSpec().setAffinity(
                            new AffinityBuilder()
                                    .withNewNodeAffinity()
                                    .withNewRequiredDuringSchedulingIgnoredDuringExecution()
                                    .withNodeSelectorTerms(new NodeSelectorTermBuilder()
                                            .addNewMatchExpression()
                                            .withKey("kubernetes.io/hostname")
                                            .withOperator("In")
                                            .withValues(targetNodeName)
                                            .endMatchExpression()
                                            .build())
                                    .endRequiredDuringSchedulingIgnoredDuringExecution()
                                    .endNodeAffinity().build()
                    );
                    StatefulSet patch = client.patchStatefulSet(statefulSet);
                    mutableData.put("sts", patch.getMetadata().getGeneration());
                    return true;
                }, false)
        };
    }

    private static String switchOver(String monitorIP) {
        return  "/scripts/switchover.sh --monitorip " + monitorIP + " --switchmode switchover ";
    }
}
