package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.MongoDBPodService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class MongoDBPodServiceImpl implements MongoDBPodService {
    @Override
    public List<Map<String, String>> getMongoDbStatusMembers(
            KubeClient kubeClient, String namespace, String crName, String podName, AppKind appKind) {
        if (StringUtils.isAnyEmpty(namespace, crName)) {
            return new ArrayList<>();
        }

        List<Map<String, String>> maps;
        //获取Status中的members信息
        String cmd = "mongo -u " + CloudAppConstant.UsernameAndPassword.mongoDBUsername
                + " -p " + CloudAppConstant.UsernameAndPassword.mongoDBPassword
                + (AppKind.MongoDB_Cluster == appKind ? " --port ${MONGO_PORT}" : "")
                + " --authenticationDatabase admin --quiet --eval 'JSON.stringify(rs.status().members)'";
        try {
            String result = kubeClient.execCmd(namespace, podName, appKind.getContainerName(), "sh", "-c", cmd);
            maps = JSONArray.parseObject(result, new TypeReference<List<Map<String, String>>>() {
            });
        } catch (Exception e) {
            log.error("getMongoDbRoleMap podName:{},error:{}", podName, e);
            return new ArrayList<>();
        }
        return maps;
    }
}
