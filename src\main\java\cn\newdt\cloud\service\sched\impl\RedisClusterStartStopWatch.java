package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.impl.RedisClusterService;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import com.shindata.redis.v1.RedisCluster;
import org.springframework.beans.factory.annotation.Autowired;

public class RedisClusterStartStopWatch implements OpsPostProcessor {
    @Autowired
    private RedisClusterService redisService;

    @Autowired
    private StartStopWatchHelper startStopWatchHelper;
    @Override
    public OpsResultDTO postProcess(TriggerHis triggerHis) throws Exception {
        return startStopWatchHelper.doStopWatchStartStop(triggerHis, RedisCluster.class, redisService,
                cr -> cr.getSpec().getMaintenance() == Boolean.TRUE,
                cr -> RedisWatch.State.valueOf(cr.getStatus().getState()) == RedisWatch.State.maintaining,
                (cr, flag) -> cr.getSpec().setMaintenance(flag),
                i -> CloudAppConstant.ROLE_PRIMARY.equals(i.getRole()), i -> true);
    }
}
