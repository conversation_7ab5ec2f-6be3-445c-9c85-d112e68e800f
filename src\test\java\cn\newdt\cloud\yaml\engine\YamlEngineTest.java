package cn.newdt.cloud.yaml.engine;

import com.pingcap.v1alpha1.TidbCluster;
import io.fabric8.kubernetes.api.model.IntOrString;
import io.fabric8.kubernetes.api.model.Volume;
import io.fabric8.kubernetes.client.utils.Serialization;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class YamlEngineTest {

    @Test
    public void marshal() {
        String yaml = "---\n" +
                "apiVersion: \"pingcap.com/v1alpha1\"\n" +
                "kind: \"TidbCluster\"\n" +
                "metadata:\n" +
                "  labels:\n" +
                "    app.kubernetes.io/managed-by: \"tidb-operator\"\n" +
                "    app.kubernetes.io/name: \"tidb-cluster\"\n" +
                "    app.kubernetes.io/instance: \"til\"\n" +
                "    app.kubernetes.io/version: \"1\"\n" +
                "  name: \"til\"\n" +
                "  namespace: \"yantestr\"\n" +
                "spec:\n" +
                "  affinity:\n" +
                "    nodeAffinity:\n" +
                "      requiredDuringSchedulingIgnoredDuringExecution:\n" +
                "        nodeSelectorTerms:\n" +
                "        - matchExpressions:\n" +
                "          - key: \"kubernetes.io/hostname\"\n" +
                "            operator: \"In\"\n" +
                "            values:\n" +
                "            - \"k8snode57\"\n" +
                "            - \"k8snode55\"\n" +
                "            - \"k8snode72\"\n" +
                "  configUpdateStrategy: \"RollingUpdate\"\n" +
                "  discovery: {}\n" +
                "  enableDynamicConfiguration: true\n" +
                "  enablePVReclaim: true\n" +
                "  helper:\n" +
                "    image: \"harbor.shindata.com/pingcap/alpine:3.16.0\"\n" +
                "    imagePullPolicy: \"IfNotPresent\"\n" +
                "  imagePullPolicy: \"IfNotPresent\"\n" +
                "  pd:\n" +
                "    additionalContainers:\n" +
                "    - args:\n" +
                "      - \"-c\"\n" +
                "      - \"/etc/filebeat/filebeat.yml\"\n" +
                "      command:\n" +
                "      - \"filebeat\"\n" +
                "      env:\n" +
                "      - name: \"APP_NAMESPACE\"\n" +
                "        valueFrom:\n" +
                "          fieldRef:\n" +
                "            apiVersion: \"v1\"\n" +
                "            fieldPath: \"metadata.namespace\"\n" +
                "      - name: \"POD_NAME\"\n" +
                "        valueFrom:\n" +
                "          fieldRef:\n" +
                "            apiVersion: \"v1\"\n" +
                "            fieldPath: \"metadata.name\"\n" +
                "      - name: \"POD_IP\"\n" +
                "        valueFrom:\n" +
                "          fieldRef:\n" +
                "            apiVersion: \"v1\"\n" +
                "            fieldPath: \"status.podIP\"\n" +
                "      - name: \"COMPONENT\"\n" +
                "        value: \"PD\"\n" +
                "      - name: \"CR_NAME\"\n" +
                "        value: \"til\"\n" +
                "      image: \"harbor.shindata.com/beats/filebeat:7.14.0\"\n" +
                "      imagePullPolicy: \"IfNotPresent\"\n" +
                "      name: \"filebeat\"\n" +
                "      resources: {}\n" +
                "      securityContext:\n" +
                "        runAsUser: 0\n" +
                "      volumeMounts:\n" +
                "      - mountPath: \"/etc/filebeat/\"\n" +
                "        name: \"tidb-filebeat-volume\"\n" +
                "        readOnly: true\n" +
                "      - mountPath: \"/var/log/shindb/tidb/\"\n" +
                "        name: \"tidb-log-volume\"\n" +
                "        readOnly: true\n" +
                "    additionalVolumeMounts:\n" +
                "    - mountPath: \"/var/log/shindb/tidb/\"\n" +
                "      name: \"tidb-log-volume\"\n" +
                "    additionalVolumes:\n" +
                "    - configMap:\n" +
                "        defaultMode: 416\n" +
                "        name: \"tidb-filebeat-config\"\n" +
                "      name: \"tidb-filebeat-volume\"\n" +
                "    - emptyDir: {}\n" +
                "      name: \"tidb-log-volume\"\n" +
                "    baseImage: \"harbor.shindata.com/pingcap/pd:v7.5.1\"\n" +
                "    config: \"[log]\\n  [log.file]\\n    filename = \\\"/var/log/shindb/tidb/tidb-server.log\\\"\\\n" +
                "      \\n    max-days = 1\\n    max-size = 64\\n\"\n" +
                "    labels:\n" +
                "      app.kubernetes.io/instance: \"til\"\n" +
                "      app.kubernetes.io/managed-by: \"tidb-operator\"\n" +
                "      app.kubernetes.io/name: \"tidb-cluster\"\n" +
                "    limits:\n" +
                "      cpu: \"2\"\n" +
                "      memory: \"3Gi\"\n" +
                "    maxFailoverCount: 3\n" +
                "    replicas: 1\n" +
                "    requests:\n" +
                "      storage: \"6Gi\"\n" +
                "    startTimeout: 30\n" +
                "    storageClassName: \"topolvm-provisioner\"\n" +
                "  podManagementPolicy: \"Parallel\"\n" +
                "  pvReclaimPolicy: \"Delete\"\n" +
                "  tidb:\n" +
                "    additionalContainers:\n" +
                "    - args:\n" +
                "      - \"-c\"\n" +
                "      - \"/etc/filebeat/filebeat.yml\"\n" +
                "      command:\n" +
                "      - \"filebeat\"\n" +
                "      env:\n" +
                "      - name: \"APP_NAMESPACE\"\n" +
                "        valueFrom:\n" +
                "          fieldRef:\n" +
                "            apiVersion: \"v1\"\n" +
                "            fieldPath: \"metadata.namespace\"\n" +
                "      - name: \"POD_NAME\"\n" +
                "        valueFrom:\n" +
                "          fieldRef:\n" +
                "            apiVersion: \"v1\"\n" +
                "            fieldPath: \"metadata.name\"\n" +
                "      - name: \"POD_IP\"\n" +
                "        valueFrom:\n" +
                "          fieldRef:\n" +
                "            apiVersion: \"v1\"\n" +
                "            fieldPath: \"status.podIP\"\n" +
                "      - name: \"COMPONENT\"\n" +
                "        value: \"TIDB\"\n" +
                "      - name: \"CR_NAME\"\n" +
                "        value: \"til\"\n" +
                "      image: \"harbor.shindata.com/beats/filebeat:7.14.0\"\n" +
                "      imagePullPolicy: \"IfNotPresent\"\n" +
                "      name: \"filebeat\"\n" +
                "      resources: {}\n" +
                "      securityContext:\n" +
                "        runAsUser: 0\n" +
                "      volumeMounts:\n" +
                "      - mountPath: \"/etc/filebeat/\"\n" +
                "        name: \"tidb-filebeat-volume\"\n" +
                "        readOnly: true\n" +
                "      - mountPath: \"/var/log/shindb/tidb/\"\n" +
                "        name: \"tidb-log-volume\"\n" +
                "        readOnly: true\n" +
                "      - mountPath: \"/var/log/tidb\"\n" +
                "        name: \"slowlog\"\n" +
                "    additionalVolumeMounts:\n" +
                "    - mountPath: \"/var/log/shindb/tidb/\"\n" +
                "      name: \"tidb-log-volume\"\n" +
                "    additionalVolumes:\n" +
                "    - configMap:\n" +
                "        defaultMode: 416\n" +
                "        name: \"tidb-filebeat-config\"\n" +
                "      name: \"tidb-filebeat-volume\"\n" +
                "    - emptyDir: {}\n" +
                "      name: \"tidb-log-volume\"\n" +
                "    baseImage: \"harbor.shindata.com/pingcap/tidb:v7.5.1\"\n" +
                "    config: \"[log]\\n  [log.file]\\n    filename = \\\"/var/log/shindb/tidb/tidb-server.log\\\"\\\n" +
                "      \\n    max-backups = 3\\n    max-days = 1\\n    max-size = 64\\n\"\n" +
                "    labels:\n" +
                "      app.kubernetes.io/instance: \"til\"\n" +
                "      app.kubernetes.io/managed-by: \"tidb-operator\"\n" +
                "      app.kubernetes.io/name: \"tidb-cluster\"\n" +
                "    limits:\n" +
                "      cpu: \"2\"\n" +
                "      memory: \"3Gi\"\n" +
                "    maxFailoverCount: 3\n" +
                "    replicas: 1\n" +
                "    requests:\n" +
                "      cpu: \"1\"\n" +
                "      memory: \"2Gi\"\n" +
                "    service:\n" +
                "      type: \"ClusterIP\"\n" +
                "  tikv:\n" +
                "    additionalContainers:\n" +
                "    - args:\n" +
                "      - \"-c\"\n" +
                "      - \"/etc/filebeat/filebeat.yml\"\n" +
                "      command:\n" +
                "      - \"filebeat\"\n" +
                "      env:\n" +
                "      - name: \"APP_NAMESPACE\"\n" +
                "        valueFrom:\n" +
                "          fieldRef:\n" +
                "            apiVersion: \"v1\"\n" +
                "            fieldPath: \"metadata.namespace\"\n" +
                "      - name: \"POD_NAME\"\n" +
                "        valueFrom:\n" +
                "          fieldRef:\n" +
                "            apiVersion: \"v1\"\n" +
                "            fieldPath: \"metadata.name\"\n" +
                "      - name: \"POD_IP\"\n" +
                "        valueFrom:\n" +
                "          fieldRef:\n" +
                "            apiVersion: \"v1\"\n" +
                "            fieldPath: \"status.podIP\"\n" +
                "      - name: \"COMPONENT\"\n" +
                "        value: \"TIKV\"\n" +
                "      - name: \"CR_NAME\"\n" +
                "        value: \"til\"\n" +
                "      image: \"harbor.shindata.com/beats/filebeat:7.14.0\"\n" +
                "      imagePullPolicy: \"IfNotPresent\"\n" +
                "      name: \"filebeat\"\n" +
                "      resources: {}\n" +
                "      securityContext:\n" +
                "        runAsUser: 0\n" +
                "      volumeMounts:\n" +
                "      - mountPath: \"/etc/filebeat/\"\n" +
                "        name: \"tidb-filebeat-volume\"\n" +
                "        readOnly: true\n" +
                "      - mountPath: \"/var/log/shindb/tidb/\"\n" +
                "        name: \"tidb-log-volume\"\n" +
                "        readOnly: true\n" +
                "    - args:\n" +
                "      - \"tail -f /dev/null\"\n" +
                "      command:\n" +
                "      - \"sh\"\n" +
                "      - \"-c\"\n" +
                "      env:\n" +
                "      - name: \"APP_TYPE\"\n" +
                "        value: \"TiDB\"\n" +
                "      - name: \"ARCH_TYPE\"\n" +
                "        value: \"Cluster\"\n" +
                "      - name: \"APP_NAMESPACE\"\n" +
                "        valueFrom:\n" +
                "          fieldRef:\n" +
                "            apiVersion: \"v1\"\n" +
                "            fieldPath: \"metadata.namespace\"\n" +
                "      - name: \"APP_NAME\"\n" +
                "        value: \"til\"\n" +
                "      image: \"harbor.shindata.com/pingcap/br:v7.5.1\"\n" +
                "      imagePullPolicy: \"IfNotPresent\"\n" +
                "      name: \"br-backup\"\n" +
                "      resources: {}\n" +
                "      securityContext:\n" +
                "        privileged: true\n" +
                "        runAsUser: 0\n" +
                "      volumeMounts:\n" +
                "      - mountPath: \"/var/lib/tikv\"\n" +
                "        name: \"tikv\"\n" +
                "      - mountPath: \"/mnt\"\n" +
                "        mountPropagation: \"Bidirectional\"\n" +
                "        name: \"backup-mnt-shared\"\n" +
                "      - mountPath: \"/scripts\"\n" +
                "        name: \"tidb--script-volume-name\"\n" +
                "        readOnly: true\n" +
                "    - args:\n" +
                "      - \"tail -f /dev/null\"\n" +
                "      command:\n" +
                "      - \"sh\"\n" +
                "      - \"-c\"\n" +
                "      image: \"harbor.shindata.com/beats/s3fs:1.94\"\n" +
                "      imagePullPolicy: \"IfNotPresent\"\n" +
                "      name: \"s3fs-container\"\n" +
                "      resources: {}\n" +
                "      securityContext:\n" +
                "        privileged: true\n" +
                "        runAsUser: 0\n" +
                "      volumeMounts:\n" +
                "      - mountPath: \"/mnt\"\n" +
                "        mountPropagation: \"Bidirectional\"\n" +
                "        name: \"backup-mnt-shared\"\n" +
                "      - mountPath: \"/scripts\"\n" +
                "        name: \"tidb--script-volume-name\"\n" +
                "        readOnly: true\n" +
                "    additionalVolumeMounts:\n" +
                "    - mountPath: \"/var/log/shindb/tidb/\"\n" +
                "      name: \"tidb-log-volume\"\n" +
                "    additionalVolumes:\n" +
                "    - configMap:\n" +
                "        defaultMode: 416\n" +
                "        name: \"tidb-filebeat-config\"\n" +
                "      name: \"tidb-filebeat-volume\"\n" +
                "    - emptyDir: {}\n" +
                "      name: \"tidb-log-volume\"\n" +
                "    - emptyDir: {}\n" +
                "      name: \"backup-mnt-shared\"\n" +
                "    - configMap:\n" +
                "        defaultMode: 416\n" +
                "        name: \"tidb-scripts-config\"\n" +
                "      name: \"tidb--script-volume-name\"\n" +
                "    baseImage: \"harbor.shindata.com/pingcap/tikv:v7.5.1\"\n" +
                "    config: \"log-level = \\\"info\\\"\\n\\n[log]\\n  [log.file]\\n    filename = \\\"/var/log/shindb/tidb/tidb-server.log\\\"\\\n" +
                "      \\n    max-days = 1\\n    max-size = 64\\n\\n[storage]\\n  reserve-space = \\\"0MB\\\"\\\n" +
                "      \\n\"\n" +
                "    labels:\n" +
                "      app.kubernetes.io/instance: \"til\"\n" +
                "      app.kubernetes.io/managed-by: \"tidb-operator\"\n" +
                "      app.kubernetes.io/name: \"tidb-cluster\"\n" +
                "    limits:\n" +
                "      cpu: \"2\"\n" +
                "      memory: \"3Gi\"\n" +
                "    maxFailoverCount: 3\n" +
                "    replicas: 3\n" +
                "    requests:\n" +
                "      storage: \"7Gi\"\n" +
                "    scalePolicy:\n" +
                "      scaleInParallelism: 1\n" +
                "      scaleOutParallelism: 1\n" +
                "    storageClassName: \"topolvm-provisioner\"\n" +
                "  timezone: \"UTC\"\n" +
                "  tlsCluster: {}\n" +
                "  version: \"\"\n";

        TidbCluster cr = YamlEngine.unmarshal(yaml, TidbCluster.class);
        IntOrString intOrStr = cr.getSpec().getPd().getRequests().get("storage");
        System.out.println(YamlEngine.marshal(intOrStr)); // kind: 1, strVal: 6Gi
        System.out.println(Serialization.asYaml(intOrStr)); // --- "6Gi"

        Volume emptyDir = cr.getSpec().getPd().getAdditionalVolumes().get(1);
        System.out.println(YamlEngine.marshal(emptyDir));
        System.out.println(Serialization.asYaml(emptyDir));
    }
}