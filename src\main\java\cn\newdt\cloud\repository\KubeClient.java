package cn.newdt.cloud.repository;

import cn.newdt.cloud.domain.KubeConfig;
import cn.newdt.cloud.domain.KubeScheduler;
import cn.newdt.cloud.domain.cr.*;
import cn.newdt.cloud.dto.Label;
import cn.newdt.cloud.dto.NodeDTO;
import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.dto.StorageClassDTO;
import com.shindata.opengauss.v1.OpenGaussCluster;
import com.shindata.opengauss.v1.OpenGaussClusterStatus;
import com.shindata.mysql.v1.MySQLHA;
import com.shindata.redis.v1.RedisCluster;
import io.fabric8.kubernetes.api.model.Node;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.api.model.apps.Deployment;
import io.fabric8.kubernetes.api.model.apps.StatefulSet;
import io.fabric8.kubernetes.api.model.coordination.v1.Lease;
import io.fabric8.kubernetes.client.CustomResource;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.fabric8.kubernetes.client.dsl.MixedOperation;
import io.fabric8.kubernetes.client.dsl.Resource;

import java.io.InputStream;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * todo custom exception
 * K8s客户端接口，在这里添加客户端通用api供业务逻辑使用。
 */
public interface KubeClient {

    /**
     * @param ns    namespace
     * @param labels labels, could be null
     */
    List<PodDTO> listPod(String ns, Label... labels) ;

    /**
     * @param ns    namespace
     */
    List<Pod> listPod(String ns) ;


    /**
     * @param t         custom resource实例对象
     * @param clz       custom resource java type
     * @param namespace
     */
    <T extends CustomResource> T createCustomResource(T t, Class<T> clz, String namespace);

    <T extends CustomResource> T createOrReplaceCustomResource(T t, Class<T> clz, String namespace);

    <T extends CustomResource> T createCustomResource(T t,Class<T> clz);

    <T extends CustomResource> void deleteCustomResource(T t, Class<T> type);

    <T extends CustomResource> boolean deleteCustomResource(String namespace, String crName, Class<T> type);

    <T extends CustomResource> T updateCustomResource(T t, Class<T> type);

    /**
     * get cr list in all namespace
     */
    <T extends CustomResource> List<T> listCustomResource(Class<T> type);

    /**
     * get cr list by namespace
     */
    <T extends CustomResource> List<T> listCustomResource(Class<T> type, String namespace);

    /**
     * get cr by namespace and name
     */
    <T extends CustomResource> T listCustomResource(Class<T> type, String name, String namespace);

    <T extends CustomResource> KubernetesResourceList<T> listCustomResource(Class<T> type, String namespace, Label... labels);

    /**
     * 获取RedisCluster CR client
     *
     * @return
     */
    @Deprecated
    MixedOperation<DistributedRedisCluster, KubernetesResourceList<DistributedRedisCluster>, Resource<DistributedRedisCluster>> getDistributedRedisCluster();

    /**
     * 获取指定命名空间下的RedisCluster CR
     *
     * @return
     */
    @Deprecated
    KubernetesResourceList<DistributedRedisCluster> listDistributedRedisCluster(String namespace);

    /**
     * 获取RedisCluster CR client
     *
     * @return
     */
    @Deprecated
    MixedOperation<RedisCluster, KubernetesResourceList<RedisCluster>, Resource<RedisCluster>> getRedisClusterClient();

    /**
     * 获取指定命名空间下的RedisCluster CR
     *
     * @return
     */
    @Deprecated
    KubernetesResourceList<RedisCluster> listRedisCluster(String namespace);

    /**
     * 获取MongoDB CR client
     *
     * @return
     */
    @Deprecated
    MixedOperation<MongoDBCommunity, KubernetesResourceList<MongoDBCommunity>, Resource<MongoDBCommunity>> getMongoDBClient();

    /**
     * 获取指定命名空间下的MongoDB CR
     *
     * @return
     */
    @Deprecated
    KubernetesResourceList<MongoDBCommunity> listMongoDB(String namespace);

    /**
     * 获取指定命名空间下的 MySQL CR
     *
     * @param namespace
     * @return
     */
    KubernetesResourceList<MySQLHA> listMysqlHA(String namespace);

    /**
     * 获取OpenGaussCluster CR client
     *
     * @return
     */
    @Deprecated
    MixedOperation<OpenGaussCluster, KubernetesResourceList<OpenGaussCluster>, Resource<OpenGaussCluster>> getOpenGaussClusterClient();

    /**
     * 获取指定命名空间下的OpenGaussCluster
     *
     * @param namespace
     * @return
     */
    @Deprecated
    KubernetesResourceList<OpenGaussCluster> listOpenGaussCluster(String namespace);

    /**
     * 执行命令-同步
     * @param command command format ["sh","-c","cmd1 && cmd2"]
     */
    String execCmd(String namespace, String pod, String container, String... command) throws Exception;

    String execCmd(String namespace, String pod, String container, int timeout, String... command) throws Exception;

    String execCmd(String namespace, String podName, String containerName, boolean tty, int timeout, String... cmd) throws Exception;
    /**
     * 执行命令-非阻塞，不需要接受响应
     */
    void execCmdOneway(String namespace, String pod, String container, String... command) throws Exception;

    List<PodDTO> listPodMetrics(String namespace, Label[] label);

    Pod getPod(String namespace,String podName);

    /**
     * get config map
     */
    ConfigMap getConfigMap(String name, String namespace);

    /**
     * delete config mp
     */
    void deleteConfigMap(String name, String namespace);

    /**
     * batch delete configmap
     */
    void deleteConfigMaps(List<String> name, String namespace);

    /**
     * list config map
     */
    List<ConfigMap> listConfigMap(String namespace, Label... labels);

    Secret createSecret(String name, String password, String namespace);
    Secret createSecret(String name, String namespace, String key, String data);

    String createSecret(String namespace, String name, Map<String, String> labels, Map<String, String> data);

    Boolean deleteSecret(String name, String namespace);

    void createPv(PersistentVolume... pvs);

    boolean deletePv(String name);

    boolean deletePvc(String namespace, String name);

    /**
     * get pv by labels
     * @param labels
     * @return PersistentVolumeList
     */
    PersistentVolumeList listPv(Map<String, String> labels);

    /**
     * get pv by scName
     *
     * @param scName
     * @return PersistentVolumeList
     */
    List<PersistentVolume> listPv(String scName);

    /**
     * list pv by label key
     * @return
     */
    PersistentVolumeList listPvWithLabelKey(String labelKey);

    /**
     * list pvc by label key
     *
     * @return
     */
    PersistentVolumeClaimList listPvcWithLabelKeyAndNameSpace(String namespace, String labelKey);

    /**
     * list pvcs by namespace and labels
     * @param namespace in any namespace if empty
     * @param labels  ignore if empty
     * @return fabric PersistentVolumeClaimList
     */
    PersistentVolumeClaimList listPvc(String namespace, Map<String, String> labels);

    /**
     * get file from pod
     * @param namespace 命名空间
     * @param podName pod名称
     * @param container 容器
     * @param file 获取指定文件全路径
     * @return
     */
    InputStream getFile(String namespace, String podName, String container, String file);

    /**
     * 上传文件到 pod文件系统
     * @param namespace 命名空间
     * @param podName pod名称
     * @param container 容器
     * @param destFile 容器内文件全路径
     * @param localFile  本地文件全路径
     * @return
     */
    Boolean uploadFile(String namespace, String podName, String container, String destFile, Path localFile);

    /**
     *get pvc by namespace、name
     * @param namespace
     * @param name
     * @return PersistentVolumeClaim
     */
    PersistentVolumeClaim getPvc(String namespace, String name);

    /**
     *get pvc by name
     * @param name
     * @return PersistentVolume
     */
    PersistentVolume getPv(String name);

    /**
     * 获取集群全部nodes
     * @return
     */
    List<NodeDTO> listNodes();

    /**
     * @param isReady master 是否 ready
     * @return master node list
     */
    List<NodeDTO> listMasterNodes(boolean isReady);

    /**
     * @return ready 的 master node list
     */
    List<NodeDTO> listReadyMasterNodes();

    /**
     * @return 第一个 ready 的 master node ip
     */
    String getReadyMasterIp();

    List<NodeDTO> listSimpleNodes(KubeScheduler kubeScheduler);

    void createLoadBalancerService(String name, String namespace,int nodePort, int port, Map<String, String> labelMap, Map<String, String> annotationMap, String loadbalancerIp);

    Node getNode(String nodeName);

    List<Service> listService(String namespace);

    Service createService(String name, String namespace, Integer nodePort, int port, Map<String, String> labelMap);

    void createService(String name, Integer writePort, String namespace, int i);

    void createClusterService(String name, String namespace, Integer port, String endpointName, Map<String,String> labels, Map<String,String> selectors);

    void createService(String name, Integer writePort, String namespace, int i, Map<String, String> label, Map<String, String> selector);

    Service getService(String namespace, String serviceName);

    void deleteService(String svcName, String namespace);

    void deleteDeployment(String deploymentName, String namespace);

    OpenGaussClusterStatus getOGCrStatus(String clusterName, String namespace);

    /**
     * apply config map: create or replace
     */
    void applyConfigMap(String name, String namespace, Map<String,String> data, Label... labels);

    void mergeConfigMap(String name, String namespace, Map<String, String> data, Map<String, String> binaryData, Label... labels);

    void applyConfigMap(ConfigMap cm, String namespace);

    String getVersion();

    List<StorageClassDTO> listStorageClass();

    void deleteStorageClass(String scName);

    void createOrReplaceNamespace(String namespace);

    Boolean deleteNamespace(String namespace);

    /**
     * 创建resourceQuota, 名称为namespace名称加固定后缀"resource-quota"
     */
    void createResourceQuota(String namespace, Map<String, String> quota, String quotaName);

    Set<String> getAllNamespaceName();

    Namespace getNamespaceName(String name);

    void updatePvcCapacity(String pvc, String namespace, String newCap);

    void updatePvClaimRef(PersistentVolume pvByLabels, String namespace);

    void updatePv(PersistentVolume pvByLabels);

    void updatePvc(PersistentVolumeClaim pvcByLabels,String namespace);

    Deployment getDeployments(String namespace, String name);

    void createOrReplaceDefaultLimitRange(String namespace, Map<String, String> limitRangeConfig);

    void deleteQuota(String namespace, String quotaName);

    void deleteLimitRange(String namespace);

    boolean checkRbac(String name, String namespace);

    void  applyYaml(String yaml, String namespace);
    void applyYaml2(String yaml);

    /**
     * 创建自定义资源, 不需要构建POJO。
     * @param context 资源定义
     * @param replace yaml
     * @param namespace 命名空间
     * @return 创建的资源
     */
//    GenericKubernetesResource applyCrYaml(ResourceDefinitionContext context, String replace, String namespace);

    /**
     * 批量创建同类型资源
     * @param kind HasMetaData#getKind()
     * @param items 要创建的资源对象列表
     */
    void createBatch(String kind, HasMetadata... items);
    void deleteBatch(String kind, HasMetadata... items);

    boolean deletePod(String namespace, String podName);

    /**
     * 更新nodePortService中的nodePort
     * @param serviceName
     * @param nodePort
     * @param namespace
     * @param type NodePort、LoadBalancer
     */
    void replaceService(String serviceName, Integer nodePort, String namespace, String type);

    void replaceService(String serviceName, Integer nodePort, String namespace, String type, Map<String, String> annotations, String loadbalancerIp);

    StatefulSet getStatefulSet(String name, String namespace);

    List<StatefulSet> listStatefulSetWithLables(String namespace, Label... labels);

    void updatePodLabel(String namespace, String name, Label label);

    ServiceAccountList getServiceAccountList(String namespace);

    void editServiceAccount(String namespace, ServiceAccount serviceAccount);

    Secret getSecret(String namespace, String secretName);

    boolean existLimitRange(String namespace);

    List<PodDTO> listPodMetricsOnly(String namespace, Label[] labelOfPod);

    KubernetesClient getClient();
    KubeConfig getConfig();

    void applyYaml(String crd);

    void deleteYaml(String yaml);

    PersistentVolumeClaimList listPvc(List<String> namespaceList, Map<String, String> labels);

    Secret updateSecret(Secret secret);

    void createPvc(PersistentVolumeClaim pvc, String nameSpace);

    void deleteStatefulset(String name, String namespace);

    void createSecret(String namespace, String secretName, Map<String, String> labels, Map<String, String> data, HasMetadata owner);

    Optional<HasMetadata> getByYaml(String doing);

    Optional<HasMetadata> getGenericResource(String apiVersion, String kind, String ns, String name);

    ResourceQuota getResourceQuota(String namespace, String name);

    /**
     * @return node列表，对每个node返回对应的k8snode资源信息，以及通过计算得到的node metric信息(capacity,usage)
     */
    List<NodeDTO> describeNodes(String nullableNodeName, KubeScheduler kubeSchedulerId);

    void createSecret(String namespace, String name, Map<String, String> of);

    StatefulSet patchStatefulSet(StatefulSet patchSts);

    Service patchNodePortSvc(Service svcInK8s, int newPort);

    void createOrReplaceSts(StatefulSet oldSts);

    void scaleSts(String name, String namespace, int replicas);

    /**
     * 通用的方式更新各种类型资源spec。限制, 仅能更新spec中的嵌套属性
     * @param gvk
     * @param plural
     * @param namespace
     * @param name
     * @param specPatch 要更新的spec属性和值
     */
    void updateGenericResource(GroupVersionKind gvk, String plural, String namespace, String name, Map<String,
            String> specPatch);

    void deletePvc(Map<String, String> labels, String namespace);

    void scaleDeploy(String namespace, String deployName, int num);

    List<Lease> getLease(String finalOperatorNamespace);

    void deleteStatefulset(String namespace, Label[] labels);

    /**
     * 创建自定义资源, 不需要构建POJO。
     * @param context 资源定义
     * @param replace yaml
     * @param namespace 命名空间
     * @return 创建的资源
     */
//    GenericKubernetesResource applyCrYaml(ResourceDefinitionContext context, String replace, String namespace);
}
