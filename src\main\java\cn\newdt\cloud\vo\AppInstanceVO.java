package cn.newdt.cloud.vo;

import cn.newdt.cloud.dto.PodDTO;
import cn.newdt.cloud.utils.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class AppInstanceVO extends PodStatusVO {

    private Integer id;

    private Integer kubeId;

    private String kubeName;

    private Integer appId;

    private String appName;

    private String appKind;

    private String role;
    /**
     * 启动时间
     */
    private String period;

    private String namespace;

    private boolean enableMaintenance;

    private String disk;

    // millisecond
    private Double rawCpu = 0.0;
    // byte
    private Long rawMemory = 0L;
    private Long rawDisk = 0L;

//    private String mysqlVersion;

    // add: 实例列表返回前端毫秒值
    private Long createTimestamp;

    private String arch;

    /**
     * 0 停止 1 启动 2 unknown
     */
    private int liveness;

    private Map<String, String> labels;

    private String brokerName;
    private String zoneRole;
//    @JsonIgnore
//    private PodDTO podDTO;

    public Long getCreateTimestamp(){
        return DateUtil.localDateTimeToEpochMilli(getStartTime());
    }

    private String componentKind;

    private List<String> containerNameList;

}
