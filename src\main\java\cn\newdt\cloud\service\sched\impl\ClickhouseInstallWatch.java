package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.impl.ClickhouseService;
import cn.newdt.cloud.utils.ClickhouseUtil;
import cn.newdt.commons.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class ClickhouseInstallWatch extends ClickhouseWatch {
    @Autowired
    private ClickhouseService clickhouseService;

    @Override
    protected void doStopWatch(CloudApp app, boolean success, TriggerHis triggerHis, OpsResultDTO build) {
        // 安装
        if (success) {
            appMultiAZService.enableAlert(app);
            Map<String, String> mergedJobDataMap = triggerHis.returnMergedJobDataMap();
            serviceManageOperationWatcherHelper.doStopWatchSvc(app, triggerHis, success, null);

            //判断是否要进行恢复操作
            if (StringUtils.isEmpty(mergedJobDataMap.get("backupHisId"))) {
                String username = mergedJobDataMap.get("username");
                String password = mergedJobDataMap.get("password"); // todo 加解密
                if (StringUtils.isNoneBlank(username, password)) {
                    if (!dbUserService.findDbUserByName(app.getId(), username).isPresent()) {
                        dbUserService.createUser(app.getId(), username, "", CloudAppConstant.UserRole.ADMIN);
                        createRootUser(username, password, app);
                    }
                }
                Map<String, String> dmpMonitorUser = operationUtil.createDMPMonitorUser();
                createRootUser(dmpMonitorUser.get("username"), dmpMonitorUser.get("password"), app);
            }

            if (autoManagement) {
                //添加纳管到dmp
                build.setMsg(operationUtil.syncToDMP(app, triggerHis));
            }
        }
    }

    private void createRootUser(String username, String password, CloudApp app) {
        try {
            String initSql = "Create user IF NOT EXISTS %s on cluster \'%s\' identified by \'%s\';";
            String grantSql = "GRANT on cluster \'%s\' ALL ON *.* TO \'%s\' WITH GRANT OPTION;";
            List<String> sqlList = new ArrayList<>();
            sqlList.add(String.format(initSql, username, ClickhouseUtil.getClickhouseClusterName(), password));
            sqlList.add(String.format(grantSql, ClickhouseUtil.getClickhouseClusterName(), username));
            clickhouseService.runExecSql(app, ActionEnum.CREATE_APP_USER, sqlList, null);
        } catch (Exception e) {
            log.error("clickhouse创建用户失败！");
            throw new CustomException(600, "clickhouse创建用户失败！");
        }
    }
}
