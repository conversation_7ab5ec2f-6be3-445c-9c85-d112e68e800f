package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.common.OpLogContext;
import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.dto.*;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.DefaultAppKindService;
import cn.newdt.cloud.service.ServiceManageOperation;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.service.sched.impl.*;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.*;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.bean.MysqlParamRules;
import cn.newdt.commons.exception.CustomException;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.pingcap.v1alpha1.TidbCluster;
import com.pingcap.v1alpha1.TidbClusterSpec;
import com.pingcap.v1alpha1.TidbCompentBasic;
import com.pingcap.v1alpha1.tidbclusterspec.Helper;
import com.pingcap.v1alpha1.tidbclusterspec.Pd;
import com.pingcap.v1alpha1.tidbclusterspec.Tidb;
import com.pingcap.v1alpha1.tidbclusterspec.Tikv;
import io.fabric8.kubernetes.api.model.ConfigMap;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.client.CustomResource;
import io.fabric8.kubernetes.client.utils.Serialization;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.Connection;
import java.sql.Driver;
import java.sql.Statement;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.CloudAppConstant.CLICKHOUSE_DATA_BACKUP_PATH;
import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgCategory.*;

@Slf4j
@Service
public class TidbService extends DefaultAppKindService<TidbCluster> implements ServiceManageOperation {


    @Override
    public AppKind getKind() {
        return AppKind.TIDB;
    }

    @Override
    public boolean nodePolicy() {
        return false;
    }


    @Override
    protected boolean supportIPAM(CloudAppVO app) {
        return false;
    }


    @Override
    protected void setInstallExtData(CloudAppVO vo) {
        Map adminUserParamMap = new HashMap();
        adminUserParamMap.put("username", vo.getUsername());
        adminUserParamMap.put("password", vo.getPassword());
        vo.setExtInstallData(adminUserParamMap);
    }

    @Override
    public void update(Integer id, OverrideSpec overrideSpec) throws Exception {
        if (overrideSpec instanceof TidbService.TidbOverrideSpec) {
            TidbResourceDTO patch = new TidbResourceDTO();
            TidbService.TidbOverrideSpec tidbOverrideSpec = (TidbService.TidbOverrideSpec) overrideSpec;
            patch.setPdCpu(tidbOverrideSpec.getPdCpu());
            patch.setPdMemory(tidbOverrideSpec.getPdMemory());
            patch.setPdDisk(tidbOverrideSpec.getPdDisk());

            patch.setTidbCpu(tidbOverrideSpec.getTidbCpu());
            patch.setTidbMemory(tidbOverrideSpec.getTidbMemory());

            patch.setTikvCpu(tidbOverrideSpec.getTikvCpu());
            patch.setTikvMemory(tidbOverrideSpec.getTikvMemory());
            patch.setTikvDisk(tidbOverrideSpec.getTikvDisk());

            patch.setAppId(id);
            patch.setId(id);
            update(patch);
        }
    }

    @Override
    public PageInfo<TidbClusterVO> searchPage(PageDTO page) {
        PageInfo<? extends CloudAppVO> pageInfo = super.searchPage(page);
        return PageUtil.page2PageInfo(pageInfo.getList(), TidbClusterVO.class, vo -> {
            TidbClusterVO clusterVO = new TidbClusterVO();
            BeanUtils.copyProperties(vo, clusterVO);

            String crYaml = StringUtils.isEmpty(clusterVO.getCr()) ? clusterVO.getCrRun() : clusterVO.getCr();
            if (StringUtils.isNotEmpty(crYaml)) {
                TidbCluster tidbCluster = YamlEngine.unmarshal(crYaml, TidbCluster.class);
                Tidb tidb = tidbCluster.getSpec().getTidb();
                clusterVO.setTidbReplicas(tidb.getReplicas());
                clusterVO.setTidbCpu(String.valueOf(tidb.getLimits().get(CloudAppConstant.ResourceName.CPU).getStrVal()));
                clusterVO.setTidbMemory(String.valueOf(tidb.getLimits().get(CloudAppConstant.ResourceName.MEMORY).getStrVal()));
                // clusterVO.setTidbDisk(String.valueOf(tidb.getRequests().get(CloudAppConstant.ResourceName.STORAGE).getStrVal()));

                Pd pd = tidbCluster.getSpec().getPd();
                clusterVO.setPdReplicas(pd.getReplicas());
                clusterVO.setPdCpu(String.valueOf(pd.getLimits().get(CloudAppConstant.ResourceName.CPU).getStrVal()));
                clusterVO.setPdMemory(String.valueOf(pd.getLimits().get(CloudAppConstant.ResourceName.MEMORY).getStrVal()));
                clusterVO.setPdDisk(String.valueOf(pd.getRequests().get(CloudAppConstant.ResourceName.STORAGE).getStrVal()));

                Tikv tikv = tidbCluster.getSpec().getTikv();
                clusterVO.setTikvReplicas(tikv.getReplicas());
                clusterVO.setTikvCpu(String.valueOf(tikv.getLimits().get(CloudAppConstant.ResourceName.CPU).getStrVal()));
                clusterVO.setTikvMemory(String.valueOf(tikv.getLimits().get(CloudAppConstant.ResourceName.MEMORY).getStrVal()));
                clusterVO.setTikvDisk(String.valueOf(tikv.getRequests().get(CloudAppConstant.ResourceName.STORAGE).getStrVal()));

            }

            //拓扑图所需
            //查询出所有的pod信息，根据pod-name进行pod的构建
            KubeClient kubeClient = clientService.get(clusterVO.getKubeId());
            String namespace = clusterVO.getNamespace();
            String crName = clusterVO.getCrName();
            List<PodDTO> podList = kubeClient.listPod(namespace, getKind().labels(crName));
            Map<String, List<String>> podByComponentMap = podList.stream().collect(Collectors.groupingBy(
                    pod -> pod.getLabels().get(CloudAppConstant.CustomLabels.APP_COMPONENT),
                    Collectors.mapping(PodDTO::getPodIp, Collectors.toList())
            ));
            clusterVO.setClusterIpMap(podByComponentMap);
            return clusterVO;
        });
    }

    @Override
    public List<ServiceManager> createService(
            String serviceType, CloudAppVO vo, List<?> serviceResources, CustomResource installCr) {
        return openSourceKindServiceBuilder(serviceType, vo, serviceResources, null);
    }

    @Override
    public void updateService(List<ServiceManager> svcMgrs, CloudApp app, Object oldServiceResource) throws Exception {
        openSourceKindUpdateServiceBuilder(svcMgrs, app, oldServiceResource);
    }

    @Override
    protected void createCrControlResource(TidbCluster cr, CloudAppVO vo) {
        // tikv 需要单独创建一个 Service 供 监控使用
        createTiKVExporterService(vo);
    }

    @Override
    public OverrideSpec reviewSpec(CloudApp app) {
        OverrideSpec overrideSpec = super.reviewSpec(app);
        TidbOverrideSpec tidbOverrideSpec = new TidbOverrideSpec();
        BeanUtils.copyProperties(overrideSpec, tidbOverrideSpec);
        TidbCluster cr = null;
        try {
            String crYaml = StringUtils.isEmpty(app.getCr()) ? app.getCrRun() : app.getCr();
            cr = YamlEngine.unmarshal(crYaml, TidbCluster.class);
            TidbClusterSpec spec = cr.getSpec();
            Map<String, IntOrString> pdLimits = spec.getPd().getLimits();
            Map<String, IntOrString> pdRequests = spec.getPd().getRequests();
            tidbOverrideSpec.setPdCpu(pdLimits.get(CloudAppConstant.ResourceName.CPU).getStrVal());
            tidbOverrideSpec.setPdMemory(pdLimits.get(CloudAppConstant.ResourceName.MEMORY).getStrVal());
            tidbOverrideSpec.setPdDisk(pdRequests.get(CloudAppConstant.ResourceName.STORAGE).getStrVal());
            tidbOverrideSpec.setPdReplicas(spec.getPd().getReplicas());

            Map<String, IntOrString> tidbLimits = spec.getTidb().getLimits();
            tidbOverrideSpec.setTidbCpu(tidbLimits.get(CloudAppConstant.ResourceName.CPU).getStrVal());
            tidbOverrideSpec.setTidbMemory(tidbLimits.get(CloudAppConstant.ResourceName.MEMORY).getStrVal());
            tidbOverrideSpec.setTidbReplicas(spec.getTidb().getReplicas());

            Map<String, IntOrString> tikvLimits = spec.getTikv().getLimits();
            Map<String, IntOrString> tikvRequests = spec.getTikv().getRequests();
            tidbOverrideSpec.setTikvCpu(tikvLimits.get(CloudAppConstant.ResourceName.CPU).getStrVal());
            tidbOverrideSpec.setTikvMemory(tikvLimits.get(CloudAppConstant.ResourceName.MEMORY).getStrVal());
            tidbOverrideSpec.setTikvDisk(tikvRequests.get(CloudAppConstant.ResourceName.STORAGE).getStrVal());
            tidbOverrideSpec.setTikvReplicas(spec.getTikv().getReplicas());
        } catch (Exception e) {
            log.error("", e);
        }
        return tidbOverrideSpec;
    }

    @Override
    public TidbCluster doInstall(CloudAppVO vo, List<String> ips) throws Exception {
        TidbClusterVO tidbClusterVO = (TidbClusterVO) vo;
        KubeClient kubeClient = clientService.get(tidbClusterVO.getKubeId());
        //应用 filebeat 的cm信息
        String crName = vo.getCrName();
        String namespace = vo.getNamespace();
        ensureFilebeatCM(kubeClient, crName, namespace);

        //应用 tidb的 备份恢复cm信息
        ensureTidbScriptsCM(kubeClient, crName, namespace);

        //需要手动 创建 tikv的Service 供 监控使用

        return buildTidbCr(tidbClusterVO);
    }

    @Override
    public void scale(int id, OverrideSpec vo, ActionEnum action) throws Exception {
        TidbOverrideSpec tidbSpec = (TidbOverrideSpec) vo;
        scale(id, tidbSpec.getPdReplicas(), tidbSpec.getTidbReplicas(), tidbSpec.getTikvReplicas());
    }

    @Transactional
    public void scale(int appId, Integer pdReplicas, Integer tidbReplicas, Integer tikvReplicas) throws Exception {
        CustPreconditions.checkState(pdReplicas > 0, "pd数量不能<1");
        CustPreconditions.checkState(tidbReplicas > 0, "tidb数量不能<1");
        CustPreconditions.checkState(tikvReplicas > 0, "tikv数量不能<1");
        CustPreconditions.checkNotNull(appId, "appId not be null!");

        //拿到相关的组件数量 判断是 扩容还是缩容
        CloudApp app = appService.get(appId);
        TidbCluster cr = clientService.get(app.getKubeId()).listCustomResource(TidbCluster.class, app.getCrName(), app.getNamespace());
        cr.setStatus(null);

        //获取cr中相关的组件信息
        TidbClusterSpec tidbSpec = cr.getSpec();
        Pd pd = tidbSpec.getPd();
        Integer crPdReplicas = pd.getReplicas();
        Tidb tidb = tidbSpec.getTidb();
        Integer crTidbReplicas = tidb.getReplicas();
        Tikv tikv = tidbSpec.getTikv();
        Integer crTikvReplicas = tikv.getReplicas();

        if (pdReplicas.equals(crPdReplicas) && tidbReplicas.equals(crTidbReplicas) && tikvReplicas.equals(crTikvReplicas)) {
            log.warn("tidb相关组件数与当前一致，未进行扩缩容操作！");
            throw new Exception("tidb相关组件数与当前一致，未进行扩缩容操作！");
        }
        int pdResult = Long.compare(pdReplicas, crPdReplicas);
        int tidbResult = Long.compare(tidbReplicas, crTidbReplicas);
        int tikvResult = Long.compare(tikvReplicas, crTikvReplicas);

        Map<String, Object> map = new HashMap<>();
        map.put("app", app);

        ActionEnum actionEnum = (pdResult == 1 || tidbResult == 1 || tikvResult == 1) ? ActionEnum.SCALE_OUT : ActionEnum.SCALE_IN;
        map.put("type", actionEnum);

        //修改相关的组件数量
        pd.setReplicas(pdReplicas);
        tidb.setReplicas(tidbReplicas);
        tikv.setReplicas(tikvReplicas);

        tidbSpec.setPd(pd);
        tidbSpec.setTidb(tidb);
        tidbSpec.setTikv(tikv);

        cr.setSpec(tidbSpec);
        app.setCrRun(YamlEngine.marshal(cr));
        String applyYaml = YamlEngine.marshal(cr);

        TidbCluster undoCr = YamlEngine.unmarshal(app.getCr(), TidbCluster.class);
        undoCr.setStatus(null);
        String undoCrYaml = Serialization.asYaml(undoCr);

        OpLogContext.instance().YAML("CR", applyYaml, undoCrYaml);
        appService.callScheduler(app, applyYaml, map, actionEnum, TidbScaleWatch.class);
        clientService.get(app.getKubeId()).updateCustomResource(cr, TidbCluster.class);

    }

    @Override
    public void update(ResourceDTO patch) throws Exception {
        TidbResourceDTO tidbResourceDTO = (TidbResourceDTO) patch;
        //计算cpu、memory
        Consumer<TidbCluster> modifier = (current) -> {
            TidbClusterSpec spec = current.getSpec();
            spec.getPd().setLimits(ImmutableMap.of(CloudAppConstant.ResourceName.CPU, new IntOrString(tidbResourceDTO.getPdCpu()), CloudAppConstant.ResourceName.MEMORY, new IntOrString(tidbResourceDTO.getPdMemory())));
            spec.getTidb().setLimits(ImmutableMap.of(CloudAppConstant.ResourceName.CPU, new IntOrString(tidbResourceDTO.getTidbCpu()), CloudAppConstant.ResourceName.MEMORY, new IntOrString(tidbResourceDTO.getTidbMemory())));
            spec.getTikv().setLimits(ImmutableMap.of(CloudAppConstant.ResourceName.CPU, new IntOrString(tidbResourceDTO.getTikvCpu()), CloudAppConstant.ResourceName.MEMORY, new IntOrString(tidbResourceDTO.getTikvMemory())));
        };

        Consumer<TidbCluster> storageModifier = (current) -> {
            TidbClusterSpec spec = current.getSpec();
            spec.getPd().setRequests(ImmutableMap.of(CloudAppConstant.ResourceName.STORAGE, new IntOrString(tidbResourceDTO.getPdDisk())));
            // spec.getTidb().setRequests(ImmutableMap.of(CloudAppConstant.ResourceName.STORAGE, new IntOrString(tidbResourceDTO.getTidbDisk())));
            spec.getTikv().setRequests(ImmutableMap.of(CloudAppConstant.ResourceName.STORAGE, new IntOrString(tidbResourceDTO.getTikvDisk())));
        };
        operationHandler.handleUpdate(patch, modifier, this, TidbCluster.class, storageModifier);
    }

    @Override
    public void delete(CloudApp app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        String[] names = getStsOrDeployNames(app);
        if (names == null) throw new RuntimeException("未查询到相关的tidb信息！");
        Arrays.stream(names).parallel().forEach(name -> {
            if (name.contains("discovery")) {
                kubeClient.scaleDeploy(app.getNamespace(), name, 0);
            }
            kubeClient.scaleSts(name, app.getNamespace(), 0);
        });
    }

    protected String[] getStsOrDeployNames(CloudApp app) {
        return new String[]{
                app.getCrName() + "-" + ComponentKindEnum.PD.name().toLowerCase(),
                app.getCrName() + "-" + ComponentKindEnum.TIDB.name().toLowerCase(),
                app.getCrName() + "-" + ComponentKindEnum.TIKV.name().toLowerCase(),
                app.getCrName() + "-" + ComponentKindEnum.DISCOVERY.name().toLowerCase()
        };
    }

    @Override
    public void recreate(CloudApp app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        String[] names = getStsOrDeployNames(app);
        if (names == null) throw new RuntimeException("未查询到相关的tidb信息！");
        String yaml = StringUtils.isEmpty(app.getCrRun()) ? app.getCr() : app.getCrRun();
        TidbCluster cr = YamlEngine.unmarshal(yaml, TidbCluster.class);
        Arrays.stream(names).parallel().forEach(name -> {
            if (name.contains("discovery")) {
                kubeClient.scaleDeploy(app.getNamespace(), name, 1);
            }
            kubeClient.scaleSts(name, app.getNamespace(), getReplicas(cr, name));
        });
    }

    private int getReplicas(TidbCluster cr, String name) {
        if (name.endsWith("pd")) {
            return cr.getSpec().getPd().getReplicas();
        }
        if (name.endsWith("tidb")) {
            return cr.getSpec().getTidb().getReplicas();
        }
        if (name.endsWith("tikv")) {
            return cr.getSpec().getTikv().getReplicas();
        }
        return 0;
    }

    @Override
    public void deleteCrControlledResources(CloudApp app) {
        KubeClient kubeClient = clientService.get(app.getKubeId());
        String namespace = app.getNamespace();
        //清理 exporter service
        kubeClient.deleteService(TidbUtil.buildTiKVExporterServiceName(app.getCrName()), namespace);
    }

    @Override
    protected void completeInstanceProperty(AppInstanceVO appInstanceVO, PodDTO pod, CloudApp app, KubeClient kubeClient) {
        //判断插件类型
        appInstanceVO.setComponentKind(TidbUtil.getComponentKind(pod));

        // 设置角色信息
        if (!ComponentKindEnum.PD.name().equalsIgnoreCase(pod.getLabels().get(CloudAppConstant.CustomLabels.APP_COMPONENT)))
            return;

        TidbCluster cr = clientService.get(app.getKubeId()).listCustomResource(TidbCluster.class, app.getCrName(), app.getNamespace());
        String pdLeaderName = cr.getStatus().getPd().getLeader().getName();
        appInstanceVO.setRole(pdLeaderName.equalsIgnoreCase(pod.getPodName()) ? CloudAppConstant.ROLE_PRIMARY : CloudAppConstant.ROLE_SECONDARY);


    }

    /**
     * 构建tidb cr 信息
     *
     * @return
     */
    private TidbCluster buildTidbCr(TidbClusterVO vo) {
        TidbCluster tidbCluster = new TidbCluster();

        String crName = vo.getCrName();
        // 设置基本的信息
        tidbCluster.setMetadata(new ObjectMetaBuilder().withName(crName).withNamespace(vo.getNamespace()).withLabels(Label.toMap(getKind().labels(crName))).build());
        // 构建spec
        TidbClusterSpec tidbClusterSpec = new TidbClusterSpec();
        tidbCluster.setSpec(tidbClusterSpec);
        tidbClusterSpec.setTimezone("UTC");
        tidbClusterSpec.setImagePullPolicy(CloudAppConstant.ImagePullPolicy.IFNOTPRESENT);

        //扩缩容是否删除相关的pvc
        tidbClusterSpec.setEnablePVReclaim(Boolean.TRUE);

        // 加快 pod启动
        tidbClusterSpec.setPodManagementPolicy(CloudAppConstant.PodManagementPolicy.PARALLEL);
        tidbClusterSpec.setConfigUpdateStrategy(CloudAppConstant.ConfigUpdateStrategy.ROLLINGUPDATE);

        //是否开启动态配置修改
        tidbClusterSpec.setEnableDynamicConfiguration(Boolean.TRUE);

        tidbClusterSpec.setPd(buildCompoentPd(vo));
        tidbClusterSpec.setTikv(buildCompoentTikv(vo));
        tidbClusterSpec.setTidb(buildCompoentTidb(vo));

        // 设置helper信息
        Helper helper = new Helper();
        helper.setImage(vo.getImageConfig().get(ImageKindEnum.Tidb_Helper));
        helper.setImagePullPolicy(CloudAppConstant.ImagePullPolicy.IFNOTPRESENT);
        tidbClusterSpec.setHelper(helper);

        // 设置相关调度策略信息
        SelectorDTO[] selector = vo.getSelector();
        Affinity affinity = new Affinity();
        if (null != selector)
            affinity.setNodeAffinity(convertCRNodeAffinity(selector, NodeAffinity.class));

        if (vo.getAntiAffinityRequired()) {
            // 强制pod反亲和
            PodAffinityTerm podAffinityTerm = new PodAffinityTermBuilder().withLabelSelector(new LabelSelectorBuilder()
                    .withMatchLabels(Label.toMap(AppKind.TIDB.labelOfPod(vo)))
                    .build()).withTopologyKey("kubernetes.io/hostname").build();
            PodAntiAffinity podAntiAffinity = new PodAntiAffinityBuilder().withRequiredDuringSchedulingIgnoredDuringExecution(Lists.newArrayList(podAffinityTerm)).build();
            affinity.setPodAntiAffinity(podAntiAffinity);
        }

        tidbClusterSpec.setAffinity(affinity);
        return tidbCluster;
    }

    private Pd buildCompoentPd(TidbClusterVO tidbClusterVO) {
        Pd pd = new Pd();
        TidbCompentBasic.TidbBasicVo tidbBasicVo = pd.buildComponetParam(tidbClusterVO);
        tidbBasicVo.setCompentConfig(buildComponentConfigParam(tidbClusterVO, tidbClusterVO.getPdParamTemplateId(), tidbClusterVO.getPdTemplateTmpParam(), ComponentKindEnum.PD));
        return pd.buildTidbPdComponent(pd, tidbBasicVo);
    }

    private Tidb buildCompoentTidb(TidbClusterVO tidbClusterVO) {
        Tidb tidb = new Tidb();
        com.pingcap.v1alpha1.tidbclusterspec.tidb.Service service = new com.pingcap.v1alpha1.tidbclusterspec.tidb.Service();
        service.setType(CloudAppConstant.ServiceType.CLUSTER_IP);
        tidb.setService(service);

        TidbCompentBasic.TidbBasicVo tidbBasicVo = tidb.buildComponetParam(tidbClusterVO);
        tidbBasicVo.setCompentConfig(buildComponentConfigParam(tidbClusterVO, tidbClusterVO.getTidbParamTemplateId(), tidbClusterVO.getTidbTemplateTmpParam(), ComponentKindEnum.TIDB));
        return tidb.buildTidbPdComponent(tidb, tidbBasicVo);
    }

    private Tikv buildCompoentTikv(TidbClusterVO tidbClusterVO) {
        Tikv tikv = new Tikv();
        TidbCompentBasic.TidbBasicVo tidbBasicVo = tikv.buildComponetParam(tidbClusterVO);
        tidbBasicVo.setCompentConfig(buildComponentConfigParam(tidbClusterVO, tidbClusterVO.getTikvParamTemplateId(), tidbClusterVO.getTikvTemplateTmpParam(), ComponentKindEnum.TIKV));
        return tikv.buildTidbPdComponent(tikv, tidbBasicVo);
    }

    /**
     * 此为模板, 一个ns下的pod共用一个. mount时替换fields属性
     */
    private void ensureFilebeatCM(KubeClient kubeClient, String cmName, String namespace) {
        ConfigMap cm = kubeClient.getConfigMap(cmName, namespace);
        if (cm == null) {
            String cmYaml = sysConfigService.findOne(OPERATOR_CONFIG, "TiDB.config");
            Map<String, String> param = new HashMap<>();
            param.put("NAMESPACE", namespace);
            param.put("NAME", TidbUtil.TIDB_FILEBEAT_CONFIGMAP_NAME);
            param.put("es_host", esUtil.getEsIp() + ":" + esUtil.getEsPort());
            param.put("es_username", esUtil.getEsUsername());
            param.put("es_pwd", esUtil.getEsPassword());
            param.put("es_protocol", esUtil.getProtocol());
            cmYaml = YamlUtil.evaluateTemplate(cmYaml, param);
            kubeClient.applyYaml(cmYaml, namespace);
        }
    }

    /**
     * 创建 tidb 的脚本 cm
     */
    private void ensureTidbScriptsCM(KubeClient kubeClient, String cmName, String namespace) {
        ConfigMap cm = kubeClient.getConfigMap(cmName, namespace);
        if (cm == null) {
            String cmYaml = sysConfigService.findOne(CONFIGMAP_TEMPLATE, "TiDB_Scripts_CM");
            Map<String, String> param = new HashMap<>();
            param.put("NAMESPACE", namespace);
            param.put("NAME", TidbUtil.TIDB_SCRIPTS_CONFIGMAP_NAME);
            cmYaml = YamlUtil.evaluateTemplate(cmYaml, param);
            kubeClient.applyYaml(cmYaml, namespace);
        }
    }

    /**
     * 需要单独给 tikv 创建一个Service 供监控使用
     */
    private void createTiKVExporterService(CloudApp app) {
        String crName = app.getCrName();
        String namespace = app.getNamespace();
        Map<String, String> labelMap = Label.toMap(getKind().labels(crName));
        labelMap.put(CloudAppConstant.CustomLabels.APP_COMPONENT, ComponentKindEnum.TIKV.name().toLowerCase());
        clientService.get(app.getKubeId()).createClusterService(
                TidbUtil.buildTiKVExporterServiceName(crName), namespace,
                20180, "metrics", labelMap, labelMap);
    }

    public String buildComponentConfigParam(TidbService.TidbClusterVO vo, Integer templateId, Map<String, String> templateTmpParam, ComponentKindEnum componentKindEnum) {
        Map<String, Object> config = new HashMap<>();
        // to do templateid select
        if (null != templateId)
            config = composeDbParamTemplateToMap(cloudDbParamTemplateService.getMysqlParamTemplate(templateId));

        // to do web temp template param
        filterParamProhibitConfig(config, templateTmpParam, componentKindEnum);

        // to do calculate param config
        autoCalculateParamFormulaConfig(vo, config, componentKindEnum);
        return TidbUtil.covertMapToTomlString(config);
    }

    private Map<String, Object> composeDbParamTemplateToMap(MySQLParamTemplateDTO mysqlParamTemplate) {
        Map<String, Object> config = new HashMap<>();
        for (MysqlParamRules each : mysqlParamTemplate.getMysqlParamRules()) {
            log.debug("[设置参数模板]遍历获取参数：key：" + each.getParaname() + "   value:" + each.getParavalue());
            CustPreconditions.checkState(!(each.getParaname() == null || "".equals(each.getParaname().trim())),
                    "variableName can't be empty");
            String value = null == each.getParavalue() ?
                    (each.getDefaultvalue() == null || "".equals(each.getDefaultvalue().trim()) ?
                            each.getOptions()[0]
                            : each.getDefaultvalue())
                    : each.getParavalue();
            if ("integer".equals(each.getType())) {
                config.put(each.getParaname().trim(), Integer.valueOf(value));
            } else if (TidbUtil.isFloat(value)) {
                config.put(each.getParaname().trim(), Double.valueOf(value));
            } else {
                config.put(each.getParaname().trim(), value);
            }
        }
        return config;
    }

    /**
     * 判断相关组件是否有不可修改参数
     *
     * @param config
     * @param templateTmpParam
     * @param componentKindEnum
     */
    private void filterParamProhibitConfig(Map<String, Object> config, Map<String, String> templateTmpParam, ComponentKindEnum componentKindEnum) {
        if (null == templateTmpParam || templateTmpParam.isEmpty())
            return;

        //查询 不可修改列表
        Map<String, String> paramFormulaMap = JSONObject.parseObject(sysConfigService.findOne(PARAM_PROHIBIT, AppKind.TIDB.getProduct())).toJavaObject(Map.class);

        if (null == paramFormulaMap || paramFormulaMap.isEmpty())
            return;

        //获取单独组件的不可修改参数
        String prohibitContent = paramFormulaMap.get(componentKindEnum.name());
        TidbUtil.prohibitParam(String.join(",", templateTmpParam.keySet()), prohibitContent);
        for (Map.Entry<String, String> entry : templateTmpParam.entrySet()) {
            if ("-deleted-".equals(entry.getValue())) {
                config.remove(entry.getKey());
            } else {
                config.put(entry.getKey(), entry.getValue());
            }
        }
    }

    /**
     * 自动计算 最近配置
     *
     * @param config
     */
    private void autoCalculateParamFormulaConfig(CloudAppVO vo, Map<String, Object> config, ComponentKindEnum componentKindEnum) {
        // 自动计算的最佳配置
        String paramFormulaStr = null;
        try {
            paramFormulaStr = sysConfigService.findOne(PARAM_FORMULA, AppKind.TIDB.getProduct());
        } catch (NullPointerException e) {
            // do nothing
            log.warn("未查询到相关的 动态资源 配置信息");
            return;
        }

        if (StringUtils.isBlank(paramFormulaStr)) {
            return;
        }
        String logDeleteDays = sysConfigService.findOne(CloudAppConstant.SysCfgCategory.LOG_CONFIG, CloudAppConstant.SysCfgName.LOG_DELETE_DAYS);
        paramFormulaStr = paramFormulaStr.replace("${max-days}", logDeleteDays);
        Map<String, String> paramFormulaMap = JSONObject.parseObject(paramFormulaStr).toJavaObject(Map.class);

        String componentParamFormulaStr = String.valueOf(null == paramFormulaMap.get(componentKindEnum.name()) ? "" : paramFormulaMap.get(componentKindEnum.name()));
        if (StringUtils.isNotEmpty(componentParamFormulaStr)) {
            Map<String, String> compentParamFormulaMap = JSONObject.parseObject(componentParamFormulaStr).toJavaObject(Map.class);
            for (Map.Entry<String, String> entry : compentParamFormulaMap.entrySet()) {
                if (!config.containsKey(entry.getKey())) {
                    Object val = TidbUtil.calculate(vo, entry.getValue());
                    config.putIfAbsent(entry.getKey(), val);
                }
            }
        }
    }


    @Getter
    @Setter
    @ToString
    public static class TidbOverrideSpec extends OverrideSpec {
        //pd
        private Integer pdReplicas;
        private String pdCpu;
        private String pdMemory;
        private String pdDisk;
        private String pdCsiType;
        private Integer pdParamTemplateId;
        private Map<String, String> pdTemplateTmpParam;

        //tikv
        private Integer tikvReplicas;
        private String tikvCpu;
        private String tikvMemory;
        private String tikvDisk;
        private String tikvCsiType;
        private Integer tikvParamTemplateId;
        private Map<String, String> tikvTemplateTmpParam;

        //tidb
        private Integer tidbReplicas;
        private String tidbCpu;
        private String tidbMemory;
        private String tidbDisk;
        private String tidbCsiType;
        private Integer tidbParamTemplateId;
        private Map<String, String> tidbTemplateTmpParam;
    }

    @Data
    public static class TidbClusterVO extends CloudAppVO {
        //pd
        private Integer pdReplicas;
        private String pdCpu;
        private String pdMemory;
        private String pdDisk;
        private String pdCsiType;
        private Integer pdParamTemplateId;
        private Map<String, String> pdTemplateTmpParam;

        //tikv
        private Integer tikvReplicas;
        private String tikvCpu;
        private String tikvMemory;
        private String tikvDisk;
        private String tikvCsiType;
        private Integer tikvParamTemplateId;
        private Map<String, String> tikvTemplateTmpParam;

        //tidb
        private Integer tidbReplicas;
        private String tidbCpu;
        private String tidbMemory;
        private String tidbDisk;
        private String tidbCsiType;
        private Integer tidbParamTemplateId;
        private Map<String, String> tidbTemplateTmpParam;

        private Map<String, List<String>> clusterIpMap;
    }

    @Override
    public InstallAppVo<TidbOverrideSpec> parseInstallVo(String data) {
        InstallAppVo<TidbOverrideSpec> vo = JsonUtil.toObject(data, new com.fasterxml.jackson.core.type.TypeReference<InstallAppVo<TidbOverrideSpec>>() {
        });
        if (vo != null) {
            if (vo.getSpec() != null && vo.getSpec().getMembers() == 0) {
                TidbOverrideSpec spec = vo.getSpec();
                spec.setMembers(spec.getPdReplicas() * spec.getTidbReplicas() * spec.getTikvReplicas());
            }
            if (!CollectionUtils.isEmpty(vo.getOverrideSpecs())) {
                for (TidbOverrideSpec spec : vo.getOverrideSpecs().values()) {
                    if (0 == spec.getMembers() && spec.getPdReplicas() != null && spec.getTidbReplicas() != null && spec.getTikvReplicas() != null)
                        spec.setMembers(spec.getPdReplicas() * spec.getTidbReplicas() * spec.getTikvReplicas());
                }
            }
        }
        return vo;
    }

    @Override
    public CloudAppVO overrideSpec(CloudAppLogic logicApp, Integer kubeId, InstallAppVo<? extends OverrideSpec> vo) {
        CloudAppVO appVO = super.overrideSpec(logicApp, kubeId, vo);
        TidbClusterVO clusterVO = new TidbClusterVO();
        BeanUtils.copyProperties(appVO, clusterVO);
        if (vo.getOverrideSpecs().get(kubeId) instanceof TidbOverrideSpec) {
            TidbOverrideSpec overrideSpec = (TidbOverrideSpec) vo.getOverrideSpecs().get(kubeId);
            clusterVO.setPdCpu(overrideSpec.getPdCpu());
            clusterVO.setPdMemory(overrideSpec.getPdMemory());
            clusterVO.setPdDisk(overrideSpec.getPdDisk());
            clusterVO.setPdCsiType(overrideSpec.getPdCsiType());
            clusterVO.setPdReplicas(overrideSpec.getPdReplicas());
            clusterVO.setPdParamTemplateId(overrideSpec.getPdParamTemplateId());
            clusterVO.setPdTemplateTmpParam(overrideSpec.getPdTemplateTmpParam());

            clusterVO.setTidbCpu(overrideSpec.getTidbCpu());
            clusterVO.setTidbMemory(overrideSpec.getTidbMemory());
            clusterVO.setTidbDisk(overrideSpec.getTidbDisk());
            clusterVO.setTidbCsiType(overrideSpec.getTidbCsiType());
            clusterVO.setTidbReplicas(overrideSpec.getTidbReplicas());
            clusterVO.setTidbParamTemplateId(overrideSpec.getTidbParamTemplateId());
            clusterVO.setTidbTemplateTmpParam(overrideSpec.getTidbTemplateTmpParam());

            clusterVO.setTikvCpu(overrideSpec.getTikvCpu());
            clusterVO.setTikvMemory(overrideSpec.getTikvMemory());
            clusterVO.setTikvDisk(overrideSpec.getTikvDisk());
            clusterVO.setTikvCsiType(overrideSpec.getTikvCsiType());
            clusterVO.setTikvReplicas(overrideSpec.getTikvReplicas());
            clusterVO.setTikvParamTemplateId(overrideSpec.getTikvParamTemplateId());
            clusterVO.setTikvTemplateTmpParam(overrideSpec.getTikvTemplateTmpParam());
        }
        return clusterVO;
    }

    @Override
    public Class<? extends OpsPostProcessor> getProcessorClass(ActionEnum action) {
        switch (action) {
            case CREATE:
                return TidbInstallWatch.class;
            case UPDATE:
                return TidbUpdateWatch.class;
            case UPDATE_SERVICE:
            case DELETE_SERVICE:
            case CREATE_SERVICE:
                return TidbServiceWatch.class;
            case SCALE_IN:
            case SCALE_OUT:
                return TidbScaleWatch.class;
            case MODIFY_PARAM:
                return TidbWatch.class;
            default:
                return super.getProcessorClass(action);
        }
    }


    /**
     * tidb获取connect 连接
     *
     * @throws Exception
     */
    public Connection getDbConnection(AppDBVO dbvo, Boolean isRoot) throws Exception {
        // 加载JDBC驱动
        Driver driver = TidbUtil.getDriver();
        return driver.connect(
                TidbUtil.getConnectionUrl(dbvo.getIp(), dbvo.getPort()), TidbUtil.getConnectProperties(isRoot));
    }

    public void runExecSql(CloudApp app, ActionEnum operationTypeEnum,
                           List<String> execSqlList, String flushSql, Boolean isRoot) {
        //获取写连接
        try (Connection connection = getDbConnection(
                accessManagementService.getFirstWriteServiceAddressByAppIdAndKubeId(
                        app.getId(), app.getKubeId()), isRoot)) {
            Statement stmt = connection.createStatement();

            if (!CollectionUtils.isEmpty(execSqlList)) {
                execSqlList.stream().forEach(
                        execsql -> {
                            try {
                                stmt.execute(execsql);
                            } catch (Exception e) {
                                throw new CustomException(600, e.getMessage());
                            }
                        }
                );
            }
            if (StringUtils.isNotEmpty(flushSql)) {
                stmt.execute(flushSql);
            }
        } catch (Exception e) {
            log.error("sql : 执行 用户权限sql报错：" + e.getMessage());
            throw new CustomException(600, operationTypeEnum.getAppOperation() + "失败");
        }
    }

    @Override
    public void restore(BackupHis backupHis, Integer appId, String restoreTime, String ftpFilename, String backupType) {
        // 获取备份存储的资源列表
        CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
        if (null == cloudBackupStorageVO)
            throw new CustomException(600, "未查询到相关的备份存储配置信息！");

        //1、保存基本信息
        RestoreHis restoreHis = new RestoreHis();
        // 0. 创建还原的对象
        restoreHis.setStatus(StatusConstant.RUNNING);
        Date startDate = new Date();
        //时间转换
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");

        Timestamp startTime = new Timestamp(System.currentTimeMillis());
        restoreHis.setStartTime(startTime);
        // 1. 插入基础恢复历史和操作记录
        CloudApp goalApp = appService.get(appId);
        CloudApp backupApp = appService.get(backupHis.getAppId());

        restoreHis.setAppId(appId);
        restoreHis.setAppName(goalApp.getName());
        restoreHis.setAppType(goalApp.getKind());
        KubeClient kubeClient = clientService.get(goalApp.getKubeId());
        String namespace = goalApp.getNamespace();
        String goalCrName = goalApp.getCrName();
        TidbCluster cr = kubeClient.listCustomResource(TidbCluster.class, goalCrName, namespace);
        //获取pod名称
        // 获取需要的 备份操作 tikv
        List<String> tikvPodNames = cr.getStatus().getTikv().getStores().values().stream().map(store -> store.getPodName()).collect(Collectors.toList());
        backupHis.setPodName(tikvPodNames.stream().collect(Collectors.joining(",")));

        //应用所属集群
        KubeConfig kubeConfig = kubeConfigService.get(goalApp.getKubeId());
        if(null == kubeConfig){
            log.error("未获取到集群！");
            backupUtil.restoreReturn(restoreHis, null, "未获取到集群！", StatusConstant.FAIL);
            return;
        }
        restoreHis.setKubeName(kubeConfig.getName());
        restoreHis.setMessage("恢复中...");
        restoreHis.setFileName(backupHis.getFileName());
        restoreHis.setRestoreDir(CLICKHOUSE_DATA_BACKUP_PATH);
        restoreHis.setFileDeleted(false);
        //插入基本信息
        backupService.commitRestoreHis(restoreHis);

        //插入操作记录
        ResourceChangeHis resourceChangeHis = backupUtil.createBasicHis(goalApp, startTime, kubeConfig);
        resourceChangeHis.setCommand("恢复");
        resourceChangeHis.setStatus("2");
        resourceChangeHis.setAction(ActionEnum.RESTORE.getActionType());
        resourceChangeHis.setMsg("恢复中...");
        Integer changeId = backupUtil.insertResourceChangeHis(resourceChangeHis);

        goalApp.setStatus(CloudAppConstant.AppStatus.PENDING);
        appService.update(goalApp);

        //备份文件名称
        String backupFileName = backupHis.getFileName();

        //备份文件路径
        String backupPath = TidbUtil.getTidbBackupPath(backupApp) + backupFileName;

        //进行路径挂载
        String containerBackupStorageRootDir = "/mnt/shared";
        String mountCommand = backupUtil.buildMountScriptCMD(
                cloudBackupStorageVO, containerBackupStorageRootDir, "sh", true);
        log.info("[tidb恢复]，执行的挂载语句为：sh -c " + mountCommand);
        tikvPodNames.parallelStream().forEach(podName -> {
            try {
                // 挂载结束
                kubeClient.execCmd(namespace, podName, TidbUtil.TIDB_MNT_CONTAINER_NAME, "sh", "-c", mountCommand);
                // 进行备份文件复制
                kubeClient.execCmd(namespace, podName, TidbUtil.TIDB_BACKUP_CONTAINER_NAME, "sh", "-c", "sh /scripts/tidb-restorefile-move.sh " + backupPath);
            } catch (Exception e) {
                backupUtil.restoreReturn(restoreHis, changeId, "TiDB恢复失败！错误信息：" + e.getMessage(), StatusConstant.FAIL);
                return;
            }
        });

        try{
            //创建定时轮询备份结果
            Map map = new HashMap();
            map.put("restoreStartDateSDF", sdf.format(startDate));
            map.put("restoreHisId", restoreHis.getRestoreHisId());
            map.put("resourceChangeId", changeId);
            map.put("restorePodNames", tikvPodNames.stream().collect(Collectors.joining(",")));
            map.put("restorePodName", tikvPodNames.get(0));
            map.put("backupFileName", backupFileName);
            appService.callScheduler(goalApp, YamlEngine.marshal(cr), map, ActionEnum.RESTORE, TidbBackupAndRestoreWatch.class, resourceChangeHis);
        }catch (Exception e){
            backupUtil.restoreReturn(restoreHis, changeId, e.getMessage(), StatusConstant.FAIL);
        }

    }

}
