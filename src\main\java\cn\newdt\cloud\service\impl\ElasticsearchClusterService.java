package cn.newdt.cloud.service.impl;

import cn.newdt.cloud.common.OpLogContext;
import cn.newdt.cloud.config.CloudRequestContext;
import cn.newdt.cloud.constant.*;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.domain.cr.Elasticsearch;
import cn.newdt.cloud.domain.cr.Kibana;
import cn.newdt.cloud.dto.*;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.*;
import cn.newdt.cloud.service.csi.CSIUtil;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.service.sched.impl.*;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.*;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.UserUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.fabric8.kubernetes.api.model.*;
import io.fabric8.kubernetes.client.CustomResource;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.action.admin.indices.alias.get.GetAliasesRequest;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.admin.indices.settings.get.GetSettingsRequest;
import org.elasticsearch.action.admin.indices.settings.get.GetSettingsResponse;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.client.GetAliasesResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.client.indices.GetIndexResponse;
import org.elasticsearch.cluster.metadata.AliasMetaData;
import org.elasticsearch.common.collect.ImmutableOpenMap;
import org.elasticsearch.common.settings.Settings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

import static cn.newdt.cloud.constant.CloudAppConstant.SysCfgCategory.CONFIGMAP_TEMPLATE;

@Service
@Slf4j
public class ElasticsearchClusterService extends DefaultAppKindService<Elasticsearch> implements ServiceManageOperation{
    public static final String SECRET_USERNAME = "elastic";
    @Autowired
    private AppOperationHandler appOperationHandler;
    @Autowired
    private CloudDatabaseUserService dbUserService;
    @Autowired
    private CloudAppConfigService appConfigService;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private ESUtil esUtil;
    @Autowired
    private ResourceManagerService resourceManagerService;
    @Override
    public AppKind getKind() {
        return AppKind.Elasticsearch;
    }

    @Override
    public boolean nodePolicy() {
        return false;
    }

    @Override
    protected boolean supportIPAM(CloudAppVO app) {
        return false;
    }

    @Override
    public String getIpReservationTarget() {
        return "pod";
    }

    @Override
    public Elasticsearch doInstall(CloudAppVO vo, List<String> ips) throws Exception {
        EsVo es = (EsVo)vo;
        KubeClient kubeClient = clientService.get(vo.getKubeId());
        if (StringUtils.isNotEmpty(vo.getUsername())
                && !dbUserService.findDbUserByName(vo.getId(), vo.getUsername()).isPresent())
            dbUserService.createUser(vo.getId(), vo.getUsername(), vo.getEncryptedPassword(), CloudAppConstant.UserRole.ADMIN);
        // 构造cr对象
        Map<ImageKindEnum, String> imageManifest = appConfigService.getImageManifest(getKind(), vo.getVersion());
        //获取es、filebeat、dump、exporter、ftp镜像
        String esImage = imageManifest.get(ImageKindEnum.MainImage);
        if (StringUtils.isBlank(esImage)){
            throw new CustomException(600, "设置image镜像错误");
        }
        String filebeatImage = imageManifest.getOrDefault(ImageKindEnum.Filebeat,"");
        String exporterImage = imageManifest.getOrDefault(ImageKindEnum.Exporter,"");
        String dumpImage = imageManifest.getOrDefault(ImageKindEnum.Dump,"");
        String ftpImage = imageManifest.getOrDefault(ImageKindEnum.FTP,"");

//        if (StringUtils.isEmpty(vo.getBackupDisk()))
//            vo.setBackupDisk(((EsVo) vo).getDisk());
//        String backupDisk = vo.getBackupDisk();
        String masterAffinityAndTolerations = "";
        String dataAffinityAndTolerations = "";
        String exporterAffinityAndTolerations = "";
        if (vo.getSelector() != null || vo.getToleration() != null || vo.getAntiAffinityRequired()) {
            NodeAffinity nodeAffinity = convertCRNodeAffinity(vo.getSelector(), NodeAffinity.class);
            List<Toleration> tolerations = convertCRTolerations(vo.getToleration(), Toleration.class);
            PodSpec podSpec = new PodSpecBuilder().withTolerations(tolerations).withAffinity(new AffinityBuilder().withNodeAffinity(nodeAffinity).build()).build();
            PodAntiAffinity masterPodAntiAffinity = null;
            PodAntiAffinity dataPodAntiAffinity = null;
            if (vo.getAntiAffinityRequired()) {
                // 强制pod反亲和
                PodAffinityTerm masterPodAffinityTerm = new PodAffinityTermBuilder().withLabelSelector(new LabelSelectorBuilder()
                        .withMatchExpressions(new LabelSelectorRequirementBuilder().withKey("elasticsearch.k8s.elastic.co/statefulset-name")
                                .withOperator("In").withValues(Lists.newArrayList(vo.getCrName() + "-es-masters")).build())
                        .build()).withTopologyKey("kubernetes.io/hostname").build();
                masterPodAntiAffinity = new PodAntiAffinityBuilder().withRequiredDuringSchedulingIgnoredDuringExecution(Lists.newArrayList(masterPodAffinityTerm)).build();
                PodAffinityTerm dataPodAffinityTerm = new PodAffinityTermBuilder().withLabelSelector(new LabelSelectorBuilder()
                        .withMatchExpressions(new LabelSelectorRequirementBuilder().withKey("elasticsearch.k8s.elastic.co/statefulset-name")
                                .withOperator("In").withValues(Lists.newArrayList(vo.getCrName() + "-es-data")).build())
                        .build()).withTopologyKey("kubernetes.io/hostname").build();
                dataPodAntiAffinity = new PodAntiAffinityBuilder().withRequiredDuringSchedulingIgnoredDuringExecution(Lists.newArrayList(dataPodAffinityTerm)).build();
            }
            // podSpec 设置master反亲和性，转为node亲和、容忍、pod反亲和yaml;
            podSpec.getAffinity().setPodAntiAffinity(masterPodAntiAffinity);
            masterAffinityAndTolerations = YamlEngine.marshal(podSpec);
            // podSpec 设置data  反亲和性，转为node亲和、容忍、pod反亲和yaml
            podSpec.getAffinity().setPodAntiAffinity(dataPodAntiAffinity);
            dataAffinityAndTolerations = YamlEngine.marshal(podSpec);
            // 清理pod反亲和设置，转为exporter的 node亲和、容忍yaml
            podSpec.getAffinity().setPodAntiAffinity(null);
            exporterAffinityAndTolerations = YamlEngine.marshal(podSpec);
            if ("{}".equals(masterAffinityAndTolerations.trim()))
                masterAffinityAndTolerations = "";
            if ("{}".equals(dataAffinityAndTolerations.trim()))
                 dataAffinityAndTolerations = "";
            if ("{}".equals(exporterAffinityAndTolerations.trim()))
                exporterAffinityAndTolerations = "";
        }

        ResourceRequirements masterResource = ResourceHelper.getInstance().resourceRequirements(new ResourceDTO() {{
            setCpu(es.getMasterCpu());
            setMemory(es.getMasterMemory());
        }});
        ResourceRequirements dataResource = ResourceHelper.getInstance().resourceRequirements(new ResourceDTO() {{
            setCpu(es.getCpu());
            setMemory(es.getMemory());
        }});

        Map<String, String> values = new HashMap<>();
        values.put("crNm", es.getCrName());
        values.put("namespace", es.getNamespace());
        values.put("esVer", es.getVersion());
        values.put("esImage", esImage);
        values.put("mastersSz", es.getMasterSize() + "");
        values.put("masterXms", (int)(MetricUtil.getLongValueOfUnit(es.getMasterMemory(), 'M') * 0.5) + "m");
        values.put("masterXmx", values.get("masterXms"));
        values.put("mastersRM", masterResource.getRequests().get("memory").toString());
        values.put("mastersRC", masterResource.getRequests().get("cpu").toString());
        values.put("mastersLM", es.getMasterMemory());
        values.put("mastersLC", es.getMasterCpu());
        values.put("mastersStoSz", es.getMasterDisk());
        values.put("dataSz", es.getDataSize() + "");
        values.put("dataXms", (int)(MetricUtil.getLongValueOfUnit(es.getMemory(), 'M') * 0.5) + "m");
        values.put("dataXmx", values.get("dataXms"));
        values.put("dataRM", dataResource.getRequests().get("memory").toString());
        values.put("dataRC", dataResource.getRequests().get("cpu").toString());
        values.put("dataLM", es.getMemory());
        values.put("dataLC", es.getCpu());
        values.put("dataStoSz", es.getDisk());
        values.put("scName", es.getStorageClassName()); // 为""时表示hostpath
        values.put("masterAffinityAndTolerations", YamlEngine.indent(masterAffinityAndTolerations, 8));
        values.put("dataAffinityAndTolerations", YamlEngine.indent(dataAffinityAndTolerations, 8));
        values.put("filebeatImage",filebeatImage);
        values.put("dumpImage",dumpImage);
        values.put("ftpImage", ftpImage);

        String volumeTemplateLabelYaml = Arrays.stream(getKind().labels(vo.getCrName())).map(label -> new StringBuilder().append(StringUtils.leftPad("", 10))
                .append(label.getName()).append(": ").append(label.getValue())
        ).collect(Collectors.joining("\n"));
        volumeTemplateLabelYaml += String.format("\n          %s: %s", CloudAppConstant.CustomLabels.RESOURCE_VERSION, vo.getResourceVersion());
        values.put("volumeTemplateLabelYaml", volumeTemplateLabelYaml);
        // POD custom label yaml
        StringBuilder podCustomLabelSection = new StringBuilder();
        for (Label label : getKind().labels(vo.getCrName())) {
            String labelPairYaml = label.getName() + ": " + label.getValue();
            podCustomLabelSection.append(StringUtils.leftPad("", 10)).append(labelPairYaml).append("\n");
        }
        values.put("podCustomLabelSection", podCustomLabelSection.toString());

        // NAS
        values.put("nfsSubPath", AppUtil.getESBackupRootPath(vo.getNamespace(), vo.getCrName()));
        values.put("backupClaimName", getBackupPvcName(vo));

        for (Map.Entry<String, String> entry : values.entrySet()) {
            if (entry.getValue() == null ||
                    (StringUtils.isBlank(entry.getValue()) && !"scName".equals(entry.getKey()) && !"dumpScName".equals(entry.getKey())
                            && !"affinity".equals(entry.getKey()) && !"masterAffinityAndTolerations".equals(entry.getKey())
                            && !"dataAffinityAndTolerations".equals(entry.getKey()))) {
                throw new CustomException(600, entry.getKey() + " 属性为空");
            }
        }
        // hostpath pv 和 pvc通过label绑定
        if (CSIUtil.isHostpath(es.getCsiType())){
            createHostPathStorage(es, kubeClient);
            Label[] pvLabels = new Label[]{
                    new Label("common.k8s.elastic.co/type", "elasticsearch"),
                    new Label("elasticsearch.k8s.elastic.co/cluster-name", vo.getCrName()),
                    new Label("elasticsearch.k8s.elastic.co/namespace", vo.getNamespace()),
                    new Label("elasticsearch.k8s.elastic.co/statefulset-name", vo.getCrName() + "-es-masters"),
            };
            // 保证宕机后Operator 建立的pvc 不会绑定到其他应用的pv
            LabelSelector pvSelector = new LabelSelectorBuilder().withMatchLabels(Arrays.stream(pvLabels).collect(Collectors.toMap(Label::getName, Label::getValue))).build();
            PersistentVolumeClaimSpec pvcTemplate = new PersistentVolumeClaimSpecBuilder().withSelector(pvSelector).build();
            values.put("masterPVNameSelector", YamlEngine.indent(YamlEngine.marshal(pvcTemplate), 8));

            pvSelector.getMatchLabels().put("elasticsearch.k8s.elastic.co/statefulset-name",vo.getCrName() + "-es-data");
            pvcTemplate = new PersistentVolumeClaimSpecBuilder().withSelector(pvSelector).build();
            values.put("dataPVNameSelector", YamlEngine.indent(YamlEngine.marshal(pvcTemplate), 8));
        } else {
            values.put("dataPVNameSelector", "");
            values.put("masterPVNameSelector", "");
        }
        Elasticsearch cr = YamlEngine.unmarshal(replace(YAML_ES, values), Elasticsearch.class);

        // 创建secret 登陆es, 在创建应用前
        Map<String, String> secretValues = new HashMap<>();
        secretValues.put("name", getSecretName(es.getCrName()));
        secretValues.put("crNm", es.getCrName());
        secretValues.put("namespace", es.getNamespace());
        secretValues.put("password", Base64.getEncoder().encodeToString(es.getPassword().getBytes()));
        secretValues.put("username", es.getUsername());
        kubeClient.applyYaml(replace(YAML_SECRET, secretValues), es.getNamespace());

        // 创建configmap 在创建应用前
        String configmap = sysConfigService.findOne(CONFIGMAP_TEMPLATE, getKind().getKind());
        kubeClient.applyYaml(replace(configmap, ImmutableMap.of("namespace", es.getNamespace())), es.getNamespace());

        // 创建 exporter Deployment
        Map<String, String> exporterValues = new HashMap<>();
        exporterValues.put("name",getDeployName(es.getCrName()));
        exporterValues.put("crNm",es.getCrName());
        exporterValues.put("password",es.getPassword());
        exporterValues.put("image", exporterImage);
        exporterValues.put("svcName", getExporterSvcName(es.getCrName()));
        exporterValues.put("affinityAndTolerations", YamlEngine.indent(exporterAffinityAndTolerations,6));
//        exporterValues.put("esApiVersion", "elasticsearch.k8s.elastic.co/v1");
//        exporterValues.put("esKind", "Elasticsearch");
//        exporterValues.put("esName", es.getCrName());

        kubeClient.applyYaml(replace(YAML_EXPORTER, exporterValues), es.getNamespace());
        // 创建 exporter Service
        kubeClient.applyYaml(replace(YAML_EXPORTER_SVC, exporterValues), es.getNamespace());
        // 创建nodeport service
//        Map<String, String> labelMap = Arrays.stream(getKind().labelOfPod(es)).collect(Collectors.toMap(e->e.getName(), e->e.getValue()));
//        kubeClient.createService(getSvcName(es.getCrName()), vo.getNamespace(), es.getWritePort(), AppKind.ElasticSearch.getDbPort(), labelMap);
//        GenericKubernetesResource cr = kubeClient.applyCrYaml(context, replace(YAML_ES, values), vo.getNamespace());
        // delete hostpath pv manually otherwise can not create es with same name


        //2 获取配置的configmap名称，判断在当前数据空间内是否已经存在了
//        String configMapYaml = sysConfigService.findOne(OPERATOR_CONFIG, "Elasticsearch.config");
//        String configMapName = "es-filebeat-cm";
//        //判断是否已经存在cm
//        ConfigMap execConfigMap = kubeClient.getConfigMap(configMapName, vo.getNamespace());
//        if(Objects.isNull(execConfigMap)){
//            // 3 若不存在，则创建
//            //替换yaml中的属性，大小写敏感，故不会替换掉大写的环境变量placeholder
//            Map<String, String> param = new HashMap<>(12);
//            param.put("namespace", vo.getNamespace());
//            param.put("name", configMapName);
//            param.put("es_host", esUtil.getEsIp() + ":" + esUtil.getEsPort());
//            param.put("es_username", esUtil.getEsUsername());
//            param.put("es_pwd", esUtil.getEsPassword());
//            param.put("es_protocol", esUtil.getProtocol());
//            configMapYaml = YamlUtil.evaluateTemplate(configMapYaml, param);
//            kubeClient.applyYaml(configMapYaml, vo.getNamespace());
//        }

//        // 创建NAS PVC
//        String finBackupStorageClassName = getBackupStorageClassName(vo.getBackupCsiType());
//        vo.setBackupStorageclassname(finBackupStorageClassName);
//        PersistentVolumeClaim pvc = KubeClientUtil.provideSharedCsiPVC(getBackupPvcName(vo), vo.getNamespace(), backupDisk,
//                Label.toMap(getKind().labels(vo.getCrName())), // required by NAS
//                vo.getBackupStorageclassname());
//        kubeClient.createBatch(pvc.getKind(), pvc);

        return cr;
    }

    private static String getBackupPvcName(CloudAppVO vo) {
        return "es-dump-data-" + vo.getCrName();
    }

    private void createHostPathStorage(EsVo es, KubeClient kubeClient) {
        List<String> dataPVNames = new ArrayList<>();
        List<String> masterPVNames = new ArrayList<>();
        List<PersistentVolume> pvsNeedRollback = new ArrayList<>();
        List<PersistentVolumeClaim> pvcs = new ArrayList<>();
        String namespace = es.getNamespace();
        String crName = es.getCrName();
        if (CSIUtil.isHostpath(es.getCsiType())) {
            String hostpathRoot = es.getHostpathRoot();
            // master 节点pv
            Label[] labels = new Label[]{
                    new Label(CloudAppConstant.CustomLabels.APP, "elasticsearch"),
                    new Label("common.k8s.elastic.co/type", "elasticsearch"),
                    new Label("elasticsearch.k8s.elastic.co/cluster-name", crName),
                    new Label("elasticsearch.k8s.elastic.co/namespace", namespace),
                    new Label("elasticsearch.k8s.elastic.co/statefulset-name", crName + "-es-masters"),
            };
            String pvSelectorLabel = "es-pv";
            String masterDisk = es.getMasterDisk();
            List<PersistentVolume> pvs = KubeClientUtil.provideHostpathPV("elasticsearch-data-" + namespace,
                    crName + "-es-masters", crName, namespace, masterDisk, hostpathRoot, es.getMasterSize(),
                    0, labels, pvSelectorLabel);
            pvsNeedRollback.addAll(pvs);
            pvcs.addAll(KubeClientUtil.provideHostpathPVC("elasticsearch-data", crName + "-es-masters", namespace, masterDisk, labels, pvs, pvSelectorLabel, 0));
            masterPVNames.addAll(pvs.stream().map(pv -> pv.getMetadata().getName()).collect(Collectors.toList()));
            // data 节点
            labels = new Label[]{
                    new Label(CloudAppConstant.CustomLabels.APP, "elasticsearch"),
                    new Label("common.k8s.elastic.co/type", "elasticsearch"),
                    new Label("elasticsearch.k8s.elastic.co/cluster-name", crName),
                    new Label("elasticsearch.k8s.elastic.co/namespace", namespace),
                    new Label("elasticsearch.k8s.elastic.co/statefulset-name", crName + "-es-data"),
            };
            String dataDisk = es.getDisk();
            pvs = KubeClientUtil.provideHostpathPV("elasticsearch-data-" + namespace, crName + "-es-data", crName, namespace, dataDisk, hostpathRoot, es.getMasterSize(),
                    0, labels, pvSelectorLabel);
            pvsNeedRollback.addAll(pvs);
            pvcs.addAll(KubeClientUtil.provideHostpathPVC("elasticsearch-data", crName + "-es-data", namespace, dataDisk, labels, pvs, pvSelectorLabel, 0));
            dataPVNames.addAll(pvs.stream().map(pv -> pv.getMetadata().getName()).collect(Collectors.toList()));
        }

//        if (CSIUtil.isHostpath(es.getBackupCsiType())) {
//            Label[] labels = new Label[]{
//                    new Label(CloudAppConstant.CustomLabels.APP, "elasticsearch"),
//                    new Label("common.k8s.elastic.co/type", "elasticsearch"),
//                    new Label("elasticsearch.k8s.elastic.co/cluster-name", crName),
//                    new Label("elasticsearch.k8s.elastic.co/namespace", namespace),
//                    new Label("elasticsearch.k8s.elastic.co/statefulset-name", crName + "-es-masters"),
//            };
//            String hostpathRoot = es.getBackupHostpathRoot();
//            String backupDisk = es.getBackupDisk();
//            List<PersistentVolume> pvs = KubeClientUtil.provideHostpathPV("es-dump-data-" + namespace, crName + "-es-data", crName, namespace, backupDisk, hostpathRoot, es.getDataSize(),
//                    0, labels, "es-pv");
//            pvsNeedRollback.addAll(pvs);
//            pvcs.addAll(KubeClientUtil.provideHostpathPVC("es-dump-data", crName + "-es-data", namespace, backupDisk, labels, pvs, "es-pv", 0));
//        }

        if (!pvsNeedRollback.isEmpty()) {
            kubeClient.createBatch(pvsNeedRollback.get(0).getKind(), pvsNeedRollback.toArray(new PersistentVolume[0]));
        }
        if (!pvcs.isEmpty()) {
            kubeClient.createBatch(pvcs.get(0).getKind(), pvcs.toArray(new PersistentVolumeClaim[0]));
        }
    }


    private String getExporterSvcName(String crName) {
        return crName;
    }

    @Override
    protected void setInstallExtData(CloudAppVO vo) {
        EsVo es = (EsVo)vo;
        Map<String, Object> defaultIndexSettings = es.getDefaultIndexSettings();
        vo.appendExtInstallKVData("password", vo.getPassword());
        vo.appendExtInstallKVData("defaultIndexSettings", defaultIndexSettings);
    }

    private String getDeployName(String crName) {
        return crName + "-es-exporter";
    }

    private String getSecretName(String crName) {
        return crName + "-es-elastic-user";
    }

    @Override
    public void deleteCrControlledResources(CloudApp app) {
        log.info("Rollback installation side effect");
        KubeClient client = clientService.get(app.getKubeId());
        client.deleteSecret(getSecretName(app.getCrName()), app.getNamespace());
        client.deleteService(AppKind.Elasticsearch.getWriteServiceName(app.getCrName(), null), app.getNamespace());
        client.deleteDeployment(getDeployName(app.getCrName()), app.getNamespace());
        client.deleteService(getExporterSvcName(app.getCrName()), app.getNamespace()); // delete exporter svc
    }

    @Override
    public Class<? extends OpsPostProcessor> getProcessorClass(ActionEnum action) {
        switch (action) {
            case CREATE: return ElasticsearchClusterInstallWatch.class;
            case UPDATE: return ElasticsearchClusterUpdateWatch.class;
            case SCALE_OUT: return ElasticsearchClusterScaleOutWatch.class;
            case SCALE_IN: return ElasticsearchClusterScaleInWatch.class;
            case UPGRADE: return ElasticsearchClusterVersionChangeWatch.class;
            case CREATE_SERVICE:
            case UPDATE_SERVICE:
            case DELETE_SERVICE:
                return ElasticsearchClusterSvcWatch.class;
            default:
                return super.getProcessorClass(action);
        }
    }

    @Override
    protected void completeInstanceProperty(List<AppInstanceVO> instances, int appId) {

    }


    @Override
    public void update(Integer id, OverrideSpec overrideSpec) throws Exception {
        if (overrideSpec instanceof ElasticsearchClusterService.EsOverrideSpec) {
            EsResourceDTO patch = new EsResourceDTO();
            patch.setDisk(((ElasticsearchClusterService.EsOverrideSpec) overrideSpec).getMasterDisk());
            patch.setMemory(((ElasticsearchClusterService.EsOverrideSpec) overrideSpec).getMasterMemory());
            patch.setCpu(((ElasticsearchClusterService.EsOverrideSpec) overrideSpec).getMasterCpu());
            patch.setDataCpu(((ElasticsearchClusterService.EsOverrideSpec) overrideSpec).getDataCpu());
            patch.setDataMemory(((ElasticsearchClusterService.EsOverrideSpec) overrideSpec).getDataMemory());
            patch.setDataDisk(((ElasticsearchClusterService.EsOverrideSpec) overrideSpec).getDataDisk());
            patch.setBackupDisk(overrideSpec.getBackupDisk());
            patch.setAppId(id);
            patch.setId(id);
            update(patch);
        }
    }

    @Override
    @Transactional
    public void update(ResourceDTO patch) throws Exception {
        CloudApp app = appService.get(patch.getAppId());
        KubeClient kubeClient = clientService.get(app.getKubeId());
        Elasticsearch crInK8s = kubeClient.listCustomResource(Elasticsearch.class, app.getCrName(), app.getNamespace());
        Elasticsearch crInDBStore = YamlEngine.unmarshal(app.getCr(), Elasticsearch.class);
        // 保持更新前的cr状态，回滚时不能回滚存储
        Elasticsearch unmarshall = YamlEngine.unmarshal(YamlEngine.marshal(crInDBStore), Elasticsearch.class);
        Elasticsearch.ElasticsearchNodeSet[] nodeSets = crInK8s.getSpec().getNodeSets();
        Elasticsearch.ElasticsearchNodeSet masterNode = nodeSets[0];
        Container elasticsearchMaster = masterNode.getPodTemplate().getSpec().getContainers().stream().filter(c -> c.getName().equals("elasticsearch"))
                .findFirst().orElseThrow(() -> new CustomException(600, "elasticsearch container was not found"));
        PersistentVolumeClaim masterPvc = masterNode.getVolumeClaimTemplates().stream().filter(cl -> cl.getMetadata().getName().equals("elasticsearch-data"))
                .findFirst().orElseThrow(() -> new CustomException(600, "volumeTemplate was not found in spec"));

        mergeResource(elasticsearchMaster.getResources(), ResourceHelper.getInstance().resourceRequirements(patch));
        mergeResource(masterPvc.getSpec().getResources().getRequests(), "storage", patch.getDisk());
        elasticsearchMaster.getEnv().get(0).setValue("-Xms" + (int)(MetricUtil.getLongValueOfUnit(patch.getMemory(), 'M') * 0.5) + "m -Xmx" + (int)(MetricUtil.getLongValueOfUnit(patch.getMemory(), 'M') * 0.5) + "m");

        Elasticsearch.ElasticsearchNodeSet dataNode = nodeSets[1];
        if (patch instanceof EsResourceDTO) {
            Container elasticsearchData = dataNode.getPodTemplate().getSpec().getContainers().stream().filter(c -> c.getName().equals("elasticsearch"))
                    .findFirst().orElseThrow(() -> new CustomException(600, "invalid cr run, elasticsearch container was not found"));
            PersistentVolumeClaim dataPvc = dataNode.getVolumeClaimTemplates().stream().filter(cl -> cl.getMetadata().getName().equals("elasticsearch-data"))
                    .findFirst().orElseThrow(() -> new CustomException(600, "elasticsearch-data volumeTemplate was not found in spec"));


            EsResourceDTO patchEs = (EsResourceDTO) patch;
            mergeResource(elasticsearchData.getResources(),
                    ResourceHelper.getInstance().resourceRequirements(new ResourceDTO(){{
                        setCpu(patchEs.getDataCpu());
                        setMemory(patchEs.getDataMemory());
                    }}));
            mergeResource(dataPvc.getSpec().getResources().getRequests(), "storage", patchEs.getDataDisk());

//            if (StringUtils.isNotEmpty(patchEs.getBackupDisk())) {
//                PersistentVolumeClaim backupPvc = dataNode.getVolumeClaimTemplates().stream().filter(cl -> cl.getMetadata().getName().equals("es-dump-data"))
//                        .findFirst().orElseThrow(() -> new CustomException(600, "es-dump-data volumeTemplate was not found in spec"));
//                mergeResource(backupPvc.getSpec().getResources().getRequests(), "storage", patchEs.getBackupDisk());
//            }
            elasticsearchData.getEnv().get(0).setValue("-Xms" + (int)(MetricUtil.getLongValueOfUnit(patchEs.getDataMemory(), 'M') * 0.5) + "m -Xmx" + (int)(MetricUtil.getLongValueOfUnit(patchEs.getDataMemory(), 'M') * 0.5) + "m");

        }

        // 更新cr run yaml
        crInDBStore.setSpec(crInK8s.getSpec());

        String applyYaml = YamlEngine.marshal(crInDBStore);
        unmarshall.getSpec().getNodeSets()[0].setVolumeClaimTemplates(masterNode.getVolumeClaimTemplates());
        unmarshall.getSpec().getNodeSets()[1].setVolumeClaimTemplates(dataNode.getVolumeClaimTemplates());
        OpLogContext.instance().CR(applyYaml, YamlEngine.marshal(unmarshall));
        appService.callScheduler(app, applyYaml, patch, ActionEnum.UPDATE, getProcessorClass(ActionEnum.UPDATE));
        kubeClient.updateCustomResource(crInK8s, Elasticsearch.class);

    }

    private void mergeResource(ResourceRequirements cur, ResourceRequirements expect) {
        expect.getRequests().keySet().stream().forEach(key -> {
            mergeResource(cur.getRequests(), key, Optional.ofNullable(expect.getRequests().get(key))
                    .map(q->q.toString()).orElse(null));
        });
        expect.getLimits().keySet().stream().forEach(key -> {
            mergeResource(cur.getLimits(), key, Optional.ofNullable(expect.getLimits().get(key))
                    .map(q->q.toString()).orElse(null));
        });
    }

    private void mergeResource(Map<String, Quantity> resource, String key, String quantity) {
        if (StringUtils.isNotEmpty(quantity)) {
            resource.put(key, new Quantity(quantity));
        }
    }

    @Override
    public void scale(int id, OverrideSpec spec, ActionEnum action) throws Exception {
        EsOverrideSpec esSpec = (EsOverrideSpec) spec;
        scale(id, esSpec.getMasterSize(), esSpec.getDataSize());
    }


    /**
     * 扩缩容统一接口
     * @param appId 应用id
     * @param masterSize 变化后的数量
     * @param dataSize 变化后的数量
     */
    @Transactional
    void scale(int appId, Integer masterSize, Integer dataSize) throws Exception {
        CustPreconditions.checkState(masterSize > 0, "master节点数量不能<1");
        CustPreconditions.checkState(dataSize> 0, "data节点数量不能<1");

        CustPreconditions.checkNotNull(appId, "appId not be null!");
        CloudApp app = appService.get(appId);
        Elasticsearch cr = clientService.get(
                app.getKubeId()).listCustomResource(Elasticsearch.class, app.getCrName(), app.getNamespace());
        String appCr = app.getCr();
        Elasticsearch.ElasticsearchNodeSet[] nodeSets = cr.getSpec().getNodeSets();
        int curMasterSize = nodeSets[0].getCount();
        int diffMasterSize = masterSize - curMasterSize;
        int curSpareSize = nodeSets[1].getCount();
        int diffSpareSize = dataSize - curSpareSize;
        if (diffMasterSize ==0 && diffSpareSize ==0 ){
            throw new CustomException(600, "master和data节点数量都未变更");
        }

        if (diffSpareSize < 0)
            allowScaleInDataNodes(appId, dataSize);

        // 预留ip成功标识
        Map<String, Object> map = new HashMap<>();
        map.put("app", app);
        Elasticsearch elasticFromAppCr;
        Label[] labels = getKind().labels(app.getCrName());
        //根据label获取到所有的pod
        List<PodDTO> podDTOS = clientService.get(
                app.getKubeId()).listPod(app.getNamespace(), labels);
        List<String> podList = podDTOS.stream().map(e -> e.getPodName()).collect(Collectors.toList());
        map.put("podList",podList);

        //扩容
        if(diffMasterSize > 0 || diffSpareSize > 0) {
            map.put("type", ActionEnum.SCALE_OUT);
            int newDataCount = nodeSets[1].getCount();
            cr.getSpec().getNodeSets()[0].setCount(masterSize);
            cr.getSpec().getNodeSets()[1].setCount(dataSize);
            elasticFromAppCr = YamlEngine.unmarshal(appCr, Elasticsearch.class);
            elasticFromAppCr.getSpec().getNodeSets()[0].setCount(masterSize);
            elasticFromAppCr.getSpec().getNodeSets()[1].setCount(dataSize);
            Quantity storage = elasticFromAppCr.getSpec().getNodeSets()[1]
                    .getVolumeClaimTemplates().get(0).getSpec().getResources().getRequests().get("storage");
            String dataDisk = storage.getAmount()+"Gi";
            app.setCrRun(YamlEngine.marshal(elasticFromAppCr));
            String storageClassName = app.getStorageClassName();
            if (storageClassName.equals("")){
                if (diffMasterSize != 0){
                    buildEsPvAndPvc(app,"master", curMasterSize, masterSize, app.getDisk(), dataDisk);
                }
                if (diffSpareSize != 0){
                    buildEsPvAndPvc(app,"data", curSpareSize, newDataCount,app.getDisk(),dataDisk);
                }
            }

            String applyYaml = YamlEngine.marshal(cr);
            OpLogContext.instance().YAML("CR", applyYaml, app.getCr());
            appService.callScheduler(app, applyYaml, map, ActionEnum.SCALE_OUT, ElasticsearchClusterScaleOutWatch.class);
        } else {
            cr.getSpec().getNodeSets()[0].setCount(masterSize);
            cr.getSpec().getNodeSets()[1].setCount(dataSize);
            elasticFromAppCr = YamlEngine.unmarshal(appCr, Elasticsearch.class);
            elasticFromAppCr.getSpec().getNodeSets()[0].setCount(masterSize);
            elasticFromAppCr.getSpec().getNodeSets()[1].setCount(dataSize);
            app.setCrRun(YamlEngine.marshal(elasticFromAppCr));

            String applyYaml = YamlEngine.marshal(cr);
            OpLogContext.instance().YAML("CR", applyYaml, app.getCr());
            appService.callScheduler(app, applyYaml, map, ActionEnum.SCALE_IN, ElasticsearchClusterScaleInWatch.class);
        }
        clientService.get(app.getKubeId()).updateCustomResource(cr, Elasticsearch.class);
    }

    /*
    * pvc删除后相应的pv状态需要设置为可绑定状态
    * */
    public void updatePvClaimRef(CloudApp app){
        List<PersistentVolume> pvByLabels = new ArrayList<>();
        Label[] labels = new Label[]{
                new Label("common.k8s.elastic.co/type", "elasticsearch"),
                new Label("elasticsearch.k8s.elastic.co/namespace", app.getNamespace()),
                new Label("elasticsearch.k8s.elastic.co/cluster-name", app.getCrName()),
        };
        Map<String, String> labelMap = Arrays.stream(labels).collect(Collectors.toMap(Label::getName, Label::getValue));
        pvByLabels  = clientService.get(app.getKubeId()).listPv(labelMap).getItems();
        List<PersistentVolume> releasedPv = pvByLabels.stream().filter(e -> e.getStatus().getPhase().equals("Released")).collect(Collectors.toList());
        for (int i = 0; i < releasedPv.size(); i++) {
            releasedPv.get(i).getSpec().setClaimRef(null);
            clientService.get(app.getKubeId()).updatePvClaimRef(releasedPv.get(i), app.getNamespace());
        }

    }

    /**
     * 检查索引副本数是否大于缩容后节点个数
     * @throws CustomException if index setting was not compatible with specified data node count
     */
    private void allowScaleInDataNodes(int appId, final int dataNodeCount) {
        GetIndexResponse getIndexResponse = catIndices(appId);
        Map<String, Settings> settings = getIndexResponse.getSettings();
        String collect = settings.keySet().stream()
                // node count need great then replica setting
                .filter(k -> dataNodeCount <= settings.get(k).getAsInt("index.number_of_replicas", 0))
                .map(k -> k + ":" + settings.get(k).getAsInt("index.number_of_replicas", 0))
                .collect(Collectors.joining("\n"));
        if (StringUtils.isNotBlank(collect)) {
            throw new CustomException(600, "存在索引副本数超过缩容后data节点数,索引副本数不得超过data节点数", collect);
        }

    }

    private int getDataNodesCount(int appId) {
        CloudApp app = appService.get(appId);
        String yaml = app.getCr();
        Elasticsearch es = YamlEngine.unmarshal(yaml, Elasticsearch.class);
        return Arrays.stream(es.getSpec().getNodeSets()).filter(this::asDataNodes).findAny()
                .orElseThrow(() -> new IllegalStateException("can not find data node config"))
                .getCount();
    }

    private boolean asDataNodes(Elasticsearch.ElasticsearchNodeSet ns) {
        return false; // todo
    }

    public GetIndexResponse catIndices(int appId) {
        CloudApp app = appService.get(appId);
        int kubeId = app.getKubeId();
        KubeClient kubeClient = clientService.get(kubeId);
        //获取写连接 service
        AppDBVO appDBVO = accessManagementService
                .getFirstWriteServiceAddressByAppIdAndKubeId(appId, kubeId);
        String ip = appDBVO.getIp();
        Secret secret = kubeClient.getSecret(app.getNamespace(), getSecretName(app.getCrName()));
        String password = new String(Base64.getDecoder().decode(secret.getData().get(SECRET_USERNAME).getBytes()));


        try (RestHighLevelClient client = new ESUtil().getClient(ip, appDBVO.getPort(), SECRET_USERNAME, password)){
            GetIndexRequest getIndexRequest = new GetIndexRequest("*");
            getIndexRequest.indicesOptions(IndicesOptions.LENIENT_EXPAND_OPEN_CLOSED);
            GetIndexResponse resp = client.indices().get(getIndexRequest, RequestOptions.DEFAULT);
            return resp;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    //修改pv的亲和属性为实际node
    public void updatePvAffinity(CloudApp app) {
        //获取一个实例的node
        KubeClient client = clientService.get(app.getKubeId());
        //生成pvc和nodeName名字的map，key是pvc名称，value为nodename
        Map<String, String> pvcAndNodeMap = new HashMap<>();
        List<PodDTO> pods = client.listPod(app.getNamespace(), AppUtil.getPodLabel(app));
        for (PodDTO pod : pods) {
            List<Volume> volumes = pod.getPod().getSpec().getVolumes();
            for (Volume volume : volumes) {
                // 数据存储PV和备份存储PV都需要更新
                if("elasticsearch-data".equalsIgnoreCase(volume.getName())
                        || "es-dump".equalsIgnoreCase(volume.getName())){
                    pvcAndNodeMap.put(volume.getPersistentVolumeClaim().getClaimName(), pod.getPod().getSpec().getNodeName());
                }
            }
        }
        //获取master和data的pv
        List<PersistentVolume> pvByLabels = new ArrayList<>();
        Label[] labels = new Label[]{
                new Label("common.k8s.elastic.co/type", "elasticsearch"),
                new Label("elasticsearch.k8s.elastic.co/cluster-name", app.getCrName()),
                new Label("elasticsearch.k8s.elastic.co/namespace", app.getNamespace()),
        };
        Map<String, String> labelMap = Arrays.stream(labels).collect(Collectors.toMap(Label::getName, Label::getValue));
        pvByLabels  = client.listPv(labelMap).getItems();
        //遍历所有pv修改亲和
        for(int i = 0;i < pvByLabels.size();i++){
            VolumeNodeAffinity nodeAffinity = pvByLabels.get(i).getSpec().getNodeAffinity();
            List<String> values = new ArrayList<>();
            String nodeName = pvcAndNodeMap.get(pvByLabels.get(i).getSpec().getClaimRef().getName());
            values.add(nodeName);
            if(null == nodeAffinity){
                //没有亲和
                VolumeNodeAffinity volumeNodeAffinity = new VolumeNodeAffinity();
                NodeSelector required = new NodeSelector();
                List<NodeSelectorTerm> nodeSelectorTerms = new ArrayList<>();
                NodeSelectorTerm nodeSelectorTerm = new NodeSelectorTerm();
                List<NodeSelectorRequirement> matchExpressions = new ArrayList<>();
                NodeSelectorRequirement nodeSelectorRequirement = new NodeSelectorRequirement();
                nodeSelectorRequirement.setKey("kubernetes.io/hostname");
                nodeSelectorRequirement.setOperator("In");
                nodeSelectorRequirement.setValues(values);

                matchExpressions.add(nodeSelectorRequirement);

                nodeSelectorTerm.setMatchExpressions(matchExpressions);

                nodeSelectorTerms.add(nodeSelectorTerm);

                required.setNodeSelectorTerms(nodeSelectorTerms);

                volumeNodeAffinity.setRequired(required);

                pvByLabels.get(i).getSpec().setNodeAffinity(volumeNodeAffinity);
            }else{
                // todo 此部分可能不需要，NodeAffinity 一旦设置即不可更改
                //有亲和
                pvByLabels.get(i).getSpec().getNodeAffinity().getRequired().getNodeSelectorTerms().get(0).getMatchExpressions().get(0).setValues(values);
            }
            clientService.get(app.getKubeId()).updatePv(pvByLabels.get(i));
        }
    }

    public void buildEsPvAndPvc(CloudApp app,String type,int originalNum, int targetNum,String masterDisk,String dataDisk){
        List<PersistentVolume> pvByLabels = new ArrayList<>();
        List<PersistentVolumeClaim> pvcByLabels = new ArrayList<>();
        //通过labels获取到所有的pv
        Label[] labels = new Label[]{
                new Label("common.k8s.elastic.co/type", "elasticsearch"),
                new Label("elasticsearch.k8s.elastic.co/namespace", app.getNamespace()),
                new Label("elasticsearch.k8s.elastic.co/cluster-name", app.getCrName()),
        };
        Map<String, String> labelMap = Arrays.stream(labels).collect(Collectors.toMap(Label::getName, Label::getValue));
        pvByLabels  = clientService.get(app.getKubeId()).listPv(labelMap).getItems();
        pvcByLabels = clientService.get(app.getKubeId()).listPvc(app.getNamespace(), labelMap).getItems();
        updatePvClaimRef(app);
        //获取到已有节点的hostpath
        PersistentVolume pv = clientService.get(app.getKubeId()).getPv(pvByLabels.get(0).getMetadata().getName());
        String hostpathRoot = pv.getSpec().getHostPath().getPath();
        String hostpath = hostpathRoot.split("/" + app.getNamespace())[0];
        List<PersistentVolume> pvsNeedRollback = new ArrayList<>();
        List<PersistentVolumeClaim> pvcs = new ArrayList<>();
        Map<String, String> pvcLabelMap = new HashMap<>();
        List<PersistentVolume> newPvsNeedCreate = new ArrayList<>();
        List<PersistentVolumeClaim> newPvcsNeedCreate = new ArrayList<>();
        Label[] pvLabels = null;
        if (type.equals("master")){
            // master 节点pv
            Label[] labelsMaster = new Label[] {
                    new Label("common.k8s.elastic.co/type", "elasticsearch"),
                    new Label("elasticsearch.k8s.elastic.co/cluster-name", app.getCrName()),
                    new Label("elasticsearch.k8s.elastic.co/namespace", app.getNamespace()),
                    new Label("elasticsearch.k8s.elastic.co/statefulset-name", app.getCrName() + "-es-masters"),
            };
            pvcLabelMap = Arrays.stream(labelsMaster).collect(Collectors.toMap(Label::getName, Label::getValue));
            for (int i = originalNum; i < targetNum; i++) {
                String pvName = String.format("elasticsearch-data-%s-%s-es-masters-%d", app.getNamespace(), app.getCrName(), i);
                String pvcName = String.format("elasticsearch-data-%s-es-masters-%d", app.getCrName(), i);
                String podName = app.getCrName() + "-es-masters-" + i;
                pvLabels = new Label[] {
                        new Label("es-pv", pvName),
                        new Label("common.k8s.elastic.co/type", "elasticsearch"),
                        new Label("elasticsearch.k8s.elastic.co/cluster-name", app.getCrName()),
                        new Label("elasticsearch.k8s.elastic.co/namespace", app.getNamespace()),
                        new Label("elasticsearch.k8s.elastic.co/statefulset-name", app.getCrName() + "-es-masters"),
                };
                String[] pathNames = {hostpath, app.getNamespace(), app.getCrName(), podName};
                pvsNeedRollback.add(MongoUtil.createHostPathPv(pvName, String.join("/", pathNames), app.getDisk(), pvLabels));
                pvcs.add(MongoUtil.buildHostPathPvc(pvcName,app.getNamespace(), app.getDisk(), pvcLabelMap, Collections.singletonMap(pvLabels[0].getName(), pvLabels[0].getValue())));
            }
            List<String> pvcNamesByPv = getPvcNamesByPv(pvByLabels, app);
            List<PersistentVolumeClaim> isDeletedPvcList = new ArrayList<>();
            Map<String, String> finalPvcLabelMap = pvcLabelMap;
            Label[] finalPvLabels = pvLabels;
            List<String> pvcNameByLabels = pvcByLabels.stream().map(e -> e.getMetadata().getName()).collect(Collectors.toList());
            isDeletedPvcList = pvcNamesByPv.stream().filter(e -> !pvcNameByLabels.contains(e))
                    .map(name -> {
                        String pvname = name.replace("elasticsearch-data-","elasticsearch-data-"+app.getNamespace()+"-");
                        if (pvname.contains("master")){
                            return MongoUtil.buildHostPathPvc(name, app.getNamespace(), app.getDisk(), finalPvcLabelMap, Collections.singletonMap(finalPvLabels[0].getName(), pvname));
                        }else {
                            return MongoUtil.buildHostPathPvc(name, app.getNamespace(), dataDisk, finalPvcLabelMap, Collections.singletonMap(finalPvLabels[0].getName(), pvname));
                        }
                    }).collect(Collectors.toList());

            createBatch(app,isDeletedPvcList);
            //现在这里的pvcByLabels是未扩容之前被删除的pvc重新被新建
            pvcByLabels = clientService.get(app.getKubeId()).listPvc(app.getNamespace(), labelMap).getItems();
            List<String> pvNameByLabels = pvByLabels.stream().map(e -> e.getMetadata().getName()).collect(Collectors.toList());
            List<String> pvsNameNeedRollback = pvsNeedRollback.stream().map(e -> e.getMetadata().getName()).collect(Collectors.toList());
            List<String> newPvsName = new ArrayList<>();
            for (int i = 0; i < pvsNameNeedRollback.size(); i++) {
                if (!pvNameByLabels.contains(pvsNameNeedRollback.get(i))){
                    newPvsName.add(pvsNameNeedRollback.get(i));
                }
            }
            newPvsNeedCreate = pvsNeedRollback.stream().filter(e -> newPvsName.stream().anyMatch(pvcname -> e.getMetadata().getName().contains(pvcname))).collect(Collectors.toList());

            List<PersistentVolume> allPvs = new ArrayList<>();
            List<String> hopePvcNames = new ArrayList<>();
            if (Objects.nonNull(newPvsNeedCreate)){
                allPvs.addAll(newPvsNeedCreate);
            }
            allPvs.addAll(pvByLabels);
            hopePvcNames = getPvcNamesByPv(allPvs, app);
            List<String> newPvcNameByLabels = pvcByLabels.stream().map(e -> e.getMetadata().getName()).collect(Collectors.toList());
            newPvcsNeedCreate = hopePvcNames.stream().filter(e -> !newPvcNameByLabels.contains(e))
                    .map(name -> {
                        String pvname = name.replace("elasticsearch-data-","elasticsearch-data-"+app.getNamespace()+"-");
                        return MongoUtil.buildHostPathPvc(name, app.getNamespace(), app.getDisk(), finalPvcLabelMap, Collections.singletonMap(finalPvLabels[0].getName(), pvname));
                    }).collect(Collectors.toList());
            createPv(app,newPvsNeedCreate);
            createBatch(app,newPvcsNeedCreate);
        }else if (type.equals("data")){
            // data 节点
            labels = new Label[] {
                    new Label("common.k8s.elastic.co/type", "elasticsearch"),
                    new Label("elasticsearch.k8s.elastic.co/cluster-name", app.getCrName()),
                    new Label("elasticsearch.k8s.elastic.co/namespace", app.getNamespace()),
                    new Label("elasticsearch.k8s.elastic.co/statefulset-name", app.getCrName() + "-es-data"),
            };
            pvcLabelMap = Arrays.stream(labels).collect(Collectors.toMap(Label::getName, Label::getValue));
            for (int i = originalNum; i < targetNum; i++) {
                String pvName = String.format("elasticsearch-data-%s-%s-es-data-%d", app.getNamespace(), app.getCrName(), i);
                String pvcName = String.format("elasticsearch-data-%s-es-data-%d", app.getCrName(), i);
                String podName = app.getCrName() + "-es-data-" + i;
                pvLabels = new Label[] {
                        new Label("es-pv", pvName),
                        new Label("common.k8s.elastic.co/type", "elasticsearch"),
                        new Label("elasticsearch.k8s.elastic.co/cluster-name", app.getCrName()),
                        new Label("elasticsearch.k8s.elastic.co/namespace", app.getNamespace()),
                        new Label("elasticsearch.k8s.elastic.co/statefulset-name", app.getCrName() + "-es-data"),
                };
                String[] pathNames = {hostpath, app.getNamespace(), app.getCrName(), podName};
                pvsNeedRollback.add(MongoUtil.createHostPathPv(pvName, String.join("/", pathNames), dataDisk, pvLabels));
                pvcs.add(MongoUtil.buildHostPathPvc(pvcName, app.getNamespace(), dataDisk, pvcLabelMap, Collections.singletonMap(pvLabels[0].getName(), pvLabels[0].getValue())));
            }
            List<String> pvcNamesByPv = new ArrayList<>();
            List<PersistentVolumeClaim> isDeletedPvcList = new ArrayList<>();
            pvcNamesByPv = getPvcNamesByPv(pvByLabels, app);
            /*for (int i = 0; i < pvByLabels.size(); i++) {
                //找出原来的pv做截取，截取出pvc，然后与原来的pvc做对比，过滤出pv存在但是pvc不存在的进行创建pvc
                String pvName = pvByLabels.get(i).getMetadata().getName();
                String[] pvcNameArr = pvName.split("-" + app.getNamespace());
                String pvcName = pvcNameArr[0]+pvcNameArr[1];
                pvcNamesByPv.add(pvcName);
            }*/
            Map<String, String> finalPvcLabelMap = pvcLabelMap;
            Label[] finalPvLabels = pvLabels;
            List<String> pvcNameByLabels = pvcByLabels.stream().map(e -> e.getMetadata().getName()).collect(Collectors.toList());
            isDeletedPvcList = pvcNamesByPv.stream().filter(e -> !pvcNameByLabels.contains(e))
                    .map(pvname -> {
                        if (pvname.contains("master")){
                            return MongoUtil.buildHostPathPvc(pvname, app.getNamespace(), app.getDisk(), finalPvcLabelMap, Collections.singletonMap(finalPvLabels[0].getName(), pvname));
                        }else {
                            return MongoUtil.buildHostPathPvc(pvname, app.getNamespace(), dataDisk, finalPvcLabelMap, Collections.singletonMap(finalPvLabels[0].getName(), pvname));
                        }
                    }).collect(Collectors.toList());

            createBatch(app,isDeletedPvcList);
            //现在这里的pvcByLabels是未扩容之前被删除的pvc重新被新建
            pvcByLabels = clientService.get(app.getKubeId()).listPvc(app.getNamespace(), labelMap).getItems();

            List<String> pvNameByLabels = pvByLabels.stream().map(e -> e.getMetadata().getName()).collect(Collectors.toList());
            List<String> pvsNameNeedRollback = pvsNeedRollback.stream().map(e -> e.getMetadata().getName()).collect(Collectors.toList());
            List<String> newPvsName = new ArrayList<>();
            for (int i = 0; i < pvsNameNeedRollback.size(); i++) {
                if (!pvNameByLabels.contains(pvsNameNeedRollback.get(i))){
                    newPvsName.add(pvsNameNeedRollback.get(i));
                }
            }
            newPvsNeedCreate = pvsNeedRollback.stream().filter(e -> newPvsName.stream().anyMatch(pvcname -> e.getMetadata().getName().contains(pvcname))).collect(Collectors.toList());

            List<PersistentVolume> allPvs = new ArrayList<>();
            List<String> hopePvcNames = new ArrayList<>();
            if (Objects.nonNull(newPvsNeedCreate)){
                allPvs.addAll(newPvsNeedCreate);
            }
            allPvs.addAll(pvByLabels);
            hopePvcNames = getPvcNamesByPv(allPvs, app);
            /*for (int i = 0; i < allPvs.size(); i++) {
                //将所有的pv获取到，然后截取出pvc，与现有的对比，没有则创建，有则不创建
                String pvcName = allPvs.get(i).getMetadata().getName();
                String[] pvNameArr = pvcName.split("-" + app.getNamespace());
                pvcName = pvNameArr[0]+pvNameArr[1];
                hopePvcNames.add(pvcName);
            }*/

            List<String> newPvcNameByLabels = pvcByLabels.stream().map(e -> e.getMetadata().getName()).collect(Collectors.toList());
            newPvcsNeedCreate = hopePvcNames.stream().filter(e -> !newPvcNameByLabels.contains(e))
                    .map(name -> {
                        String pvname = name.replace("elasticsearch-data-","elasticsearch-data-"+app.getNamespace()+"-");
                        return MongoUtil.buildHostPathPvc(name, app.getNamespace(), dataDisk, finalPvcLabelMap, Collections.singletonMap(finalPvLabels[0].getName(), pvname));
                    }).collect(Collectors.toList());
            createPv(app,newPvsNeedCreate);
            createBatch(app,newPvcsNeedCreate);
        }
    }

    public List<String> getPvcNamesByPv(List<PersistentVolume> pvByLabels,CloudApp app){
        List<String> pvcNamesByPv = new ArrayList<>();
        for (int i = 0; i < pvByLabels.size(); i++) {
            //找出原来的pv做截取，截取出pvc，然后与原来的pvc做对比，过滤出pv存在但是pvc不存在的进行创建pvc
            String pvName = pvByLabels.get(i).getMetadata().getName();
            String[] pvcNameArr = pvName.split("-" + app.getNamespace());
            String pvcName = pvcNameArr[0]+pvcNameArr[1];
            pvcNamesByPv.add(pvcName);
        }
        return pvcNamesByPv;
    }

    public void  createPv(CloudApp app,List<PersistentVolume> pvsNeedRollback){
        if (!pvsNeedRollback.isEmpty()) {
            KubeClient kubeClient = clientService.get(app.getKubeId());
            PersistentVolume[] arr = new PersistentVolume[pvsNeedRollback.size()];
            kubeClient.createPv(pvsNeedRollback.toArray(arr));
        }
    }

    public void  createBatch(CloudApp app,List<PersistentVolumeClaim> pvcs){
        if (!pvcs.isEmpty()) {
            KubeClient kubeClient = clientService.get(app.getKubeId());
            kubeClient.createBatch(pvcs.get(0).getKind(), pvcs.toArray(new PersistentVolumeClaim[0]));
        }
    }

    @Override
    public PageInfo<? extends CloudAppVO> searchPage(PageDTO page) {
        if (page.getCondition().get("kubeGroupId") != null) {
            int groupId = Integer.parseInt(page.getCondition().get("kubeGroupId") + "");
            List<Integer> kubeIds = kubeGroupService.selectKubesOfGroup(groupId).stream().map(k -> k.getId()).collect(Collectors.toList());
            if (!kubeIds.isEmpty()) {
                page.getCondition().put("kubeIds", kubeIds);
            }
        }
        PageInfo<? extends CloudAppVO> pageInfo = super.searchPage(page);
        List<EsVo> voList = pageInfo.getList().stream().map(app -> {
            EsVo esVo = new EsVo();
            BeanUtils.copyProperties(app, esVo);
            String crYaml = StringUtils.isEmpty(esVo.getCr()) ? esVo.getCrRun() : esVo.getCr();
            if (StringUtils.isNoneBlank(crYaml)) {
                Elasticsearch cr = YamlEngine.unmarshal(crYaml, Elasticsearch.class);
                esVo.setMasterSize(cr.getSpec().getNodeSets()[0].getCount());
                esVo.setDataSize(cr.getSpec().getNodeSets()[1].getCount());
                esVo.setMasterCpu(cr.getSpec().getNodeSets()[0].getPodTemplate().getSpec().getContainers().get(0).getResources().getLimits().get("cpu") + "");
                esVo.setMasterMemory(cr.getSpec().getNodeSets()[0].getPodTemplate().getSpec().getContainers().get(0).getResources().getLimits().get("memory") + "");
                esVo.setDataCpu(cr.getSpec().getNodeSets()[1].getPodTemplate().getSpec().getContainers().get(0).getResources().getLimits().get("cpu") + "");
                esVo.setDataMemory(cr.getSpec().getNodeSets()[1].getPodTemplate().getSpec().getContainers().get(0).getResources().getLimits().get("memory") + "");
                esVo.setDataDisk(cr.getSpec().getNodeSets()[1].getVolumeClaimTemplates().get(0).getSpec().getResources().getRequests().get("storage") + "");
                esVo.setMasterDisk(cr.getSpec().getNodeSets()[0].getVolumeClaimTemplates().get(0).getSpec().getResources().getRequests().get("storage") + "");
                esVo.setSlaveCount(esVo.getDataSize());

                KubernetesResourceList<Kibana> list = clientService.get(esVo.getKubeId()).listCustomResource(Kibana.class, null,
                        new Label(CloudAppConstant.ES_REF, esVo.getCrName()));
                esVo.setDeletable(list.getItems().isEmpty());
//                esVo.setDiskUsageRaw(0);
//                esVo.setCpuUsageRaw(0);
//                esVo.setMemoryUsageRaw(0);
            }
            return esVo;
        }).collect(Collectors.toList());
        return PageUtil.page2PageInfo(voList, CloudAppVO.class, esVo -> {
            return esVo;
        });
    }

    public List<Map<String, Object>> queryUnInstallKinabaList(Integer zoneId, Integer kubeGroupId, Integer kubeId, String namespace) {

        Map<String, Object> params = new HashMap<>();
        if (kubeId != null) {
            params.put("kubeId", kubeId);
        } else if (kubeGroupId != null) {
            List<Integer> kubeIds = kubeGroupService.selectKubesOfGroup(kubeGroupId).stream().map(k -> k.getId()).collect(Collectors.toList());
            if (kubeIds.isEmpty()) {
                return Collections.emptyList();
            }
            params.put("kubeIds", kubeIds);
        } else if (zoneId != null) {
            List<Integer> kubeIds = kubeConfigService.list(ImmutableMap.of("zoneId", zoneId)).stream().map(KubeConfig::getId).collect(Collectors.toList());
            if (!kubeIds.isEmpty()) {
                params.put("kubeIds", kubeIds);
            }
        }
        params.put("namespace", namespace);
        params.put("kind", AppKind.Elasticsearch.getKind());
        params.put("arch", AppKind.Elasticsearch.getArch());

        List<CloudApp> appByKubeIdAndNamespaceAndKind = appService.findHealthyManagedApps(params);

        //过滤出未创建Kinaba的es应用
        appByKubeIdAndNamespaceAndKind = filterCreateKinabaEs(appByKubeIdAndNamespaceAndKind, namespace, kubeGroupId, kubeId);

        List<Map<String, Object>> resList = new ArrayList<>();
        for (CloudApp everyApp : appByKubeIdAndNamespaceAndKind) {
            String appName = everyApp.getName();
            Integer appId = everyApp.getId();
            Map<String, Object> resMap = new HashMap<>();
            resMap.put("name", appName);
            resMap.put("id", appId);
            resMap.put("kubeId", everyApp.getKubeId());
            resMap.put("version", everyApp.getVersion());
            resMap.put("arch", AppKind.Kibana.getArch());
            resMap.put("kind", AppKind.Kibana.getKind());
            resList.add(resMap);
        }
        return resList;
    }

    /**
     * 过滤已经创建Kinaba的es应用
     * @param esList
     * @param namespace
     * @param kubeGroupId
     * @param kubeId
     * @return
     */
    private List<CloudApp> filterCreateKinabaEs(List<CloudApp> esList, String namespace, Integer kubeGroupId, Integer kubeId) {
        //查询redis相关信息
        Map<String, Object> kinabaParamMap = new HashMap<>();
        kinabaParamMap.put("namespace", namespace);
        if (kubeId != null) {
            kinabaParamMap.put("kubeId", kubeId);
        } else if (kubeGroupId != null) {
            List<Integer> kubeIds = kubeGroupService.selectKubesOfGroup(kubeGroupId).stream().map(k -> k.getId()).collect(Collectors.toList());
            if (kubeIds.isEmpty()) {
                return Collections.emptyList();
            }
            kinabaParamMap.put("kubeIds", kubeIds);
        }
        kinabaParamMap.put("kind", AppKind.Kibana.getKind());
        kinabaParamMap.put("arch", AppKind.Kibana.getArch());
        List<CloudApp> kinabaAppList = appService.findNotCompleteDeletedList(kinabaParamMap);
        if (CollectionUtils.isEmpty(kinabaAppList))
            return esList;

        Set<String> kinabaCrNameSet = kinabaAppList.stream().map(CloudApp::getCrName).collect(Collectors.toSet());
        return esList.stream().filter(nsApp -> {return !kinabaCrNameSet.contains(nsApp.getCrName());}).collect(Collectors.toList());
    }

    @Override
    protected void completeInstanceProperty(AppInstanceVO appInstanceVO, PodDTO pod, CloudApp app, KubeClient client) {
        Map<String, String> annotations = pod.getPod().getMetadata().getLabels();
        appInstanceVO.setRole(annotations.entrySet().stream()
                .filter(i -> nodeLabelList.contains(i.getKey()) && "true".equals(i.getValue()))
                .map(i -> i.getKey().substring(i.getKey().indexOf("/")+1).replace("node-", ""))
                .collect(Collectors.joining(",")));
    }

    /**
     * 字符串占位符替换，根据给定values map, 将字符串中的匹配占位符-${key}, 替换为values中对应value
     * @param strTemplate 字符串模板
     * @param values map of the values
     * @return The result of the replace operation.
     * todo 优化: 重复扫描, 创建多个string对象, 字符串拼接
     */
    String replace(String strTemplate, Map<String, String> values) {
        String result = strTemplate;
        for (String key : values.keySet()) {
            result = result.replace("${" + key + "}", values.get(key));
        }
        return result;
    }

    @Override
    public CloudAppVO overrideSpec(CloudAppLogic logicApp, Integer kubeId, InstallAppVo<? extends OverrideSpec> vo) {
        CloudAppVO appVO = super.overrideSpec(logicApp, kubeId, vo);
        EsVo esVo = new EsVo();
        BeanUtils.copyProperties(appVO, esVo);
        if (vo.getOverrideSpecs().get(kubeId) instanceof EsOverrideSpec) {
            EsOverrideSpec overrideSpec = (EsOverrideSpec) vo.getOverrideSpecs().get(kubeId);
            esVo.setCpu(overrideSpec.getDataCpu());
            esVo.setDisk(overrideSpec.getDataDisk());
            esVo.setMemory(overrideSpec.getDataMemory());
            esVo.setDataSize(overrideSpec.getDataSize());
            esVo.setMasterCpu(overrideSpec.getMasterCpu());
            esVo.setMasterMemory(overrideSpec.getMasterMemory());
            esVo.setMasterDisk(overrideSpec.getMasterDisk());
            esVo.setDefaultIndexSettings(overrideSpec.getDefaultIndexSettings());
            esVo.setMasterSize(overrideSpec.getMasterSize());
            esVo.setBackupHostpathRoot(overrideSpec.getBackupHostpathRoot());
        }
        return esVo;
    }

    @Override
    public OverrideSpec reviewSpec(CloudApp app) {
        OverrideSpec overrideSpec = super.reviewSpec(app);
        EsOverrideSpec esOverrideSpec = new EsOverrideSpec();
        BeanUtils.copyProperties(overrideSpec, esOverrideSpec);
        String yaml = app.getCr();
        Elasticsearch cr = YamlEngine.unmarshal(yaml, Elasticsearch.class);
        Elasticsearch.ElasticsearchNodeSet dataNodes = cr.getSpec().getNodeSets()[1];
        esOverrideSpec.setDataSize(dataNodes.getCount());

        esOverrideSpec.setDataCpu(app.getCpu());
        esOverrideSpec.setDataMemory(app.getMemory());
        esOverrideSpec.setDataDisk(app.getDisk());

        Elasticsearch.ElasticsearchNodeSet masterNodes = cr.getSpec().getNodeSets()[0];
        esOverrideSpec.setMasterSize(masterNodes.getCount());
        Map<String, Quantity> limits = masterNodes.getPodTemplate().getSpec().getContainers().get(0).getResources().getLimits();
        esOverrideSpec.setMasterCpu(limits.get("cpu").toString());
        esOverrideSpec.setMasterMemory(limits.get("memory").toString());
        esOverrideSpec.setMasterDisk(masterNodes.getVolumeClaimTemplates().get(0).getSpec().getResources().getRequests().get("storage").toString());

        return esOverrideSpec;
    }

    @Override
    public InstallAppVo<? extends OverrideSpec> parseInstallVo(String data) {
        InstallAppVo<EsOverrideSpec> vo = JsonUtil.toObject(data, new TypeReference<InstallAppVo<EsOverrideSpec>>() {
        });
        if (vo != null) {
            if (vo.getSpec() != null && vo.getSpec().getMembers() == 0) {
                EsOverrideSpec spec = vo.getSpec();
                spec.setMembers(spec.getDataSize() + spec.getMasterSize());
            }
            if (!CollectionUtils.isEmpty(vo.getOverrideSpecs())) {
                for (EsOverrideSpec spec : vo.getOverrideSpecs().values()) {
                    if (spec.getMembers() == 0 && spec.getMasterSize() != null && spec.getDataSize() != null)
                        spec.setMembers(spec.getMasterSize() + spec.getDataSize());
                }
            }
        }
        return vo;
    }

    @Override
    @Transactional
    public void upgrade(int appid, String version) throws Exception {
        CloudApp app = appService.get(appid);

        CustPreconditions.checkState(!app.getVersion().equals(version), "当前版本即为" + version);
        CustPreconditions.checkState(AppUtil.compareVersion(app.getVersion(), version) > 0, String.format("版本变更，从%s变为%s, 节点不能降级", app.getVersion(), version));

        BiFunction<Map<ImageKindEnum, String>, Elasticsearch, Elasticsearch> modifier = (imageManifest, cr) -> {
            cr.getSpec().setImage(imageManifest.get(ImageKindEnum.MainImage));
            return cr;
        };
        appOperationHandler.handleUpgrade(appid, version, modifier, Elasticsearch.class, this);

    }

    public List<AppInstanceVO> getDataPod(CloudApp app) {
        return findInstanceList(app.getId(), null, null).stream()
                .filter(i -> i.getRole().contains("data")).collect(Collectors.toList());
    }

    @Override
    public List<ServiceManager> createService(
            String serviceType, CloudAppVO vo, List<?> serviceResources, CustomResource installCr) {
        return openSourceKindServiceBuilder(serviceType, vo, serviceResources, null);
    }

    @Override
    public void updateService(
            List<ServiceManager> svcMgrs, CloudApp app, Object oldServiceResource) throws Exception {
        openSourceKindUpdateServiceBuilder(svcMgrs, app, oldServiceResource);
    }

    /**
     * 查询es索引
     *
     * @param logicid
     * @return
     */
    public List<Map<String, String>> indexList(Integer logicid) {
        CloudApp app = appService.getCloudAppByLogicId(logicid);
        Integer appId = app.getId();
        int kubeId = app.getKubeId();
        KubeClient kubeClient = clientService.get(kubeId);
        //获取写连接 service
        AppDBVO appDBVO = accessManagementService
                .getFirstWriteServiceAddressByAppIdAndKubeId(appId, kubeId);
        String ip = appDBVO.getIp();
        Integer port = appDBVO.getPort();
        Secret secret = kubeClient.getSecret(app.getNamespace(), getSecretName(app.getCrName()));
        String password = new String(Base64.getDecoder().decode(secret.getData().get(SECRET_USERNAME).getBytes()));

        //client查询
        log.info("当前应用logicid:" + logicid + ",es查询到的ip：" + ip + ",port:" + port);
        RestHighLevelClient client = esUtil.getClient(ip, port, SECRET_USERNAME, password);
        //构建返回值数据
        try {
            Set<String> esIndexViewName = getEsIndexViewName(client);
            if (null == esIndexViewName && esIndexViewName.isEmpty())
                return new ArrayList<>();

            //调用mergeSet对数据进行处理
            List<Map<String, String>> resultList = mergeSet(esIndexViewName);

            Map<String, String> esIndexViewCreateTime = getEsIndexViewCreateTime(client);
            //根据返回的indexCreateTime数据进行判空
            if (esIndexViewCreateTime == null || esIndexViewCreateTime.isEmpty())
                return resultList;

            //使用转换函数将map中时间戳转换为String类型的时间
            Map<String, String> indexCreateTimeFormat = Maps.transformEntries(esIndexViewCreateTime, (s, o) -> formatDate(o));
            resultList = mergeMap(resultList, indexCreateTimeFormat, "createdate");

            String esIndexViewStoreSize = getEsIndexViewStoreSize(ip, port, SECRET_USERNAME, password);
            //调方法处理json
            Map<String, String> StoreSizeResaultMap = disposeJSON(esIndexViewStoreSize,true);
            resultList = mergeMap(resultList, StoreSizeResaultMap, "storesize");

            String esIndexViewStoreNumbers = getEsIndexViewStoreNumbers(ip, port, SECRET_USERNAME, password);
            //调方法处理json
            Map<String, String> StoreNumbersResaultMap = disposeJSON(esIndexViewStoreNumbers, false);
            resultList = mergeMap(resultList, StoreNumbersResaultMap, "storenumbers");

            return resultList;
        } catch (Exception e) {
            log.error("查询es索引失败" + e);
            throw new CustomException(600, "查询索引失败");
        }finally {
            try {
                client.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 删除es索引
     *
     * @param logicid
     * @param indexname
     */
    public void deleteIndex(Integer logicid, String indexname) {
        CloudApp app = appService.getCloudAppByLogicId(logicid);
        Integer appId = app.getId();
        Integer kubeId = app.getKubeId();
        ResourceChangeHis resourceChangeHis = appService.getResourceChangeHis(app, ActionEnum.DELETE_INDEX, StatusConstant.RUNNING, "", null);
        resourceChangeHisService.add(resourceChangeHis);
        KubeClient kubeClient = clientService.get(kubeId);
        //获取写连接 service
        AppDBVO appDBVO = accessManagementService
                .getFirstWriteServiceAddressByAppIdAndKubeId(appId, kubeId);
        String ip = appDBVO.getIp();
        Integer port = appDBVO.getPort();
        //获取用户名密码
        Secret secret = kubeClient.getSecret(app.getNamespace(), getSecretName(app.getCrName()));
        String password = new String(Base64.getDecoder().decode(secret.getData().get(SECRET_USERNAME).getBytes()));

        //client查询
        log.info("当前应用logicid:" + logicid + ",es查询到的ip：" + ip + ",port:" + port);
        RestHighLevelClient client = esUtil.getClient(ip, port, SECRET_USERNAME, password);

        try {
            if (!esUtil.checkIndex(client, indexname)) {
                updateResourceChangeHis(resourceChangeHis, StatusConstant.FAIL, "当前索引(" + indexname + ")不存在,删除索引失败");
                throw new CustomException(600, "当前索引不存在,删除失败");
            }

            DeleteIndexRequest request = new DeleteIndexRequest(indexname);
            client.indices().delete(request, RequestOptions.DEFAULT);
            updateResourceChangeHis(resourceChangeHis, StatusConstant.SUCCESS, "删除es索引(" + indexname + ")索引成功");
        } catch (Exception e) {
            log.error("删除索引失败：" + e.getMessage());
            updateResourceChangeHis(resourceChangeHis, StatusConstant.FAIL, "删除es索引(" + indexname + ")索引失败");
            throw new CustomException(600, "当前所选索引删除失败");
        } finally {
            try {
                client.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 获取index 的名称
     * @param client
     * @return
     */
    private Set<String> getEsIndexViewName(RestHighLevelClient client) {
        try {
            GetAliasesResponse getAliasesResponse = client.indices().getAlias(new GetAliasesRequest(), RequestOptions.DEFAULT);
            Map<String, Set<AliasMetaData>> aliases = getAliasesResponse.getAliases();
            return aliases.keySet();
        } catch (IOException e) {
            log.error("查询indexname失败" + e);
            throw new CustomException(600, "查询es索引名称失败");
        }
    }

    /**
     * 获取 index 创建时间
     * @return
     */
    public Map<String, String> getEsIndexViewCreateTime(RestHighLevelClient client) {
        Map<String, String> map = new HashMap<>();
        try {
            GetSettingsResponse getIndexResponse = client.indices().getSettings(new GetSettingsRequest(), RequestOptions.DEFAULT);
            //所有setting，其中creation_date是index创建的时间戳，转换后就是创建时间
            ImmutableOpenMap<String, Settings> indexToSettings = getIndexResponse.getIndexToSettings();

            Iterator<String> iterator = indexToSettings.keysIt();
            while (iterator.hasNext()) {
                String key = iterator.next();
                map.put(key, indexToSettings.get(key).get("index.creation_date", null));
            }
        } catch (IOException e) {
            log.error("查询index创建时间失败" + e);
            e.printStackTrace();
        }
        return map;
    }

    /**
     * 获取 esindex的存储大小
     *
     * @param hostname 主机名
     * @param port     端口
     * @param username 用户名
     * @param password 密码
     * @return es集群信息
     * @throws URISyntaxException URISyntaxException
     * @throws IOException        IOException
     */
    public String getEsIndexViewStoreSize(String hostname, Integer port, String username, String password){

        String esApiUrl = getEsApiUrl(hostname, port, "/_stats/store");
        String esUserPassword = username + ":" + password;
        String authInfo = Base64.getEncoder().encodeToString(esUserPassword.getBytes());
        log.info("getEsIndexViewStoreSize方法请求:url: {}, Base64加密后的账密为: {}", esApiUrl, authInfo);

        try {
            HttpGet httpGet = getHttpGet(esApiUrl, authInfo);
            return executeHttp(httpGet);
        } catch (Exception e) {
            log.error("执行http请求获取es信息失败:{}", e);
            throw new CustomException(600, "执行http请求获取es信息失败");
        }
    }

    /**
     * 获取es集群信息
     *
     * @param hostname 主机名
     * @param port     端口
     * @param username 用户名
     * @param password 密码
     * @return es集群信息
     * @throws URISyntaxException URISyntaxException
     * @throws IOException        IOException
     */

    public String getEsIndexViewStoreNumbers(String hostname, Integer port, String username, String password){
        String esApiUrl = getEsApiUrl(hostname, port, "/_stats/docs");
        String esUserPassword = username + ":" + password;
        String authInfo = Base64.getEncoder().encodeToString(esUserPassword.getBytes());
        log.info("getEsIndexViewStoreNumbers方法请求:url: {}, Base64加密后的账密为: {}", esApiUrl, authInfo);

        try {
            HttpGet httpGet = getHttpGet(esApiUrl, authInfo);
            return executeHttp(httpGet);
        } catch (Exception e) {
            log.error("执行http请求获取es信息失败:{}", e);
            throw new CustomException(600, "执行http请求获取es信息失败");
        }
    }

    private String getEsApiUrl(String hostname, Integer port,String suffixStr) {
        //        String url = "http://"+username+":"+password+"@"+hostname+":"+port+"/_stats/docs";
        return "http://" + hostname + ":" + port + suffixStr;
    }

    /**
     * 构建 get 的http请求
     * @param esApiUrl
     * @param authInfo
     * @return
     * @throws URISyntaxException
     */
    private HttpGet getHttpGet(String esApiUrl, String authInfo) throws URISyntaxException {
        //创建一个uri对象
        URIBuilder uriBuilder = new URIBuilder(esApiUrl);

        //创建httpGet远程连接实例,这里传入目标的网络地址
        HttpGet httpGet = new HttpGet(uriBuilder.build());

        // 设置配置请求参数(没有可忽略)
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(35000)// 连接主机服务超时时间
                .setConnectionRequestTimeout(35000)// 请求超时时间
                .setSocketTimeout(60000)// 数据读取超时时间
                .build();

        // 为httpGet实例设置配置
        httpGet.setConfig(requestConfig);
        httpGet.setHeader("Authorization", "Basic " + authInfo);
        return httpGet;
    }

    /**
     * 执行 http 请求并拿到相关结果
     *
     * @return
     */
    private String executeHttp(HttpGet httpGet) throws IOException {
        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;
        try {
            client = HttpClients.createDefault();
            //执行请求
            response = client.execute(httpGet);

            //获取响应实体, 响应内容
            //通过EntityUtils中的toString方法将结果转换为字符串
           return EntityUtils.toString(response.getEntity());
        } catch (Exception e) {
            log.error("执行http请求获取es信息失败:{}", e);
            throw new CustomException(600, "执行http请求获取es信息失败");
        } finally {
            response.close();
            client.close();
        }
    }

    /**
     *合并Set到List<Map>
     */
    private List<Map<String,String>> mergeSet(Set<String> set){
        ArrayList<Map<String, String>> arrayList = new ArrayList<>();
        for (String index :set){
            HashMap<String, String> changemap = new HashMap<>();
            changemap.put("index",index);
            arrayList.add(changemap);
        }
        return arrayList;
    }

    /**
     *合并Map到List<Map>
     */
    private List<Map<String,String>> mergeMap(List<Map<String,String>> arrayList, Map<String,String> partMap, String param){
        for (Map<String, String> map : arrayList) {
            String index = map.get("index");
            if (partMap.containsKey(index)){
                map.put(param,partMap.get(index));
            }
        }
        return arrayList;
    }

    /**
     *时间戳转换为标准时间
     */
    private String formatDate(String time){
        Long times  = Long.valueOf(time);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattime = format.format(times);
        return formattime;
    }

    /**
     * 转换JSON数据
     */
    private Map<String, String> disposeJSON(String jsonstr, boolean flag) {
        JSONObject jsonObject = JSONObject.parseObject(jsonstr);
        JSONObject jsonObject1 = jsonObject.getJSONObject("indices");
        Map<String, String> resultmap = new HashMap<>();
        Set<String> set = jsonObject1.keySet();

        if (flag) {
            for (String index : set) {
                if (jsonObject1.getJSONObject(index).getJSONObject("total").getJSONObject("store") == null) {
                    resultmap.put(index, "0");
                } else {
                    String size = jsonObject1.getJSONObject(index).getJSONObject("total").getJSONObject("store").getString("size_in_bytes");
                    resultmap.put(index, size);
                }
            }
        } else {
            for (String index : set) {
                if (jsonObject1.getJSONObject(index).getJSONObject("total").getJSONObject("docs") == null) {
                    resultmap.put(index, "0");
                } else {
                    String count = jsonObject1.getJSONObject(index).getJSONObject("total").getJSONObject("docs").getString("count");
                    resultmap.put(index, count);
                }
            }
        }
        return resultmap;
    }

    protected String[] getStsOrDeployNames(CloudApp app) {
        return new String[]{app.getCrName() + "-es-data", app.getCrName() + "-es-masters"};
    }

    @Override
    public void delete(CloudApp app) {
        super.delete(app);
        clientService.get(app.getKubeId()).scaleDeploy(app.getNamespace(), getDeployName(app.getCrName()), 0);
    }

    @Override
    public void recreate(CloudApp app) {
        super.recreate(app);
        clientService.get(app.getKubeId()).scaleDeploy(app.getNamespace(), getDeployName(app.getCrName()), 1);
    }
    @Override
    public void restore(BackupHis backupHis, Integer appId, String restoreTime, String ftpFilename, String backupType) {
        RestoreHis restoreHis = new RestoreHis();
        restoreHis.setStatus(StatusConstant.RUNNING);

        Date startDate = new Date();
        //所有pod
        List<PodDTO> pods = new ArrayList<PodDTO>();
        //时间转换
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH:mm:ss");
        //操作记录
        ResourceChangeHis resourceChangeHis = null;
        Integer changeId = null;
        try{
            Timestamp startTime = new Timestamp(System.currentTimeMillis());
            restoreHis.setStartTime(startTime);
            // 1. 插入基础恢复历史和操作记录
            Integer backupAppId = backupHis.getAppId();
            CloudApp goalApp = appService.get(appId);
            CloudApp backupApp = appService.get(backupAppId);
            restoreHis.setAppId(appId);
            restoreHis.setAppName(goalApp.getName());
            restoreHis.setAppType(goalApp.getKind());
            KubeClient kubeClient = clientService.get(goalApp.getKubeId());
            //获取一个datapod执行恢复操作
            //获取所有节点
            PodDTO masterPod = backupUtil.getRestorePodEs(goalApp, kubeClient).get(0);
            if(null == masterPod){
                backupUtil.backupReturn(backupHis, changeId, StatusConstant.FAIL, "", "当前app："+goalApp.getName()+"未获取到master节点！");
                return;
            }
            restoreHis.setPodName(masterPod.getPodName());

            //应用所属集群
            KubeConfig byId = kubeConfigService.get(goalApp.getKubeId());
            if(null == byId){
                log.error("[es Restore]未获取到集群！");
                backupUtil.restoreReturn(restoreHis, changeId, "未获取到集群！", StatusConstant.FAIL);
                return;
            }
            restoreHis.setKubeName(byId.getName());
            restoreHis.setMessage("恢复中...");
            String restoreDir = AppUtil.getESBackupRootPath(goalApp.getNamespace(), goalApp.getCrName());
            restoreHis.setRestoreDir(restoreDir);
            restoreHis.setFileName(backupHis.getFileName());
            restoreHis.setFileDeleted(false);
            //插入基本信息
            backupService.commitRestoreHis(restoreHis);
            //插入操作记录
            String cr = StringUtils.isEmpty(goalApp.getCr()) ? goalApp.getCrRun() : goalApp.getCr();
            resourceChangeHis = new ResourceChangeHis(){{
                setInsertTime(startTime);
                setKind(goalApp.getKind());
                setKubeId(goalApp.getKubeId());
                setNamespace(goalApp.getNamespace());
                setCommand("恢复");
                setStatus("2");
                setAction(ActionEnum.RESTORE.getActionType());
                setMsg("恢复中...");
                setAppId(goalApp.getId());
                setAppLogicId(goalApp.getLogicAppId());
                setAppName(goalApp.getName());
                setKubeName(byId.getName());
                setYaml(cr);
                setUserName(UserUtil.getAsyncUserinfo().getUsername());
                setUserIp(CloudRequestContext.getContext().getUserIp());
                setLastEndTimestamp(System.currentTimeMillis());
            }};
            changeId = backupUtil.insertResourceChangeHis(resourceChangeHis);
            goalApp.setStatus(CloudAppConstant.AppStatus.PENDING);
            appService.update(goalApp);
//            checkRestoreAndRecord(resourceChangeHis, goalApp, backupHis, restoreHis);

            //密码，执行命令：kubectl get secret hdbk-4-es-elastic-user -n rongqiyun -o go-template='{{.data.elastic | base64decode}}'
            String password = kubeClient.getSecret(goalApp.getNamespace(), goalApp.getCrName() + "-es-elastic-user").getData().get("elastic");
            password = new String(Base64.getDecoder().decode(password));
            String encodePassword = URLEncoder.encode(password, "UTF-8");
            Date now = new Date();
            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMddHHmmss");
            String nowStr = sdf1.format(now);

//            // 拷贝备份文件
//            if (!backupHis.getAppId().equals(goalAppId))
//                try {
//                    String backupFileFullName = AppUtil.getESBackupRootPath(backupApp.getNamespace(), backupApp.getCrName()) + "/" + backupHis.getFileName();
//                    String res = kubeClient.execCmd(goalApp.getNamespace(), masterPod.getPodName(), "dump",
//                            "sh", "-c", "cp -rf " + backupFileFullName + " " + restoreDir);
//                } catch (Exception e) {
//                    log.error("es恢复拷贝文件失败失败！");
//                    restoreReturn(restoreHis, changeId, "拷贝文件失败！" + e.getMessage(), StatusConstant.FAIL);
//                    return;
//                }

            //执行恢复命令
            //获取时间戳
            String restoreTimestamop = backupHis.getFileName().substring(backupHis.getFileName().indexOf("-") + 1, backupHis.getFileName().lastIndexOf(".tar.gz"));
            String restoreScript = "";
            CloudBackupStorageVO cloudBackupStorageVO = resourceManagerService.listBackupStorage();
            if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.NAS)) {
                String mountPath = cloudBackupStorageVO.getServer() + ":" + cloudBackupStorageVO.getMountPath();
                restoreScript = "bash /scripts/restore.sh "
                        + masterPod.getPodIp()
                        + " " + AppKind.Elasticsearch.getDbPort() + " "
                        + encodePassword
                        + " " + restoreTimestamop
                        + " " + CloudAppConstant.OperatorStorageType.NFS
                        + " " + mountPath
                        + " " + backupApp.getCrName();
            } else if (cloudBackupStorageVO.getStorageType().equalsIgnoreCase(CloudAppConstant.StorageType.S3)) {
                String region = cloudBackupStorageVO.getRegion();
                if (org.springframework.util.StringUtils.isEmpty(region)) {
                    region = "\"\"";
                }
                restoreScript = "bash /scripts/restore.sh "
                        + masterPod.getPodIp()
                        + " " + AppKind.Elasticsearch.getDbPort() + " "
                        + encodePassword
                        + " " + restoreTimestamop
                        + " " + CloudAppConstant.StorageType.S3
                        + " " + cloudBackupStorageVO.getServer()
                        + " " + backupApp.getCrName()
                        + " " + region
                        + " " + cloudBackupStorageVO.getBucket()
                        + " " + cloudBackupStorageVO.getAccessKey()
                        + " " + cloudBackupStorageVO.getSecretKey();
            } else {
                throw new CustomException(600, "备份失败！不支持当前备份存储类型:" + cloudBackupStorageVO.getStorageType());
            }
            log.info("[es Restore]执行的es恢复命令:" + restoreScript);
            kubeClient.execCmdOneway(goalApp.getNamespace(), masterPod.getPodName(),
                    "dump", "sh", "-c", restoreScript);
            //创建定时轮询恢复结果
            Map map = new HashMap();
            map.put("restoreHisId", restoreHis.getRestoreHisId());
            map.put("resourceChangeId", changeId);
            map.put("restoreTimestamp", restoreTimestamop);
            map.put("restorePodName", masterPod.getPodName());
            map.put("restoreStartDateSDF", sdf.format(startDate));
            appService.callScheduler(goalApp, cr, map, ActionEnum.RESTORE, ElasticsearchBackupAndRestoreWatch.class, resourceChangeHis);
        }catch (Exception e){
            log.error("[es Restore]", e);
            backupUtil.restoreReturn(restoreHis, changeId, e.getMessage(), StatusConstant.FAIL);
        }
    }

    /**
     * 进行集群组调度的时候使用：
     * 需要获取到 每个应用类型 各个组件的 资源信息
     */
    @Override
    public List<ResourceDTO> getAppComponentResource(InstallAppVo<? extends OverrideSpec> vo) {
        List<ResourceDTO> result = new ArrayList<>();
        EsOverrideSpec spec = (EsOverrideSpec) vo.getSpec();
        ResourceDTO masterResourceDto = new ResourceDTO();
        masterResourceDto.setCpu(spec.getMasterCpu());
        masterResourceDto.setMemory(spec.getMasterMemory());
        masterResourceDto.setReplicas(spec.masterSize);
        result.add(masterResourceDto);

        ResourceDTO dataResourceDTO = new ResourceDTO();
        dataResourceDTO.setCpu(spec.getDataCpu());
        dataResourceDTO.setMemory(spec.getDataMemory());
        dataResourceDTO.setReplicas(spec.dataSize);
        result.add(dataResourceDTO);
        return result;
    }

    @Getter
    @Setter
    @ToString
    public static class EsOverrideSpec extends OverrideSpec {
        private Integer masterSize;
        private String masterCpu;
        private String masterMemory;
        private String masterDisk;
        private Integer dataSize;
        private String dataCpu;
        private String dataMemory;
        private String dataDisk;
        private String backupHostpathRoot;
        private Map<String, Object> defaultIndexSettings;
    }

    @Getter @Setter
    public static class EsVo extends CloudAppVO{
        /**
         * 分片个数
         */
        private Integer masterSize;

        /**
         * 备库个数
         */
        private Integer dataSize;

        /**
         * 数据节点cpu
         */
        private String dataCpu;

        /**
         * 数据节点memory
         */
        private String dataMemory;

        /**
         * 数据节点disk
         */
        private String dataDisk;
        /**
         * master节点cpu
         */
        private String masterCpu;

        /**
         * master节点memory
         */
        private String masterMemory;

        /**
         * master节点disk
         */
        private String masterDisk;
        /**
         * 索引模板
         */
        private Map<String, Object> defaultIndexSettings;
    }

    static final Set<String> nodeLabelList = new HashSet<>();

    static {
        nodeLabelList.add("elasticsearch.k8s.elastic.co/node-data");
        nodeLabelList.add("elasticsearch.k8s.elastic.co/node-data_cold");
        nodeLabelList.add("elasticsearch.k8s.elastic.co/node-data_content");
        nodeLabelList.add("elasticsearch.k8s.elastic.co/node-data_hot");
        nodeLabelList.add("elasticsearch.k8s.elastic.co/node-data_warm");
        nodeLabelList.add("elasticsearch.k8s.elastic.co/node-ingest");
        nodeLabelList.add("elasticsearch.k8s.elastic.co/node-master");
        nodeLabelList.add("elasticsearch.k8s.elastic.co/node-ml");
        nodeLabelList.add("elasticsearch.k8s.elastic.co/node-remote_cluster_client");
        nodeLabelList.add("elasticsearch.k8s.elastic.co/node-transform");
        nodeLabelList.add("elasticsearch.k8s.elastic.co/node-voting_only");
    }

    static final String YAML_SECRET = "apiVersion: v1\n" +
            "data:\n" +
            "  ${username}: ${password}\n" +
            "kind: Secret\n" +
            "metadata:\n" +
            "  labels:\n" +
            "    common.k8s.elastic.co/type: elasticsearch\n" +
            "    eck.k8s.elastic.co/credentials: \"true\"\n" +
            "    elasticsearch.k8s.elastic.co/cluster-name: ${crNm}\n" +
            "  name: ${name}\n" +
            "  namespace: ${namespace}\n" +
            "type: Opaque\n";

    static final String YAML_ES = "apiVersion: elasticsearch.k8s.elastic.co/v1\n" +
            "kind: Elasticsearch\n" +
            "metadata:\n" +
            "  name: ${crNm}\n" +
            "  namespace: ${namespace}\n" +
            "spec:\n" +
            "  version: ${esVer}\n" +
            "  image: ${esImage}\n" +
            "  volumeClaimDeletePolicy: DeleteOnScaledownOnly\n" +
            "  http:\n" +
            "    tls:\n" +
            "      selfSignedCertificate:\n" +
            "        disabled: true\n" +
            "  # http:\n" +
            "    # service:\n" +
            "      # spec:\n" +
            "        # type: NodePort\n" +
            "        # ports:\n" +
            "          # - port: 9200\n" +
            "            # targetPort: 9200\n" +
            "            # nodePort: 30007\n" +
            "  nodeSets:\n" +
            "  - name: masters\n" +
            "    count: ${mastersSz}\n" +
            "    config:\n" +
            "      # On Elasticsearch versions before 7.9.0, replace the node.roles configuration with the following:\n" +
            "      # node.master: true\n" +
            "      node.roles: [\"master\"]\n" +  // 区分master节点角色和data节点角色. 通为 专一节点
            "      xpack.ml.enabled: true\n" +
//            "      xpack.security.authc:\n" +
//            "          anonymous:\n" +
//            "            username: anonymous\n" +
//            "            roles: superuser\n" +
//            "            authz_exception: false\n" +
            "      # node.remote_cluster_client: false\n" +
            "      # node.store.allow_mmap: false\n" +
            "    podTemplate:\n" +
            "      metadata:\n" +
            "        labels:\n" +
            "${podCustomLabelSection}\n" +
            "      spec:\n" +
            "${masterAffinityAndTolerations}\n" +
            "        initContainers:\n" +
            "        - name: sysctl\n" +
            "          securityContext:\n" +
            "            privileged: true\n" +
            "          command: ['sh', '-c', 'sysctl -w vm.max_map_count=262144']\n" +
            "        containers:\n" +
            "        - name: elasticsearch\n" +
            "          env:\n" +
            "          - name: ES_JAVA_OPTS\n" +
            "            value: -Xms${masterXms} -Xmx${masterXmx}\n" +
            "          imagePullPolicy: IfNotPresent\n" +
            "          resources:\n" +
            "            requests:\n" +
            "              memory: ${mastersRM}\n" +
            "              cpu: ${mastersRC}\n" +
            "            limits:\n" +
            "              memory: ${mastersLM}\n" +
            "              cpu: ${mastersLC}\n" +
            "          volumeMounts:\n" +
            "            - name: timezone\n" +
            "              mountPath: /etc/localtime\n" +
            "        - name: filebeat\n" +
            "          image: ${filebeatImage}\n" +
            "          command:\n" +
            "            - bash\n" +
            "          args:\n" +
            "            - /etc/filebeat/filebeat-entrypoint.sh\n" +
            "          env:\n" +
            "            - name: NAMESPACE\n" +
            "              valueFrom:\n" +
            "                fieldRef:\n" +
            "                  apiVersion: v1\n" +
            "                  fieldPath: metadata.namespace\n" +
            "            - name: POD_NAME\n" +
            "              valueFrom:\n" +
            "                fieldRef:\n" +
            "                  apiVersion: v1\n" +
            "                  fieldPath: metadata.name\n" +
            "            - name: POD_IP\n" +
            "              valueFrom:\n" +
            "                fieldRef:\n" +
            "                  apiVersion: v1\n" +
            "                  fieldPath: status.podIP\n" +
            "            - name: ES_NAME\n" +
            "              value: ${crNm}\n" +
            "            - name: FILEBEAT_CFG_FILE\n" +
            "              value: elasticsearch-cluster-filebeat.yaml\n" +
            "          resources:\n" +
            "            limits:\n" +
            "              cpu: 100m\n" +
            "              memory: 200Mi\n" +
            "            requests:\n" +
            "              cpu: 50m\n" +
            "              memory: 50Mi\n" +
            "          volumeMounts:\n" +
            "            - name: es-filebeat\n" +
            "              mountPath: /etc/filebeat/\n" +
            "            - name: elasticsearch-logs\n" +
            "              mountPath: /usr/share/elasticsearch/logs\n" +
            "            - name: timezone\n" +
            "              mountPath: /etc/localtime\n" +
            "            - name: es-ftp\n" +
            "              mountPath: /scripts/\n" +
            "        volumes:\n" +
            "          - name: es-filebeat\n" +
            "            configMap:\n" +
            "              name: operator-filebeat-configmap\n" +
            "          - name: timezone\n" +
            "            hostPath:\n" +
            "              path: /usr/share/zoneinfo/Asia/Shanghai\n" +
            "              type: ''\n" +
            "          - name: es-ftp\n" +
            "            configMap:\n" +
            "              name: es-script-cm\n" +
            "    volumeClaimTemplates:\n" +
            "    - metadata:\n" +
            "        name: elasticsearch-data\n" +
            "        labels: \n" +
            "          common.k8s.elastic.co/type: elasticsearch\n" +
            "          elasticsearch.k8s.elastic.co/cluster-name: ${crNm}\n" +
            "          elasticsearch.k8s.elastic.co/namespace: ${namespace}\n" +
            "          elasticsearch.k8s.elastic.co/statefulset-name: ${crNm}-es-master\n" +
            "${volumeTemplateLabelYaml}\n" +
            "      spec:\n" +
            "${masterPVNameSelector}\n" +
            "        accessModes:\n" +
            "        - ReadWriteOnce\n" +
            "        resources:\n" +
            "          requests:\n" +
            "            storage: ${mastersStoSz}\n" +
            "        storageClassName: ${scName}\n" +
            "  - name: data\n" +
            "    count: ${dataSz}\n" +
            "    config:\n" +
            "      # On Elasticsearch versions before 7.9.0, replace the node.roles configuration with the following:\n" +
            "      # node.master: false\n" +
            "      # node.data: true\n" +
            "      # node.ingest: true\n" +
            "      # node.ml: true\n" +
            "      # node.transform: true\n" +
            "      node.roles: [\"data\"]\n" + // 区分master节点角色和data节点角色. 通为 专一节点
            "      # node.remote_cluster_client: false\n" +
            "      # node.store.allow_mmap: false\n" +
            "    podTemplate:\n" +
            "      metadata:\n" +
            "        labels: \n" +
            "${podCustomLabelSection}\n" +
            "      spec:\n" +
            "${dataAffinityAndTolerations}\n" +
            "        initContainers:\n" +
            "        - name: sysctl\n" +
            "          securityContext:\n" +
            "            privileged: true\n" +
            "          command: ['sh', '-c', 'sysctl -w vm.max_map_count=262144; mkdir -p ${nfsSubPath}']\n" +
//            "          volumeMounts:\n" +
//            "            - name: es-dump-data\n" +
//            "              mountPath: /backup/\n" +
            "        containers:\n" +
            "        - name: elasticsearch\n" +
            "          env:\n" +
            "          - name: ES_JAVA_OPTS\n" +
            "            value: -Xms${dataXms} -Xmx${dataXmx}\n" +
            "          resources:\n" +
            "            requests:\n" +
            "              memory: ${dataRM}\n" +
            "              cpu: ${dataRC}\n" +
            "            limits:\n" +
            "              memory: ${dataLM}\n" +
            "              cpu: ${dataLC}\n" +
            "          volumeMounts:\n" +
            "            - name: timezone\n" +
            "              mountPath: /etc/localtime\n" +
            "        - name: filebeat\n" +
            "          image: ${filebeatImage}\n" +
            "          command:\n" +
            "            - bash\n" +
            "          args:\n" +
            "            - /etc/filebeat/filebeat-entrypoint.sh\n" +
            "          env:\n" +
            "            - name: NAMESPACE\n" +
            "              valueFrom:\n" +
            "                fieldRef:\n" +
            "                  apiVersion: v1\n" +
            "                  fieldPath: metadata.namespace\n" +
            "            - name: POD_NAME\n" +
            "              valueFrom:\n" +
            "                fieldRef:\n" +
            "                  apiVersion: v1\n" +
            "                  fieldPath: metadata.name\n" +
            "            - name: POD_IP\n" +
            "              valueFrom:\n" +
            "                fieldRef:\n" +
            "                  apiVersion: v1\n" +
            "                  fieldPath: status.podIP\n" +
            "            - name: ES_NAME\n" +
            "              value: ${crNm}\n" +
            "            - name: FILEBEAT_CFG_FILE\n" +
            "              value: elasticsearch-cluster-filebeat.yaml\n" +
            "          resources:\n" +
            "            limits:\n" +
            "              cpu: 100m\n" +
            "              memory: 200Mi\n" +
            "            requests:\n" +
            "              cpu: 50m\n" +
            "              memory: 50Mi\n" +
            "          volumeMounts:\n" +
            "            - name: es-filebeat\n" +
            "              mountPath: /etc/filebeat/\n" +
            "            - name: elasticsearch-logs\n" +
            "              mountPath: /usr/share/elasticsearch/logs\n" +
            "            - name: timezone\n" +
            "              mountPath: /etc/localtime\n" +
            "            - name: es-ftp\n" +
            "              mountPath: /scripts/\n" +
            "        - name: dump\n" +
            "          securityContext:\n" +
            "            privileged: true\n" +
            "          image: ${dumpImage}\n" +
            "          env:\n" +
            "            - name: NAMESPACE\n" +
            "              valueFrom:\n" +
            "                fieldRef:\n" +
            "                  apiVersion: v1\n" +
            "                  fieldPath: metadata.namespace\n" +
            "            - name: CR_NAME\n" +
            "              value: ${crNm}\n" +
            "          resources:\n" +
            "            limits:\n" +
            "              cpu: 100m\n" +
            "              memory: 200Mi\n" +
            "            requests:\n" +
            "              cpu: 50m\n" +
            "              memory: 50Mi\n" +
            "          volumeMounts:\n" +
//            "            - name: es-dump-data\n" +
//            "              mountPath: /backup/\n" +
            "            - name: elasticsearch-data\n" +
            "              mountPath: /data/\n" +
            "            - name: es-ftp\n" +
            "              mountPath: /scripts/\n" +
            "            - name: timezone\n" +
            "              mountPath: /etc/localtime\n" +
            "          lifecycle:\n" +
            "            preStop:\n" +
            "              exec:\n" +
            "                command:\n" +
            "                  - bash\n" +
            "                  - /scripts/mount-prestop.sh\n" +
//            "        - name: ftp\n" +
//            "          image: ${ftpImage}\n" +
//            "          command:\n" +
//            "            - /bin/sh\n" +
//            "          args:\n" +
//            "            - '-c'\n" +
//            "            - bash /es-scripts/ftp-entrypoint.sh\n" +
//            "          env:\n" +
//            "            - name: NAMESPACE\n" +
//            "              valueFrom:\n" +
//            "                fieldRef:\n" +
//            "                  apiVersion: v1\n" +
//            "                  fieldPath: metadata.namespace\n" +
//            "            - name: POD_NAME\n" +
//            "              valueFrom:\n" +
//            "                fieldRef:\n" +
//            "                  apiVersion: v1\n" +
//            "                  fieldPath: metadata.name\n" +
//            "            - name: ES_NAME\n" +
//            "              value: ${crNm}\n" +
//            "          resources:\n" +
//            "            limits:\n" +
//            "              cpu: 100m\n" +
//            "              memory: 1Gi\n" +
//            "            requests:\n" +
//            "              cpu: 50m\n" +
//            "              memory: 50Mi\n" +
//            "          volumeMounts:\n" +
//            "            - name: es-ftp\n" +
//            "              mountPath: /es-scripts/\n" +
//            "            - name: es-dump-data\n" +
//            "              mountPath: /backup/\n" +
//            "            - name: elasticsearch-data\n" +
//            "              mountPath: /data/\n" +
            "        volumes:\n" +
            "          - name: es-filebeat\n" +
            "            configMap:\n" +
            "              name: operator-filebeat-configmap\n" +
//            "          - name: es-dump-data\n" +
//            "            persistentVolumeClaim:\n" +
//            "              claimName: ${backupClaimName}\n" +
            "          - name: es-ftp\n" +
            "            configMap:\n" +
            "              name: es-script-cm\n" +
            "          - name: timezone\n" +
            "            hostPath:\n" +
            "              path: /usr/share/zoneinfo/Asia/Shanghai\n" +
            "              type: ''\n" +
            "    volumeClaimTemplates:\n" +
            "    - metadata:\n" +
            "        name: elasticsearch-data\n" +
            "        labels: \n" +
            "          common.k8s.elastic.co/type: elasticsearch\n" +
            "          elasticsearch.k8s.elastic.co/cluster-name: ${crNm}\n" +
            "          elasticsearch.k8s.elastic.co/namespace: ${namespace}\n" +
            "          elasticsearch.k8s.elastic.co/statefulset-name: ${crNm}-es-data\n" +
            "${volumeTemplateLabelYaml}\n" +
            "      spec:\n" +
            "${dataPVNameSelector}\n" +
            "        accessModes:\n" +
            "        - ReadWriteOnce\n" +
            "        resources:\n" +
            "          requests:\n" +
            "            storage: ${dataStoSz}\n" +
            "        storageClassName: ${scName}\n";
//            "    - metadata:\n" +
//            "        name: es-dump-data\n" +
//            "        labels: \n" +
//            "          common.k8s.elastic.co/type: elasticsearch\n" +
//            "          elasticsearch.k8s.elastic.co/cluster-name: ${crNm}\n" +
//            "          elasticsearch.k8s.elastic.co/namespace: ${namespace}\n" +
//            "          elasticsearch.k8s.elastic.co/statefulset-name: ${crNm}-es-data\n" +
//            "${volumeTemplateLabelYaml}\n" +
//            "      spec:\n" +
//            "${dataPVNameSelector}\n" +
//            "        accessModes:\n" +
//            "        - ReadWriteOnce\n" +
//            "        resources:\n" +
//            "          requests:\n" +
//            "            storage: ${dumpStorageSize}\n" +
//            "        storageClassName: ${dumpScName}";


    // crd
//    final ResourceDefinitionContext context = new ResourceDefinitionContext
//            .Builder()
//            .withGroup("elasticsearch.k8s.elastic.co")
//            .withKind("Elasticsearch")
//            .withPlural("elasticsearches")
//            .withNamespaced(true)
//            .withVersion("v1")
//            .build();
    static final String YAML_EXPORTER = "apiVersion: apps/v1\n" +
            "kind: Deployment\n" +
            "metadata:\n" +
            "  name: ${name}\n" +
            "  labels:\n" +
            "    app: elasticsearch-exporter\n" +
//            "  ownerReferences:\n"+
//            "  - apiVersion: ${esApiVersion}" +
//            "    blockOwnerDeletion: false" +
//            "    controller: true" +
//            "    kind: ${esKind}" +
//            "    name: ${esName}" +
//            "    uid: ${esUID}" +
            "spec:\n" +
            "  replicas: 1\n" +
            "  selector:\n" +
            "    matchLabels:\n" +
            "      app.kubernetes.io/app: elasticsearch\n" +
            "      app.kubernetes.io/component: exporter\n" +
            "      app.kubernetes.io/name: ${crNm}\n" +
            "  template:\n" +
            "    metadata:\n" +
            "      annotations:\n" +
            "        prometheus.io/port: 9114\n" +
            "        prometheus.io/path: /_prometheus/metrics\n" +
            "        prometheus.io/scrape: true\n" +
            "      labels:\n" +
            "        app.kubernetes.io/app: elasticsearch\n" +
            "        app.kubernetes.io/component: exporter\n" +
            "        app.kubernetes.io/name: ${crNm}\n" +
            "    spec:\n" +
            "      volumes:\n" +
            "        - name: timezone\n" +
            "          hostPath:\n" +
            "            path: /usr/share/zoneinfo/Asia/Shanghai\n" +
            "            type: ''\n" +
            "      containers:\n" +
            "      - name: elasticsearch-exporter\n" +
            "        image: ${image}\n" +
            "        resources:\n" +
            "          limits:\n" +
            "            cpu: 300m\n" +
            "          requests:\n" +
            "            cpu: 200m\n" +
            "        ports:\n" +
            "        - containerPort: 9114\n" +
            "          name: https\n" +
            "        command:\n" +
            "        - /bin/elasticsearch_exporter\n" +
            "        - --es.all\n" +
            "        - --web.telemetry-path=/_prometheus/metrics\n" +
            "        - --es.ssl-skip-verify\n" +
            "        - --es.uri=http://elastic:${password}@${crNm}-es-http:9200\n" +
            "        securityContext:\n" +
            "          capabilities:\n" +
            "            drop:\n" +
            "            - SETPCAP\n" +
            "            - MKNOD\n" +
            "            - AUDIT_WRITE\n" +
            "            - CHOWN\n" +
            "            - NET_RAW\n" +
            "            - DAC_OVERRIDE\n" +
            "            - FOWNER\n" +
            "            - FSETID\n" +
            "            - KILL\n" +
            "            - SETGID\n" +
            "            - SETUID\n" +
            "            - NET_BIND_SERVICE\n" +
            "            - SYS_CHROOT\n" +
            "            - SETFCAP\n" +
            "          readOnlyRootFilesystem: true\n" +
            "        volumeMounts:\n" +
            "          - name: timezone\n" +
            "            mountPath: /etc/localtime\n" +
            "        livenessProbe:\n" +
            "          httpGet:\n" +
            "            path: /healthz\n" +
            "            port: 9114\n" +
            "          initialDelaySeconds: 30\n" +
            "          timeoutSeconds: 10\n" +
            "        readinessProbe:\n" +
            "          httpGet:\n" +
            "            path: /healthz\n" +
            "            port: 9114\n" +
            "          initialDelaySeconds: 10\n" +
            "          timeoutSeconds: 10\n" +
            "${affinityAndTolerations}\n" +
            "\n";
    private static final String YAML_EXPORTER_SVC = "apiVersion: v1\n" +
            "kind: Service\n" +
            "metadata:\n" +
            "  labels:\n" +
            "    app.kubernetes.io/app: elasticsearch\n" +
            "    app.kubernetes.io/component: exporter\n" +
            "    app.kubernetes.io/name: ${crNm}\n" +
            "  name: ${svcName}\n" +
            "spec:\n" +
            "  ports:\n" +
            "  - name: https\n" +
            "    port: 9114\n" +
            "    protocol: TCP\n" +
            "    targetPort: 9114\n" +
            "  selector:\n" +
            "    app.kubernetes.io/app: elasticsearch\n" +
            "    app.kubernetes.io/component: exporter\n" +
            "    app.kubernetes.io/name: ${crNm}\n" +
            "  sessionAffinity: None\n" +
            "  type: ClusterIP\n" +
            "status:\n" +
            "  loadBalancer: {}";

}