package cn.newdt.cloud.service;

import cn.newdt.cloud.common.OpLogContext;
import cn.newdt.cloud.config.CloudRequestContext;
import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.domain.*;
import cn.newdt.cloud.domain.alert.AlertRuleConfig;
import cn.newdt.cloud.domain.cr.MongoDBCluster;
import cn.newdt.cloud.dto.*;
import cn.newdt.cloud.filter.ResourceView;
import cn.newdt.cloud.mapper.ServiceManagerMapper;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.service.alert.AlertConfigService;
import cn.newdt.cloud.service.impl.AccessManagementService;
import cn.newdt.cloud.service.impl.AppDeleteOperationHandler;
import cn.newdt.cloud.service.impl.KibanaService;
import cn.newdt.cloud.service.impl.TenantService;
import cn.newdt.cloud.utils.*;
import cn.newdt.cloud.vo.*;
import cn.newdt.cloud.yaml.engine.YamlEngine;
import cn.newdt.commons.bean.UserInfo;
import cn.newdt.commons.exception.CustomException;
import cn.newdt.commons.utils.UserUtil;
import com.github.pagehelper.PageInfo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service @Primary
@Slf4j
@Getter
public class AppMultiAZService {
    @Value("${autoManagement:true}")
    protected boolean autoManagement;

    @Autowired
    protected OperationUtil operationUtil;
    @Autowired
    private CloudAppService appService;
    @Autowired
    private NetworkService networkService;
    @Autowired
    private KubeConfigService kubeConfigService;
    @Autowired
    private ResourceManagerService resourceManagerService;
    @Autowired
    CloudAppLogicService appLogicService;
    @Autowired
    private TenantService tenantService;
    @Autowired
    private MultiAzNetworkService multiAzNetworkService;
    @Autowired
    private CloudImageConfigService imageConfigService;
    @Autowired
    private CloudAppConfigService appConfigService;
    @Autowired
    private AccessManagementService accessManagementService;
    @Autowired
    private KubeClientService kubeClientService;
    @Autowired
    private ServiceManagerMapper serviceManagerMapper;
    @Autowired
    private AppScheduler appScheduler;
    @Autowired
    private CloudZoneService zoneService;
    @Value("${app.multi-az.cluster-role-label:}")
    public String MULTI_AZ_CLUSTER_ROLE = "com.shindata.cloud/multi-az-cluster-role";
    @Autowired
    private KubeSchedulerService kubeSchedulerService;
    @Autowired
    private AlertConfigService alertConfigService;

    private ThreadPoolExecutor executor = new ThreadPoolExecutor(2 * Runtime.getRuntime().availableProcessors(),
            25 * Runtime.getRuntime().availableProcessors(),
            60L, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(500));
    private EnumSet<ActionEnum> multiAZOperation =
            EnumSet.of(ActionEnum.DELETE, ActionEnum.START, ActionEnum.STOP, ActionEnum.RECREATE);
    private EnumSet<ActionEnum> serviceOperation = EnumSet.of(ActionEnum.CREATE_SERVICE, ActionEnum.DELETE_SERVICE, ActionEnum.UPDATE_SERVICE);

    /**
     * 应用安装-多中心部署方式
     */
    @Transactional(propagation = Propagation.REQUIRED)
    public CloudAppLogic install(InstallAppVo<? extends OverrideSpec> vo) {
        // 校验以及做一些转换
        validateInstallVo(vo);

        scheduleApp(vo);

        // logic app落表
        CloudAppLogic logicApp = saveLogicApp(vo);

        // 安装物理app，多可用区部署时每个可用区分别创建实例
        AppKindService appKindService = AppServiceLoader.getInstance(AppKind.valueOf(vo.getKind(), vo.getArch()));
        //todo 创建svc：访问管理挑选放到安装的最开始，并改用 ServiceManager作为参数进行传递，在方法内判断 ServiceType 调用不同处理.
        // NodePort 端口挑选和 LBIP 端口挑选，AppkindService的install不再重新挑选 NodePort 或者 LBIP，只使用
        // 根据实例类型计算需要ServiceManager数量
        // MultiAzNetworkService根据ServiceManager填充NodePort 或者 LBIP
        //判断是否多可用区部署
        if (DeployType.multi_az == DeployType.valueOf(vo.getDeployType())) {
            List<Integer> ports = null;
            Map<Integer, List<String>> ips = null;
            try {
                // 启用访问管理 禁用自动分配端口
//                ports = multiAzNetworkService.allocateNodePort(appKindService.getNodeportNum(),
//                        vo.getOverrideSpecs().keySet().stream().mapToInt(i -> i).toArray(), executor, true);
//                ips = multiAzNetworkService.allocateIpOnMultiAzs(vo, logicApp);
                if (appKindService instanceof MultiAZService && ((MultiAZService)appKindService).isParallel()) {
                    installInParallel(vo, logicApp, appKindService, ports, ips, UserUtil.getCurrentUser());
                } else {
                    // 顺序安装, 先部署primary集群, 成功后部署其他集群
                    int primaryKubeId = vo.getPrimaryKubeId();
                    //liantodo 创建svc：访问管理挑选放到安装的最开始，直接设置好 CloudAppVO 的对应属性，下游不再需要重新获取
                    Comparator<Integer> kubePriorityPolicy = Comparator.<Integer, Boolean>comparing(
                                    kubeId -> kubeId == primaryKubeId, Comparator.reverseOrder())
                            .thenComparing(Comparator.naturalOrder());
                    CustPreconditions.checkNotNull(vo.getPrimaryKubeId(), "未指定同城中的主集群");
                    installSequential(vo, primaryKubeId, logicApp, appKindService, kubePriorityPolicy);
                }
            } catch (Exception e) {
                multiAzNetworkService.rollbackMultiAZNetworkAllocation(ports, ips, vo, ((IpReservable)appKindService).getIpReservationTarget());
                if (e instanceof CustomException) throw e;
                log.error("", e);
                throw new CustomException(600, "安装失败");
            } finally {
                MultiAZContext.SharedContext.remove();
            }
        } else { // 单集群
            for (Integer kubeId : vo.getOverrideSpecs().keySet()) {
                CloudAppVO app = appKindService.overrideSpec(logicApp, kubeId, vo);
                appKindService.install(app);
            }
        }
        return logicApp;
    }

    private void installSequential(InstallAppVo<? extends OverrideSpec> vo, int primaryKubeId, CloudAppLogic logicApp, AppKindService appKindService, Comparator<Integer> kubePriorityPolicy) {
        Collection<Integer> kubeIds = vo.getOverrideSpecs().keySet().stream().sorted(kubePriorityPolicy).collect(Collectors.toList());
        List<CloudAppVO> appsToRollback = new ArrayList<>();
        MultiAZContext.SharedContext.put(false, logicApp.getId());
        try {
            for (Integer kubeId : kubeIds) {
                if (kubeId != primaryKubeId) { // 多中心
                    // check if primary zone has shared its context
                    if (MultiAZContext.SharedContext.get().exchange(null) == null)
                        throw new IllegalArgumentException("primary cr is not set in the deployment context");
                }
                CloudAppVO app = appKindService.overrideSpec(logicApp, kubeId, vo);
                appKindService.install(app);
                appsToRollback.add(app);
            }
        } catch (Exception e) {
            log.info("install failed because " + e.getMessage());
            log.info("cancel and rollback");
            rollback(appKindService, appsToRollback);
            throw new CustomException(600, "应用提交失败 " + e.getMessage());
        }
    }

    /**
     * 同一中心一个告警
     */
    public void enableAlert(CloudApp app) {
        log.info("[app install], skipped enable alert for app: {}, kind: {}, namespace: {}",
                app.getCrName(), app.getKind(), app.getNamespace());
        // skipped. 由dmp进行告警配置，包括安装自动应用模板. 避免并发创建多个相同配置或互相覆盖造成不一致.
//        try {
//            AlertRuleConfig alertRuleConfig = alertConfigService.describeCurrentRuleSet(app.getNamespace(), app.getKind() + "_" + app.getArch(), app.getName());
//            if (alertRuleConfig == null) {
//                try {
//                    alertConfigService.createConfigForApp(app);
//                } catch (Exception e) {
//                    log.error("enable alert error", e);
//                }
//            }
//        } catch (Exception ignored) {
//            throw ignored;
//        }
    }

    /**
     * 从集群列表中选择primary kube 作为同城primary role
     */
    private Integer determinePrimaryKubeId(InstallAppVo<? extends OverrideSpec> vo) {
        List<Integer> kubeIds = new ArrayList<>(vo.getOverrideSpecs().keySet());
        if (kubeIds.size() == 1) return kubeIds.get(0);

        Map<String, Object> map = Collections.singletonMap("kubeIds", kubeIds);
        List<KubeConfig> list = kubeConfigService.list(map);
        return list.stream().filter(k -> k.getLabels() != null
                        && CloudAppConstant.ROLE_PRIMARY.equals(k.formatLabelMap().get(MULTI_AZ_CLUSTER_ROLE)))
                .map(k -> k.getId())
                .findFirst()
                .orElse(list.get(ThreadLocalRandom.current().nextInt(vo.getOverrideSpecs().size())).getId());
    }

    /**
     * 用户指定zone或zone+kubeGroup，调度结果更新到 overrideSpecs. 未指定集群根据条件自动选择，否则不进行调度直接返回 <br>
     * 如果指定了亲和关联组件(nameserver之于broker, es之于kibana, zk至于clickhouse)，则调度到该组件所在集群。
     * <dl>
     *     <dt>**1. spec config**</dt>
     *     <dd>a) overridespec <br>
     *     </dd>
     *     <dd>b) 单可用区 + spec <br>
     *     scheduleConfig: affinityKube + [zone+kubegroup+kube]
     *     </dd>
     *     <dd>c) 多可用区 + spec <br>
     *     scheduleConfig: affinityKube + [zone+kubegroup+kube]
     *     </dd>
     *
     *     <dt>**2. schedule config**</dt>
     *     <dd>a) affinity != null</dd>
     *     <dd>b) kube != null</dd>
     *     <dd>c) kubeGroup + zone</dd>
     * </dl>
     * Update: kubeGroup has deprecated
     */
    private void scheduleApp(InstallAppVo<? extends OverrideSpec> vo) {
        if (!CollectionUtils.isEmpty(vo.getOverrideSpecs()))
            return;

        ScheduleConfig scheduleConfig = vo.getScheduleConfig();
        int primaryKubeId;
        Map<Integer, Integer> kubeIds;  // [zoneId, kubeId]
        AppScheduler.MultiAZScheduleResult scheduleResult = null;
        Integer affinitiveLogicId = scheduleConfig.getAffinitiveLogicId();

        if (affinitiveLogicId != null && affinitiveLogicId > 0) {
            primaryKubeId = appLogicService.get(affinitiveLogicId).getPrimaryKubeId();
            kubeIds = appLogicService.getPhysicApps(affinitiveLogicId).stream()
                    .map(app -> kubeConfigService.get(app.getKubeId()))
                    .collect(Collectors.toMap(cfg -> cfg.getZoneId(), cfg -> cfg.getId()));
        } else {
            // 调度应用所需资源为所有组件的资源申请
            AppKindService appKindService = AppServiceLoader.getInstance(AppKind.valueOf(vo.getKind(), vo.getArch()));
            List<ResourceDTO> resourceDTOList = appKindService.getAppComponentResource(vo);
            if (CollectionUtils.isEmpty(resourceDTOList))
                throw new CustomException(600, "调度获取应用组件信息失败");
            resourceDTOList.forEach(
                    resourceDTO -> resourceDTO.setKubeSchedulerId(vo.getKubeSchedulerId()));

            List<AppScheduler.ScheduleContext> contexts = scheduleConfig.getZoneList().stream()
                    .map(cfg -> {
                        Optional.ofNullable(zoneService.getById(cfg.getZoneId()))
                                        .ifPresent(zone -> cfg.setName(zone.getName()));
                        AppScheduler.ScheduleContext sc = new AppScheduler.ScheduleContext(
                                cfg,
                                resourceDTOList);
                        return sc;
                    })
                    .collect(Collectors.toList());

            // 执行 -  多可用区调度, 每个可用区分别选择kube_id.
            AppScheduler.ScheduleContextByZone scb = new AppScheduler.ScheduleContextByZone(contexts, vo.getTenantId());
            // 如果指定集群直接返回
            scheduleResult = appScheduler.schedule(scb);
            primaryKubeId = scheduleResult.getPrimaryKubeId();
            kubeIds = scheduleResult.getResult().entrySet().stream()
                    .collect(
                            Collectors.toMap(e -> e.getKey(),
                                    e -> e.getValue().getKubeDTOS().get(0).getKubeId())
                    );
        }

        // 根据调度结果设置vo属性
        vo.setPrimaryKubeId(primaryKubeId);
        OverrideSpec spec = vo.getSpec();
        Map overrideSpecs = new HashMap<>();
        vo.setOverrideSpecs(overrideSpecs);
        for (Integer zoneId : kubeIds.keySet()) {
            int kubeId = kubeIds.get(zoneId);
            spec.setKubeId(kubeId);
            spec.setMembers(spec.getMembers());
            if (scheduleResult != null) {
                AppScheduler.KubeDTO kubeDTO = scheduleResult.getResult().get(zoneId).getKubeDTOS().get(0);
                if (kubeDTO.getSimulateResults() != null)
                    spec.setIpNodes(kubeDTO.getSimulateResults().stream()
                            .map(n -> new CloudApp.IpNode(n.getNode().getNodeName(), null))
                            .limit(spec.getMembers())
                            .toArray(CloudApp.IpNode[]::new));
            }
            overrideSpecs.put(kubeId, JsonUtil.toObject(spec.getClass(), JsonUtil.toJson(spec)));
        }
    }

    private void installInParallel(InstallAppVo<? extends OverrideSpec> vo, CloudAppLogic logicApp, AppKindService appKindService, List<Integer> ports, Map<Integer, List<String>> ips, UserInfo userInfo) {
        Map<Integer, ? extends OverrideSpec> overrideSpecs = vo.getOverrideSpecs();
        ExecutorCompletionService<CloudAppVO> executorService = new ExecutorCompletionService<>(executor);
        List<Future<CloudAppVO>> futureList = new ArrayList<>();
        // 遍历各个集群安装应用
        for (Integer kubeId : overrideSpecs.keySet()) {
            List<Integer> finalPorts = ports;
            Future<CloudAppVO> future = executorService.submit(() -> {
                UserUtil.setAsyncUserInfo(userInfo);
                CloudAppVO app = appKindService.overrideSpec(logicApp, kubeId, vo);
                configure(app, finalPorts, ips);
                appKindService.install(app);
                return app;
            });
            futureList.add(future);
        }
        // 多中心一致性
        List<CloudAppVO> appsToRollback = new ArrayList<>();
        for (int i = 0; i < overrideSpecs.keySet().size(); i++) {
            try {
                appsToRollback.add(executorService.take().get());
            } catch (InterruptedException | ExecutionException e) {
                log.info("install failed because " + e.getMessage());
                log.info("cancel and rollback");
                // 三种情况: cancel未执行完的, rollback提交成功的, 发生异常的在install方法中已经自行回滚
                cancelRemainingThreads(futureList);
                rollback(appKindService, appsToRollback);
                throw new CustomException(600, "应用提交失败 " + e.getCause().getMessage());
            }
        }
    }

    public boolean needInstallRemaining(CloudApp watchedApp) {
        CloudAppLogic logic = appLogicService.get(watchedApp.getLogicAppId());
        log.info("see if there are remaining apps need to be installed, logicApp's name {}, id {}", logic.getName(), logic.getId());
        // 如果watched主集群应用
        return DeployType.valueOf(logic.getDeployType()) == DeployType.multi_az
                && watchedApp.getKubeId().equals(logic.getPrimaryKubeId());
    }

    /**
     * 如果选择非并行方式安装多中心应用, 则在主集群安装完成后调用此方法安装后续集群
     */
    public void installRemaining(CloudApp watchedApp, InstallAppVo<OverrideSpec> vo, Map<Integer, List<String>> ipMap) {
        CloudAppLogic logic = appLogicService.get(watchedApp.getLogicAppId());
        if (DeployType.valueOf(logic.getDeployType()) == DeployType.multi_az) {
            // 如果watched主集群应用
            if (watchedApp.getKubeId().equals(logic.getPrimaryKubeId())) {
                Map<Integer, CloudApp> kubeAppMap = appLogicService.getPhysicApps(logic.getId()).stream().collect(Collectors.toMap(a -> a.getKubeId(), a -> a));
                CloudApp appOnPrimaryKube = kubeAppMap.get(logic.getPrimaryKubeId());
                for (Integer kubeId : vo.getOverrideSpecs().keySet()) {
                    if (!kubeAppMap.containsKey(kubeId)) {// 尚未安装
                        AppKindService appKindService = AppServiceLoader.getInstance(AppKind.valueOf(vo.getKind(), vo.getArch()));
                        CloudAppVO app = appKindService.overrideSpec(logic, kubeId, vo);
                        logInfo(app, ActionEnum.CREATE.getActionType(), "found not installed app");
                        // configure role, remote ips, nodeport(same cross kubes)
                        app.setWritePort(appOnPrimaryKube.getWritePort());
                        app.setReadPort(appOnPrimaryKube.getReadPort());
                        app.setDisasterRecoveryRole(CloudAppConstant.ROLE_SECONDARY);
                        app.setOwnerTenant(watchedApp.getOwnerTenant());
                        app.setOwnerUser(watchedApp.getOwnerUser());
                        app.setOwnerName(watchedApp.getOwnerName());
                        // 将已经占用的IP分配给应用
                        configure(app, Stream.of(appOnPrimaryKube.getWritePort(), appOnPrimaryKube.getReadPort()).collect(Collectors.toList()),
                                ipMap);
                        appKindService.install(app);
                    }
                }
            }
            if (log.isDebugEnabled())
                log.debug("watching app is not on primary kube, should not trigger remaining app's installation");
        }
    }

    /**
     * 分配ip 并预留.
     */
//    private void installSequential(InstallAppVo<? extends OverrideSpec> vo, CloudAppLogic appLogic, AppKindService appKindService) {
//        // install on primary kube
//        int primaryKubeId = vo.getPrimaryKubeId();
//         app = appKindService.overrideSpec(appLogic, primaryKubeId, vo);
//        try {
//            // 将install vo存于watch job datamap中
//            appKindService.install(app, vo)
//        } catch (Exception e) {
//        }
//    }

    private void configure(CloudAppVO app, List<Integer> ports, Map<Integer, List<String>> ips) {
        if (!CollectionUtils.isEmpty(ports)) {
            app.setWritePort(ports.get(0));
            if (ports.size() > 1)
                app.setReadPort(ports.get(1));
        }
        if (!CollectionUtils.isEmpty(ips)) {
            CloudApp.IpNode[] ipNodes = ips.get(app.getKubeId()).stream().map(ip -> new CloudApp.IpNode(null, ip)).toArray(CloudApp.IpNode[]::new);
            app.setIpList(JsonUtil.toJson(ipNodes));
            List<String> remoteIps = ips.keySet().stream().filter(id -> !id.equals(app.getKubeId())).map(id -> ips.get(id)).flatMap(Collection::stream).collect(Collectors.toList());
            app.setRemoteIps(remoteIps);
        }
    }

    @ResourceView
    public PageInfo<InstallAppVo> searchPage(PageDTO pageDTO) {
        // 应用视图-逻辑应用列表
        List<CloudAppLogic> list = appLogicService.listMap(pageDTO);
        if (list.isEmpty()) return new PageInfo<>(Collections.emptyList());

        UserInfo userInfo = UserUtil.getCurrentUser();
        // 按appKind分类关联物理应用
        Map<Integer, List<CloudAppVO>> collect = list.stream()
                .map(app -> AppKind.valueOf(app.getKind(), app.getArch())).distinct()
                .map(appKind -> AppServiceLoader.getInstance(appKind))
                .parallel()
                .flatMap(appKindService -> {
                    UserUtil.setAsyncUserInfo(userInfo);
                    CloudRequestContext.setupTenantContext(userInfo, null, tenantService);
                    List<Integer> logicIds = list.stream()
                            // 筛选该appkind对应的逻辑应用
                            .filter(app -> AppKind.valueOf(app.getKind(), app.getArch()) == appKindService.getKind())
                            .map(i -> i.getId()).collect(Collectors.toList());
                    return appKindService.searchPage(new PageDTO() {{
                        setCondition(new HashMap() {{
                            put("logicAppIds", logicIds);
                        }});
                        setFlags(pageDTO.getFlags());
                    }}).getList().stream();
                }).collect(Collectors.groupingBy(CloudApp::getLogicAppId));
        // todo 集群视图, resource字段修改
        return PageUtil.page2PageInfo(list, InstallAppVo.class, e -> convertToVO(collect, e));
    }

    public InstallAppVo convertToVO(Map<Integer, List<CloudAppVO>> physicAppsMap, CloudAppLogic e) {
        InstallAppVo<? extends OverrideSpec> vo = new InstallAppVo();
        String kind = e.getKind();
        try {
            vo.setLogicId(e.getId());
            vo.setName(e.getName());
            vo.setAppSystemName(e.getAppSystemName());
            vo.setKind(kind);
            vo.setArch(e.getArch());
            vo.setDeployType(e.getDeployType());
            vo.setNamespace(e.getNamespace());
            vo.setOwnerName(e.getOwnerName());
            vo.setOwnerId(e.getOwnerUser());
            vo.setOwnerTenantId(e.getOwnerTenant());
            vo.setCreateTimestamp(DateUtil.localDateTimeToEpochMilli(e.getCreateTime()));
            vo.setUpdateTimestamp(DateUtil.localDateTimeToEpochMilli(e.getUpdateTime()));
            // put primary zone in front
            List<CloudAppVO> appAzs = Optional.ofNullable(physicAppsMap.get(e.getId())).orElse(Collections.emptyList());
            appAzs.sort(Comparator.comparing(app -> app.getRole()));
            vo.setApp_azs(appAzs);
            //查询访问管理信息：vo 添加 serviceList，替代 serviceIPs 和 nodeportlist
            vo.setServiceType(vo.getApp_azs().stream().map(CloudAppVO::getServiceType).findFirst().orElse(null));
            vo.setServiceList(accessManagementService.AZServiceList(e.getId()));
            vo.setVersion(vo.getApp_azs().stream().map(CloudApp::getVersion).filter(Objects::nonNull).findFirst().orElse(e.getVersion()));
            vo.setResources(vo.getApp_azs().stream()
                    .reduce(new ResourceMetricVO(), ResourceMetricVO::aggregateResourceMetric, (t1, t2) -> t1));// todo combiner
            vo.setCrDeleted(vo.getApp_azs().stream().map(CloudAppVO::isCrDeleted).reduce(true, (a, b) -> a && b));
            vo.setOpsExecutable(vo.getApp_azs().stream().map(CloudAppVO::isOpsExecutable).reduce(true, (a, b) -> a && b));
            vo.setCpuRaw(vo.getResources().getCpu());
            vo.setCpuUsageRaw(vo.getResources().getCpuUsage());
            vo.setMemoryRaw(vo.getResources().getMemory());
            vo.setMemoryUsageRaw(vo.getResources().getMemoryUsage());
            vo.setDiskRaw(vo.getResources().getDisk());
            vo.setDiskUsageRaw(vo.getResources().getDiskUsage());
            vo.setBackupDiskRaw(vo.getResources().getBackupDisk());
            vo.setBackupDiskUsageRaw(vo.getResources().getBackupDiskUsage());
            vo.setMembers(vo.getApp_azs().stream().map(CloudAppVO::getMembers).mapToInt(i->i).sum());
            // todo refact
            vo.setZookeeperName(vo.getApp_azs().stream().map(CloudAppVO::getZookeeperName).filter(Objects::nonNull).findFirst().orElse(null));
            if (kind.equalsIgnoreCase("Broker")) {
                vo.setMasterSize(vo.getApp_azs().stream().map(CloudAppVO::getMasterSize).mapToInt(i->i).sum());
                vo.setSlaveCount(vo.getApp_azs().stream().map(CloudAppVO::getSlaveCount).mapToInt(i->i).sum());
            } else if (kind.equalsIgnoreCase("Elasticsearch")) {
                vo.setSlaveCount(vo.getApp_azs().stream().map(CloudAppVO::getDataSize).mapToInt(i->i).sum());
            }  else if (kind.equalsIgnoreCase("Kibana")) {
                KibanaService.KibanaVO kibanaVO = (KibanaService.KibanaVO) vo.getApp_azs().get(0);
                vo.setEsName(kibanaVO.getEsName());
            } else if(kind.equalsIgnoreCase("Redis") && vo.getArch().equalsIgnoreCase("Cluster")){
                vo.setMasterSize(vo.getApp_azs().stream().map(CloudAppVO::getMasterSize).mapToInt(i->i).sum());
                vo.setSlaveCount(vo.getApp_azs().stream().map(CloudAppVO::getSlaveCount).mapToInt(i->i).sum());
            }else if(kind.equalsIgnoreCase("MongoDB") && vo.getArch().equalsIgnoreCase("Cluster")){
                CloudApp app = appService.getCloudAppByLogicId(vo.getLogicId());
                String cr = app.getCr();
                if(null == cr || "".equalsIgnoreCase(cr)){
                    cr = app.getCrRun();
                }
                MongoDBCluster mongoDBClusterCr = YamlEngine.unmarshal(cr, MongoDBCluster.class);
                Integer masterSize = mongoDBClusterCr.getSpec().getShardServers().getShardCount();
                vo.setMasterSize(masterSize);
                vo.setSlaveCount(vo.getMembers() - masterSize);
            }else {
                vo.setSlaveCount(vo.getMembers() - 1);
            }
//            vo.setNodeportList(nodeportList);
            vo.setDeleted(vo.getApp_azs().stream().map(CloudAppVO::getDeleted).reduce(true, (e1, e2) -> e1 && e2));
            //判断是否有deletable
            vo.setDeletable(vo.getApp_azs().stream().map(CloudAppVO::getDeletable).reduce(true, (e1, e2) -> e1 && e2));
            vo.setKubeSchedulerId(e.getKubeSchedulerId());
            vo.setKubeSchedulerName(e.getKubeSchedulerName());
            vo.setAllowRollback(vo.getApp_azs().stream().anyMatch(CloudAppVO::isAllowRollback));
        } catch (Exception ex) {
            log.error("populate appVO error", ex);
        }
        return vo;
    }

    public PageInfo<InstallAppVo> searchWithOutAdditionalInfo(PageDTO pageDTO) {
        // 应用视图
        List<CloudAppLogic> list = appLogicService.listMap(pageDTO);
        if (list.isEmpty()) return new PageInfo<>(Collections.emptyList());
        List<CloudApp> physicApps = appService.getByJoinLogicApp(pageDTO.getCondition());
        Map<Integer, List<CloudApp>> physicAppsMap = physicApps.stream().filter(a->a.getLogicAppId() != null).collect(Collectors.groupingBy(e -> e.getLogicAppId()));
        PageInfo<InstallAppVo> pageInfo = PageUtil.page2PageInfo(list, InstallAppVo.class, e -> {
            InstallAppVo vo = new InstallAppVo();
            try {
                vo.setLogicId(e.getId());
                vo.setName(e.getName());
                vo.setVersion(e.getVersion());
                vo.setKind(e.getKind());
                vo.setArch(e.getArch());
                vo.setDeployType(e.getDeployType());
                vo.setNamespace(e.getNamespace());
                vo.setCreateTimestamp(DateUtil.localDateTimeToEpochMilli(e.getCreateTime()));
                vo.setUpdateTimestamp(DateUtil.localDateTimeToEpochMilli(e.getUpdateTime()));
                List<CloudApp> app_azs = Optional.ofNullable(physicAppsMap.get(e.getId())).orElse(Collections.emptyList());
                vo.setDeleted(app_azs.stream().map(CloudApp::getDeleted).reduce(true, (e1, e2) -> e1 && e2));
                vo.setYaml(app_azs.stream().map(a->StringUtils.isEmpty(a.getCr()) ? a.getCrRun() : a.getCr()).collect(Collectors.joining("\n---\n")));
                vo.setOwnerName(e.getOwnerName());
            } catch (Exception ex) {
                log.error("populate appVO error", ex);
            }
            return vo;
        });
        // todo 集群视图, resource字段修改
        return pageInfo;
    }


    private void rollback(AppKindService appKindService, List<CloudAppVO> appsToRollback) {
        for (CloudAppVO app : appsToRollback) {
            executor.submit(() -> rollback(appKindService, app));
        }
    }

    private void rollback(AppKindService appKindService, CloudAppVO app) {
        // 删除资源
        appKindService.deletePerm(app);
//        appKindService.deleteCrControlledResources(app);
        // 删除
        networkService.rollbackParallel(app, "pod"); // todo
        // todo 删除pvc
//        resourceManagerService.deletePvc(app.getKubeId(), app.getNamespace(), app.getCrName());
        // 删除记录
        app.setDeleted(true);
        appService.update(app);

        logInfo(app, "Install rollback", "rollback succeed");
    }

    private void logInfo(CloudApp app, String action, String msg) {
        log.info(action + " [{}/{} in cluster {}] " + msg, app.getNamespace(), app.getName(), app.getKubeId());
    }

    private String logError(CloudApp app, String action, String msg, Exception e) {
        Throwable cause = e.getCause();
        if (cause != null)
            msg = msg.replaceAll(cause.getClass().getName() + ": ", "");
        String errorMsg = String.format(action + "[%s/%s] %s", app.getNamespace(), app.getName(), msg);
//        log.error(errorMsg, e); suppress to reduce duplicate log
        return errorMsg;
    }

    private void cancelRemainingThreads(List<Future<CloudAppVO>> futures) {
        for (Future f : futures)
            f.cancel(true);
    }

    void validateInstallVo(InstallAppVo vo) {
        // todo
        CustPreconditions.checkNotNull(vo.getDeployType(), "缺少参数:部署方式");
        DeployType deployType = null;
        try {
            deployType = DeployType.valueOf(vo.getDeployType());
        } catch (IllegalArgumentException e) {
            throw new CustomException(600, "参数错误-部署方式");
        }

        // 多种配置方式具有优先级
        // 1. overrideSpecs
        // 2. spec
        List<ScheduleConfig.ZoneConfig> scheduleConfigs = vo.getScheduleConfig() == null ? null : vo.getScheduleConfig().getZoneList();
        CustPreconditions.checkState(scheduleConfigs != null &&
                scheduleConfigs.stream().noneMatch(sc->sc.getZoneId()==0), "没有指定可用区");

        Collection kubeIds = null;
        if (!CollectionUtils.isEmpty(vo.getOverrideSpecs())) {
            kubeIds = vo.getOverrideSpecs().keySet();
//            vo.setKubeGroupId(null); // ignore
//            if (vo.getKubeId() != null) {
//                CustPreconditions.checkState(vo.getKubeId().length == vo.getOverrideSpecs().size(), "参数错误");
//            }
        } else {
            CustPreconditions.checkNotNull(vo.getSpec(), "参数错误");
            kubeIds = scheduleConfigs.stream().map(z -> z.getKubeId())
                            .collect(Collectors.toList());
        }

        if (DeployType.single == deployType ) {
            CustPreconditions.checkState(scheduleConfigs.size() == 1, "单AZ部署只能指定一个集群");
        } else {
            // 检查主可用区
            CustPreconditions.checkState(scheduleConfigs.size() > 1, "没有指定多个可用区");
            boolean expression = scheduleConfigs.stream().anyMatch(
                    sc -> CloudAppConstant.ROLE_PRIMARY.equals(sc.getDrRole()));
            if (!expression) {
                CloudZone defaultZone;
                if ((defaultZone = zoneService.getDefault()) != null)
                    for (ScheduleConfig.ZoneConfig sc : scheduleConfigs) {
                        if (sc.getZoneId() == defaultZone.getId())
                            sc.setDrRole(CloudAppConstant.ROLE_PRIMARY);
                    }
            }
            CustPreconditions.checkState(expression, "参数错误-缺少主可用区");
        }

        validateNamespace(vo);
        // 集群不能重复, todo 跨集群一致
        String crName = HanUtil.toPinyin(vo.getName()); //appService.generateCrName(new AppMetadata(kubeId, logicApp.getNamespace(), null, logicApp.getName(), vo.getKind(), vo.getArch(), overrideSpec.getMembers()), null);
        vo.setCrName(crName.toLowerCase());

        AppKindService appKindService = AppServiceLoader.getInstance(AppKind.valueOf(vo.getKind(), vo.getArch()));

        vo.setAppSystemName(appKindService.getAppSystemName(vo));

        // 计算members
        if (vo.getSpec() != null && vo.getSpec().getMembers() == 0) {
            OverrideSpec spec = vo.getSpec();
            if (spec.getMasterSize() != null && spec.getSpareSize() != null) {
                spec.setMembers(spec.getMasterSize() * (spec.getSpareSize() + 1));
            }
        }
        if (!CollectionUtils.isEmpty(vo.getOverrideSpecs())) {
            for (Object o : vo.getOverrideSpecs().values()) {
                OverrideSpec spec = (OverrideSpec)o;
                if (spec.getMembers() == 0 && spec.getMasterSize() != null && spec.getSpareSize() != null)
                    spec.setMembers(spec.getMasterSize() * (spec.getSpareSize() + 1));
            }
        }
        if (vo.getSpec() != null && vo.getSpec().getMembers() == 0) {
            throw new CustomException(600, "规格参数-实例个数不能为0");
        }
        if (!CollectionUtils.isEmpty(vo.getOverrideSpecs())) {
            if (vo.getOverrideSpecs().values().stream().anyMatch(o->((OverrideSpec)o).getMembers() == 0)) {
                throw new CustomException(600, "规格参数-实例个数不能为0");
            }
        }

        // 自动选择集群时不能指定存储类型
        if (kubeIds == null) {
            vo.getSpec().setCsiType(null);
            vo.getSpec().setBackupCsiType(null);
        }

    }

    void validateNamespace(InstallAppVo vo) {
        if (StringUtils.isEmpty(vo.getNamespace())) {
            CustPreconditions.checkNotNull(CloudRequestContext.getContext().getTenantId(), "缺少参数: 所属租户");
            vo.setNamespace(tenantService.findById(CloudRequestContext.getContext().getTenantId()).getNamespace());
            vo.setTenantId(CloudRequestContext.getContext().getTenantId());
        } else {
            vo.setTenantId(tenantService.findByNamespace(vo.getNamespace())
                    .orElseThrow(() -> new IllegalArgumentException("namespace 解析失败"))
                    .getId());
        }
    }

    public CloudAppLogic saveLogicApp(InstallAppVo<? extends OverrideSpec> vo) {
        CloudAppLogic lp = new CloudAppLogic();
        lp.setName(vo.getName());
        lp.setCrName(vo.getCrName());
        lp.setAppSystemName(vo.getAppSystemName());
        lp.setKind(vo.getKind());
        lp.setArch(vo.getArch());
        lp.setVersion(vo.getVersion());
        LocalDateTime now = LocalDateTime.now();
        lp.setCreateTime(now);
        lp.setUpdateTime(now);
        UserInfo userInfo = UserUtil.getCurrentUser();
        if (userInfo != null) {
            lp.setOwnerName(userInfo.getUsername());
            lp.setOwnerUser(userInfo.getUserid());
        }
        lp.setOwnerTenant(vo.getTenantId());
        lp.setPrimaryKubeId(vo.getPrimaryKubeId());
        lp.setDeployType(vo.getDeployType());

        lp.setNamespace(vo.getNamespace());
        lp.setMembers(vo.getOverrideSpecs().values().stream().map(OverrideSpec::getMembers).mapToInt(i -> i).sum());
        lp.setKubeSchedulerId(vo.getKubeSchedulerId());
        appLogicService.insert(lp);
        return lp;
    }

    /**
     * 实例列表-资源、指标部分
     */
    public List<AppInstanceVO> getInstances(Integer appLogicId, String sortProp, String sort) {
        List<AppInstanceVO> vos = new ArrayList<>();

        if (appLogicId != null) {
            List<CloudApp> apps = appLogicService.getPhysicApps(appLogicId);

            List<CompletableFuture<List<AppInstanceVO>>> futures = new ArrayList<>();
            for (CloudApp app : apps) {
                AppKindService appKindService = AppServiceLoader.getInstance(AppKind.valueOf(app.getKind(), app.getArch()));
                futures.add(CompletableFuture.supplyAsync(() -> appKindService.findInstanceList(app.getId(), sortProp, sort), executor));
            }
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            for (CompletableFuture<List<AppInstanceVO>> future : futures) {
                vos.addAll(future.join());
            }
        }
        if (vos.isEmpty()) return Collections.emptyList();

        // 查询kube name 填充到vo中
        Map<Integer, String> kubeNameMap = kubeConfigService.list(
                Collections.singletonMap("kubeIds", vos.stream().map(vo -> vo.getKubeId())
                        .collect(Collectors.toList()))).stream().collect(Collectors.toMap(k -> k.getId(), k -> k.getName()));
        vos.forEach(vo -> vo.setKubeName(kubeNameMap.get(vo.getKubeId())));

        // fixme for sharded system, sort by shard number
        vos.sort(Comparator.comparing(AppInstanceVO::getZoneRole, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(AppInstanceVO::getKubeId)
                    .thenComparing(AppInstanceVO::getComponentKind, Comparator.nullsLast(Comparator.naturalOrder())) // refact appKind component order definition
                    .thenComparing(AppInstanceVO::getRole, Comparator.nullsLast(Comparator.naturalOrder()))
        );

        return vos;
    }

    private static int compareRole(String role1, String role) {
        String s = "primary,source,master";
        if (s.contains(role1)) {
            if (s.contains(role)) {
                return 0;
            }
            return -1;
        }
        if (s.contains(role)) {
            return 1;
        }

        return role1.compareTo(role);
    }


    /**
     * 实例列表-基本信息部分
     */
    public List<AppInstanceVO> getInstancesBasicInfo(int appLogicId) {
        List<AppInstanceVO> vos = new ArrayList<>();
        List<CloudApp> apps = appLogicService.getPhysicApps(appLogicId);
        List<CompletableFuture<List<AppInstanceVO>>> futures = new ArrayList<>();
        for (CloudApp app : apps) {
            futures.add(CompletableFuture.supplyAsync(() -> appService.findInstances(app.getId()), executor));
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        for (CompletableFuture<List<AppInstanceVO>> future : futures) {
            vos.addAll(future.join());
        }
        // 查询kube name 填充到vo中
        Map<Integer, String> kubeNameMap = kubeConfigService.list(
                Collections.singletonMap("kubeIds", vos.stream().map(vo -> vo.getKubeId()).collect(Collectors.toList()))
            ).stream().collect(Collectors.toMap(k -> k.getId(), k -> k.getName()));
        vos.forEach(vo -> vo.setKubeName(kubeNameMap.get(vo.getKubeId())));
        // 排序 kubeId->role->pod
        vos.sort((e, t) -> {
            if (!Objects.equals(e.getKubeId(), t.getKubeId())) {
                return e.getKubeId() - t.getKubeId();
            }
            if (Objects.equals(e.getRole(), t.getRole())) {
                return e.getPodName() == null ? 0 : compareRole(e.getPodName(), t.getPodName());
            }
            return e.getRole() == null ? 0 : compareRole(e.getRole(), t.getRole()); // todo role 应该按语义排序而不是字符集
        });
        return vos;
    }

    /**
     * 应用变更操作(扩缩容,资源升级,迁移)
     *
     * @param id
     * @param vo overrideSpec中包含变更的属性
     */
    @Transactional
    public void update(int id, InstallAppVo<? extends OverrideSpec> vo, ActionEnum action, String componentKind) {
        // 查看当前的spec
        List<CloudApp> apps = appLogicService.getPhysicApps(id);
        log.info("查询各中心的实际实例个数" + apps.size());
        // 访问管理挑选在各集群同时空闲的端口
//        if (action == ActionEnum.CREATE_SERVICE || action == ActionEnum.UPDATE_SERVICE){
//            String serviceType = vo.getServiceType();
//            if (serviceType.equals(CloudAppConstant.ServiceType.NODE_PORT)) {
//                Boolean manualPort = vo.getOverrideSpecs().get(-1).getManualPort();
//                if (manualPort != null && manualPort) {
//                    accessManagementService.checkNodePort(apps, vo);
//                } else {
//                    accessManagementService.genNodePortForAppView(apps, vo, action);
//                }
//            } else if (serviceType.equals(CloudAppConstant.ServiceType.LOAD_BALANCER)) {
//                accessManagementService.genServiceResource(1, app.getKubeId(), serviceType);
//            }
//        }
        // 解析出操作类型 todo 多线程
        apps.sort(Comparator.comparing(CloudApp::getRole));
        int primaryKubeId = 0;
        Map<Integer, OpLogContext> opLogContextMap = new HashMap<>();
        for (CloudApp app : apps) {
            if (!serviceOperation.contains(action)) {
                if (null != vo.getOverrideSpecs() && vo.getOverrideSpecs().get(app.getKubeId()) == null)
                    if (vo.isMultiaz_propagate()) {
                        // for 多中心资源升级，同步组件规格(备可用区属性)会放在主可用区规格中
                        Map<Integer, OverrideSpec> overrideSpecs = (Map<Integer, OverrideSpec>) vo.getOverrideSpecs();
                        overrideSpecs.put(app.getKubeId(), vo.getOverrideSpecs().get(primaryKubeId));
                    } else
                        continue;
            } else if (app.getRole().equals(CloudAppConstant.ROLE_STANDBY)) // 仅展示主的访问管理，也仅操作主
                continue;
            if (CloudAppConstant.ROLE_PRIMARY.equals(app.getRole()))
                primaryKubeId = app.getKubeId();
            opLogContextMap.put(app.getId(), OpLogContext.instance()); // keep reference of head of chain before reset
            try {
                doUpdate(app, vo, action, componentKind);
            } catch (Exception e) {
                // roll back
                for (int appId : opLogContextMap.keySet()) {
                    OpLogContext currentContext = opLogContextMap.get(appId);
                    do {
                        OpLogContext.OpLog opLog = currentContext.getOpLog();
                        if (StringUtils.isNotEmpty(opLog.getKind())) {
                            if (opLog.getKind().equals(OpLogService.Kind.YAML)) {
                                // the applying will take time to finish
                                kubeClientService.get(appService.get(appId).getKubeId()).applyYaml2(opLog.getUndoing());
                            }
                        }
                    } while ((currentContext = currentContext.track()) != null);
                }
                throw e;
            }
        }
    }

//    private void coordinateUpdate(List<CloudApp> apps, InstallAppVo<? extends OverrideSpec> vo, ActionEnum action, AppKindService appKindService) {
//        for (CloudApp app : apps) {
//            OverrideSpec overrideSpec = vo.getOverrideSpecs().get(app.getKubeId());
//            switch (action) {
//                case SCALE_OUT:
//                    String ipReservationTarget = ((IpReservable) appKindService).getIpReservationTarget();
//                    List<String> ips = networkService.allocateIp(app, overrideSpec.getMembers() - app.getMembers(), ipReservationTarget);
//
//            }
//        }
//
//    }

    private void doUpdate(CloudApp app, InstallAppVo<? extends OverrideSpec> vo, ActionEnum action, String componentKind) {
        AppKind kind = AppKind.valueOf(app.getKind(), app.getArch());
        AppKindService appKindService = AppServiceLoader.getInstance(kind);
        try {
            OverrideSpec overrideSpec;
            switch (action) {
                case SCALE_OUT:
                    overrideSpec = vo.getOverrideSpecs().get(app.getKubeId());
                    // 判断该中心的应用是否需要扩容，避免扩容时报错. 依赖appkindservice.parseVo 中设置member属性
                    if (kind != AppKind.MongoDB_Cluster && kind != AppKind.Redis_Cluster && overrideSpec.getMembers() == app.getMembers()) {
                        logInfo(app, ActionEnum.SCALE_OUT.getActionType(), "该应用没有扩容 当前实例数" + app.getMembers());
                    }

                    appKindService.scale(app.getId(), overrideSpec, action);
                    break;
                case SCALE_IN:
                    overrideSpec= vo.getOverrideSpecs().get(app.getKubeId());
                    if (overrideSpec.getMembers() == 0 && overrideSpec.getIpNodes() != null) {
                        overrideSpec.setMembers(overrideSpec.getIpNodes().length);
                    }
                    if (overrideSpec.getMembers() == app.getMembers()) {
                        logInfo(app, ActionEnum.SCALE_IN.getActionType(), "该应用没有缩容 当前实例数" + app.getMembers());
                    }

                    // todo 改造redis,sentinel 缩容参数ipNode为减少的节点
                    if (overrideSpec.getIpNodes() != null && kind != AppKind.Redis_Cluster) {
                        CloudApp.IpNode[] scaled = Arrays.stream(app.getIpNode()).filter(in -> {
                            for (CloudApp.IpNode ipNode : overrideSpec.getIpNodes()) {
                                if (in.getIp().equals(ipNode.getIp())) {
                                    return false;
                                }
                            }
                            return true;
                        }).toArray(CloudApp.IpNode[]::new);
                        overrideSpec.setIpNodes(scaled);
                    }
                    appKindService.scale(app.getId(), overrideSpec, action);
                    break;
                case UPDATE:
                    overrideSpec= vo.getOverrideSpecs().get(app.getKubeId());
                    appKindService.update(app.getId(), overrideSpec);
                    break;
                case MIGRATE:
                    if (appKindService instanceof MigrateOperation) {
                        overrideSpec= vo.getOverrideSpecs().get(app.getKubeId());
                        MigrateDTO migrateDto = new MigrateDTO();
                        migrateDto.setAppId(app.getId());
                        List<CloudApp.IpNode> migratedNodes = Arrays.stream(overrideSpec.getIpNodes()).collect(Collectors.toList());
                        if (migratedNodes.isEmpty())
                            throw new IllegalArgumentException("未解析到迁移节点");
                        CloudApp.IpNode[] ipnodeArr = null;
                        if (AppKind.Dameng.equals(kind)) {
                            //setIpnode
                            KubeClient client = kubeClientService.get(app.getKubeId());
                            List<PodDTO> pods = client.listPod(app.getNamespace(), kind.labelOfPod(app));
                            List<CloudApp.IpNode> ipnodes = pods.stream().map(pod -> {
                                //放入podName
                                if (pod.getPodIp().equalsIgnoreCase(overrideSpec.getIpNodes()[0].getIp())) {
                                    migrateDto.setPodName(pod.getPodName());
                                }
                                CloudApp.IpNode ipNode = new CloudApp.IpNode();
                                ipNode.setNode(pod.getNodeName());
                                ipNode.setIp(pod.getPodIp());
                                return ipNode;
                            }).collect(Collectors.toList());
                            ipnodeArr = ipnodes.toArray(new CloudApp.IpNode[ipnodes.size()]);
                        }
                        CloudApp.IpNode[] ipnodeArrF = ipnodeArr;
                        migrateDto.setMigrateNodes(migratedNodes.stream().map(mn -> {
                            MigrateDTO.MigrateNode migratedNodePair = new MigrateDTO.MigrateNode();
                            migratedNodePair.setNewNode(mn);
                            if (AppKind.Dameng.equals(kind)) {
                                migratedNodePair.setOldNode(Arrays.stream(ipnodeArrF)
                                        .filter(in -> in.getIp().equals(mn.getIp()))
                                        .findFirst().orElseThrow(() -> new IllegalArgumentException("迁移参数解析失败")));
                            } else {
                                migratedNodePair.setOldNode(Arrays.stream(app.getIpNode())
                                        .filter(in -> in.getIp().equals(mn.getIp()))
                                        .findFirst().orElseThrow(() -> new IllegalArgumentException("迁移参数解析失败")));
                            }
                            return migratedNodePair;
                        }).collect(Collectors.toList()));
                        ((MigrateOperation)appKindService).migrate(migrateDto);
                    }
                    break;
                case UPGRADE:
                    // 检查镜像配置
                    CloudAppConfig appConfig = appConfigService.get(kind, vo.getVersion());
                    if (appConfig == null){
                        throw new IllegalStateException(vo.getVersion() + " 版本不支持，请检查镜像配置");
                    }
                    appKindService.upgrade(app.getId(), vo.getVersion());
                    break;
                case UPDATE_SERVICE:
                    overrideSpec = vo.getOverrideSpecs().get(-1);
                    log.info("[doUpdate] 应用视图-访问管理更新端口，data:{}", overrideSpec.toString());
                    accessManagementService.updateService(
                            overrideSpec.getId(), overrideSpec.isManualService(), overrideSpec.getServiceResource());
                    break;
                case MODIFY_PARAM:
                    overrideSpec= vo.getOverrideSpecs().get(app.getKubeId());
                    log.info("[doUpdate] 应用视图-参数修改，data:{}",overrideSpec.toString());
                    appKindService.modifyConfigParam(overrideSpec.getParams(), app.getId(), componentKind);
                    break;
                case UPDATE_PASSWORD:
                    overrideSpec= vo.getOverrideSpecs().get(app.getKubeId());
                    Password password = new Password(overrideSpec.getParams().get("oldPassword"),overrideSpec.getParams().get("newPassword"));
                    log.info("[doUpdate] 应用视图-密码修改，data:{}",overrideSpec.toString());
                    appKindService.updatePassword(password, app.getId());
                    break;
                default:
                    throw new UnsupportedOperationException("操作不支持");
            }
        } catch (Exception e) {
            String msg = logError(app, action.getAppOperation(), e.getMessage(), e);
            throw new CustomException(600, msg, e);
        }
    }

    private List<CloudApp.IpNode> cal(Integer logicAppId, InstallAppVo<? extends OverrideSpec> vo) {
        Map<Integer, CloudApp> appMap = appLogicService.getPhysicApps(logicAppId).stream().collect(Collectors.toMap(a -> a.getKubeId(), a -> a));
        List<CloudApp.IpNode> scaledCrossZone = new ArrayList<>();
        for (Integer kubeId : vo.getOverrideSpecs().keySet()) {
            OverrideSpec overrideSpec = vo.getOverrideSpecs().get(kubeId);
            if (overrideSpec.getIpNodes() != null && overrideSpec.getIpNodes().length > 0) {
                // 检查缩容
                CloudApp app = appMap.get(kubeId);
                if (overrideSpec.getIpNodes().length < app.getIpNode().length) {
                    List<CloudApp.IpNode> scaled = Arrays.stream(app.getIpNode()).filter(in -> {
                        for (CloudApp.IpNode ipNode : overrideSpec.getIpNodes()) {
                            if (in.getIp().equals(ipNode.getIp())) {
                                return false;
                            }
                        }
                        return true;
                    }).collect(Collectors.toList());
                    scaledCrossZone.addAll(scaled);
                }
            }
        }
        return scaledCrossZone;

    }

    private boolean isSpecChanged(String ori, String cur) {
        return StringUtils.isNoneBlank(cur) && !cur.equalsIgnoreCase(ori);
    }

    /**
     * 应用操作(启,停,删除,备份)
     */
    public Object operate(int id, ActionEnum action) {
        CloudAppLogic logicApp = appLogicService.get(id);
        List<CloudApp> apps = appLogicService.getPhysicApps(id);
        log.info("查询各中心的实际应用个数" + apps.size());
        // 解析出操作类型
        // Note: 不回滚

        // 删除
        if (action == ActionEnum.DELETE && (apps.isEmpty() || apps.stream().allMatch(CloudApp::getDeleted))) {
            updateDelete(id, CloudAppConstant.AppDeleteStatus.DELETED);
            return null;
        }
        List<CompletableFuture<HashMap<String, Object>>> futures = new ArrayList<>();
        UserInfo userInfo = UserUtil.getCurrentUser();
        for (CloudApp app : apps) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                UserUtil.setAsyncUserInfo(userInfo);
                HashMap<String, Object> result = doOperate(logicApp, app, action);
                UserUtil.clear();
                return result;
            }, ThreadUtil.getExecutor()));
        }
        // 操作失败时提醒
        String errorMsg = "";
        for (CompletableFuture<HashMap<String, Object>> future : futures) {
            String msg;
            if (null == future.join()){
                continue;
            }else if (StringUtils.isNotEmpty((msg = future.join().get("err") == null?null : future.join().get("err").toString()))) {
                errorMsg += msg + "\n";
            }else{
                return future.join().get("info");
            }
        }
        if (StringUtils.isNotEmpty(errorMsg)) {
            throw new CustomException(600, errorMsg);
        }
        return null;
    }

    /**
     * @return formatted error message
     */
    private HashMap<String, Object> doOperate(CloudAppLogic logicApp, CloudApp app, ActionEnum action) {
        AppKindService appKindService = AppServiceLoader.getInstance(AppKind.valueOf(app.getKind(), app.getArch()));
        if (!multiAZOperation.contains(action) && CloudAppConstant.ROLE_STANDBY.equals(app.getRole())) {
            return null;
        }
        HashMap<String, Object> resMap = new HashMap<>();
        try {
            switch (action) {
                case DELETE:
                    //取消纳管
                    if (autoManagement) {
                        operationUtil.cancelManageApp(logicApp.getId());
                    }
                    new AppDeleteOperationHandler(this, appService).handleDelete(appKindService, app);
                    // appKindService.uninstall(app.getId());
                    break;
                case BACKUP: // deprecated 复用原来的备份接口
                    if (appKindService instanceof BackupOperation) {
                        if (Objects.equals(logicApp.getPrimaryKubeId(), app.getKubeId()))
                            ((BackupOperation) appKindService).backup(app.getId());
                        else log.info("skip because {} is not primary k8s cluster", app.getKubeId());
                    } else {
                        throw new UnsupportedOperationException();
                    }
                    break;
                case RESTORE: // 恢复单独接口, 在备份列表触发
                case STOP:
                    if (appKindService instanceof StartStopOperation) {
                        ((StartStopOperation) appKindService).stop(app.getId(), null);
                    } else {
                        throw new UnsupportedOperationException();
                    }
                    break;
                case START:
                    if (appKindService instanceof StartStopOperation) {
                        ((StartStopOperation) appKindService).start(app.getId(), null);
                    } else {
                        throw new UnsupportedOperationException();
                    }
                    break;
                case BIG_KEYS:
                    if (appKindService instanceof BigKeysOperation) {
                        String bigKeys = ((BigKeysOperation) appKindService).bigKeys(app.getId());
                        resMap.put("info", bigKeys);
                        return resMap;
                    } else {
                        throw new UnsupportedOperationException();
                    }
                case CLEAN_DATA:
                    if (appKindService instanceof CleanDataOperation) {
                        ((CleanDataOperation) appKindService).cleanData(app.getId());
                    } else {
                        throw new UnsupportedOperationException();
                    }
                    break;
                case RECREATE:
                    new AppDeleteOperationHandler(this, appService).handleRecreate(appKindService, app);
                    break;
                case SWITCH_AZ_ROLE:
                    if (appKindService instanceof MultiAZService) {
                        ((MultiAZService) appKindService).switchAZRole(logicApp.getId(), app.getId());
                    } else {
                        throw new UnsupportedOperationException();
                    }
                    break;
            }
        } catch (Exception e) {
            log.error("", e);
            resMap.put("err", logError(app, action.getAppOperation(), e.getMessage(), e));
            return resMap;
        }
        logInfo(app, action.toString(), "submit successfully");
        return null;
    }

    /**
     * 将逻辑应用state置为deleted
     * @param status 1 删除 2 回收 0 正常
     */
    public void updateDelete(Integer logicAppId, int status) {
        if (logicAppId == null) {
            throw new IllegalArgumentException("logicAppId不能为null");
        } else {
            List<CloudApp> apps = appLogicService.getPhysicApps(logicAppId);
            if (apps.stream().allMatch(app -> app.getIsDeleted() == status)) {
                if (appLogicService.updateDelete(logicAppId, status) > 0) {
                    log.info("update logic app {} as " + status, logicAppId);
                }
            }
        }
    }

    public void tryUpdateVersion(Integer logicAppId, String version) {
        if (logicAppId == null) {
            throw new IllegalArgumentException("logicAppId不能为null");
        } else {
            log.info("update logic app's [delete] as 1 when its physic app was delete");
            List<CloudApp> apps = appLogicService.getPhysicApps(logicAppId);
            Boolean reduce = apps.stream().map(app->app.getVersion().equals(version)).reduce(true, (b, b1) -> b & b1);
            if (reduce) {
                CloudAppLogic update = new CloudAppLogic();
                update.setId(logicAppId);
                update.setVersion(version);

                if (appLogicService.update(update) > 0) {
                    log.info("update logic app version {} as {}", logicAppId, version);
                }
            }
        }

    }

    public Collection<String> getSupportedVersions(String kind) {
        return appConfigService.get(AppKind.valueOf(kind)).stream().map(CloudAppConfig::getVersion).collect(Collectors.toSet());
    }

    /**
     * 根据规格变化解析进行的操作
     */
    private ActionEnum parseAction(CloudApp app, OverrideSpec overrideSpec) {
        ActionEnum action = null;
        // 解析操作类型
        if (overrideSpec.getMembers() > 0 && !Objects.equals(app.getMembers(), overrideSpec.getMembers())) {
            if (overrideSpec.getMembers() > app.getMembers()) {
                action = ActionEnum.SCALE_OUT;
            }
            if (overrideSpec.getMembers() < app.getMembers()) {
                action = ActionEnum.SCALE_IN;
            }
        }
        if (isSpecChanged(app.getCpu(), overrideSpec.getCpu())) {
            action = ActionEnum.UPDATE;
        }
        if (isSpecChanged(app.getMemory(), overrideSpec.getMemory())) {
            action = ActionEnum.UPDATE;
        }
        if (isSpecChanged(app.getDisk(), overrideSpec.getDisk())) {
            action = ActionEnum.UPDATE;
        }
        if (overrideSpec.getIpNodes() != null && overrideSpec.getIpNodes().length > 0) {
            // 检查缩容
            if (overrideSpec.getIpNodes().length < app.getIpNode().length) {
                List<CloudApp.IpNode> scaled = Arrays.stream(app.getIpNode()).filter(in -> {
                    for (CloudApp.IpNode ipNode : overrideSpec.getIpNodes()) {
                        if (in.getIp().equals(ipNode.getIp())) {
                            return false;
                        }
                    }
                    return true;
                }).collect(Collectors.toList());
            }
            // 检查迁移
            if (overrideSpec.getIpNodes().length == app.getIpNode().length) {
                Optional<CloudApp.IpNode> migrated = Arrays.stream(overrideSpec.getIpNodes())
                        .filter(in -> {
                            for (CloudApp.IpNode ipNode : app.getIpNode()) {
                                if (in.getIp().equals(ipNode.getIp()) && !in.getNode().equals(ipNode.getNode())) {
                                    return true;
                                }
                            }
                            return false;
                        }).findFirst();
            }
        }
        return action;
    }


    /**
     * 选择主库所在的应用为多中心MySQL备份源,
     * @param appLogicId 多中心应用id
     * @return 选择的物理app id
     */
    public Integer selectBackupSourceForLogicApp(Integer appLogicId) {
        CloudAppLogic logic = appLogicService.get(appLogicId);
        Stream<CloudApp> stream = appLogicService.getPhysicApps(appLogicId).stream();
        Integer appId = null;
        if (AppMultiAZService.DeployType.valueOf(logic.getDeployType()) == AppMultiAZService.DeployType.multi_az) {
            if (logic.getPrimaryKubeId() == null) {
                appId = getInstances(logic.getId(), null, null).stream()
                        .filter(ivo -> CloudAppConstant.ROLE_PRIMARY.equalsIgnoreCase(ivo.getRole()) ||
                                CloudAppConstant.ROLE_SOURCE.equalsIgnoreCase(ivo.getRole()))
                        .findFirst()
                        .map(e -> e.getAppId())
                        .orElse(null);
            } else {
                stream = stream.filter(app->app.getKubeId().equals(logic.getPrimaryKubeId()));
                appId = stream.findFirst().map(e -> e.getId()).orElse(null);
            }
        } else if (AppMultiAZService.DeployType.valueOf(logic.getDeployType()) == DeployType.single) {
            appId = stream.findFirst().map(e -> e.getId()).orElse(null);
        }

        return appId;
    }

    public Object operate(int id, int appId, ActionEnum action) {
        CloudAppLogic logic = appLogicService.get(id);
        appLogicService.getPhysicApps(id).stream().filter(app -> app.getId().equals(appId))
                .findFirst().map(app -> {
                    return doOperate(logic, app, action);
                }).ifPresent((res) -> {
                            throw new CustomException(600, (String) res.get("err"));
                        }
                );
        return null;
    }

    public Object getMultiAZSummary(int id) {
        return appLogicService.getPhysicApps(id).parallelStream()
                .map(app -> {
                    AppKindService instance = AppServiceLoader.getInstance(app.getKind(), app.getArch());
                    if (instance instanceof MultiAZService) {
                        return ((MultiAZService<?>) instance).getAZSummary(app);
                    }
                    throw new CustomException(600, "实例不支持多可用区");
                }).collect(Collectors.toList());
    }


    public enum DeployType {
        multi_az, single;

        public static boolean isParallel(String deployType) {
            return DeployType.multi_az.name().equals(deployType);
        }
    }

    public static void main(String[] args) {
        AppMultiAZService appInstallService = new AppMultiAZService();
        System.out.println(appInstallService.isSpecChanged(null, null));
        System.out.println(appInstallService.isSpecChanged(null, "1m"));
        System.out.println(appInstallService.isSpecChanged("1m", null));
        System.out.println(appInstallService.isSpecChanged("1m", ""));
        System.out.println(appInstallService.isSpecChanged("1m", "1M"));
        System.out.println(appInstallService.isSpecChanged("1m", "2M"));
    }
}
