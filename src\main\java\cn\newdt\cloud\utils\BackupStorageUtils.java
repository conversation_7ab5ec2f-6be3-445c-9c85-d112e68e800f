package cn.newdt.cloud.utils;

import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.domain.BackupHis;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.service.CloudAppService;
import cn.newdt.cloud.service.impl.ResourceManagerServiceImpl;
import cn.newdt.cloud.vo.CloudBackupStorageVO;
import cn.newdt.commons.exception.CustomException;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import javax.net.ssl.*;
import java.security.cert.X509Certificate;
import java.security.SecureRandom;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.nio.file.Files;

@Slf4j
@Component
public class BackupStorageUtils {
    @Autowired
    private ResourceManagerServiceImpl resourceManagerService;
    @Autowired
    private CloudAppService cloudAppService;

    public InputStream downloadBackupFileForInputStream(BackupHis backupHis) {
        //1.获取备份历史
        //2.获取共享存储客户端
        //3.根据备份历史的信息，获取目标备份文件
        //4.返回输入流

        //备份文件名称
        String backupFileName = backupHis.getFileName();

        //获取应用
        CloudApp app = cloudAppService.get(backupHis.getAppId());
        String namespace = app.getNamespace();
        String crName = app.getCrName();
        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());

        // 备份文件相对路径
        String contextPath = appKind.getBackupFilePath(namespace, crName, backupFileName);
        if (contextPath == null) {
            throw new IllegalArgumentException(app.getKind() + " 不支持");
        }

        CloudBackupStorageVO cloudBackupStorage = resourceManagerService.listBackupStorage();
        if (CloudAppConstant.StorageType.NAS.equalsIgnoreCase(cloudBackupStorage.getStorageType())) {
            // 由于 nfs java 客户端只支持 nfsv3，所以使用 mount 操作系统上的方式，这样兼容 nfsv3 和 nfsv4
            String nasIp = cloudBackupStorage.getServer();
            String nasPath = cloudBackupStorage.getMountPath();
            //nas需要拼接根路径
            String completeBackupPath = CloudAppConstant.CLOUD_MOUNT_PATH.NAS_PATH + File.separator + contextPath;
            return getInputStreamByNfs(nasIp, nasPath, completeBackupPath);
        } else if (CloudAppConstant.StorageType.S3.equalsIgnoreCase(cloudBackupStorage.getStorageType())) {
            String accessKey = cloudBackupStorage.getAccessKey();
            String secretKey = cloudBackupStorage.getSecretKey();
            String region = cloudBackupStorage.getRegion();
            String server = cloudBackupStorage.getServer();
            String bucket = cloudBackupStorage.getBucket();
            return getInputStreamByS3(accessKey, secretKey, region, server, bucket, contextPath);
        } else {
            throw new RuntimeException("不支持的备份存储类型");
        }

    }

    private FileInputStream getInputStreamByNfs(String nasIp, String nasPath, String completeBackupPath) {
        boolean isMount = resourceManagerService.checkIsMount(nasIp, nasPath);
        if (!isMount) {
            // 挂载
            resourceManagerService.mountToNas(nasIp, nasPath);
        }
        // 检查文件是否存在
        File file = new File(completeBackupPath);
        if (!Files.exists(file.toPath())) {
            throw new IllegalArgumentException("备份文件不存在");
        }
        try {
            return new FileInputStream(file);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }
    }

    private InputStream getInputStreamByS3(String accessKey, String secretKey,
                                           String region, String server, String bucket, String objectName) {
        MinioClient minioClient = null;
        InputStream stream = null;
        try {
            // 创建一个信任所有证书的 OkHttpClient
            OkHttpClient okHttpClient = getUnsafeOkHttpClient();
            // 创建 MinioClient 对象，并使用自定义的 OkHttpClient
            minioClient = new MinioClient.Builder()
                    .endpoint(server)
                    .credentials(accessKey, secretKey)
                    .region(region)
                    .httpClient(okHttpClient)
                    .build();

            // 使用流方式下载对象
            stream = minioClient.getObject(GetObjectArgs.builder()
                    .bucket(bucket)
                    .object(objectName)
                    .build());
        } catch (Exception e) {
            log.error("[downloadBackupFile] 从S3获取文件流失败", e);
            throw new CustomException("从备份存储获取文件流失败");
        }
        return stream;
    }

    /**
     * 创建一个不验证 SSL 证书的 OkHttpClient。
     * @return OkHttpClient
     */
    private OkHttpClient getUnsafeOkHttpClient() {
        try {
            // 创建一个信任所有证书的 TrustManager
            final TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public void checkServerTrusted(X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public X509Certificate[] getAcceptedIssuers() {
                            return new X509Certificate[]{};
                        }
                    }
            };

            // 安装信任所有证书的 TrustManager
            final SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new SecureRandom());

            // 创建一个不验证主机名的 HostnameVerifier
            final HostnameVerifier trustAllHostnameVerifier = (hostname, session) -> true;

            // 创建 OkHttpClient 并配置 SSL
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            builder.sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustAllCerts[0]);
            builder.hostnameVerifier(trustAllHostnameVerifier);

            // 可以根据需要添加其他配置，例如超时
            // builder.connectTimeout(10, TimeUnit.SECONDS);
            // builder.readTimeout(10, TimeUnit.SECONDS);
            // builder.writeTimeout(10, TimeUnit.SECONDS);

            return builder.build();
        } catch (Exception e) {
            throw new RuntimeException("Failed to create unsafe OkHttpClient", e);
        }
    }

}
