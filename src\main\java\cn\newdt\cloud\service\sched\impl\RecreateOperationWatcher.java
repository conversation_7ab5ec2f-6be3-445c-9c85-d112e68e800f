package cn.newdt.cloud.service.sched.impl;

import cn.newdt.cloud.constant.ActionEnum;
import cn.newdt.cloud.constant.AppKind;
import cn.newdt.cloud.constant.CloudAppConstant;
import cn.newdt.cloud.constant.StatusConstant;
import cn.newdt.cloud.domain.CloudApp;
import cn.newdt.cloud.domain.ResourceChangeHis;
import cn.newdt.cloud.dto.OpsResultDTO;
import cn.newdt.cloud.repository.KubeClient;
import cn.newdt.cloud.sched.TriggerHis;
import cn.newdt.cloud.service.AppKindService;
import cn.newdt.cloud.service.AppServiceLoader;
import cn.newdt.cloud.service.sched.OpsPostProcessor;
import cn.newdt.cloud.utils.AutowireCapableBeanFactory;
import cn.newdt.cloud.utils.JsonUtil;
import io.fabric8.kubernetes.client.CustomResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.invoke.MethodHandle;
import java.lang.invoke.MethodHandles;
import java.lang.invoke.MethodType;
import java.util.Map;

import static cn.newdt.cloud.constant.ScheduleConstant.JOB_DATA_KEY_CHANGE_ID;

public class RecreateOperationWatcher extends OpsProcessorContext implements OpsPostProcessor {

    @Autowired
    AutowireCapableBeanFactory beanFactory;

    @Override
    public OpsResultDTO postProcess(TriggerHis triggerHis) throws Exception {
        OpsResultDTO.Builder result = OpsResultDTO.builder().stopJob(false);
        Map<String, String> jobDataMap = triggerHis.returnMergedJobDataMap();
        ResourceChangeHis resourceHis = resourceChangeHisService.get(Integer.parseInt(jobDataMap.get(JOB_DATA_KEY_CHANGE_ID)));
        Map<String, Object> rsDataMap = resourceHis.mutableDataMap();

        String appId = jobDataMap.get("appId");
        CloudApp app = appService.get(Integer.valueOf(appId));

        CustomResource cr = appService.getCustomResource(app, AppKind.valueOf(app.getKind(), app.getArch()).getCrClass());
        if (cr == null)
            result.stopJob(true).status(StatusConstant.FAIL).msg("Custom resource was deleted");
        // recreate pod
        if (cr != null && recreatePod(triggerHis, result, rsDataMap, app, resourceHis)) {
            // enable alert config
            try {
                alertConfigService.enableAlertFor(app);
                result.stopJob(true).status(StatusConstant.SUCCESS);
            } catch (Exception e) {
                result.msg("enable alert failed").msg(e.getMessage());
            }
        }
        // update app delete status to 0(recycled)
        if (result.isSuccessful()) {
            app.setIsDeleted(CloudAppConstant.AppDeleteStatus.NORMAL);
            appService.update(app);
            appMultiAZService.updateDelete(app.getLogicAppId(), CloudAppConstant.AppDeleteStatus.NORMAL);
            //重新纳管
            if (autoManagement && StringUtils.isNotEmpty(AppKind.valueOf(app.getKind(), app.getArch()).instType())) {
                logInfo(app, ActionEnum.CREATE, "app will be managed into CMDB");
                result.msg("sync cloud app to DMP");
                result.msg(operationUtil.syncToDMP(app, triggerHis));
            }
        }
        if (result.isStopped())
            appService.handleWatchResult(app.getId(), result.isSuccessful());

        return result.build();
    }

    /**
     * @return true if recreate success
     */
    private boolean recreatePod(TriggerHis triggerHis, OpsResultDTO.Builder result, Map<String, Object> rsDataMap, CloudApp app, ResourceChangeHis resourceHis) {
        boolean crUpdated = Boolean.parseBoolean(String.valueOf(rsDataMap.getOrDefault("cr_updated", false)));
        AppKindService instance = AppServiceLoader.getInstance(app.getKind(), app.getArch());

        if (!crUpdated) {
            instance.recreate(app);
            rsDataMap.put("cr_updated", true);
            resourceHis.setDataMap(JsonUtil.toJson(rsDataMap));
            resourceChangeHisService.update(resourceHis);
            return false;
        }
        // check cr recreate succeeded
        AppKind appKind = AppKind.valueOf(app.getKind(), app.getArch());
        Class<? extends OpsPostProcessor> processorClass =
                AppServiceLoader.getInstance(appKind)
                        .getProcessorClass(ActionEnum.CREATE); // watch recreate via watcher for operation of install.
        try {
            Object bean = beanFactory.createBean(processorClass);
            MethodHandle evalMH = MethodHandles.lookup().findVirtual(processorClass, "evalOpsResult",
                    MethodType.methodType(OpsResultDTO.Builder.class, TriggerHis.class, CustomResource.class, CloudApp.class, KubeClient.class));
            KubeClient kubeClient = kubeClientService.get(app.getKubeId());
            CustomResource cr = kubeClient
                    .listCustomResource(appKind.getCrClass(), app.getCrName(), app.getNamespace());

            OpsResultDTO.Builder opsRetBuilder = (OpsResultDTO.Builder) evalMH.invoke(bean, triggerHis, cr, app, kubeClient);
            OpsResultDTO opsRet = opsRetBuilder.build();
            result.msg(opsRet.getMsg());
             if (opsRetBuilder.isSuccessful()) {
                return true;
            }
        } catch (Throwable e) {
            result.msg(e.getMessage());
            logInfo(app, ActionEnum.RECREATE, e.getMessage());

        }
        return false;
    }
}
